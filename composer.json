{"require": {"php": ">=8.2", "ext-json": "*", "ext-bcmath": "*", "topthink/framework": "^8.0", "topthink/think-dumper": "^1.0", "topthink/think-ide-helper": "^2.0", "shuipf/common": "^2.0", "shuipf/api": "^1.0", "shuipf/swoole": "^1.0", "shuipf/aliyun-log": "^1.0", "shuipf/chinese-calendar": "dev-main", "overtrue/php-opencc": "^1.2", "zjkal/time-helper": "^1.0", "6tail/tyme4php": "^1.3", "openspout/openspout": "^4.0"}, "require-dev": {"shuipf/testing": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "tests\\": "tests"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"shuipf/packages": {"type": "composer", "url": "https://packages.shuipf.com/"}}}