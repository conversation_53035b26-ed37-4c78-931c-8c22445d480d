<?php
// +----------------------------------------------------------------------
// | 配置
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\App;
use think\facade\Env;

return [
    // http服务配置
    'http' => [
        'enable' => true,
        // 监听地址
        'host' => '0.0.0.0',
        // 监听端口
        'port' => 8170,
        // 工作进程
        'worker_num' => Env::get('swoole.worker_num', 1),
        // 额外配置
        'options' => [],
    ],
    // 热更新配置
    'hot_update' => [
        // 是否启用热更新
        'enable' => Env::get('swoole.hot_update', false),
        // 监控文件类型
        'name' => ['*.php', '*.env'],
        // 监控访问
        'include' => [
            // App::getRootPath() . 'view' . DIRECTORY_SEPARATOR,
            App::getBasePath(),
        ],
        'exclude' => [],
    ],
    'rpc' => [
        'server' => [
            'enable' => true,
            'port' => 9170,
            'services' => [
                \api\rpc\Api::class,
            ],
        ],
        'client' => [
            // 接口服务
            'api' => [
                'host' => Env::get('rpc_api.host', '127.0.0.1'),
                'port' => Env::get('rpc_api.port', 9220),
                'max_active' => Env::get('rpc_api.max_active', 10),
                'max_wait_time' => 1,
            ],
        ],
    ],
    // 连接池
    'pool' => [
        'db' => [
            'enable' => true,
            'min_active' => Env::get('pool_db.min_active', 0),
            'max_active' => Env::get('pool_db.max_active', 5),
            'max_wait_time' => 1,
        ],
        'cache' => [
            'enable' => true,
            'min_active' => Env::get('pool_cache.min_active', 0),
            'max_active' => Env::get('pool_cache.max_active', 10),
            'max_wait_time' => 1,
        ],
    ],
    // 协程
    'coroutine' => [
        'enable' => true,
        'flags' => SWOOLE_HOOK_ALL,
    ],
    // 沙箱模式下需要复位的对象
    'resetters' => [],
    // 需要创建的内存表结构
    'tables' => [],
    // 每次请求前需要清空的实例
    'instances' => [],
    // 每次请求前需要重新执行的服务
    'services' => [],
];
