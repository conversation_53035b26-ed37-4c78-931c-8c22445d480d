<?php
// +----------------------------------------------------------------------
// | 数据库培训
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

use think\facade\Env;

return [
    // 默认使用的数据库连接配置
    'default' => Env::get('database.driver', 'default'),
    // 自定义时间查询规则
    'time_query_rule' => [],
    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp' => true,
    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',
    // 数据库连接配置信息
    'connections' => [
        // 数据库
        'default' => [
            // 数据库类型
            'type' => Env::get('database.type', 'mysql'),
            // 服务器地址
            'hostname' => Env::get('database.hostname', '127.0.0.1'),
            // 数据库名
            'database' => Env::get('database.database', ''),
            // 用户名
            'username' => Env::get('database.username', 'root'),
            // 密码
            'password' => Env::get('database.password', ''),
            // 端口
            'hostport' => Env::get('database.hostport', '3306'),
            // 数据库连接参数
            'params' => [],
            // 数据库编码默认采用utf8
            'charset' => Env::get('database.charset', 'utf8'),
            // 数据库表前缀
            'prefix' => Env::get('database.prefix', ''),
            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy' => Env::get('database.deploy', 0),
            // 数据库读写是否分离 主从式有效
            'rw_separate' => true,
            // 读写分离后 主服务器数量
            'master_num' => 1,
            // 指定从服务器序号
            'slave_no' => '',
            // 是否严格检查字段是否存在
            'fields_strict' => true,
            // 是否需要断线重连
            'break_reconnect' => true,
            // 监听SQL
            'trigger_sql' => Env::get('app_debug', false),
            // 开启字段缓存
            'fields_cache' => true,
        ],
    ],
];
