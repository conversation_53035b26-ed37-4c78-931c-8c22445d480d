<?php
// +----------------------------------------------------------------------
// | php-cs-fixer 配置文件
// | https://github.com/FriendsOfPHP/PHP-CS-Fixer
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

require __DIR__ . '/vendor/autoload.php';

use PhpCsFixer\Config;
use Symfony\Component\Finder\Finder;

$finder = Finder::create()
    // 不包括“隐藏”目录和文件(以点开头)。
    ->ignoreDotFiles(false)
    // 强制查找器遵守.gitignore并根据列出的规则忽略文件。
    ->ignoreVCSIgnored(true)
    ->notName(['rpc.php'])
    ->files()
    ->name('/\.php$/')
    ->exclude('vendor')
    ->in([__DIR__ . '/app', __DIR__ . '/config', __DIR__ . '/tests']);

$config = new Config();
$config->setRiskyAllowed(true)
    // https://cs.symfony.com/doc/rules/index.html
    ->setRules(
        [
            // 规则集
            '@PSR12' => true,
            '@PHP80Migration' => true,

            // 数组语法, 推荐使用 short 语法.
            // short => "[]"; long => "array()"
            'array_syntax' => ['syntax' => 'short'],
            // 操作符=>不应该被多行空白包围。
            'no_multiline_whitespace_around_double_arrow' => true,
            // 数组索引应该始终使用方括号编写。
            'normalize_index_brace' => true,
            // 数组应该像函数/方法参数一样格式化，前导或末尾没有单行空格。
            'trim_array_spaces' => true,
            // list 语句语法, 推荐使用 long 语法.
            // 例：$array = [1, 2, 3];
            // short => "[$a, $b, $c] = $array;"; long => "list($a, $b, $c) = $array;"
            'list_syntax' => ['syntax' => 'short'],
            // 命名空间声明之后必须有一个空行
            'blank_line_after_namespace' => true,
            // 确保PHP打开标记没有在同一行上的代码，并且后面跟着一个空行.
            'blank_line_after_opening_tag' => false,
            // 确保在PHP打开标记的同一行中没有代码
            'linebreak_after_opening_tag' => true,
            // PHP 文件最后一行应移除结束标签
            'no_closing_tag' => true,
            // 使用 __DIR__ 代替 dirname(__FILE__)
            'dir_constant' => true,
            // 如果可能，将strpos()调用替换为str_starts_with()或str_contains()。
            'modernize_strpos' => true,
            // 使用 elseif 代替 else if
            'elseif' => true,
            // 使用 preg 系列函数 代替 ereg 系列函数
            'ereg_to_preg' => true,
            // PHP代码必须使用 <?php 或 <?=
            'full_opening_tag' => true,
            // include/require 函数和文件路径中间用一个空格分开，文件路径不应该放在括号内.
            // include 'a.php'; 代替 include('a.php');
            'include' => true,
            // true、false, null 必须使用小写.
            //'lowercase_constants' => true,
            // PHP 系统关键字必须使用小写.
            // 例：echo, break
            'lowercase_keywords' => true,
            // 使用 new 创建的类实例, 后面必须跟上大括号 "()". new A(); 代替 new A;
            'new_with_braces' => true,
            // 将PHP4风格的构造函数转换为 __construct
            'no_php4_constructor' => true,
            // PHP单行数组不应该有尾随逗号。
            'no_trailing_comma_in_singleline_array' => true,
            // 未使用的 use 必须删除
            'no_unused_imports' => true,
            // 在数组声明中, 每个逗号前都不能有空格.
            'no_whitespace_before_comma_in_array' => true,
            // 类中的 常量、成员变量、构造函数 等等之间的排序
            // 默认排序：['use_trait', 'constant_public', 'constant_protected', 'constant_private', 'property_public', 'property_protected', 'property_private', 'construct', 'destruct', 'magic', 'phpunit', 'method_public', 'method_protected', 'method_private']
            'ordered_class_elements' => [
                'order' => ['use_trait', 'constant_public', 'constant_protected', 'constant_private'],
            ],
            // 在数组声明中，每个逗号后都必须有一个空格。
            'whitespace_after_comma_in_array' => true,
            // PHP代码必须只使用UTF-8没有BOM(删除BOM)。
            'encoding' => true,
            // 删除零宽度空间(ZWSP)、非中断空间(NBSP)和其他不可见的unicode符号。
            'non_printable_character' => ['use_escape_sequences_in_strings' => true],
            // 引用内部类时，必须使用正确的大小写。
            'class_reference_name_casing' => true,
            // PHP常量true, false和null必须使用正确的大小写来写。
            'constant_case' => ['case' => 'lower'],
            // 类静态引用self、static和parent必须小写。
            'lowercase_static_reference' => true,
            // 魔术常量应该使用正确的大小写来引用。
            'magic_constant_casing' => true,
            // 神奇的方法定义和调用必须使用正确的大小写
            'magic_method_casing' => true,
            // PHP定义的函数应该使用正确的大小写来调用
            'native_function_casing' => true,
            // 函数的本地类型提示应该使用正确的大小写
            'native_function_type_declaration_casing' => true,
            // 当多个 unset 使用的时候，合并处理
            'combine_consecutive_unsets' => true,
            // 强制转换和变量之间应该有一个空格或无
            'cast_spaces' => ['space' => 'none'],
            // Cast应该用小写字母书写
            'lowercase_cast' => true,
            // 将intval、floatval、doubleval、strval和boolval函数调用替换为根据类型转换操作符
            'modernize_types_casting' => true,
            // 不能使用使用双感叹号的短类型转换bool
            'no_short_bool_cast' => true,
            // 类型转换(boolean)和(integer)应该写成(bool)和(int)， (double)和(real)为(float)， (binary)为(string)
            'short_scalar_cast' => true,
            // 在方法参数和方法调用中，逗号前不能有空格，逗号后必须有一个空格。参数列表可以跨多行分割，随后的每一行缩进一次。这样做时，列表中的第一项必须在下一行，并且每行必须只有一个参数。
            'method_argument_space' => [
                // 逗号后是否保留多个空格
                'keep_multiple_spaces_after_comma' => true,
                // 定义如何处理包含换行符的函数参数列表
                'on_multiline' => 'ensure_fully_multiline',
            ],
            // 在单行上调用方法或函数时，最后一个参数后面绝对不能有逗号
            'no_trailing_comma_in_singleline_function_call' => true,
            // 不能导入它不使用的变量
            'lambda_not_used_import' => true,
            // 函数内爆必须按文档中的顺序用两个参数调用
            'implode_call' => true,
            // .拼接必须有空格分割
            'concat_space' => ['spacing' => 'one'],
        ]
    )
    ->setFinder($finder);

return $config;
