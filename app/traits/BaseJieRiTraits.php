<?php
// +----------------------------------------------------------------------
// | BaseJieRiTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;
use think\facade\Cache;

trait BaseJieRiTraits
{
    /**
     * 根据年份获取节日数据
     * @param int $year
     * @return array
     * @throws Exception
     */
    protected function getJieRiListByYear(int $year): array
    {
        $cacheKeyName = "JieRiListByYear/{$year}";
        $result = Cache::get($cacheKeyName, false);
        if (false === $result) {
            $result = Huangli::getJieRiListByYear($year);
            Cache::set($cacheKeyName, $result, 3600);
        }
        return $result;
    }

    /**
     * 根据日期获得当天节日
     * @param string $date
     * @return array
     * @throws Exception
     */
    protected function getJieRi(string $date): array
    {
        $time = strtotime($date);
        $year = (int)date('Y', $time);
        // 节气列表
        $jieQiList = SolarTerm::getAllJieQi($year);
        // 节日列表
        $jieRiList = $this->getJieRiListByYear($year);
        // 节气
        $str = [];
        $date = date('Y-m-d', $time);
        foreach ($jieQiList as $k => $v) {
            if (date('Y-m-d', strtotime($v)) == $date) {
                $str[] = $k;
                break;
            }
        }
        if (isset($jieRiList['jie'][$time])) {
            foreach ($jieRiList['jie'][$time] as $value) {
                $str[] = $value['title'];
            }
        }
        if (isset($jieRiList['fo'][$time])) {
            $str[] = $jieRiList['fo'][$time];
        }
        return $str;
    }
}
