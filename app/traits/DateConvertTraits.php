<?php
// +----------------------------------------------------------------------
// | DateConvertTraits 日期转换
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

trait DateConvertTraits
{
    /**
     * 获取指定日期起多少天
     * @param string $date
     * @param int $planTime
     * @return string
     */
    protected function getAcquisitionDays(string $date, int $planTime = 6)
    {
        $todayTimestamp = strtotime($date);
        $sixMonthsTimestamp = strtotime("+{$planTime} months", $todayTimestamp);
        $sixMonthsDays = ($sixMonthsTimestamp - $todayTimestamp) / (60 * 60 * 24);
        return number_format($sixMonthsDays, 0, '.', '');
    }
}
