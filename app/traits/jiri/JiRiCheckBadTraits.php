<?php
// +----------------------------------------------------------------------
// | 吉日中不好的整理
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\jiri;

trait JiRiCheckBadTraits
{
    /**
     * 岁破
     * @param string $str 流年年支+日支
     * @return bool
     */
    private function checkSuiPo(string $str): bool
    {
        $list = ['子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳'];
        return in_array($str, $list);
    }

    /**
     * 小红沙
     * @param string $str 流日月支+红沙日支
     * @return bool
     */
    private function checkXiaoHongSha(string $str): bool
    {
        $list = ['寅酉', '卯巳', '辰丑', '巳酉', '午巳', '未丑', '申酉', '酉巳', '戌丑', '亥酉', '子巳', '丑丑'];
        return in_array($str, $list);
    }

    /**
     * 受死
     * @param string $str 流日月支+日支
     * @return bool
     */
    private function checkShouSi(string $str): bool
    {
        $list = ['寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉'];
        return in_array($str, $list);
    }

    /**
     * 杨公忌+九毒日+天地交泰
     * @param string $str 农历月数字+日数字 以下划线组合
     * @return bool
     */
    private function checkYangGong(string $str): bool
    {
        $list = [
            '5_6', '5_7', '5_14', '5_15', '5_16', '5_17', '5_25', '5_26', '5_27',
        ];
        $bool1 = $this->checkYangGongOnly($str);
        $bool2 = in_array($str, $list);
        return ($bool1 || $bool2);
    }

    /**
     * 杨公忌
     * @param string $str 农历月数字+日数字 以下划线组合
     * @return bool
     */
    private function checkYangGongOnly(string $str): bool
    {
        $list = [
            '1_13', '2_11', '3_9', '4_7', '5_5', '6_3', '7_1', '7_29', '8_27', '9_25', '10_23', '11_21', '12_19',
        ];
        return in_array($str, $list);
    }

    /**
     * 三娘煞
     * @param string $daygz 日干支
     * @param int $nongliDayNumber 日数字
     * @return bool
     */
    private function checkSanNiangSha(string $daygz, $nongliDayNumber): bool
    {
        $str = $daygz . '_' . $nongliDayNumber;
        $list = ['庚午_3', '辛未_7', '戊申_13', '己酉_18', '丙午_22', '丁未_27'];
        return in_array($str, $list);
    }

    /**
     * 月破
     * @param string $str 月支+日支
     * @return bool
     */
    private function checkYuePo(string $str): bool
    {
        $list = [
            '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未',
        ];
        return in_array($str, $list);
    }

    /**
     * 披麻
     * @param string $str
     * @return bool
     */
    private function checkPiMa(string $str): bool
    {
        $list = [
            '寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯',
        ];
        return in_array($str, $list);
    }

    /**
     * 课命六合 流日日支+女命年支
     * @param string $str 流日日支+女命年支
     * @return bool
     */
    private function checkKeMingLiuHe(string $str): bool
    {
        $list = [
            '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子',
        ];
        return in_array($str, $list);
    }

    /**
     * 课命三合 流日日支+女命年支
     * @param string $str 流日日支+女命年支
     * @return bool
     */
    private function checkKeMingSanHe(string $str): bool
    {
        $list = [
            '寅午', '卯亥', '辰申', '巳酉', '午寅', '未亥', '申子', '酉丑', '戌午', '亥卯', '子申', '丑巳',
            '寅戌', '卯未', '辰子', '巳丑', '午戌', '未卯', '申辰', '酉巳', '戌寅', '亥未', '子辰', '丑酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 课内三合 流日月支    流日日支
     * @param string $str 流日月支+流日日支
     * @return bool
     */
    private function checkKeNeiSanHe(string $str): bool
    {
        $list = [
            '卯亥', '辰申', '巳酉', '午寅', '未亥', '申子', '酉丑', '戌午', '亥卯', '子申', '丑巳', '卯未',
            '辰子', '巳丑', '午戌', '未卯', '申辰', '酉巳', '戌寅', '亥未', '子辰', '丑酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 课内六合  流日月支+流日日支
     * @param string $str 流日月支+流日日支
     * @return bool
     */
    private function checkKeNeiLiuHe(string $str): bool
    {
        $list = [
            '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子',
        ];
        return in_array($str, $list);
    }

    /**
     * 正四废日
     * @param string $monthDz 月地支
     * @param string $daygz 日干支
     * @return bool
     */
    private function checkZhengSiFeiRi(string $monthDz, string $daygz): bool
    {
        $bool = false;
        switch ($monthDz) {
            case '寅':
            case '卯':
            case '辰':
                $bool = in_array($daygz, ['庚申', '辛酉']);
                break;
            case '巳':
            case '午':
            case '未':
                $bool = in_array($daygz, ['壬子', '癸亥']);
                break;
            case '申':
            case '酉':
            case '戌':
                $bool = in_array($daygz, ['甲寅', '乙卯']);
                break;
            default:
                $bool = in_array($daygz, ['丙午', '丁巳']);
                break;
        }
        return $bool;
    }

    /**
     * 月厌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    private function checkYueYan(string $mdz, string $ddz): bool
    {
        $list = ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天吏
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    private function checkTianLi(string $mdz, string $ddz): bool
    {
        $list = ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 归忌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    private function checkGuiJi(string $mdz, string $ddz): bool
    {
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        return in_array($mdz . $ddz, $listGuiJi);
    }

    /**
     * 十恶大败
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSHiEr(string $dgz): bool
    {
        // 十恶大败
        $listShiEr = ['甲辰', '乙巳', '丙申', '丁亥', '戊戌', '己丑', '庚辰', '辛巳', '壬申', '癸亥'];
        return in_array($dgz, $listShiEr);
    }

    /**
     * 往亡
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkWangWang(string $mdz, string $ddz): bool
    {
        $list = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 四废
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSiFei(string $mdz, string $dgz): bool
    {
        $list = [
            '寅庚申', '卯庚申', '辰庚申', '巳壬子', '午壬子', '未壬子', '申甲寅', '酉甲寅', '戌甲寅', '亥丙午',
            '子丙午', '丑丙午', '寅辛酉', '卯辛酉', '辰辛酉', '巳癸亥', '午癸亥', '未癸亥', '申乙卯', '酉乙卯',
            '戌乙卯', '亥丁巳', '子丁巳', '丑丁巳',
        ];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 大时
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkDaShi(string $mdz, string $ddz): bool
    {
        $list = ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 劫煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkJieSha(string $mdz, string $ddz): bool
    {
        $list = ['寅亥', '卯申', '辰巳', '巳寅', '午亥', '未申', '申巳', '酉寅', '戌亥', '亥申', '子巳', '丑寅'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 五墓
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkWuMu(string $mdz, string $dgz): bool
    {
        $list = ['寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰'];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 月刑
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueXin(string $mdz, string $ddz): bool
    {
        $list = ['寅巳', '卯子', '辰辰', '巳申', '午午', '未丑', '申寅', '酉酉', '戌未', '亥亥', '子卯', '丑戌'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueSha(string $mdz, string $ddz): bool
    {
        $list = ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 灾煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkZaiSha(string $mdz, string $ddz): bool
    {
        $list = ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 四穷日
     * @param string $string 月支+流日柱干支
     * @return bool
     */
    protected function checkSiQiong(string $string): bool
    {
        $list = [
            '寅乙亥', '卯乙亥', '辰乙亥', '巳丁亥', '午丁亥', '未丁亥', '申辛亥', '酉辛亥', '戌辛亥', '亥癸亥', '子癸亥', '丑癸亥',
        ];
        return in_array($string, $list);
    }

    /**
     * 天贼
     * @param string $mdz 月地支
     * @param string $ddz 日地支
     * @return bool
     */
    protected function checkTianZei(string $mdz, string $ddz): bool
    {
        $list = [
            '寅丑', '卯子', '辰亥', '巳戌', '午酉', '未申', '申未', '酉午', '戌巳', '亥辰', '子卯', '丑寅',
        ];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 小耗
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkXiaoHao(string $mdz, string $ddz): bool
    {
        $list = ['寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 岁煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkSuiSha(string $mdz, string $ddz): bool
    {
        $list = ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 四耗
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSiHao(string $mdz, string $dgz): bool
    {
        $list = ['寅壬子', '卯壬子', '辰壬子', '巳乙卯', '午乙卯', '未乙卯', '申戊午', '酉戊午', '戌戊午', '亥辛酉', '子辛酉', '丑辛酉'];
        return in_array($mdz . $dgz, $list);
    }


    /**
     * 劫煞可变条件2   0 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return int 寅申 3, 辰 丑 未 戌 2, 巳 亥 1
     */
    protected function checkJieSha2(string $mdz, array $deArray): int
    {
        if ($mdz == '寅' || $mdz == '申') {
            return 3;
        }
        if (in_array($mdz, ['丑', '辰', '未', '戌']) && $deArray) {
            return 2;
        }
        if (in_array($mdz, ['巳', '亥'])) {
            if (array_intersect(['月德', '天德合'], $deArray)) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 灾煞可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool true 可用 false 不可用
     */
    protected function checkZaiSha2(string $mdz, array $deArray): bool
    {
        return in_array($mdz, ['丑', '寅', '辰', '巳', '未', '申', '戌', '亥']) && count($deArray) > 0;
    }

    /**
     * 大败(大时)可变条件2 0 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return int 0 不可用 1丑未 2 巳申亥 ,3 戌辰
     */
    protected function checkDaShi2(string $mdz, array $deArray): int
    {
        if (in_array($mdz, ['巳', '寅', '申', '亥'])) {
            return 2;
        }
        if (in_array($mdz, ['戌', '辰'])) {
            return 3;
        }
        if (in_array($mdz, ['丑', '未'])) {
            if (array_intersect(['月德', '天德'], $deArray)) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 致死（天吏）可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool true 可用 false 不可用
     */
    protected function checkTianLi2(string $mdz, array $deArray): bool
    {
        return in_array($mdz, ['寅', '巳', '申', '亥']) && !empty($deArray);
    }

    /**
     * 五墓可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool true 可用 false 不可用
     */
    protected function checkWuMu2(string $mdz, array $deArray): bool
    {
        return in_array($mdz, ['子', '午']) && in_array('月德', $deArray);
    }

    /**
     * 月刑可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param bool $bool 含有月德、天德合、天愿 之一为 true 否则为 false
     * @return bool
     */
    protected function checkYueXin2(string $mdz, bool $bool): bool
    {
        return $mdz == '巳' && $bool;
    }

    /**
     * 岁煞可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param bool $bool 含有月德、天愿 之一为 true 否则为 false
     * @return bool
     */
    protected function checkSuiSha2(string $mdz, bool $bool): bool
    {
        return in_array($mdz, ['卯', '酉']) && $bool;
    }

    /**
     * 月煞可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool
     */
    protected function checkYueSha2(string $mdz, array $deArray): bool
    {
        if (in_array($mdz, ['卯', '酉'])) {
            if (array_intersect(['月德', '天德'], $deArray)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 小耗可变条件2 true 可用 false 不可用
     * @param string $mdz
     * @param array $deArray
     * @return bool
     */
    protected function checkXiaoHao2(string $mdz, array $deArray): bool
    {
        if (in_array($mdz, ['丑', '寅', '辰', '巳', '未', '申', '戌', '亥'])) {
            if ($deArray) {
                return true;
            }
        }
        return false;
    }

    /**
     * 四耗 可变条件 true 可用 false 不可用
     * @param string $mdz
     * @param array $deArray
     * @param bool $isSanhe
     * @return int
     */
    protected function checkSiHao2(string $mdz, array $deArray, bool $isSanhe): int
    {
        if (in_array($mdz, ['丑', '未', '戌']) && $isSanhe) {
            return 1;
        }
        if (in_array('天德合', $deArray) && in_array($mdz, ['寅', '申'])) {
            return 2;
        }
        if ($mdz == '巳' && in_array('月德合', $deArray)) {
            return 4;
        }
        if ($mdz == '辰') {
            if (array_intersect(['天德', '月德'], $deArray) || $isSanhe) {
                return 3;
            }
        }
        return 0;
    }
}
