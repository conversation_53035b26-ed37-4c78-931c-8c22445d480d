<?php
// +----------------------------------------------------------------------
// | 吉日公共代码
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits\jiri;

use app\lib\bazi\BaziExt;
use app\lib\new2021\Feipan;
use app\lib\Utils;
use calendar\exceptions\Exception;
use calendar\SolarTerm;
use think\facade\Cache;

trait JiRiBaseTraits
{
    /**
     * 成局/旺相/得地
     * @param array $jiNian
     * @return string
     */
    protected function getChenJuWanDe(array $jiNian): string
    {
        $bool = $this->getChenJu($jiNian);
        if (!empty($bool)) {
            return $bool;
        }
        $wr = BaziExt::$wr;
        $IsWr = $wr[$jiNian['d'][0]][$jiNian['m'][1]];
        if ($IsWr) {
            return '旺';
        }
        return $this->getDeDi($jiNian);
    }

    /**
     * 判断是否成局
     * @param array $jiNian
     * @return string
     */
    protected function getChenJu(array $jiNian): string
    {
        $bool = '';
        $dzAllList = [$jiNian['y'][1], $jiNian['m'][1], $jiNian['d'][1], $jiNian['h'][1]];
        $tgAllList = [$jiNian['y'][0], $jiNian['m'][0], $jiNian['d'][0], $jiNian['h'][0]];
        // 三会
        $listSanLiu = ['丑亥子', '卯寅辰', '午巳未', '戌申酉'];
        // 三合
        $listSanHe = ['子申辰', '午寅戌', '亥卯未', '丑巳酉'];
        // 六合
        $listLiuHe = ['子丑', '寅亥', '卯戌', '辰酉', '巳申', '午未'];
        // 半三合
        $listHalfSanHe = [
            '子申', '午寅', '亥卯', '巳酉', '子辰', '午戌', '卯未', '丑酉', '申辰', '寅戌', '亥未', '丑巳',
        ];
        // 天干五合
        $listTgWuHe = ['己甲', '乙庚', '丙辛', '丁壬', '戊癸'];
        // 地支三三组合
        $SanLiuCheckList = [
            [$jiNian['y'][1], $jiNian['m'][1], $jiNian['d'][1]],
            [$jiNian['y'][1], $jiNian['m'][1], $jiNian['h'][1]],
            [$jiNian['y'][1], $jiNian['d'][1], $jiNian['h'][1]],
            [$jiNian['m'][1], $jiNian['d'][1], $jiNian['h'][1]],
        ];
        $tgLiuHeCHeck = Utils::combination($tgAllList, 2);

        $liuHeCheckList = Utils::combination($dzAllList, 2);
        if ($this->checkIntersect($SanLiuCheckList, $listSanLiu)) {
            $bool = '三会';
        }
        if ($this->checkIntersect($SanLiuCheckList, $listSanHe)) {
            $bool = '三合';
        }
        if (empty($bool) && $this->checkIntersect($liuHeCheckList, $listLiuHe)) {
            $bool = '六合';
        }
        if (!$bool) {
            if ($this->checkIntersect($tgLiuHeCHeck, $listTgWuHe) && $this->checkIntersect($liuHeCheckList, $listHalfSanHe)) {
                $bool = '半三合';
            }
        }
        return $bool;
    }

    /**
     * 判断日干得地
     * @param array $jiNian
     * @return string
     */
    protected function getDeDi(array $jiNian): string
    {
        // 除月支之外的其他地支与日干符合条件为得地
        // 长生
        $listChanShen = [
            '甲亥', '乙午', '丙寅', '丁酉', '戊寅', '己酉', '庚巳', '辛子', '壬申', '癸卯',
        ];
        // 冠带
        $listGuan = [
            '甲丑', '乙辰', '丙辰', '丁未', '戊辰', '己未', '庚未', '辛戌', '壬戌', '癸丑',
        ];
        // 临官
        $listLin = [
            '甲寅', '乙卯', '丙巳', '丁午', '戊巳', '己午', '庚申', '辛酉', '壬亥', '癸子',
        ];
        // 帝旺
        $listDi = [
            '甲卯', '乙寅', '丙午', '丁巳', '戊午', '己巳', '庚酉', '辛申', '壬子', '癸亥',
        ];
        $check = [
            $jiNian['d'][0] . $jiNian['y'][1],
            $jiNian['d'][0] . $jiNian['d'][1],
            $jiNian['d'][0] . $jiNian['h'][1],
        ];
        if (array_intersect($check, $listChanShen)) {
            return '长生';
        }
        if (array_intersect($check, $listGuan)) {
            return '冠带';
        }
        if (array_intersect($check, $listLin)) {
            return '临官';
        }
        if (array_intersect($check, $listDi)) {
            return '帝旺';
        }
        return '';
    }

    /**
     * 动工方位
     * @param array $jiNian
     * @return array
     */
    protected function getDongGongPos(array $jiNian): array
    {
        // 流日干支
        $list = [
            [
                ['庚午', '辛未', '壬申', '丁酉', '戊戌', '己亥', '丙子', '丁丑', '戊寅'],
                ['正东', '正北', '东北', '西南', '正南'],
            ],
            [
                ['癸酉', '甲戌', '乙亥', '庚子', '辛丑', '壬寅', '癸卯', '甲辰', '乙巳'],
                ['东南', '东北', '正东', '正西', '西南'],
            ],
            [
                ['己卯', '庚辰', '辛巳', '丙午', '丁未', '戊申'],
                ['西北', '西南', '正西', '正东', '东北'],
            ],
            [
                ['壬午', '癸未', '甲申', '己酉', '庚戌', '辛亥'],
                ['正西', '正南', '西南', '东北', '正北'],
            ],
            [
                ['乙酉', '丙戌', '丁亥', '壬子', '癸丑', '甲寅'],
                ['东北', '西北', '正北', '正南', '东南'],
            ],
            [
                ['戊子', '己丑', '庚寅', '乙卯', '丙辰', '丁巳'],
                ['正南', '正东', '东南', '西北', '正西'],
            ],
            [
                ['甲子', '乙丑', '丙寅', '辛卯', '壬辰', '癸巳', '戊午', '己未', '庚申'],
                ['正北', '正西', '西北', '东南', '正东'],
            ],
            [
                ['丁卯', '戊辰', '己巳', '甲午', '乙未', '丙申', '辛酉', '壬戌', '癸亥'],
                ['西南', '东南', '正南', '正北', '西北'],
            ],
        ];

        $list3 = [
            '子' => '南', '丑' => '东', '寅' => '北', '卯' => '西', '辰' => '南', '巳' => '东',
            '午' => '北', '未' => '西', '申' => '南', '酉' => '东', '戌' => '北', '亥' => '西',
        ];
        $ddz = $jiNian['d'][1];
        $sha = $list3[$ddz];
        // $nian = $listNianXiongPos[$jiNian['y'][1]];
        $day = implode('', $jiNian['d']);
        $res = [];
        foreach ($list as $v) {
            if (in_array($day, $v[0])) {
                $res = $v[1];
                break;
            }
        }
        if (empty($res)) {
            $res = $list[0][1];
        }
        $result = [];
        foreach ($res as $v) {
            // if (in_array($v, $nian)) {
            //     continue;
            // }
            if (str_contains($v, $sha)) {
                continue;
            }
            if (count($result) >= 2) {
                break;
            }
            $result[] = $v;
        }
        return $result;
    }

    /**
     * 贵登天门时
     * @param string $jieqi 节气
     * @param string $str 日干+时干支
     * @return bool
     */
    protected function getTianMen(string $jieqi, string $str): bool
    {
        $listTianMen = [
            [
                '甲丁卯', '甲癸酉', '乙丙戌', '丙己亥', '丁辛丑', '戊乙卯', '戊辛酉', '己丙寅', '庚己卯', '庚乙酉', '辛丙申', '壬丁未', '癸丁巳',
            ],
            [
                '乙乙酉', '丙戊戌', '丁庚子', '戊甲寅', '戊庚申', '己乙丑', '己癸酉', '庚戊寅', '庚甲申', '辛辛卯', '辛乙未', '壬丙午', '癸丙辰',
            ],
            [
                '丁己酉', '丁辛亥', '戊癸丑', '戊己未', '己甲子', '己壬申', '庚丁丑', '庚癸未', '辛庚寅', '辛甲午', '壬乙巳', '癸乙卯',
            ],
            [
                '丙戊戌', '丁戊申', '丁庚戌', '戊壬子', '戊戊午', '己辛未', '己乙亥', '庚丙子', '庚壬午', '庚己丑', '辛癸巳', '壬壬寅', '壬甲辰', '癸甲寅',
            ],
            [
                '乙丙戌', '丙丁酉', '丁丁未', '戊丁巳', '戊癸亥', '己庚午', '己甲戌', '庚辛巳', '庚丁亥', '辛戊子', '辛壬辰', '壬癸卯', '壬辛丑',
            ],
            [
                '乙乙酉', '丙丙申', '丁丙午', '戊丙辰', '戊壬戌', '己己巳', '庚庚辰', '庚丙戌', '辛辛卯', '辛己亥', '壬庚子', '壬壬寅', '癸甲寅',
            ],
            [
                '甲癸酉', '乙甲申', '丙乙未', '丁乙巳', '戊乙卯', '戊辛酉', '己戊辰', '庚己卯', '庚乙酉', '辛戊戌', '壬辛亥', '癸癸丑',
            ],
            [
                '甲丙寅', '甲壬申', '乙己卯', '乙癸未', '丙乙未', '丁甲辰', '己丁卯', '辛丁酉', '壬庚戌', '癸壬子',
            ],
            [
                '甲乙丑', '甲辛未', '乙戊寅', '乙壬午', '丙辛卯', '丙癸巳', '丁癸卯', '壬己酉', '癸辛酉', '癸癸亥',
            ],
            [
                '甲庚午', '甲甲子', '乙丁丑', '乙辛巳', '丙壬辰', '丙庚寅', '癸庚申', '癸壬戌',
            ],
            [
                '甲己巳', '甲乙亥', '乙丙子', '乙庚辰', '丙己丑', '丁癸卯', '己戊辰', '癸己未', '癸辛酉',
            ],
            [
                '甲戊辰', '甲甲戌', '乙己卯', '乙丁亥', '丙戊子', '丁壬寅', '己丁卯', '壬戊申', '癸戊午', '癸庚申',
            ],
        ];
        $listJieQi = [
            ['雨水', '惊蛰'], ['春分', '清明'], ['谷雨', '立夏'], ['小满', '芒种'], ['夏至', '小暑'], ['大暑', '立秋'],
            ['处暑', '白露'], ['秋分', '寒露'], ['霜降', '立冬'], ['小雪', '大雪'], ['冬至', '小寒'], ['大寒', '立春'],
        ];
        $key = 0;
        foreach ($listJieQi as $k => $v) {
            if (in_array($jieqi, $v)) {
                $key = $k;
                break;
            }
        }
        return in_array($str, $listTianMen[$key]);
    }

    /**
     * 黄道吉时
     * @param string $str 日支+时支
     * @return bool
     */
    protected function getHuanDaoJiShi(string $str): bool
    {
        $list = [
            '寅丑', '寅辰', '寅巳', '寅未', '卯卯', '卯午', '卯未', '卯酉', '辰巳', '辰申', '辰酉', '辰亥',
            '巳未', '巳戌', '巳亥', '巳丑', '午酉', '午子', '午丑', '午卯', '未亥', '未寅', '未卯', '未巳',
            '申丑', '申辰', '申巳', '申未', '酉卯', '酉午', '酉未', '酉酉', '戌巳', '戌申', '戌酉', '戌亥',
            '亥未', '亥戌', '亥亥', '亥丑', '子酉', '子子', '子丑', '子卯', '丑亥', '丑寅', '丑卯', '丑巳',
        ];
        return in_array($str, $list);
    }

    /**
     * 判断数组交集
     * @param array $arr
     * @param array $arr2
     * @return bool
     */
    private function checkIntersect(array $arr, array $arr2): bool
    {
        $bool = false;
        foreach ($arr as $v) {
            sort($v);
            $vStr = implode('', $v);
            if (in_array($vStr, $arr2)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 求四离 四绝 公历
     * 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
     * @param int $year
     * @return array
     */
    protected function getJieQiDay(int $year): array
    {
        $list = [
            '春分', '夏至', '秋分', '冬至', '立春', '立夏', '立秋', '立冬',
        ];
        $jieqi = $this->getAllJieQi($year);
        $day = [];
        foreach ($jieqi as $k => $v) {
            if (in_array($k, $list)) {
                $day[] = date('Y-m-d', (strtotime($v) - 86400));
            }
        }
        return $day;
    }

    /**
     * 获得指定年份的节气
     * @param int $year
     * @return array
     */
    protected function getAllJieQi(int $year): array
    {
        $cacheKeyName = "jieQiYear/{$year}";
        $data = Cache::get($cacheKeyName, false);
        if (false === $data) {
            $data = SolarTerm::getAllJieQi($year);
            Cache::set($cacheKeyName, $data, 3600);
        }
        return $data;
    }

    /**
     * 风水建议
     * @param string $otime
     * @return array
     * @throws Exception
     */
    protected function getFengShui(string $otime): array
    {
        $timeAmp = strtotime($otime);
        $year = (int)date('Y', $timeAmp);
        $m = (int)date('m', $timeAmp);
        if ($m >= 12) {
            $year++;
        }
        $otime = $year . '-06-01';
        $feipan = new Feipan();
        $list = $feipan->info($otime);

        $year = date('Y', strtotime($otime));
        $listInfo = [
            1 => '【year】年一白贪狼星飞临【postion】。一白星是吉星，主人缘、桃花、情缘、个人魅力等，在八卦为坎卦，五行为水。代表清净、安宁，适合人们休养和保养的吉祥之气，属于三大吉星之一。<br />【year】年若这个方位布局得当，提升吉气，则家中人缘良好，桃花运旺盛，个人情缘和睦，个人魅力好，在事业有做领导的也会领导魅力增加，有助于正当事业的提升。<br />若当年此方位布局不当，有不好的事物压制此方位，或者此方位有垃圾或者杂物或者风水煞气，都会影响此方面的运势，造成个人烂桃花多，姻缘多败少成，事业容易遇到小人，难以升职等，所以需要重视。<br />合理布局方法：【year】年【postion】应该要干净整洁，不放杂物，而【postion】外的环境也最好不要有垃圾场，医院，寺庙等建筑；在住宅的【postion】适合放白色水晶球，粉色水晶球，以及绿植类的开运植物，富贵竹，发财树等都可以，若有不好的煞气，需要针对性的选择风水物以及方法化解开运。',
            2 => '【year】年二黑巨门星飞临【postion】。二黑巨门星是凶星，又称为病符星，飞临方位会主导疾病和伤痛以及健康相关的问题，这个方位也是每年要重点关注的方位，以化解疾病灾凶，保一年健康平安。<br />二黑巨门星代表坤卦，在五行为土，通常的化解方法是用五行金来化解。所以，【year】年在家里的【postion】可以放置铜葫芦，或者真五帝钱（顺治、康熙、雍正、乾隆、嘉庆，五个皇帝名号的铜钱币用红绳串成的吊坠）来摆放化解，效果很好。<br />另外，此方位如果有难以化解的大门，卧室门，卫生间等，最好门后挂中国结或者五帝钱；也可以在当年年初的时候，去趟医院做下健康检查，或者抽血检验，以主动应验不利，则可保一年安康。还可以做一些力所能及的慈善之事，初一和十五到河边放生鱼类或乌龟等以祈福，都是很好的方法。',
            3 => '【year】年三碧禄存星飞临【postion】。禄存星是一颗凶星，也是是非之星，代表是非，矛盾，争讼等现象。在八卦中代表震卦，五行为木。此方位布局得当则利于人缘和睦，事业顺利，诉讼可赢，官司有理，尤其利于家里有做律师、法官、辩论、运动员等职业的人；若是布局不当，则会遭遇口舌是非，甚至是官非的风险。<br />【year】年【postion】宜静不宜动，宜化泄不宜生旺。<br />合理布局方法：此方位可以摆放石象摆件或者石狮子摆件以求吉祥，也可以摆放龙泉宝剑以镇凶星，宝剑一定要平放，剑头朝外；此方位不要摆放绿植，当年这个方位不要动土。若提前感知有官非或者争讼之事，可以在年初去趟检察院或者法院一趟，以主动应验以化解。',
            4 => '【year】年四绿文曲星飞临【postion】。文曲星是一颗中吉的星，主要是代表学业、考运、文运、利文职事业等，布局得当则考运顺利，文章焕发，文职工作者文思泉涌，事业顺利；如果此方位布局不好，有煞气压制，那么考试失败，事业受阻，文思不顺。<br />文曲星是代表巽卦，五行为木，当年力量还是比较强的，是比较利于文章科考的，有明年参加各种考试的朋友，对这个方位要好好利用一下，定然会考试顺利。<br />有孩子参加高考，或者家长参加各种资格考试的朋友，可以在家里的【postion】摆放学习桌，或者是卧室的【postion】摆放学习桌，如果格局所限，做不到的话，也可以在学习桌摆放文昌塔，富贵竹(四支)，或者毛笔架 (挂放四支毛笔) 则形成风水意向峦头，有利催旺文昌运。',
            5 => '【year】年五黄廉贞星飞临【postion】。五黄廉贞星是一颗凶星，也称为五黄煞，主凶灾、祸患。所以，廉贞星所到之处，当年都是不吉的方位，千万不能动土装修，不宜摆放红色，紫色，黄色物品，不宜摆放易燃易爆物品，以及一些化工危险品，要清理干净。此方位每年都要重点关注。<br />廉贞星五行为土，在八卦的中宫。【year】年到【postion】，布局得当则平安无事，无灾无凶，若是此方位有煞气，激发五黄煞则有凶险，尤其对于经常开车，经常外出，或者做高风险行业的人，则一定要布局好。<br /> 合理布局方法：此方位可以摆放五帝钱、铜铃铛、龙泉宝剑、铜葫芦等金属物品以化泄五黄力量，当年宜静不宜动。家中有做高危行业的人员，不妨在年初去医院抽血体检或献血，以主动应验血光之灾，则可预防意外血光。另外，可以通过适当做慈善，做善事，初一和十五放生鱼类和龟类灵性动物，以求吉祥安康。',
            6 => '【year】年六白武曲星飞临【postion】。武曲星是一颗吉星，在八卦是乾卦，五行为金。主要代表贵气、横财、偏财、武职、权势等。此方位布局得当则利于家人升职，尤其是武职工作的人，易得偏财和意外之财，可结交权势之人等；如果布局不当，则无贵人帮扶，甚至遭遇小人，无大财，挣钱费力。<br />【year】年飞临【postion】，这个方位布局好，可以增运，主宅內人员的工作进取，有步步高升的气势，且易得官方之提拔，或易得贵人相助。<br />方位布局方法：此方位可以摆放铜八卦镜以遮挡不良煞气，也可以摆放风水鱼缸，麒麟等催旺六白星，使得财气顺畅，也可以摆放五行湿土类的物品，有院子的话，这个方位是草坪或者菜园也是不错的风水格局。',
            7 => '【year】年七赤破军星飞临【postion】。破军星是一颗凶星，为惊门，主争斗，兵器，破败，耗财等。若方位布局得当为欢声笑语，财运好，添置资产或不动产；若布局不当有口舌是非，打架斗殴，或者刀兵之伤。<br />破军星在八字为兑卦，五行为金，个性凶残。常会引发是非官灾，以及盗贼之事。但此星若能合理利用，对于做偏门行业以及艺术类、娱乐行业的人则会非常有利。运用的好可以催旺财运，偏财降临，布局不当不但口舌是非，而且也会遭遇偷盗，甚至桃花劫等。<br />方位合理布局方法：一般用麒麟摆件化煞，或者水晶类以及五行为水的开运物来化泄煞气，还可以用国旗这种比较有正能量的物件来开运（对于家中有做官的人效果更好），这个方位最好不要是厕所或者厨房，如果是的话需要化解，以泰山石可以镇之以形化煞。',
            8 => '【year】年八白左辅星飞临【postion】。左辅星是一颗吉星，代表正财、不动产、事业、生育等。是目前八运当中最吉利的星，也是当运星神，所到之处，财运大好，工作事业顺利，有利升职加薪，尤其利于体制内的工作，对于企事业单位内工作的人员更应该催旺这颗星。<br />左辅星在八卦为艮卦，五行为土。可以增旺土之五行以增旺吉气。<br />方位合理布局方法 : 【year】年可在【postion】摆放陶瓷类摆件，瓷瓶，陶瓷牛摆件，或者玉石类的摆件，平安扣摆件，如意摆件等，都是可以催旺吉气，大利正财和事业运势的，而且可以保平安纳祥。',
            9 => '【year】年九紫右弼星飞临【postion】。右弼星是一颗吉星，代表喜庆，喜事，姻缘，感情，生子，名气等，此方位布局得当会有喜事临 门，姻缘不错，感情幸福，有添丁增财之喜事；若此方位不吉利，则不会有对应的喜庆之事，甚至是好事办砸，乐极生悲。<br />右弼星在八卦为离卦，五行为火。【year】年在【postion】，在此方位要保持整洁、干净、气场流通，光线充足，不要堆放杂物以及晦气之物，以免影响喜庆运气的到来。<br />合理布局方法：此方位保持干燥、整洁，不能阴暗潮湿，最好光线明亮，如果此方位是厕所，请夜晚开长明灯，厕所门口摆放红地毯。另外，此方也比较适合为书房，书桌以及办公室，此方位适合摆放花瓶 (插桃花) ，也适合养绿植但不能是水养，只能是土养的绿植，而且是大叶的吉祥之物，如：富贵竹，发财树，幸福树等。',
        ];
        $table = [];
        $info = [];
        foreach ($list as $v) {
            $posNum = $v['pos_num'];

            $pos = $v['pos'] . '方';
            if ($pos == '正中方') {
                $pos = '中宫';
            }
            $table[$posNum] = [
                't' => '',
                'pos' => $pos,
                'xing' => $v['y_star'],
                'name' => $v['y_title'],
            ];
            $yNum = $v['y'];
            $explain = $listInfo[$yNum];
            $replacePos = "【{$pos}】";
            $explain = str_replace("【year】", $year, $explain);
            $explain = str_replace("【postion】", $replacePos, $explain);
            $pos = ($pos == '中宫' ? '中宫方位' : $pos);
            $info[] = [
                'pos' => $pos,
                'xing' => $v['y_star'],
                'name' => $v['y_title'],
                'info' => $explain,
            ];
        }
        ksort($table);
        return [
            'table' => array_values($table),
            'info' => $info,
        ];
    }
}
