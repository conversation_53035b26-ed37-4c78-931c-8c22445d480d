<?php
// +----------------------------------------------------------------------
// | JiRiCheckTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use think\helper\Str;

trait JiRiCheckTraits
{
    /**
     * 不将
     * @param int $month 农历月份数字
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkBuJian(int $month, string $rgz): bool
    {
        $list = [
            1 => ['辛亥', '辛丑', '辛卯', '庚子', '庚寅', '己亥', '己卯', '丁亥', '丁丑', '丁卯', '丙子', '丙寅'],
            2 => ['庚戌', '庚子', '庚寅', '己亥', '己丑', '丁亥', '丁丑', '丙戌', '丙子', '丙寅', '乙亥', '乙丑'],
            3 => ['己酉', '己亥', '己丑', '丁酉', '丁亥', '丁丑', '丙戌', '丙子', '乙酉', '乙亥', '乙丑', '甲戌', '甲子'],
            4 => ['丁酉', '丁亥', '丙申', '丙戌', '丙子', '乙酉', '乙亥', '甲申', '甲戌', '甲子', '戊申', '戊戌', '戊子'],
            5 => ['丙申', '丙戌', '乙未', '乙酉', '乙亥', '甲申', '甲戌', '戊申', '戊戌', '癸未', '癸酉', '癸亥'],
            6 => ['乙未', '乙酉', '甲午', '甲申', '甲戌', '戊午', '戊申', '戊戌', '癸未', '癸酉', '壬午', '壬申', '壬戌'],
            7 => ['乙巳', '乙未', '乙酉', '甲午', '甲申', '戊午', '戊申', '癸巳', '癸未', '癸酉', '壬午', '壬申'],
            8 => ['甲辰', '甲午', '甲申', '戊辰', '戊午', '戊申', '癸巳', '癸未', '壬辰', '壬午', '壬申', '辛巳', '辛未'],
            9 => ['戊辰', '戊午', '癸卯', '癸巳', '癸未', '壬辰', '壬午', '辛卯', '辛巳', '辛未', '庚辰', '庚午'],
            10 => ['癸卯', '癸巳', '壬寅', '壬辰', '壬午', '辛卯', '辛巳', '庚寅', '庚辰', '庚午', '己卯', '己巳'],
            11 => ['壬寅', '壬辰', '辛丑', '辛卯', '辛巳', '庚寅', '庚辰', '己丑', '乙卯', '己巳', '丁丑', '丁卯', '丁巳'],
            12 => ['辛丑', '辛卯', '庚子', '庚寅', '庚辰', '己丑', '己卯', '丁丑', '丁卯', '丙子', '丙寅', '丙辰'],
        ];
        $bool = false;
        $list2 = $list[$month] ?? [];
        if (in_array($rgz, $list2)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 季分
     * @param int $month 农历月份数字
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkJiFen(int $month, string $rgz): bool
    {
        $list = [
            1 => ['壬午', '戊子', '丙午', '壬子', '辛未', '己未', '乙卯', '癸卯'],
            2 => ['戊子', '乙未', '癸丑'],
            3 => ['戊寅', '壬寅', '甲寅', '丁卯', '己卯', '庚午'],
            4 => ['乙卯', '己卯', '丁卯', '辛卯', '癸卯'],
            5 => ['乙丑', '丁丑', '己丑', '辛丑', '癸丑'],
            6 => ['己卯', '戊寅', '庚辰', '己卯'],
            7 => ['丙子', '壬子', '丙辰', '己未'],
            8 => ['乙丑', '丁丑', '己丑', '癸丑', '己巳'],
            9 => ['己卯', '己巳', '丙午', '己未'],
            10 => ['丁卯', '辛未', '戊辰', '丁未', '乙卯'],
            11 => ['戊辰', '甲辰', '丙辰'],
            12 => ['戊寅', '壬寅', '甲寅', '戊辰', '己巳', '癸巳', '乙卯'],
        ];
        $bool = false;
        $list2 = $list[$month] ?? [];
        if (in_array($rgz, $list2)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 岁德
     * @param string $str 年份天干+日期天干
     * @return bool
     */
    protected function checkSuiDe(string $str): bool
    {
        $list = ['甲甲', '乙庚', '丙丙', '丁壬', '戊戊', '己甲', '庚庚', '辛丙', '壬壬', '癸戊'];
        return in_array($str, $list);
    }

    /**
     * 岁德合
     * @param string $str 年份天干+日期天干
     * @return bool
     */
    protected function checkSuiDeHe(string $str): bool
    {
        $list = ['甲己', '乙乙', '丙辛', '丁丁', '戊癸', '己己', '庚乙', '辛辛', '壬丁', '癸癸'];
        return in_array($str, $list);
    }

    /**
     * 红鸾天喜
     * @param string $str 月支+日支
     * @return bool
     */
    protected function checkHongLuanTianXi(string $str): bool
    {
        $list = ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'];
        return in_array($str, $list);
    }

    /**
     * 天德
     * @param string $str 月支+日支或月支+日干
     * @return bool
     */
    protected function checkTianDe(string $str): bool
    {
        $list = ['寅丁', '卯申', '辰壬', '巳辛', '午亥', '未甲', '申癸', '酉寅', '戌丙', '亥乙', '子巳', '丑庚'];
        return in_array($str, $list);
    }

    /**
     * 天德合
     * @param string $str 月支+日支或月支+日干
     * @return bool
     */
    protected function checkTianDeHe(string $str): bool
    {
        $list = ['寅壬', '卯巳', '辰丁', '巳丙', '午寅', '未己', '申戊', '酉亥', '戌辛', '亥庚', '子申', '丑乙'];
        return in_array($str, $list);
    }

    /**
     * 月德
     * @param string $str 月支+日支或日干
     * @return bool
     */
    protected function checkYueDe(string $str): bool
    {
        $list = ['寅丙', '卯甲', '辰壬', '巳庚', '午丙', '未甲', '申壬', '酉庚', '戌丙', '亥甲', '子壬', '丑庚'];
        return in_array($str, $list);
    }

    /**
     * 月德合
     * @param string $str 月支+日支，月支+日干
     * @return bool
     */
    protected function checkYueDeHe(string $str): bool
    {
        $list = ['寅辛', '卯己', '辰丁', '巳乙', '午辛', '未己', '申丁', '酉乙', '戌辛', '亥己', '子丁', '丑乙'];
        return in_array($str, $list);
    }

    /**
     * 天赦
     * @param string $mdz 月地支
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkTianSe(string $mdz, string $rgz): bool
    {
        $bool = false;
        switch ($mdz) {
            case '寅':
            case '卯':
            case '辰':
                if ($rgz === '戊寅') {
                    $bool = true;
                }
                break;
            case '巳':
            case '午':
            case '未':
                if ($rgz === '甲午') {
                    $bool = true;
                }
                break;
            case '申':
            case '酉':
            case '戌':
                if ($rgz === '戊申') {
                    $bool = true;
                }
                break;
            default:
                if ($rgz === '甲子') {
                    $bool = true;
                }
                break;
        }
        return $bool;
    }

    /**
     * 天愿
     * @param string $str 月支+日干支
     * @return bool
     */
    protected function checkTianYuan(string $str): bool
    {
        $list = ['寅乙亥', '卯甲戌', '辰乙酉', '巳丙申', '午丁未', '未戊午', '申己巳', '酉庚辰', '戌辛卯', '亥壬寅', '子癸丑', '丑甲子'];
        return in_array($str, $list);
    }

    /**
     * 月恩
     * @param string $str 月支+日干
     * @return bool
     */
    protected function checkYueEn(string $str): bool
    {
        $list = ['寅丙', '卯丁', '辰庚', '巳己', '午戊', '未辛', '申壬', '酉癸', '戌庚', '亥乙', '子甲', '丑辛'];
        return in_array($str, $list);
    }

    /**
     * 四相
     * @param string $mdz 月地支
     * @param string $rtg 日天支
     * @return bool
     */
    protected function checkSiXian(string $mdz, string $rtg): bool
    {
        $bool = false;
        switch ($mdz) {
            case '寅':
            case '卯':
            case '辰':
                if (in_array($rtg, ['丙', '丁'])) {
                    $bool = true;
                }
                break;
            case '巳':
            case '午':
            case '未':
                if (in_array($rtg, ['戊', '己'])) {
                    $bool = true;
                }
                break;
            case '申':
            case '酉':
            case '戌':
                if (in_array($rtg, ['壬', '癸'])) {
                    $bool = true;
                }
                break;
            default:
                if (in_array($rtg, ['甲', '乙'])) {
                    $bool = true;
                }
                break;
        }
        return $bool;
    }

    /**
     * 时德
     * @param string $str 月支+日支
     * @return bool
     */
    protected function checkShiDe(string $str): bool
    {
        $list = ['寅午', '卯午', '辰午', '巳辰', '午辰', '未辰', '申子', '酉子', '戌子', '亥寅', '子寅', '丑寅'];
        return in_array($str, $list);
    }

    /**
     * 判断是否三合
     * @param string $dz 月支+日支
     * @return bool
     */
    private function getSanHeByDz(string $dz): bool
    {
        $sanheList = [
            '子辰', '丑巳', '寅午', '卯亥', '辰子', '巳酉', '午寅', '未亥', '申辰', '酉巳',
            '戌午', '亥卯', '子申', '丑酉', '寅戌', '卯未', '辰申', '巳丑', '午戌', '未卯',
            '申子', '酉丑', '戌寅', '亥未',
        ];
        return in_array($dz, $sanheList);
    }

    /**
     * 显星
     * @param int $m 农历月份数字
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkXianXing(int $m, string $rgz): bool
    {
        $list = [
            1 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            2 => ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            3 => ['己丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            4 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            5 => ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            6 => ['己丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            7 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            8 => ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            9 => ['己丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            10 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            11 => ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            12 => ['己丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
        ];
        $bool = false;
        $list2 = $list[$m] ?? [];
        if (in_array($rgz, $list2)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 曲星
     * @param int $m 农历月份数字
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkQuXing(int $m, string $rgz): bool
    {
        $list = [
            1 => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            2 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            3 => ['丙寅', '乙卯', '甲申', '癸巳', '壬寅', '辛卯', '庚申'],
            4 => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            5 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            6 => ['丙寅', '乙卯', '甲申', '癸巳', '壬寅', '辛卯', '庚申'],
            7 => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            8 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            9 => ['丙寅', '乙卯', '甲申', '癸巳', '壬寅', '辛卯', '庚申'],
            10 => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            11 => ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            12 => ['丙寅', '乙卯', '甲申', '癸巳', '壬寅', '辛卯', '庚申'],
        ];
        $bool = false;
        $list2 = $list[$m] ?? [];
        if (in_array($rgz, $list2)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 传星
     * @param int $m 农历月份数字
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkChuanXing(int $m, string $rgz): bool
    {
        $list = [
            1 => ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            2 => ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            3 => ['己巳', '戊寅', '己亥', '丙申', '乙巳', '甲寅', '癸亥'],
            4 => ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            5 => ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            6 => ['己巳', '戊寅', '己亥', '丙申', '乙巳', '甲寅', '癸亥'],
            7 => ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            8 => ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            9 => ['己巳', '戊寅', '己亥', '丙申', '乙巳', '甲寅', '癸亥'],
            10 => ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            11 => ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            12 => ['己巳', '戊寅', '己亥', '丙申', '乙巳', '甲寅', '癸亥'],
        ];
        $bool = false;
        $list2 = $list[$m] ?? [];
        if (in_array($rgz, $list2)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 大利月
     * @param string $sx 生肖
     * @return array
     */
    protected function getDaLiYue(string $sx): array
    {
        $list = [
            '鼠' => [12, 6], '牛' => [11, 5], '虎' => [8, 2], '兔' => [1, 7], '龙' => [4, 10], '蛇' => [3, 9],
            '马' => [12, 6], '羊' => [11, 5], '猴' => [8, 2], '鸡' => [1, 7], '狗' => [4, 10], '猪' => [3, 9],
        ];
        return $list[$sx];
    }

    /**
     * 小利月
     * @param string $sx
     * @return int[]
     */
    protected function getXiaoLiYue(string $sx): array
    {
        $list = [
            '虎' => [3, 9], '兔' => [6, 12], '龙' => [5, 11], '蛇' => [2, 8], '马' => [1, 7], '羊' => [4, 10],
            '猴' => [3, 9], '鸡' => [6, 12], '狗' => [5, 11], '猪' => [2, 8], '鼠' => [1, 7], '牛' => [4, 10],
        ];
        return $list[$sx];
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'],
            '辰' => ['巳', '酉', '丑'],
            '戌' => ['亥', '卯', '未'],
            '未' => ['申', '子', '辰'],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $str = $k;
                break;
            }
        }
        return $str;
    }

    /**
     * 人专
     * @param int $month 农历月份数字
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkRenZhuan(int $month, string $dgz): bool
    {
        if (in_array($month, [1, 4, 7, 10])) {
            $list = ['辛未', '庚辰', '己丑', '戊戍', '丁未', '丙辰'];
        } elseif (in_array($month, [2, 5, 8, 11])) {
            $list = ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'];
        } else {
            $list = ['己巳', '戊寅', '丁亥', '丙申', '乙巳', '甲寅', '癸亥'];
        }
        return in_array($dgz, $list);
    }

    /**
     * 煞贡
     * @param int $month 农历月份数字
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkShaGong(int $month, string $dgz): bool
    {
        if (in_array($month, [1, 4, 7, 10])) {
            $list = ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'];
        } elseif (in_array($month, [2, 5, 8, 11])) {
            $list = ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'];
        } else {
            $list = ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'];
        }
        return in_array($dgz, $list);
    }

    /**
     * 驿马
     * @param string $mdz 月地支
     * @param string $ddz 日地支
     * @return bool
     */
    protected function checkYiMa(string $mdz, string $ddz): bool
    {
        $list = ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 民日
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkMingRi(string $mdz, string $ddz): bool
    {
        $list = ['寅午', '卯午', '辰午', '巳酉', '午酉', '未酉', '申子', '酉子', '戌子', '亥卯', '子卯', '丑卯'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天马
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianMa(string $mdz, string $ddz): bool
    {
        $list = ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 五富
     * @param string $str 月支+日支
     * @return bool
     */
    protected function checkWuFu(string $str): bool
    {
        $list = ['寅亥', '卯寅', '辰巳', '巳申', '午亥', '未寅', '申巳', '酉申', '戌亥', '亥寅', '子巳', '丑申'];
        return in_array($str, $list);
    }

    /**
     * 月财
     * @param string $dzMd 月支+日支
     * @return bool
     */
    protected function checkYueCai(string $dzMd): bool
    {
        $list = [
            '寅午', '卯乙', '辰巳', '巳未', '午酉', '未亥', '申午', '酉乙', '戌巳', '亥未', '子酉', '丑亥',
        ];
        return in_array($dzMd, $list);
    }

    /**
     * 天财
     * @param string $str
     * @return bool
     */
    protected function checkTianCai(string $str): bool
    {
        $list = ['寅辰', '卯午', '辰申', '巳戌', '午子', '未寅', '申辰', '酉午', '戌申', '亥戌', '子子', '丑寅'];
        return in_array($str, $list);
    }

    /**
     * 母仓
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkMuCang(string $mdz, string $ddz): bool
    {
        $list = [
            '寅' => ['亥', '子'], '卯' => ['亥', '子'], '辰' => ['亥', '子'],
            '巳' => ['寅', '卯'], '午' => ['寅', '卯'], '未' => ['寅', '卯'],
            '申' => ['辰', '戌', '丑', '未'], '酉' => ['辰', '戌', '丑', '未'], '戌' => ['辰', '戌', '丑', '未'],
            '亥' => ['申', '酉'], '子' => ['申', '酉'], '丑' => ['申', '酉'],
        ];
        $list1 = $list[$mdz];
        return in_array($ddz, $list1);
    }

    /**
     * 相日
     * @param string $mdz
     * @param string $ddz
     * @return bool
     */
    protected function checkXiangRi(string $mdz, string $ddz): bool
    {
        $list = [
            '巳' => '寅卯辰', '申' => '巳午未', '亥' => '申酉戌', '寅' => '亥子丑',
        ];
        if (isset($list[$ddz])) {
            if (Str::contains($list[$ddz], $mdz)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 官日
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkGuanRi(string $mdz, string $ddz): bool
    {
        return in_array($mdz . $ddz, ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子']);
    }

    /**
     * 王日
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkWangRi(string $mdz, string $ddz): bool
    {
        $list = ['寅寅', '卯寅', '辰寅', '巳巳', '午巳', '未巳', '申申', '酉申', '戌申', '亥亥', '子亥', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 守日
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkShouRi1(string $mdz, string $ddz): bool
    {
        $list = ['寅酉', '卯酉', '辰酉', '巳子', '午子', '未子', '申卯', '酉卯', '戌卯', '亥午', '子午', '丑午'];
        return in_array($mdz . $ddz, $list);
    }
}
