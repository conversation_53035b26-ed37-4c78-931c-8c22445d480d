<?php
// +----------------------------------------------------------------------
// | CityLnglatTraits
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use app\model\astro\Region;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

trait CityLnglatTraits
{
    /**
     * 获得经纬度
     * @param string $provinces 省份
     * @param string $city 城市
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getlngLat(string $provinces, string $city): array
    {
        $info = Region::getCityInfo($provinces, $city);
        if (empty($info)) {
            return [
                'lng' => 121.47,
                'lat' => 31.24,
            ];
        }
        return [
            'lng' => number_format($info['lng'], 2),
            'lat' => number_format($info['lat'], 2),
        ];
    }
}
