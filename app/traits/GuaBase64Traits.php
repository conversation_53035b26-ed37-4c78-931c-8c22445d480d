<?php
// +----------------------------------------------------------------------
// | 64卦
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\traits;

use calendar\Calendar;

trait GuaBase64Traits
{
    /**
     * 获得
     * @param array $bases 八字相关
     * @param array $newBases 流年八字相关
     * @return array
     */
    public function getGua64GZ(array $bases, array $newBases): array
    {
        $gua = ['乾', '兑', '离', '震', '巽', '坎', '艮', '坤'];
        // $guayy = ['阳阳阳' => '乾', '阴阳阳' => '兑', '阳阴阳' => '离', '阴阴阳' => '震', '阳阳阴' => '巽', '阴阳阴' => '坎', '阳阴阴' => '艮', '阴阴阴' => '坤',];
        $guayy = ['阳阳阳' => '乾', '阳阳阴' => '兑', '阳阴阳' => '离', '阳阴阴' => '震', '阴阳阳' => '巽', '阴阳阴' => '坎', '阴阴阳' => '艮', '阴阴阴' => '坤'];
        $guaNum = $this->getGuaNumBase($bases);
        $guaT = ($guaNum['year'] + $guaNum['month'] + $guaNum['day']) % 8;
        $guaD = (array_sum($guaNum)) % 8;
        $guaT = $guaT == 0 ? 8 : $guaT;
        $guaD = $guaD == 0 ? 8 : $guaD;

        $newGuaNum = $this->getGuaNumBase($newBases);
        $newGuaT = ($newGuaNum['year'] + $newGuaNum['month'] + $newGuaNum['day']) % 6;
        $newGuaT = $newGuaT == 0 ? 6 : $newGuaT;

        // 最终得出命主的挂
        if ($newGuaT <= 3) {
            $temp = $gua[$guaD - 1];
            $tempGua = array_search($temp, $guayy);
            $guaList = preg_split('/(?<!^)(?!$)/u', $tempGua);
            $guaList[$newGuaT - 1] = $guaList[$newGuaT - 1] == '阳' ? '阴' : '阳';
            $guaD = $guayy[implode('', $guaList)];
            $guaT = $gua[$guaT - 1];
        } else {
            $temp = $gua[$guaT - 1];
            $tempGua = array_search($temp, $guayy);
            $guaList = preg_split('/(?<!^)(?!$)/u', $tempGua);
            $guaList[$newGuaT - 4] = $guaList[$newGuaT - 4] == '阳' ? '阴' : '阳';
            $guaD = $gua[$guaD - 1];
            $guaT = $guayy[implode('', $guaList)];
        }
        return [
            $guaD,
            $guaT,
        ];
    }

    /**
     * 根据八字基础返加年月日时的干支序号
     * @param array $bases
     * @return array
     */
    protected function getGuaNumBase(array $bases): array
    {
        $guaDiZhi = Calendar::DI_ZHI;
        $guaMonth = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
        $guaDay = [
            '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
            '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
            '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十',
        ];
        $nongliMonth = mb_strlen($bases['nongli']['m']) >= 3 ? mb_substr($bases['nongli']['m'], 1, 2, 'utf-8') : $bases['nongli']['m'];
        return [
            'year' => array_search($bases['jinian']['y'][1], $guaDiZhi) + 1,
            'month' => array_search($nongliMonth, $guaMonth) + 1,
            'day' => array_search($bases['nongli']['d'], $guaDay) + 1,
            'hour' => array_search($bases['jinian']['h'][1], $guaDiZhi) + 1,
        ];
    }
}
