<?php
// +----------------------------------------------------------------------
// | Jiehunjiri
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v3;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\jiri\JiRiCheckBadTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Jiehunjiri
{
    use JiRiCheckTraits;
    use JiRiCheckBadTraits;

    /**
     * @var Ex 男
     */
    protected Ex $mLunar;

    /**
     * @var Ex 女
     */
    protected Ex $fLunar;

    /**
     * 基础盘
     * @var array
     */
    protected array $basePan;

    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户过滤信息
     * @var array
     */
    protected array $userFiter = [];

    /**
     * 年份对应的节气信息
     * @var array
     */
    protected array $jieQiYear = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            //男姓名
            // 'malename' => I('malename', '', 'trim'),
            // 男出生时间
            'maletime' => input('maletime', '', 'trim'),
            // 男父生肖
            'malefsx' => input('malefsx', 0, 'intval'),
            // 男母生肖
            'malemsx' => input('malemsx', 0, 'intval'),
            // 女姓名
            //'femalename' => I('femalename', '', 'trim'),
            // 女出生时间
            'femaletime' => input('femaletime', '', 'trim'),
            // 女父生肖
            'femalefsx' => input('femalefsx', 0, 'intval'),
            // 女母生肖
            'femalemsx' => input('femalemsx', 0, 'intval'),
            // 起始时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
            // 月分
            'plan_time' => input('plan_time', 1, 'intval'),

        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'maletime|男方出生时间' => ['require', 'isDateOrTime:出生时间'],
                'malefsx|男父生肖' => ['require', 'between:0,12'],
                'malemsx|男母生肖' => ['require', 'between:0,12'],
                'femaletime|女方出生时间' => ['require', 'isDateOrTime:出生时间'],
                'femalefsx|女父生肖' => ['require', 'between:0,12'],
                'femalemsx|女母生肖' => ['require', 'between:0,12'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->mLunar = Ex::date($data['maletime'])->sex(0);
        $this->fLunar = Ex::date($data['femaletime'])->sex(1);
        $this->basePan = [
            'm' => $this->getBasePan($this->mLunar),
            'f' => $this->getBasePan($this->fLunar),
        ];
        $sxF = $this->basePan['f']['shengxiao'];
        $xiaoLi = $this->getXiaoLiYue($sxF);
        $daLi = $this->getDaLiYue($sxF);
        $this->userFiter = [
            'dali' => $daLi,
            'xiaoli' => $xiaoLi,
            'cx' => $this->getUserChongXing(),
        ];
        $jiSanXiang = $this->getJiSanXiang($sxF);
        $mydz = $this->basePan['m']['jinian']['y'][1];
        $fydz = $this->basePan['f']['jinian']['y'][1];
        $sanHe = BaziCommon::getSanHeDz2([[$mydz, $fydz]]);
        if (BaziCommon::getXianChong($mydz . $fydz)) {
            $ugx = '从属相合婚的角度来看，男方与女方的生肖相冲，你们之间会有些小问题，选择吉日成婚可以化解这种不利关系。';
        } elseif (BaziCommon::getXianXin($mydz . $fydz)) {
            $ugx = '从属相合婚的角度来看，男方与女方的生肖相刑，遇事多谦让，选择吉日成婚可以化解这种不利关系，让婚姻状态更加稳定。';
        } elseif (BaziCommon::liuHeDz($mydz . $fydz) || $sanHe[2]) {
            $ugx = '从属相合婚的角度来看，男方与女方的生肖相合，选择吉日成婚可以让婚姻关系更和睦。';
        } else {
            $ugx = '从属相合婚的角度来看，男方与女方的生肖组合中规中矩，选择吉日成婚可以让感情关系更稳定。';
        }
        return [
            'pan' => $this->basePan,
            'xiao_li' => $xiaoLi,
            'da_li' => $daLi,
            'sx_gx' => $ugx,
            'ji_san_xiang' => $jiSanXiang,
            'jiri' => $this->getDayList(),
        ];
    }

    /**
     * 吉日列表
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        $startTime = strtotime($this->orginData['otime']);
        $endTime = strtotime("+{$this->orginData['plan_time']} month", $startTime);
        $limitNum = round(($endTime - $startTime) / 86400);
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳节',
            '10_1' => '寒衣节',
        ];
        $result = [];
        $explain = [];
        $changeExplain = [];
        $userFiter = $this->userFiter;
        $jiNianF = $this->fLunar->getLunarTganDzhi();
        $hourList = $this->getHourUse();
        for ($i = 1; $i <= $limitNum; $i++) {
            $curTime = $startTime + ($i * 86400);
            $cYear = date('Y', $curTime);
            $cGongli = date('Y-m-d', $curTime);
            $jieqiArr = $this->getJieqiByYear($cYear);
            $jieQi = $jieqiArr[$cGongli] ?? '';
            $huangli = Huangli::date($cGongli);
            $base = $huangli->getLunarByBetween();
            $week = Huangli::getWeekChs($curTime);
            $keyStr = $base['_nongli']['y'] . '年' . $base['nongli']['m'];
            $jxArr = $huangli->getJiXiong();
            //$jiShenHl = $this->filterJXShen($jxArr['jishen']);
            $jiShenHl = $jxArr['jishen'];
            $xiongHl = $this->filterJXShen($jxArr['xiong'], 1);
            $zhiRi = $huangli->getZhiRi();
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $hdType = $zhiRi['huan_dao'] == '黑道' ? 0 : 1;
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $nongliM = $base['_nongli']['m'];
            $mdNum = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            $ydz = $jiNian['y'][1];
            $mdz = $jiNian['m'][1];
            $ddz = $jiNian['d'][1];
            $dtg = $jiNian['d'][0];
            $ytg = $jiNian['y'][0];
            $dgz = implode('', $jiNian['d']);
            $dzMd = $mdz . $ddz;
            $chongXingU = [];
            $reasonJr = [];
            $reason = [];
            if (isset($userFiter['cx']['chong'][$ddz])) {
                $chongXingU[] = ['gx' => '冲', 'name' => $userFiter['cx']['chong'][$ddz]];
            }
            if (isset($userFiter['cx']['xing'][$ddz])) {
                $chongXingU[] = ['gx' => '刑', 'name' => $userFiter['cx']['xing'][$ddz]];
            }
            if ($this->checkYangGongOnly($mdNum)) {
                $reasonJr[] = '杨公忌日';
            }
            if ($jieQi == '清明') {
                $reasonJr[] = '清明节';
            }
            foreach ($listNo as $k => $v) {
                if ($k == $mdNum) {
                    $reasonJr[] = $v;
                }
            }
            // 彭祖百忌、杨公忌：三娘煞；披麻日；小红沙日；朔日；清明节；七月半；重阳节；寒衣节；天狗日
            if ($this->checkSanNiangSha($dgz, $base['_nongli']['d'])) {
                $reasonJr[] = '三娘煞';
            }
            // 彭祖忌 亥日例：所有地支为亥的日子都去掉
            if ($ddz === '亥') {
                $reasonJr[] = "彭祖";
            }
            if ($this->checkXiaoHongSha($mdz . $ddz)) {
                $reason[] = '披麻';
            }
            // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉
            // if ($base['_nongli']['d'] == 1) {
            //     $reasonJr[] = '朔日';
            // }
            // if ($this->checkTianGou($jiNianF['y'][1], $ddz)) {
            //     $reasonJr[] = '天狗';
            // }
            // 岁破、月破、受死、往亡
            if ($this->checkShouSi($dzMd)) {
                $reason[] = '受死';
            }
            if ($this->checkSuiPo($ydz . $ddz)) {
                $reason[] = '岁破';
            }
            if ($this->checkYuePo($dzMd)) {
                $reason[] = '月破';
            }
            if ($this->checkWangWang($mdz, $ddz)) {
                $reason[] = '往亡';
            }
            $isLiuHe = BaziCommon::liuHeDz($mdz . $ddz);
            $tianDe = $this->checkTianDe($dzMd) || $this->checkTianDe($mdz . $dtg);
            $yueDe = $this->checkYueDe($dzMd) || $this->checkYueDe($mdz . $dtg);
            $tianDeHe = $this->checkTianDeHe($dzMd) || $this->checkTianDeHe($mdz . $dtg);
            $yueDeHe = $this->checkYueDeHe($dzMd) || $this->checkYueDeHe($mdz . $dtg);
            $tianYuan = $this->checkTianYuan($mdz . $dgz);
            $siXian = $this->checkSiXian($mdz, $dtg);
            $tianSe = $this->checkTianSe($mdz, $dgz);
            $deArray = [];
            if ($tianDe) {
                $deArray[] = '天德';
            }
            if ($yueDe) {
                $deArray[] = '月德';
            }
            if ($tianDeHe) {
                $deArray[] = '天德合';
            }
            if ($yueDeHe) {
                $deArray[] = '月德合';
            }
            $xiaoXiong = [];
            // 劫煞、灾煞、月煞、天吏、五离、五墓、月刑、月厌、厌对、八专
            $jieSha2 = $this->checkJieSha2($mdz, $deArray);
            $zaiSha2 = $this->checkZaiSha2($mdz, $deArray) ? 1 : 0;
            //$daShi2 = $this->checkDaShi2($mdz, $deArray);
            $wuMu2 = $this->checkWuMu2($mdz, $deArray) ? 1 : 0;

            $listYs2 = [
                'yue_de' => $yueDe, 'yue_de_he' => $yueDeHe, 'tian_yuan' => $tianYuan,
                'tian_de' => $tianDe, 'tian_de_he' => $tianDeHe, 'liu_he' => $isLiuHe,
                'si_xian' => $siXian, 'tian_se' => $tianSe,
            ];
            $tianLi2 = $this->checkTianLi3($mdz, $listYs2);
            $yueXing2 = $this->checkYueXin3($mdz, $listYs2);
            $yueSha2 = $this->checkYueSha3($mdz, $listYs2);
            $change = [0 => [], 1 => []];// 0 普通  1 可变
            // 不宜不忌
            $listByBj = 0;
            if ($this->checkJieSha($mdz, $ddz)) {
                if ($jieSha2) {
                    $change[1][] = '劫煞' . $jieSha2;
                    if ($jieSha2 != 4) {
                        $listByBj = 1;
                    }
                } else {
                    $change[0][] = '劫煞';
                }
            }
            if ($this->checkZaiSha($mdz, $ddz)) {
                $change[$zaiSha2][] = '灾煞';
                if ($zaiSha2) {
                    if (!(in_array($mdz, ["寅", "巳", "申", "亥"]) && !($yueDeHe || $tianDeHe))) {
                        $listByBj = 1;
                    }
                }
            }
            if ($this->checkYueSha($mdz, $ddz)) {
                if ($yueSha2) {
                    $change[1][] = '月煞' . $yueSha2;
                } else {
                    $change[0][] = '月煞';
                }
            }
            // 致死（天吏）
            if ($this->checkTianLi($mdz, $ddz)) {
                if ($tianLi2) {
                    $change[1][] = '天吏' . $tianLi2;
                    if (in_array($mdz, ["寅", "巳", "申", "亥"])) {
                        $listByBj = 1;
                    }
                } else {
                    $change[0][] = '天吏';
                }
            }
            if ($this->checkWuMu($mdz, $dgz)) {
                $change[$wuMu2][] = '五墓';
            }
            if ($this->checkYueXin($mdz, $ddz)) {
                if ($yueXing2) {
                    $change[1][] = '月刑' . $yueXing2;
                    if (in_array($mdz, ["寅", "巳"])) {
                        $listByBj = 1;
                    }
                } else {
                    $change[0][] = '月刑';
                }
            }
            if ($dgz == '己酉') {
                $change[1][] = '五离';
            }
            if ($this->checkYueYan($mdz, $ddz)) {
                $yueYan2 = $this->checkYueYan2($mdz, $deArray);
                if ($yueYan2) {
                    $change[1][] = '月厌' . $yueYan2;
                    if (in_array($mdz, ["丑", "寅", "未", "申"])) {
                        $listByBj = 1;
                    }
                } else {
                    $change[0][] = '月厌';
                }
            }
            if ($this->checkYanDui($mdz, $ddz)) {
                if ($deArray || $tianSe) {
                    $change[1][] = '厌对';
                    $listByBj = 1;
                } else {
                    $change[0][] = '厌对';
                }
            }
            if (in_array($dgz, ["丁未", "己未", "庚申", "甲寅", "癸丑"])) {
                if (in_array($dgz, ["丁未", "癸丑"]) && $tianYuan) {
                    $change[1][] = '八专';
                    $listByBj = 1;
                } else {
                    $change[0][] = '八专';
                }
            }

            if ($change[0] && !$hdType) {
                $reason = array_merge($reason, $change[0]);
            }
            $jianChu = $huangli->getJianChu();
            $shaXian = Huangli::getSanSha($ddz);
            $bool = $chongXingU || $reason || $reasonJr;
            $chongDz = BaziCommon::getChongDz($ddz);
            $chongSx = $this->mLunar->getZodiac($chongDz);
            $xiongHl = array_merge($reason, $xiongHl);
            if ($bool && $hdType) {
                $shenSha = '';// 不符合日子且黄道日
            }
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $curTime),
                    'm' => (int)date('m', $curTime),
                    'd' => (int)date('d', $curTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']),
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'type' => $bool ? 0 : 1, // 0 不合 1 符合
                // 'type_cn' => $bool ? '不宜' : '平',
                'type_hl' => $hdType, // 黄道 1 黑道 0
                'position' => $huangli->getPosition(),
                'jianchu' => $jianChu, // 建除
                'shensha' => $shenSha, // 值日神煞
                // 煞向
                'sha_xian' => str_replace('煞', '', $shaXian[0]),
                // 日冲
                'chong_sx' => $chongSx,
                // 正冲
                'zheng_chong' => $this->getZhengChong($dgz),
                // 与用户相冲或相刑结果
                'chong_x_u' => $chongXingU,
                // 原因
                'reason' => $reason,
                // 可变凶神
                'change' => [],
                // 传统节日
                'fest' => $reasonJr,
                // 吉凶神
                'shen' => [],
                // 时辰
                'hour' => [],
            ];
            $num = $result[$keyStr]['num'] ?? 0;
            if (in_array($nongliM, $userFiter['dali'])) {
                $isDaXiaoLi = 'da';
            } elseif (in_array($nongliM, $userFiter['xiaoli'])) {
                $isDaXiaoLi = 'xiao';
            } else {
                $isDaXiaoLi = '';
            }
            if (!$bool) {
                $num++;
                $jiShenHl = $this->filterJXShen($jxArr['jishen']);
                $sanHeDz = [
                    [$ydz, $mdz, $ddz],
                ];
                $sanHe = BaziCommon::getSanHeDz2($sanHeDz);
                $sanHeBool = !empty($sanHe[3]);
                $type = 1;
                // 六合、三合、天愿、民日、天喜、月恩、四相、时德、不将日、天赦、显星、曲星、传星
                $jsXiao = [];
                if ($isLiuHe) {
                    $jsXiao[] = '六合';
                }
                if ($sanHeBool) {
                    $jsXiao[] = '三合';
                }
                if ($tianYuan) {
                    $jsXiao[] = '天愿';
                }
                if ($this->checkMingRi($mdz, $ddz)) {
                    $jsXiao[] = '民日';
                }
                if ($this->checkTianXi($mdz, $ddz)) {
                    $jsXiao[] = '天喜';
                }
                if ($this->checkYueEn($mdz . $dtg)) {
                    $jsXiao[] = '月恩';
                }
                if ($siXian) {
                    $jsXiao[] = '四相';
                }
                if ($this->checkShiDe($mdz . $ddz)) {
                    $jsXiao[] = '时德';
                }
                if ($this->checkBuJian($nongliM, $dgz)) {
                    $jsXiao[] = '不将';
                }
                if ($tianSe) {
                    $jsXiao[] = '天赦';
                }
                if ($this->checkChuanXing($nongliM, $dgz)) {
                    $jsXiao[] = '传星';
                }
                if ($this->checkXianXing($nongliM, $dgz)) {
                    $jsXiao[] = '显星';
                }
                if ($this->checkQuXing($nongliM, $dgz)) {
                    $jsXiao[] = '曲星';
                }
                if ($isDaXiaoLi && !$hdType) {
                    if ($change[1] && $listByBj) {
                        $type = 2;
                    }
                    if ($jsXiao && empty($reason)) {
                        $type = 2;
                    }
                }
                if ($hdType) {
                    $type = 2;
                    if (!$isDaXiaoLi && $deArray) {
                        $type = 3;
                    }
                }
                $changeX = array_merge($change[0], $change[1]);
                if ($isDaXiaoLi) {
                    if ($hdType && array_intersect(["天吏", "五离", "五墓", "月刑", "月厌", "厌对", "八专"], $changeX)) {
                        $type = 3;
                    }
                    if (!$hdType && $deArray) {
                        $type = 3;
                    }
                    if ($hdType) {
                        if ($deArray || $jsXiao) {
                            $type = 4;
                        }
                        if ($change[1] && $listByBj) {
                            $type = 4;
                        }
                    }
                }
                $tmpRes['reason'] = array_merge($deArray, $jsXiao);
                $tmpRes['change'] = $change[1];
                $tmpRes['type'] = $type;
                $tmpRes['hour'] = $hourList[$ddz];
                $jiShenHl = array_merge($tmpRes['reason'], $jiShenHl);
            }

            $tmpRes['shen'] = [
                'jishen' => $jiShenHl,
                'xiong' => $xiongHl,
            ];
            $result[$keyStr]['num'] = $num;
            $result[$keyStr]['title'] = $keyStr;
            $result[$keyStr]['da_xiao'] = $isDaXiaoLi;
            if ($shenSha) {
                $explain[$shenSha] = $this->getExplain($shenSha);
            }
            foreach ($tmpRes['fest'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['reason'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['change'] as $v) {
                $changeExplain[$v] = $this->getExplainChange($v);
            }
            $result[$keyStr]['info'][] = $tmpRes;
        }
        return [
            'list' => array_values($result),
            'explain' => $explain,
            'change' => $changeExplain,
            'type_cn' => [
                0 => '不宜', 1 => '平', 2 => '小吉', 3 => '中吉', 4 => '大吉',
            ],
        ];
    }

    /**
     * 用户基础信息
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBasePan(Ex $lunar): array
    {
        $base = $lunar->getLunarByBetween();
        $kongWang = [];
        foreach ($base['jinian'] as $k => $v) {
            if ($k == 'h') {
                continue;
            }
            $kongWang[$k] = Huangli::getKongWangbyGz(implode('', $v));
        }
        $fate = $lunar->getFate();
        $fateAge = $fate['age'];
        $jiaoYunStr = "+{$fateAge['year']} year {$fateAge['month']} month {$fateAge['day']} day +{$fateAge['hour']} hour";
        $tmpJaoTime = strtotime($jiaoYunStr, $lunar->dateTime->getTimestamp());
        $time = date('Y-m-d H:00:00', $tmpJaoTime);
        $lunarJiao = Ex::date($time);
        $baseJiao = $lunarJiao->getLunarByBetween();
        $jiaoRes = [
            'gongli' => [
                'y' => (int)$lunarJiao->dateTime->format('Y'),
                'm' => (int)$lunarJiao->dateTime->format('m'),
                'd' => (int)$lunarJiao->dateTime->format('d'),
                'h' => (int)$lunarJiao->dateTime->format('h'),
            ],
            'nongli' => $baseJiao['nongli'],
            '_nongli' => $baseJiao['_nongli'],
            'h' => $baseJiao['jinian']['h'][1],
        ];
        $year = (int)$lunar->dateTime->format('Y');
        $uTime = $lunar->dateTime->getTimestamp();
        $jq = SolarTerm::getAllJieQi($year);
        $liChun = $jq['立春'];
        $isbeforLC = strtotime($liChun) > $uTime ? '未' : '已';
        $result = [
            // 天干十神
            'god' => $lunar->getGod(),
            // 地支十神
            '_god' => $lunar->_getGod(),
            // 地势
            'terrain' => $lunar->getTerrain(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 立春
            'lichun' => $liChun,
            // 是否已过
            'is_lc' => $isbeforLC,
            // 空亡
            'kongwang' => $kongWang,
            // 大运
            'fate' => $fate,
            // 交运
            'jiaoyun' => $jiaoRes,
        ];
        return array_merge($base, $result);
    }

    /**
     * 获得相冲相刑地支数组
     * @return array
     */
    protected function getUserChongXing(): array
    {
        $data = $this->orginData;
        $ygzM = $this->mLunar->getLunarGanzhiYear();
        $ygzF = $this->fLunar->getLunarGanzhiYear();
        $list1 = [
            '男方父亲' => $data['malefsx'],
            '男方母亲' => $data['malemsx'],
            '女方父亲' => $data['femalefsx'],
            '女方母亲' => $data['femalemsx'],
        ];
        $list2 = [
            '男方' => $ygzM[1], '女方' => $ygzF[1],
        ];
        $list1 = array_unique($list1);
        $listDz = Calendar::DI_ZHI;
        $chongRes = [];

        foreach ($list1 as $k => $v) {
            if ($v < 1 || $v > 12) {
                continue;
            }
            $num = $v - 1;
            $dz = $listDz[$num];
            $chongDz = BaziCommon::getChongDz($dz);
            $chongRes[$chongDz][] = [$k, $dz];
        }
        $xingRes = [];
        foreach ($list2 as $k => $v) {
            $chongDz = BaziCommon::getChongDz($v);
            $chongRes[$chongDz][] = [$k, $v];
            $listXing = BaziCommon::getXingByDz($v);
            foreach ($listXing as $v1) {
                $xingRes[$v1][] = [$k, $v];
            }
        }
        return [
            'chong' => $chongRes,
            'xing' => $xingRes,
        ];
    }

    /**
     * 符合条件的使用时辰
     * @return array
     */
    protected function getHourUse(): array
    {
        $listDz = Calendar::DI_ZHI;
        $jiNianM = $this->mLunar->getLunarTganDzhi();
        $jiNianF = $this->fLunar->getLunarTganDzhi();
        $ydzM = $jiNianM['y'][1];
        $ydzF = $jiNianF['y'][1];
        $dtgM = $jiNianM['d'][0];
        $dtgF = $jiNianF['d'][0];
        // 吉利时辰的找法
        $listJL = [
            '子' => ['子', '丑', '卯', '午', '申'], '丑' => ['寅', '卯', '巳', '申', '戌'], '寅' => ['辰', '巳', '未', '戌', '子'],
            '卯' => ['午', '未', '酉', '子', '寅'], '辰' => ['申', '酉', '亥', '寅', '辰'], '巳' => ['戌', '亥', '丑', '辰', '午'],
            '午' => ['子', '丑', '卯', '午', '申'], '未' => ['寅', '卯', '巳', '申', '戌'], '申' => ['辰', '巳', '未', '戌', '子'],
            '酉' => ['午', '未', '酉', '子', '寅'], '戌' => ['申', '酉', '亥', '寅', '辰'], '亥' => ['戌', '亥', '丑', '辰', '午'],
        ];
        $list = [
            '子' => ['丑', '辰', '申', '亥'], '丑' => ['子', '巳', '酉', '亥'], '寅' => ['亥', '午', '戌', '卯', '辰'], '卯' => ['戌', '亥', '未', '辰', '寅'],
            '辰' => ['酉', '子', '申', '寅', '卯'], '巳' => ['申', '酉', '丑', '午', '未'], '午' => ['未', '寅', '戌', '巳', '未'], '未' => ['午', '亥', '卯', '巳'],
            '申' => ['巳', '辰', '子', '酉', '戌'], '酉' => ['辰', '巳', '丑', '戌', '申'], '戌' => ['卯', '午', '寅', '申', '酉'], '亥' => ['寅', '卯', '未', '子', '丑'],
        ];
        $result = [];
        // 时辰对应的时区间
        $listHour = [
            '子' => '23时00分-00时59分', '丑' => '01时00分-02时59分', '寅' => '03时00分-04时59分', '卯' => '05时00分-06时59分',
            '辰' => '07时00分-08时59分', '巳' => '09时00分-10时59分', '午' => '11时00分-12时59分', '未' => '13时00分-14时59分',
            '申' => '15时00分-16时59分', '酉' => '17时00分-18时59分', '戌' => '19时00分-20时59分', '亥' => '21时00分-22时59分',
        ];
        $sxBase = new SxBase();
        foreach ($listDz as $ddz) {
            $tmp = [];
            foreach ($listDz as $hdz) {
                if (BaziCommon::getXianChong($ddz . $hdz) || BaziCommon::getXianChong($ydzM . $hdz) || BaziCommon::getXianChong($ydzF . $hdz)) {
                    continue;
                }
                if (BaziCommon::getXianXin($ddz . $hdz) || BaziCommon::getXianXin($ydzM . $hdz) || BaziCommon::getXianXin($ydzF . $hdz)) {
                    continue;
                }
                if ($this->checkJianRen($dtgM, $ddz, $hdz) || $this->checkJianRen($dtgF, $ddz, $hdz)) {
                    continue;
                }
                if ($this->checkZhenSanSha($ydzM, $ddz, $hdz)) {
                    continue;
                }
                if (in_array($hdz, $listJL[$ddz]) || in_array($hdz, $list[$ddz])) {
                    $hSx = $sxBase->getsxByDz($hdz);
                    $chongSx = $sxBase->getChong($hSx);
                    $tmp[] = [
                        'chong' => $chongSx['name'],
                        'dz' => $hdz,
                        'hour' => $listHour[$hdz],
                    ];
                }
            }
            $result[$ddz] = $tmp;
        }
        return $result;
    }

    /**
     * 箭刃
     * @param string $dtg 用户日干
     * @param string $ddz 流日支
     * @param string $hdz 时支
     * @return bool
     */
    protected function checkJianRen(string $dtg, string $ddz, string $hdz): bool
    {
        $list = [
            '甲卯酉', '乙辰戌', '丙午子', '丁未丑', '戊午子', '己未丑', '庚酉卯', '辛戌辰', '壬子午', '癸丑未',
            '甲酉卯', '乙戌辰', '丙子午', '丁丑未', '戊子午', '己丑未', '庚卯酉', '辛辰戌', '壬午子', '癸未丑',
        ];
        return in_array($dtg . $ddz . $hdz, $list);
    }

    /**
     * 真三杀
     * @param string $ydz 用户年支
     * @param string $ddz 日支
     * @param string $hdz 时支
     * @return bool
     */
    protected function checkZhenSanSha(string $ydz, string $ddz, string $hdz): bool
    {
        $list = [
            '子未未', '丑辰辰', '寅丑丑', '卯戌戌', '辰未未', '巳辰辰', '午丑丑', '未戌戌', '申未未', '酉辰辰', '戌丑丑', '亥戌戌',
        ];
        return in_array($ydz . $ddz . $hdz, $list);
    }

    /**
     * 过滤黄历算法中的指定吉凶神
     * @param array $arr
     * @param int $type 0吉神 1凶神
     * @return array
     */
    protected function filterJXShen(array $arr, int $type = 0): array
    {
        // 过滤掉黄历的吉凶神
        if ($type) {
            $listNo = [
                '天吏', '五离', '五墓', '月刑', '月厌', '厌对', '八专', '岁破', '月破', '受死', '往亡',
                '劫煞', '灾煞', '月煞', '天吏', '五离', '五墓', '月刑', '月厌', '八专', '招摇', '却煞',
            ];// 凶
        } else {
            // 吉
            $listNo = [
                '天德', '天德合', '月德', '月德合', '六合', '三合', '天愿', '民日', '天喜', '月恩', '四相', '时德', '不将日', '天赦', '显星', '曲星', '传星',
            ];
        }
        if ($listNo) {
            foreach ($arr as $k => $v) {
                if (in_array($v, $listNo)) {
                    unset($arr[$k]);
                }
            }
        }
        return array_values($arr);
    }

    /**
     * 获得年份内所有节气
     * @param $year
     * @return array
     */
    protected function getJieqiByYear($year): array
    {
        $arr = $this->jieQiYear;
        if (isset($arr[$year])) {
            return $arr[$year];
        }
        $jArr = SolarTerm::getAllJieQi($year);
        $jieqiArr = [];
        foreach ($jArr as $k => $v) {
            $jTmp = date('Y-m-d', strtotime($v));
            $jieqiArr[$jTmp] = $k;
        }
        $this->jieQiYear[$year] = $jieqiArr;
        return $jieqiArr;
    }

    /**
     * 女命天狗
     * @param string $ydz 女命年支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianGou(string $ydz, string $ddz): bool
    {
        $list = [
            '子戌', '丑亥', '寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉',
        ];
        return in_array($ydz . $ddz, $list);
    }

    /**
     * 月厌2
     * @param string $mdz
     * @param array $deArray
     * @return int
     */
    protected function checkYueYan2(string $mdz, array $deArray): int
    {
        if (array_intersect(['天德', '月德', '月德合'], $deArray)) {
            if (in_array($mdz, ['丑', '寅', '未', '申'])) {
                return 1;
            }
            // 不宜不忌
            if (in_array($mdz, ['辰', '巳', '戌', '亥'])) {
                return 2;
            }
        }
        return 0;
    }

    /**
     * 可变凶神解释
     * @param string $str
     * @return string
     */
    protected function getExplainChange(string $str): string
    {
        $list = [
            '月厌2' => '月厌又叫地火，是阴建的时辰，是厌魅之神。当值之日忌纳采问名、结婚等，但当月与天德、月德、月德合任一吉神并用时，吉凶相抵，则不忌结婚。',
            '月厌1' => '月厌又叫地火，是阴建的时辰，是厌魅之神。当值之日忌纳采问名、结婚等，但当月与天德、月德、月德合任一吉神并用时，吉胜于凶，则不忌结婚。',
            '劫煞1' => '劫煞，是一年的阴气，主管杀害。当值之日忌纳采问名、结婚等。但己月遇到月德或天德合，只忌安抚边境、选将训兵、出师、求医疗病，余者都不忌讳。',
            '劫煞2' => '劫煞为绝地，是一年的阴气，主管杀害。当值之日忌纳采问名、结婚等。但辰月与天德、月德、天德合、月德合任一吉神并用时，只忌安抚边境、选将训兵、出师、求医疗病，余者都不忌讳。',
            '劫煞3' => '劫煞，是一年的阴气，主管杀害。当值之日忌纳采问名、结婚等。但寅月是六合当值，只忌安抚边境、选将训兵、出师、求医疗病，余者都不忌讳。',
            '劫煞4' => '劫煞，是一年的阴气，主管杀害。当值之日忌纳采问名、结婚等。但当月遇到月德或月德合，吉凶相抵，故不忌结婚，可自由选择。',
            '灾煞' => '灾煞，主管灾难、疾病、困厄之事。当值之日忌纳采问名、结婚等。但丑月与天德、月德、天德合、月德合任一吉神并用时，吉足胜凶，则不忌讳结婚。',
            '月煞1' => '月煞又叫月杀，是指月内的杀神。它当值之日忌纳采问名、结婚。但卯月遇到天德或天愿时，吉足胜凶，则宜结婚。',
            '月煞2' => '月煞又叫月杀，是指月内的杀神。它当值之日忌纳采问名、结婚。但卯月为六合当值，吉凶相抵，则不忌结婚。',
            '月煞3' => '月煞又叫月杀，是指月内的杀神。它当值之日忌纳采问名、结婚。但子月为六害当值与月德合并用，吉凶相抵，则不忌结婚。',
            '月刑1' => '月刑是月建刑伤之地，当值之日忌纳采问名、结婚等，但巳月遇到月德、天德合、天愿，化凶为吉，则不忌结婚。',
            '月刑2' => '月刑是月建刑伤之地，当值之日忌纳采问名、结婚等，但巳月是相日、六合当值，吉凶足以相抵，则不忌结婚。',
            '月刑3' => '月刑是月建刑伤之地，当值之日忌纳采问名、结婚等，但当月与德合并，吉凶足以相抵，则不忌结婚。',
            '天吏1' => '天吏又叫致死，是三合死地，当值之日忌纳采问名、结婚等。但寅月与德合并用，吉胜于忌，只忌讳军事、疗病，其余都不忌讳，故也不忌结婚。',
            '天吏2' => '天吏又叫致死，是三合死地，当值之日忌纳采问名、结婚等。但辰月与德神并用，不忌结婚。',
            '五墓' => '五墓是五行旺干临于墓库，与月德并用，则为三合旺气发于天干而不可按墓来论，所以不忌结婚等。',
            '五离' => '五离是五合之对冲，与德合、天赦、三合、六合并用，那么吉大于五合，因此不忌讳结婚。',
            '八专' => '八专以天干、地支取义，之所以忌讳婚嫁，是取阴阳同居之意，而没有别的意思。与天愿并用，只忌讳军事，其余不忌。',
            '厌对' => '厌对又叫招摇，是以月厌对冲取义，所以忌讳结婚。当存在天德(合)、月德(合)和天赦之一，则是阴相从阳，所以不忌讳结婚。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '天德' => '天德是指天的福德，三合的旺气，所值之日百事大吉。',
            '天德合' => '天德合，是合德之神。合德之神相助，百事大吉。',
            '月德' => '月德是月的德神，寓意吉祥。为吉日，适宜结婚之事。',
            '月德合' => '月德合，五行的精符相会聚为合。所值之日，所有的好运气将来临，诸事皆宜。',
            '天赦' => '天赦，是上天赦免、饶恕罪过的神，有利于消灾化煞,诸事不忌。',
            '天愿' => '天愿是月中的善神，五行在这天有极旺的气势，所值之日宜结婚。',
            '四相' => '四相，指拥有四时王相贵气的日子，纯粹的小吉日，适合婚嫁诸喜事。',
            '时德' => '时德又称四时天德，是四季中的德神，做事吉利。',
            '不将' => '不将吉日，诸事皆宜，凡事皆可用。',
            '六合' => '六合就是日、月与星宿相合的日子。此日宜结婚等，诸事皆宜。',
            '三合' => '三合是指不同位而同气，是五行之生、旺、库三支会在一起，称之为三合局，宜结婚等。',
            '民日' => '民日是百姓所用的日子，是一月之内做事的良辰吉日，宜纳采问名、结婚等。',
            '天喜' => '天喜是星相术士的四柱神煞之一，天喜之日，宜纳采问名、结婚等。',
            '月恩' => '月恩是指阳建所生的天干，子与母相从称之为月恩。月恩日适于营造、婚姻、迁徙等。',
            '五合' => '五合指一月之内的良辰吉日。五合之日适宜结婚、会亲友等事。',
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺。',
            '曲星' => '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄，万事称心的寓意。',
            '岁破' => '岁破是太岁的所冲之辰。它所处之地不能兴动、移徙、嫁娶、远行，侵犯它的人将损伤财物并危害一家之主。',
            '月破' => '月破是月建对冲的日子，又是月建气绝之地，因而所禁忌的有很多。德神到此失去了威力，不能降福，所以一同禁忌，诸事皆忌。',
            '受死' => '该日寓意不吉祥，因而有不宜诸吉事的说法。',
            '劫煞' => '劫煞者主有杀害之意，犯之者主有盗窃、祸害之事出现。',
            '月煞' => '月内的杀神，它当值之日禁忌留宾客，兴穿掘，营种植，纳群畜。',
            '灾煞' => '灾煞主管灾、病、厄之事，所临之方，犯之主疾病临门。',
            '往亡' => '往就是去，亡就是没有，往亡就是往而不返的意思，是五行终墓之地，是万物皆归之处，往而亡。',
            '天吏' => '是月中凶神，当值之日禁忌临官，赴任，远行，词讼。天吏是三合的死气，五行到这里死而无气，是上天的凶吏，全没有生的意思。',
            '五墓' => '五墓日是一种忌日，可悼念，而用在其余诸事不吉。',
            '月厌' => '该日又称大祸日，是忌结婚、搬家、赴任、搬家等的凶日。',
            '月刑' => '月刑是月建刑伤之地，与三煞一样忌结婚、会亲友等事。',
            '厌对' => '厌对是月厌多冲向的时日，当值之日忌讳结婚。忌讳乘船渡水。',
            '八专' => '八专日禁忌出动军队，结婚。禁忌出动军队，是因为敌我双方同位，则各自撤退。禁忌结婚，是因为阴阳同居一处，没有分别。',
            '五离' => '五离，是月中的离神，五离之日禁忌结婚，会亲友，交易，立券。五离就是阴阳重会的日子，二次相合就会相离。',
            '四废' => '四废，是四季衰败凋谢的时日，是五行没有气，福德不到的时日,百事都禁忌。',
            '彭祖' => '彭祖百忌中有“亥不嫁娶，不利新郎”，解释为，亥日不能结婚。',
            '杨公忌日' => '这日有诸事不宜说法，结婚最好避开该日。',
            '小红沙' => '红沙日有诸事不宜的说法，需要规避。',
            '披麻' => '指长辈去世，子孙身披麻布服，头上戴白，表示哀悼，因此该日不宜结婚。',
            '三娘煞' => '在婚姻上该日属于大忌之日，古话有，迎亲结婚无男女的说法，故不宜结婚。',
            '清明节' => '该日乃是用来扫坟祭祖的，若是用来结婚有些不合时宜。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，诸喜事不宜。',
            '重阳节' => '当日有大事勿用的说法，忌讳结婚。',
            '寒衣节' => '寒衣节乃是传统的四大鬼节之一，寓意不吉。',
            '朔日' => '此系恶鬼聚拢之辰，忌结婚进宅会客作乐。',
            '天狗' => '犯之主无子，该日结婚对子嗣影响较大，不宜结婚。',
            '青龙' => '天乙星，天贵星，用事半吉，如同唐符、国印、福星天乙贵人同到，可作全吉用。',
            '明堂' => '贵人星，明辅星，百事用之大吉。',
            '金匮' => '福德星，月仙星，吉，宜结婚，不宜整戎伍。',
            '宝光' => '宝光星、天德星，当值之日适宜兴动各种事物，能为事情的顺利有促进作用。',
            '玉堂' => '少微星，天开星，不利泥灶，其余百事皆吉。',
            '司命' => '凤辇星，日仙星，司命日间用百事吉，夜间用事不利。',
            '天刑' => '天刑黑道凶日，天刑星，只忌结婚、移徙等事。',
            '朱雀' => '朱雀黑道凶日，天讼星，利用公事，常人凶，只忌出行、移居、词讼。',
            '白虎' => '白虎黑道凶日，天杀星，忌修造、结婚、针刺。',
            '天牢' => '天牢黑道凶日，锁神星，阴人用事皆吉，忌出行、移居、词讼。今日既没有宜结婚的吉神，也没有忌结婚的凶神，可根据自身需求选择是否结婚。',
            '玄武' => '玄武黑道凶日，天狱星，君子合天道用事，小吉。小人用事凶，需防贼盗，忌词讼、博戏。',
            '勾陈' => '勾陈黑道凶日，地狱星，不可兴土工、营屋舍、移徙、远行、结婚、出军。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 劫煞可变条件2   0 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return int 寅申 3, 辰 丑 未 戌 2, 巳 亥 1 子 午 卯 酉 4
     */
    protected function checkJieSha2(string $mdz, array $deArray): int
    {
        if ($mdz == '寅' || $mdz == '申') {
            return 3;
        }
        if (in_array($mdz, ['丑', '辰', '未', '戌']) && $deArray) {
            return 2;
        }
        if (in_array($mdz, ['巳', '亥'])) {
            if (array_intersect(['月德', '天德合'], $deArray)) {
                return 1;
            }
        }
        if (in_array($mdz, ["子", "午", "卯", "酉"])) {
            if (array_intersect(['月德', '月德合'], $deArray)) {
                return 4;
            }
        }
        return 0;
    }

    /**
     * 月煞可变条件2
     * @param string $mdz
     * @param array $arr
     * @return int
     */
    protected function checkYueSha3(string $mdz, array $arr): int
    {
        if (in_array($mdz, ['卯', '酉'])) {
            if ($arr['tian_de'] || $arr['tian_yuan']) {
                return 1;
            }
            if ($arr['liu_he']) {
                return 2;
            }
        }
        if (in_array($mdz, ['子', '午']) && $arr['yue_de_he']) {
            return 3;
        }
        return 0;
    }

    /**
     * 月刑
     * @param string $mdz
     * @param array $arr
     * @return int
     */
    protected function checkYueXin3(string $mdz, array $arr): int
    {
        if ($mdz == '巳') {
            if ($arr['yue_de'] || $arr['tian_de_he'] || $arr['tian_yuan']) {
                return 1;
            }
            if ($arr['si_xian'] || $arr['liu_he']) {
                return 2;
            }
        }
        if (in_array($mdz, ['寅', '辰', '酉', '亥', '丑', '戌'])) {
            if ($arr['tian_de'] || $arr['yue_de'] || $arr['tian_de_he'] || $arr['yue_de_he']) {
                return 3;
            }
        }
        return 0;
    }

    /**
     * 天吏条件2
     * @param string $mdz 月支
     * @param array $deArray
     * @return int
     */
    protected function checkTianLi3(string $mdz, array $deArray): int
    {
        $bool1 = $deArray['tian_de'] || $deArray['yue_de'] || $deArray['tian_de_he'] || $deArray['yue_de_he'];
        $bool2 = $deArray['tian_se'] || $deArray['tian_yuan'];
        if (!empty($deArray)) {
            if (in_array($mdz, ['寅', '申', '巳', '亥'])) {
                return 1;
            }
        }
        if (in_array($mdz, ['辰', '戌', '丑', '未']) && ($bool1 || $bool2)) {
            return 2;
        }
        return 0;
    }

    /**
     * 厌对
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYanDui(string $mdz, string $ddz): bool
    {
        $list = [
            '寅辰', '卯卯', '辰寅', '巳丑', '午子', '未亥', '申戌', '酉酉', '戌申', '亥未', '子午', '丑巳',
        ];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天喜
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianXi(string $mdz, string $ddz): bool
    {
        $list = [
            '寅未', '卯午', '辰巳', '巳辰', '午卯', '未寅', '申丑', '酉子', '戌亥', '亥戌', '子酉', '丑申',
        ];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 忌三相
     * @param string $sx 生肖
     * @return string[]
     */
    protected function getJiSanXiang(string $sx): array
    {
        if (in_array($sx, ['龙', '鼠', '猴'])) {
            return ['蛇', '鸡', '牛'];
        }
        if (in_array($sx, ['蛇', '牛', '鸡'])) {
            return ['虎', '马', '狗'];
        }
        if (in_array($sx, ['虎', '马', '狗'])) {
            return ['猪', '兔', '羊'];
        }
        return ['龙', '鼠', '猴'];
    }

    /**
     * 获得正冲
     * @param string $dayGz
     * @return array
     */
    protected function getZhengChong(string $dayGz): array
    {
        $list1 = [
            '甲子' => '1924、1984', '乙丑' => '1925、1985', '丙寅' => '1926、1986', '丁卯' => '1927、1987',
            '戊辰' => '1928、1988', '己巳' => '1929、1989', '庚午' => '1930、1990', '辛未' => '1931、1991',
            '壬申' => '1932、1992', '癸酉' => '1933、1993', '甲戌' => '1934、1994', '乙亥' => '1935、1995',
            '丙子' => '1936、1996', '丁丑' => '1937、1997', '戊寅' => '1938、1998', '己卯' => '1939、1999',
            '庚辰' => '1940、2000', '辛巳' => '1941、2001', '壬午' => '1942、2002', '癸未' => '1943、2003',
            '甲申' => '1944、2004', '乙酉' => '1945、2005', '丙戌' => '1946、2006', '丁亥' => '1947、2007',
            '戊子' => '1948、2008', '己丑' => '1949、2009', '庚寅' => '1950、2010', '辛卯' => '1951、2011',
            '壬辰' => '1952、2012', '癸巳' => '1953、2013', '甲午' => '1954、2014', '乙未' => '1955、2015',
            '丙申' => '1956、2016', '丁酉' => '1957、2017', '戊戌' => '1958、2018', '己亥' => '1959、2019',
            '庚子' => '1960、2020', '辛丑' => '1961、2021', '壬寅' => '1962、2022', '癸卯' => '1963、2023',
            '甲辰' => '1964、2024', '乙巳' => '1965、2025', '丙午' => '1966、2026', '丁未' => '1967、2027',
            '戊申' => '1968、2028', '己酉' => '1969、2029', '庚戌' => '1970、2030', '辛亥' => '1971、2031',
            '壬子' => '1972、2032', '癸丑' => '1973、2033', '甲寅' => '1974、2034', '乙卯' => '1975、2035',
            '丙辰' => '1976、2036', '丁巳' => '1977、2037', '戊午' => '1978、2038', '己未' => '1979、2039',
            '庚申' => '1980、2040', '辛酉' => '1981、2041', '壬戌' => '1982、2042', '癸亥' => '1983、2043',
        ];
        $key = Huangli::getZhengChong($dayGz);
        return [
            'gz' => $key,
            'year' => $list1[$key] ?? '',
        ];
    }
}
