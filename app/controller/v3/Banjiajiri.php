<?php
// +----------------------------------------------------------------------
// | Banjiajiri. 搬家吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v3;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Banjiajiri
{
    use JiRiCheckTraits;

    /**
     * 传入原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @var Ex
     */
    protected Ex $lunar1;

    /**
     * @var Ex|null
     */
    protected ?Ex $lunar2 = null;

    /**
     * 节气数组
     * @var array
     */
    protected array $jieQiArr = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time1' => input('time1', '', 'trim'), //出生时间
            'sex1' => input('sex1', -1, 'intval'),
            'time2' => input('time2', '', 'trim'), //出生时间
            'sex2' => input('sex2', 0, 'intval'),
            'otime' => input('otime', '', 'trim'), //订单时间
            'longs' => input('longs', 1, 'intval'), //天数
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time1|出生时间1' => ['require', 'isDateOrTime:出生时间1'],
                'sex1|性别1' => ['require', 'in:0,1'],
                'time2|出生时间2' => ['isDateOrTime:出生时间2'],
                'sex2|性别2' => ['in:0,1'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'longs|测算天数' => ['require', 'between:1,730'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar1 = Ex::date($data['time1'])->sex($data['sex1']);
        $panArr = [];
        $panArr[] = $this->getBazi($this->lunar1);
        if ($data['time2']) {
            $this->lunar2 = Ex::date($data['time2'])->sex($data['sex2']);
            $panArr[] = $this->getBazi($this->lunar2);
        }
        return [
            'pan' => $panArr,
            'jiri' => $this->getJiRi(),
        ];
    }

    /**
     * 八字基础信息
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBazi(Ex $lunar): array
    {
        $base = $lunar->getLunarByBetween();
        $res = [
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
        ];
        $ydz = $base['jinian']['y'][1];
        $ddz = $base['jinian']['d'][1];
        $res = array_merge($base, $res);
        $ulist = [$ydz, $ddz];
        $chong = ['dz' => [], 'reason' => []];
        foreach ($ulist as $v) {
            if (isset($chong['dz'][$v])) {
                continue;
            }
            $dz1 = $this->getChongDz($v);
            if (isset($chong['dz'][$dz1])) {
                continue;
            }
            $chong['dz'][$v] = $dz1 . '日';
            $chong['reason'][$v] = $v . $dz1 . "相冲";
        }
        $res['chong'] = [
            'dz' => array_values($chong['dz']),
            'reason' => array_values($chong['reason']),
        ];
        $shaArr = $this->getTianGanSiSha($ydz);
        $res['tgss'] = "{$shaArr['no']}日{$shaArr['no']}时";
        $res['gongsha'] = $this->huiTouGongShaLiu($base['jinian']);
        return $res;
    }

    /**
     * 日期列表
     * @return array
     * @throws Exception
     */
    protected function getJiRi()
    {
        $listNo = ['7-15' => "七月半", "9-9" => "重阳", "10-1" => "寒衣"];
        // 杨公忌日 农历月份+日
        $listYangGong = ['1-13', '2-11', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19'];
        // 农历月份+日支
        $listMdz = [
            '小红沙' => ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'],
            '受死' => ['1|戌', '2|辰', '3|亥', '4|巳', '5|子', '6|午', '7|丑', '8|未', '9|寅', '10|申', '11|卯', '12|酉'],
        ];
        $listZaiSha = [
            '寅子' => ['壬子', '丙子'], '卯酉' => [], '辰午' => ['壬午'], '巳卯' => ['辛卯', '乙卯'],
            '午子' => [], '未酉' => ['己酉'], '申午' => ['戊午', '壬午'], '酉卯' => [], '戌子' => ['丙子'],
            '亥酉' => ['乙酉', '己酉'], '子午' => [], '丑卯' => ['乙卯'],
        ];
        $listYueSha = [
            '寅丑' => [], '卯戌' => ['甲戌'], '辰未' => [], '巳辰' => [],
            '午丑' => [], '未戌' => [], '申未' => [], '酉辰' => ['庚辰', '丙辰'],
            '戌丑' => [], '亥戌' => [], '子未' => [], '丑辰' => [],
        ];
        $listYueXing = [
            '寅巳' => ['丁巳', '辛巳'], '卯子' => [], '辰辰' => ['壬辰'], '巳申' => ['庚申'],
            '午午' => [], '未丑' => [], '申寅' => [], '酉酉' => ['乙酉'],
            '戌未' => ['辛未'], '亥亥' => ['乙亥', '己亥'], '子卯' => [], '丑戌' => ['庚戌'],
        ];
        $listYueYan = [
            '寅戌' => [], '卯酉' => [], '辰申' => ['壬申'], '巳未' => ['辛未', '乙未'], '午午' => ['甲午'],
            '未巳' => [], '申辰' => [], '酉卯' => [], '戌寅' => ['丙寅'], '亥丑' => ['乙丑', '己丑'],
            '子子' => ['甲子'], '丑亥' => [],
        ];
        $listDaShi = [
            '寅卯' => '卯', '卯子' => '', '辰酉' => '酉', '巳午' => '午', '午卯' => '辛卯', '未子' => '甲子', '申酉' => '酉',
            '酉午' => '庚午', '戌卯' => '卯', '亥子' => '子', '子酉' => '丁酉', '丑午' => '庚午',
        ];
        $listTianLi = [
            '寅酉' => ['丁酉', '辛酉'], '卯午' => [], '辰卯' => ['丁卯'], '巳子' => ['丙子', '庚子'], '午酉' => [],
            '未午' => ['甲午', '戊午'], '申卯' => ['癸卯', '丁卯'], '酉子' => [], '戌酉' => ['辛酉'], '亥午' => ['庚午', '甲午'],
            '子卯' => [], '丑子' => ['甲子'],
        ];
        //劫煞
        $listJieSha = [
            '寅亥' => '', '卯申' => '甲申', '辰巳' => '丁巳', '巳寅' => '庚寅', '午亥' => '辛亥', '未申' => '甲申',
            '申巳' => '', '酉寅' => '庚寅', '戌亥' => '辛亥', '亥申' => '甲申', '子巳' => '丁巳', '丑寅' => '庚寅',
        ];
        $jnU1 = $this->lunar1->getLunarTganDzhi();
        $jnU2 = $this->lunar2 ? $this->lunar2->getLunarTganDzhi() : [];
        $uydz1 = $jnU1['y'][1];
        $uddz1 = $jnU1['d'][1];
        $uydz2 = $jnU2['y'][1] ?? '';
        $uddz2 = $jnU2['d'][1] ?? '';
        $otime = strtotime($this->orginData['otime']);
        $limit = $this->orginData['longs'];
        $num = 0;
        $res = [];
        $explain = ['jc' => [], 'shen' => []];
        $shiMingArr = ["寅", "卯", "辰", "巳", "午", "未", "申"];
        $sxBase = new SxBase();
        for ($i = 0; $i < $limit; $i++) {
            $tmpTime = $otime + $i * 86400;
            $timeStr = date('Y-m-d', $tmpTime);
            $tmpYear = (int)date('Y', $tmpTime);
            $huangli = Huangli::date($timeStr);
            $base = $huangli->getLunarByBetween();
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $nongliNum = $base['_nongli'];
            $zhiRi = $huangli->getZhiRi();
            $jxArr = $huangli->getJiXiong();
            $dgz = implode('', $jiNian['d']);
            $ddz = $jiNian['d'][1];
            $dtg = $jiNian['d'][0];
            $mdz = $jiNian['m'][1];
            $ydz = $jiNian['y'][1];
            $ytg = $jiNian['y'][0];
            $yueRi = $mdz . $ddz;
            $bool = true;
            $noList = [];
            // 岁破、受死、月破、黑道凶日
            // 寒衣、清明、重阳、七月半、杨公忌日、红沙（小红沙）、无禄（十恶大败）
            $xinChong = $this->getLiuGx($ddz);
            if ($xinChong) {
                $noList[] = $xinChong;
                $bool = false;
            }
            // 岁破 2.流日支与命主年支相冲为岁破；
            if (BaziCommon::getXianChong($ddz . $uydz1) || ($uydz2 && BaziCommon::getXianChong($ddz . $uydz2))) {
                $noList[] = '岁破';
                $bool = false;
            }
            $nlmd = $base['_nongli']['m'] . '-' . $base['_nongli']['d'];
            if (isset($listNo[$nlmd])) {
                $noList[] = $listNo[$nlmd];
                $bool = false;
            }
            $jieQi = $this->getJieQibyYear($tmpYear);
            if ($jieQi['清明'] === $timeStr) {
                $noList[] = '清明';
                $bool = false;
            }
            // 杨公忌日
            if (in_array($nlmd, $listYangGong)) {
                $noList[] = '杨公忌日';
                $bool = false;
            }
            // 农历月数字+日地支 小红沙,受死
            $mtoddz = $base['_nongli']['m'] . '|' . $jiNian['d'][1];
            foreach ($listMdz as $k => $v) {
                if (in_array($mtoddz, $v)) {
                    $noList[] = $k;
                    $bool = false;
                }
            }
            //            if ($this->checkSHiEr($dgz)) {
            //                $noList[] = '十恶大败';
            //                $bool = false;
            //            }
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            // 4.黑道凶神、
            if ($zhiRi['huan_dao'] === '黑道') {
                //$noList[] = $shenSha;
                $bool = false;
            }
            if ($this->checkYuePo($mdz, $ddz)) {
                $noList[] = '月破';
                $bool = false;
            }
            $week = Huangli::getWeekChs($tmpTime);
            $sanSha = Huangli::getSanSha($ddz);
            $jianChu = $huangli->getJianChu();
            // 破、危、平、收、闭、建、定
            $typeJc = in_array($jianChu, ["破", "平", "危", "收", "闭", "建", "定"]) ? 0 : 1;
            if (in_array('搬家', $jxArr['ji'])) {
                $noList[] = '黄历不宜';
                $bool = false;
            }
            $chongDz = BaziCommon::getChongDz($ddz);
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']),
                'diff' => $i,
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'jx' => $jxArr,
                'position' => $huangli->getPosition(),
                'type' => $bool ? 1 : 0,
                'type_hl' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                'type_jc' => 1,
                'type_ss' => 1,
                // 煞向
                'sha_xian' => str_replace('煞', '', $sanSha[0]),
                // 正冲
                'zheng_chong' => [
                    'gz' => $chongDz,
                    'sx' => $sxBase->getsxByDz($chongDz),
                    'year' => '',
                ],
                'gongsha' => [],
                'jianchu' => $jianChu, //建除
                'shensha' => $shenSha,
                'hour' => [],
                'reason' => [],
            ];
            $gongSha = $this->huiTouGongShaLiu($jiNian);
            if ($gongSha && ($gongSha['dz'] == $uydz1 || $uydz2 == $gongSha['dz'])) {
                $noList[] = '回头贡杀';
                $bool = false;
            }
            if ($bool) {
                $num++;
                $tmpRes['gongsha'] = $gongSha;
                $reason = [];
                if ($this->checkTianDe($mdz . $ddz) || $this->checkTianDe($mdz . $dtg)) {
                    $reason[] = '天德';
                }
                if ($this->checkTianDeHe($mdz . $ddz) || $this->checkTianDeHe($mdz . $dtg)) {
                    $reason[] = '天德合';
                }
                if ($this->checkYueDe($mdz . $ddz)) {
                    $reason[] = '月德';
                }
                if ($this->checkYueDeHe($mdz . $ddz) || $this->checkYueDeHe($mdz . $dtg)) {
                    $reason[] = '月德合';
                }
                if ($this->checkTianSe($jiNian['m'][1], $dgz)) {
                    $reason[] = '天赦';
                }
                if ($this->checkYueEn($jiNian['m'][1] . $jiNian['d'][0])) {
                    $reason[] = '月恩';
                }
                if ($this->checkSiXian($jiNian['m'][1], $jiNian['d'][0])) {
                    $reason[] = '四相';
                }
                if ($this->checkTianYuan($jiNian['m'][1] . $dgz)) {
                    $reason[] = '天愿';
                }
                if ($this->checkShiDe($yueRi)) {
                    $reason[] = '时德';
                }
                if ($this->checkYiMaXin($uddz1, $ddz) || $this->checkYiMaXin($uddz2, $ddz)) {
                    $reason[] = '驿马';
                }
                if ($this->checkMingRi($mdz, $ddz)) {
                    $reason[] = '民日';
                }
                if ($this->checkTianMa($mdz, $ddz)) {
                    $reason[] = '天马';
                }

                // 流年年支，+与流日日支
                if (BaziCommon::liuHeDz($ddz . $ydz)) {
                    $reason[] = '六合';
                }
                if ($this->checkRenZhuan($nongliNum['m'], $dgz)) {
                    $reason[] = '人专';
                }
                if ($this->checkShaGong($nongliNum['m'], $dgz)) {
                    $reason[] = '煞贡';
                }
                if ($this->getSanHeByDz($mdz . $ddz)) {
                    $reason[] = '三合';
                }
                if ($this->checkSuiDe($ytg . $dtg)) {
                    $reason[] = '岁德';
                }
                if ($this->checkSuiDeHe($ytg . $dtg)) {
                    $reason[] = '岁德合';
                }
                if (in_array('天恩', $jxArr['jishen'])) {
                    $reason[] = '天恩';
                }
                if (in_array('五富', $jxArr['jishen'])) {
                    $reason[] = '五富';
                }
                if (!$typeJc) {
                    $tmpRes['type_jc'] = 0;
                }
                $hourDetail = $huangli->getHourDetail();
                $hourRes = $this->getHour($dgz, $hourDetail);
                if ($jianChu === '司命') {
                    foreach ($hourRes as $k3 => $v3) {
                        if (!in_array($v3[0], $shiMingArr)) {
                            unset($hourRes[$k3]);
                        }
                    }
                }
                // "寅", "卯", "辰", "巳", "午", "未", "申"
                $tmpRes['hour'] = array_values($hourRes);
            } else {
                if ($typeJc) {
                    $tmpRes['type_jc'] = 0;
                }
                // 破、危、平、收、闭、建、定、  黑道：天刑、朱雀、白虎、天牢、勾陈、玄武
                // 劫煞（绝地）月支+日支
                if (isset($listJieSha[$yueRi])) {
                    $noList[] = '劫煞';
                }
                // 灾煞（正冲，凶于劫煞）
                if (isset($listZaiSha[$yueRi])) {
                    $noList[] = "灾煞";
                }
                if ($this->checkWuMu($mdz, $dgz)) {
                    $noList[] = "五墓";
                }
                // 月煞（尽地）
                if (isset($listYueSha[$yueRi])) {
                    $noList[] = '月煞';
                }
                // 月刑（月建刑伤之地）
                if (isset($listYueXing[$yueRi])) {
                    $noList[] = '月刑';
                }
                // 月厌
                if (isset($listYueYan[$yueRi])) {
                    $noList[] = '月厌';
                }
                // 大时（大败咸池）
                if (isset($listDaShi[$yueRi])) {
                    $noList[] = '大时';
                }
                // 天吏（凶于大时）
                if (isset($listTianLi[$yueRi])) {
                    $noList[] = '天吏';
                }
                // 四废
                if ($this->getSiFei($mdz, $dgz)) {
                    $noList[] = '四废';
                }
                if ($this->checkYueHai($nongliNum['m'], $ddz)) {
                    $noList[] = '月害';
                }
                if ($this->checkGuiJi($mdz, $ddz)) {
                    $noList[] = '归忌';
                }
                $tomorrowInt = $tmpTime + 86400;
                $tomorrowYear = date('Y', $tomorrowInt);
                $tomorrow = date('Y-m-d', $tomorrowInt);
                $tomorrowJieQiArr = $this->getJieqiByYear($tomorrowYear);
                $tomorrowJieQi = $tomorrowJieQiArr[$tomorrow] ?? '';
                // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天   2农历初1
                if (in_array($tomorrowJieQi, ["立春", "立夏", "立秋", "立冬"])) {
                    // 四绝
                    $noList[] = '四绝';
                } elseif (in_array($tomorrowJieQi, ["春分", "秋分", "夏至", "冬至"])) {
                    $noList[] = '四离';
                }
                if ($tmpRes['type_hl']) {
                    $tmpRes['type_ss'] = 0;
                }
                $tmpRes['reason'] = $noList;
            }
            foreach ($tmpRes['reason'] as $r1) {
                if (isset($explain['shen'][$r1])) {
                    continue;
                }
                $explain['shen'][$r1] = $this->getExplain($r1);
            }
            if ($tmpRes['type_jc']) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if ($tmpRes['type_ss']) {
                $explain['shen'][$shenSha] = $this->getExplain($shenSha);
            }
            $res[] = $tmpRes;
        }
        return [
            'explain' => $explain,
            'list' => $res,
            'num' => $num,
        ];
    }

    /**
     * 根据阳历年份获得当年所有节气
     * @param $year
     * @return array
     */
    protected function getJieQibyYear($year): array
    {
        if (isset($this->jieQiArr[$year])) {
            return $this->jieQiArr[$year];
        }
        $jieqi = SolarTerm::getAllJieQi($year);
        $res = [];
        foreach ($jieqi as $k => $v) {
            $tmp = date('Y-m-d', strtotime($v));
            $res[$k] = $tmp;
        }
        $this->jieQiArr[$year] = $res;
        return $res;
    }

    /**
     * 四废
     * 凡春天出生，八字日柱为庚申或辛酉;夏天出生，八字日柱为壬子或癸亥;秋天出生，八字日柱为甲寅或乙卯;冬天出生，八字日柱为丙午或丁巳，均视为命带“四废”神煞。
     * @param string $mdz 月地支
     * @param string $rgz 日干支
     * @return bool
     */
    public function getSiFei(string $mdz, string $rgz): bool
    {
        switch ($mdz) {
            case '子':
            case '丑':
            case '亥':
                $list = ['丙午', '丁巳'];
                break;
            case '寅':
            case '卯':
            case '辰':
                $list = ['庚申', '辛酉'];
                break;
            case '巳':
            case '午':
            case '未':
                $list = ['壬子', '癸亥'];
                break;
            default:
                $list = ['甲寅', '乙卯'];
                break;
        }
        return in_array($rgz, $list);
    }

    /**
     * 驿马星
     * @param string $ddz 日支
     * @param string $ddz1 流日支
     * @return bool
     */
    protected function checkYiMaXin($ddz, $ddz1): bool
    {
        // 驿马星 日支+流日支
        $listYiMa = ['子寅', '丑亥', '寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳'];
        return in_array($ddz . $ddz1, $listYiMa);
    }

    /**
     * 根据地支获得对应的相冲地支
     * @param string $dz
     * @return string
     */
    private function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    private function getTianGanSiSha(string $dz): array
    {
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $result = ['ju' => ['寅', '午', '戌'], 'no' => '丑', 'sx' => '牛'];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 流日和用户关系
     * @param string $dz 日支
     * @return string
     */
    protected function getLiuGx(string $dz): string
    {
        $jiNianM = $this->lunar1->getLunarTganDzhi();
        $jiNianF = $this->lunar2 ? $this->lunar2->getLunarTganDzhi() : [];
        $ydzM = $jiNianM['y'][1];
        $ddzM = $jiNianM['d'][1];
        $ydzF = $jiNianF ? $jiNianF['y'][1] : '';
        $ddzF = $jiNianF ? $jiNianF['d'][1] : '';
        $list = [
            $ydzM, $ddzM, $ydzF, $ddzF,
        ];
        $xin = '';
        $chong = '';
        foreach ($list as $v) {
            if (BaziCommon::getXianXin($v . $dz)) {
                $xin = '刑';
            }
            if (BaziCommon::getXianChong($v . $dz)) {
                $chong = '冲';
            }
        }
        $result = $xin . $chong;
        if (empty($result)) {
            if (BaziCommon::getXianChong($ddzM . $dz) || BaziCommon::getXianChong($ddzF . $dz)) {
                $result = '冲';
            }
        }
        return $result;
    }

    /**
     * 吉日吉时
     * @param $dgz
     * @param array $detail
     * @return array
     */
    protected function getHour($dgz, $detail = []): array
    {
        $list = [
            '卯' => [
                '甲子', '乙丑', '丁卯', '庚午', '辛未', '丙子', '丁丑', '己卯', '壬午', '癸未', '戊子', '己丑', '辛卯',
                '甲午', '乙未', '庚子', '辛丑', '癸卯', '丙午', '丁未', '壬子', '癸丑', '乙卯', '戊午', '己未',
            ],
            '辰' => [
                '丙寅', '戊辰', '己巳', '壬申', '乙亥', '戊寅', '庚辰', '辛巳', '甲申', '丁亥', '庚寅', '壬辰', '癸巳',
                '丙申', '己亥', '壬寅', '甲辰', '乙巳', '戊申', '辛亥', '甲寅', '丙辰', '丁巳', '庚申', '癸亥',
            ],
            '午' => ['癸酉', '乙酉', '丁酉', '己酉', '辛酉'],
            '巳' => ['甲戌', '丙戌', '戊戌', '庚戌', '壬戌'],
        ];
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $list1 = [
            '乙丑' => ['巳' => ['出行', '搬家']], '丙寅' => ['巳' => ['搬家']], '丁卯' => ['午' => ['搬家']],
            '戊辰' => ['巳' => ['搬家']], '己巳' => ['午' => ['出行', '搬家']], '庚午' => ['午' => ['出行', '搬家']],
            '辛未' => ['巳' => ['出行', '搬家']], '壬申' => ['巳' => ['出行', '搬家']], '乙亥' => ['午' => ['出行', '搬家']],
            '丁丑' => ['巳' => ['搬家']], '戊寅' => ['巳' => ['出行', '搬家']], '己卯' => ['午' => ['出行', '搬家']],
            '庚辰' => ['巳' => ['搬家']], '辛巳' => ['午' => ['搬家']], '壬午' => ['午' => ['搬家']], '癸未' => ['巳' => ['出行', '搬家']],
            '甲申' => ['巳' => ['出行', '搬家']], '丁亥' => ['午' => ['出行', '搬家']], '己丑' => ['巳' => ['出行', '搬家']],
            '庚寅' => ['巳' => ['出行', '搬家']], '辛卯' => ['午' => ['出行', '搬家']], '壬辰' => ['巳' => ['搬家']],
            '癸巳' => ['午' => ['出行', '搬家']], '甲午' => ['午' => ['出行', '搬家']], '乙未' => ['巳' => ['出行', '搬家']],
            '丙申' => ['巳' => ['搬家']], '己亥' => ['午' => ['搬家']], '辛丑' => ['巳' => ['出行', '搬家']],
            '壬寅' => ['巳' => ['搬家']], '癸卯' => ['午' => ['出行', '搬家']], '甲辰' => ['巳' => ['搬家']],
            '乙巳' => ['午' => ['出行', '搬家']], '丙午' => ['午' => ['搬家']], '丁未' => ['巳' => ['搬家']],
            '戊申' => ['巳' => ['出行', '搬家']], '辛亥' => ['午' => ['搬家']], '癸丑' => ['巳' => ['搬家']],
            '甲寅' => ['巳' => ['出行', '搬家']], '乙卯' => ['午' => ['出行', '搬家']], '丙辰' => ['巳' => ['搬家']],
            '丁巳' => ['午' => ['出行', '搬家']], '戊午' => ['午' => ['出行', '搬家']], '己未' => ['巳' => ['出行', '搬家']],
            '庚申' => ['巳' => ['出行', '搬家']], '癸亥' => ['午' => ['出行', '搬家']],
        ];
        $result = [];
        $sxBase = new SxBase();
        foreach ($list as $k => $v) {
            $dz1 = $this->getChongDz($k);
            $result[] = [
                $k,
                $hourList[$k],
                $sxBase->getsxByDz($dz1),
            ];
        }
        $list2 = $list1[$dgz] ?? [];
        $shiYi = [];
        foreach ($detail as $v) {
            $tmpDz = $v['h'][1];
            $shiYi[$tmpDz] = $v['shiyi'];
        }
        foreach ($list2 as $k => $v) {
            $tmpYi = $shiYi[$k];
            $dz1 = $this->getChongDz($k);
            if (array_intersect($tmpYi, $v)) {
                $result[] = [
                    $k, $hourList[$k],
                    $sxBase->getsxByDz($dz1),
                ];
            }
        }
        return $result;
    }

    /**
     * 获得解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '成' => '成功、天帝纪万物成就的大吉日子，凡事皆顺。（万物死而后萌，终而始复之意。故有天医日之称，全吉之象，诸事皆宜。）',
            '除' => '有去旧迎生之义，宜见医生或做手术、捉贼、驱鬼、治邪、装修。最忌当官上任、婚嫁、落葬、开张、移居、动土、旅游。',
            '定' => '是死气也，宜结婚、上香祈福、安牀。忌移居、开张、播种植物。',
            '开' => '开始、开展的日子。（阳气再生之象。）',
            '危' => '危虽是黄道日，但危本为“高”的意思，高则有险，故有“危险”之说，虽然“高”乃出人头地、出类拔萃也，但不利搬家。',
            '执' => '破日之从神，曰小耗，天帝执行万物赐天福，较差的日子。（建日之气至此而绝，故称小耗）',
            '闭' => '关闭、收藏、天地阴阳闭寒的日子。（生于开而养于闭，始而为建之义。诸事皆平和。但忌开刀见血，因为闭之义为不可泄。）',
            '建' => '是健旺之气，适宜见官、去官府办事，与官员交涉。忌落葬，容易出生子孙愚蠢、沉迷酒色之人。',
            '满' => '丰益盈溢之象，宜建货仓、置保险箱。此星为土瘟，忌动土、安葬。',
            '平' => '普通的日子。平常、官人集合平分的日子。（物满必溢，溢之则平，平平安安即是福。）',
            '破' => '日月相冲，曰大耗，斗柄相冲相向必破坏的日子，大事不宜，（与建日相冲，破坏月令气，称大耗。）平平安安即是福。',
            '收' => '收成、收获，天帝宝库收纳的日子。（物之极宜收之，不吉不凶之日。）',
            '八风' => '忌讳乘船渡水，与天德，月德，天德合，月德合，六合并用，则风以合来确定，则不忌（天赦不得与八风并用）',
            '八专' => '八专日禁忌出动军队，嫁娶。禁忌出动军队，是因为敌我双方同位，则各自撤退。禁忌嫁娶，是因为阴阳同居一处，没有分别。',
            '宝日' => '宝日，指的是天干生地支的意思。天干生地支就是得其天时。',
            '兵吉' => '就是月内用兵的良辰吉日。当值之日适宜出师命将，攻伐略地。',
            '兵禁' => '兵禁就是用兵的凶辰，当值之日禁忌出师振旅，阅武教战。不能用兵，用兵必定遭天殃。没有战事就不能从我方开始。',
            '博士' => '是太岁的善神，与岁德、月德同临化喜神，执掌案牍，主持拟议，宜修作大吉。所主理之处适宜举荐贤能，对国家有益处。',
            '除神' => '除神是与五合相对冲的日子，适宜沐浴，扫除，消灾害。',
            '触水龙' => '触是犯的意思，龙为水物，所谓触犯水中之龙，无非是五行相克的含义。触水龙日忌渡水涉江河，犯之不吉利。(触水龙没有天赦日)',
            '大煞' => '大煞是岁三合五行健旺的日时日，将星职位，名叫刺史，是月中的廉察。主有刑伤；违犯它择优刑杀之灾。',
            '大时' => '绝烟火日，也为大时，乃将军之象，五行到此而败绝，为极凶之神。',
            '伐日' => '伐日，指的是地支克制天干的意思，伐日当值禁忌讨伐征战，出军掠地。伐就是对方征伐自己一方，对方克制己方。它的意义刚好背逆。',
            '反支' => '官府在反支日不接受奏章，反支的意思就是将完尽，禁忌上表彰。',
            '福生' => '福生是月中福神，是月内祈求福愿的神，当值之日宜祈福求恩，祀神致祭。朝向它能够向月建邀福。看它所朝向的都是可以造福的。',
            '复日' => '复日就是魁罡所在的时辰。复日禁忌做凶事，有利于做吉利事。得禄最吉利，鸣吠遇到复日就禁忌。德赦，六合相遇复日就不用禁忌。只留存下名字不影响意义。',
            '官日' => '官日，是四季中临官的时日，有诸侯之象。（官日是臣下所用的日）',
            '归忌' => '归忌是月内的凶神，归忌，是墓旺，退归于旺、生，所以这一日忌讳远回，移徙(搬家)。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '吉期' => '吉期是吉庆之神，所理值之日适宜出征，行军，攻城寨，兴吊伐，会姻亲。吉期是与约见肝胆相照的好兄弟，位于建的前一个时辰，可以邀约吉庆的事情，因此得名。',
            '劫煞' => '劫煞是一年的阴气，主管杀害，三煞对人是非常不利的，是流年中的大煞，因此会导致流年不顺，发生各种灾祸和不利，因此一定要避之。',
            '解神' => '解神是月中善神。是月中上奏折直谏的忠直的臣子。所值之日，宜上词章、雪寃枉。',
            '金堂' => '金堂是月中善神，所值之日宜营建宫室，兴造修筑。金堂是建神的安乐之堂，通常在玉宇前面类似王公修建宅地的次序。',
            '敬安' => '敬安者，敬重、端肃、安定、逸心乐之意。人与人之间互相恭敬则必安，敬安之日乃为恭顺之神当值，故宜召开有关家族、企业、组织等相关会议，亦宜拜访长辈，推荐朋友、求职、赴任等。',
            '九丑' => '感情多会产生争端，古代选择此日有短寿，不得善终之意，女命选择止日要防止产厄。',
            '九焦' => '九焦是月中杀神，逆反天地运行的规律。当值之日禁忌冶炼，铸造，种植，修筑园圃。',
            '九坎' => '九坎是月中杀神。坎就是陷坑，危险，不平坦的意思，逆反天地运行的规律。当值之日禁忌乘船渡水，修筑堤坝，筑造城墙，修盖房屋。',
            '九空' => '九空是月内的杀神，也是墓库破散之神，所理值之日禁忌修造仓库，出入货财。恐有破财危险。',
            '临日' => '临日是以上临下的意思，是阳建的时辰，奉上命来传达给百官的，当值之日忌讳临民，诉讼。',
            '六合' => '此日与太岁六合，诸事皆宜。六合当值日最适合会宾客，结婚姻，立券交易。',
            '六仪' => '六仪是月中吉神，所理值之日适宜牧养，生财，栽植树木，接亲纳礼，视事临官。六仪是月中正礼仪的神，月厌主掌暗昧，六仪与它相敌冲，凭借为例逼迫月厌。使它不敢随便丧失仪容。所以用此命名。',
            '民日' => '民日是百姓所用的日，宜宴会、结婚、纳采问名、进人口、搬移、开市、立券、交易、纳财。',
            '鸣吠' => '鸡鸣犬吠之意，民间鸡叫和狗叫有避邪的意思，',
            '鸣吠对' => '鸣吠对日也是安葬吉日，适宜破图，斩草之类。',
            '母仓' => '母仓是吉神，指的是五行当旺所生者，主吉。',
            '普护' => '普护是神荫之神。所值当日适宜祭祀，寻医避病。普护是月中普护万物，没有偏私的神，通常与要安相对。代表暗中相护。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，该日诸吉事不宜。',
            '气往亡' => '气往亡日就是犯往亡煞的凶日。天地间气化运行，逢气往亡日凶日则郁结不通导致煞气，称作往亡煞，故诸多禁忌。嫁娶、入宅、出行、赴任、纳采、起基造房等逢气往亡日，就是犯了往亡煞。往亡煞尤其忌讳结婚嫁娶、搬家入宅、出行赴任。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来搬家有些不合时宜。',
            '人专' => '值此星，名曰：太阴、金堂符入宅，凡造作、嫁娶、移徙、上任赴官、入宅、入学、开店、葬埋，遇此，一年之内，主有贵子，三年之内，有官者升官，无官者百事吉庆，大发财源，喜事交集，得外人力，僧道用之俱吉。',
            '三合' => '三合者如聚结群力，众志成城，故宜搬家、订亲、嫁娶、结盟、会友、立券交易、开市、纳财。',
            '煞贡' => '有入宅即安的寓意，百事吉庆，对财运有益，喜事连连，尤其适合乔迁搬家。',
            '生气' => '生气是极福之神，代表生命力、活动、生机之意。如果出军战阵则宜回避。',
            '圣心' => '圣心是月中福神，当值之日宜上表彰，行恩泽。营百事。辛劳而不敢安定是圣人的心性。',
            '时德' => '四时天德，是四季中的德神。适宜庆赐，宴乐。拜官，赏贺。做事吉利。',
            '时阳' => '时阳是月中执掌阳气的神，是夫父之象。因此时阳主事威仪正直，有礼义仪容，所以时阳当值之日适宜叙婚姻，行宴乐。',
            '时阴' => '时阴是月中的阴神，是母妇之象，阴神主理事情，机密难以揣测，所以所理值之日适宜运筹谋算，策划计谋，亲和子孙，会聚亲友。',
            '守日' => '守日，是四季胎，绝的时日，无气能够自守，守而等待将来。（守日是封疆大臣所用的日）',
            '受死' => '该日寓意不吉祥，因而有不宜诸吉事的说法。',
            '死气' => '死气是月建为临官前临死位，是无气之辰，表示所行之运陷入死穴，对事不吉利。',
            '四绝' => '古人忌讳穷尽，认为这样的日子不吉，故把它们定为四绝日。',
            '四离' => '古人崇尚和合，认为这样的日子不吉，故把它们定为四离日。',
            '四废' => '四废，是四季衰败凋谢的时日，是五行没有气，福德不到的时日。所理值之日禁忌出军，征伐，造舍，迎亲，封建，拜官，纳财，开市。百事都禁忌。',
            '四耗' => '四耗是指四季休干所临春分，秋分，夏至，冬至的时日，其当值之日禁忌会姻亲，出师，开仓库，施债负。事物到了“分”必然消散，“至”就是尽头，这是阴阳两数尽了而将要分，又有休干相临，因此叫耗。',
            '四击' => '四季以土旺为守日，四击与它相冲，四季所值之日紧急出军，边防等事情。',
            '四忌' => '春甲子日，夏丙子日，秋庚子日，冬壬子日，忌结婚嫁娶。',
            '四穷' => '四穷是说亥是阴绝的时辰，用四季旺盛的天干相临，所以叫四穷。所理值支付，不可远行，征伐，出纳财物。开业求财结婚竖造移动慎用。',
            '四相' => '四相指的是四季王相的时日。宜修营，起工，洋芋，生财，栽植种莳，移徙（搬家），远行。',
            '岁德' => '岁德所管理的地方万福聚集，众多祸殃自行避开，应该有修建营造，会获得福佑，遇事有逢凶化吉之意。',
            '岁德合' => '属上吉，天德合并月德合，有宜没有忌，宜修方，造葬，百事皆吉。',
            '岁干合' => '岁干之合与岁德之合性质一样，凡岁干相合，及阳阳配合，所理之方，百事不忌。',
            '岁破' => '岁破者又名叫大耗，犯之者损害家长、常年会有破财之兆，惟有征伐为大吉，其余皆凶；',
            '岁煞' => '岁煞者乃太岁三合局之前三辰也，犯之者主伤害子孙及六畜。',
            '岁支德' => '执守之德，是岁中的德神，德即得，是得福的意思，负责救危济弱，所理之方，百事不忌。',
            '太岁' => '太岁是君王之象，若是国家巡狩省方，出师掠城，营建宫阙，开拓疆土，不能朝向它，黎民百姓修房营宅，筑墙垒院都要避开。这一方固然吉利，但不是平民百姓所能使用。',
            '天仓' => '天仓是指天库之神，它当值在之日适宜修仓库，受赏赐，纳财，牧养。仓就是藏，是月建的私藏之神。',
            '天德' => '指天之福德，即自然界养育万物的生机之所。天德所在的方位和时间适宜兴举各种事务。该时辰各类事情都较合适，特别适合结婚典礼。天德是三合之气。',
            '天德合' => '天德合是合德之神，所临之方，各类凶神皆避，可以营造宫室，修筑城墙，所值之日适合出师、嫁娶、诸事皆宜。',
            '天恩' => '天恩日为上天施恩德泽予民之日。',
            '天后' => '天后是月中福神。它当值之日适宜求医疗病，祈福礼神。天后主管生育万物，是万物之母，因此这样命名。或有绝处逢生的意思。',
            '天吏' => '是月中凶神，当值之日禁忌临官，赴任，远行，词讼。天吏是三合的死气，五行到这里死而无气，是上天的凶吏，全没有生的意思，诸事不宜。',
            '天马' => '天马是天的坐骑，当值之日适宜拜公卿，择贤良，宣布政事，远行出征。',
            '天赦' => '天赦者，赦其过罪之神也，其日可以缓刑狱，雪冤枉，如果与德神汇合，诸事皆宜，尤其宜于兴造土木。',
            '天愿' => '天愿是月中善神，它所理值之日，宜嫁娶，纳财敦睦亲族、喜神眷顾，能趋吉避凶。',
            '天贼' => '天贼是月中的盗神，是盗贼。当值之日禁忌远行。',
            '土符' => '土符就是土神，是掌握土地信符的神。掌握五方土地，当值之日禁忌破土，穿井，开渠，筑墙，应尊重它，不要去冒犯它。',
            '土府' => '专门忌讳营建宫室，修宫室，维修城郭，筑堤防，兴造动土，修仓库，修置产室，开渠穿井，安碓碾，修饰垣墙，平治道涂，破屋坏垣，栽种，破图。',
            '王日' => '王日，是四季中正王的时日，是四正的方位，有帝王之气象，所以得到政权进行治理。（王日是天子所用的日子）',
            '往亡' => '往就是去，亡就是没有，往亡就是往而不返的意思，是五行终墓之地，是万物皆归之处，往而亡。',
            '十恶大败' => '十天禄落空，因此叫无禄。又叫十恶大败日，就只有随各人的生年和本命来避开他。无禄日就是禄陷旬空，所以诸事不宜，把它作为禁忌。',
            '五富' => '五富是富足昌盛之神，是月中的余盛之神，当值之日适宜兴举运动，诸事吉。',
            '五合' => '五合指一月之内的良辰吉日，适宜结婚姻，会亲友，立券，交易。',
            '五离' => '五离，是月中的离神，五离之日禁忌结婚姻，会亲友，交易，立券。五离就是阴阳重会的日子。',
            '五墓' => '五墓日是一种忌日，可悼念，而用在其余诸事不吉。',
            '五虚' => '五虚是四季的断绝辰时，万物逢绝就损毁腐朽，它们中空无实，所以叫虚。当值之日禁忌开仓库，营种莳、出财宝、施负债。',
            '相日' => '相日，是四季中官日相生的，是相气的时日，有宰相之象。（相日是亲近君王之臣所用的日。）',
            '小耗' => '小耗是一年中的虚耗之神，主小的损害多主理的方位不适宜运动，进出，做生意，买卖，制造，违反者定有损失，虚惊一类的事情。百事忌。',
            '小红沙' => '红沙日有诸事不宜的说法，需要规避。',
            '小时' => '小时就是指月建。是朗将之象。是当时的盛旺时辰。忌讳结婚姻，因为这一日阳气单独占持盛旺之气；忌开仓库，因为旺气一发而不可收拾。',
            '续世' => '为人子女，能继志述事为孝亲。续世者，月之善神，亦有日新月异、绵延永续之意，故续世日最宜婚嫁、祭祀、求子，甚至受孕。',
            '血忌' => '旧俗指忌讳见血的日子，逢该日不杀牲，寓意不好，不利搬家。',
            '厌对' => '厌对是月厌所冲向的时日，当值之日忌讳嫁娶。厌对为不吉之神，凡结婚嫁娶逢，厌对日，不吉，对公公、婆婆不利。（无公婆不忌）。厌对日杀姑日克公婆、为祸新郎母亲。',
            '杨公忌日' => '这日诸事不宜，有凶险的寓意。',
            '阳德' => '阳德，是月中的德神。德者得也，得到天地间最适宜、和谐之气化。阳德，为月中之德神，阳德日为德神当值之日，气化调合，诸事顺遂。',
            '要安' => '要安是月中的吉神，当值之日适宜安抚边境，修城隍庙。要安，关键时使用他可以得到平安。',
            '义日' => '义日，指的是地支生天干的意思。地支生天干就是得其地利。',
            '益后' => '益后是月中福神，是对子孙后代有补益的神，各自遵从其道有益于将来。当值之日宜造舍宅，筑院墙，行嫁娶，安产室。',
            '驿马' => '驿马为发动之要神，岁、月、日、时之中有之。俗云：三合头冲为驿马。即谓驿马所居之处为三合首一字之冲神。驿马是奔波、外求，进而不已之神，所以是日逢出行、赴任、移徙、谒贵等事均可选用。或有绝处逢生的意思。',
            '阴德' => '阴德日为阴德之神当值之日。主有扬善嫉恶，明察功过之神，凡有冤情待平复，或行善积德、惠泽贫困之举，选用阴德日其愿顺遂。',
            '不将' => '不将在阴阳中解释为阴阳中和，若是阳气过重伤夫，阴气过重伤妇。而阴阳中和，则万事大吉。',
            '游祸' => '游祸神，因为它流动在四隅，所以叫游，因为它过于旺盛，所以叫祸。是月中的恶神，当值之日禁忌扶摇，请医，祝神，致祭。',
            '玉宇' => '玉宇是月中的贵神，当值之日适宜修宫阙，维修亭台，结婚姻，会宾客。玉宇是月建所安居之室。',
            '月德' => '月德是月德德神，取土，修营适宜朝向的方位，宴乐，伤官赴任用月德日有利。',
            '月德合' => '月德合日，百福并集，诸事皆宜，是个好日子，宜多加参考运用，尤利于由内而外拓展所图。',
            '月恩' => '受恩之日，这天适合开始一段新的生活。诸事不忌，可搬家可选该日。',
            '月害' => '月害，是阳建所害的日子，理值之日，禁忌攻城野战，牧养群畜，结婚姻，请巫医，纳奴婢。六害指的是不合。',
            '月建' => '月建是阳建之神，所主理的方向战斗，攻伐都应该背向它，是为了使自己的盛旺之气来共计对方休困的部队。不宜搬家、结婚。',
            '月空' => '所理值之日适宜出谋划策，陈述计策，宜上表、修造、动土。',
            '月破' => '月破是月建对冲的日子，又是月建气绝之地，因而所禁忌的有很多。德神到此失去了威力，不能降福，所以一同禁忌。诸事皆忌。',
            '月煞' => '月内的杀神，它当值之日禁忌留宾客，兴穿掘，营种植，纳群畜。',
            '月刑' => '月刑是月建刑伤之地，所以忌讳的与三煞相同。',
            '月厌' => '该日又称大祸日，是忌嫁娶、赴任、搬家等的凶日。',
            '灾煞' => '灾煞主管灾难，疾病，困厄之事，诸事不宜。',
            '直星' => '不及煞贡、人专二星，值此星，名曰：金贵德入宅，凡上官、嫁娶、开店、修造、葬埋，遇此，三年之内，有吉庆事，居官者加官进禄，庶人百事称心，牛马兴旺，当年招横财，生财致富，但直星如遇金神七煞值日，最凶险，半年内见凶事，五月内主夫妇累遭官事，切不可犯，用者宜忌之，切记避之。',
            '制日' => '制日，指的是天干克制地支的意思。制日当值有利于行军。天干克制地支就是得其人和，自己一方能都克制对方。',
            '重日' => '红喜事吉，凶事葬事凶。这日禁忌做凶事，恐怕重犯。利于做吉事。好事成双。',
            '重阳' => '当日有大事勿用的说法，忌讳搬家。',
            '专日' => '专日，指的是天干合地支的五行都相同的意思，转日当值禁忌出动军队。余事皆可。',
            '奏书' => '是太岁之贵神，与岁德、月德同临化喜神，执掌奏记，主持伺察，宜祭祀，求福，营建宫室，修饰垣墙大吉。是察私屈扬德意之神。所主理之处适宜举荐贤能，对国家有益处。',
            '披麻杀' => '披麻杀主有丧亊或遇到灾祸。不吉利，宜避之。',
            '五鬼' => '五鬼指五瘟之神，主有各种疾病；诸事不利。',
            '刑冲' => '该日与缘主八字即相刑也相冲（好事怕冲，不动则伤，未来发展挫折）,此日不吉，宜避之。',
            '刑' => '该日与缘主相刑（表象为牢狱、疾病、伤残、痛苦）,此日不吉，宜避之。',
            '冲' => '该日与缘主相冲（表象为婚姻感情不顺，有分开之象）,此日不吉，宜避之。',
            '宝光' => '宝光星、天德星，其时大亨，作事有成，利有攸往，出行吉。当值之日适宜兴动各种事物，能为事情的顺利有促进作用。',
            '金匮' => '又称金匮黄道吉日，福德星，月仙星，利释道用事，阍者女子用事，吉。宜嫁娶，不宜整戎伍，余事皆可。',
            '明堂' => '又称明堂黄道吉日，贵人星，明辅星，利见大人，利有攸往，怕作必成。',
            '青龙' => '又称青龙黄道吉日，天乙星，天贵星，利有攸往，所作必成，所求皆得。',
            '司命' => '又称司命黄道吉日，凤辇星，月仙星，此时从寅至申时用事大吉，从酉至丑时有事不吉，即白天吉，晚上不利。',
            '玉堂' => '又称玉堂黄道吉日，少微星，天开星，百事吉，求事成，出行有财，宜文书喜庆之事。',
            '白虎' => '又称白虎黑道凶日，今日是白虎黑道凶日，白虎者为岁中凶神，犯之者主丧服之灾，切宜慎之。',
            '勾陈' => '又称勾陈黑道凶日，地狱星，此时所作一切事，有始无终，先喜后悲，不利攸往。起造安葬，犯此绝嗣。',
            '天牢' => '又称天牢黑道凶日，镇神星，阴人用事皆吉，其余都不利。',
            '天刑' => '又称天刑黑道凶日，天刑星，利于出师，战无不克，其他动作谋为皆不宜用，大忌词讼。',
            '玄武' => '又称玄武黑道凶日，天狱星，君子用之吉，小人用之凶，忌词讼博戏。',
            '朱雀' => '又称朱雀黑道凶日，天讼星，利用公事，常人凶，诸事忌用，谨防争讼。所理值方位，当值之日都不可以兴土功、营屋舍、搬家、嫁娶。',
            '黄历不宜' => '此日黄历忌搬家。',
            '回头贡杀' => '命主生肖今日犯回头贡杀，应规避。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 十恶大败
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSHiEr(string $dgz): bool
    {
        // 十恶大败
        $listShiEr = ['甲辰', '乙巳', '丙申', '丁亥', '戊戌', '己丑', '庚辰', '辛巳', '壬申', '癸亥'];
        return in_array($dgz, $listShiEr);
    }

    /**
     * 月破（破日）
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYuePo(string $mdz, string $ddz): bool
    {
        $list = ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天马
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianMa(string $mdz, string $ddz): bool
    {
        $list = ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 民日
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkMingRi(string $mdz, string $ddz): bool
    {
        $list = ['寅午', '巳午', '申午', '亥酉', '卯酉', '午酉', '酉子', '子子', '辰子', '未卯', '戌卯', '丑卯'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 五墓
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkWuMu(string $mdz, string $dgz): bool
    {
        $list = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 月害
     * @param int $ynum 农历月份
     * @param string $dz 日支
     * @return bool
     */
    protected function checkYueHai(int $ynum, string $dz): bool
    {
        $list = ['1|巳', '2|辰', '3|卯', '4|寅', '5|丑', '6|子', '7|亥', '8|戌', '9|酉', '10|申', '11|未', '12|午'];
        return in_array($ynum . '|' . $dz, $list);
    }

    /**
     * 归忌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkGuiJi(string $mdz, string $ddz): bool
    {
        $list = ['子寅', '丑子', '寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return array
     */
    protected function huiTouGongShaLiu(array $jiNian): array
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $sxBase = new SxBase();
        $result = [];
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $result = [
                    'dzs' => implode('', $v),
                    'dz' => $k,
                    'sx' => $sxBase->getsxByDz($k),
                ];
                break;
            }
        }
        return $result;
    }
}
