<?php
// +----------------------------------------------------------------------
// | Kaiyejiri. 开业吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v3;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\DateConvertTraits;
use app\traits\jiri\JiRiCheckBadTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Kaiyejiri
{
    use JiRiCheckBadTraits;
    use JiRiCheckTraits;
    use DateConvertTraits;

    /**
     * @var Ex
     */
    protected $lunar;

    /**
     * 传入数据
     * @var array
     */
    protected $orginData;

    /**
     * 开业吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            // 性别 男 0 女 1
            'sex' => input('sex', 0, 'intval'),
            // 日期范围 1-12月
            'month' => input('month', 1, 'intval'),
            // 请求日期
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'month|展示月份' => ['require', 'between:1,25'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $dayList = $this->getDayList();
        return [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 提示数据
            'nodz' => $this->getNodz(),
            // 吉日
            'ji' => $dayList,
        ];
    }

    /**
     * 提示数据
     * @return array
     */
    protected function getNodz()
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $ydz = $jiNian['y'][1];
        $ddz = $jiNian['d'][1];
        $cdz1 = $this->getChongDz($ydz);
        $cdz2 = $this->getChongDz($ddz);
        $str1 = "{$cdz1}日、{$cdz2}日；因{$cdz1}{$ydz}相冲，{$cdz2}{$ddz}相冲";
        $shaArr = $this->getTianGanSiSha($ydz);
        return [
            'one' => $str1,
            'two' => "{$shaArr['no']}日{$shaArr['no']}时",
            'ju' => implode('', $shaArr['ju']),
            'no' => $shaArr['no'],
            'sx' => $shaArr['sx'],
        ];
    }

    /**
     * 根据地支获得对应的地支
     * @param string $dz
     * @return string
     */
    protected function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    protected function getTianGanSiSha(string $dz): array
    {
        $list = [
            "丑" => ["寅", "午", "戌"], "辰" => ["巳", "酉", "丑"], "戌" => ["亥", "卯", "未"], "未" => ["申", "子", "辰"],
        ];
        $result = ['ju' => ["寅", "午", "戌"], 'no' => '丑', 'sx' => '牛'];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 吉日
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        // 吉时
        $listJiShi = [
            '甲子' => ['酉'], '乙丑' => ['卯', '申'], '丙寅' => ['未'],
            '丁卯' => ['午', '未'], '戊辰' => ['巳', '申'], '己巳' => ['辰', '午'],
            '庚午' => ['卯', '申'], '辛未' => ['申'], '壬申' => ['辰', '巳'],
            '癸酉' => ['午'], '甲戌' => ['巳', '申'], '乙亥' => ['辰', '午'],
            '丙子' => ['酉'],
            '丁丑' => ['巳', '申'],
            '戊寅' => ['未'],
            '己卯' => ['卯', '午'],
            '庚辰' => ['巳', '申'],
            '辛巳' => ['辰', '午'],
            '壬午' => ['卯', '酉'],
            '癸未' => ['巳', '申'],
            '甲申' => ['辰', '巳'],
            '乙酉' => ['午', '未'],
            '丙戌' => ['巳', '申'],
            '丁亥' => ['辰', '午'],
            '戊子' => ['酉'],
            '己丑' => ['卯', '巳'],
            '庚寅' => ['未'],
            '辛卯' => ['卯', '午'],
            '壬辰' => ['巳', '酉'],
            '癸巳' => ['辰', '午'],
            '甲午' => ['卯', '申'],
            '乙未' => ['申'],
            '丙申' => ['巳'],
            '丁酉' => ['午', '未'],
            '戊戌' => ['巳', '申'],
            '己亥' => ['辰', '午'],
            '庚子' => ['酉'],
            '辛丑' => ['卯', '巳'],
            '壬寅' => ['未'],
            '癸卯' => ['卯', '午'],
            '甲辰' => ['巳', '酉'],
            '乙巳' => ['辰', '午'],
            '丙午' => ['卯', '申'],
            '丁未' => ['巳', '申'],
            '戊申' => ['辰', '巳'],
            '己酉' => ['午', '未'],
            '庚戌' => ['巳', '申'],
            '辛亥' => ['辰', '午'],
            '壬子' => ['辰', '酉'],
            '癸丑' => ['卯', '巳'],
            '甲寅' => ['未'],
            '乙卯' => ['卯', '午'],
            '丙辰' => ['巳', '酉'],
            '丁巳' => ['辰', '午'],
            '戊午' => ['卯', '酉'],
            '己未' => ['巳', '申'],
            '庚申' => ['辰', '巳'],
            '辛酉' => ['午', '未'],
            '壬戌' => ['巳'],
            '癸亥' => ['辰', '午'],
        ];
        // 煞向
        $listShaXian = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $uYdz = $jiNian['y'][1];
        $uDdz = $jiNian['d'][1];
        $uDtg = $jiNian['d'][0];
        // 测算天数
        $limitNum = $this->getAcquisitionDays($this->orginData['otime'], $this->orginData['month']);
        $list = [];
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳',
            '10_1' => '寒衣',
        ];
        $explain = [];
        for ($i = 1; $i <= $limitNum; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $keyYm = date('Y年m月', $time);
            $year = date('Y', $time);
            $huangli = Huangli::date($time);
            $base1 = $huangli->getLunarByBetween();
            $jiNian1 = $base1['jinian'];
            $ytg1 = $jiNian1['y'][0];
            $ydz1 = $jiNian1['y'][1];
            $mdz1 = $jiNian1['m'][1];
            $ddz1 = $jiNian1['d'][1];
            $dtg1 = $jiNian1['d'][0];
            $dgz1 = implode('', $jiNian1['d']);
            $monthNum = (int)$base1['_nongli']['m'];
            $ziRi = $huangli->getZhiRi();
            $shenSha = $ziRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $bool = true;
            $noList = [];
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初一）七月半（七月十五）
            if (in_array($nongliNumberStr, ['7_15', '9_9', '10_1'])) {
                $noList[] = $listNo[$nongliNumberStr] ?? '';
                $bool = false;
            }
            $chongXinStr = '';
            if (BaziExt::getXianChongDz($uDdz, $ddz1)) {
                $bool = false;
                $chongXinStr .= '冲';
            }
            if (BaziExt::getXianXinDz($uDdz, $ddz1)) {
                $bool = false;
                $chongXinStr .= '刑';
            }
            $chongXinStr2 = '';
            if (BaziExt::getXianChongDz($uYdz, $ddz1)) {
                $bool = false;
                $chongXinStr2 .= '冲';
            }
            if (BaziExt::getXianXinDz($uYdz, $ddz1)) {
                $bool = false;
                $chongXinStr2 .= '刑';
            }
            $fenxi = [];
            if ($chongXinStr2) {
                $fenxi[] = ['年', $chongXinStr2];
            }
            if ($chongXinStr) {
                $fenxi[] = ['日', $chongXinStr];
            }
            $listJq = $this->getJieQiDay($year);
            $jieQi = $listJq[$timeStr] ?? '';
            if ($jieQi === '清明') {
                $bool = false;
                $noList[] = '清明';
            }
            // 明天
            $nextTime = $time + 86400;
            $nextDay = date('Y-m-d', $nextTime);
            $nextYear = date('Y', $nextTime);
            $listNextJq = $this->getJieQiDay($nextYear);
            $nextJq = $listNextJq[$nextDay] ?? '';
            if (in_array($nextJq, ['春分', '夏至', '秋分', '冬至'])) {
                $bool = false;
                $noList[] = '四离日';
            } elseif (in_array($nextJq, ["立春", "立夏", "立秋", "立冬"])) {
                $bool = false;
                $noList[] = '四绝日';
            }
            // 杨公忌日
            if ($this->checkYangGongOnly($nongliNumberStr)) {
                $bool = false;
                $noList[] = '杨公忌日';
            }
            $tmpMdzDdz = $mdz1 . $ddz1;
            if ($this->checkXiaoHongSha($tmpMdzDdz)) {
                $bool = false;
                $noList[] = '小红沙';
            }

            if ($ziRi['huan_dao'] == '黑道') {
                //$noList[] = $shaShen . '黑道日';
                $bool = false;
            }
            // 去掉劫煞、灾煞、岁煞、月刑、月厌、大时、天吏、四废、五墓、归忌、往亡、岁破、月煞、小耗、天贼、四耗、四穷
            if ($this->checkJieSha($mdz1, $ddz1)) {
                $noList[] = '劫煞日';
                $bool = false;
            }
            if ($this->checkZaiSha($mdz1, $ddz1)) {
                $noList[] = '灾煞日';
                $bool = false;
            }
            if ($this->checkYueXin($mdz1, $ddz1)) {
                $noList[] = '月刑日';
                $bool = false;
            }
            if ($this->checkYueYan($mdz1, $ddz1)) {
                $noList[] = '月厌日';
                $bool = false;
            }
            if ($this->checkDaShi($mdz1, $ddz1)) {
                $noList[] = '大时日';
                $bool = false;
            }
            if ($this->checkTianLi($mdz1, $ddz1)) {
                $noList[] = '天吏日';
                $bool = false;
            }
            if ($this->checkSiFei($mdz1, $dgz1)) {
                $noList[] = '四废日';
                $bool = false;
            }
            if ($this->checkWuMu($mdz1, $dgz1)) {
                $noList[] = '五墓日';
                $bool = false;
            }
            if ($this->checkWangWang($mdz1, $ddz1)) {
                $noList[] = '往亡日';
                $bool = false;
            }
            // 岁破
            if (BaziExt::getXianChongDz($ddz1, $ydz1)) {
                $bool = false;
                $noList[] = '岁破日';
            }
            if ($this->checkYueSha($mdz1, $ddz1)) {
                $noList[] = '月煞日';
                $bool = false;
            }
            if ($this->checkXiaoHao($mdz1, $ddz1)) {
                $noList[] = '小耗日';
                $bool = false;
            }
            $yueRi2 = $mdz1 . $dgz1;
            // 四耗   四穷日
            if ($this->checkSiHao($yueRi2)) {
                $noList[] = '四耗日';
                $bool = false;
            }
            if ($this->checkSiQiong($yueRi2)) {
                $noList[] = '四穷日';
                $bool = false;
            }
            $jiNianTmp = $jiNian1;
            unset($jiNianTmp['h']);
            $position = $huangli->getPosition();
            $hehai = $huangli->getTodayHeHai();
            $jiXiongYiJi = $huangli->getJiXiong();
            $tmpRes = [
                'date' => [
                    'y' => date('Y', $time),
                    'm' => date('m', $time),
                    'd' => date('d', $time),
                    'week' => Huangli::getWeekChs($time), // 星期
                    'jinian' => $jiNianTmp,
                    'nongli' => $base1['nongli'],
                    '_nongli' => $base1['_nongli'],
                ],
                // 财神
                'cai_shen' => $position['cai_shen'],
                // 煞向
                'sha_xian' => $listShaXian[$jiNianTmp['d'][1]],
                // 相冲
                'chong' => $hehai['xian_chong'],
                // 吉神
                'jishen' => $jiXiongYiJi['jishen'],
                'xiong' => $jiXiongYiJi['xiong'],
                // 算法合不合，不合为空
                'type' => 'buyi',
                'type_hd' => $ziRi['huan_dao'] == '黑道' ? 0 : 1,
                // 值日
                'xing_shen' => $ziRi['xing_shen'],
                'shensha' => $shenSha,
                'reason' => [],
                'fenxi' => [],
            ];
            $tmpTotal = (int)($list[$keyYm]['total'] ?? 0);
            $gongSha = $this->huiTouGongShaLiu($jiNianTmp);
            if ($gongSha && $uYdz == $gongSha) {
                $noList[] = '回头贡杀';
                $bool = false;
            }
            if ($bool) {
                $reason = [];
                $hourResult = $this->getJishi($jiNian1['d'], $shenSha);
                $tmpRes['type'] = $this->getJiType($jiNianTmp);
                if ($this->checkTianDe($mdz1 . $ddz1) || $this->checkTianDe($mdz1 . $dtg1)) {
                    $reason[] = '天德';
                }
                if ($this->checkTianDeHe($mdz1 . $ddz1) || $this->checkTianDeHe($mdz1 . $dtg1)) {
                    $reason[] = '天德合';
                }
                if ($this->checkYueDe($mdz1 . $ddz1)) {
                    $reason[] = '月德';
                }
                if ($this->checkYueDeHe($mdz1 . $ddz1) || $this->checkYueDeHe($mdz1 . $dtg1)) {
                    $reason[] = '月德合';
                }
                if ($this->checkTianSe($mdz1, $dgz1)) {
                    $reason[] = '天赦';
                }
                if ($this->checkYueEn($mdz1 . $dtg1)) {
                    $reason[] = '月恩';
                }
                if ($this->checkSiXian($mdz1, $dtg1)) {
                    $reason[] = '四相';
                }
                if ($this->checkTianYuan($mdz1 . $dgz1)) {
                    $reason[] = '天愿';
                }
                if ($this->checkShiDe($mdz1 . $ddz1)) {
                    $reason[] = '时德';
                }
                if ($this->checkBuJian($monthNum, $dgz1)) {
                    $reason[] = '不将';
                }
                if ($this->checkXianXing($monthNum, $dgz1)) {
                    $reason[] = '显星';
                }
                if ($this->checkChuanXing($monthNum, $dgz1)) {
                    $reason[] = '传星';
                }
                if ($this->checkQuXing($monthNum, $dgz1)) {
                    $reason[] = '曲星';
                }
                if ($this->checkTianYi($uDtg, $ddz1)) {
                    $reason[] = '天乙贵人';
                }
                if ($this->checkJiFen($monthNum, $dgz1)) {
                    $reason[] = '季分';
                }
                if ($this->checkSuiDe($ytg1 . $dtg1)) {
                    $reason[] = '岁德';
                }
                if ($this->checkSuiDeHe($ytg1 . $dtg1)) {
                    $reason[] = '岁德合';
                }
                $tmpRes['gongsha'] = $gongSha;
                $tmpRes['hour'] = $hourResult;
                $tmpRes['reason'] = $reason;
                $tmpTotal++;
            } else {
                if ($ziRi['huan_dao'] !== '黑道') {
                    $tmpRes['shensha'] = '';
                }
                $tmpRes['reason'] = $noList;
                $tmpRes['fenxi'] = $fenxi;
            }
            $list[$keyYm]['title'] = $keyYm;
            $list[$keyYm]['total'] = $tmpTotal;
            $list[$keyYm]['list'][] = $tmpRes;
            foreach ($tmpRes['reason'] as $v) {
                if (isset($explain[$v])) {
                    continue;
                }
                $explain[$v] = $this->getExplain($v);
            }
            if ($tmpRes['shensha']) {
                $explain[$shenSha] = $this->getExplain($shenSha);
            }
        }
        return [
            'explain' => $explain,
            'info' => array_values($list),
        ];
    }

    /**
     * 求四离 四绝 公历
     * 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
     * @param $year
     * @return array
     */
    protected function getJieQiDay($year): array
    {
        $list = [
            '春分', '夏至', '秋分', '冬至', '立春', '立夏', '立秋', '立冬',
        ];
        $jieqi = SolarTerm::getAllJieQi($year);
        $day = [];
        foreach ($jieqi as $k => $v) {
            if (in_array($k, $list)) {
                $day[] = date('Y-m-d', (strtotime($v) - 86400));
            }
        }
        return $day;
    }

    /**
     * 求平日里面的吉日状态
     * @param $jiNian
     * @return string
     */
    protected function getJiType($jiNian): string
    {
        $keyStr = 'ping';
        $listPing = [
            '寅' => ['乙', '亥', '午', '辰', '戌', '子', '亥', '午', '辰', '亥'],
            '卯' => ['甲', '戌', '午', '巳', '亥', '丑', '寅', '乙', '午', '戌'],
            '辰' => ['乙', '酉', '午', '午', '子', '寅', '巳', '巳', '申', '酉'],
            '巳' => ['丙', '申', '酉', '未', '丑', '卯', '申', '未', '戌', '申'],
            '午' => ['丁', '未', '酉', '申', '寅', '辰', '亥', '酉', '子', '未'],
            '未' => ['戊', '午', '酉', '酉', '卯', '巳', '寅', '亥', '寅', '午'],
            '申' => ['己', '巳', '子', '戌', '辰', '午', '巳', '午', '辰', '巳'],
            '酉' => ['庚', '辰', '子', '亥', '巳', '未', '申', '乙', '午', '辰'],
            '戌' => ['辛', '卯', '子', '子', '午', '申', '亥', '巳', '申', '卯'],
            '亥' => ['壬', '寅', '卯', '丑', '未', '酉', '寅', '未', '戌', '寅'],
            '子' => ['癸', '丑', '卯', '寅', '申', '戌', '巳', '酉', '子', '丑'],
            '丑' => ['甲', '子', '卯', '卯', '酉', '亥', '申', '亥', '寅', '子'],
        ];
        $listDa = [
            '寅' => ['丁', '壬', '丙', '辛'],
            '卯' => ['申', '巳', '甲', '己'],
            '辰' => ['壬', '丁', '壬', '丁'],
            '巳' => ['辛', '丙', '庚', '乙'],
            '午' => ['亥', '寅', '丙', '辛'],
            '未' => ['甲', '己', '甲', '己'],
            '申' => ['癸', '戊', '壬', '丁'],
            '酉' => ['寅', '亥', '庚', '乙'],
            '戌' => ['丙', '辛', '丙', '辛'],
            '亥' => ['乙', '庚', '甲', '己'],
            '子' => ['巳', '申', '壬', '丁'],
            '丑' => ['庚', '乙', '庚', '乙'],
        ];
        if (array_intersect($jiNian['d'], $listPing[$jiNian['m'][1]])) {
            if (array_intersect($jiNian['d'], $listDa[$jiNian['m'][1]])) {
                $keyStr = 'da';
            } else {
                $keyStr = 'xiao';
            }
        }
        return $keyStr;
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $str = $k;
                break;
            }
        }
        return $str;
    }

    /**
     * 劫煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkJieSha(string $mdz, string $ddz): bool
    {
        $list = ['寅亥', '卯申', '辰巳', '巳寅', '午亥', '未申', '申巳', '酉寅', '戌亥', '亥申', '子巳', '丑寅'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueSha(string $mdz, string $ddz): bool
    {
        $list = ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 灾煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkZaiSha(string $mdz, string $ddz): bool
    {
        $list = ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月刑
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueXin(string $mdz, string $ddz): bool
    {
        $list = ['寅巳', '卯子', '辰辰', '巳申', '午午', '未丑', '申寅', '酉酉', '戌未', '亥亥', '子卯', '丑戌'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月厌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueYan(string $mdz, string $ddz): bool
    {
        $list = ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天吏
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianLi(string $mdz, string $ddz): bool
    {
        $list = ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 大时
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkDaShi(string $mdz, string $ddz): bool
    {
        $list = ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 四废
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSiFei(string $mdz, string $dgz): bool
    {
        $list = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
        ];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 五墓
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkWuMu(string $mdz, string $dgz): bool
    {
        $list = ['寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰'];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 归忌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkGuiJi(string $mdz, string $ddz): bool
    {
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        return in_array($mdz . $ddz, $listGuiJi);
    }

    /**
     * 往亡
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkWangWang(string $mdz, string $ddz): bool
    {
        $list = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 小耗
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkXiaoHao(string $mdz, string $ddz): bool
    {
        $list = ['寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 判断四耗日
     * @param string $string 月支+流日柱干支
     * @return bool
     */
    protected function checkSiHao(string $string): bool
    {
        $list = [
            '子辛酉', '丑辛酉', '寅壬子', '卯壬子', '辰壬子', '巳乙卯', '午乙卯', '未乙卯', '申戊午', '酉戊午', '戌戊午', '亥辛酉',
        ];
        return in_array($string, $list);
    }

    /**
     * 四穷日
     * @param string $string 月支+流日柱干支
     * @return bool
     */
    protected function checkSiQiong(string $string): bool
    {
        $list = [
            '寅乙亥', '卯乙亥', '辰乙亥', '巳丁亥', '午丁亥', '未丁亥', '申辛亥', '酉辛亥', '戌辛亥', '亥癸亥', '子癸亥', '丑癸亥',
        ];
        return in_array($string, $list);
    }

    /**
     * 天乙贵人 用户日干+流日支
     * @param string $tg 用户日干
     * @param string $dz 流日支
     * @return bool
     */
    protected function checkTianYi($tg, $dz): bool
    {
        $list = [
            '甲丑', '甲未', '戊丑', '戊未', '乙子', '乙申', '己子', '己申', '丙亥', '丙酉',
            '丁亥', '丁酉', '庚寅', '庚寅', '辛午', '辛午', '壬卯', '壬巳', '癸卯', '癸巳',
        ];
        return in_array($tg . $dz, $list);
    }

    /**
     * 吉时
     * @param array $gzDay 日干支
     * @param string $shenSha
     * @return array
     */
    protected function getJishi(array $gzDay, string $shenSha): array
    {
        $dzList = Calendar::DI_ZHI;
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];

        $jn = $this->lunar->getLunarGanzhiYear();
        $ydz1 = $jn[1];

        $result = [];
        $sxBase = new SxBase();
        foreach ($dzList as $v) {
            if (in_array($v, ['子', '丑', '寅'])) {
                continue;
            }
            if (in_array($v, ['子', '丑', '寅', '卯', '戌', '亥']) && $shenSha == '司命') {
                continue;
            }
            $hgz = Huangli::getGanzhiHour($gzDay[0], $v);
            $sx = $sxBase->getsxByDz($v);
            $chongSx = $sxBase->getChong($sx);
            $str = $v . $gzDay[1];
            $str2 = $v . $ydz1;
            if (BaziCommon::getXianChong($str) || BaziCommon::getXianXin($str) || BaziCommon::getXianHai($str)) {
                continue;
            }
            if (BaziCommon::getXianChong($str2) || BaziCommon::getXianPo($str2) || BaziCommon::getXianHai($str2)) {
                continue;
            }
            $sanSha = Huangli::getSanSha($v);
            $position = Huangli::getPositionbyTg($hgz[0]);
            $result[] = [
                'dz' => $v,
                'h' => $hourList[$v],
                'sha' => $sanSha[0],
                'cai_shen' => $position['cai_shen'],
                'chong' => $chongSx['name'],
            ];
        }
        return array_slice($result, 0, 3);
    }

    /**
     * 解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '传星' => '三皇吉星之一，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '曲星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '天德' => '占得天时，有三合旺气，是上等的吉日，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜开、开业诸事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，装修大吉。',
            '四相' => '拥有四时王相的贵气，有吉神庇佑，利于开业。',
            '时德' => '得到天地舒畅之气，为四时所生，适宜祈福的日子。',
            '不将' => '传统吉日，当日开业，寓意顺顺利利，心想事成。',
            '天乙贵人' => '此日属于易有贵人扶持相助，开业可选该日。',
            '季分' => '宜迁移、开业的传统吉日，寓意美好幸福。',
            '岁德' => '德神护佑的吉日，积福之日，福气汇聚。象征开业平顺，万事如意。',
            '岁德合' => '该日有福神庇佑，可逢凶化吉，易有贵人相助。',
            '劫煞日' => '劫煞为打劫之星，主路遇不安，土匪劫道，有红伤血光之灾。',
            '灾煞日' => '灾煞为灾星，主牢狱凶灾，健康受损，六畜不安，寓意不祥。',
            '岁煞日' => '岁煞是当头太岁，太岁头上动土，招惹是非口舌，官司牢狱破财之事。',
            '月刑日' => '该日主凶破、刑伤。对喜事大有不吉。',
            '月厌日' => '该日又称大祸日，寓意不吉，最好规避。',
            '大时日' => '该日标识表示精光、消减，在运势上容易影响到一个人的心境。',
            '天吏日' => '天吏的解释为天子的官吏，有奉天命而治人罪的寓意。',
            '四废日' => '日值四废，作事不易成功，容易有始无终。',
            '五墓日' => '五墓日是一种忌日，诸喜事不宜。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '岁破日' => '岁破有大事勿用，喜事不宜的说法。',
            '月煞日' => '该星的本质有灾厄，疾病的说法，寓意颇为不吉。',
            '小耗日' => '小耗有损失钱财，遗失，购贵物的说法，需注意避开。',
            '天贼日' => '天贼是专门行盗窃的凶神，遇之恐破财。',
            '四耗日' => '四耗有漏财破财运，财运不稳定，做事不吉，难有所成的说法。',
            '四穷日' => '四穷有财运波动，多起伏不稳的说法，最好避开。',
            '四离日' => '日值四离，多有不吉，大事勿用。',
            '清明' => '这日通常都是祭奠先祖，缅怀已故之人，最好规避。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，选择该日有些不吉。',
            '重阳' => '当日有大事勿用的说法，开业最好避开。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '小红沙' => '红沙日有诸事不宜的说法，需要注意避开。',
            '杨公忌日' => '这日有诸事不宜，大事勿用的说法，最好避开。',
            '青龙' => '传统习俗中的吉利日子，此日有祥瑞之召的日子。',
            '明堂' => '传统习俗中的吉利日子。该日是明辅星的日子，利于百事。',
            '金匮' => '金匮属于福德星，乃是黄道日，该日有利于入宅、开业等诸喜事。',
            '玉堂' => '传统习俗中的吉利日子，用于开业乃是大吉。',
            '司命' => '传统习俗中的吉利日子，用于开业为上选，当日一切顺利。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克。该日万事皆忌，不宜有大动作。',
            '朱雀' => '天讼星，利用公事，常人凶，喜事忌用。',
            '白虎' => '白虎是天杀星，为凶恶之神，最好规避该日。',
            '天牢' => '天牢有围困，官司的说法。',
            '玄武' => '该日容易出现一些口舌之事，也容易犯部分小人，在一些大事上比较忌讳。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '宝光' => '传统习俗中的吉利日子，利于出行安全，开业大吉。',
            '回头贡杀' => '命主生肖今日犯回头贡杀，应规避。',
        ];
        return $list[$str] ?? '';
    }
}
