<?php
// +----------------------------------------------------------------------
// |  Dingziqm
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\controller\v3;

use app\lib\Utils;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\DingziYi;
use app\model\baobaoqm\Jinyong;
use app\model\baobaoqm\QmjyUse;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;
use think\helper\Str;

class Dingziqm
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 姓数组
     * @var array
     */
    protected array $xingArr = [];

    /**
     * 拼音数组
     * @var array
     */
    protected array $wordPys = [];

    /**
     * 已使用的名字
     * @var array
     */
    protected array $mingIn = [];

    /**
     * 已使用的字
     * @var array
     */
    protected array $ziUse = [];

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 姓氏
            'xing' => input('xing', '', 'trim'),
            // 生日
            'time' => input('time', '', 'trim'),
            // 性别 0 男 1 女
            'sex' => input('sex', 0, 'intval'),
            // 定字的位置 0不限 1 第一个字 2 第二个字
            'pos' => input('pos', 1, 'intval'),
            // 是否已出生 0 未出生 1 已出生
            'born' => input('born', 1, 'intval'),
            // 中间字
            'zi' => input('zi', '', 'trim'),
            // 禁用字
            'jin' => input('jin', '', 'trim'),
            // 订单时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
            // 取出的字数
            'total' => input('total', 50, 'intval'),
            // 名字列表
            'mings' => input('mings', '', 'trim'),
        ];
        $data['jin'] = str_replace(' ', '', $data['jin']);
        $validate = Validate::rule(
            [
                'xing|姓氏' => ['require', 'chs', 'length:1,2'],
                'sex|性别' => ['require', 'in:0,1'],
                'time|出生时间' => ['require', 'date', 'before:2099-12-31', 'after:1900-01-01'],
                'zi|字' => ['chs', 'length:1'],
                'jin|禁用字' => ['chs'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        // 校验
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->orginData = $data;
        $result = [
            'base' => $this->lunar->getLunarByBetween(),
        ];
        if ($data['mings']) {
            $ming = explode(',', $data['mings']);
        } else {
            $str = $this->orginData['xing'] . $this->orginData['zi'];
            $str .= $this->orginData['jin'];
            $this->wordPys = Cnword::getInfo2(Utils::mbStrSplit($str));
            $num1 = (int)($data['total'] / 2);
            $num2 = $data['total'] - $num1;
            if ($data['zi']) {
                if ($data['born']) {
                    $ming = $this->getMingWithZi($data['sex'], $data['total']);
                } else {
                    $ming1 = $this->getMingWithZi(0, $num1);
                    $ming2 = $this->getMingWithZi(1, $num2);
                    $ming = array_merge($ming1, $ming2);
                }
            } else {
                if ($data['born']) {
                    $ming = $this->getMings($data['sex'], $data['total']);
                } else {
                    $ming1 = $this->getMings(0, $num1);
                    $ming2 = $this->getMings(1, $num2);
                    $ming = array_merge($ming1, $ming2);
                }
            }
        }
        $result['ming'] = $this->getMingDetail($ming);
        return $result;
    }

    /**
     * 获得订字起名的名字数组
     * @param $sex
     * @param $num
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMingWithZi($sex, $num)
    {
        $list = DingziYi::whereIn('sex', [$sex, 2])
            ->limit(5000)
            ->cache(300)
            ->select();
        $list = $list->shuffle();
        $result = [];
        $i = 0;
        // py不带音标 py2 音标
        $jinPy = $this->getJinPy($this->orginData['jin']);
        // print_r("禁用拼音：" . json_encode($jinPy, JSON_UNESCAPED_UNICODE) . "\n");
        // $jinPy2 = $this->getJinPy($str);
        // py音标 py2 不带音标
        $listJin = $this->getJinYongAll();
        $dingInfo = $this->wordPys[$this->orginData['zi']] ?? ['py' => '', 'py2' => ''];
        $dingPy = DingziYi::pyStrToArray($dingInfo['py']);
        $dingPy2 = DingziYi::pyStrToArray($dingInfo['py2']);
        $pyUse = [];
        foreach ($list as $info) {
            if ($i >= $num) {
                break;
            }
            // 去除用户填写的禁用文字
            if ($this->arrayContains($info['pyName'], $jinPy['py2'])) {
                // $aaa[$info['zi']] = "去除用户填写的禁用文字，" . json_encode($info['pyName'], JSON_UNESCAPED_UNICODE);
                continue;
            }
            if ($this->arrayContains($info['py2Name'], $jinPy['py'])) {
                // $aaa[$info['zi']] = "去除用户填写的禁用文字，" . json_encode($info['py2Name'], JSON_UNESCAPED_UNICODE);
                continue;
            }
            // 类型 1 第一个字 2 第二个字
            $posC = $this->orginData['pos'];
            $maxNum = (int)($num / 2);
            switch ($this->orginData['pos']) {
                case 0:
                    if ($i >= $maxNum) {
                        $posC = 2;
                    } else {
                        $posC = 1;
                    }
                    break;
                case 1:
                    $posC = 2;
                    break;
                case 2:
                    $posC = 1;
                    break;
            }
            if ($info['pos'] != 0 && Str::contains($posC, $info['pos'])) {
                // $aaa[$info['zi']] = "位置限制：{$posC}|{$info['pos']}";
                continue;
            }
            if ($posC == 1) {
                $ming = $info['zi'] . $this->orginData['zi'];
                $tmpPy = Utils::dikaer([$info['pyName'], $dingPy2]);
                $tmpPy2 = Utils::dikaer([$info['py2Name'], $dingPy]);//不带音标
            } else {
                $ming = $this->orginData['zi'] . $info['zi'];
                $tmpPy = Utils::dikaer([$dingPy2, $info['pyName']]);
                $tmpPy2 = Utils::dikaer([$dingPy, $info['py2Name']]);//不带音标
            }
            if (in_array($ming, $listJin['zi'])) {
                // $aaa[$info['zi']] = "禁用库名：{$ming}";
                continue;
            }
            if ($this->arrayContains($tmpPy, $listJin['py'])) {
                // $aaa[$info['zi']] = "禁用拼音：" . json_encode($tmpPy, JSON_UNESCAPED_UNICODE);
                continue;
            }
            if ($this->arrayContains($tmpPy2, $listJin['py2'])) {
                // $aaa[$info['zi']] = "禁用拼音2：" . json_encode($tmpPy2, JSON_UNESCAPED_UNICODE);
                continue;
            }
            if ($this->arrayContains($tmpPy2, $pyUse)) {
                // 是否已使用过
                continue;
            }
            $pyUse = array_values(array_unique(array_merge($tmpPy2, $pyUse)));
            if (in_array($ming, $this->mingIn)) {
                // $aaa[$info['zi']] = "名字已使用过。{$ming}|{$sex}";
                continue;
            }
            $this->mingIn[] = $ming;
            $result[] = $ming . '|' . $sex;
            $i++;
        }
        return $result;
    }

    /**
     * 检查数组中数据是否存在另一数组中
     * @param array $arr 被检查的数据
     * @param array $haystack 核对的数据
     * @return bool
     */
    protected function arrayContains(array $arr, array $haystack): bool
    {
        $bool = false;
        foreach ($arr as $v) {
            if (in_array($v, $haystack)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 获得名字列表
     * @param $sex
     * @param $num
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMings($sex, $num)
    {
        $query = QmjyUse::whereIn('sex', [$sex, 2])
            ->whereIn('typem', [1, 12, 13, 123]);
        $total = (clone $query)->cache(1200)->count();
        $maxPage = (int)($total / 500);
        $diff = $total % 500;
        if ($diff > 100) {
            $maxPage++;
        }
        $maxPage = max($maxPage, 1);
        $page = rand(1, $maxPage);
        $list = QmjyUse::whereIn('sex', [$sex, 2])
            ->whereIn('typem', [1, 12, 13, 123])
            ->page($page)
            ->limit(500)
            ->cache(300)
            ->select();
        $list = $list->shuffle();
        $result = [];
        $i = 0;
        // py不带音标 py2 带音标
        $jinPy = $this->getJinPy($this->orginData['jin']);
        // py音标 py2 不带音标
        $listJin = $this->getJinYongAll();
        $pyUse = [];
        foreach ($list as $info) {
            if ($i >= $num) {
                break;
            }
            // 音标
            $tmpPy = str_replace(' ', '', $info['py']);
            $tmpPyArr = $tmpPy ? explode('|', $tmpPy) : [];

            if ($jinPy['py2'] && $this->arrayContains($tmpPyArr, $jinPy['py2'])) {
                continue;
            }
            // 文字
            if (in_array($info['title'], $listJin['zi'])) {
                continue;
            }
            // 不带音标
            $tmpPy2 = str_replace(' ', '', $info['py2']);
            $tmpPy2Arr = $tmpPy ? explode('|', $tmpPy2) : [];
            if ($this->arrayContains($tmpPyArr, $listJin['py'])) {
                continue;
            }
            if ($this->arrayContains($tmpPy2Arr, $listJin['py2'])) {
                continue;
            }
            // 判断是否已使用
            if ($this->arrayContains($tmpPyArr, $pyUse)) {
                continue;
            }
            foreach ($tmpPyArr as $v1) {
                $pyUse[$v1] = $v1;
            }
            if (in_array($info['title'], $this->mingIn)) {
                continue;
            }
            $this->mingIn[] = $info['title'];
            $ziArr = Utils::mbStrSplit($info['title']);
            $bool = false;
            foreach ($ziArr as $v) {
                $tmpNum = $this->ziUse[$v] ?? 0;
                if ($tmpNum > 2) {
                    $bool = true;
                    break;
                }
            }
            if ($bool) {
                continue;
            }
            foreach ($ziArr as $v) {
                $tmpNum = $this->ziUse[$v] ?? 0;
                $tmpNum++;
                $this->ziUse[$v] = $tmpNum;
            }
            $i++;
            $result[] = $info['title'] . '|' . $sex;
        }
        return $result;
    }

    /**
     * 获得所有禁用库列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getJinYongAll(): array
    {
        $info = Jinyong::where('typem', 1)->cache(300)->find();
        $result = [
            'zi' => [],
            // 带音标
            'py' => [],
            // 不带音标
            'py2' => [],
        ];
        if (empty($info)) {
            return $result;
        }
        return [
            'zi' => $info['mings'] ? explode('|', $info['mings']) : [],
            'py' => $info['py'] ? explode('|', $info['py']) : [],
            'py2' => $info['py_no'] ? explode('|', $info['py_no']) : [],
        ];
    }

    /**
     * 获得文字所有拼音
     * @param string $jinStr
     * @return array[]
     */
    protected function getJinPy(string $jinStr)
    {
        $jinArr = $jinStr ? Utils::mbStrSplit($jinStr) : [];
        $jinPy2 = [];
        $jinPy = [];
        foreach ($jinArr as $v) {
            if (!isset($this->wordPys[$v])) {
                continue;
            }
            $py2 = $this->wordPys[$v]['py2'] ?? '';
            if (!empty($py2)) {
                $py2 = str_replace(['、', '，', ''], ',', $py2);
                $py2Arr = explode(',', $py2);
                foreach ($py2Arr as $v2) {
                    $jinPy2[$v2] = $v2;
                }
            }
            $py = $this->wordPys[$v]['py'] ?? '';
            if (!empty($py)) {
                $py = str_replace(['、', '，', ''], ',', $py);
                $pyArr = explode(',', $py);
                foreach ($pyArr as $v2) {
                    $jinPy[$v2] = $v2;
                }
            }
        }
        return [
            // 不含音标
            'py' => array_values($jinPy),
            'py2' => array_values($jinPy2),
        ];
    }

    /**
     * 名字详情
     * @param array $ming
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMingDetail(array $ming)
    {
        $result = [];
        $mingStr = '';
        foreach ($ming as $k => $v) {
            $tmp = explode('|', $v);
            if (count($tmp) != 2) {
                unset($ming[$k]);
                continue;
            }
            $result['mings'][] = [
                'ming' => $tmp[0],
                'gender' => (int)$tmp[1],
                'zis' => Utils::mbStrSplit($tmp[0]),
            ];
            $mingStr .= $tmp[0];
        }
        $arr = array_values(array_unique(Utils::mbStrSplit($mingStr)));
        $listZi = Cnword::getInfo2($arr);
        foreach ($listZi as $tmp2) {
            $result['info'][$tmp2['zi']] = [
                'zi' => $tmp2['zi'],
                'big5' => $tmp2['big5'],
                'bihua' => $tmp2['bihua'],
                'bihua2' => $tmp2['bihua2'],
                'bushou' => $tmp2['bushou'] ?? '',
                'jiegou' => $tmp2['jiegou'],
                'py' => $this->getOnePy($tmp2['py']),
                'py2' => $this->getOnePy($tmp2['py2']),
                'wx' => $tmp2['wx'],
                'detail' => $tmp2['detail'],
                'reason' => $tmp2['detail3']['mean'] ?? '',
            ];
        }
        return $result;
    }

    /**
     * 获得拼音
     * @param string $str
     * @return string
     */
    protected function getOnePy(string $str): string
    {
        if (empty($str)) {
            return '';
        }
        $str = str_replace(['，', ' ', '、'], ',', $str);
        $pyArr = explode(',', $str);
        return $pyArr[0];
    }
}
