<?php
// +----------------------------------------------------------------------
// | Tichejiri 提车吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v3;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\lib\Utils;
use app\traits\jiri\JiRiCheckBadTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Tichejiri
{
    use JiRiCheckTraits;
    use JiRiCheckBadTraits;

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 传入数据
     * @var array
     */
    protected array $orginData;

    /**
     * 年份对应的节气信息
     * @var array
     */
    protected array $jieQiYear = [];

    /**
     * @var array
     */
    protected array $chongXingU = [];

    /**
     * 开业吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            // 性别 男 0 女 1
            'sex' => input('sex', 0, 'intval'),
            // 日期范围 1-12月
            'month' => input('month', 1, 'intval'),
            // 请求日期
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'month|展示月份' => ['require', 'between:1,25'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);

        $base = $this->lunar->getLunarByBetween();
        $noDz = $this->getNodz();
        $this->chongXingU = [
            'chong' => $noDz['chong'], 'xing' => $noDz['xing'],
        ];
        $dayList = $this->getDayList();
        return [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 提示数据
            'nodz' => $noDz,
            // 吉日
            'ji' => $dayList,
        ];
    }

    /**
     * 提示数据
     * @return array
     */
    protected function getNodz(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $ydz = $jiNian['y'][1];
        //$ddz = $jiNian['d'][1];
        $dzArr = array_unique([$ydz]);
        $reason = [];
        $chongRes = [];
        $xingRes = [];
        foreach ($dzArr as $dz) {
            $chong = BaziCommon::getChongDz($dz);
            $chongRes[$chong] = $chong;
            $reason[] = "{$chong}{$dz}相冲";
            $xing = BaziCommon::getXingByDz($dz);
            foreach ($xing as $v) {
                $xingRes[$v] = $v;
                $reason[] = "{$v}{$dz}相刑";
            }
        }
        $resDz = array_unique(array_merge($chongRes, $xingRes));
        $reason = array_unique($reason);
        $str1 = implode('日、', $resDz) . "日；因" . implode('，', $reason);
        $shaArr = $this->getTianGanSiSha($ydz);
        $uYear = (int)$this->lunar->dateTime->format('Y');
        $jq = SolarTerm::getAllJieQi($uYear);
        $liChun = $jq['立春'];
        return [
            'one' => $str1,
            'two' => "{$shaArr['no']}日{$shaArr['no']}时",
            'ju' => implode('', $shaArr['ju']),
            'no' => $shaArr['no'],
            'sx' => $shaArr['sx'],
            'chong' => array_values($chongRes),
            'xing' => array_values($xingRes),
            'li_chun' => date('Y年m月d日H时i分s秒', strtotime($liChun)),
        ];
    }

    /**
     * 根据地支获得对应的地支
     * @param string $dz
     * @return string
     */
    protected function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    protected function getTianGanSiSha(string $dz): array
    {
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $result = ['ju' => [], 'no' => '', 'sx' => ''];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 吉日
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        $otime = strtotime($this->orginData['otime']);
        $long = $this->orginData['month'];
        $maxTime = strtotime("+{$long}month", $otime);
        $limitNum = round(($maxTime - $otime) / 86400);
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳',
            '10_1' => '寒衣',
        ];
        $result = [];
        $explain = [];
        $changeExplain = [];
        $listHourUse = $this->getHourUse();
        $noHourSiMing = ["酉", "戌", "亥"];
        $ygzU = $this->lunar->getLunarGanzhiYear();
        $ydzU = $ygzU[1];
        $sxBase = new SxBase();
        for ($i = 1; $i <= $limitNum; $i++) {
            $curTime = $otime + ($i * 86400);
            $cYear = date('Y', $curTime);
            $cGongli = date('Y-m-d', $curTime);
            $keyStr = date('Y-m', $curTime);
            $jieqiArr = $this->getJieqiByYear($cYear);
            $jieQi = $jieqiArr[$cGongli] ?? '';
            $huangli = Huangli::date($cGongli);
            $base = $huangli->getLunarByBetween();
            $week = Huangli::getWeekChs($curTime);
            $jxArr = $huangli->getJiXiong();
            $jiShenHl = $this->filterJXShen($jxArr['jishen']);
            $xiongHl = $this->filterJXShen($jxArr['xiong'], 1);
            $zhiRi = $huangli->getZhiRi();
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $hdType = $zhiRi['huan_dao'] == '黑道' ? 0 : 1;
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $mdNum = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            $ydz = $jiNian['y'][1];
            $mdz = $jiNian['m'][1];
            $ddz = $jiNian['d'][1];
            $dtg = $jiNian['d'][0];
            $ytg = $jiNian['y'][0];
            $dzMd = $mdz . $ddz;
            $dgz = implode('', $jiNian['d']);
            $reason = [];
            $reasonJr = [];
            $xingChongArr = [];
            if (in_array($ddz, $this->chongXingU['chong'])) {
                $xingChongArr[] = '冲';
            }
            if (in_array($ddz, $this->chongXingU['xing'])) {
                $xingChongArr[] = '刑';
            }
            if ($this->checkYangGongOnly($mdNum)) {
                $reasonJr[] = '杨公忌日';
            }
            if ($jieQi == '清明') {
                $reasonJr[] = '清明';
            }
            foreach ($listNo as $k => $v) {
                if ($k == $mdNum) {
                    $reasonJr[] = $v;
                }
            }
            if ($this->checkSiFei($mdz, $dgz)) {
                $reasonJr[] = '丧车煞日';
            }
            // 四离 四绝 四废 受死 往亡 月破 岁破
            if ($this->checkSuiPo($ydz . $ddz)) {
                $reason[] = '岁破';
            }
            if ($this->checkShouSi($dzMd)) {
                $reason[] = '受死';
            }
            if ($this->checkWangWang($mdz, $ddz)) {
                $reason[] = '往亡';
            }
            $xiongRes = [];
            if ($this->checkYuePo($dzMd)) {
                $xiongRes[] = '月破';
            }
            $tomorrow = date('Y-m-d', $curTime + 86400);
            $jqNext = $jieqiArr[$tomorrow] ?? '';
            if (in_array($jqNext, ["立春", "立夏", "立秋", "立冬"])) {
                // 四绝
                $reason[] = '四绝';
            } elseif (in_array($jqNext, ["春分", "秋分", "夏至", "冬至"])) {
                $reason[] = '四离';
            }
            $tianDe = $this->checkTianDe($dzMd) || $this->checkTianDe($mdz . $dtg);
            $yueDe = $this->checkYueDe($dzMd) || $this->checkYueDe($mdz . $dtg);
            $tianDeHe = $this->checkTianDeHe($dzMd) || $this->checkTianDeHe($mdz . $dtg);
            $yueDeHe = $this->checkYueDeHe($dzMd) || $this->checkYueDeHe($mdz . $dtg);
            $tianYuan = $this->checkTianYuan($mdz . $dgz);
            $deArray = [];
            if ($tianDe) {
                $deArray[] = '天德';
            }
            if ($yueDe) {
                $deArray[] = '月德';
            }
            if ($tianDeHe) {
                $deArray[] = '天德合';
            }
            if ($yueDeHe) {
                $deArray[] = '月德合';
            }
            $jieSha2 = $this->checkJieSha2($mdz, $deArray);
            $zaiSha2 = $this->checkZaiSha2($mdz, $deArray) ? 1 : 0;
            $daShi2 = $this->checkDaShi2($mdz, $deArray);
            $tianLi2 = $this->checkTianLi2($mdz, $deArray) ? 1 : 0;
            $wuMu2 = $this->checkWuMu2($mdz, $deArray) ? 1 : 0;
            //$yueXing2 = $this->checkYueXin2($mdz, ($yueDe || $tianDeHe || $tianYuan)) ? 1 : 0;
            $deArray2 = $deArray;
            if ($tianYuan) {
                $deArray2[] = '天愿';
            }
            $yueSha2 = $this->checkYueSha2($mdz, $deArray2) ? 1 : 0;
            $change = [0 => [], 1 => []];//0 普通  1 可变
            if ($this->checkJieSha($mdz, $ddz)) {
                if ($jieSha2) {
                    $change[1][] = '劫煞' . $jieSha2;
                } else {
                    $change[0][] = '劫煞';
                }
            }
            if ($this->checkZaiSha($mdz, $ddz)) {
                $change[$zaiSha2][] = '灾煞';
            }
            // 大时 大败 咸池
            if ($this->checkDaShi($mdz, $ddz)) {
                if ($daShi2) {
                    $change[1][] = '大时' . $daShi2;
                } else {
                    $change[0][] = '大时';
                }
            }
            // 致死（天吏）
            if ($this->checkTianLi($mdz, $ddz)) {
                $change[$tianLi2][] = '天吏';
            }
            if ($this->checkWuMu($mdz, $dgz)) {
                $change[$wuMu2][] = '五墓';
            }
            //            if ($this->checkYueXin($mdz, $ddz)) {
            //                $change[$yueXing2][] = '月刑';
            //            }
            if ($this->checkYueSha($mdz, $ddz)) {
                $change[$yueSha2][] = '月煞';
            }
            if ($this->checkJiuKong($mdz, $ddz)) {
                $jiuKong2 = array_intersect(["月德", "月德合", "天德合"], $deArray) ? 1 : 0;
                $change[$jiuKong2][] = '九空';
            }
            $sanHeDz = [
                [$ydz, $mdz, $ddz],
            ];
            $sanHe = BaziCommon::getSanHeDz2($sanHeDz);
            $sanHeBool = !empty($sanHe[3]);
            if ($this->checkSiHao($mdz, $dgz)) {
                $siHao2 = $this->checkSiHao2($mdz, $deArray, $sanHeBool);
                if ($siHao2) {
                    $change[$siHao2][] = '四耗' . $siHao2;
                } else {
                    $change[0][] = '四耗';
                }
            }
            //            if ($this->checkYueYan($mdz, $ddz)) {
            //                $yueYan2 = $this->checkYueYan2($mdz, $deArray) ? 1 : 0;
            //                $change[$yueYan2][] = '四耗';
            //            }
            $xiongRes = array_merge($xiongRes, $change[0]);
            $reason = array_merge($reason, $xiongRes);
            $bool = $xingChongArr || $reason || $reasonJr;
            $shaXian = Huangli::getSanSha($ddz);
            $chongDz = BaziCommon::getChongDz($ddz);
            $chongSx = $this->lunar->getZodiac($chongDz);
            $jianChu = $huangli->getJianChu();
            $gongsha = $this->huiTouGongShaLiu($jiNian);
            if ($gongsha == $ydzU) {
                $bool = true;
            }
            $gongsha .= $sxBase->getsxByDz($gongsha);
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $curTime),
                    'm' => (int)date('m', $curTime),
                    'd' => (int)date('d', $curTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']),
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'type' => $bool ? 0 : 1, // 0 不合 1 符合
                // 'type_cn' => $bool ? '不宜' : '平',
                'type_hl' => $hdType, // 黄道 1 黑道 0
                'position' => $huangli->getPosition(),
                'jianchu' => $jianChu, // 建除
                'shensha' => $shenSha, // 值日神煞
                // 煞向
                'sha_xian' => str_replace('煞', '', $shaXian[0]),
                // 日冲
                'chong_sx' => $chongSx,
                // 与用户相冲或相刑结果
                'chong_x_u' => $xingChongArr,
                // 原因
                'reason' => $reason,
                'change' => [],
                'gongsha' => $gongsha,
                'fest' => $reasonJr,
            ];
            $num = $result[$keyStr]['num'] ?? 0;
            $dayHour = [];
            $jiShenRes = [];
            if (!$bool) {
                $num++;
                $type = 1;
                if ($this->checkYueEn($mdz . $dtg)) {
                    $jiShenRes[] = '月恩';
                }
                // 驿马 四相 时德 月恩 三合 六合 显星 曲星 传星 岁德 岁德合 五富 天马 天乙贵人 王日 民日 相日 官日 守日 天喜
                if ($this->checkMingRi($mdz, $ddz)) {
                    $jiShenRes[] = '民日';
                }
                if ($this->checkWuFu($dzMd)) {
                    $jiShenRes[] = '五富';
                }
                if (BaziCommon::liuHeDz($mdz . $ddz)) {
                    $jiShenRes[] = '六合';
                }
                if ($this->checkTianMa($mdz, $ddz)) {
                    $jiShenRes[] = '天马';
                }
                if ($sanHeBool) {
                    $jiShenRes[] = '三合';
                }
                $nongliNum = (int)$base['_nongli']['m'];
                if ($this->checkSuiDe($ytg . $dtg)) {
                    $jiShenRes[] = '岁德';
                }
                if ($this->checkSuiDeHe($ytg . $dtg)) {
                    $jiShenRes[] = '岁德合';
                }
                if ($this->checkSiXian($mdz, $dtg)) {
                    $jiShenRes[] = '四相';
                }
                if ($this->checkXiangRi($mdz, $ddz)) {
                    $jiShenRes[] = '相日';
                }
                if ($this->checkChuanXing($nongliNum, $dgz)) {
                    $jiShenRes[] = '传星';
                }
                if ($this->checkXianXing($nongliNum, $dgz)) {
                    $jiShenRes[] = '显星';
                }
                if ($this->checkQuXing($nongliNum, $dgz)) {
                    $jiShenRes[] = '曲星';
                }
                if ($this->checkShiDe($dzMd)) {
                    $jiShenRes[] = '时德';
                }
                foreach ($jiNian as $k => $v) {
                    if (!in_array($k, ['y', 'd'])) {
                        continue;
                    }
                    $tmpTg = $v[0];
                    foreach ($jiNian as $v1) {
                        if ($this->getTianYiGuiRen($tmpTg, $v1[1])) {
                            $jiShenRes[] = '天乙贵人';
                            break 2;
                        }
                    }
                }
                if ($this->checkWangRi($mdz, $ddz)) {
                    $jiShenRes[] = '王日';
                }
                if ($this->checkGuanRi($mdz, $ddz)) {
                    $jiShenRes[] = '官日';
                }
                if ($this->checkShouRi1($mdz, $ddz)) {
                    $jiShenRes[] = '守日';
                }
                if ($change[1] || ($jiShenRes && !$hdType) || $hdType) {
                    $type = 2;
                }

                if ($this->checkTianSe($mdz, $dgz)) {
                    $deArray[] = '天赦';
                }
                if ($tianYuan) {
                    $deArray[] = '天愿';
                }
                if (($hdType && $jiShenRes && empty($change[1])) || ($deArray && !$hdType && empty($change[1]))) {
                    $type = 3;
                }
                if ($deArray && $hdType && empty($change[1])) {
                    $type = 4;
                }
                $jiShenRes = array_merge($deArray, $jiShenRes);
                $dayHour = $listHourUse[$ddz];
                if ($shenSha == '司命') {
                    foreach ($noHourSiMing as $dzNo) {
                        unset($dayHour[$dzNo]);
                    }
                }
                $dayHour = array_values($dayHour);
                $tmpRes['type'] = $type;
                $tmpRes['change'] = $change[1];
                $tmpRes['reason'] = $jiShenRes;
            }
            $tmpRes['shen'] = [
                'jishen' => array_values(array_merge($jiShenRes, $jiShenHl)),
                'xiong' => array_values(array_merge($xiongRes, $xiongHl)),
            ];
            $tmpRes['hour'] = $dayHour;
            $result[$keyStr]['num'] = $num;
            $explain[$shenSha] = $this->getExplain($shenSha);
            foreach ($tmpRes['fest'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['reason'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['change'] as $v) {
                $changeExplain[$v] = $this->getExplainChange($v);
            }
            $result[$keyStr]['info'][] = $tmpRes;
        }

        return [
            'list' => array_values($result),
            'explain' => $explain,
            'change' => $changeExplain,
            'type_cn' => [
                0 => '不宜', 1 => '平', 2 => '小吉', 3 => '中吉', 4 => '大吉',
            ],
        ];
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            "丑" => ["寅", "午", "戌"], "辰" => ["巳", "酉", "丑"], "戌" => ["亥", "卯", "未"], "未" => ["申", "子", "辰"],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $str = $k;
                break;
            }
        }
        return $str;
    }

    /**
     * 过滤黄历算法中的指定吉凶神
     * @param array $arr
     * @param int $type 0吉神 1凶神
     * @return array
     */
    protected function filterJXShen(array $arr, int $type = 0): array
    {
        // 过滤掉黄历的吉凶神
        if ($type) {
            $listNo = ["九空", "月破", "劫煞", "灾煞", "大时", "大败", "咸池", "天吏", "致死", "月煞", "五墓", "岁破"];//凶
        } else {
            // 吉
            $listNo = [
                "月恩", "天愿", "天德", "月德", "天德合", "月德合", "民日", "五富", "母仓",
                "六合", "三合", "岁德", "岁德合", "天乙贵人", "四相", "相日", "传星", "显星", "曲星", "天赦", "时德",
                "天马", "王日", "官日", "守日",
            ];
        }
        if ($listNo) {
            foreach ($arr as $k => $v) {
                if (in_array($v, $listNo)) {
                    unset($arr[$k]);
                }
            }
        }
        return array_values($arr);
    }

    /**
     * 月厌可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool true 可用 false 不可用
     */
    protected function checkYueYan2(string $mdz, array $deArray): bool
    {
        if (in_array($mdz, ["丑", "寅", "未", "申"])) {
            if (array_intersect(['天德', '月德', '月德合'], $deArray)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获得用户能用到的所有时辰
     * @return array
     */
    protected function getHourUse(): array
    {
        $ygz = $this->lunar->getLunarGanzhiYear();
        $ydzU = $ygz[1];
        $list = [
            "子" => ["丑", "辰", "申", "丑", "亥"], "丑" => ["子", "巳", "酉", "子", "亥"], "寅" => ["亥", "午", "戌", "卯", "辰"], "卯" => ["戌", "亥", "未", "辰", "寅"],
            "辰" => ["酉", "子", "申", "寅", "卯"], "巳" => ["申", "酉", "丑", "午", "未"], "午" => ["未", "寅", "戌", "巳", "未"], "未" => ["午", "亥", "卯", "巳", "午"],
            "申" => ["巳", "辰", "子", "酉", "戌"], "酉" => ["辰", "巳", "丑", "戌", "申"], "戌" => ["卯", "午", "寅", "申", "酉"], "亥" => ["寅", "卯", "未", "子", "丑"],
        ];
        $listU = $list[$ydzU];
        $result = [];
        $no = ["子", "丑", "寅"];
        $listHourInfo = $this->getHourInfo();
        foreach ($list as $k => $v) {
            $tmpArr = [];
            $useList = array_unique(array_merge($listU, $v));
            $useList = Utils::sortbyGz($useList);
            foreach ($useList as $k1 => $dz) {
                // 去除夜间
                if (in_array($dz, $no)) {
                    continue;
                }
                // 去除跟用户年支相冲相刑时辰
                if (BaziCommon::getXianChong($ydzU . $dz) || BaziCommon::getXianXin($ydzU . $dz)) {
                    continue;
                }
                //                if (BaziCommon::getXianChong($k . $dz) || BaziCommon::getXianXin($k . $dz)) {
                //                    continue;
                //                }
                $tmpArr[$dz] = $listHourInfo[$dz];
            }
            $result[$k] = $tmpArr;
        }
        return $result;
    }

    /**
     * 获得时辰详细信息
     * @return array
     */
    protected function getHourInfo(): array
    {
        $list = Calendar::DI_ZHI;
        // 时辰对应的时区间
        $listHour = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $sxBase = new SxBase();
        $result = [];
        foreach ($list as $k => $dz) {
            $chongDz = BaziCommon::getChongDz($dz);
            $chongSx = $sxBase->getsxByDz($chongDz);
            $result[$dz] = [
                'dz' => $dz,
                't' => $listHour[$dz],
                'chong' => $chongSx,
            ];
        }
        return $result;
    }

    /**
     * 天乙贵人(以年干+各柱地支 或日干+各柱地支)
     * @param string $tg 天干
     * @param string $dz 地支
     * @return bool
     */
    public function getTianYiGuiRen(string $tg, string $dz): bool
    {
        $list = [
            '甲' => ['丑', '未'], '戊' => ['丑', '未'], '乙' => ['子', '申'], '己' => ['子', '申'], '丙' => ['亥', '酉'],
            '丁' => ['亥', '酉'], '庚' => ['寅', '午'], '辛' => ['寅', '午'], '壬' => ['卯', '巳'], '癸' => ['卯', '巳'],
        ];
        $dzList = $list[$tg];
        return in_array($dz, $dzList);
    }

    /**
     * 获得解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '季分' => '季分是嫁娶的上吉之神，同不将一样，诸事皆宜，凡事皆可用。',
            '十恶大败' => '十恶意思是不赦重罪，大败意思是表示消减，是一种极凶的征兆。它所当值之日大事勿用。',
            '劫煞' => '劫煞是一年的阴气，主管杀害，所值之日忌出行、立券、交易等，易招致灾祸，不宜提车。',
            '灾煞' => '灾煞是指五行阴气的方位，主管灾难、疾病、困厄之事，违犯者易发生病灾或遭遇伤身之祸。',
            '月煞' => '月煞又叫月杀，是指月内的杀神。所值之日忌出行、交易等。',
            '大时' => '大时又名大败，属于败绝之日，五行到这里就没有生机。所值之日忌出行、立券交易等。',
            '天吏' => '天吏又叫致死，是三合的死地，全没有生机的意思。所值之日忌行幸、立券、交易、出货财等。',
            '五墓' => '五墓代表五行的旺气临于墓库。所值之日忌行幸、遣使、立券、交易等。',
            '九空' => '九空是墓库破散的神，是三合库地之冲。所值之日忌出行、出财、交易、立券等。',
            '天德' => '天德是指天的福德，三合的旺气，所值之日宜出行，百事大吉。',
            '天德合' => '天德合是合德之神，合德之神相助，百事大吉。',
            '月德' => '月德是月的德神，寓意吉祥，百事并吉。',
            '月德合' => '月德合是五行的精符相会聚为合。所值之日，所有的好运气将来临，诸事皆宜。',
            '天赦' => '天赦是上天赦免、饶恕罪过的神，有利于消灾化煞，宜出行，诸事不忌。',
            '天愿' => '天愿是月中的善神，五行在这天有极旺的气势，所值之日宜出行、交易、立券。',
            '月恩' => '月恩是阳建所生的天干，子与母相从称之为月恩。所值之日适合出行，百事吉，宜提车。',
            '四相' => '四相是四季王相的时日，此日适宜出行。',
            '时德' => '时德又称四时天德，是四季中的德神。所值之日宜出行，做事吉利。',
            '天乙贵人' => '天乙是天上的神，其神最尊贵。所到的地方，一切凶煞都隐藏避开，诸事皆宜。',
            '三合' => '三合是指不同位而同气，是五行之生、旺、库三支会在一起，宜出行、交易等。',
            '六合' => '六合是日、月与星宿相合的日子。此日宜立券、交易等。',
            '五富' => '五富是富足昌盛之神，是月中的余盛之神。此日宜交易、立券，百事吉。',
            '岁德' => '岁德是岁中的德神，它所管理的地方万福聚集，各种灾祸都自行避开，百事大吉。',
            '岁德合' => '岁德合是岁德五合的天干，是土地一年四季滋生万物的功德，属上吉，有宜没有忌。',
            '显星' => '三皇吉星之一，又称煞贡，有步步高升的寓意，象征富贵兴旺，大发财源。',
            '传星' => '三皇吉星之一，又称人专，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '曲星' => '三皇吉星之一，又称直星，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '民日' => '民日是百姓所用的日子，是指一月之内做事的良辰吉日，宜订立契约、交易等。',
            '官日' => '官日是四季中临官的时日，有王侯之象，是指一月之内做事的良辰吉日，适合提车。',
            '相日' => '相日是四季中官日相生的，有宰相之象，是指一个月内做事的良辰吉日，适合提车。',
            '守日' => '守日是四季胎、绝的时日，是指一个月内做事的良辰吉日，适合提车。',
            '王日' => '王日是四季中正王的时日，有帝王的气象，是一个月内做事的良辰吉日，适合提车。',
            '驿马' => '驿马就是驿骑，有选取行远的意思。此日宜出行，适合提车。',
            '天马' => '天马是天的坐骑。所值之日宜出行，适用于提车。',
            '天喜' => '天喜日是指大喜的日子。所值之日宜提车。',
            '四离' => '四离是阴阳分、至节气之前一辰，忌讳出行，提车最好避开。',
            '四绝' => '四绝是立春、立夏、立秋、立冬的前一日，忌讳出行，提车最好避开。',
            '四废' => '四废是四季衰败凋谢的时日，是五行没有气，福德不到的时日，忌讳出行、交易等，提车最好避开。',
            '月破' => '月破之日是月建对冲的日子，德神失去了降福的威力，忌出行、交易、立券等，提车不宜。',
            '岁破' => '岁破是太岁的所冲之辰，大事勿用，提车最好避开。',
            '受死' => '受死日黄道倾斜，生气弱、晦气横流。日值受死，大事勿用。',
            '往亡' => '往就是去，亡就是没有，往亡有往而不返的意思，所值之日不宜出行、求财等。',
            '清明' => '清明是扫墓祭祖的肃穆节日，若是用来提车有些不合时宜。',
            '七月半' => '七月半又称中元节，在传统文化中属于鬼节，选择该日提车有些不太适合，之后可能会影响整体运势。',
            '重阳' => '重阳节是一个祭祖的节日，有礼敬祖先、慎终追远的礼俗观念，提车最好避开此日。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '杨公忌日' => '杨公忌日又叫杨公十三忌，此日诸事不宜，容易招致灾祸，提车宜规避。   ',
            '丧车煞日' => '丧车煞日和四废日相同，取五行绝处。所值之日容易丢失车辆或遭遇伤身之祸。',
            '青龙' => '青龙黄道日，天乙星，天贵星，用事半吉，如同唐符，国印、福星天乙贵人同到，可作为全吉用，万事顺意。',
            '明堂' => '明堂黄道日，贵人星，明辅星，寓意贵人相助，做事容易成功。',
            '金匮' => '金匮黄道日，福德星，月仙星，忌发兵凶，其余百事吉。',
            '宝光' => '宝光黄道日，宝光星、天德星，当值之日适宜兴动各种事物，行事一帆风顺。',
            '玉堂' => '玉堂黄道日，少微星，天开星，不利泥灶，其余百事皆吉。',
            '司命' => '司命黄道日，凤辇星，日仙星，白天用事大吉，晚上用事不利。',
            '天刑' => '天刑黑道凶日，天刑星，该日利于出师，忌修造、词讼、嫁娶、移徙等事。',
            '朱雀' => '朱雀黑道凶日，天讼星，利用公事，常人凶，谨防争讼。',
            '白虎' => '白虎黑道凶日，天杀星，宜出师畋猎祭祀，忌修造、嫁娶、针刺。',
            '天牢' => '天牢黑道凶日，锁神星，阴人用事皆吉，忌移居、词讼。',
            '玄武' => '玄武黑道凶日，天狱星，君子合天道用事为吉，小人用事凶，需防贼盗，忌词讼、博戏。',
            '勾陈' => '勾陈黑道凶日，地狱星，不可兴土工、营屋舍、移徙、远行、嫁娶、出军。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 凶神在吉的时候解释
     * @param string $str 解释名称
     * @return string
     */
    protected function getExplainChange(string $str): string
    {
        $list = [
            '劫煞1' => '劫煞为绝地，是一年的阴气，主管杀害。所值之日忌出行、立券、交易等。但当月遇到月德或天德合，只忌安抚边境、选将训兵、出师、求医疗病，其余都不忌讳。',
            '劫煞2' => '劫煞为绝地，是一年的阴气，主管杀害。所值之日忌出行、立券、交易等。但当月与天德、月德、天德合、月德合任一吉神并用时，只忌安抚边境、选将训兵、出师、求医疗病，其余都不忌讳。',
            '劫煞3' => '劫煞为绝地，是一年的阴气，主管杀害。所值之日忌出行、立券、交易等。但当月是六合当值，只忌安抚边境、选将训兵、出师、求医疗病，其余都不忌讳。',
            '灾煞' => '灾煞主管灾难、疾病、困厄之事。所值之日忌出行、立券、交易等。但当月与天德、月德、天德合、月德合任一吉神并用时，吉足胜凶，则不忌讳。',
            '月煞' => '月煞又叫月杀，是指月内的杀神。它所值之日忌行幸、立券交易等。但当月遇到天德、月德或天愿任一吉神时，吉足胜凶，则不忌讳。',
            '大时1' => '大时又名大败，属于败绝之日，五行到这里就没有生机。所值当日忌讳出行、遣使、立券交易等。但当月遇到天德或月德时，只忌讳安抚边境、选将训兵、出师，其余都不忌讳。',
            '大时2' => '大时又名大败，属于败绝之日，五行到这里就没有生机。所值当日忌讳出行、遣使、立券交易等。但当月是官日当值，只忌讳安抚边境、选将训兵、出师，其余都不忌讳。',
            '大时3' => '大时又名大败，属于败绝之日，五行到这里就没有生机。所值当日忌讳出行、遣使、立券交易等。但当月是六合当值，只忌讳安抚边境、选将训兵、出师，其余都不忌讳。',
            '天吏' => '天吏又叫致死，是三合的死地，全没有生机的意思。所值之日忌行幸、立券、交易等。但当月与天德、月德、天德合、月德合任一吉神并用时，则不忌讳。',
            '五墓' => '五墓代表五行的旺气临于墓库。所值之日忌行幸、遣使、立券、交易等事宜。但当月遇到月德时，吉足胜凶，则不忌讳。',
            '九空' => '九空是墓库破散的神，是三合库地之冲。所值之日忌出行、出财、交易、立券等。但当月与月德、天德合、月德合任一吉神并用时，则不忌讳。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 九空（协纪辩方）
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkJiuKong(string $mdz, string $ddz): bool
    {
        $list = ['寅辰', '卯丑', '辰戌', '巳未', '午辰', '未丑', '申戌', '酉未', '戌辰', '亥丑', '子戌', '丑未'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月煞可变条件2 true 可用 false 不可用
     * @param string $mdz 月地支
     * @param array $deArray 天德 月德 天德合 月德合数组
     * @return bool
     */
    protected function checkYueSha2(string $mdz, array $deArray): bool
    {
        if (in_array($mdz, ['卯', '酉'])) {
            if (array_intersect(['月德', '天德', '天愿'], $deArray)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 用户和流日的关系
     * @param array $jiNian
     * @return array
     */
    private function getLiuGx(array $jiNian): array
    {
        $listXing = [
            '子卯' => '无礼之刑', '丑戌' => '恃势之刑', '寅巳' => '无恩之刑', '卯子' => '无礼之刑', '辰辰' => '自刑', '巳申' => '无恩之刑',
            '午午' => '自刑', '未丑' => '恃势之刑', '申寅' => '无恩之刑', '酉酉' => '自刑', '戌未' => '恃势之刑', '亥亥' => '自刑',
        ];
        $jiNianU = $this->lunar->getLunarTganDzhi();
        $dtgU = $jiNianU['d'][0];
        $dtgL = $jiNian['d'][0];
        $ydzToYdz = $jiNianU['y'][1] . $jiNian['y'][1];
        $ydzToDdz = $jiNianU['y'][1] . $jiNian['m'][1];
        $result = [
            'chong' => [],
            'xing' => $listXing[$ydzToDdz] ?? '',
            'shen' => BaziExt::getXianShenTg($dtgL, $dtgU) ? ($dtgL . $dtgU) : '',
            'ke' => BaziExt::getXianKeTg($dtgL, $dtgU) ? ($dtgL . $dtgU) : '',
        ];
        if (BaziCommon::getXianChong($ydzToYdz)) {
            $result['chong'][] = $ydzToYdz;
        }
        if (BaziCommon::getXianChong($ydzToDdz)) {
            $result['chong'][] = $ydzToDdz;
        }
        return $result;
    }

    /**
     * 获得年份内所有节气
     * @param $year
     * @return array
     */
    private function getJieqiByYear($year): array
    {
        $arr = $this->jieQiYear;
        if (isset($arr[$year])) {
            return $arr[$year];
        }
        $jArr = SolarTerm::getAllJieQi($year);
        $jieqiArr = [];
        foreach ($jArr as $k => $v) {
            $jTmp = date('Y-m-d', strtotime($v));
            $jieqiArr[$jTmp] = $k;
        }
        $this->jieQiYear[$year] = $jieqiArr;
        return $jieqiArr;
    }
}
