<?php
// +----------------------------------------------------------------------
// | Lingqian.灵签接口
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\model\lingqian\Daisen;
use app\model\lingqian\Guanyin;
use app\model\lingqian\Wealth;
use app\model\lingqian\Yuelao;
use app\validate\ValidateBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Lingqian
{
    /**
     * 灵签接口
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            'type' => input('type', '', 'trim'),
            'num' => input('num', 0, 'intval'),
            'select' => input('select', '', 'trim'),
        ];
        $rules = [
            'type|类型' => ['require', 'in:yuelao,huangdaxian,guanyin,caishen'],
        ];
        // 验证
        switch ($data['type']) {
            case 'yuelao':
                $rules['num|签'] = ['require', 'between:0,100'];
                break;
            case 'guanyin':
                $rules['num|签'] = ['require', 'between:1,100'];
                $rules['select|选项'] = ['require', 'in:cause,marriage,wealth,study'];
                break;
            case 'huangdaxian':
                $rules['num|签'] = ['require', 'between:1,100'];
                $rules['select|选项'] = ['require', 'in:wealth,futrue,health,marriage,thisyear'];
                break;
            case 'caishen':
                $rules['num|签'] = ['require', 'between:1,62'];
                $rules['select|选项'] = ['require', 'in:wealth,study,marriage,health'];
                break;
            default:
        }
        $validate = new ValidateBase();
        $validate->rule($rules);
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        // 数据处理
        $reuslt = [];
        switch ($data['type']) {
            // 月老
            case 'yuelao':
                $yuelao = Yuelao::where('id', $data['num'])->field('content,content2')->find();
                $pos = strpos($yuelao['content2'], '<strong>白话浅释：</strong>');
                if ($pos === false) {
                    $reuslt['content'] = $yuelao['content2'] . '<p style="text-indent:2em;"><strong>白话详解：</strong></p><p style="text-indent:2em;">' . implode('<p style="text-indent:2em;">', $yuelao['content']) . '</p></p>';
                } else {
                    $reuslt['content'] = preg_replace("/<p>　　<strong>白话浅释：<\/strong><\/p>\r\n<p>.*<\/p>/", '<p style="text-indent:2em;"><strong>白话详解：</strong></p><p style="text-indent:2em;">' . implode('<p style="text-indent:2em;">', $yuelao['content']) . '</p></p>', $yuelao['content2']);
                }
                break;
            // 观音
            case 'guanyin':
                $guanyin = Guanyin::where('id', $data['num'])->field('content,notes,' . $data['select'])->find();
                $content = preg_replace('/[\s\S]*?【二十四　下签】<br \/>/', $guanyin['notes'] . '<br />【二十四　下签】<br />', $guanyin['content']);
                $reuslt['content'] = preg_replace("/【解曰】<br \/>[\s\S]*?【古人典故】<br \/>/", '【解曰】<br />' . $guanyin[$data['select']] . '<br />【古人典故】<br />', $content);
                break;
            // 黄大仙
            case 'huangdaxian':
                $huangdaxian = Daisen::where('id', $data['num'])->field('content,' . $data['select'])->find();
                $reuslt['content'] = preg_replace('/<p>【<b>释义<\/b>】<\/p>[\s\S]*?.*/', '<p>【<b>释义</b>】</p><p style="text-indent:2em;">' . $huangdaxian[$data['select']] . '</p>', $huangdaxian['content']);
                break;
            case 'caishen':
                $caishen = Wealth::where('id', $data['num'])->field('content,' . $data['select'])->find();
                $reuslt['content'] = preg_replace('/<p>　　解曰：[\s\S]*?<\/p>$/', '<p><strong>【解曰】</strong></p><p style="text-indent:2em;">' . $caishen[$data['select']] . '</p>', $caishen['content']);
                break;
            default:
        }
        return $reuslt;
    }
}
