<?php
// +----------------------------------------------------------------------
// | Bazihehun 八字合婚
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\plugin\WuXing;
use think\facade\Request;
use think\helper\Str;

class Bazihehun
{
    /**
     * 男用户实例
     * @var Ex
     */
    protected Ex $nan;

    /**
     * 女用户实例
     * @var Ex
     */
    protected Ex $nv;

    /**
     * 订单创建时间
     * @var Ex
     */
    protected Ex $lunarO;

    /**
     * 神煞
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户基础数据
     * @var array
     */
    protected array $userInfo = [];

    /**
     * 订单创建年份
     * @var int
     */
    protected int $yearO = 2025;

    /**
     * 八字合婚
     * @return array
     * @throws Exception
     */
    public function index()
    {
        // 获取用户信息
        $data = [
            // 男出生时间
            'nantime' => Request::param('nantime', '', 'trim'),
            // 女出生时间
            'nvtime' => Request::param('nvtime', '', 'trim'),
            // 订单时间
            'otime' => Request::param('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'nantime|男出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'nvtime|女出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;

        // 订单创建年份
        $this->yearO = date('Y', strtotime($this->orginData['otime']));
        $this->shaShen = new ShaShen();
        // 男
        $this->nan = Ex::date($data['nantime'])->sex(0);
        // 女
        $this->nv = Ex::date($data['nvtime'])->sex(1);
        // 订单创建时间
        $this->lunarO = Ex::date($data['otime']);
        // 用户基础信息
        $baseNan = $this->getBasePan(0);
        $baseNv = $this->getBasePan(1);
        $this->userInfo = [
            // 男
            $baseNan,
            // 女
            $baseNv,
        ];
        // 不展示输出
        unset($baseNan['god_tzs'], $baseNan['god_ths'], $baseNan['_god'], $baseNan['gx']);
        unset($baseNv['god_tzs'], $baseNv['god_ths'], $baseNv['_god'], $baseNv['gx']);

        // 优缺点
        $youQueNan = $this->getBaziYouQue(0);
        $youQueNv = $this->getBaziYouQue(1);

        // 八字点评
        $dianPin = [
            'nan' => [
                'mingju' => $this->getMingJuTeDian(0),
                'you' => $youQueNan['you'],
                'que' => $youQueNan['que'],
            ],
            'nv' => [
                'mingju' => $this->getMingJuTeDian(1),
                'you' => $youQueNv['you'],
                'que' => $youQueNv['que'],
            ],
        ];

        // 理想伴侣
        $banLv = $this->getLiXianBanlv();

        // 恋爱分析
        $lianAiSh = [
            'nan' => [
                'xingge' => $this->getLianAiXingGe(0),
                'banlv' => $banLv['nan'],
            ],
            'nv' => [
                'xingge' => $this->getLianAiXingGe(1),
                'banlv' => $banLv['nv'],
            ],
        ];

        // 分数
        $fenArr = [
            // 年柱合分
            'year' => $this->getFenByYear(),
            // 日柱合分
            'day' => $this->getFenByDay(),
            // 属相分数
            'shuxian' => $this->getShuXiangFen(),
            // 纳音分数
            'nayin' => $this->getNaYinFen(),
            // 神煞合婚
            'shensha' => $this->getShenShaFen(),
            // 六亲合婚
            'liuqing' => $this->liuQingFen(),
            // 喜用神分
            'xyfen' => $this->getXiYongFen(),
        ];

        // 八字神煞
        $shensha = [
            'nan' => $this->shaShen->detail($this->userInfo[0]['base']['jinian'], 0),
            'nv' => $this->shaShen->detail($this->userInfo[1]['base']['jinian'], 1),
        ];

        return [
            // 基本盘
            'pan' => [
                'nan' => $baseNan,
                'nv' => $baseNv,
            ],
            // 订单时间
            'otime' => [
                // 八字信息
                'base' => $this->lunarO->getLunarByBetween(),
            ],
            // 八字点评
            'dianpi' => $dianPin,
            // 恋爱分析
            'lianai' => $lianAiSh,
            // 旺夫旺妻
            'wangyun' => [
                'nan' => $this->getWangFuQi(0),
                'nv' => $this->getWangFuQi(1),
            ],
            // 婚后生活
            'shenghuo' => [
                // 甜蜜度
                'tianmi' => $this->getHunYinTianMi(),
                // 稳定度
                'wending' => $this->getHunHuoWenDing(),
            ],
            // 婚前婚后财富
            'caifu' => [
                'nan' => $this->getCaiFuBySex(0),
                'nv' => $this->getCaiFuBySex(1),
            ],
            // 感情运
            'gangqing' => [
                'nan' => $this->getGanQing(0),
                'nv' => $this->getGanQing(1),
            ],
            // 神煞
            'shensha' => $shensha,
            // 五行参数
            'wxcs' => $this->wxcs(),
            // 相互关系
            'xhgx' => $this->xhgx(),
            // 天干关系
            'tggx' => $this->tggx($shensha),
            // 分数
            'fen' => $fenArr,
            // 合婚分数
            'fen_he' => $this->getHeHunFen($fenArr),
            // 潜在风险
            'qianzai' => $this->getQianZai(),
        ];
    }

    /**
     * 用户基本盘信息
     * @param int $sex
     * @return array
     * @throws Exception
     */
    protected function getBasePan(int $sex): array
    {
        $lunar = $sex ? $this->nv : $this->nan;
        $base = $lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        // 几岁
        $base['age'] = $this->yearO - (int)$lunar->dateTime->format('Y') + 1;
        // 原局地支桃花数（子午卯酉）
        $base['dzths'] = 0;
        foreach ($base['jinian'] as $k => $v) {
            if (in_array($v[1], ['子', '午', '卯', '酉'])) {
                $base['dzths'] += 1;
            }
        }
        // 命宫
        $base['minggong'] = $this->getMinggong($lunar);
        $godHide = $lunar->_getGod();
        $godT = $lunar->getGod();
        $godZ = [
            'year' => $godHide['year']['god'][0],
            'month' => $godHide['month']['god'][0],
            'day' => $godHide['day']['god'][0],
            'hour' => $godHide['hour']['god'][0],
        ];
        $dayun = $lunar->getDayun();
        $i = BaziExt::getKeyWithArray($this->yearO, $dayun['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $dayun['curr'] = $i;
        $fenXi = $lunar->getWxFenxi();
        $godTz = array_merge(array_values($godT), array_values($godZ));
        $godTzS = $this->getGodSum($godTz);
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $godTH = array_merge(array_values($godT), $godH2);
        $godTHS = $this->getGodSum($godTH);
        $baziEx = new BaziEx($lunar);
        return [
            'base' => $base,
            'god' => $godT,
            '_god' => $godHide,
            'godz' => $godZ,
            // 十神统计
            'god_tzs' => $godTzS,
            'god_ths' => $godTHS,
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
            // 纪年五行的相关数据 不含藏干
            'jnwx' => $this->getJnWx($jiNian),
            // 旺弱
            'wr' => BaziExt::getWr($jiNian['d'][0], $jiNian['d'][1]),
            // 干支关系
            'gx' => $this->getJnGx($jiNian),
            // 旺度
            'wangdu' => $baziEx->getWangDu(),
            // 获得空亡
            'kw' => Huangli::getKongWangbyGz($jiNian['d'][0] . $jiNian['d'][1]),
            'kwy' => Huangli::getKongWangbyGz($jiNian['y'][0] . $jiNian['y'][1]),
            'xy' => [
                'xy' => $fenXi['xy'],
                'shen' => $fenXi['shen'],
            ],
            // 大运
            'dayun' => $dayun,
        ];
    }

    /**
     * 根据干支获得命宫
     * @param Ex $lunar
     * @return array
     */
    protected function getMinggong(Ex $lunar): array
    {
        $jiNian = $lunar->getLunarTganDzhi();
        $gz = BaziExt::getMingGong($jiNian);
        $dtg = $jiNian['d'][0];
        return [
            // 干支
            'gz' => $gz,
            // 天干十神
            'god' => BaziCommon::getGodName($dtg . $gz[0]),
            // 地支主气十神
            'godz' => BaziCommon::getGodName($dtg . $gz[1]),
            // 神煞
            'shensha' => $this->shaShen->liuNianToBazi($gz, $jiNian),
        ];
    }

    /**
     * 五行参数
     * @return array
     */
    protected function wxcs()
    {
        // 组合十神数据表，五行十神
        $zhssb = [
            '金' => ['金' => '比劫', '木' => '才财', '水' => '食伤', '火' => '官杀', '土' => '印枭',],
            '木' => ['金' => '官杀', '木' => '比劫', '水' => '印枭', '火' => '食伤', '土' => '才财',],
            '水' => ['金' => '印枭', '木' => '食伤', '水' => '比劫', '火' => '才财', '土' => '官杀',],
            '火' => ['金' => '才财', '木' => '印枭', '水' => '官杀', '火' => '比劫', '土' => '食伤',],
            '土' => ['金' => '食伤', '木' => '官杀', '水' => '才财', '火' => '印枭', '土' => '比劫',],
        ];
        // 纪年五行
        $jnWxdNan = $this->userInfo[0]['jnwx']['wx']['d'][0];
        $jnWxdNv = $this->userInfo[1]['jnwx']['wx']['d'][0];

        // 取得组合十神
        $zhssListNan = $zhssb[$jnWxdNan];
        $zhssListNv = $zhssb[$jnWxdNv];

        // 取得五行数值
        $wuxingNumNan = $this->nan->getWuxingNum();
        $wuxingNumNv = $this->nv->getWuxingNum();

        // 取得最大和最小五行
        $maxNan = array_keys($wuxingNumNan, max($wuxingNumNan))[0];
        $minNan = array_keys($wuxingNumNan, min($wuxingNumNan))[0];
        $maxNv = array_keys($wuxingNumNv, max($wuxingNumNv))[0];
        $minNv = array_keys($wuxingNumNv, min($wuxingNumNv))[0];

        return [
            'nan' => [
                'min' => $minNan,
                'min_ss' => $zhssListNan[$minNan],
                'max' => $maxNan,
                'max_ss' => $zhssListNan[$maxNan],
            ],
            'nv' => [
                'min' => $maxNv,
                'min_ss' => $zhssListNv[$minNv],
                'max' => $maxNv,
                'max_ss' => $zhssListNv[$maxNv],
            ],
        ];
    }

    /**
     * 相互关系
     * @return array
     */
    protected function xhgx()
    {
        // 纪年
        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];

        // 男
        $ytgNan = $jiNianNan['y'][0];
        $ydzNan = $jiNianNan['y'][1];
        $mtgNan = $jiNianNan['m'][0];
        $mdzNan = $jiNianNan['m'][1];
        $dtgNan = $jiNianNan['d'][0];
        $ddzNan = $jiNianNan['d'][1];
        $htgNan = $jiNianNan['h'][0];
        $hdzNan = $jiNianNan['h'][1];

        // 女
        $ytgNv = $jiNianNv['y'][0];
        $ydzNv = $jiNianNv['y'][1];
        $mtgNv = $jiNianNv['m'][0];
        $mdzNv = $jiNianNv['m'][1];
        $dtgNv = $jiNianNv['d'][0];
        $ddzNv = $jiNianNv['d'][1];
        $htgNv = $jiNianNv['h'][0];
        $hdzNv = $jiNianNv['h'][1];

        $result = [];

        // 女
        foreach ($jiNianNv as $k => $v) {
            if (
                // 根据男的年干找到结果，看看女的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($ytgNan, $v[1]) ||
                // 根据男的日干找到结果，看看女的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($dtgNan, $v[1])
            ) {
                $result['nv'][$v[1] . '天乙贵人'] = [$v[1], '天乙贵人', '寓意：催旺贵人运，遇事有人帮。'];
            }
            if ($this->shaShen->getTianChu($ytgNan . $v[1]) || $this->shaShen->getTianChu($htgNan . $v[1])) {
                $result['nv'][$v[1] . '天厨贵人'] = [$v[1], '天厨贵人', '寓意：催旺口福，主提升家庭生活品质。'];
            }
            if ($k !== 'y' && $this->shaShen->getZaiSha($ydzNan . $v[1])) {
                $result['nv'][$v[1] . '灾煞'] = [$v[1], '灾煞', '寓意：出现障碍，不让强出头。'];
            }
            if ($this->shaShen->getTianDe($mdzNan . $v[0])) {
                $result['nv'][$v[0] . '天德贵人'] = [$v[0], '天德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDe($mdzNan . $v[0])) {
                $result['nv'][$v[0] . '月德贵人'] = [$v[0], '月德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getTianDeHe($mdzNan . $v[0])) {
                $result['nv'][$v[0] . '天德合'] = [$v[0], '天德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDeHe($mdzNan . $v[0])) {
                $result['nv'][$v[0] . '月德合'] = [$v[0], '月德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYangRen($dtgNan . $v[1])) {
                $result['nv'][$v[1] . '羊刃'] = [$v[1], '羊刃', '寓意：情绪冲动，脾气大倔强。'];
            }
            if ($this->shaShen->getXianChi($ydzNan . $v[1])) {
                $result['nv'][$v[1] . '桃花'] = [$v[1], '桃花', '寓意：催旺桃花运，主异性缘旺。'];
            }
        }
        $result['nv'] = array_values($result['nv'] ?? []);

        // 男
        foreach ($jiNianNan as $k => $v) {
            if (
                // 根据女的年干找到结果，看看男的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($ytgNv, $v[1]) ||
                // 根据女的日干找到结果，看看男的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($dtgNv, $v[1])
            ) {
                $result['nan'][$v[1] . '天乙贵人'] = [$v[1], '天乙贵人', '寓意：催旺贵人运，遇事有人帮。'];
            }
            if ($this->shaShen->getTianChu($ytgNv . $v[1]) || $this->shaShen->getTianChu($htgNv . $v[1])) {
                $result['nan'][$v[1] . '天厨贵人'] = [$v[1], '天厨贵人', '寓意：催旺口福，主提升家庭生活品质。'];
            }
            if ($k !== 'y' && $this->shaShen->getZaiSha($ydzNv . $v[1])) {
                $result['nan'][$v[1] . '灾煞'] = [$v[1], '灾煞', '寓意：出现障碍，不让强出头。'];
            }
            if ($this->shaShen->getTianDe($mdzNv . $v[0])) {
                $result['nan'][$v[0] . '天德贵人'] = [$v[0], '天德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDe($mdzNv . $v[0])) {
                $result['nan'][$v[0] . '月德贵人'] = [$v[0], '月德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getTianDeHe($mdzNv . $v[0])) {
                $result['nan'][$v[0] . '天德合'] = [$v[0], '天德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDeHe($mdzNv . $v[0])) {
                $result['nan'][$v[0] . '月德合'] = [$v[0], '月德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYangRen($dtgNv . $v[1])) {
                $result['nan'][$v[1] . '羊刃'] = [$v[1], '羊刃', '寓意：情绪冲动，脾气大倔强。'];
            }
            if ($this->shaShen->getXianChi($ydzNv . $v[1])) {
                $result['nan'][$v[1] . '桃花'] = [$v[1], '桃花', '寓意：催旺桃花运，主异性缘旺。'];
            }
        }
        $result['nan'] = array_values($result['nan'] ?? []);
        return $result;
    }

    /**
     * 天干关系
     * @param array $shensha 神煞
     * @return array
     */
    protected function tggx(array $shensha)
    {
        // 纪年
        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];

        // 天干关系表
        $tggxb = [
            '甲' => [
                '相同' => ['甲'],
                '相生' => ['丙', '丁'],
                '相克' => ['戊', '己'],
                '相冲' => ['庚'],
                '五合' => ['己'],
            ],
            '乙' => [
                '相同' => ['乙'],
                '相生' => ['丙', '丁'],
                '相克' => ['戊', '己'],
                '相冲' => ['辛'],
                '五合' => ['庚'],
            ],
            '丙' => [
                '相同' => ['丙'],
                '相生' => ['戊', '己'],
                '相克' => ['庚', '辛'],
                '相冲' => ['壬'],
                '五合' => ['辛'],
            ],
            '丁' => [
                '相同' => ['丁'],
                '相生' => ['戊', '己'],
                '相克' => ['庚', '辛'],
                '相冲' => ['癸'],
                '五合' => ['壬'],
            ],
            '戊' => [
                '相同' => ['戊'],
                '相生' => ['庚', '辛'],
                '相克' => ['壬', '癸'],
                '相冲' => ['无'],
                '五合' => ['癸'],
            ],
            '己' => [
                '相同' => ['己'],
                '相生' => ['庚', '辛'],
                '相克' => ['壬', '癸'],
                '相冲' => ['无'],
                '五合' => ['甲'],
            ],
            '庚' => [
                '相同' => ['庚'],
                '相生' => ['壬', '癸'],
                '相克' => ['甲', '乙'],
                '相冲' => ['甲'],
                '五合' => ['乙'],
            ],
            '辛' => [
                '相同' => ['辛'],
                '相生' => ['壬', '癸'],
                '相克' => ['甲', '乙'],
                '相冲' => ['乙'],
                '五合' => ['丙'],
            ],
            '壬' => [
                '相同' => ['壬'],
                '相生' => ['甲', '乙'],
                '相克' => ['丙', '丁'],
                '相冲' => ['丙'],
                '五合' => ['丁'],
            ],
            '癸' => [
                '相同' => ['癸'],
                '相生' => ['甲', '乙'],
                '相克' => ['丙', '丁'],
                '相冲' => ['丁'],
                '五合' => ['戊'],
            ],
        ];
        // 地支关系
        $dzgxb = [
            '子' => [
                '相同' => ['子'],
                '相破' => ['酉'],
                '相冲' => ['午'],
                '相刑' => ['卯'],
                '相害' => ['未'],
                '三合' => ['辰', '申'],
                '三会' => ['丑', '亥'],
                '六合' => ['丑'],
                '暗合' => ['巳'],
            ],
            '丑' => [
                '相同' => ['丑'],
                '相破' => ['辰'],
                '相冲' => ['未'],
                '相刑' => ['戌'],
                '相害' => ['午'],
                '三合' => ['巳', '酉'],
                '三会' => ['子', '亥'],
                '六合' => ['子'],
                '暗合' => ['寅'],
            ],
            '寅' => [
                '相同' => ['寅'],
                '相破' => ['亥'],
                '相冲' => ['申'],
                '相刑' => ['巳'],
                '相害' => ['巳'],
                '三合' => ['午', '戌'],
                '三会' => ['卯', '辰'],
                '六合' => ['亥'],
                '暗合' => ['丑', '午'],
            ],
            '卯' => [
                '相同' => ['卯'],
                '相破' => ['午'],
                '相冲' => ['酉'],
                '相刑' => ['子'],
                '相害' => ['辰'],
                '三合' => ['亥', '未'],
                '三会' => ['辰', '寅'],
                '六合' => ['戌'],
                '暗合' => ['申'],
            ],
            '辰' => [
                '相同' => ['辰'],
                '相破' => ['丑'],
                '相冲' => ['戌'],
                '相刑' => ['辰'],
                '相害' => ['卯'],
                '三合' => ['子', '申'],
                '三会' => ['寅', '卯'],
                '六合' => ['酉'],
            ],
            '巳' => [
                '相同' => ['巳'],
                '相破' => ['申'],
                '相冲' => ['亥'],
                '相刑' => ['申'],
                '相害' => ['寅'],
                '三合' => ['酉', '丑'],
                '三会' => ['午', '未'],
                '六合' => ['申'],
                '暗合' => ['酉', '子'],
            ],
            '午' => [
                '相同' => ['午'],
                '相破' => ['卯'],
                '相冲' => ['子'],
                '相刑' => ['午'],
                '相害' => ['丑'],
                '三合' => ['寅', '戌'],
                '三会' => ['巳', '未'],
                '六合' => ['未'],
                '暗合' => ['寅', '亥'],
            ],
            '未' => [
                '相同' => ['未'],
                '相破' => ['戌'],
                '相冲' => ['丑'],
                '相刑' => ['丑'],
                '相害' => ['子'],
                '三合' => ['亥', '卯'],
                '三会' => ['巳', '午'],
                '六合' => ['午'],
            ],
            '申' => [
                '相同' => ['申'],
                '相破' => ['巳'],
                '相冲' => ['寅'],
                '相刑' => ['寅'],
                '相害' => ['亥'],
                '三合' => ['辰', '子'],
                '三会' => ['酉', '戌'],
                '六合' => ['巳'],
                '暗合' => ['卯'],
            ],
            '酉' => [
                '相同' => ['酉'],
                '相破' => ['子'],
                '相冲' => ['卯'],
                '相刑' => ['酉'],
                '相害' => ['戌'],
                '三合' => ['巳', '丑'],
                '三会' => ['戌', '申'],
                '六合' => ['辰'],
                '暗合' => ['巳'],
            ],
            '戌' => [
                '相同' => ['戌'],
                '相破' => ['未'],
                '相冲' => ['辰'],
                '相刑' => ['未'],
                '相害' => ['酉'],
                '三合' => ['午', '寅'],
                '三会' => ['申', '酉'],
                '六合' => ['卯'],
            ],
            '亥' => [
                '相同' => ['亥'],
                '相破' => ['寅'],
                '相冲' => ['巳'],
                '相刑' => ['亥'],
                '相害' => ['申'],
                '三合' => ['卯', '未'],
                '三会' => ['子', '丑'],
                '六合' => ['寅'],
                '暗合' => ['午'],
            ],
        ];
        // 五行相克表
        $wxxkb = [
            '金木', '木土', '水火', '火金', '土水',
            '木金', '水土', '金火', '火水', '土木',
        ];
        // 五行相生表
        $wxxsb = [
            '金水', '水金', '木火', '火木', '水木',
            '木水', '火土', '土火', '土金', '金土',
        ];
        // 四柱关系概要解释表
        $szgxgyjsb = [
            '相刑' => '可能导致双方性格上的冲突和不合，影响彼此间的相处和沟通。',
            '相生' => '有助于增进双方的理解和包容，对关系有积极的影响。',
            '相冲' => '可能带来性格上的对立和冲突，需要双方注意调节和沟通。',
            '相害' => '可能带来一定的负面影响，如健康、财运等方面的不顺，也可能影响双方的感情和信任。',
            '相合' => '表示双方性格、观念等方面有相似之处，有助于增进感情和默契。',
            '相克' => '可能带来一些挑战和困难，需要双方共同努力去克服。',
            '三合、三会' => '通常被视为吉利的象征，表示双方在某些方面有共同点和互补性，有利于关系的和谐与发展。',
            '五合' => '在天干中较为常见，但年干五合的具体影响可能不如其他地支关系显著，一般也象征和谐与互助。',
            '暗合' => '年干暗合可能意味着双方在某种层面上有默契或共同的目标，但不易被外界察觉，对双方关系有潜在的正面影响。',
        ];
        // 四柱天干地支详情表
        $sztgdzxqb = [
            'ytg' => [
                '含义' => '年干代表着个体的根本性格特征、早年环境以及家族遗传等因素。它在八字命局中占据核心地位，对整体格局有着不可忽视的影响。',
                '影响' => '年干若为吉神，可能预示着命主早年生活顺遂，性格平和；若为凶神，则可能意味着早年经历波折，性格复杂多变，但也可能因此激发出非凡的斗志和成就。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突。',
                        '运势影响' => '在运势上可能相互冲突，影响双方的生活和事业。',
                        '婚姻关系' => '需要双方高度理解和包容，否则婚姻可能面临挑战。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互学习和促进成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的思维方式和行为习惯，易于产生共鸣。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退。',
                        '婚姻关系' => '彼此间有较强的默契和认同感，有助于婚姻的稳定，但也可能因过于相似而缺乏新鲜感。',
                    ],
                ],
            ],
            'ydz' => [
                '含义' => '年支则与个体的根基、家族背景以及早年成长环境紧密相关。它反映了命主在社会中的定位感和归属感。',
                '影响' => '年支的稳定与否对命主的内心安全感和外在表现有着直接的影响。有利的年支组合可能带来事业和家庭的顺遂，而不利的组合则可能带来挑战和变故。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方性格可能截然相反，容易产生冲突。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定。',
                    ],
                    '相破' => [
                        '性格影响' => '双方性格可能存在不和谐因素，容易产生矛盾。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能带有相似的家族烙印和兴趣爱好。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇。',
                        '婚姻关系' => '彼此间有较强的认同感和归属感，但也可能因过于相似而缺乏变化。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方性格可能存在不利影响，容易产生伤害和矛盾。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业。',
                        '婚姻关系' => '需要双方付出更多努力来克服障碍，维护婚姻关系。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方性格可能和谐互补，易于相处和协作。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
            'mtg' => [
                '含义' => '月干代表着个体的情感表达、内在性格以及对外界环境的适应能力。它反映了命主在特定年龄段（通常是青少年到成年初期）的生活状态和心理特征。',
                '影响' => '月干若为吉神，可能预示着命主在此阶段情感丰富、性格开朗，易于与他人建立良好关系；若为凶神，则可能意味着命主在此阶段经历情感波折，性格较为内敛或复杂，但也可能因此培养出独特的魅力和深度。同时，月干还影响着命主的社交能力和人际关系。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成默契的情感关系。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互理解和包容，促进彼此的成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和默契，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感表达方式和内在性格，易于产生共鸣和情感上的默契。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退，相互支持。',
                        '婚姻关系' => '彼此间有较强的情感纽带和认同感，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'mdz' => [
                '含义' => '月支则与个体的家庭背景、成长环境以及情感需求紧密相关。它反映了命主在情感层面的归属感和安全感。',
                '影响' => '月支的稳定与否对命主的情感表达和内心安全感有着直接的影响。有利的月支组合可能带来温馨的家庭氛围和和谐的情感关系，而不利的组合则可能带来情感上的挑战和内心的不安。同时，月支还与命主的运势和事业发展有着密切的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方情感表达方式可能截然相反，容易产生冲突和误解。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感需求可能存在不和谐因素，容易产生矛盾和冲突。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展和生活质量。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感需求和家庭背景，易于产生共鸣和情感上的联系。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇，能够共同把握机会，共同发展。',
                        '婚姻关系' => '彼此间有较强的情感纽带和归属感，有助于婚姻的稳定和和谐。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业发展。',
                        '婚姻关系' => '需要双方付出更多努力来克服障碍，维护婚姻关系的和谐与稳定。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方情感关系可能和谐互补，易于相处和协作，形成默契的情感纽带。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
            'dtg' => [
                '含义' => '日干代表着个体的核心性格、自我表达能力以及对外界环境的直接反应。它如同一面镜子，映照出命主最本质的一面。',
                '影响' => '日干的属性若为吉，可能预示着命主性格开朗、积极向上，善于表达自我；若为凶，则可能意味着命主性格内敛或复杂，但也可能因此孕育出独特的魅力和深度。同时，日干还与命主的运势和事业发展息息相关，影响着其一生的走向。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成深厚的情感纽带。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐，否则可能面临分离的风险。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解来调和。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免因性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互欣赏和学习，促进彼此的成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的核心性格和自我表达方式，易于产生共鸣和理解。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够携手共进。',
                        '婚姻关系' => '彼此间有较强的性格契合度和默契感，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'ddz' => [
                '含义' => '日支则与个体的内心世界、情感需求以及家庭背景紧密相连。它如同一扇窗，透露出命主情感层面的真实面貌。',
                '影响' => '日支的稳定与否直接影响着命主的情感表达和内心安全感。有利的日支组合可能带来和谐的家庭氛围和稳定的情感关系，而不利的组合则可能引发情感上的波折和内心的不安。同时，日支还与命主的社交能力和人际关系有着千丝万缕的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方情感表达方式可能截然相反，容易产生冲突和误解，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整策略。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定和和谐，加强沟通和交流。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感需求可能存在不和谐因素，容易产生矛盾和冲突，需要更多的沟通和理解来调和。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展和生活质量，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐，否则可能面临分离的风险。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感需求和家庭背景，易于产生共鸣和情感上的联系，增进彼此的理解。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇，能够共同把握机会，实现共同发展。',
                        '婚姻关系' => '彼此间有较强的情感纽带和归属感，有助于婚姻的稳定和和谐，增强家庭的凝聚力。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解来化解冲突。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业发展，需要共同努力来克服障碍。',
                        '婚姻关系' => '需要双方付出更多努力来维护婚姻关系的和谐与稳定，增强信任和包容度。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方情感关系可能和谐互补，易于相处和协作，形成默契的情感纽带，增进彼此的理解和信任。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来，提升彼此的生活质量和发展空间。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满，增强家庭的幸福感和归属感。',
                    ],
                ],
            ],
            'htg' => [
                '含义' => '时干代表着个体的行动力、决策能力以及对外界环境的应变能力。它如同一面旗帜，引领着命主在生活中的前进方向。',
                '影响' => '时干的属性若为吉，可能预示着命主行动果断、决策力强，善于把握时机；若为凶，则可能意味着命主行事犹豫或冲动，但也可能因此锻炼出独特的判断力和行动力。同时，时干还与命主的晚年运势和子女状况有着紧密的联系。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成默契的行动力和决策方式。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协来维系关系中的行动力和决策共识。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整策略。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解来调和行动力和决策方式。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免因性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互激励和促进，共同提升行动力和决策能力。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同创造美好的未来。',
                        '婚姻关系' => '关系中充满活力和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的行动力和决策方式，易于在行动上产生共鸣和协作。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退，相互支持。',
                        '婚姻关系' => '彼此间有较强的行动默契和决策共识，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'hdz' => [
                '含义' => '时支则与个体的内心世界、情感归宿以及晚年生活紧密相连。它如同一盏明灯，照亮了命主晚年生活的道路。',
                '影响' => '时支的稳定与否直接影响着命主的情感寄托和晚年生活的幸福感。有利的时支组合可能带来温馨的晚年生活和和谐的情感关系，而不利的组合则可能引发情感上的孤独和晚年生活的不安。同时，时支还与命主的社交圈层和人际关系有着密切的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方晚年生活的期望可能截然相反，容易产生冲突和误解，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在晚年运势上可能相互冲突，给双方带来挑战和变化。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感归宿可能存在不和谐因素，容易产生矛盾和冲突，需要更多的沟通和理解来调和晚年生活的期望。',
                        '运势影响' => '在晚年运势上可能相互破坏，影响双方的生活质量和幸福感。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感归宿和晚年生活期望，易于产生共鸣和情感上的联系。',
                        '运势影响' => '在晚年运势上可能面临相似的社会环境和机遇，能够共同把握机会，享受美好的晚年生活。',
                        '婚姻关系' => '彼此间有较强的情感纽带和晚年生活的共同期望，有助于婚姻的稳定和和谐。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解来化解晚年生活的冲突。',
                        '运势影响' => '在晚年运势上可能相互阻碍，影响双方的生活质量和幸福感。',
                        '婚姻关系' => '需要双方付出更多努力来维护婚姻关系的和谐与稳定。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方晚年生活的期望可能和谐互补，易于相处和协作，形成默契的情感纽带。',
                        '运势影响' => '在晚年运势上可能形成强强联合，共同创造美好的晚年生活。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
        ];

        // 男女天干地支关系表
        $nvtgdzgxb = [];
        $nvtgdzgxbNan = [];
        $nvtgdzgxbNv = [];
        // 需要加 b 标签
        $tintBArr = ['三合', '三会', '五合', '六合', '暗合'];
        // 四柱关系
        $szgxArr = [];
        // 以男的走一遍
        foreach ($jiNianNan as $k => $v) {
            // 天干关系
            $tggxNan = $tggxb[$jiNianNan[$k][0]];
            // 地支关系
            $dzgxNan = $dzgxb[$jiNianNan[$k][1]];

            foreach ($tggxNan as $name => $var) {
                if (in_array($jiNianNv[$k][0], (array)$var)) {
                    $nvtgdzgxbNan[$k]['tg'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbNan[$k]['tg'])) {
                $nvtgdzgxbNan[$k]['tg'][] = '无';
            }
            foreach ($dzgxNan as $name => $var) {
                if (in_array($jiNianNv[$k][1], (array)$var)) {
                    $nvtgdzgxbNan[$k]['dz'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbNan[$k]['dz'])) {
                $nvtgdzgxbNan[$k]['dz'][] = '无';
            }
        }
        // 以女的走一遍
        foreach ($jiNianNv as $k => $v) {
            // 天干关系
            $tggxNv = $tggxb[$jiNianNv[$k][0]];
            // 地支关系
            $dzgxNv = $dzgxb[$jiNianNv[$k][1]];

            foreach ($tggxNv as $name => $var) {
                if (in_array($jiNianNan[$k][0], (array)$var)) {
                    $nvtgdzgxbNv[$k]['tg'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbNv[$k]['tg'])) {
                $nvtgdzgxbNv[$k]['tg'][] = '无';
            }
            foreach ($dzgxNv as $name => $var) {
                if (in_array($jiNianNan[$k][1], (array)$var)) {
                    $nvtgdzgxbNv[$k]['dz'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbNv[$k]['dz'])) {
                $nvtgdzgxbNv[$k]['dz'][] = '无';
            }
        }
        // 结果合并，如果男的其中一个为“无”，以女的填充
        $nvtgdzgxb = $nvtgdzgxbNan;
        foreach ($nvtgdzgxbNan as $k => $list) {
            foreach ($list as $na => $nameList) {
                foreach ($nameList as $k2 => $var) {
                    if ($var == '无') {
                        $nvtgdzgxb[$k][$na][$k2] = $nvtgdzgxbNv[$k][$na][$k2];
                    }
                }
            }
        }

        // 年柱、日柱纳音五行
        $nayinYnan = preg_split('/(?<!^)(?!$)/u', $this->userInfo[0]['na_yin']['year']);
        $nayinDnan = preg_split('/(?<!^)(?!$)/u', $this->userInfo[0]['na_yin']['day']);
        $nayinYnv = preg_split('/(?<!^)(?!$)/u', $this->userInfo[1]['na_yin']['year']);
        $nayinDnv = preg_split('/(?<!^)(?!$)/u', $this->userInfo[1]['na_yin']['day']);
        $ynNyWx = [
            'nan' => [
                'y' => end($nayinYnan),
                'd' => end($nayinDnan),
            ],
            'nv' => [
                'y' => end($nayinYnv),
                'd' => end($nayinDnv),
            ],
        ];

        // 五行相生相克
        $wxxsxk = [
            'y' => '无',
            'd' => '无',
        ];
        $nayinYearNan = $this->userInfo[0]['na_yin']['year'];
        $nayinYearNv = $this->userInfo[1]['na_yin']['year'];
        $nayinDayNan = $this->userInfo[0]['na_yin']['day'];
        $nayinDayNv = $this->userInfo[1]['na_yin']['day'];
        // 男女纳音年相同
        if ($nayinYearNan === $nayinYearNv) {
            $wxxsxk['y'] = '相同';
        } else {
            // 检查是否存在相克或相生关系
            $pair = $ynNyWx['nan']['y'] . $ynNyWx['nv']['y'];
            if (in_array($pair, $wxxkb)) {
                $wxxsxk['y'] = '相克';
            } elseif (in_array($pair, $wxxsb)) {
                $wxxsxk['y'] = '相生';
            }
        }
        // 男女纳音月相同
        if ($nayinDayNan === $nayinDayNv) {
            $wxxsxk['d'] = '相同';
        } else {
            // 检查是否存在相克或相生关系
            $pair = $ynNyWx['nan']['d'] . $ynNyWx['nv']['d'];
            if (in_array($pair, $wxxkb)) {
                $wxxsxk['d'] = '相克';
            } elseif (in_array($pair, $wxxsb)) {
                $wxxsxk['d'] = '相生';
            }
        }

        // 夫妻宫加持吉神
        $fqgjcjs = [];
        $shenshaAll = array_merge($shensha['nan']['d'], $shensha['nv']['d']);
        foreach ($shenshaAll as $name) {
            if (in_array($name, ['天乙贵人', '天德贵人', '月德贵人', '天德合', '月德合'])) {
                $fqgjcjs[$name] = $name;
            }
        }
        $fqgjcjs = array_values($fqgjcjs);

        // 四柱概况解释
        $szgkjs = [];
        foreach ($szgxArr as $name) {
            foreach ($szgxgyjsb as $na => $js) {
                $naArr = explode('、', $na);
                if (in_array($name, $naArr)) {
                    $szgkjs[$name] = $js;
                }
            }
        }
        // 四柱天干地支详情
        $sztgdzxq = [];
        foreach ($nvtgdzgxb as $t => $val) {
            // 循环天干地支
            foreach ($val as $tgdz => $item) {
                $key = "{$t}{$tgdz}";
                $keyName = match ($key) {
                    'ytg' => '年干',
                    'ydz' => '年支',
                    'mtg' => '月干',
                    'mdz' => '月支',
                    'dtg' => '日干',
                    'ddz' => '日支',
                    'htg' => '时干',
                    'hdz' => '时支',
                };
                // 获取天干地支详情
                $tgdzxq = $sztgdzxqb[$key]['关系'] ?? [];
                if (empty($tgdzxq)) {
                    continue;
                }
                $gx = [];
                foreach ($item as $name) {
                    $na = strip_tags($name);
                    $jsArr = [];
                    $jsKey = '';
                    if (!isset($tgdzxq[$na])) {
                        foreach ($tgdzxq as $title => $js) {
                            $titleArr = explode('、', $title);
                            if (in_array($na, $titleArr)) {
                                $jsArr = $js;
                                $jsKey = $title;
                            }
                        }
                    } else {
                        $jsArr = $tgdzxq[$na];
                        $jsKey = $na;
                    }
                    if (empty($jsArr)) {
                        continue;
                    }
                    // 判断是否类似 ‘三合、三会、六合、暗合’ 这种一组的，需要合并
                    if (isset($gx[$jsKey])) {
                        $gx[$jsKey] = [
                            'name' => $gx[$jsKey]['name'] . '、' . $na,
                            'js' => $jsArr,
                        ];
                    } else {
                        $gx[$jsKey] = [
                            'name' => $na,
                            'js' => $jsArr,
                        ];
                    }
                }
                if (!empty($gx)) {
                    $sztgdzxq[] = [
                        'name' => $keyName,
                        'dy' => [
                            '含义' => $sztgdzxqb[$key]['含义'],
                            '影响' => $sztgdzxqb[$key]['影响'],
                        ],
                        'gx' => array_values($gx),
                    ];
                }
            }
        }

        $result = [
            // 男女天干地支关系
            'nvtgdzgxb' => $nvtgdzgxb,
            // 年柱、日柱纳音五行
            'ynnywx' => $ynNyWx,
            // 五行相生相克
            'wxxsxk' => $wxxsxk,
            // 夫妻宫加持吉神
            'fqgjcjs' => $fqgjcjs,
            // 四柱概况解释
            'szgkjs' => $szgkjs,
            // 四柱天干地支详情
            'sztgdzxq' => $sztgdzxq,
        ];
        return $result;
    }

    /**
     * 获得纪年五行的相关数据 不含藏干
     * @param array $jiNian
     * @return array
     */
    protected function getJnWx(array $jiNian): array
    {
        $jnWx = [];
        $jnNum = [
            '金' => ['wx' => '金', 'num' => 0], '木' => ['wx' => '木', 'num' => 0],
            '水' => ['wx' => '水', 'num' => 0], '火' => ['wx' => '火', 'num' => 0], '土' => ['wx' => '土', 'num' => 0],
        ];
        $jnYy = [];
        $wxAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $tmpWx = $wxAttr[$v1];
                $jnWx[$k][$k1] = $tmpWx;
                $jnNum[$tmpWx]['num']++;
                $jnYy[$k][$k1] = BaziExt::getYinYang($v1) ? '阳' : '阴';
            }
        }
        return [
            'wx' => $jnWx,
            'yy' => $jnYy,
            'num' => array_values($jnNum),
        ];
    }

    /**
     * 干支关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnGx(array $jiNian): array
    {
        $result = [];
        foreach ($jiNian as $k => $v) {
            $arr = [];
            $result[$k]['t'] = [];
            $result[$k]['d'] = [];
            foreach ($jiNian as $k1 => $v1) {
                if ($k == $k1) {
                    continue;
                }
                foreach ($jiNian as $k2 => $v2) {
                    if ($k2 == $k1 || $k2 == $k) {
                        continue;
                    }
                    $arr[] = [$v[1], $v1[1], $v2[1]];
                }
                $strTg = $v[0] . $v1[0];
                $strDz = $v[1] . $v1[1];
                // 天干相合
                if (BaziCommon::xianHeTg($strTg)) {
                    $result[$k]['t'][] = '合';
                }
                // 天干或地支相克
                if (BaziCommon::getXianKe($strTg)) {
                    $result[$k]['t'][] = '克';
                }
                // 根据天干+天干或地支+地支来判断是否相冲
                if (BaziCommon::getXianChong($strTg)) {
                    $result[$k]['t'][] = '冲';
                }
                // 地支六合
                if (BaziCommon::liuHeDz($strDz)) {
                    $result[$k]['d'][] = '六合';
                }
                // 天干或地支相克
                if (BaziCommon::getXianKe($strDz)) {
                    $result[$k]['d'][] = '克';
                }
                // 地支相刑
                if (BaziCommon::getXianXin($strDz)) {
                    $result[$k]['d'][] = '刑';
                }
                // 天干或地支相克
                if (BaziCommon::getXianKe($v1[1] . $v[1])) {
                    $result[$k]['d'][] = '被克';
                }
                // 根据天干+天干或地支+地支来判断是否相冲
                if (BaziCommon::getXianChong($strDz)) {
                    $result[$k]['d'][] = '冲';
                }
            }
            // 地支三合或三会 包含 全和半
            $sanHeRes = BaziCommon::getSanHeDz2($arr);
            if (!empty($sanHeRes[3])) {
                $result[$k]['d'][] = '三合';
            }
            if (!empty($sanHeRes[2])) {
                $result[$k]['d'][] = '半三合';
            }
            $result[$k]['t'] = array_values($result[$k]['t']);
            $result[$k]['d'] = array_values($result[$k]['d']);
        }
        return $result;
    }

    /**
     * 命局特点
     * @param int $sex 性别 0男 1女
     * @return array
     */
    protected function getMingJuTeDian(int $sex): array
    {
        $info = $this->userInfo[$sex];
        if ($sex) {
            // 女
            $list = [
                'one' => [
                    '偏印' => '你行事低调而敏锐，善于捕捉周围细微变化，生活工作井然有序，独立且享受独处，但孤独过久也会感到落寞。你拥有出色的学习天赋和奇特思维，对玄学和星座抱有浓厚兴趣。',
                    '正印' => '你性格温和有礼，宽容仁慈，亲和力强，易相处。偏爱与有内涵、有教养的人交往，享受这样的交流。善于沟通，为他人着想，淡泊名利，但可能因此缺乏进取心，生活略显平庸。',
                    '正财' => '你保守诚实，稳重不浮夸，行事低调，不追求标新立异。工作细致耐心，责任心强，对家人朋友忠诚守信，细心友善，是顾家的典范。',
                    '偏财' => '你乐观活泼，充满活力，对生活有强烈的控制欲，不喜欢意外。对朋友慷慨大方，渴望名利和富贵生活，思维活跃，能力出众，精明强干，善于抓住机会。但要注意避免过于虚浮和缺乏节制。',
                    '正官' => '你自控能力强，自律性高，端庄稳重，严肃认真。工作注重效率，善于计划，有组织和管理才能，但可能过于循规蹈矩而显得刻板。',
                    '七杀' => '你果断坚决，勇敢无畏，有追求和担当精神，责任心强，善于统筹和组织。对朋友正直豪爽，有强烈的正义感，但可能过于叛逆冲动。',
                    '比肩' => '你独立自主，自信强烈，勇于追求理想和目标。坚持自我，不易改变决定，但可能过于自我，不听取他人意见而引发争执。善于交朋友，但知心朋友少，懂得分寸和进退。',
                    '劫财' => '你个性突出，想法独特，坚持自我价值，重视他人尊重。有野心，努力获得认可，但可能过于盲目而缺乏理智。应变能力强，爱打抱不平，不畏强权。',
                    '食神' => '你聪明文雅，开朗乐观，重视精神生活，偏好文学艺术，但可能过于幻想而不切实际。性格温和，善解人意，依赖心强，可能缺乏毅力和有懒惰倾向。喜欢零食和烹调，对饮食文化有见解。',
                    '伤官' => '你领悟性强，对文学艺术感受敏锐，学习能力强，兴趣广泛但可能博而不精。高傲自信，不受世俗礼法约束，特立独行，易对他人提出高要求而任性缺乏约束。可能伤人而不自知，易招误会和是非。',
                ],
                'two' => [
                    '甲己' => '你性格坚强，对事业充满进取心，积极向前，并擅长应对复杂的人情世故。你拥有强大的钱财控制力，经济头脑敏锐。',
                    '己甲' => '你具备管理天赋，善于观察他人，并与领导保持良好关系。你性格稳重，处理事务比同龄人更成熟，喜欢担任管理职务，但需注意应对周围的竞争压力。',
                    '乙庚' => '你性格温柔且多情，自我约束力强，处世严谨，对自己和他人都有较高要求。你喜欢与人交流，探讨深刻话题。虽然外表看似理性，但内心充满感性。',
                    '庚乙' => '你为人正直直爽，具有强烈的同情心。在处理问题时能公正无私，设身处地为他人着想，非常适合从事需要公正和同情心的职业，如法官、律师等。',
                    '丙辛' => '你在日常生活中对事物有较强的掌控欲，性格刚毅，喜欢在集体中展现自己的优势，但也能保持适度。你对经济财务方面非常敏感，懂得多种合法的赚钱方法。',
                    '辛丙' => '你在爱情中可能不够专注，与伴侣的关系或许不够稳定。你拥有众多异性朋友，但难以对某一异性保持持久的真诚。然而，你慷慨大方，对金钱看得很开，对家人和朋友都非常慷慨。',
                    '壬丁' => '你做事可能缺乏恒心，容易放弃。但对于自己真正喜欢的事物，能够长期保持热情并坚持下去。你聪明敏锐，但遇事容易激动，缺乏耐心。在处理问题时善于从他人角度思考，但在小事上可能过于固执。',
                    '丁壬' => '你热爱美丽的事物，对恋人的外貌有较高要求，并支持通过医美手段提升外貌。你在关键时刻能够克制自己的情绪，不轻易冲动。你的性格善变，因为真正了解你的人很少，所以有时会感到孤独。',
                    '戊癸' => '你具有强烈的家庭观念，对家庭有很强的依赖感。在与他人相处时，你总能像家人一样融洽，容易信任别人。在选择恋人时，你也会比较看重对方的家庭情况和家庭观念。你的学习能力不错，对文字敏感，喜欢阅读，思维能力强。',
                    '癸戊' => '你花钱没有明确的预算，收入多支出也多，自制力在节省方面较弱。然而你的商业头脑不错，财运也较好。在爱情中你可能更喜欢比自己年长、更成熟的男性。',
                ],
                'three' => [
                    '正印' => '你心地善良，勤于思考，非常重视人际关系，并对他人有较强的依赖性，渴望得到他人的关怀与照顾。然而，有时你可能会有些不切实际的想法，并且在处理人际关系时可能因不慎而触碰到他人的敏感点。',
                    '偏印' => '你具有极高的精明度和领悟力，但性格中过于谨慎的一面可能使你在交往中不易展露真实想法，对他人保持一定的防备心态，且容易往负面方向联想。这种心态可能会影响你的人际交往效果，建议你适当调整。',
                    '正官' => '你责任心极强，善于自我约束和控制，但由于背负了过多的压力和责任，可能给人一种严肃、沉重的感觉。在处理事情时，你可能过于严苛，缺乏灵活性，急于求成，这可能让你显得有些不近人情。',
                    '七杀' => '你勇敢正直，对邪恶事物有着强烈的反感，待人热情且富有创新精神。在工作中，你坚决果断，敢于直面困难，具有很强的上进心和事业心，同时也有着出色的责任心和领导能力。',
                    '正财' => '你是一个踏实稳重的人，喜欢维持现状，做事谨慎且有些固执，不太善于冒险和主动出击。你非常重视物质生活，特别是金钱方面，因此得失心可能会比较重。',
                    '偏财' => '你乐观活泼，充满活力，对生活有着强烈的控制欲，不喜欢出现意外情况。你对朋友非常慷慨大方，经常为他们花费金钱。你渴望过上名利双收、荣华富贵的生活，思维活跃、能力出众，善于抓住每一个机会，具有成功的潜力。',
                    '比肩' => '你是一个独立自主、自信的女性。你具有强烈的个人意识，富有进取精神，勇于追求自己的理想和目标。一旦做出决定，就不会轻易改变。但有时候，你可能过于坚持自我，不听取他人的意见，从而引发争执。你喜欢交朋友，但真正交心的朋友却很少。你懂得分寸，知道进退，会与人保持一定的距离。',
                    '劫财' => '你个性突出，想法独特，坚守自己的价值观，非常在乎别人是否尊重自己。你不服输，有野心，愿意付出努力来获得别人的认可。你应变能力强，爱打抱不平，不畏强权。',
                    '食神' => '你是聪明文雅、开朗乐观的人，重视精神生活，对文学艺术有偏好。但有时候，你可能过于幻想，不切实际。你依赖性较强，可能缺乏毅力或有懒惰的倾向。你喜欢吃零食和烹调，对饮食文化有一定的见解。',
                    '伤官' => '你领悟力强，对文学艺术感受敏锐，学习能力强且兴趣广泛，但可能博而不精。你有比较高傲的一面，不喜欢被世俗礼法约束，以自我为中心，比较特立独行。你可能容易对身边的人提出高要求，伤人而不自知，从而招致误会和是非。',
                ],
            ];
        } else {
            $list = [
                // 天干地支十神相同 天干和地支都没有合，冲
                'one' => [
                    '偏印' => '你行事低调且沉稳，思维敏捷，善于捕捉生活的细微之处。高度的警觉性让你给人留下精明能干的印象。你喜欢独处，独立生活能力强，但可能在人际交往中显得自私或冷漠。对心理学、宗教等领域有浓厚兴趣，心思细腻，但也容易因此而烦恼。',
                    '正印' => '你性格温和，宽容仁慈，重视感情，待人亲切有礼，善于沟通。你尊重他人，为他人着想，容易心软，对名利淡泊，展现出良好的教养。但可能因缺乏进取心而显得迟钝或消极。',
                    '正财' => '你性格保守且沉稳，工作踏实，不喜欢标新立异。你安守本分，给人留下可靠的印象。你忠诚守信，细心照顾身边的人。但可能过于拘谨，缺乏进取心，有时显得懦弱或无能。',
                    '偏财' => '你个性乐观开朗，充满活力，慷慨大方，常常为朋友花费大量金钱。你向往名利和荣华富贵的生活，但能把握当下，想法多且新颖，能力强悍，处事机智圆滑。若能适当调整控制欲，抓住机遇，便可成功，事业也会一帆风顺。',
                    '正官' => '你成熟稳重，有事业心，自律性和自制力较强，给人留下严肃认真的印象。你对朋友重情重义，重视信用。你工作效率高，不做无目的、无价值的工作，遵循合理规则办事。但可能因过于刻板而缺乏灵活应变能力。',
                    '七杀' => '你坚决果断，敢作敢为，勇于突破环境，开创新机，你的想法和见解具有革命性。你对事业有追求，有责任心，个性正直，有正义感，嫉恶如仇，待人热情。但有时会显现出叛逆冲动的一面。',
                    '比肩' => '你个人意识强烈，自小独立自主，自尊心强，追求自己的理想和目标，不轻易改变。你为人处世客观坦诚，有很多朋友，但明分寸，知进退，与人保持恰当距离。然而，可能因过于自我而缺乏合群精神，导致孤立无援。',
                    '劫财' => '你是个有主见的人，个性鲜明，热忱坦直，在意别人对自己的看法和是否尊重自己。你为赢得自尊付出超出常人的努力，处事圆滑世故，随机应变。你爱打抱不平，但可能因缺乏理智而变得蛮横冲动。',
                    '食神' => '你是个感性的人，心思细腻，追求精神生活，对文学和艺术有天赋。你性情温和，待人有善意，思想脱俗，气质不俗。你口才好，爱吃喝玩乐，享受生活。但有时会过于理想化，脱离现实。',
                    '伤官' => '你才华横溢，博学多能，思想奇异，领悟力与兴趣广泛，但不精通。你个性上有高傲的一面，桀骜不驯，不拘泥于世俗礼法。你较以自我为中心，特立独行，不合群，有时伤人不自知，容易招致误会和是非。',

                ],
                // 日干+其它干
                'two' => [
                    '甲己' => '你非常注重事业发展，性格积极向上，具有强烈的上进心和经济头脑，对金钱的掌控力也很强。',
                    '己甲' => '你擅长管理，深谙人情世故，与领导关系良好。但需注意与同事的交际方式，有时会显得争强好胜，热衷于扮演领导角色，然而处理事情稳妥可靠。',
                    '乙庚' => '你风度翩翩，自控能力强，对人对事都有严格要求，喜欢与人深入探讨问题并分享见解。外表看似理性，内心实则感性。',
                    '庚乙' => '你铁面无私，能够刚正不阿地处理问题，善于设身处地替他人着想，非常适合从事国家公职。性格坦率真诚，同理心强。',
                    '丙辛' => '你对周围事物有较强的控制欲，性格略显张扬，喜欢自我表现但懂得适可而止。经济头脑发达，总能想出多且正当的赚钱点子。',
                    '辛丙' => '你豪爽大方，对家人朋友非常慷慨，金钱上很少计较。然而对待爱情可能不够认真，异性朋友虽多但难以维持长久真诚的关系。',
                    '壬丁' => '你聪明睿智，但有时过于冲动莽撞，做事情缺乏耐心，容易半途而废。不过对于自己真正喜欢的事情，你能够坚持不懈。处理问题时很懂得换位思考，但在一些小事上容易固执己见。',
                    '丁壬' => '你既聪明睿智又容易冲动莽撞，常常缺乏耐心，喜欢半途而废。然而对于自己喜欢的事情却能够坚持到底。在处理问题时懂得换位思考，但在小事上容易表现得固执。',
                    '戊癸' => '你桃花运佳，在爱情中非常看重恋人的家庭情况和观念。你依赖家庭，顾家和睦，与人相处融洽，容易信任别人。喜欢阅读，文字功底和文学思维都很不错。',
                    '癸戊' => '你生意头脑聪明，财运亨通，更喜欢与成熟有魅力的女性交往。赚钱能力强但花销也大，省钱方面的自制力相对较差。',
                ],
                // 月支十神
                'three' => [
                    '正印' => '你善于思考，性格平和，非常重视人际关系，并对他人有一定的依赖性，希望得到照顾。但有时你可能会有不切实际的想法，这可能会无意中得罪他人。',
                    '偏印' => '你聪明且领悟力强，然而性格较为谨慎，在社交中防备心重，难以轻易信任他人。需要注意的是，过于聪明和算计有时可能导致你显得不合群，因此调整人际关系是必要的。',
                    '正官' => '你自控能力强，具备高度的责任心，并懂得自我管理。但需要注意的是，背负过多压力可能使你显得严肃拘谨，而过于理性则可能导致缺乏独立思考和灵活变通的能力。在追求功利的过程中，可能会显得有些不近人情。',
                    '七杀' => '你勇敢正直，待人热情，具有果断的开拓精神。在工作中，你敢于直面困难，有上进心，敢做敢为，并懂得创新。你对人对事都表现出强烈的责任心，并具备一定的领导能力。',
                    '正财' => '你性格稳重且踏实，喜欢安分守己，维持现状。在处理事务时，你表现出谨慎和固执的特点，但可能缺乏冒险精神和主动性。你非常重视金钱和物质生活，得失心相对较重。',
                    '偏财' => '你乐观开朗，充满活力，为人慷慨大方。你向往名利和荣华富贵的生活，但能够把握当下，拥有众多新颖的想法和强大的能力。你处事圆滑机智，只要适当调整控制欲并抓住机遇，便可取得成功，事业也会一帆风顺。',
                    '比肩' => '你独立自主，自信心强，个人意识强烈且意志坚定，不易改变决定。但有时你可能过于刚愎自用，容易与人发生争执。虽然朋友众多，但交心的却较少。你能够把握分寸，知进退，与人保持一定的距离。',
                    '劫财' => '你个性鲜明，想法独特，坚持自己的价值观，不服输。你野心勃勃，为了赢得自尊付出了超出常人的努力。你对朋友慷慨大方，爱打抱不平且不畏惧强权。',
                    '食神' => '你具备艺术天赋，善于幻想且兴趣广泛，但不喜欢受到拘束。你依赖心强，有时做事虎头蛇尾缺乏毅力。你为人和气向往自由不受拘束的生活。',
                    '伤官' => '你才华横溢博学多能思想奇异领悟力广泛但不精通。你个性高傲桀骜不驯不受世俗礼法拘束以自我为中心特立独行。你有时可能无意中伤人不自知这可能会导致误会和招惹是非。',
                ],
            ];
        }
        $godT = $info['god'];
        $godZ = $info['godz'];
        $godHide = $info['_god'];
        $gxArr = $info['gx'];
        $result = [];
        foreach ($godT as $k => $v) {
            $strK = substr($k, 0, 1);
            if (array_intersect(['合', '冲'], $gxArr[$strK]['t'])) {
                continue;
            }
            foreach ($godHide as $k1 => $v1) {
                $strK1 = substr($k1, 0, 1);
                if (in_array($v, $v1['god']) && empty(array_intersect(['三合', '冲', '六合', '半三合'], $gxArr[$strK1]['d']))) {
                    $result[$v] = $list['one'][$v];
                }
            }
        }
        if (!empty($result)) {
            return array_values($result);
        }
        $jiNian = $info['base']['jinian'];
        $ytg = $jiNian['d'][0];
        foreach ($jiNian as $k => $v) {
            if ($k == 'd') {
                continue;
            }
            $str = $ytg . $v[0];
            if (isset($list['two'][$str])) {
                $result[$str] = $list['two'][$str];
            }
        }
        if (!empty($result)) {
            return array_values($result);
        }
        $godZM = $godZ['month'];
        return [
            $list['three'][$godZM],
        ];
    }

    /**
     * 优缺点
     * @param int $sex 性别 0男 1女
     * @return array
     */
    protected function getBaziYouQue(int $sex): array
    {
        $you = [];
        $que = [];
        $info = $this->userInfo[$sex];
        $xyShen = $info['xy']['shen'];
        $godThs = $info['god_ths'];
        if ($sex) {
            $listY = [
                '才财' => [
                    '你诚实守信，知足常乐，并善于分辨是非。由于你乐于助人，因此拥有极佳的人缘。在婚后，你对家庭非常负责，既体贴丈夫又疼爱孩子。',
                    '你慷慨重情，性格中带有侠义之气，为人豪爽开朗。你具备多方面的才能，口才好，交际能力强，对外关系良好。做事时你果断明智，对物质有敏锐的洞察力，并掌握了一定的经济手段。',
                ],
                '官杀' => [
                    '你品德高尚，善良且坚定，拥有强烈的自尊心。面对困难，你从不气馁，做事公正有条理，总能制定出周密的计划，并因此取得适当的成就，赢得他人的认可和尊重，很有可能因此被委以重任。',
                    '你待人热情，行事雷厉风行，秉持正义与善良，总是乐于助人。但有时你的脾气稍显暴躁，容易冲动，性格中有些许古怪，可能会因此行事不够稳重。在日常生活和工作中，你展现出了一定的进取心，做事有魄力，争强好胜，不屈不挠。',
                ],
                '印枭' => [
                    '你在与人交往中表现和善，通情达理，重情重义。你有自己的理想追求，聪明且富有内涵，善于思考分析，行事灵活变通，应变能力强，但从不轻易显露锋芒。',
                    '你敏感而细致，悟性高，拥有奇特的感受力，并具备一定的创造天赋。你内心警觉，精于偏门技艺，擅长观察他人的言语和神色，精明干练且足智多谋。但需要注意的是，你有时可能会显得有些冷酷和尖刻。',
                ],
                '食伤' => [
                    '你聪明机智，温文尔雅且具有忍让精神。你不轻易与人发生争执，总是保持优雅的风度，并散发出文艺气质。你重视身心一体性，感情充沛，对文学、艺术和宗教等领域都充满了浓厚的兴趣。',
                    '你容貌秀丽可爱，多才多艺，伶牙俐齿，聪明干练。你行事果断，充满活力，总是积极进取，并因此容易取得一定的成果，赢得声望和荣誉。',
                ],
                '比劫' => [
                    '你忠诚守信，意志坚定，不会轻易改变自己的选择。你真心与人交往，自尊心强，是非分明，有自己的主见和积极进取的精神。你清楚自己的需求，并拥有坚韧不拔的毅力和高度的容忍度，因此常常能够在逆境中获得自己想要的东西。',
                    '你性情热情直率，有些傲气，但勇于投机取巧，有勇气面对困难并勇往直前。你适合与他人共同经营事业或自主创业。你重情重义，与兄弟姐妹感情深厚，并具备一定的社交才能，因此朋友们在社会上对你帮助颇多。你能言善辩，心思敏捷，善于随机应变。',
                ],
            ];
            $listJ = [
                '才财' => [
                    '你性格温和、体贴，但有时过于谨慎，可能会容易受人影响。你有很强的自我保护意识，对新事物可能缺乏足够的勇气去尝试，需要更多的鼓励和支持。',
                    '你喜欢稳定的生活环境，对现有状况感到满足，乐于听到他人的赞美，并且也会给予他人适当的恭维。然而，你可能需要注意饮酒的态度和方式，以及对他人评价的客观性，以保持真诚和善意。',
                ],
                '官杀' => [
                    '你在某些情况下可能表现出较强的依赖性，行事有时显得犹豫不决，对自己有较高要求但行动不够果断。这种谨慎可能会导致你错失一些机会。你需要学会更加勇敢地面对挑战和失败。',
                    '你在处理事务时可能容易感到急躁和不安，有很多顾虑和担忧。你可能需要更多的安全感和稳定性。此外，你通常不太愿意透露自己的真实想法，善于保护自己的隐私。',
                ],
                '印枭' => [
                    '在与他人长期相处的过程中，你可能会产生较强的依赖感，有时想法过于简单和理想化，导致实际情况与预期有所差距。你具有较强的自尊心和面子观念，需要学会更加客观地看待自己和他人。',
                    '你具有精明干练的特点，但可能缺乏持久的耐心和专注力。你善于寻找捷径，学习能力强但可能难以取得显著的成就。你需要注意在处理人际关系时保持谦逊和合作的态度，以避免骄傲和孤立。',
                ],
                '食伤' => [
                    '你可能缺乏刻苦耐劳的精神和坚定的主观意识，有时想法过于浮夸难以实现。在言语表达上可能华而不实，容易给人留下不切实际的印象。你需要学会更加踏实和务实地面对生活。',
                    '你可能容易放纵自己的欲望和情绪，以自我为中心而忽略他人的感受。在行事上可能缺乏明确的计划和规则，容易凭个人喜好做出决定而带来不必要的麻烦。你需要学会更加理性和客观地看待问题。',
                ],
                '比劫' => [
                    '你可能给人一种孤傲的印象，自我封闭、独断专行，不易与他人妥协。在团体中虽然突出但可能知心朋友较少。你需要学会更加开放和包容地与他人相处以建立良好的人际关系。',
                    '你性格固执己见、自满自足不易妥协在处理感情问题时可能显得不够灵活和变通对自己人要求严苛而对外人则较为宽容这可能会引发一些家庭争执，需要学会更加公正和平等地对待身边的人。你喜欢高谈阔论但需要注意言行一致避免空洞无物。',
                ],
            ];
            //            if ($godTzS['jc'] >= 3) {
            //                $que[] = '易于被冲动所驱使，有时行为可能过于激烈。性格活泼，但占有欲较强，感情往往成为主导因素。';
            //            }
            //            if ($godTzS['sg'] >= 3) {
            //                $que[] = '行事不拘泥于传统礼法，具有叛逆的一面，自我控制能力相对较弱，可能在责任感上有所欠缺。在情感世界中，难以坚定拒绝诱惑，因此恋爱关系可能较为短暂，缺乏持久性。';
            //            }
        } else {
            $listY = [
                '才财' => [
                    '你以正直为本，充满爱心，总是乐于伸出援手，对他人充满热情，因此赢得了广泛的人缘。在婚姻中，你对妻子忠贞不二，对家庭尽职尽责。',
                    '你慷慨大方，通情达理，善于与人交往，总是带着积极的心态和热情待人。在生活和工作中，你处事圆滑，机智过人，有胆有识，常常能够抓住机遇。',
                ],
                '官杀' => [
                    '你品格高尚，廉洁自律，心地善良但又不失坚定，面对困难从不轻易放弃。你为人处世公正有条理，做事总能取得良好的成果，深受他人尊重和信赖，很有可能会获得高位。',
                    '你待人如火，果断决绝，正义凛然，乐于助人。但有时性情稍显急躁，脾气有些古怪，行事率直。在生活和工作中，你充满上进心，有魄力，争强好胜，自尊心强，不轻言败。',
                ],
                '印枭' => [
                    '你宽厚待人，通情达理，真心实意，重义轻利。你有远大的志向，聪明睿智，却深藏不露。你思考能力强，善于随机应变。',
                    '你心思缜密，领悟力高，能深入他人内心，创造力丰富，时刻保持警惕。你在偏业上有独到之处，精明干练，擅长观察他人言色，机智过人，但有时显得有些苛刻。',
                ],
                '食伤' => [
                    '你聪明伶俐，性格温和，待人宽厚，不易与人发生冲突，风度翩翩，具有文艺气质，注重精神与物质的平衡。你感性且富有情怀，对文学、艺术、宗教等领域有深厚的兴趣。',
                    '你外貌俊秀，才华横溢，领导能力强，口才好，精明老练，魄力十足。你充满活力，勇于拼搏，积极进取，在某些领域很容易取得卓越的成就和威望。',
                ],
                '比劫' => [
                    '你敦厚朴实，诚实守信，意志坚定，与朋友交往真诚。你自尊心强，有独特的见解，明辨是非，坚韧不拔。你努力进取，有耐力，总能找到适合自己的环境并逆风翻盘。',
                    '你性情热情直率，豪爽大方，自尊心强，勇往直前。你讲义气，适合与他人共同创业或自己开创事业。你重感情，与兄弟姐妹相处和睦，有交际才能和口才，善于随机应变。',
                ],
            ];
            $listJ = [
                '才财' => [
                    '你性格谨慎，有较强的自我保护意识，行事时常显胆小。虽然深思熟虑，但容易满足现状，缺乏冒险和进取的精神。在决策时常常犹豫不决，可能因此错失一些良好的机会。',
                    '你喜欢稳定的生活，不喜欢变化和冒险，容易沉迷于现有的安逸状态。有时过于喜欢饮酒和享乐，对他人的奉承很受用，但在评论他人时可能过于夸张，缺乏真诚和客观性。',
                ],
                '官杀' => [
                    '你面临的压力大，主动性较弱，行事风格偏向保守，有较强的依赖心理。面对任务和挑战时容易感到紧张和不安，对失败的恐惧使你常常犹豫不决，可能导致错失良机。',
                    '你的性格中带有急躁和软弱的一面，行事时常常畏首畏尾，追求平稳的生活，缺乏斗志和决心。你习惯隐藏自己的真实想法和情感，这可能使你在人际交往中显得不够坦诚。',
                ],
                '印枭' => [
                    '你有较强的自尊心和面子观念，有时表现出利己的思想，缺乏独立能力和过强的依赖心理。你的思想比较纯真，容易将事情理想化，这可能导致实际计划进行不够顺利。',
                    '你精明强干，但缺乏持久的耐心和专注力，常常举棋不定，小聪明多而大成就少。在与人交往中可能显得过于计较、自私和傲气，难以融入他人。你虽然愿意拼搏和奉献，勤劳工作，但心中常有怨气。你的性格固执而能吃苦。',
                ],
                '食伤' => [
                    '你缺乏刻苦耐劳的精神，有较强的依赖心理，喜欢夸夸其谈而实际行动不足，常常纸上谈兵而结果不如预期。你容易感到疲倦和自命不凡，有时显得顾影自怜。',
                    '你行事时比较肆意，强调自我而忽视他人感受，不太遵守规则和约束，这可能导致你得罪人或惹上麻烦。',
                ],
                '比劫' => [
                    '你行事风格自我，不易与他人妥协和相处。虽然朋友众多但知己较少。你为人耿直严肃，对待部下和亲人要求严格，可能给人留下不够圆滑和不通人情的印象。',
                    '你的性格固执而自尊心强，有时显得高傲和冒失，具有双重人格的特点。你占有欲强，对待感情不够灵活和融通，对自己人要求高而对别人较为宽待，这可能导致家庭关系不够融洽。你常常夸夸其谈，善于为自己狡辩。',
                ],
            ];
            //            if ($godTzS['jc'] >= 3) {
            //                $que[] = '性格鲁莽，易冲动，有时精力过剩，具掠夺性，占有欲强，易做出格事。感情方面易失理智，行事冲动。';
            //            }
            //            if ($godTzS['sg'] >= 3) {
            //                $que[] = '不受世俗礼法约束，行为有时叛逆，易轻视他人，自我约束弱，责任感不足。感情方面难挡诱惑，易感情不专，恋情多短暂。';
            //            }
        }
        $listEn = [
            '才财' => ['zc', 'pc'], '官杀' => ['zg', 'qs'], '印枭' => ['zy', 'py'], '食伤' => ['ss', 'sg'], '比劫' => ['bj', 'jc'],
        ];
        foreach ($listEn as $k => $v) {
            $en = $listEn[$k];
            $bool1 = in_array($k, [$xyShen['yong'], $xyShen['xi']]);
            $bool2 = in_array($k, [$xyShen['ji'], $xyShen['qiu']]);
            foreach ($en as $k1 => $v1) {
                if ($godThs[$v1] == 0) {
                    continue;
                }
                if ($bool1) {
                    $you[] = $listY[$k][$k1];
                } elseif ($bool2) {
                    $que[] = $listJ[$k][$k1];
                }
            }
        }
        return [
            'you' => $you,
            'que' => $que,
        ];
    }

    /**
     * 恋爱性格
     * @param int $sex 性别 0男 1女
     * @return array
     */
    protected function getLianAiXingGe(int $sex): array
    {
        $info = $this->userInfo[$sex];
        $godZ = $info['godz'];
        $godZd = $godZ['day'];
        $godTzs = $info['god_tzs'];
        $godThs = $info['god_ths'];
        $jiNian = $info['base']['jinian'];
        $gx = $info['gx'];
        $dgz = implode('', $jiNian['d']);
        $result = [];
        if ($sex) {
            $list = [
                '正印' => '在情感关系中，你高度重视与伴侣的心灵契合，对对方设有较高标准，期望其兼具气质与内涵。在这段关系中，一方可能会产生强烈的依恋心理，并投入更多精力来照顾对方。你乐于奉献，将伴侣视为家人一般，以温柔、耐心的态度进行交流，提供细致入微的关怀。然而，这种强调细腻呵护和深刻互动的方式可能导致过度依赖，使个人的独立性受到一定影响，有时可能会产生逃避现实责任的想法，或面临被过度索取的风险。因此，需要认真思考如何维持健康的情感关系。',
                '偏印' => '你在感情中有着独特的追求，可能深情地爱着对方，或表现出某种恋父情结，使恋爱关系显得较为模糊。由于缺乏安全感，你可能不善于传达内心的情感，从而抑制了浓烈的爱意，使对方难以察觉。你注重隐私，对伴侣难以完全敞开心扉，有所保留。在追求精神共鸣的同时，你深爱着对方，但也可能展现出强烈的独占欲，要求对方全身心地投入。这种执着和疑虑可能导致沟通不畅，进而影响感情的发展。建议你多表达自己，坦诚地分享忧虑，给予伴侣更多的信任。',
                '正财' => '你对待感情的态度守旧、含蓄、保守而谨慎。作为忠诚的恋人，你用心对待伴侣，专注地经营着这段感情，细心地回应对方的需求。你追求情感和物质上的安全感，不喜欢被外界因素所牵引，对变化持谨慎态度。虽然你的含蓄内敛可能显得不够浪漫，甚至有时过于沉静，但你能真心付出，展现出强烈的责任心，给予伴侣信任和安全感。',
                '偏财' => '你在感情中展现出浪漫慷慨、温柔多情的一面，直率而热烈地表达爱意，以伴侣为荣。你的思维活跃，充满情调，为对方带来惊喜和欢乐。然而，你也可能表现出较强的控制欲，希望成为感情中的主导者，这可能对感情的深度发展产生一定影响。建议你改善控制欲，增强与伴侣的沟通。',
                '食神' => '面对感情时，你显得含蓄而保守，但内心却丰富多情。你可能曾经历过难忘的感情历程，注重仪式感，偶尔表现出情绪化的一面。然而，在大多数情况下，你体贴、善解人意，对理想化的追求较高。你期待找到符合要求的伴侣，将其视为灵魂伴侣，若未能如愿，则可能感到孤独。建议你保持平和的心态，积极寻找合适的伴侣。',
                '伤官' => '你对感情和伴侣有着理想化的追求，不愿被传统束缚。然而，这种理想化有时会造成困扰。你的恋爱性格脱俗、有情调，真诚地对待伴侣，用才华为对方创造美好回忆。但你也可能表现出情绪化、自我中心的一面，忽视对方的感受，从而伤害到伴侣，影响感情的稳定性。建议你调整心态，理智地面对感情。',
                '比肩' => '在感情中，你保持独立性，要求平等的恋爱关系。然而，这可能导致关系中出现矛盾和争吵，难以和谐相处。你可能具有一定的自恋倾向，喜欢与思想观念一致或个性独特的人交往。在照顾伴侣需求方面，你可能需要更加关注对方的感受，尊重对方。你的表达方式直接、不轻易妥协，这可能会影响感情的和谐度。建议你学习妥协和理解。',
                '劫财' => '你希望在感情中双方能保持独立平等的关系，因此倾向于选择背景相近、性情相似的人作为伴侣。在恋爱中，你可能表现出自我中心和一定的控制欲，态度忽冷忽热，忽略了对方的感受，这可能对感情造成不良影响。建议你注意调节自己的态度，增强与伴侣的沟通和理解。',
                '正官' => '你持有端正的恋爱观，对待感情认真负责、慎重保守。你愿意为对方付出，并包容其缺点，期望伴侣能在困难时与你共同承担。然而，你可能表现出一定的占有欲和严肃性，对伴侣的限制较多，导致缺少浪漫情调。这可能使伴侣产生抱怨。建议你调整自己的方式，增加一些浪漫元素。',
                '七杀' => '你喜欢恋爱中的浪漫和刺激感，具有保护欲和独特的魅力。然而，在关系中你可能过于强势地管制对方，表现出任性霸道的一面。在出现分歧和争吵时，你不轻易让步或改变自己的立场，这可能会影响双方的融洽度。建议你及时调整自己的状态，增强与伴侣的沟通和理解。',
            ];
            $result[] = $list[$godZd];
            // 女命八字同时出现正官和七杀
            if ($godTzs['zg'] > 0 && $godTzs['qs'] > 0) {
                $result[] = '你的八字中同时存在正官和七杀，这意味着你与异性的缘分比较深厚。虽然你有一定的魅力，但要注意的是，在感情关系中不要过于依赖异性，要保持自己的独立性和自主性。同时，也要学会处理复杂的情感问题，避免因多情而影响婚姻生活的稳定。';
            }
            // 命局同时出现官杀和食伤
            if (($godTzs['zg'] + $godTzs['qs']) > 0 && ($godTzs['ss'] + $godTzs['sg']) > 0) {
                $result[] = '你对婚姻的信心不足，常常表现出疑虑，对伴侣持有怀疑态度。若长此以往，可能会动摇婚姻的稳固，甚至引发婚姻危机。建议你多与伴侣沟通，增进理解，以建立健康的婚姻关系。';
            }
            // 日柱坐魁罡
            if ($this->shaShen->getKuiGang($dgz)) {
                $result[] = '你外表靓丽，个性坚强，对待感情态度强势，有些以自我为中心，倾向于掌握主导权，期望对方顺从你的意愿。你言辞坚决但内心温柔，有坚定的原则，然而有时过于直率，可能因此与伴侣产生矛盾。';
            }
            // 原局驿马星被冲
            $ydz = $jiNian['y'][1];
            $ddz = $jiNian['d'][1];
            foreach ($jiNian as $k => $v) {
                if (in_array('被冲', $gx[$k]['d'])) {
                    if ($this->shaShen->getYiMa($ydz . $v[1]) || $this->shaShen->getYiMa($ddz . $v[1])) {
                        $result[] = '在感情中，你可能感到迷茫，心思不易安定，与异性的关系错综复杂，导致感情纠缠不清，难以做出明确的选择。';
                        break;
                    }
                }
            }
            // 原局有伤官
            if ($godTzs['sg'] > 0) {
                $result[] = '你对另一半抱有高期待，对婚姻生活的物质层面也有一定的期望，因此你通常不会轻易降低自己的标准。';
            }
            // 命局伤官大于等于三且命局印星数量≤1（看藏干）
            if ($godThs['sg'] >= 3 && ($godThs['zy'] + $godThs['py']) <= 1) {
                $result[] = '在婚姻中，你可能表现出过度的自我中心，倾向于以自己的想法为主导，这可能会导致你忽略伴侣的需求，同时你也可能试图过度控制对方，这可能引发离婚的风险。你需要认识到这一点并做出适当的调整。';
            }
            // 咸池桃花 年支+月时支
            $listXianCi = ['子酉', '丑午', '寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子'];
            $arr = [$jiNian['y'][1] . $jiNian['m'][1], $jiNian['y'][1] . $jiNian['h'][1]];
            // 命局有桃花星且被合
            $xianCi = 0;
            $xianCiHe = 0;
            foreach ($arr as $v) {
                if (in_array($v, $listXianCi)) {
                    if (BaziCommon::liuHeDz($v)) {
                        $xianCiHe++;
                    }
                    $xianCi++;
                }
            }
            // 命局有桃花星且被合
            if ($xianCi && $xianCiHe) {
                $result[] = '你的异性缘比较好，身边不乏追求者，但要注意的是，不要让暧昧不清的关系影响你的生活和婚姻。';
            }
            // 命局无官杀且无桃花星（含地支藏干主气）
            if (($godThs['zg'] + $godThs['qs']) == 0 && $xianCi == 0) {
                $result[] = '你的异性缘较淡，找到合适的伴侣可能会有些困难。但是婚后容易与另一半产生隔阂，需要加强双方之间的交流沟通，同时注意增加一些生活的情调。';
            }
            // 命局只有一个正官或者七杀，且为喜用神
            if (($godTzs['zg'] + $godTzs['qs']) && in_array('官杀', [$info['xy']['shen']['yong'], $info['xy']['shen']['xi']])) {
                $result[] = '你的异性缘适中，在恋爱中你也有自己的想法和追求，能够真诚地对待感情。在爱情和婚姻方面一般不会遇到太大的挫折。';
            }
            // 命局官杀和财星相邻
            $god10 = implode('', $info['god']) . '|' . implode('', $godZ);
            $arr = ['正官正财', '正官偏财', '七杀正财', '七杀偏财'];
            $bool1 = false;
            foreach ($arr as $v) {
                if (Str::contains($god10, $v)) {
                    $bool1 = true;
                    break;
                }
            }
            if ($bool1) {
                $result[] = '在婚姻中你愿意为对方付出，凡事都为对方着想，能够无微不至地照顾对方。但在事业上可能无法给对方提供太多帮助，甚至会面临生命危机时选择牺牲自己来救下另一半。需要注意的是，你可能忽视了自己的父母。';
            }
            // 命局伤官＞3，且命局印星和比劫相加数量≤3
            if ($godTzs['sg'] > 3 && ($godTzs['zy'] + $godTzs['py'] + $godTzs['bj'] + $godTzs['jc']) <= 3) {
                $result[] = '你的八字中伤官比较多，一生当中可能会有多段恋爱经历。可能会因为感情问题而烦恼，有比较大的概率会晚婚。在婚后生活中，夫妻之间可能会发生一些争吵与冷战，需要注意调节自己的情绪。';
            }
            // 日支为七杀
            if ($godZd == '七杀') {
                $result[] = '你的八字中日支为七杀，对外人可能比较和善，但在家庭中你可能会表现得比较强势，对另一半有些严苛。往往会说对方的不是，需要注意调整自己的态度，耐下心来与对方沟通。';
            }
            // 日柱为丁酉、丁亥、癸巳、癸卯其中一个
            if (in_array($dgz, ['丁酉', '丁亥', '癸巳', '癸卯'])) {
                $result[] = '你外貌偏秀气，温和且聪颖，为人谦虚仁德。你的另一半是你的贵人，可能会给你带来比较大的帮助。';
            }
            // 命局比劫≥3（不看藏干）
            if (($godTzs['bj'] + $godTzs['jc']) >= 3) {
                $result[] = '你在平常的生活中可能有些争强好胜，争吵与是非较多。如果有姐妹，双方关系可能不太融洽。在夫妻关系中可能处于相对强势的地位，对男方父母有可能一定的意见。生活中可能也不会相让。';
            }
        } else {
            // 日支十神
            $list = [
                '正印' => '你非常重视与伴侣的情感和谐，对对方有一定的期望和标准，尤其看重其气质和内在修养。因此，你的伴侣通常在这方面表现出色。你们的关系中，容易出现互相照顾的情况，其中一方可能有较强的依赖心理。你作为伴侣，愿意付出很多，将对方视为家人，给予大量的耐心和关心，通情达理，包容对方。你注重自身形象，自尊心强，渴望与伴侣有更深层次的精神交流。然而，你也需要注意，过度的情感依赖可能会影响你的独立性，有时会有逃避现实的倾向，甚至过度付出。为了维护这段感情，你需要学会更理智地处理。',
                '偏印' => '你追求完美无缺的爱情，深情且专一地对待伴侣。但有时可能表现出恋父情结，导致情感状态迷茫和不安，难以真实表达情感，让对方难以理解。在交往中，你注重隐私，对不完全信任的人会有所保留。你追求身心完全融合的爱情，深爱着对方，但占有欲也较强，期望对方能全心全意对待这段关系。有时你们可能难以深入交流，影响情感进展。建议你与伴侣多沟通，尝试给予更多的信任。',
                '正财' => '你以保守、忠诚和慎重的态度对待感情。作为感情的守护者，你专一且愿意付出耐心来经营关系，体贴关心对方，细心留意其想法。你注重感情的平稳发展和物质生活的保障，喜欢在感情中占主导地位，不喜欢太多的变数。虽然你的思维方式有些传统，甚至有点木讷，但你的真心、本分、强烈的责任感和对伴侣的忠诚，让你成为一个能给对方安全感的恋人。',
                '偏财' => '在感情中，你浪漫而慷慨，总是毫不保留地表达自己的情感，以伴侣为荣，愿意为对方付出。你的惊喜和快乐常常能给对方带来愉悦。然而，你也有一定的控制欲，想成为关系中的主导者，这可能会影响你们感情的深度发展。为了改善这一点，你需要注意平衡自己的控制欲和对方的需求。',
                '食神' => '你在感情中表现得含蓄保守，但内心情感丰富且多情。你可能经历过非凡的恋爱历程，对感情有着理想化的追求。如果遇到灵魂伴侣，你会非常专一；否则，可能会感到孤独。你注重感情生活的情趣，大多数时候能够体贴、理解对方。',
                '伤官' => '你对感情和伴侣有较高的要求，不受传统观念束缚，追求完美的感情生活。你的恋爱性格脱俗，颇有情调，能以真心相待并为对方创造浪漫回忆。然而，你有时情绪化，可能表现出自我中心的一面，对伴侣忽冷忽热，影响感情稳定性。你需要学会控制情绪化的行为并更加关注伴侣的需求以保持感情的稳定。',
                '比肩' => '在感情中，你强调双方的平等和独立性。在恋爱中可能遇到矛盾和争吵，难以建立融洽的关系。你可能有一些自恋倾向，更喜欢与观念一致或个性鲜明的人交往。在生活中，你可能无法很好地照顾到伴侣的需求。虽然你尊重对方的想法但表达方式直接、自我且不易让步这可能会影响感情的和谐度。为了改善这种情况你需要学会更加关注伴侣的需求并尝试以更柔和的方式表达自己的想法。',
                '劫财' => '你认为双方的感情应保持独立、平等的关系。在选择伴侣时，你可能更倾向于选择背景和思想相似的人。在感情中，你希望拥有自己的私人空间并可能表现出矛盾的一面。你有一定的控制欲对待伴侣忽冷忽热有时会忽略对方的感受这可能会影响双方感情的稳定度。为了保持感情的稳定你需要注意调节自己的行为和态度更加关注伴侣的感受并给予对方足够的关注和支持。',
                '正官' => '你以慎重而保守的态度对待感情，可靠而有担当，愿意为伴侣付出并具有很强的忍耐力能给对方带来安全感。同时你也希望伴侣能与你并肩同行共同面对生活中的挑战。然而你也有一定的占有欲并可能不自觉地想要控制对方在感情生活中可能缺乏浪漫和仪式感。为了改善这种情况你需要注意调整自己的行为和态度更加注重浪漫和仪式感以满足伴侣的需求并增进双方的感情。',
                '七杀' => '你希望恋爱充满浪漫和激情，在感情中富有情调、有男子气概、负责且有担当，对伴侣有保护欲。但在双方关系中，你可能显得有些强势，有控制伴侣的倾向，脾气不太好。与另一半相处时，可能会发生争吵，且往往不会轻易妥协或做出改变，这可能会给感情带来影响。建议你注意调整自己的状态和态度，学会更加平和地处理矛盾并尊重伴侣的想法和需求。',
            ];
            $result[] = $list[$godZd];
            // 男命八字同时出现正财和偏财
            if ($godTzs['zc'] > 0 && $godTzs['pc'] > 0) {
                $result[] = '你的八字中同时拥有正财和偏财，这使得你在感情方面有着较好的异性缘。然而，由于你心思不定，容易与其他异性产生纠葛，导致婚姻中出现第三者的问题。因此，你需要学会控制自己的情绪，避免因情感问题而影响婚姻稳定。';
            }
            // 命局劫财＞3，且命局官星和印星相加数量≤3（看藏干）
            if ($godThs['jc'] > 3 && ($godThs['zg'] + $godThs['qs'] + $godThs['zy'] + $godThs['py']) <= 3) {
                $result[] = '你的劫财星比较重，这意味着你有多次恋爱经历的可能性较大。然而，晚婚的情况可能会使你错过适婚年龄，婚后也容易出现冷战与争吵等负面情况。因此，你需要学会控制自己的情绪，并适当调整自己的心态，以保持婚姻的和谐。';
            }
            // 命局偏财>2且无正财
            if ($godTzs['pc'] > 2 && $godTzs['zc'] == 0) {
                $result[] = '你的八字中没有正财星，只有偏财星，这表明你对家庭观念较低，天性风流的一面较强。此外，你的身边可能会有一些情人存在，这可能会对你的婚姻造成一定的影响。因此，你需要学会控制自己的欲望和行为，以避免对婚姻造成不良后果。';
            }
            // 日柱为丁酉、丁亥、癸巳、癸卯其中一个
            if (in_array($dgz, ['丁酉', '丁亥', '癸巳', '癸卯'])) {
                $result[] = '你的配偶是你的贵人，能够为你带来较大的帮助和助力。你外表俊秀、聪明且比较斯文，说话做事干脆利落，具有谦虚有仁德的特点。在你的感情生活中，你需要珍惜这段关系，用心经营和维护它。';
            }
            // 原局只有一个正财或者偏财
            if (($godTzs['zc'] + $godTzs['pc']) == 1) {
                $result[] = '你对待感情比较专一，注重家庭生活，即使可能有情人也不会抛弃家庭。你的感情状况相对稳定，一般不会出现离婚等情况。在感情生活中，你需要保持忠诚和稳定的伴侣关系，不要做出背叛对方的行为。';
            }
        }
        return $result;
    }

    /**
     * 理想伴侣
     * @return string[]
     */
    protected function getLiXianBanlv(): array
    {
        // 理想伴侣 日支
        $list = [
            [
                '正印' => '在选择伴侣时，你更倾向于那些具有成熟魅力的异性，期望他们重视家庭，并在生活中迁就和照顾你。但同时，你也应尊重彼此的个人空间，加强沟通，倾听对方的声音，避免过度的关爱造成负担。',
                '偏印' => '你希望伴侣能以自己为中心，给予顺从和支持，但在此过程中，也要尊重双方的独立性，积极听取对方的意见，并为彼此留出适当的个人空间。',
                '正官' => '你追求的是稳定的婚姻关系，因此你希望伴侣具有强烈的责任感，拥有良好的家庭背景，能够胜任处理家庭琐事和扮演贤内助的角色。同时，你也会努力履行自己作为丈夫的职责。',
                '七杀' => '你理想的伴侣是能与你并肩同行的人，你希望他/她拥有独立的个性，不过分依赖你，能够自己有所作为，共同承担生活的责任和挑战。',
                '正财' => '你期待伴侣能成为你的贤内助，勤俭持家并对家庭有责任感。同时，你也希望他/她性格开朗、豁达，能够带给你乐观的生活态度和积极的能量。',
                '偏财' => '你理想中的伴侣应具备出众的外貌，能够顺从你的想法，拥有良好的家庭条件，并能带给你成就感。尽管你可能在某些时候表现得不够专一，但你仍希望他/她在这段感情中保持忠诚。',
                '食神' => '你希望伴侣是一个深情而浪漫的人，懂得生活情趣，能与你一同创造婚姻生活中的惊喜与美好，共同营造温馨的家庭氛围。',
                '伤官' => '作为一个浪漫多情的人，你追求的是和谐稳定的婚姻家庭。因此，你理想中的伴侣应具备善解人意、大方得体的特质，同时对你和你的家庭有一定的责任感。',
                '比肩' => '你希望伴侣拥有独立的个性，能够与你齐心协力、并肩同行，在面对困难时能够共同承担，分享成功的喜悦和生活的点滴。',
                '劫财' => '你的理想伴侣应具备独立自主的性格，能够与你并肩同行、共同解决问题、共担风雨。你们将一起品味生活的酸甜苦辣，共同创造美好的未来。',
            ],
            [
                '正印' => '你期望伴侣展现成熟稳重的一面，能在生活中迁就和照顾你，为家庭无私奉献。但请记住，保持适度的关爱，良好的沟通，并为彼此保留一些私人空间。',
                '偏印' => '你希望在双方关系中占据主导，伴侣能顺从你的想法，给予你支持，并有一定的执着。但在与伴侣相处时，请确保保持开放的沟通，尊重对方的独立个性，并共同维护一些私人空间。',
                '正官' => '你渴望稳定的婚姻生活，希望伴侣具有强烈的责任感和威望，能够为你带来好名声和地位上的保障。',
                '七杀' => '你更倾向于风趣幽默、才华横溢且有责任心的伴侣，期望他/她在社会中有一定的地位和权威。',
                '正财' => '你期待遇到一个能够保护你、给你安全感的良人，他/她能够尊重并听从你的意见。',
                '偏财' => '你希望伴侣能够顺从你，拥有足够的经济实力来满足你的日常需求。尽管你可能在某些时候表现出花心的一面，但你仍期望他/她对你保持忠诚和深情。',
                '食神' => '你向往热烈而深情的婚姻生活，希望伴侣能够带给你浪漫和注重生活仪式感的体验，共同创造美好的回忆。',
                '伤官' => '你理想中的伴侣应该是多情且才华横溢的，你期待他/她能够充分发挥自己的才能，并在事业上取得一定的成就。',
                '比肩' => '你希望伴侣在事业上能够取得成功，拥有强大的经济能力、出色的行动力和判断力，同时具备开拓创新的精神。',
                '劫财' => '你期望伴侣具备强大的行动力和判断力，能够在关键时刻果断做出决策，具有开拓创新的精神，并在事业上取得显著成就，为婚姻提供稳固的经济基础。',
            ],
        ];
        $godZNand = $this->userInfo[0]['godz']['day'];
        $godZNvd = $this->userInfo[1]['godz']['day'];
        return [
            'nan' => $list[0][$godZNand] ?? '',
            'nv' => $list[1][$godZNvd] ?? '',
        ];
    }

    /**
     * 旺夫旺妻
     * @param int $sex 性别 0男 1女
     * @return string
     */
    protected function getWangFuQi(int $sex): string
    {
        $info = $this->userInfo[$sex];
        $xy = $info['xy'];
        $wangDu = $info['wangdu'];
        $jiNian = $info['base']['jinian'];
        $jnWx = $info['jnwx'];
        $dzArr = array_column($jiNian, 1);
        $dgz = implode('', $jiNian['d']);
        $mdz = $jiNian['m'][1];
        $hdz = $jiNian['h'][1];
        $ddz = $jiNian['d'][1];
        $shenShaRes = $this->shaShen->detail($jiNian, $sex);
        $godTzs = $info['god_tzs'];
        $godThs = $info['god_ths'];
        $godT = $info['god'];
        $godZ = $info['godz'];
        $godHide = $info['_god'];
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $result = [];
        if ($sex) {
            // 身旺格，日支为正官或七杀为喜用神
            if ($wangDu == '身旺格') {
                if (in_array($godZ['day'], ['正官', '七杀']) && in_array('官杀', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                    $result[] = '身旺，日坐官星为喜用代表着旺夫运强，这样命格的女性，事业运都会较为不错，且事业能给自己带来不少的财富，丈夫也会因此得到自身的资助。';
                }
            }
            // 日支为正财或偏财，且为喜用神
            if (in_array($godZ['day'], ['正财', '偏财']) && in_array('才财', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $result[] = '日坐官星为喜用神意味着旺夫运强，在结婚后自身会得到丈夫的支持和帮助，使得事业和财运都蒸蒸日上。这类命格的女性，在婚后通常都会支持丈夫，帮助丈夫的事业，助旺丈夫的运势。';
            }
            // 日柱坐下的神煞为天乙贵人
            if (in_array('天乙贵人', $shenShaRes['d'])) {
                $result[] = '日坐天乙贵人意味着旺夫运强，这类命格的女性结婚后善于管理家庭，愿意将所有的心思都放在家庭上，并且在各方面都能够为丈夫排忧解难，家庭生活会比较和谐美满。';
            }
            // 日坐偏财，且正官或偏官为用神
            if ($godZ['day'] == '偏财' && $xy['shen']['yong'] = '官杀') {
                $result[] = '命理学认为，如果日主偏强、财官为用，那么这类女性通常比较自信独立，积极乐观，能够给丈夫带来积极的帮助和影响。她们善于打理家庭中的琐事，愿意将心思放在家庭上，从而助旺丈夫的运势。';
            }
            if ($godZ['day'] == '正官') {
                $result[] = '日坐正官的女性有着较强的责任心，善于处理家庭事务，愿意为自己的丈夫分忧解难。这种性格特质能够帮助她们在事业上取得成功，让丈夫没有后顾之忧。';
            } elseif ($godZ['day'] == '食神') {
                $result[] = '日坐食神的女性一生财运旺盛，物质生活比较富足。结婚后，她们也能够给丈夫带来财富和运气，助旺丈夫的运势。';
            }
            // 只有一个正官或一个七杀（看藏干）
            if (($godThs['zg'] + $godThs['qs']) == 1) {
                $result[] = '夫星纯正不杂意味着女性的性格温柔贤惠，对待家庭富有责任心，追求幸福和睦的家庭。她们事事都与丈夫同心同力，在各个方面帮助丈夫。';
            }
            // 天干同时有正财，正官，正印
            $arr1 = array_intersect(['正财', '正官', '正印'], $godT);
            if (count($arr1) == 3) {
                $result[] = '财官印俱全的女性比较温柔亲切，善解人意，善于沟通。她们会让丈夫在日常的生活中感觉温暖和舒适。此外，这类女性对待丈夫和家庭都富有责任心，在各个方面都能够对丈夫有所帮助。';
            }
            // 天干正官或七杀对应的五行被月支生扶
            $wxM1 = $jnWx['wx']['m'][1];
            foreach ($godT as $k => $v) {
                if (in_array($v, ['正官', '七杀'])) {
                    $strK = substr($k, 0, 1);
                    $wx = $jnWx['wx'][$strK][0];
                    if (in_array(BaziCommon::getWuxingGuanXi($wxM1, $wx), ['生', '扶'])) {
                        $result[] = '官星得令的女性心地善良，诚实守信，人缘也较为不错。在结婚后，也都以家庭为重，愿意将心思花在家庭上面，对家人和自己负责。这样为人处事的风格也会给丈夫带来积极影响。';
                        break;
                    }
                }
            }
            // 天干有财星，官星以及印星
            if (array_intersect(['正财', '偏财'], $godT) && array_intersect(['正官', '七杀'], $godT) && array_intersect(['正印', '偏印'], $godT)) {
                $result[] = '八字财、官、印俱现意味着旺夫运强，这类命格的女性通常温柔贤惠、平易近人、富有责任心。在结婚后，她们愿意将自己的重心放在家庭上，在各个方面都能够对丈夫有所帮助。';
            }
            // 正官数量大于伤官数量
            if ($godTzs['zg'] > $godTzs['sg']) {
                $result[] = '官强伤弱意味着旺夫运好，这类命格的女性心思细腻，性格比较独立，不依赖他人，为人处事都有自己的主见。在结婚后，能助旺自己的家庭，对丈夫的事业和财运都有所帮助。';
            }
            // 调用格局判断，身旺且原局无正官和七杀
            if ($wangDu == '身旺格' && ($godTzs['zg'] + $godTzs['qs']) == 0) {
                $result[] = '身旺无官杀意味着旺夫运一般，这类命格的女性对婚后生活的要求较高，结婚后可能会和丈夫因为意见不合而发生争吵。婚姻生活中，应多注意体贴对方，互相理解才能够保持幸福。';
            }
            // 地支包含辰戌丑未
            $arr1 = array_intersect(['辰', '戌', '丑', '未'], $dzArr);
            if (count($arr1) == 4) {
                $result[] = '辰戌丑未全意味着旺夫运一般，这类命格的人外貌出众，性格随和，交际广泛，有不少的异性朋友，所以在结婚后会让丈夫有危机感，长此以往就会导致矛盾的发生。建议在结婚后要与异性保持一定的距离，让伴侣时刻感受到自己的爱。';
            }
            // 原局伤官数量大于等于三
            if ($godTzs['sg'] >= 3) {
                $result[] = '八字伤官旺意味着旺夫运一般，这类命格的女性比较自我，在感情中经常会忽略对方的想法，过于固执己见则会导致双发发生矛盾。建议在婚姻生活中，要时刻关注对方的需求，遇到问题学会换位思考，这样才能更加和睦。';
            }
            // 日支和时支存在相刑
            if (BaziCommon::getXianXin($ddz . $hdz)) {
                $result[] = '日时支相刑意味着旺夫运不太理想，这类命格的女性追求完美的婚姻生活，结婚后可能因为一些方面达不到自己的要求，导致和丈夫发生矛盾。建议在婚姻生活里，多体谅对方，脚踏实地的去追求幸福生活。';
            }
            // 地支同时存在卯酉
            if (in_array('卯', $dzArr) && in_array('酉', $dzArr)) {
                $result[] = '这类命格的女性，旺夫运不太理想的女性，婚后可能过于依赖对方，对不满的事情容易责备对方，引发夫妻矛盾。互相理解和知足能让婚姻更幸福。';
            }
            // 日柱为甲寅  日柱为戊申  日柱为甲辰或甲戌
            switch ($dgz) {
                case '甲寅':
                    $result[] = '日柱为甲寅的女性旺夫运不佳，婚后可能因固执己见而与丈夫争吵、冷战。学会在婚姻中示弱，换位思考能增进甜蜜和幸福。';
                    break;
                case '戊申':
                    $result[] = '日柱为戊申的女性旺夫运一般，对家庭生活期望高，可能因丈夫未达期望而产生矛盾。多站在对方角度思考能让婚姻更美满。';
                    break;
                case '甲辰':
                case '甲戌':
                    $result[] = '这类命格的女性，旺夫运普通，对婚姻有高要求，容易钻牛角尖，影响夫妻和睦。婚后要知足，多为对方考虑，换位思考能让婚姻更幸福。';
                    break;
            }
            // 用神为印枭，喜神为官杀
            if ($xy['shen']['yong'] == '印枭' && $xy['shen']['xi'] == '官杀') {
                $result[] = '印星为用神，官星为喜神的女性旺夫运强，温柔体贴，善于持家，婚后以家庭为重，是丈夫的得力助手。';
            }
            // 伤官数量大于等于4(看藏干)
            if ($godThs['sg'] >= 4) {
                $result[] = '伤官重重的女性旺夫运欠佳，对婚姻期望高，可能因不满现状而与丈夫争吵，影响夫妻和睦。互相体谅和换位思考能让婚姻更美好。';
            }
            // 日支和时支相冲
            if (BaziCommon::getXianChong($ddz . $hdz)) {
                $result[] = '日时相冲的女性旺夫运不理想，婚后可能过于自私，忽视丈夫感受，导致夫妻不和。互相体谅和站在对方角度思考能增进婚姻美满。';
            }
            $terrain = $this->nv->getTerrain();
            // 日支为官星，且坐下神煞为死绝墓
            if (in_array($godZ['day'], ['正官', '七杀']) && in_array($terrain['day'], ['死', '绝', '墓'])) {
                $result[] = '这类命格的女性对伴侣和婚姻的要求比较高，结婚后可能因为各方面达不到自己的要求而和伴侣发生争执，甚至会让伴侣感觉难堪。建议结婚后要注重实际，懂得知足，多体谅丈夫，这样才能有和谐美满的婚姻。';
            }
            // 日支为伤官
            if ($godZ['day'] == '伤官') {
                $result[] = '这类命格的女性大多对婚姻的期待较高，并且追求完美，结婚后可能会发现很多事情并不是自己想象中的那样，因为达不到预期，所以容易和丈夫发生一些争执。若是能够懂得知足，互相体谅，婚后生活则会越来越好。';
            }
            // 伤宫为喜用神，且八字中伤官带财，而不见官星的命格
            if (in_array('食伤', [$xy['shen']['yong'], $xy['shen']['xi']])) {
                foreach ($godT as $k => $v) {
                    $arr1 = [$v, $godZ[$k]];
                    $arr2 = array_intersect(['正财', '偏财'], $arr1);
                    if (in_array('伤官', $arr1) && !empty($arr2)) {
                        $result[] = '这类命格的女性大多温柔贤惠，善于照顾家庭，能帮助自己的丈夫解决很多烦恼，助旺丈夫的运势。她们对待感情比较细腻，愿意为家庭付出，是理想的妻子人选。';
                        break;
                    }
                }
            }
            // 日禄归时(日主天干为甲，生在寅时，时支的寅字就是甲木天干的禄神)
            if ($jiNian['d'][0] == '甲' && $jiNian['h'][0] == '寅') {
                $result[] = '这类命格的女性一生运势都比较好，婚后能助旺丈夫的运势，在事业和财富上都能够帮助丈夫，且多半能生贵子，婚姻生活比较幸福美满。不过需要注意的是，虽然她们有着不错的运势，但也需要夫妻之间相互理解和支持，才能让婚姻更加稳定和幸福。';
            }
            if (empty($result)) {
                $result[] = '你的旺夫程度一般，你对家庭的态度可能比较平淡，对待另一半有时会有些冷淡，这可能会给双方关系带来一定影响。建议你多关注伴侣的需求和感受，增强彼此之间的沟通和互动，以增进夫妻间的感情。';
            }
        } else {
            // 日柱坐下的神煞为天乙贵人
            if (in_array('天乙贵人', $shenShaRes['d'])) {
                $result[] = '日坐贵人命格的男命有着极好的运气，特别是在结婚后可能会遇到不少贵人的帮助，也能让妻子的运势不断上升，助旺妻子的运势。他们通常非常顾家和关心家人，是值得信赖和依靠的对象。';
            }
            // 日支为正财或偏财，且为喜用神
            if (in_array($godZ['day'], ['正财', '偏财']) && in_array('才财', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $result[] = '日坐正财为喜用神代表着旺妻运强，在结婚后自身会得到妻子或者妻家的鼎力相助，无论是事业还是人脉都能得到开拓和发展。这类命格的男性，在婚后通常都会支持妻子，帮助妻子的事业，助旺妻子的运势。他们非常重视家庭的稳定和经济状况的改善。';
            }
            // 只有一个正财或一个偏财（看藏干）
            if (($godThs['zc'] + $godThs['pc']) == 1) {
                $result[] = '正偏财不杂意味着旺妻运强，这类命格的男性对妻子非常专一，对家庭富有责任心。他们通常非常勤奋努力工作赚钱养家糊口，同时也会尽力维护家庭的和睦与稳定。因此他们的妻子可以享受到安稳富足的生活。';
            }
            // 日支为食伤或伤官
            if (in_array($godZ['day'], ['食神', '伤官'])) {
                $result[] = '日坐食伤意味着旺妻运好，这类命格的男性心思都比较细腻，在感情方面也非常注重细节。在婚后更愿意将心思花在家庭上，对妻子的各方面都呵护到位。他们通常非常顾家和关心家人，是理想的伴侣选择。';
            }
            // 原局中正偏印数量大于等于三个
            if (($godTzs['zy'] + $godTzs['py']) >= 3) {
                $result[] = "印多意味着旺妻运一般，这类命格的男性在结婚后经常会因为不够体贴妻子而导致妻子伤心难过。建议夫妻之间应多站在对方的立场去思考问题，这样才能更加和睦地相处。";
            }
            // 地支包含辰戌丑未
            $tmp1 = array_intersect(['辰', '戌', '丑', '未'], $dzArr);
            if (count($tmp1) == 4) {
                $result[] = '辰戌丑未全意味着旺妻运一般，这类命格的人交友广泛，人缘较好，有不少的异性朋友。建议在结婚后要和异性保持距离，对待伴侣一心一意。他们比较特立独行，喜欢我行我素，容易忽略妻子的感受，因此会导致婚姻不够和谐。若能够互相体谅，认真倾听对方的建议，婚姻生活才会变得越来越幸福。';
            }
            // 日柱为甲辰或甲戌
            if (in_array($dgz, ['甲辰', '甲戌'])) {
                $result[] = '这类命格的男性，旺妻运一般，在婚姻中比较特立独行。他们责任心较强，愿意将心思花在家庭上，对妻子也十分体贴。但是需要注意的是，他们的固执性格可能会导致夫妻之间产生矛盾。建议在结婚后多为妻子着想，认真倾听妻子的建议和感受，这样才能使婚姻更加稳定。';
            }
            // 正财和偏财为用神，且同柱十二长生有帝旺
            if ($xy['shen']['yong'] == '才财') {
                $terrain = $this->nan->getTerrain();
                $bool = false;
                foreach ($godT as $k => $v) {
                    if (in_array($v, ['正财', '偏财']) || in_array($godZ[$k], ['正财', '偏财'])) {
                        if ($terrain[$k] == '帝旺') {
                            $bool = true;
                            break;
                        }
                    }
                }
                if ($bool) {
                    $result[] = '妻星为喜用，生旺之地，乃旺妻之吉兆。此类男士，责任心甚强，婚后倾心于家，体贴入微，婚姻和谐，幸福美满。';
                }
            }
            // 日支为比劫
            if (in_array($godZ['day'], ['比肩', '劫财'])) {
                $result[] = '日支比劫意味着旺妻运一般，这类命格的男性比较固执。在结婚后可能会比较大男子主义，容易忽略对方的感受。建议在结婚后多关注妻子的需求和感受，多与妻子沟通交流，尊重妻子的意见和想法。这样可以让夫妻之间的相处变得更加和谐美满。';
            }
            // 月支或时支冲日支
            if (BaziCommon::getXianChong($mdz . $ddz) || BaziCommon::getXianChong($hdz . $ddz)) {
                $result[] = '日支受年、月支之冲意味着旺妻运不太理想。这类命格的男性人缘较好，异性朋友多。结婚后可能会因为和异性过于亲密而导致妻子没有安全感。建议在结婚后和异性保持一定的距离，避免让妻子感到不安。同时也要注意自己的言行举止，不要做出任何伤害妻子的事情。';
            }
            // 日支和时支相刑
            if (BaziCommon::getXianXin($ddz . $hdz)) {
                $result[] = '日时相刑意味着旺妻运不太理想。这类命格的男性在感情方面比较粗心大意，容易忽略妻子的需求。若是能更加体贴对方，注重生活中的细节，婚姻生活也会变得更加美好。';
            }
            // 日支和时支相冲
            if (BaziCommon::getXianChong($ddz . $hdz)) {
                $result[] = '日时相冲意味着旺妻运不理想。这类命格的男性性格过于自我，对任何事情都有一种不在乎的态度。结婚后可能对婚姻或伴侣不够重视，导致夫妻关系紧张。建议结婚后多关注家庭，体贴妻子，这样才能让婚姻生活变得温馨。';
            }
            // 原局中正财和偏财数量大于等于三，但是身弱格或从弱格
            if (($godTzs['zc'] + $godTzs['pc']) >= 3 && in_array($wangDu, ['身弱格', '从弱格'])) {
                $result[] = '此类命格男士，感情中易显自私，常忽略伴侣感受，婚后可能因不够体贴而致夫妻不和。建议婚后多关注家庭，体贴妻子，以美满婚姻生活。';
            }
            // 天干有正财且其他天干无偏财且同柱地支无偏财
            if (!in_array('偏财', $godT)) {
                foreach ($godT as $k => $v) {
                    if ($v == '正财' && $godZ[$k] != '偏财') {
                        $result[] = '此命格男性，感情专一，婚后照顾妻子无微不至，给予妻子强烈安全感。总体而言，能让妻子感到安心，具有较强旺妻特质。';
                        break;
                    }
                }
            }
            // 妻宫为食伤
            if (in_array($godZ['day'], ['食神', '伤官'])) {
                $result[] = '这类命格男性，性格温柔善良，心思细腻，婚后体贴妻子，对家庭和妻子负责，能在各方面扶助妻子。';
            }
            // 食神伤官数>=4
            if (($godTzs['ss'] + $godTzs['sg']) >= 4) {
                $result[] = '此类男性，心思细腻，善于捕捉微妙情绪，浪漫满怀，婚后照顾妻子情绪，夫妻情深意切，和睦相处。';
            }
            // 男命身旺，且食神和伤官数>=3或正财偏财数>=3
            if ($wangDu == '身旺格' && ($godTzs['ss'] + $godTzs['sg']) >= 3 && ($godTzs['zc'] + $godTzs['pc']) >= 3) {
                $result[] = '这类此命格男性，风流倜傥，婚后亦难收敛，易惹桃花，或有损家庭和睦。建议收敛心性，专注家庭，以确保婚姻美满。';
            }
            if (empty($result)) {
                $result[] = '旺妻运中等，个性独立，不轻易妥协，亦不迁就对方。追求付出与回报之平衡，若伴侣能互相尊重，平等相待，则家庭氛围融洽。';
            }
        }
        return current($result);
    }

    /**
     * 年柱合分
     * @return float
     */
    protected function getFenByYear(): float
    {
        $infoNan = $this->userInfo[0];
        $infoNv = $this->userInfo[1];
        $jiNianNan = $infoNan['base']['jinian'];
        $jiNianNv = $infoNv['base']['jinian'];
        $xyNan = $infoNan['xy'];
        $xyNv = $infoNv['xy'];
        $gxNan = $infoNan['gx'];
        $gxNv = $infoNv['gx'];
        $yongXiNan = [$xyNan['xy']['yong'], $xyNan['xy']['xi']];
        $yongXiNv = [$xyNv['xy']['yong'], $xyNv['xy']['xi']];
        $wxAttr = WuXing::GZ_TO_WX;
        $ytgNan = $jiNianNan['y'][0];
        $ytgNanWx = $wxAttr[$ytgNan];
        $ytgNv = $jiNianNv['y'][0];
        $ytgFWx = $wxAttr[$ytgNv];
        $he = BaziCommon::getHehua($ytgNan . $ytgNv);
        $chong = BaziCommon::getXianChong($ytgNan . $ytgNv);
        $ke = BaziCommon::getXianKe($ytgNan . $ytgNv);
        $shen = BaziCommon::getWuxingGuanXi($ytgNanWx, $ytgFWx);
        $shen1 = BaziCommon::getWuxingGuanXi($ytgFWx, $ytgNanWx);
        $ckNan = array_intersect(['冲', '被克'], $gxNan['y']['t']) ? 1 : 0;
        $ckNv = array_intersect(['冲', '被克'], $gxNv['y']['t']) ? 1 : 0;
        if ($ckNan && $ckNv) {
            $num3 = 2;
        } elseif (!$ckNan && !$ckNv) {
            $num3 = 1;
        } elseif ($ckNan && !$ckNv) {
            $num3 = 3;
        } else {
            $num3 = 4;
        }
        // 天干  相合 1 相冲 2 相克 3 相生 4 无关系 5
        // 天干 为喜用  1 都是忌 2 男喜用 女忌 3 男忌 女喜用 4
        // 不被他柱 冲克 1 都被冲克 2 男冲克 女无 3 男无 女冲克 4
        $listTg = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 2, 124 => 2, 131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 2, 23 => 1, 24 => 1, 31 => 0, 32 => 2, 33 => 1, 34 => 1,
            411 => 5, 412 => 2, 413 => 3, 414 => 3, 421 => 0, 422 => 4, 423 => 3, 424 => 3, 431 => 3, 432 => 3, 433 => 1, 434 => 4, 441 => 3, 442 => 3, 443 => 4, 444 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        // 地支  相合 1 相冲 2 相破 3 相刑 4 相害 5 生地 6 墓地 7 其它 8
        $listDz = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 3, 124 => 3, 131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 1, 23 => 0.5, 24 => 0.5, 31 => 1, 32 => 2, 33 => 1.5, 34 => 1.5, 41 => 0.5, 42 => 1.5, 43 => 1, 44 => 1, 51 => 0, 52 => 0.5, 53 => 0.25, 54 => 0.25,
            611 => 5, 612 => 2, 613 => 3, 614 => 3, 621 => 0, 622 => 4, 623 => 3, 624 => 3, 631 => 3, 632 => 3, 633 => 1, 634 => 4, 641 => 3, 642 => 3, 643 => 4, 644 => 1,
            711 => 5, 712 => 2, 713 => 3, 714 => 3, 721 => 0, 722 => 4, 723 => 3, 724 => 3, 731 => 3, 732 => 3, 733 => 1, 734 => 4, 741 => 3, 742 => 3, 743 => 4, 744 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        $b11 = in_array($ytgNanWx, $yongXiNv) ? 1 : 2;
        $b12 = in_array($ytgFWx, $yongXiNan) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b11 . $b12];
        if ($he) {
            $num2 = $this->getGx3($he);
            $str = (int)("1{$num2}{$num3}");
        } elseif ($chong) {
            $str = (int)("2{$num4}");
        } elseif ($ke) {
            $str = (int)("3{$num4}");
        } elseif ($shen == '生' || $shen1 == '生') {
            $shenTg = $shen ? $ytgFWx : $ytgNanWx;
            $num2 = $this->getGx3($shenTg);
            $str = (int)("4{$num2}{$num3}");
        } else {
            $str = (int)("8{$num4}");
        }
        $fen = $listTg[$str] ?? 3;
        $ydzNan = $jiNianNan['y'][1];
        $ydzNv = $jiNianNv['y'][1];
        $ydzNanWx = $wxAttr[$ydzNan];
        $ydzNvWx = $wxAttr[$ydzNv];
        $ckNan = array_intersect(['冲', '被克'], $gxNan['y']['d']) ? 1 : 0;
        $ckNv = array_intersect(['冲', '被克'], $gxNv['y']['d']) ? 1 : 0;
        if ($ckNan && $ckNv) {
            $num3 = 2;
        } elseif (!$ckNan && !$ckNv) {
            $num3 = 1;
        } elseif ($ckNan && !$ckNv) {
            $num3 = 3;
        } else {
            $num3 = 4;
        }
        $heDz = BaziCommon::getHehua($ydzNan . $ydzNv);
        $str2 = 0;
        $b11 = in_array($ydzNanWx, $yongXiNv) ? 1 : 2;
        $b12 = in_array($ydzNvWx, $yongXiNan) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b11 . $b12];
        $sanShen = $this->getSanHeSm($ydzNanWx . $ydzNvWx);
        $sanDi = $this->getSanHeSm($ydzNanWx . $ydzNvWx, 1);
        if ($heDz) {
            $num2 = $this->getGx3($heDz);
            $str2 = (int)("1{$num2}{$num3}");
        } elseif (BaziCommon::getXianChong($ydzNan . $ydzNv)) {
            $str2 = (int)("2{$num4}");
        } elseif (BaziCommon::getXianPo($ydzNan . $ydzNv) || BaziCommon::getXianPo($ydzNv . $ydzNan)) {
            $str2 = (int)("3{$num4}");
        } elseif (BaziCommon::getXianXin($ydzNan . $ydzNv)) {
            $str2 = (int)("4{$num4}");
        } elseif (BaziCommon::getXianHai($ydzNan . $ydzNv)) {
            $str2 = (int)("5{$num4}");
        } elseif ($sanShen) {
            $num2 = $this->getGx3($sanShen);
            $str2 = (int)("6{$num2}{$num3}");
        } elseif ($sanDi) {
            $num2 = $this->getGx3($sanDi);
            $str2 = (int)("7{$num2}{$num3}");
        } else {
            $str2 = (int)("8{$num4}");
        }
        $fen1 = $listDz[$str2] ?? 0;
        $fen += $fen1;
        return $fen;
    }

    /**
     * 属相合婚
     * @return float
     */
    protected function getShuXiangFen(): float
    {
        $list = [
            [6.5, 10, 6.5, 3, 7.5, 6.5, 1, 0, 8, 5, 6.5, 6.5],
            [10, 6.5, 6.5, 6.5, 5, 7, 0, 1, 6.5, 7.5, 3, 6.5],
            [6.5, 6.5, 6.5, 6.5, 6.5, 0, 8, 6.5, 1, 6.5, 7, 10],
            [3, 6.5, 6.5, 6.5, 0, 6.5, 5, 7, 6.5, 1, 10, 8],
            [7.5, 5, 6.5, 0, 3, 6.5, 6.5, 6.5, 7, 10, 1, 6.5],
            [6.5, 7, 0, 6.5, 6.5, 6.5, 6.5, 6.5, 7, 8, 6.5, 1],
            [1, 0, 8, 5, 6.5, 6.5, 3, 10, 6.5, 6.5, 7.5, 6.5],
            [0, 1, 6.5, 7.5, 6.5, 6.5, 10, 6.5, 6.5, 6.5, 5, 7],
            [8, 6.5, 1, 6.5, 7, 10, 6.5, 6.5, 6.5, 6.5, 6.5, 0],
            [5, 7.5, 6.5, 1, 10, 8, 6.5, 6.5, 6.5, 3, 0, 6.5],
            [6.5, 6.5, 7, 10, 1, 6.5, 7.5, 3, 6.5, 0, 6.5, 6.5],
            [6.5, 6.5, 10, 7.5, 6.5, 1, 6.5, 7, 0, 6.5, 6.5, 3],
            [6.5, 6.5, 10, 7.5, 6.5, 1, 6.5, 7, 0, 6.5, 6.5, 3],
        ];
        $ydzNan = $this->userInfo[0]['base']['jinian']['y'][1];
        $ydzNv = $this->userInfo[1]['base']['jinian']['y'][1];
        $dzArr = Calendar::DI_ZHI;
        $ydzNani = (int)array_search($ydzNan, $dzArr);
        $ydzNvi = (int)array_search($ydzNv, $dzArr);
        return $list[$ydzNani][$ydzNvi] ?? 0;
    }

    /**
     * 神煞合分
     * @return int
     */
    protected function getShenShaFen(): int
    {
        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];
        $ydzNan = $jiNianNan['y'][1];
        $ydzNv = $jiNianNv['y'][1];
        $mdzNan = $jiNianNan['m'][1];
        $mdzNv = $jiNianNv['m'][1];
        $dtgNan = $jiNianNan['d'][0];
        $dgzNan = implode('', $jiNianNan['d']);
        $dgzNan = implode('', $jiNianNv['d']);
        $hgzNan = implode('', $jiNianNan['h']);
        $ssNan = $this->shaShen->detail($jiNianNan, 0);
        $ssNv = $this->shaShen->detail($jiNianNv, 1);
        $ssNan1 = array_merge($ssNan['y'], $ssNan['m'], $ssNan['d'], $ssNan['h']);
        $ssNv1 = array_merge($ssNv['y'], $ssNv['m'], $ssNv['d'], $ssNv['h']);
        $fen = 10;
        if (in_array('孤辰', $ssNan1)) {
            $fen--;
        }
        if (in_array('寡宿', $ssNv1)) {
            $fen--;
        }
        if (in_array('孤鸾煞', $ssNan1)) {
            $fen = $fen - 2;
        }
        if (in_array('孤鸾煞', $ssNv1)) {
            $fen = $fen - 2;
        }
        if ($this->checkBaBiSha($ydzNan . $mdzNan)) {
            $fen--;
        }
        if ($this->checkBaBiSha($ydzNv . $mdzNv)) {
            $fen = $fen - 5;
        }
        $naYinNv = $this->userInfo[1]['na_yin'];
        $ynyFWx = mb_substr($naYinNv['year'], 2, 1);
        if ($this->checkWangMenGua($ynyFWx . $mdzNv)) {
            $fen = $fen - 3;
        }
        if ($this->checkGuShuiPo($ydzNan . $mdzNan, 0)) {
            $fen = $fen - 2;
        }
        if ($this->checkGuShuiPo($ydzNv . $mdzNv, 1)) {
            $fen = $fen - 2;
        }
        if ($this->checkTieSaoZhou($ydzNan . $mdzNan, 0)) {
            $fen--;
        }
        if ($this->checkTieSaoZhou($ydzNv . $mdzNv, 1)) {
            $fen--;
        }
        if ($this->checkTianSaoXin($ydzNan, $dgzNan) || $this->checkTianSaoXin($ydzNan, $hgzNan)) {
            $fen = $fen - 3;
        }
        $wxAttr = WuXing::GZ_TO_WX;
        $dtgWxNv = $wxAttr[$dtgNan];
        if ($this->checkDiSaoXin($dtgWxNv, $mdzNv)) {
            $fen = $fen - 3;
        }
        if (in_array('阴差阳错', $ssNan1)) {
            $fen--;
        }
        if (in_array('阴差阳错', $ssNv1)) {
            $fen--;
        }
        if (in_array('红艳', $ssNan1)) {
            $fen--;
        }
        if (in_array('红艳', $ssNv1)) {
            $fen--;
        }
        if (in_array('羊刃', $ssNan1)) {
            $fen -= 4;
        }
        if (in_array('羊刃', $ssNv1)) {
            $fen -= 4;
        }
        if (in_array('元辰', $ssNan1)) {
            $fen--;
        }
        if (in_array('元辰', $ssNv1)) {
            $fen--;
        }
        if (in_array('咸池', $ssNan1)) {
            $fen--;
        }
        if (in_array('咸池', $ssNv1)) {
            $fen -= 3;
        }
        if ($fen < 1) {
            $fen = 1;
        }
        return $fen;
    }

    /**
     * 六亲合婚
     * @return int
     */
    protected function liuQingFen(): int
    {
        $godThsNan = $this->userInfo[0]['god_ths'];
        $godThsNv = $this->userInfo[1]['god_ths'];
        $godTNan = $this->userInfo[0]['god'];
        $godHideNan = $this->userInfo[0]['_god'];
        $godTNv = $this->userInfo[1]['god'];
        $godHideNv = $this->userInfo[1]['_god'];
        $godHNan = array_merge($godHideNan['year']['god'], $godHideNan['month']['god'], $godHideNan['day']['god'], $godHideNan['hour']['god']);
        $godHNv = array_merge($godHideNv['year']['god'], $godHideNv['month']['god'], $godHideNv['day']['god'], $godHideNv['hour']['god']);
        if (($godThsNan['bj'] + $godThsNan['jc']) >= 3 && ($godThsNan['zg'] + $godThsNan['qs']) <= 1) {
            $fen = -9;
        } elseif (($godThsNan['bj'] + $godThsNan['jc']) >= 3 && ($godThsNan['zg'] + $godThsNan['qs']) >= 3) {
            $fen = -5;
        } elseif (($godThsNan['ss'] + $godThsNan['sg']) >= 3 && ($godThsNan['zy'] + $godThsNan['py']) <= 1) {
            $fen = 0;
        } elseif (($godThsNan['ss'] + $godThsNan['sg']) >= 3 && ($godThsNan['zy'] + $godThsNan['py']) >= 3) {
            $fen = -2;
        } elseif (in_array('偏财', $godTNan) && in_array('偏财', $godHNan)) {
            $fen = -4;
        } else {
            $fen = -2;
        }
        if (($godThsNv['ss'] + $godThsNv['sg']) >= 3 && ($godThsNv['zy'] + $godThsNv['py']) <= 1) {
            $fen1 = -9;
        } elseif (($godThsNv['ss'] + $godThsNv['sg']) >= 3 && ($godThsNv['zy'] + $godThsNv['py']) >= 3) {
            $fen1 = -5;
        } elseif (($godThsNv['zc'] + $godThsNv['pc']) >= 3 && ($godThsNv['bj'] + $godThsNv['jc']) <= 1) {
            $fen1 = 0;
        } elseif (($godThsNv['zc'] + $godThsNv['pc']) >= 3 && ($godThsNv['bj'] + $godThsNv['jc']) >= 3) {
            $fen1 = 0;
        } elseif (array_intersect(['正官', '七杀'], $godTNv) && array_intersect(['正官', '七杀'], $godHNv)) {
            $fen1 = -4;
        } else {
            $fen1 = -2;
        }
        $fen += $fen1;
        $fen = 10 + $fen;
        if ($fen < 1) {
            $fen = 1;
        }
        return $fen;
    }

    /**
     * 喜用神分数
     * @return float
     */
    protected function getXiYongFen(): float
    {
        $xyNan = $this->userInfo[0]['xy'];
        $xyNv = $this->userInfo[1]['xy'];
        $jnWxNan = array_column($this->userInfo[0]['jnwx']['num'], 'num', 'wx');
        $jnWxNv = array_column($this->userInfo[1]['jnwx']['num'], 'num', 'wx');
        arsort($jnWxNan);
        arsort($jnWxNv);
        $jnNan1 = $this->userInfo[0]['jnwx']['wx'];
        $jnNv1 = $this->userInfo[1]['jnwx']['wx'];
        $wxNan = array_keys($jnWxNan);
        $wxNv = array_keys($jnWxNv);
        $wxNumNan = array_values($jnWxNan);
        $wxNumNv = array_values($jnWxNv);
        if ($wxNumNan[0] >= 3 && in_array(WuXing::getWuxingGuanXi($jnNan1['m'][1], $wxNan[0]), ['生', '扶'])) {
            $list = ['木木' => 10, '木水' => 8, '水水' => 10, '水金' => 8, '金金' => 10, '金土' => 8, '火木' => 8, '火火' => 10, '土土' => 10, '土火' => 8];
            $str = $xyNan['xy']['yong'] . $wxNan[0];
            if (isset($list[$str])) {
                return $list[$str];
            }
        }
        if ($wxNumNv[0] >= 3 && in_array(WuXing::getWuxingGuanXi($jnNv1['m'][1], $wxNv[0]), ['生', '扶'])) {
            $list = ['木木' => 10, '木水' => 8, '水水' => 10, '水金' => 8, '金金' => 10, '金土' => 8, '火木' => 8, '火火' => 10, '土土' => 10, '土火' => 8];
            $str = $xyNan['xy']['yong'] . $wxNan[0];
            if (isset($list[$str])) {
                return $list[$str];
            }
        }
        if (WuXing::getWuxingGuanXi($xyNan['xy']['yong'], $xyNv['xy']['yong']) == '生') {
            $fen = 5;
        } elseif ($xyNan['shen']['yong'] == $xyNv['shen']['xi']) {
            $fen = 4.5;
        } elseif ($xyNan['shen']['yong'] == $xyNv['shen']['yong']) {
            $fen = 4;
        } elseif ($xyNan['shen']['ji'] == $xyNv['shen']['yong']) {
            $fen = 3.5;
        } elseif (WuXing::getWuxingGuanXi($xyNan['xy']['ji'], $xyNv['xy']['yong']) == '克') {
            $fen = 3;
        } elseif (in_array(WuXing::getWuxingGuanXi($xyNan['xy']['ji'], $xyNv['xy']['yong']), ['生', '扶'])) {
            $fen = 2;
        } else {
            $fen = 3.5;
        }
        $list1 = [
            '泄泄' => 5, '扶泄' => 4.5, '生泄' => 4, '克泄' => 2.5, '泄扶' => 3.5, '扶扶' => 4, '生扶' => 3.5, '克扶' => 2.5,
            '泄生' => 3, '扶生' => 3.5, '生生' => 3, '克生' => 2.5, '泄克' => 3.5, '扶克' => 2.5, '生克' => 2, '克克' => 2,
        ];
        $str = WuXing::getWuxingGuanXi($xyNan['xy']['yong'], $xyNv['xy']['yong']) . WuXing::getWuxingGuanXi($xyNan['xy']['xi'], $xyNv['xy']['xi']);
        $fen1 = $list1[$str] ?? 0;
        return $fen + $fen1;
    }

    /**
     * 纳音合婚分数
     * @return float
     */
    protected function getNaYinFen(): float
    {
        $list = [
            '桑柘木剑锋金' => 4, '海中金覆灯火' => 7.5, '天上火涧下水' => 8.5, '泉中水大驿土' => 4.5, '大林木金箔金' => 7.5, '大溪水沙中土' => 8.5, '海中金霹雳火' => 4.5,
            '白腊金炉中火' => 4, '山头火天河水' => 4.5, '大林木钗钏金' => 7.5, '剑锋金桑柘木' => 4, '覆灯火海中金' => 7.5, '涧下水天上火' => 8.5, '大驿土泉中水' => 4.5,
            '金箔金大林木' => 7.5, '沙中土大溪水' => 8.5, '霹雳火海中金' => 4.5, '炉中火白腊金' => 4, '天河水山头火' => 4.5, '钗钏金大林木' => 7.5,
        ];
        $list1 = [
            '火火' => 5, '火金' => 4.5, '火木' => 9, '火水' => 7.5, '火土' => 7.5, '金火' => 6, '金金' => 7, '金木' => 7, '金水' => 10, '金土' => 9,
            '木火' => 8.5, '木金' => 7, '木木' => 5.5, '木水' => 9, '木土' => 5.5, '水火' => 4.5, '水金' => 9, '水木' => 9, '水水' => 7, '水土' => 6,
            '土火' => 9, '土金' => 8.5, '土木' => 4, '土水' => 5, '土土' => 10,
        ];
        $nyNan = $this->userInfo[0]['na_yin']['year'];
        $nyNv = $this->userInfo[1]['na_yin']['year'];
        if (isset($list[$nyNan . $nyNv])) {
            $fen = $list[$nyNan . $nyNv];
        } else {
            $wx = mb_substr($nyNan, 2, 1);
            $wx1 = mb_substr($nyNv, 2, 1);
            $fen = $list1[$wx . $wx1] ?? 0;
        }
        return $fen;
    }

    /**
     * 潜在能力
     * @return array
     */
    protected function getQianZai(): array
    {
        $list = [
            1 => '婚后热情可能减退，对伴侣略显冷淡，时有争吵，这可能让伴侣觉得感情不如婚前，从而在夫妻之间产生隔阂。',
            2 => '你对婚姻有独到见解，主动经营，认真对待感情，能给伴侣带来安全感，使感情进一步升温。你们相处和谐，彼此欣赏对方的人格魅力，婚姻稳定。',
            3 => '婚后事业心重，可能因工作忽略伴侣，但你善于快速解决夫妻问题，稳定感情。除非伴侣极度缺乏安全感，否则婚姻受影响的可能性较小。',
            4 => '婚后可能表现出冲动、叛逆、偏激的特点，不擅长处理夫妻和家庭关系，容易引发矛盾。若伴侣不够宽容包容，长此以往可能影响婚姻稳定。',
            5 => '婚后想法奇特，注重个人面子，在夫妻相处时可能显得自负。你有浪漫主义思想，想给伴侣留下深刻印象，但可能让对方觉得轻浮。',
            6 => '婚后个人想法多，自信且在意面子。你擅长甜言蜜语和小浪漫，有利于感情升温。你能兑现承诺，降低婚变概率。',
            7 => '你负责任，愿意为家庭分忧，主动承担家务，照顾孩子和老人。在这样的和谐家庭生活下，婚变可能性很低。',
            8 => '婚后可能变得唠叨，话语多，这可能引起伴侣的厌烦。你希望婚姻稳定，但需注意言行举止，以免引发婚变。',
            9 => '婚后可能难以保持冷静，容易因小事发脾气，态度变差。但及时沟通可以化解问题，避免感情隔阂。',
            10 => '婚后遇到重要问题时，你会主动与伴侣商量，处理事情公正、合情合理。即使出现矛盾，也能迅速解决，降低婚变概率。',
            11 => '婚后可能变得懒惰、依赖，期望伴侣承担更多责任。你在表达感情时可能显得笨拙，如果双方都有类似情况，可能会降低彼此的安全感。若矛盾未能及时解决，可能引发婚变。',
            12 => '在婚姻中，伴侣能理解和尊重你的想法，你们之间有独特的相处模式。虽然会有摩擦，但问题可以得到解决。你具备情感魅力。不过，仍需加强与伴侣的沟通交流。',
            13 => '在婚姻中可能表现得较为强势，固执己见，不愿意听取他人的意见。婚后需要控制脾气，加强交流，约束自己的行为，避免使用暴力。',
            14 => '婚姻中可能会遇到波折和困难，争吵与矛盾在所难免。需要注意情绪控制，多与伴侣进行沟通，共同面对和解决问题，以避免离婚和分居的风险。',
        ];
        $list1 = [
            1 => '你具备浓厚的家庭观念，对婚姻认真负责，能够自我约束，与异性保持适当的距离。你尊重并体贴妻子，因此婚变的可能性较低。',
            2 => '你的异性缘较好，在与异性相处时需要注意分寸和界限，以避免过于亲密而给婚姻带来危机。',
            3 => '婚后异性缘依然很好，感情态度可能多变。虽然有真心付出的时候，但也可能显得散漫。与异性相处时需注意保持适当的距离，避免对婚姻造成冲击。在生活中还需不断改善自己。',
            4 => '婚后异性缘仍然很好，但你会更加重视伴侣，真心对待婚姻。你会自觉与异性保持适当的距离，不让其他因素影响夫妻之间的感情。',
            5 => '根据八字分析，你命中带有正才和偏财，婚后与异性相处时需要特别审慎，以避免产生误解和感情纠缠，从而维护婚姻的稳定。',
            6 => '夫妻之间的感情可能不够深厚，容易发生摩擦和争执。事业和财运可能受到妻子以及婚姻经济问题的影响，给你带来沉重的压力。',
            7 => '婚姻之路可能多有波折和挑战，容易受到外界的冲击。配偶的感情可能不稳定，或者存在其他不利于婚姻的因素。',
            8 => '你的异性缘很好，但缘分难以把握。婚后伴侣可能性格强势，以他/她的意见为主。',
            9 => '婚后生活可能起伏不定，容易与妻子发生争执。但冲突通常不会达到决裂的程度。在口舌之争中需要保持适度，以免对婚姻关系产生不良影响。',
            10 => '婚后双方之间可能存在摩擦和争吵，应努力改善沟通和相互理解，以避免二次婚姻的风险。',
            11 => '婚后可能因琐事争吵或安全感不足而导致关系僵化，沟通不畅也会对婚姻产生不利影响。',
            12 => '婚后你会与异性保持适当的距离，避免过分亲近，以维护婚姻的稳定，并防范再婚的风险。',
            13 => '婚后可能因小事与伴侣发生争执，需要学会包容和沟通，以确保婚姻的长久幸福。',
            14 => '婚后意见不合或与妻子争吵的情况可能长期存在，这容易累积导致感情不和，对婚姻的发展产生不利影响。',
            15 => '婚后应与异性保持适度的距离和关系，避免过分亲近，以维护婚姻的稳定，并防止再婚的风险。',
            16 => '婚后你可能表现出强势的个性，不愿意示弱，这可能会给伴侣带来压力，影响婚姻的稳定。建议你多关注伴侣的情绪，适当收敛自己的个性，学会示弱以维系婚姻。',
            17 => '婚后你的固执想法可能得不到伴侣的认同和支持，这容易引发矛盾和争吵。长此以往，可能会对婚姻的稳定产生不良影响。',
            18 => '婚后你需要多包容妻子，减少争吵和冲突，以维护婚姻关系的稳定。',
            19 => '婚姻可能经历许多波折和挑战，夫妻之间的热情逐渐减退，感情变得平淡。你应尊重伴侣的感受和需求，避免对他/她提出过高的要求，以免对婚姻的稳定产生不利影响，甚至导致二次婚姻。此外，事业和财运也可能受到妻子或异性的影响。',
            20 => '你的异性缘很好，朋友众多，容易发生感情风波。这可能会给婚姻和家庭带来不稳定因素。婚后建议你与异性保持适当的距离和关系，以维护婚姻的稳定并避免多次婚姻的风险。',
            21 => '婚姻生活中可能会遇到一些不顺心的事情，建议你注意伴侣的异性朋友并多加管束。同时多沟通交流，提高婚姻的幸福指数。',
            22 => '婚后观念不合可能导致频繁的争吵和冲突，你需要对此加以控制和管理，以避免再婚的念头。',
            23 => '在恋爱和婚姻中需特别防范第三者的出现。建议你加强与妻子的有效沟通，共同面对和解决问题，以提高婚姻的质量。',
            24 => '婚后与伴侣之间容易发生争吵和冲突，处理问题的方法可能存在矛盾。双方应互谅互让、相互包容以避免夫妻感情不和睦影响婚姻生活的稳定。',
            25 => '婚后双方的想法可能不一致导致矛盾的产生。如果长此以往感情可能会出现裂痕。因此建议你注重与妻子的有效沟通及时修正问题确保婚姻的长久幸福。',
            26 => '婚后处理问题时如果观念差异大且互不相让可能导致婚姻陷入困境。你应与异性朋友保持适当的距离并尊重伴侣的意见以防范婚姻危机。',
            27 => '婚姻之路可能曲折多变。婚后你的异性缘仍然很好但需要自我克制。特别要防范第三者的插足以维护婚姻的稳定。',
            28 => '婚后与异性相处时应保持适度的距离和关系避免给伴侣带来不安全感以维护婚姻关系的稳定。',
            29 => '婚后夫妻感情可能逐渐平淡双方因忙碌而减少关注交流不足可能忽略伴侣的感受。建议你保持对伴侣的热情和关注以免影响婚姻生活的美满。',
            30 => '婚后生活可能变得平淡琐事争吵虽然不会对婚姻造成太大影响但需防范热情减退和产生隔阂的风险。',
        ];
        $list2 = [
            1 => '婚后你异性缘不减，应警惕诱惑。与异性交往要适度，以免误会伤害夫妻感情。',
            2 => '作为妻子，你忠于家庭，与异性保持界限，婚姻稳固，风险较低。',
            3 => '日常工作生活中，你与异性互动频繁，但需谨守分寸，防止情感纠葛。',
            4 => '婚姻波折多，夫妻不和，易受外界影响。需调整心态，注重健康。',
            5 => '婚姻或许未尽如人意，但无第三者插足。面对伴侣压力，保持冷静至关重要。',
            6 => '夫妻争吵频繁，沟通是关键。共同解决问题，才能维系婚姻长久。',
            7 => '婚后常有争吵、缺乏安全感，有效沟通是减少隔阂、稳定婚姻的关键。',
            8 => '与异性交往要把握尺度，以免损害婚姻和夫妻感情。',
            9 => '夫妻间争吵难免，学会宽容和退让是维护婚姻稳定的重要一环。',
            10 => '婚后意见不合易引发争吵，长期僵持可能影响婚姻稳定，甚至导致分离。',
            11 => '夫妻感情平淡时，尝试制造惊喜、理解对方，以减轻婚姻中的危机感。',
            12 => '在婚姻中，你应更开放地沟通，避免让伴侣猜测，以维护婚姻和谐。',
            13 => '婚姻历经波折，与伴侣沟通不足。加强交流，防范潜在危机。',
            14 => '婚后与丈夫争吵时，多一份理解和包容，有助于婚姻更长久。',
            15 => '婚后感情易淡化，提升仪式感、增加惊喜和体谅可增强婚姻幸福感。',
            16 => '你异性缘好，婚后需自我约束，与异性保持距离，避免婚姻危机。',
            17 => '婚姻波折多，需警惕伴侣的异性朋友，以防婚外情导致出轨。',
            18 => '婚后生活细节易引发矛盾，多包容理解可避免婚姻不稳和二婚风险。',
            19 => '婚后现实与预期有落差，可能导致小摩擦。保持平衡心态，维护婚姻稳定。',
            20 => '情感历程波折多，易陷复杂纠葛。观察自己和伴侣身边的异性，降低风险。',
            21 => '婚后因观念不同易争吵，互让一步有助于维护婚姻稳定。',
            22 => '你的婚姻之路或许坎坷，初婚困难多，再婚可能性大。',
            23 => '婚姻稳定性差，异性缘好。与异性保持距离是维护婚姻的关键。',
            24 => '你异性缘佳，婚后应自律，与异性保持适度关系以维护夫妻感情。',
            25 => '婚后感情渐淡，忙碌中别忘了关心对方。共同维护美满婚姻。',
            26 => '婚后异性朋友虽多，但应保持适当距离。维护婚姻稳定至关重要。',
        ];

        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];
        $ydzNan = $jiNianNan['y'][1];
        $mdzNan = $jiNianNan['m'][1];
        $ddzNan = $jiNianNan['d'][1];
        $hdzNan = $jiNianNan['h'][1];
        $ydzNv = $jiNianNv['y'][1];
        $mdzNv = $jiNianNv['m'][1];
        $ddzNv = $jiNianNv['d'][1];
        $hdzNv = $jiNianNv['h'][1];
        $godTNan = $this->userInfo[0]['god'];
        $godTNv = $this->userInfo[1]['god'];
        $godThsNan = $this->userInfo[0]['god_ths'];
        $godThsNv = $this->userInfo[1]['god_ths'];
        $godTzsNan = $this->userInfo[0]['god_tzs'];
        $godTzsNv = $this->userInfo[1]['god_tzs'];
        $godHideNan = $this->userInfo[0]['_god'];
        $godHideNv = $this->userInfo[1]['_god'];
        $godHNan = array_merge($godHideNan['year']['god'], $godHideNan['month']['god'], $godHideNan['day']['god'], $godHideNan['hour']['god']);
        $godHNv = array_merge($godHideNv['year']['god'], $godHideNv['month']['god'], $godHideNv['day']['god'], $godHideNv['hour']['god']);

        $kwNan = explode(',', Huangli::getKongWangbyGz($jiNianNan['y'][0] . $jiNianNan['y'][1]));
        $kwNv = explode(',', Huangli::getKongWangbyGz($jiNianNv['y'][0] . $jiNianNv['y'][1]));
        $godzNv = $this->userInfo[1]['godz'];
        $godzNan = $this->userInfo[0]['godz'];
        $resKey = [
            0 => $this->getQianZaiOne(0),
            1 => $this->getQianZaiOne(1),
        ];
        $resNan = [];
        if (in_array('伤官', $godHNan)) {
            if (!in_array('伤官', $godTNan)) {
                // 八字地支有伤官，天干没有（看地支藏干）
                $resNan[] = $list1[1];
            } else {
                // 八字天干地支都有伤官（看地支藏干）
                $resNan[] = $list1[2];
                // 八字天干地支都有伤官（看地支藏干），且天干地支无财星（看地支藏干）
                if (($godThsNan['zc'] + $godThsNan['pc']) == 0) {
                    $resNan[] = $list1[3];
                }
                // 八字天干地支都有伤官（看地支藏干），且八字原局有正财（不看藏干）
                if ($godTzsNan['zc'] > 0) {
                    $resNan[] = $list1[4];
                }
            }
        }
        if (in_array('正财', $godTNan) && in_array('偏财', $godTNan)) {
            // 八字命局中天干有正财和偏财，地支也有正财和偏财（看藏干） 5
            if (in_array('正财', $godHNan) && in_array('偏财', $godHNan)) {
                $resNan[] = $list[5];
            }
        }
        $xyNan = $this->userInfo[0]['xy'];
        $xyNv = $this->userInfo[1]['xy'];
        // 财星为八字忌神，且八字命局正财加偏财数量≥3（不看藏干） 6
        if ($xyNan['shen']['ji'] == '才财' && ($godTzsNan['zc'] + $godTzsNan['pc']) >= 3) {
            $resNan[] = $list1[6];
        }
        $gxNan = $this->userInfo[0]['gx'];
        // 日支被他支刑冲 7
        if (array_intersect(['刑', '冲'], $gxNan['d']['d'])) {
            $resNan[] = $list1[7];
        }
        $wangDuNan = $this->userInfo[0]['wangdu'];
        $wangDuNv = $this->userInfo[1]['wangdu'];
        // 八字格局弱，且原局正财加偏财≥3 8
        if (in_array($wangDuNan, ['身弱格', '从弱格']) && ($godTzsNan['zc'] + $godTzsNan['pc']) >= 3) {
            $resNan[] = $list1[8];
        }
        // 日支为偏印，月支为正印或偏印 9
        if ($godzNan['day'] == '偏印' && in_array($godzNan['month'], ['正印', '偏印'])) {
            $resNan[] = $list1[9];
        } elseif ($godzNan['day'] == '正印' && in_array($godzNan['month'], ['食神', '伤官'])) { // 日支为正印，月支为食神或伤官 10
            $resNan[] = $list1[10];
        } elseif ($godzNan['day'] == '比肩' && in_array($godzNan['month'], ['正官', '七杀'])) { // 日支十神为比肩，月支为正官或七杀 11
            $resNan[] = $list1[11];
        }
        // 月支或时支和日支相同 12
        if ($mdzNan == $ddzNan || $hdzNan == $ddzNan) {
            $resNan[] = $list1[12];
        }
        foreach ($godzNan as $k => $v) {
            $str = substr($k, 0, 1);
            // 伤官地支空亡 13
            if ($v == '伤官' && in_array($jiNianNan[$str][1], $kwNan)) {
                $resNan[] = $list1[13];
                break;
            }
        }
        // 日支被月支或时支相合 14
        if (BaziCommon::liuHeDz($ddzNan . $mdzNan) || BaziCommon::liuHeDz($ddzNan . $hdzNan)) {
            $resNan[] = $list1[14];
        }
        // 原局中同时存在正财和偏财（不看藏干） 15
        if (($godTzsNan['zc'] + $godTzsNan['pc']) >= 2) {
            $resNan[] = $list1[15];
        }
        $yyNan = $this->userInfo[0]['jnwx']['yy'];
        $yyNan1 = array_merge($yyNan['y'], $yyNan['m'], $yyNan['d'], $yyNan['h']);
        // 八字天干阴阳属性全阴，地支阴阳属性也是全阴 16
        if (!in_array('阳', $yyNan1)) {
            $resNan[] = $list1[16];
        }
        foreach ($godzNan as $k => $v) {
            $str = substr($k, 0, 1);
            if (!in_array($v, ['正财', '偏财'])) {
                continue;
            }
            foreach ($jiNianNan as $k1 => $v1) {
                if ($k1 == $str) {
                    continue;
                }
                // 遍历八字原局，地支财星和比劫冲、克(不看藏干) 18
                if (BaziCommon::getXianChong($jiNianNan[$str][1] . $v1[1]) || BaziCommon::getXianKe($jiNianNan[$str][1] . $v1[1])) {
                    $resNan[] = $list1[18];
                    break 2;
                }
            }
        }
        // 1、正财或偏财为忌神。2、正财和偏财数量大于等于4（需看藏干）。3、正财和偏财在月干或时干或日支。（三个条件同时满足） 19
        if (in_array($xyNan['shen']['ji'], ['正财', '偏财']) && ($godTzsNan['zc'] + $godTzsNan['pc']) >= 4 && array_intersect(['正财', '偏财'], [$godTNan['month'], $godTNan['hour'], $godzNan['day']])) {
            $resNan[] = $list1[19];
        }
        // 正财和偏财数量大于等于4（包括藏干） 20
        if (($godThsNan['zc'] + $godThsNan['pc']) >= 4) {
            $resNan[] = $list1[20];
        }
        $b1 = false;
        foreach ($godTNan as $k => $v) {
            $str = substr($k, 0, 1);
            if (!in_array($v, ['正财', '偏财'])) {
                continue;
            }
            $str2 = $jiNianNan[$str][0];
            foreach ($godTNan as $k1 => $v1) {
                if ($k == $k1 || !in_array($v, ['正财', '偏财'])) {
                    continue;
                }
                $str1 = substr($k1, 0, 1);
                $str3 = $jiNianNan[$str1][0];
                if (BaziCommon::xianHeTg($str2 . $str3)) {
                    $b1 = true;
                    break 2;
                }
            }
        }
        foreach ($godzNan as $k => $v) {
            $str = substr($k, 0, 1);
            if (!in_array($v, ['正财', '偏财'])) {
                continue;
            }
            $str2 = $jiNianNan[$str][0];
            foreach ($godzNan as $k1 => $v1) {
                if ($k == $k1 || !in_array($v, ['正财', '偏财'])) {
                    continue;
                }
                $str1 = substr($k1, 0, 1);
                $str3 = $jiNianNan[$str1][0];
                if (BaziCommon::xianHeTg($str2 . $str3)) {
                    $b1 = true;
                    break 2;
                }
            }
        }
        // 命局中正财或偏财被合（不看藏干）【合：天干相合和地支六合或地支三合】 21
        if ($b1) {
            $resNan[] = $list1[21];
        }
        // 日支为比肩或劫财 22
        if (in_array($godzNan['day'], ['比肩', '劫财'])) {
            $resNan[] = $list1[22];
        } elseif (in_array($godzNan['day'], ['正印', '偏印'])) { // 日支为正印或偏印 25
            $resNan[] = $list1[25];
        } elseif (in_array($godzNan['day'], ['正财', '偏财'])) {
            // 日支为正财或偏财，且天干有正财或偏财 26
            if (array_intersect(['正财', '偏财'], $godTNan)) {
                $resNan[] = $list1[26];
            }
        }
        // 天干同时有正财、偏财、比肩或劫财 23
        if (in_array('正财', $godTNan) && in_array('偏财', $godTNan) && (in_array('比肩', $godTNan) || in_array('劫财', $godTNan))) {
            $resNan[] = $list1[23];
        }
        // 天干同时有正财或偏财、比肩、劫财 24
        if (in_array('比肩', $godTNan) && in_array('劫财', $godTNan) && (in_array('正财', $godTNan) || in_array('偏财', $godTNan))) {
            $resNan[] = $list1[24];
        }
        $godTNanSum = $this->getGodSum($godTNan);
        $godHNanSum = $this->getGodSum($godHNan);
        // 天干有两个偏财，且地支藏干也有两个偏财 27
        if ($godTNanSum['pc'] == 2 && $godHNanSum['pc'] == 2) {
            $resNan[] = $list1[27];
        }
        // 地支藏有两个偏财（需要看藏干） 28
        if ($godHNanSum['pc'] == 2) {
            $resNan[] = $list1[28];
        }
        // 遍历八字原局，比肩的数量多于官正财和偏财总和数量 29
        if ($godTzsNan['bj'] > ($godTzsNan['zc'] + $godTzsNan['pc'])) {
            $resNan[] = $list1[29];
        }
        if (empty($resNan)) {
            $resNan[] = $list1[30];
        }
        $resNv = [];
        // 八字天干地支都有正财或者偏财其中一个 1
        if (array_intersect(['正财', '偏财'], $godHNv) && array_intersect(['正财', '偏财'], $godTNv)) {
            $resNv[] = $list2[1];
        }
        // 八字地支有偏财，天干没有（看地支藏干） 2
        if (in_array('偏财', $godHNv) && !in_array('偏财', $godTNv)) {
            $resNv[] = $list2[2];
        }
        // 八字命局天干有正官和七杀，地支也有正官七杀（看藏干） 3
        if (in_array('正官', $godTNv) && in_array('七杀', $godTNv) && in_array('正官', $godHNv) && in_array('七杀', $godHNv)) {
            $resNv[] = $list2[3];
        }
        if ($xyNv['shen']['ji'] = '官杀') {
            // 官星为八字忌神，且八字命局正官加偏官数量≥3（不看藏干） 4
            if (($godTzsNv['zg'] + $godTzsNv['qs']) >= 3) {
                $resNv[] = $list2[4];
            }
            // 八字只有一个正官或者一个七杀，且官星为忌神 5
            if ($godTzsNv['zg'] == 1 || $godTzsNv['qs'] == 1) {
                $resNv[] = $list2[5];
            }
        }
        if ($godzNv['day'] == '正印') {
            // 日支为正印，月支为食神或伤官 6
            if (in_array($godzNv['month'], ['食神', '伤官'])) {
                $resNv[] = $list2[6];
            }
        } elseif ($godzNv['day'] == '比肩') {
            // 日支十神为比肩，月支为正官或七杀 7
            if (in_array($godzNv['month'], ['正官', '七杀'])) {
                $resNv[] = $list2[7];
            }
        }
        // 月支或时支和日支相同 8
        if ($mdzNv == $ddzNv || $hdzNv == $mdzNv) {
            $resNv[] = $list2[8];
        }
        foreach ($godzNv as $k => $v) {
            if ($k == 'y' && $v != '伤官') {
                continue;
            }
            $str = substr($k, 0, 1);
            // 伤官地支空亡 9
            if (in_array($jiNianNv[$str][1], $kwNv)) {
                $resNv[] = $list2[9];
                break;
            }
        }
        // 日支被月支或时支相合 10
        if (BaziCommon::liuHeDz($mdzNv . $ddzNv) || BaziCommon::liuHeDz($hdzNv . $ddzNv)) {
            $resNv[] = $list2[10];
        }
        // 原局中同时存在正官和七杀（不看藏干） 11
        if ($godTzsNv['zg'] > 0 && $godTzsNv['qs'] > 0) {
            $resNv[] = $list2[11];
        }
        $yyNv = $this->userInfo[1]['jnwx']['yy'];
        $yyNv1 = array_merge($yyNv['y'], $yyNv['m'], $yyNv['d'], $yyNv['h']);
        // 八字天干阴阳属性全阴，地支阴阳属性也是全阴 12
        if (!in_array('阳', $yyNv1)) {
            $resNv[] = $list2[12];
        }
        foreach ($godzNv as $k => $v) {
            if (!in_array($v, ['正官', '七杀'])) {
                continue;
            }
            $str = substr($k, 0, 1);
            foreach ($godzNv as $k1 => $v1) {
                if (!in_array($v, ['食神', '伤官'])) {
                    continue;
                }
                $str1 = substr($k1, 0, 1);
                $str2 = $jiNianNv[$str][1];
                $str3 = $jiNianNv[$str1][1];
                // 遍历八字原局，地支官星和食伤冲、克(不看藏干) 14
                if (BaziCommon::getXianChong($str2 . $str3) || BaziCommon::getXianKe($str2 . $str3)) {
                    $resNv[] = $list2[14];
                    break 2;
                }
            }
        }
        // 1、正官或七杀为忌神。2、正官和七杀数量大于等于4（需看藏干）。3、正官或七杀在月干或时干或日支。（三个条件同时满足） 15
        if ($xyNv['shen']['ji'] == '官杀' && ($godThsNv['zg'] + $godThsNv['qs']) >= 4 && array_intersect(['正官', '七杀'], [$godTNv['month'], $godTNv['hour'], $godzNv['day']])) {
            $resNv[] = $list2[15];
        }
        // 正官和七杀数量大于等于4（包括藏干） 16
        if (($godThsNv['zg'] + $godThsNv['qs']) >= 4) {
            $resNv[] = $list2[16];
        }
        $b1 = false;
        foreach ($godTNv as $k => $v) {
            $str = substr($k, 0, 1);
            if (!in_array($v, ['正财', '偏财'])) {
                continue;
            }
            $str2 = $jiNianNv[$str][0];
            foreach ($godTNv as $k1 => $v1) {
                if ($k == $k1 || !in_array($v, ['正财', '偏财'])) {
                    continue;
                }
                $str1 = substr($k1, 0, 1);
                $str3 = $jiNianNv[$str1][0];
                if (BaziCommon::xianHeTg($str2 . $str3)) {
                    $b1 = true;
                    break 2;
                }
            }
        }
        foreach ($godzNv as $k => $v) {
            $str = substr($k, 0, 1);
            if (!in_array($v, ['正财', '偏财'])) {
                continue;
            }
            $str2 = $jiNianNv[$str][0];
            foreach ($godzNv as $k1 => $v1) {
                if ($k == $k1 || !in_array($v, ['正财', '偏财'])) {
                    continue;
                }
                $str1 = substr($k1, 0, 1);
                $str3 = $jiNianNv[$str1][0];
                if (BaziCommon::xianHeTg($str2 . $str3)) {
                    $b1 = true;
                    break 2;
                }
            }
        }
        // 命局中七杀或正官被合（不看藏干）【合：天干相合和地支六合或地支三合】 17
        if ($b1) {
            $resNv[] = $list2[17];
        }
        // 日支为食神或伤官 18
        if (in_array($godzNv['day'], ['食神', '伤官'])) {
            $resNv[] = $list2[18];
            // 日支为伤官 19
            if ($godzNv['day'] == '伤官') {
                $resNv[] = $list2[19];
            }
        }
        // 天干同时有正官，七杀，比肩或劫财 20
        if (in_array('正官', $godTNv) && in_array('七杀', $godTNv) && (in_array('比肩', $godTNv) || in_array('劫财', $godTNv))) {
            $resNv[] = $list2[20];
        }
        // 天干同时有正官或七杀，食神，伤官。 21
        if (in_array('食神', $godTNv) && in_array('伤官', $godTNv) && (in_array('正官', $godTNv) || in_array('七杀', $godTNv))) {
            $resNv[] = $list2[21];
        }
        // 1、年、月、日柱（天干和地支）十神为正官或七杀。2、且正官或七杀被刑、冲、克、害、穿、合 22
        // 天干十神表 23
        $godHNvSum = $this->getGodSum($godHNv);
        // 地支藏有两个偏官（需要看藏干） 24
        if ($godHNvSum['qs'] == 2) {
            $resNv[] = $list2[24];
        }
        // 遍历八字原局，比肩的数量多于官正官和七杀总和数量 25
        if ($godTzsNv['bj'] > ($godTzsNv['zg'] + $godTzsNv['qs'])) {
            $resNv[] = $list2[25];
        }
        $arr = array_column($jiNianNv, 1);
        $arr1 = array_intersect(['子', '午', '卯', '酉'], $arr);
        // 地支子午卯酉出现三支或者三支以上 26
        if (count($arr1) >= 3) {
            $resNv[] = $list2[26];
        }
        foreach ($resKey[0] as $v) {
            if (in_array($v, $resKey[1])) {
                $resNan[] = $list[$v];
            }
        }
        foreach ($resKey[1] as $v) {
            if (in_array($v, $resKey[0])) {
                $resNv[] = $list[$v];
            }
        }
        return [
            'nan' => array_slice($resNan, 0, 3),
            'nv' => array_slice($resNv, 0, 3),
        ];
    }

    /**
     * 潜在不限的序号
     * @param int $sex 性别 0男 1女
     * @return array
     */
    protected function getQianZaiOne(int $sex)
    {
        $jiNian = $this->userInfo[$sex]['base']['jinian'];
        $mdz = $jiNian['m'][1];
        $ddz = $jiNian['d'][1];
        $hdz = $jiNian['h'][1];
        $godTNan = $this->userInfo[0]['god'];
        $godThsNan = $this->userInfo[0]['god_ths'];
        $godTzsNan = $this->userInfo[0]['god_tzs'];
        $godHideNan = $this->userInfo[0]['_god'];
        $godHNan = array_merge($godHideNan['year']['god'], $godHideNan['month']['god'], $godHideNan['day']['god'], $godHideNan['hour']['god']);
        $kwNan = explode(',', Huangli::getKongWangbyGz($jiNian['y'][0] . $jiNian['y'][1]));
        $result = [];
        if (in_array($ddz, $kwNan)) {
            $result[] = 1;
        }
        // 八字天干地支都有偏印（看地支藏干），且原局偏印数量小于财（正财偏财）的数量（看地支藏干）
        if (in_array('偏印', $godTNan) && $godThsNan['py'] < ($godThsNan['zc'] + $godThsNan['pc'])) {
            $result[] = 2;
        }
        if ($godThsNan['qs'] < ($godThsNan['ss'] + $godThsNan['sg'])) {
            $result[] = 3;
        } else {
            $result[] = 4;
        }
        if (in_array('伤官', $godTNan) && in_array('伤官', $godHNan)) {
            if (($godThsNan['zy'] + $godThsNan['py']) < $godThsNan['sg']) {
                $result[] = 5;
            } else {
                $result[] = 6;
            }
        }
        if (($godThsNan['ss'] + $godThsNan['sg']) >= 3 || ($godTzsNan['ss'] + $godTzsNan['sg']) >= 2) {
            $result[] = 7;
        }
        if (in_array('食神', $godTNan) && in_array('食神', $godHNan)) {
            $result[] = 8;
        }
        $godzNan = $this->userInfo[0]['godz'];
        if ($godzNan['month'] == '食神') {
            $result[] = 9;
        }
        if ($godThsNan['zg'] < ($godThsNan['ss'] + $godThsNan['sg'])) {
            $result[] = 10;
        }
        if (in_array('正印', $godTNan) && in_array('正印', $godHNan)) {
            if (($godThsNan['zy'] + $godThsNan['py']) > ($godThsNan['zc'] + $godThsNan['pc'])) {
                $result[] = 11;
            }
            if ($godThsNan['zy'] < ($godThsNan['zc'] + $godThsNan['pc'])) {
                $result[] = 12;
            }
        }
        if (array_intersect(['比肩', '劫财'], $godHideNan['hour']['god'])) {
            $result[] = 13;
        }
        // 14.月支和日支相冲 2.日支和时支相冲 （两种算法有任意一个都输出）
        if (BaziCommon::getXianChong($mdz . $ddz) || BaziCommon::getXianChong($ddz . $hdz)) {
            $result[] = 14;
        }
        return $result;
    }

    /**
     * 合婚分数
     * @param array $fenArr 分数数组
     * @return array
     */
    protected function getHeHunFen(array $fenArr): array
    {
        $total = array_sum($fenArr) + $fenArr['xyfen'] * 3;
        asort($fenArr);
        $keys = array_keys($fenArr);
        $min = $keys[0];
        $name = '';
        if ($total <= 25) {
            $list = [
                'shuxian' => '婚姻配对欠佳，占有欲强，希望掌握对方动向。但婚姻需独立空间，保持神秘和惊喜。',
                'nayin' => '婚姻配对不佳，相处或缺包容。初期新鲜，后期问题显。需磨合适应，以免影响婚姻。',
                'year' => '婚姻配对欠佳，处理问题有差异。需良好沟通，顾及对方感受，免生怀疑。',
                'day' => '婚姻配对欠佳，有感情基础但吸引力减。话题多变，忽略美好。需关注生活琐事中的幸福。',
                'shensha' => '婚姻配对欠佳，与理想有差距。婚后美感褪，缺点显。需适应彼此习惯和性格。',
                'liuqing' => '婚姻配对欠佳，面对问题难达共识。心理诉求无回应，失落积累。需提高情感灵敏度。',
                'xyfen' => '婚姻配对欠佳，面对家庭问题易分歧。双方角度和顾虑不同，需商量、理解以达成一致。',
            ];
        } elseif ($total <= 30) {
            $list = [
                'shuxian' => '婚姻配对不佳，聚少离多，沟通不足，因家庭分歧而矛盾加剧、感情疏远。多为对方考虑、平心沟通可改善。',
                'nayin' => '婚姻配对欠佳，常因家庭争执。关注点和解决方式不同导致障碍和隔阂，未及时发现问题则关系疏远。',
                'year' => '婚姻组合不太好，同甘共苦不易。遇困无法感同身受时，意见难一致。随时间推移，易出现婚姻不利现象。',
                'day' => '婚姻组合欠佳，初期美好，共同生活后分歧显。敏感问题引发争执，影响尊重和快乐。需互相包容和认可，保持平稳。',
                'shensha' => '婚姻组合不良，婚前婚后差距大导致不满。小事放大，争吵不断，感情消磨。需共同讨论未来经营，否则不了了之。',
                'liuqing' => '婚姻组合不佳，异性缘旺导致猜忌和隔阂。桃花多影响安全感。需坚守自我、与异性保持距离、给予信任和信心，提高幸福度。考验心性和爱意。',
                'xyfen' => '婚姻组合欠佳，婚后问题多、易分歧和争吵。若无妥善解决，终将导致分离。',
            ];
        } elseif ($total <= 40) {
            $list = [
                'shuxian' => '婚姻组合虽不突出，但不代表不幸福。聚少离多、异地生活可能冲淡感情、引发争吵。需减少异地时间、增强互动、保持热情。',
                'nayin' => '婚姻组合一般，但不意味结局不佳。婚后家庭问题需磨合与包容，长时间相处可化解问题、减少争吵、维系感情。',
                'year' => '婚姻组合欠佳，虽多为对方考虑，但分歧时坚持自我。此现象常见，但及时解决可避免隐患爆发。',
                'day' => '婚姻组合一般，好坏取决于对家庭生活的态度。需正确表达不满、交流解决问题，以保幸福。',
                'shensha' => '婚姻组合不太好，婚后对感情和家庭生活各有畅想，易因大事争吵。需有效沟通、确定计划以避免翻旧账。',
                'liuqing' => '婚姻组合稍差，身边异性可能影响感情。需主动与异性保持距离、给予安全感，以减少隔阂、考验忠诚。',
                'xyfen' => '婚姻组合一般，不意味完全差劲。生活常见问题易引发争吵，及时处理可避免问题累积爆发。',
            ];
        } elseif ($total <= 60) {
            $list = [
                'shuxian' => '婚姻组合一般，相处问题不大，但不保证平稳。处理问题需考虑对方感受，以避免埋下隐患。',
                'nayin' => '婚姻组合一般，婚后责任增加，可能发现更多缺点。需及时沟通、包容和指出问题，避免一味忍让导致无法忍受。',
                'year' => '婚姻一般，有深厚感情基础，会为对方考虑。但需增加沟通、避免交流差异导致误解和不幸福。',
                'day' => '婚姻组合一般，常因小事争吵，甚至放大问题。需及时应对和改善，以维护婚姻生活。',
                'shensha' => '婚姻相对一般，虽能互帮互助，但感情基础差距在遇问题时易造成看法差异，可能成为彼此心中的刺。',
                'liuqing' => '婚姻组合一般，接人待物、处理事情差异逐渐显露，可能出现大分歧。需换位思考、寻找适合双方的相处模式。',
                'xyfen' => '婚姻组合一般，稳定但新鲜感可能减弱。忙碌工作可能影响感情交流，需加强交流、共创小美好。',
            ];
        } elseif ($total <= 70) {
            $list = [
                'shuxian' => '你们的婚姻稳定且平凡，感情平稳且默契。生活中的习惯差异和困扰可通过包容和真诚沟通来解决。',
                'nayin' => '你们的婚姻平凡中见真情，已准备好共同面对生活的挑战。面对困难时需携手共进，这样才能使感情更加坚定。',
                'year' => '你们的婚姻稳定，感情基础深厚，但看待问题的角度或有不同。应开放心扉，增加交流，以避免情感上的障碍。',
                'day' => '你们的婚姻稳定，日常生活中能够互相配合和包容。应更关注伴侣和婚姻的经营，而不是在意外界的看法。',
                'shensha' => '你们的婚姻稳定，但生活习惯和消费观念或有不同。需要时间去磨合和适应，同时多关注对方的感受，保持情感交流。',
                'liuqing' => '你们的婚姻稳定，有良好的感情基础，虽然会因琐事争吵。但注重沟通、尊重和理解是维系婚姻和感情的关键。',
                'xyfen' => '你们的婚姻稳定，但随着时间的推移，双方的小缺点可能会逐渐暴露。需要耐心、包容和理解来找到舒适的相处方式。',
            ];
        } elseif ($total <= 80) {
            $list = [
                'shuxian' => '你们的婚姻组合良好，默契十足，矛盾易于解决。但为了稳固婚姻，还需共同努力协调家庭问题。',
                'nayin' => '你们的结合被外界看好，对家庭和事业都有助益。但婚后需注意处理琐事引发的矛盾，以提升生活的融洽度。',
                'year' => '你们的婚姻组合不错，感情融洽。为了长久相伴，需互相包容和沟通以克服久处后暴露的缺点。',
                'day' => '你们的婚姻组合良好，婚后虽有分歧但能随时间磨合适应。为了感情更加融洽，需多沟通交流和相互包容。',
                'shensha' => '你们的婚姻组合较佳，生活方式的差异可能带来摩擦。但相互理解和包容后能找到和谐相处的方式。',
                'liuqing' => '面对困难时你们或许有不同的处理方式，但能共同面对并互相支持。不必过于在意外界评论。',
                'xyfen' => '你们的婚姻组合较好，有感情上的默契。婚后生活可能围绕琐事展开，与预期有所不同。为了增加情调需加强沟通和制造惊喜。',
            ];
        } elseif ($total <= 90) {
            $list = [
                'shuxian' => '你们的婚姻组合优秀，从恋爱到婚姻感情深厚。面对家庭责任如子女教育、父母赡养等问题时需共同努力。',
                'nayin' => '你们婚后相处融洽即使兴趣不同也不影响感情。能接纳对方的优缺点并共同学习进步。有明确的未来规划相伴到老的可能性很大。',
                'year' => '你们的婚姻组合良好几乎没有难以解决的矛盾高度契合。小打小闹不会成为问题修成正果的希望很大。',
                'day' => '你们的婚姻组合不错即使琐事也不会影响感情。婚后感情可能趋于平淡但有落差时不放弃努力制造小惊喜可以稳定婚姻。',
                'shensha' => '你们的婚姻组合很好默契感强给予对方安全感。婚后难免有磕碰但需包容和理解以提升婚姻质量。',
                'liuqing' => '你们婚后生活融洽相处愉快。遇到看法、态度不一致时可能产生矛盾需及时沟通并有共同的目标和认同感。',
                'xyfen' => '你们婚后愿意分享未来规划和幸福生活的理解。生活方式的差异可能导致分歧但经过时间磨合和适应后感情会更加融洽幸福指数会提升。',
            ];
        } else {
            $list = [
                'shuxian' => '你们婚后处理问题时想法一致配合默契在分歧时能及时沟通。琐事成为爱情的调味品婚姻的幸福指数很高。',
                'nayin' => '你们婚后感情甜蜜是朋友眼中的完美一对。珍惜、包容、理解对方给予足够的安全感。即使偶有摩擦相伴到老的可能性也很大。',
                'year' => '你们的婚姻非常幸福认定彼此为一生伴侣经常给对方小惊喜增加仪式感。虽然生活方式有所不同但愿意为对方适应。',
                'day' => '你们在婚姻中彼此提升共同进步默契度很高。因琐事产生矛盾时能妥协、有效沟通并包容对方。',
                'shensha' => '你们从恋爱到婚姻感情一直深厚默契度满分。彼此一个眼神就能了解对方的想法很少因为沟通产生分歧非常认可对方。',
                'liuqing' => '你们婚后责任感很强能设身处地为对方着想。遇到问题时不逃避及时交流并通过行动表达关心。在意见分歧时能够理性解决。',
                'xyfen' => '你们婚后感情很好愿意倾听对方分享的事业和生活小事。虽然消费方式不同可能产生摩擦但经过磨合后能适应对方感情不会受到影响生活也会越来越好。',
            ];
        }
        if ($total < 30) {
            $total = round($total / 0.8);
        } elseif ($total < 50) {
            $total = round($total / 0.81);
        } elseif ($total < 60) {
            $total = round($total / 0.82);
        } elseif ($total < 66) {
            $total = round($total / 0.84);
        } elseif ($total < 79) {
            $total = 78;
        }
        if ($total < 29) {
            $name = '大凶';
        } elseif ($total < 38) {
            $name = '凶';
        } elseif ($total < 50) {
            $name = '小凶';
        } elseif ($total < 60) {
            $name = '平';
        } elseif ($total < 80) {
            $name = '小吉';
        } elseif ($total < 88) {
            $name = '吉';
        } else {
            $name = '大吉';
        }
        return [
            'name' => $name,
            'fen' => $total,
            'info' => $list[$min] ?? '',
        ];
    }

    /**
     * 婚后甜蜜度
     * @return array
     */
    protected function getHunYinTianMi(): array
    {
        $infoNan = $this->userInfo[0];
        $infoNv = $this->userInfo[1];
        $godHideNan = $infoNan['_god'];
        $godHideNv = $infoNv['_god'];
        $godTzsNan = $infoNan['god_tzs'];
        $godTzsNv = $infoNv['god_tzs'];
        $wangDuNan = $infoNan['wangdu'];
        $wangDuNv = $infoNv['wangdu'];
        // 纪年
        $jiNianNan = $infoNan['base']['jinian'];
        $jiNianNv = $infoNv['base']['jinian'];
        $dtgNan = $jiNianNan['d'][0];
        $dtgNv = $jiNianNv['d'][0];
        $result = [];
        $arr = [
            ['zg', 'qs'], ['zc', 'pc'], ['zy', 'py'], ['ss', 'sg'], ['bj', 'jc'],
        ];
        $arrNan = [];
        $arrNv = [];
        foreach ($arr as $v) {
            $key = implode('', $v);
            $arrNan[$key] = $godTzsNan[$v[0]] + $godTzsNan[$v[1]];
            $arrNv[$key] = $godTzsNv[$v[0]] + $godTzsNv[$v[1]];
        }

        $b1Nan = in_array($wangDuNan, ['身旺格', '从旺格']) ? 1 : 0;
        $b1Nv = in_array($wangDuNv, ['身旺格', '从旺格']) ? 1 : 0;
        $jnWxNan = array_column($infoNan['jnwx']['num'], 'num', 'wx');
        $jnWxNv = array_column($infoNv['jnwx']['num'], 'num', 'wx');
        arsort($jnWxNan);
        arsort($jnWxNv);
        // 结合男女双方喜用神：1、男方的喜用神=女方忌神；2、男方喜用神包含女方忌神 3结合男女双方八字：1.男方其中一个五行≤1，则女方对应五行≥3（例，男方八字只有一个木，女方八字则有三个木）2.女方其中一个五行≤1，则男方对应五行≥3（例，女方八字只有一个木，男方八字则有三个以上的木）（需看藏干）
        $xyNan = $infoNan['xy'];
        $xyNv = $infoNv['xy'];
        $b2 = false;
        if (in_array($xyNv['xy']['ji'], [$xyNan['xy']['yong'], $xyNan['xy']['xi']]) || in_array($xyNan['xy']['ji'], [$xyNv['xy']['yong'], $xyNv['xy']['xi']])) {
            $b2 = true;
        }
        $jnWxNan2 = $jnWxNan;
        $jnWxNv2 = $jnWxNv;
        $wxAttr = WuXing::GZ_TO_WX;
        if (false === $b2) {
            foreach ($godHideNan as $k => $v) {
                foreach ($v['hide'] as $v1) {
                    $jnWxNan2[$wxAttr[$v1]]++;
                }
                foreach ($godHideNv[$k]['hide'] as $v1) {
                    $jnWxNv2[$wxAttr[$v1]]++;
                }
            }
            foreach ($jnWxNan2 as $k => $v) {
                if (($v <= 1 && $jnWxNv2[$k] >= 3) || ($v >= 1 && $jnWxNv2[$k] <= 3)) {
                    $b2 = true;
                    break;
                }
            }
        }
        if ($b2) {
            $result[] = '你们双方的八字互补，使得双方的八字达到一种平衡，尽管两人从性格上或者其他方面上天差地别，但是相处起来却额外的融洽，并会给对方带来好运。婚后你们可以互相支持，相互匡助，志同道合的你们，会更加的幸福。';
        }
        // 结合男女双方八字：天干五合和地支六合次数≥3个
        $gxCountNan = $this->getGxCount($infoNan['gx']);
        $gxCountNv = $this->getGxCount($infoNv['gx']);
        $numNan = $gxCountNan['t']['合'] ?? 0;
        $numNv = $gxCountNv['t']['合'] ?? 0;
        $numNan += $gxCountNan['d']['六合'] ?? 0;
        $numNv += $gxCountNv['d']['六合'] ?? 0;
        if ($numNan >= 3 && $numNv >= 3) {
            $result[] = '两人八字相合，象征互助融合，感情亲密无间，难舍难分，预示婚姻融洽幸福。';
        }
        if ($dtgNan == $dtgNv && ($b1Nan + $b1Nv) == 1) {
            $result[] = '你俩性格互补，刚柔并济，主内外有序，欣赏彼此优缺点，共同构建美满家庭，享受幸福婚姻。';
        }
        if (BaziCommon::getHehua($dtgNan . $dtgNv)) {
            $result[] = '双方八字相合，意味互助情深，融合无间，倾慕彼此，预示婚姻融洽幸福。';
            if (BaziCommon::liuHeDz($jiNianNan['d'][1] . $jiNianNv['d'][1])) {
                $result[] = '双两人八字相投，意味相助相成，亲密无间，难舍难分，预示婚姻和谐幸福。';
            }
        }
        // 结合男女双方八字来看：男女双方格局为都为身强，且男方日干是女方的官杀（正官或七杀），女方的日干是男方的财星（正财或偏财）
        if ($b1Nan && $b1Nv) {
            if (in_array(BaziCommon::getGodName($dtgNan . $dtgNv), ['正官', '七杀']) || in_array(BaziCommon::getGodName($dtgNv . $dtgNan), ['正财', '偏财'])) {
                $result[] = '你们彼此助旺事业财运，相互支持，预示婚后生活融洽和谐。';
            }
        }
        // 结合男女双方八字来看：1、男女双方的日支五行相生；2、男女双方的日支六合
        if (WuXing::getWuxingGuanXi($infoNan['jnwx']['wx']['d'][1], $infoNv['jnwx']['wx']['d'][1]) == '生' && BaziCommon::liuHeDz($jiNianNan['d'][1] . $jiNianNv['d'][1])) {
            $result[] = '双方八字生合相生，预示夫妻关系融洽，感情深厚，无惧第三者破坏。';
        }
        // 无论男女，格局为身旺，且日支主气为比肩
        $godzNan = $infoNan['godz'];
        $godzNv = $infoNv['godz'];
        if (($wangDuNan == '身旺格' && $godzNan == '比肩') || $wangDuNv == '身旺格' && $godzNv == '比肩') {
            $result[] = '你们婚后易拌嘴吵架，需关注夫妻关系，避免问题影响婚姻和谐。';
        } elseif (($wangDuNan == '身弱格' && $godzNan == '比肩') || $wangDuNv == '身弱格' && $godzNv == '比肩') {
            $result[] = '你的另一半愿与你共进退，并肩前行，预示婚后生活幸福美感。';
        }
        $godThsNan = $infoNan['god_ths'];
        // 八字财星数量≥3，且比劫数量小于等于2（看藏干）
        if (($godThsNan['zc'] + $godThsNan['pc']) >= 3 && $godThsNan['bj'] <= 2) {
            $result[] = '男方八字财星旺盛，婚姻缘佳，预示幸福美满婚姻，对另一半百依百顺，解决问题能力强。';
        }
        $bool = true;
        $jnWxNan2C = array_sum($jnWxNan2);
        $jnWxNv2C = array_sum($jnWxNv2);
        foreach ($jnWxNan2 as $k => $v) {
            $tmp = $v / $jnWxNan2C;
            $tmp1 = $jnWxNv2[$k] / $jnWxNv2C;
            if ((($tmp < 0.2 && $tmp > 0.3)) || ($tmp1 < 0.2 && $tmp1 > 0.3)) {
                $bool = false;
                break;
            }
        }
        if ($bool) {
            $result[] = '你们五行均衡，性格温和。另一半五行互补，夫妻扶持到老。';
        }
        // 八字正印数量大于等于三且日支主气为正印（看藏干）
        $godThsNv = $infoNv['god_ths'];
        if ($godThsNv['zy'] >= 3 && $godzNv['day'] == '正印') {
            $result[] = '女方印多，包容心强，婚姻危机中坚持解决问题。体贴、体谅丈夫，包容缺点，捍卫婚姻。婚姻稳定。';
        }
        // 1，正官为喜用且八字原局有财，且日支主气五行为喜用；2，日支主气五行为八字忌神且月支时支克日支（两条满足其一则输出）
        $bool = false;
        if (in_array($infoNv['jnwx']['wx']['d'][1], [$xyNv['xy']['yong'], $xyNv['xy']['xi']])) {
            if (in_array('官杀', [$xyNv['shen']['yong'], $xyNv['shen']['xi']]) && $godTzsNv['zg'] > 0) {
                if ($godTzsNv['zc'] > 0 || $godTzsNv['pc'] > 0) {
                    $bool = true;
                }
            }
        } elseif ($infoNv['jnwx']['wx']['d'][1] == $xyNv['xy']['ji']) {
            if (BaziCommon::getXianKe($jiNianNv['m'][1] . $jiNianNv['d'][1]) || BaziCommon::getXianKe($jiNianNv['h'][1] . $jiNianNv['d'][1])) {
                $bool = true;
            }
        }
        if ($bool) {
            $result[] = '女方八字吉祥，老公健康财运好。因有这样老公感到幸福骄傲，婚姻有保障，生活温馨。';
        }
        // 日支主气为官星（正官或者偏官）且为喜用
        if (in_array('官杀', [$xyNv['shen']['yong'], $xyNv['shen']['xi']]) && in_array($godzNv['day'], ['正官', '七杀'])) {
            $result[] = '女方八字吉祥，另一半在身边有助。婚后生活少矛盾，婚姻美满。';
        }
        if (empty($result)) {
            $result[] = '婚姻平淡，或因缺少激情，夫妻距离远，感情冷淡。';
        }
        // 婆媳关系
        $poXi = [];
        // 八字原局日支为印星（正印或者偏印） 男
        if (in_array($godzNan['day'], ['正印', '偏印'])) {
            $poXi[] = '婚姻中易出现婆媳矛盾，影响夫妻感情。婆媳关系不和，妨碍美满婚姻。';
        }
        // 1.八字比劫数量≥3，且格局为强格 ，且八字无偏财 （看藏干） 女
        if (in_array($wangDuNv, ['身旺格', '从旺格']) && ($godThsNv['bj'] + $godThsNv['jc']) >= 3 && $godThsNv['pc'] == 0) {
            $poXi[] = '女方婚姻中婆媳矛盾较少，因婆婆包容心强、不破坏夫妻关系。不住一起，矛盾可能性小。';
        }
        // 八字官星为用神，且八字中有偏财和正官（看藏干） 女
        if ($xyNv['shen']['yong'] == '官杀' && $godThsNv['pc'] > 0 && $godThsNv['zg'] > 0) {
            $poXi[] = '婚姻中婆媳关系和谐，丈夫调和效果佳。事业有成，不制造矛盾，家庭关系融洽。';
        }
        // 1.年干为伤官，且其他天干和年支无印星（正印和偏印）。2.年干为伤官且年支不为比肩和劫财。两个算法满足一条则输出
        if ($infoNan['god']['year'] == '伤官') {
            if (!array_intersect(['正印', '偏印'], $infoNan['god']) || !in_array($godzNan['year'], ['比肩', '劫财'])) {
                $poXi[] = '你妻子与母亲关系可能不融洽，意见不合，言语冲突。需多加劝和，维护家庭和谐。';
            }
        }
        if ($infoNv['god']['year'] == '伤官') {
            if (!array_intersect(['正印', '偏印'], $infoNv['god']) || !in_array($godzNv['year'], ['比肩', '劫财'])) {
                $poXi[] = '你和婆婆相处欠佳，话题少，接触有限，不建议同住。家中文凭最高者可改善关系，一物降一物。';
            }
        }
        // 日支主气为伤官
        if ($godzNv['day'] == '伤官' || $godzNan['day'] == '伤官') {
            $poXi[] = '婆媳关系可能不融洽，话题少，与亲戚不亲近，困难时难得亲友帮助。';
        }
        // 日支与月支相冲 男
        if (BaziCommon::getXianChong($jiNianNan['d'][1] . $jiNianNan['m'][1])) {
            $poXi[] = '婆媳关系欠佳，常拌嘴，不宜长期同住。男方处境为难，最好分开居住，避免矛盾。';
        }
        // 月支主气是劫财 女
        if ($godzNv['month'] == '劫财') {
            $poXi[] = '妻子与婆婆关系紧张，婆婆性格急躁、古板、脾气差。可寻求有话语权者调解。';
        }
        // 八字月干或者时干为偏财且偏财旺且为忌神
        $godTF = $infoNv['god'];
        if (in_array('偏财', [$godTF['month'], $godTF['hour']]) && $xyNv['shen']['ji'] == '才财' && $godTzsNv['pc'] >= 3) {
            $poXi[] = '妻子与婆婆关系僵硬，欠缺交流。应多注意沟通，谨慎言辞，各退一步海阔天空。';
        }
        // 日支与年支相冲或者相刑
        if (BaziCommon::getXianChong($jiNianNv['d'][1] . $jiNianNv['y'][1]) || BaziCommon::getXianXin($jiNianNv['d'][1] . $jiNianNv['y'][1])) {
            $poXi[] = '妻子与婆婆相处不理想，或因八字不合。不宜长期同住，以防矛盾升级。';
        }
        // 八字和偏印和七杀≥3且八字财星（正财加偏财）≤1（看藏干）
        if (($godThsNv['py'] + $godThsNv['qs']) >= 3 && ($godThsNv['zc'] + $godThsNv['pc']) <= 1) {
            $poXi[] = '妻子与婆婆关系欠佳，或因性格及处事方式。应自我反省，调整改变。分开居住可减少摩擦。';
        }
        if (empty($poXi)) {
            $poXi[] = '婚后与父母关系和谐，但交流较少，帮助有限。日后生活需夫妻共同努力，无太多话题。';
        }
        return array_merge($result, $poXi);
    }

    /**
     * 婚后生活稳定度
     * @return array
     */
    protected function getHunHuoWenDing(): array
    {
        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];
        $kwNan = explode(',', $this->userInfo[0]['kw']);
        $kwNv = explode(',', $this->userInfo[1]['kw']);
        $list = [
            'y' => '恋爱时热情如火，婚后初期感情冷淡，需提高婚姻幸福感。',
            'm' => '婚前初婚恩爱甜蜜，中年缺少激情，应重新找回热情，提升婚姻幸福感。',
            'h' => '婚前初婚热情满满，晚年熟悉度增加，感情可能冷淡，影响婚姻和谐。',
            'o' => '建议夫妻多沟通，避免感情冷淡。孩子教育、家庭财富等问题应商量，保持感情和谐。',
        ];
        $resWd = $list['o'];
        foreach ($jiNianNan as $k => $v) {
            if ($k == 'd') {
                continue;
            }
            if (in_array($v, $kwNan) || in_array($jiNianNv[$k][1], $kwNv)) {
                $resWd = $list[$k];
                break;
            }
        }
        $ydzNan = $jiNianNan['y'][1];
        $ydzNv = $jiNianNv['y'][1];
        $mdzNan = $jiNianNan['m'][1];
        $ddzNan = $jiNianNan['d'][1];
        $mdzNv = $jiNianNv['m'][1];
        $ddzNv = $jiNianNv['d'][1];
        $hdzNan = $jiNianNan['h'][1];
        $hdzNv = $jiNianNv['h'][1];
        if (BaziCommon::getXianChong($mdzNan . $ddzNan) || BaziCommon::getXianChong($mdzNv . $ddzNv)) {
            $resSh = '婚后与长辈同住，意见不合易产生分歧，影响夫妻感情和睦。建议分开居住，提升生活幸福感。';
        } elseif ($mdzNan == $ddzNan || $mdzNv == $ddzNv) {
            $resSh = '婚后夫妻易受第三者干扰，不一定是外遇，可能是工作同事或他人评论，闹小脾气影响婚姻。';
        } elseif (BaziCommon::getXianXin($mdzNan . $ddzNan) || BaziCommon::getXianXin($mdzNv . $ddzNv)) {
            $resSh = '与父母关系平淡，无语言冲突，但背后表达意见，潜移默化干扰夫妻生活。';
        } elseif (BaziCommon::liuHeDz($mdzNan . $ddzNan) || BaziCommon::liuHeDz($mdzNv . $ddzNv) || BaziCommon::liuHeDz($ydzNan . $ddzNan) || BaziCommon::liuHeDz($ydzNv . $ddzNv)) {
            $resSh = '婚后与父母相处愉快，无代沟，交流无障碍。父母常帮助减轻压力，但需提防陌生人或职场第三者干扰。';
        } elseif (BaziCommon::getXianHai($mdzNan . $ddzNan) || BaziCommon::getXianHai($mdzNv . $ddzNv)) {
            $resSh = '婚后与父母关系不友好，观念和生活习惯差异大，易产生代沟。父母过多干预生活细节，感到烦闷但不至于升级为严重矛盾。';
        } else {
            $resSh = '婚后夫妻生活稳定，与父母沟通相处无大问题。父母不过多干预生活，工作生活中无第三者出现。';
        }
        if (BaziCommon::getXianXin($hdzNan . $ddzNan) || BaziCommon::getXianXin($hdzNv . $ddzNv)) {
            $resqq = '婚后与亲戚关系不亲近，易产生矛盾。虽无言语冲突，但背地不认可生活方式，对方隐秘表达意见，影响夫妻生活。';
        } elseif (BaziCommon::getXianChong($hdzNan . $ddzNan) || BaziCommon::getXianChong($hdzNv . $ddzNv)) {
            $resqq = '婚后与亲戚朋友易摩擦，可能有语言冲突。感情不被看好，与子女也易冲突，孩子叛逆，婚后生活不愉快。';
        } elseif (BaziCommon::getXianPo($hdzNan . $ddzNan) || BaziCommon::getXianPo($hdzNv . $ddzNv)) {
            $resqq = '婚后与亲戚朋友沟通不畅，有孩子后沟通更困难。观念不同导致代沟，相处有阻碍。';
        } elseif (BaziCommon::getXianHai($hdzNan . $ddzNan) || BaziCommon::getXianHai($hdzNv . $ddzNv)) {
            $resqq = '婚后与亲戚朋友不和，因生活态度、习惯不同，意见不统一。一定影响夫妻生活稳定，但不至于升级为严重家庭矛盾。';
        } elseif ($hdzNan == $ddzNan || $hdzNv == $ddzNv) {
            $resqq = '婚后夫妻感情易受熟人打扰，不一定是外遇。熟人对生活有不同看法，想给予意见，但干预导致困扰和厌烦。';
        } else {
            $b1 = BaziCommon::liuHeDz($hdzNan . $ddzNan) || BaziCommon::liuHeDz($hdzNv . $ddzNv) || BaziCommon::getAnHe($hdzNan . $ddzNan) || BaziCommon::getAnHe($hdzNv . $ddzNv);
            $tmpSanHe = BaziCommon::getSanHeDz2([[$hdzNan, $ddzNan], [$hdzNv, $ddzNv]], 0);
            if ($tmpSanHe[2] || $b1) {
                $resqq = '家庭生活美满，婚后与亲朋好友相处融洽，交流无障碍。孩子出生后相处愉快，但需提防熟人第三者介入家庭生活。';
            } else {
                $resqq = '家庭和睦，婚后夫妻生活稳定，与孩子相处稳定，无亲朋好友干预生活。';
            }
        }
        return [
            // 稳定
            $resWd,
            // 生活
            $resSh,
            $resqq,
        ];
    }

    /**
     * 根据性别获得财富
     * @param int $sex 性别 0男 1女
     * @return string[]
     */
    protected function getCaiFuBySex(int $sex): array
    {
        $info = $this->userInfo[$sex];
        $dayun = $info['dayun'];
        $terrainArr = Ex::getTerrainData();
        $jiNian = $info['base']['jinian'];
        $ytg = $jiNian['y'][0];
        $mtg = $jiNian['m'][0];
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $dgz = implode('', $jiNian['d']);
        $cur = $dayun['curr'];
        $next = $dayun['curr'] + 1;
        if ($next > 7) {
            $next = 7;
        }
        $xy = $info['xy'];
        $gzStr = $dayun['eight']['chronlogy_year'][$cur];
        $gzNextStr = $dayun['eight']['chronlogy_year'][$next];
        $gz = Utils::mbStrSplit($gzStr);
        $gzNext = Utils::mbStrSplit($gzNextStr);
        $keT = BaziCommon::getXianKe($gz[0] . $dtg);
        $tong = $gz[0] == $dtg;
        $xin = BaziCommon::getXianXin($gz[1] . $ddz);
        $chong = BaziCommon::getXianChong($gz[1] . $ddz);
        $terrain = $terrainArr[$dtg . $gz[1]];
        $keTNext = BaziCommon::getXianKe($gzNext[0] . $dtg);
        $tongNext = $gzNext[0] == $dtg;
        $xinNext = BaziCommon::getXianXin($gzNext[1] . $ddz);
        $chongNext = BaziCommon::getXianChong($gzNext[1] . $ddz);
        $wxAttr = WuXing::GZ_TO_WX;
        $sanHe = BaziCommon::getSanHeDz2([[$gz[1], $ddz]]);
        $gzTgWx = $wxAttr[$gz[0]];
        $gzDzWx = $wxAttr[$gz[1]];
        $gzTgWxN = $wxAttr[$gzNext[0]];
        $gzDzWxN = $wxAttr[$gzNext[1]];
        $ddzWx = $wxAttr[$ddz];
        $dtgWx = $wxAttr[$dtg];
        $sf = WuXing::getWuxingGuanXi($ddzWx, $dtgWx);
        $boolSf = in_array($sf, ['生', '扶']);
        $kw = explode(',', $info['kw']);
        $hehuaNext = BaziCommon::getHehua($gzNext[0] . $dtg);
        $sanHeNext = BaziCommon::getSanHeDz2([[$gzNext[1], $ddz]]);
        if ($sex) {
            if (!$keT && !$xin && !$chong && !$tong) {
                $key1 = 151;
            } elseif (in_array($gzTgWx, [$xy['xy']['yong'], $xy['xy']['xi']]) && $dgz == $gzStr) {
                $key1 = 153;
            } elseif ($gzTgWx == $xy['xy']['xi'] && BaziCommon::getGodName($gz[0] . $dtg)) {
                $key1 = 154;
            } elseif ($gzTgWx == $xy['xy']['yong'] && $mtg == $gz[0]) {
                $key1 = 155;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $mtg == $gz[0]) {
                $key1 = 156;
            } elseif ($gzTgWx == $xy['xy']['xi'] && $mtg == $gz[0]) {
                $key1 = 157;
            } elseif (empty(array_intersect([$xy['xy']['xi'], $xy['xy']['qiu']], [$gzTgWx, $gzDzWx]))) {
                $key1 = 158;
            } elseif ($dtg == $gz[0]) {
                $key1 = 152;
            } else {
                $key1 = 150;
            }
            if (!in_array($jiNian['y'][1], $kw)) {
                $key2 = $boolSf ? 251 : 252;
            } else {
                $key2 = 250;
            }
            if (!$keTNext && !$tongNext && !$xinNext && !$chongNext) {
                $key3 = 351;
            } elseif ($gzTgWxN == $xy['xy']['yong'] && BaziCommon::getHehua($gzNext[0] . $dtg)) {
                $key3 = 353;
            } elseif ($gzTgWxN == $xy['xy']['ji'] && $gzNext[0] == $dtg) {
                $key3 = 354;
            } elseif (BaziCommon::getXianChong($gzNext[1] . $jiNian['m'][1])) {
                $key3 = 355;
            } elseif ($gzTgWxN == $xy['xy']['yong'] && $gzDzWxN == $xy['xy']['ji'] && empty($hehuaNext) && !$xinNext && !$chongNext) {
                $key3 = 356;
            } elseif ($gzTgWxN == $xy['xy']['ji'] && $gzNext[1] == $ddz) {
                $key3 = 357;
            } elseif ($gzNext[0] == $dtg) {
                $key3 = 352;
            } else {
                $key3 = 350;
            }
        } else {
            if (!$keT && !$xin && !$chong && !$tong) {
                $key1 = 101;
            } elseif (in_array($gzTgWx, [$xy['xy']['yong'], $xy['xy']['xi'], $xy['xy']['qiu']])) {
                $key1 = 102;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $sanHe[2]) {
                $key1 = 103;
            } elseif ($gzTgWx == $xy['xy']['xi'] && BaziCommon::getHehua($ytg . $mtg)) {
                $key1 = 104;
            } elseif ($gzTgWx == $xy['xy']['qiu'] && $gz[0] == $dtg) {
                $key1 = 105;
            } elseif ($gzTgWx == $xy['xy']['xi'] && $terrain == '绝') {
                $key1 = 106;
            } else {
                $key1 = 100;
            }
            if (!in_array($jiNian['y'][1], $kw)) {
                $key2 = $boolSf ? 201 : 202;
            } else {
                $key2 = 200;
            }
            if (!$keT && !$xin && !$chong && !$tong) {
                $key3 = 301;
            } elseif (!$chongNext) {
                $key3 = 302;
            } elseif ($sanHeNext[2]) {
                $key3 = 303;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $gzNext[0] == $dtg) {
                $key3 = 304;
            } elseif ($gzTgWx == $xy['xy']['yong'] && $gzNext[0] == $dtg) {
                $key3 = 305;
            } else {
                $key3 = 300;
            }
        }
        $list = [
            101 => '婚前男方财运平平，工作稳定但难以承担家庭重负。若肯吃苦耐劳，或有可观收入。夫妻共同努力，方可积累财富。',
            201 => '男方条件虽未达伴侣家庭期望，但得贤良伴侣，愿协调关系，共筑美满家庭。',
            301 => '婚后数年，男方财运一般，努力付出或有所回报。工作环境稳定，家庭经济时有紧张。夫妻共同打拼，方能积累更多财富。',
            151 => '婚前经济自足，承担家庭开销或有困难。伴侣相互扶持，共担生活压力，方可达衣食无忧之境。',
            251 => '夫妻宫和谐，与夫家关系融洽，婚姻多得支持。可能获得房产、汽车等资产援助，伴侣亦愿共享财务资源。',
            351 => '婚后女方财运一般，满足生活需求但难以支撑家庭。节俭开支，可享安稳生活。提高经济能力需夫妻共同打拼，相互扶持。',
            102 => '男方婚前财运平平，工作或有阻碍。然能力出众，把握机遇，事业可进展顺利，为婚姻奠定财富基础。',
            202 => '与配偶娘家关系和睦，婚姻多得支持。或获车房等物资援助，伴侣慷慨大方，共享财富。',
            302 => '婚后数年财运不佳，事业收入遇困境，易与上司产生矛盾，降职风险高。需冷静思考，把握时机调整心态。',
            152 => '女方婚前财运亨通，事业有成并扩展人脉。工作思路清晰灵活得亲友相助是积累财富之良机。',
            352 => '婚后运势旺盛得长辈或贵人扶持事业创新高峰。工作发展顺利副业收入丰厚积累超越同龄人之财富。',
            303 => '婚后数年运势低迷工作急躁难以发挥能力易犯错误。需调整心态避免冷漠错失机遇与帮助。',
            153 => '婚后生活美满得长辈或职场贵人相助人际关系广阔。事业发展顺利灵活变通带来新机遇是积累财富之佳期。',
            103 => '婚前运势旺盛事业发展顺利收入丰厚易积累财富。身边兄弟朋友是贵人时常得到帮助。',
            104 => '命喜星合婚前大运不佳男方工作难以发挥才能易犯错误影响收入。心态未调整好言行易冷淡烦躁疏远身边人影响机会与助力。',
            154 => '女方婚前运势欠佳经济自足但难以支撑家庭开销。事业需付出更多努力得父母有限支持日后仍需与伴侣共同努力打拼。',
            353 => '婚后运势一般财富受多方面因素影响。婚后数年事业或遇阻碍工作变动需家人支持帮助。',
            155 => '女方婚后运势旺盛事业发展迅速个人能力突出有责任心投入工作打下良好经济基础积蓄丰厚。',
            156 => '婚前急于积累财富心态急切事业易受阻碍忽略亲友难以得到帮助。开销大增影响婚前财富积累需调整心态。',
            354 => '婚后因增加家庭经济收入而心态浮躁易为财所困忽略亲友关系工作中常犯小错影响事业发展。需调整状态限制开销。',
            105 => '男方婚前运势欠佳工作竞争激烈遇困难难以与同事建立信任交际开支增加。需调整状态防小人陷害寻找解决方法。',
            304 => '婚后运势不佳事业易受小人阻碍同事间缺乏信任面对失误易自责情绪压抑生活压力大交际开销增加。需调整状态与伴侣多沟通交流。',
            157 => '女方婚后运势旺盛能力强负责工作遇困难能灵活解决迎来事业高速发展期通常拥有比同龄人更丰富的基础。',
            355 => '婚后数年财运平淡精神紧张易因琐事胡思乱想陷入负面情绪。美妆服装开销大易受商家陷阱影响经济遇困。需调整心态理性消费与伴侣共同规划家庭财务。',
            106 => '男方整体运势欠佳事业或停滞不前工作状态差做事急躁自我负担重易产生压力。需调整情绪平和度过困难时期。',
            305 => '婚后运势旺盛有机会得到长辈或职场贵人相助事业发展顺利副业易有成就家庭积蓄丰厚。',
            158 => '女方婚前经济自足但支撑家庭稍显吃力建议与伴侣共同商议同居及开支规划避免无谓花销做好理财储蓄为婚姻打下良好基础。',
            356 => '婚后心态转变注重家庭节约开销学会比价购物夫妻话题围绕家常。事业心或有所下降或受阻于家庭因素影响难以有大突破可学习新技能或提升学历以备后用。',
            159 => '婚前运势不佳事业竞争激烈工作状态不稳定易受他人言行影响投入工作困难收入受损日常生活压力大或因病破财需调整情绪加强沟通。',
            252 => '男方家庭支持有限初期负担家庭吃力需个人努力创造婚姻价值婆媳相处需多加忍耐协调以维护家庭和谐。',
            357 => '婚后运势不佳工作易受他人影响完成度事业竞争激烈收入或受损生活压力大或因病破财需调整心态减轻压力以应对困境。',
            200 => '虽条件未达伴侣家庭期望但得贤良伴侣愿意协调关系共同经营家庭与生活以创造美满未来。',
            150 => '女方婚前运势欠佳经济能力有限难以满足家庭开销需求事业需付出更多努力日常工作得父母有限支援日后仍需与伴侣共同努力以改善经济状况。',
            100 => '男方婚前财运一般满足个人需求但难以承担家庭重任积累财富需夫妻共同努力相互鼓励虽与理想有差距但不愁衣食生活安稳。',
            250 => '婚后男方家庭给予部分帮助但初期负担仍重需个人努力创造更多婚姻价值婆媳关系需耐心协调以维护家庭和睦。',
            300 => '婚后数年男方财运一般付出努力可有所回报工作环境稳定家庭经济虽有压力但可承担积累更多财富仍需夫妻共同打拼相互扶持共进。',
            350 => '女方婚后财运不理想满足基本生活需求但与理想有差距若不追求高质量生活亦可过得安稳滋润提高经济能力仍需夫妻共同经营相互包容鼓励共渡难关。',
        ];
        return [
            $list[$key1],
            $list[$key2],
            $list[$key3],
        ];
    }

    /**
     * 感情运
     * @param int $sex 性别 0男 1女
     * @return array
     */
    protected function getGanQing(int $sex): array
    {
        $gz = BaziExt::getGanZhi($this->yearO);
        $info = $this->userInfo[$sex];
        $jiNian = $info['base']['jinian'];
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $arr = [
            [$ddz, $gz[1]],
        ];
        $hez = BaziCommon::liuHeDz($gz[1] . $ddz);
        $sanHe = BaziCommon::getSanHeDz2($arr);
        $anhe = BaziCommon::getAnHe($gz[1] . $ddz);
        $chong = BaziCommon::getXianChong($gz[1] . $ddz);
        $xin = BaziCommon::getXianXin($gz[1] . $ddz);
        $hai = BaziCommon::getXianHai($gz[1] . $ddz);
        $po = BaziCommon::getXianPo($gz[1] . $ddz);
        $ke = BaziCommon::getXianKe($gz[1] . $ddz);
        $result = [];
        if ($hez || isset($sanHe[2])) {
            $result[] = '今年单身的你有成婚之念，对未来充满期待，寻求心仪的伴侣。桃花旺盛时，与异性保持适当距离，深化了解喜欢的人，维持良好的交往关系。';
            $result[] = $sex ? '热恋中的你，今年可考虑与伴侣步入婚姻，婚后感情将更上一层楼。' : '已婚者或有生育打算，期待孩子的到来，或正积极备孕，或有意外之喜。';
        }
        if ($anhe) {
            $result[] = '单身的你在今年对感情和婚姻的态度略显迷茫，既追求完美的恋爱又不愿过早成婚，享受恋爱的过程。期待与喜欢的人共筑爱巢，开启新生活。';
            if ($sex) {
                $result[] = '热恋中的你今年需为感情注入新鲜活力，展现温柔体贴的一面。已婚者或有意外怀孕之喜，对伴侣的态度更加柔和顺从。';
            }
        }
        if ($chong) {
            $result[] = '单身的你在今年对感情充满冲劲，渴望建立家庭，迅速寻找合适的伴侣成婚。在面对喜欢的人时可能难以把握距离，感情变化迅速。';
            $result[] = $sex ? '热恋中的你今年是成婚的好时机，可与伴侣及双方父母商讨婚事。' : '已婚者的感情生活将增添趣味，有机会与伴侣共度甜蜜时光，深化感情。';
        }
        if ($hai) {
            $result[] = '单身的你在今年对感情的态度矛盾重重，既渴望自由恋爱又想安定下来。建议在与异性交往时谨慎表达情感，避免误解以利于感情发展。';
            $result[] = $sex ? '热恋中的你对伴侣的期望更高，追求完美婚礼和婚姻生活。' : '已婚者则期望伴侣在事业上取得更大成就为家庭提供更多保障。';
        }
        // 日支与流年地支无刑冲克害合破关系
        if (!($xin || $chong || $ke || $hai || $po || $anhe || $hez || isset($sanHe[2]))) {
            if ($sex) {
                $result[] = '热恋中的你今年可能暂不急于成婚或受到外部因素影响。已婚者今年可能没有明确的生育计划但也可能会有意外之喜。';
                $result[] = '今年你将更加考验对方的爱意和态度以确定是否适合步入婚姻。不论已婚与否都有机会深入了解伴侣的过去和社交圈以促进感情发展。';
            } else {
                $result[] = '热恋中的你今年可能对婚姻感到准备不足担心承担责任。若伴侣希望今年成婚请给予足够的支持。已婚者期望婚姻生活稳定平淡双方坦诚沟通。';
                $result[] = '今年对你来说感情生活较为平淡暂无结婚打算也不受父母催促。已婚者将大部分时间与伴侣共度减少了社交活动但感情稳定生活圈子可能略显单调。';
            }
        }
        if (empty($result)) {
            $result[] = $sex ? '热恋中的你今年感情波动不大对伴侣的依赖和情感寄托有所减弱但感情基础依然稳固。' : '与异性朋友的交往保持适当距离避免产生不必要的误会。';
        }
        $godLT = BaziCommon::getGodName($dtg . $gz[0]);
        $godtLd = BaziCommon::getGodName($ddz . $gz[1]);
        if (!array_intersect(['正官', '七杀'], [$godLT, $godtLd])) {
            $result[] = $sex ? '热恋中的你今年在感情上显得较为被动减少了与伴侣的相处时间和交流频率但这并不影响你对伴侣的深厚感情。' : '请保持自律避免产生不忠的想法即使没有其他桃花出现。';
        }
        return $result;
    }

    /**
     * 日柱合分
     * @return float
     */
    protected function getFenByDay(): float
    {
        // 纪年
        $jiNianNan = $this->userInfo[0]['base']['jinian'];
        $jiNianNv = $this->userInfo[1]['base']['jinian'];
        $dtgNan = $jiNianNan['d'][0];
        $dtgNv = $jiNianNv['d'][0];
        $ddzNan = $jiNianNan['d'][1];
        $ddzNv = $jiNianNv['d'][1];
        // 喜用神
        $xyNan = $this->userInfo[0]['xy'];
        $xyNv = $this->userInfo[1]['xy'];
        $yongXiNan = [$xyNan['xy']['yong'], $xyNan['xy']['xi']];
        $yongXiNv = [$xyNv['xy']['yong'], $xyNv['xy']['xi']];
        // 关系
        $gxNan = $this->userInfo[0]['gx'];
        $gxNv = $this->userInfo[1]['gx'];
        // 干支对应的五行
        $wxAttr = WuXing::GZ_TO_WX;
        $ddzNanWx = $wxAttr[$ddzNan];
        $ddzNvWx = $wxAttr[$ddzNv];
        $listTg = [
            '甲甲' => 3.5, '甲乙' => 3.5, '甲丙' => 5, '甲丁' => 4.5, '甲戊' => 1, '甲己' => 5,
            '甲庚' => 0, '甲辛' => 3.5, '甲壬' => 5, '甲癸' => 4.5, '乙甲' => 3.5, '乙乙' => 3.5,
            '乙丙' => 4.5, '乙丁' => 5, '乙戊' => 3.5, '乙己' => 1, '乙庚' => 5, '乙辛' => 0,
            '乙壬' => 4.5, '乙癸' => 5, '丙甲' => 5, '丙乙' => 4.5, '丙丙' => 3.5, '丙丁' => 3.5,
            '丙戊' => 5, '丙己' => 4.5, '丙庚' => 1, '丙辛' => 5, '丙壬' => 0, '丙癸' => 3.5,
            '丁甲' => 4.5, '丁乙' => 5, '丁丙' => 3.5, '丁丁' => 3.5, '丁戊' => 4.5, '丁己' => 5,
            '丁庚' => 3.5, '丁辛' => 1, '丁壬' => 5, '丁癸' => 0, '戊甲' => 1, '戊乙' => 3.5,
            '戊丙' => 5, '戊丁' => 4.5, '戊戊' => 3.5, '戊己' => 3.5, '戊庚' => 5, '戊辛' => 4.5,
            '戊壬' => 1, '戊癸' => 5, '己甲' => 5, '己乙' => 1, '己丙' => 4.5, '己丁' => 5,
            '己戊' => 3.5, '己己' => 3.5, '己庚' => 4.5, '己辛' => 5, '己壬' => 3.5, '己癸' => 1,
            '庚甲' => 0, '庚乙' => 5, '庚丙' => 1, '庚丁' => 3.5, '庚戊' => 5, '庚己' => 4.5,
            '庚庚' => 3.5, '庚辛' => 3.5, '庚壬' => 5, '庚癸' => 4.5, '辛甲' => 3.5, '辛乙' => 0,
            '辛丙' => 5, '辛丁' => 1, '辛戊' => 4.5, '辛己' => 5, '辛庚' => 3.5, '辛辛' => 3.5,
            '辛壬' => 4.5, '辛癸' => 5, '壬甲' => 5, '壬乙' => 4.5, '壬丙' => 0, '壬丁' => 5,
            '壬戊' => 1, '壬己' => 3.5, '壬庚' => 5, '壬辛' => 4.5, '壬壬' => 3.5, '壬癸' => 3.5,
            '癸甲' => 4.5, '癸乙' => 5, '癸丙' => 3.5, '癸丁' => 0, '癸戊' => 5, '癸己' => 1,
            '癸庚' => 4.5, '癸辛' => 5, '癸壬' => 3.5, '癸癸' => 3.5,
        ];
        $fen = $listTg[$dtgNan . $dtgNv] ?? 0;
        // 相合 1 相冲 2 相破 3 相刑 4 相害 5 生地 6 墓地 7 其它 8
        // 合为喜用 1 合为忌 2  男喜用 女忌 3   男忌 女喜用 4
        // 他支冲克  无 1 都有 2  男有 女无 3  男无女有 4
        $listDz = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 3, 124 => 3,
            131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 1, 23 => 0.5, 24 => 0.5, 31 => 1, 32 => 2, 33 => 1.5, 34 => 1.5,
            41 => 0.5, 42 => 1.5, 43 => 1, 44 => 1, 51 => 0, 52 => 0.5, 53 => 0.25, 54 => 0.25,
            611 => 5, 612 => 2, 613 => 3, 614 => 3, 621 => 0, 622 => 4, 623 => 3, 624 => 3,
            631 => 3, 632 => 3, 633 => 1, 634 => 4, 641 => 3, 642 => 3, 643 => 4, 644 => 1,
            711 => 5, 712 => 2, 713 => 3, 714 => 3, 721 => 0, 722 => 4, 723 => 3, 724 => 3,
            731 => 3, 732 => 3, 733 => 1, 734 => 4, 741 => 3, 742 => 3, 743 => 4, 744 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        $he = BaziCommon::getHehua($ddzNan . $ddzNvWx);
        // 1有 2 无
        $ckNan = array_intersect(['冲', '被克'], $gxNan['d']['d']) ? 1 : 2;
        $ckNv = array_intersect(['冲', '被克'], $gxNv['d']['d']) ? 1 : 2;
        $list11 = [22 => 1, 11 => 2, 12 => 3, 21 => 4];
        $num3 = $list11[$ckNan . $ckNv];
        // 1 喜用 2 忌闲仇
        $b1 = in_array($ddzNanWx, $yongXiNv) ? 1 : 2;
        $b2 = in_array($ddzNvWx, $yongXiNan) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b1 . $b2];
        $sanShen = $this->getSanHeSm($ddzNan . $ddzNv);
        $sanDi = $this->getSanHeSm($ddzNan . $ddzNv, 1);
        $str = 0;
        if ($he) {
            $num2 = $this->getGx3($he);
            $str = (int)("1{$num2}{$num3}");
        } elseif (BaziCommon::getXianChong($ddzNan . $ddzNv)) {
            $str = (int)("2{$num4}");
        } elseif (BaziCommon::getXianPo($ddzNan . $ddzNv)) {
            $str = (int)("3{$num4}");
        } elseif (BaziCommon::getXianXin($ddzNan . $ddzNv)) {
            $str = (int)("4{$num4}");
        } elseif (BaziCommon::getXianHai($ddzNan . $ddzNv)) {
            $str = (int)("5{$num4}");
        } elseif ($sanShen) {
            $num2 = $this->getGx3($sanShen);
            $str = (int)("6{$num2}{$num3}");
        } elseif ($sanDi) {
            $num2 = $this->getGx3($sanDi);
            $str = (int)("7{$num2}{$num3}");
        } else {
            $str = (int)("8{$num4}");
        }
        $fen1 = $listDz[$str] ?? 0;
        $fen += $fen1;
        return $fen;
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'ss' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }

    /**
     * 五行和喜用关系
     * @param string $wx
     * @return int
     */
    protected function getGx3(string $wx): int
    {
        $xyNan = $this->userInfo[0]['xy'];
        $xyNv = $this->userInfo[1]['xy'];
        $yongXiM = [$xyNan['xy']['yong'], $xyNan['xy']['xi']];
        $yongXiF = [$xyNv['xy']['yong'], $xyNv['xy']['xi']];
        // 合为喜用 1 合为忌 2  男喜用 女忌 3   男忌 女喜用 4
        $b1 = in_array($wx, $yongXiF);
        $b2 = in_array($wx, $yongXiM);
        $res = 0;
        if ($b1 && $b2) {
            $res = 1;
        } elseif (!$b1 && !$b2) {
            $res = 2;
        } elseif ($b1 && !$b2) {
            $res = 3;
        } elseif ($b2 && !$b1) {
            $res = 4;
        }
        return $res;
    }

    /**
     * 干支关系统计
     * @param array $gx
     * @return array
     */
    protected function getGxCount(array $gx): array
    {
        $arrT = array_merge($gx['y']['t'], $gx['m']['t'], $gx['d']['t'], $gx['h']['t']);
        $arrD = array_merge($gx['y']['d'], $gx['m']['d'], $gx['d']['d'], $gx['h']['d']);
        return [
            't' => array_count_values($arrT),
            'd' => array_count_values($arrD),
        ];
    }

    /**
     * 生墓地半三合
     * @param string $str 地支+地支
     * @param int $type 0 生地 1 墓地
     * @return string
     */
    protected function getSanHeSm(string $str, int $type = 0): string
    {
        $str = Utils::sortGz($str);
        $list = [
            // 生地
            ['子申' => '水', '卯亥' => '木', '寅午' => '火', '巳酉' => '金'],
            // 墓地
            ['子辰' => '水', '卯未' => '木', '午戌' => '火', '丑酉' => '金'],
        ];
        return $list[$type][$str] ?? '';
    }

    /**
     * 八败煞
     * @param string $str 年支+月支
     * @return bool
     */
    protected function checkBaBiSha(string $str): bool
    {
        $list = [
            '子未', '丑戌', '寅丑', '卯丑', '辰未', '巳未', '午丑', '未辰', '申戌', '酉戌', '戌辰', '亥辰',
        ];
        return in_array($str, $list);
    }

    /**
     * 望门寡
     * @param string $str 年纳音五行+月支
     * @return bool
     */
    protected function checkWangMenGua(string $str): bool
    {
        $list = ['金申', '木巳', '水亥', '火寅', '土巳'];
        return in_array($str, $list);
    }

    /**
     * 骨髓破
     * @param string $str 年支+月支
     * @param int $sex 性别 性别 0男 1女
     * @return bool
     */
    protected function checkGuShuiPo(string $str, int $sex): bool
    {
        $list = [
            ['子卯', '丑辰', '寅亥', '卯午', '辰丑', '巳寅', '午酉', '未戌', '申巳', '酉子', '戌未', '亥申'],
            ['子未', '丑巳', '寅辰', '卯寅', '辰未', '巳巳', '午辰', '未寅', '申未', '酉巳', '戌辰', '亥寅'],
        ];
        return in_array($str, $list[$sex]);
    }

    /**
     * 铁扫帚（需要分男女）
     * @param string $str 年支+月支
     * @param int $sex 性别 0男 1女
     * @return bool
     */
    protected function checkTieSaoZhou(string $str, int $sex): bool
    {
        $list = [
            [
                '子寅', '丑未', '寅巳', '卯卯', '辰寅', '巳未', '午巳', '未卯', '申寅', '酉未', '戌巳', '亥卯',
            ],
            [
                '子未', '丑巳', '寅辰', '卯寅', '辰未', '巳巳', '午辰', '未寅', '申未', '酉巳', '戌辰', '亥寅',
                '子丑', '丑戌', '寅申', '卯酉', '辰丑', '巳戌', '午申', '未酉', '申丑', '酉戌', '戌申', '亥酉',
            ],
        ];
        return in_array($str, $list[$sex]);
    }

    /**
     * 天扫星
     * @param string $str 年干
     * @param string $gz 日柱或时柱
     * @return bool
     */
    protected function checkTianSaoXin(string $str, string $gz): bool
    {
        $list = ['甲癸未', '乙壬午', '丙辛巳', '丁庚辰', '戊己卯', '己戊寅', '庚丁丑', '辛丙子', '壬乙亥', '癸甲戌'];
        return in_array($str . $gz, $list);
    }

    /**
     * 地扫星
     * @param string $dwx 日主五行
     * @param string $mdz 月支
     * @return bool
     */
    protected function checkDiSaoXin(string $dwx, string $mdz): bool
    {
        $list = [
            '金' => ['午', '未', '申'], '木' => ['卯', '辰', '巳'],
            '水' => ['酉', '戌', '亥'], '火' => ['子', '丑', '寅'],
            '土' => ['卯', '辰', '巳'],
        ];
        return in_array($mdz, $list[$dwx]);
    }
}
