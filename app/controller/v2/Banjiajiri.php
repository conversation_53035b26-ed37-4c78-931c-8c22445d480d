<?php
// +----------------------------------------------------------------------
// | Banjiajiri. 搬家吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\lib\Utils;
use app\traits\jiri\JiRiCheckBadTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Banjiajiri
{
    use JiRiCheckTraits;
    use JiRiCheckBadTraits;

    /**
     * 日历基础
     * @var Ex
     */
    protected Ex $lunar1;

    /**
     * @var Ex|null
     */
    protected ?Ex $lunar2 = null;

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户冲刑地支
     * @var array
     */
    protected array $chongXingDz = [];

    /**
     * 年份对应的节气信息
     * @var array
     */
    protected array $jieQiYear = [];

    /**
     * 搬家吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time1' => input('time1', '', 'trim'),
            'sex1' => input('sex1', 0, 'intval'),
            // 出生时间2
            'time2' => input('time2', '', 'trim'),
            'sex2' => input('sex2', 1, 'intval'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 默认3个月
            'longs' => input('longs', 3, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time1|出生时间1' => ['require', 'isDateOrTime:出生时间1'],
                'sex1|性别1' => ['require', 'in:0,1'],
                'time2|出生时间2' => ['isDateOrTime:出生时间2'],
                'sex2|性别2' => ['in:0,1'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'longs|时间区间' => ['require', 'between:1,25'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar1 = Ex::date($data['time1'])->sex($data['sex1']);
        $pan = [
            $this->getBazi($this->lunar1),
        ];
        if (!empty($data['time2'])) {
            $this->lunar2 = Ex::date($data['time2'])->sex($data['sex2']);
            $pan[] = $this->getBazi($this->lunar2);
        }
        $this->chongXingDz = $this->getChongXing();
        $result = [
            'pan' => $pan,
            'chong_xing' => $this->chongXingDz['all'],
            'ji' => $this->getDayList(),
        ];
        return $result;
    }

    /**
     * 八字基础信息
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBazi(Ex $lunar): array
    {
        $base = $lunar->getLunarByBetween();
        $res = [
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
        ];
        $res = array_merge($base, $res);
        return $res;
    }

    /**
     * 获得跟用户年柱冲刑地支
     * @return array
     */
    protected function getChongXing(): array
    {
        $ygz = $this->lunar1->getLunarGanzhiYear();
        $ydz = [$ygz[1]];
        if ($this->lunar2) {
            $ygz = $this->lunar2->getLunarGanzhiYear();
            $ydz[] = $ygz[1];
        }
        $resChong = [];
        $resXing = [];
        foreach ($ydz as $dz) {
            $chongDz = BaziCommon::getChongDz($dz);
            $resChong[$chongDz] = $chongDz;
            $xing = BaziCommon::getXingByDz($dz);
            foreach ($xing as $v) {
                $resXing[$v] = $v;
            }
        }
        $resAll = array_values(array_merge($resChong, $resXing));
        return [
            'chong' => array_values($resChong),
            'xing' => array_values($resXing),
            'all' => $resAll,
        ];
    }

    /**
     * 获得日子列表
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        $otime = strtotime($this->orginData['otime']);
        $long = $this->orginData['longs'];
        $maxTime = strtotime("+{$long}month", $otime);
        $limitNum = round(($maxTime - $otime) / 86400);
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳节',
            '10_1' => '寒衣节',
        ];
        $result = [];
        $explain = [];
        $changeExplain = [];
        $chongXinDz = $this->chongXingDz;
        $listHour = $this->getHourUse();
        $jiNianU = $this->lunar1->getLunarTganDzhi();
        $ydzU = [$jiNianU['y'][1]];
        $noHourSiMing = ["酉", "戌", "亥"];
        if ($this->lunar2) {
            $jiNianU2 = $this->lunar2->getLunarTganDzhi();
            $ydzU[] = $jiNianU2['y'][1];
        }
        for ($i = 0; $i < $limitNum; $i++) {
            $curTime = $otime + ($i * 86400);
            $cYear = date('Y', $curTime);
            $cGongli = date('Y-m-d', $curTime);
            $keyStr = date('Y-m', $curTime);
            $jieqiArr = $this->getJieqiByYear($cYear);
            $jieQi = $jieqiArr[$cGongli] ?? '';
            $huangli = Huangli::date($cGongli);
            $base = $huangli->getLunarByBetween();
            $week = Huangli::getWeekChs($curTime);
            $jxArr = $huangli->getJiXiong();
            $jiShenHl = $this->filterJXShen($jxArr['jishen']);
            $xiongHl = $this->filterJXShen($jxArr['xiong'], 1);
            $zhiRi = $huangli->getZhiRi();
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $hdType = $zhiRi['huan_dao'] == '黑道' ? 0 : 1;
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $mdNum = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            $ydz = $jiNian['y'][1];
            $mdz = $jiNian['m'][1];
            $ddz = $jiNian['d'][1];
            $dtg = $jiNian['d'][0];
            $ytg = $jiNian['y'][0];
            $dzMd = $mdz . $ddz;
            $dgz = implode('', $jiNian['d']);
            $reason = [];
            $reasonJr = [];
            $xiongRes = [];
            $xingChongArr = [];
            if (in_array($ddz, $chongXinDz['chong'])) {
                $xingChongArr[] = '冲';
            }
            if (in_array($ddz, $chongXinDz['xing'])) {
                $xingChongArr[] = '刑';
            }
            if ($this->checkYangGongOnly($mdNum)) {
                $reasonJr[] = '杨公忌日';
            }
            if ($jieQi == '清明') {
                $reasonJr[] = '清明节';
            }
            foreach ($listNo as $k => $v) {
                if ($k == $mdNum) {
                    $reasonJr[] = $v;
                }
            }
            if ($this->checkSuiPo($ydz . $ddz)) {
                $reason[] = '岁破';
            }
            if ($this->checkShouSi($dzMd)) {
                $reason[] = '受死';
            }
            //不可变凶神月厌、归忌、往亡、月破、四废
            //            if ($this->checkYueYan($mdz, $ddz)) {
            //                $xiongRes[] = '月厌';
            //            }
            if ($this->checkGuiJi($mdz, $ddz)) {
                $xiongRes[] = '归忌';
            }
            if ($this->checkWangWang($mdz, $ddz)) {
                $xiongRes[] = '往亡';
            }
            if ($this->checkYuePo($dzMd)) {
                $xiongRes[] = '月破';
            }
            if ($this->checkSiFei($mdz, $dgz)) {
                $xiongRes[] = '四废';
            }
            $tomorrow = date('Y-m-d', $curTime + 86400);
            $jqNext = $jieqiArr[$tomorrow] ?? '';
            if (in_array($jqNext, ["立春", "立夏", "立秋", "立冬"])) {
                // 四绝
                $reason[] = '四绝';
            } elseif (in_array($jqNext, ["春分", "秋分", "夏至", "冬至"])) {
                $reason[] = '四离';
            }
            $tianDe = $this->checkTianDe($dzMd) || $this->checkTianDe($mdz . $dtg);
            $yueDe = $this->checkYueDe($dzMd) || $this->checkYueDe($mdz . $dtg);
            $tianDeHe = $this->checkTianDeHe($dzMd) || $this->checkTianDeHe($mdz . $dtg);
            $yueDeHe = $this->checkYueDeHe($dzMd) || $this->checkYueDeHe($mdz . $dtg);
            $tianYuan = $this->checkTianYuan($mdz . $dgz);
            $deArray = [];
            if ($tianDe) {
                $deArray[] = '天德';
            }
            if ($yueDe) {
                $deArray[] = '月德';
            }
            if ($tianDeHe) {
                $deArray[] = '天德合';
            }
            if ($yueDeHe) {
                $deArray[] = '月德合';
            }
            $jieSha2 = $this->checkJieSha2($mdz, $deArray) ? 1 : 0;
            $zaiSha2 = $this->checkZaiSha2($mdz, $deArray) ? 1 : 0;
            $daShi2 = $this->checkDaShi2($mdz, $deArray) ? 1 : 0;
            $tianLi2 = $this->checkTianLi2($mdz, $deArray) ? 1 : 0;
            $wuMu2 = $this->checkWuMu2($mdz, $deArray) ? 1 : 0;
            $yueXing2 = $this->checkYueXin2($mdz, ($yueDe || $tianDeHe || $tianYuan)) ? 1 : 0;
            $yueSha2 = $this->checkYueSha2($mdz, $deArray) ? 1 : 0;
            $change = [0 => [], 1 => []];//0 普通  1 可变
            if ($this->checkJieSha($mdz, $ddz)) {
                $change[$jieSha2][] = '劫煞';
            }
            if ($this->checkZaiSha($mdz, $ddz)) {
                $change[$zaiSha2][] = '灾煞';
            }
            // 大时 大败 咸池
            if ($this->checkDaShi($mdz, $ddz)) {
                $change[$daShi2][] = '大时';
            }
            // 致死（天吏）
            if ($this->checkTianLi($mdz, $ddz)) {
                $change[$tianLi2][] = '天吏';
            }
            //            if ($this->checkWuMu($mdz, $dgz)) {
            //                $change[$wuMu2][] = '五墓';
            //            }
            //            if ($this->checkYueXin($mdz, $ddz)) {
            //                $change[$yueXing2][] = '月刑';
            //            }
            if ($this->checkYueSha($mdz, $ddz)) {
                $change[$yueSha2][] = '月煞';
            }
            $xiongRes = array_merge($xiongRes, $change[0]);
            $reason = array_merge($reason, $xiongRes);
            $bool = $xingChongArr || $reason || $reasonJr;
            $sanSha = Huangli::getSanSha($ddz);
            $chongDz = BaziCommon::getChongDz($ddz);
            $chongSx = $this->lunar1->getZodiac($chongDz);
            // $a[$cGongli] = implode(',', $reason);
            $gongsha = $this->huiTouGongShaLiu($jiNian);
            // 去掉回头贡杀
            if (in_array($gongsha, $ydzU)) {
                $bool = true;
            }
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $curTime),
                    'm' => (int)date('m', $curTime),
                    'd' => (int)date('d', $curTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']),
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'type' => $bool ? 0 : 1, // 0 不合 1 符合
                // 'type_cn' => $bool ? '不宜' : '平',
                'type_hl' => $hdType, // 黄道 1 黑道 0
                'position' => $huangli->getPosition(),
                'jianchu' => $huangli->getJianChu(), //建除
                'shensha' => $shenSha, // 值日神煞
                // 煞向
                'sha_xian' => str_replace('煞', '', $sanSha[0]),
                // 日冲
                'chong_sx' => $chongSx,
                // 与用户相冲或相刑结果
                'chong_x_u' => $xingChongArr,
                // 原因
                'reason' => $reason,
                'change' => [],
                'gongsha' => $gongsha,
                // 农历节日过滤
                'fest' => $reasonJr,
            ];
            $jiShenRes = $deArray;
            $dayHour = [];
            if (!$bool) {
                $type = 1;
                $tmpRes['change'] = $change[1];
                if ($hdType || $change[1]) {
                    $type = 2;
                }
                if ($this->checkYueEn($mdz . $dtg)) {
                    $jiShenRes[] = '月恩';
                }
                if ($tianYuan) {
                    $jiShenRes[] = '天愿';
                }
                if ($this->checkTianSe($mdz, $dtg)) {
                    $jiShenRes[] = '天赦';
                }
                if ($jiShenRes) {
                    $type = $hdType ? 4 : 3;
                }
                $reasonZhong = [];
                // 驿马 四相 时德  民日 天马 六合 人专 贡煞 三合 岁德 岁德合
                if ($this->checkYiMa($mdz, $ddz)) {
                    $reasonZhong[] = '驿马';
                }
                if ($this->checkSiXian($mdz, $dtg)) {
                    $reasonZhong[] = '四相';
                }
                if ($this->checkMingRi($mdz, $ddz)) {
                    $reasonZhong[] = '民日';
                }
                if ($this->checkTianMa($mdz, $ddz)) {
                    $reasonZhong[] = '天马';
                }
                if (BaziCommon::liuHeDz($mdz . $ddz)) {
                    $reasonZhong[] = '六合';
                }
                $sanHeDz = [
                    [$ydz, $mdz, $ddz],
                ];
                $sanHe = BaziCommon::getSanHeDz2($sanHeDz);
                if (!empty($sanHe[3])) {
                    $reasonZhong[] = '三合';
                }

                if ($this->checkSuiDe($ytg . $dtg)) {
                    $reasonZhong[] = '岁德';
                }
                if ($this->checkSuiDeHe($ytg . $dtg)) {
                    $reasonZhong[] = '岁德合';
                }
                if ($this->checkWangRi($mdz, $ddz)) {
                    $reasonZhong[] = '王日';
                }
                if ($this->checkGuanRi($mdz, $ddz)) {
                    $reasonZhong[] = '官日';
                }
                if ($this->checkShouRi1($mdz, $ddz)) {
                    $reasonZhong[] = '守日';
                }
                if ($this->checkXiangRi($mdz, $ddz)) {
                    $reasonZhong[] = '相日';
                }
                if ($reasonZhong) {
                    $jiShenRes = array_merge($jiShenRes, $reasonZhong);
                    if ($type != 4) {
                        $type = 3;
                    }
                }
                $dayHour = $listHour[$ddz];
                if ($shenSha == '司命') {
                    foreach ($noHourSiMing as $dzNo) {
                        unset($dayHour[$dzNo]);
                    }
                }
                $tmpRes['type'] = $type;
                $tmpRes['reason'] = $jiShenRes;
            }
            $tmpRes['shen'] = [
                'jishen' => array_values(array_unique(array_merge($jiShenRes, $jiShenHl))),
                'xiong' => array_values(array_unique(array_merge($xiongRes, $xiongHl))),
            ];
            $tmpRes['hour'] = $dayHour;
            $num = $result[$keyStr]['num'] ?? 0;
            if (!$bool) {
                $num++;
            }
            $result[$keyStr]['num'] = $num;
            $explain[$shenSha] = $this->getExplain($shenSha);
            foreach ($tmpRes['fest'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['reason'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['change'] as $v) {
                $changeExplain[$v] = $this->getExplain($v, true);
            }
            $result[$keyStr]['info'][] = $tmpRes;
        }
        return [
            'list' => array_values($result),
            'explain' => $explain,
            'change' => $changeExplain,
        ];
    }

    /**
     * 获得解释
     * @param string $str
     * @param bool $bool 固定false 可变 true
     * @return string
     */
    protected function getExplain(string $str, bool $bool = false): string
    {
        if ($bool) {
            // 可变
            $list = [
                '劫煞' => '劫煞，是一年的阴气，主管杀伐和伤害，寓意不吉。当值之日忌讳成亲、行军、出行、搬家、兴造。但当月与天德，月德，天德合，月德合和三合其中之一并临时，只忌安抚边境、选将训兵、出师、求医疗病，余者都不忌讳',
                '灾煞' => '灾煞，主管灾难、疾病、困厄之事。当值之日禁忌搬家、出行、营造。但如果在当月遇到天、月德或天、月德合时，则不忌搬家。',
                '大时' => '大时：又称大败，属于绝败之日。因为五行到此无生机，是属于较凶的日子。所值当日禁忌搬家、筑室、会亲。但如果在当月遇到天、月德、天、月德合、官日、六合之一时，则不忌搬家。',
                '天吏' => '天吏（致死），指的是天上的凶吏，全没有生机的意思。当值之日禁忌赴任、远行、搬家。当月遇到天、月德或天、月德合大吉神时，凶性被化，不忌搬家。',
                '五墓' => '五墓，代表五行的旺气临于墓地，寓意不吉。所值之日，忌营造、嫁娶、搬动等事宜。但它逢月德时，则不忌讳搬家。',
                '月刑' => '月刑有刑伤之意，因而该日多主不吉，犯之则多有争斗，忌讳搬家。但临巳月，又遇到当月大吉之神吉神，吉神庇佑，不忌搬家。',
            ];
        } else {
            $list = [
                '清明节' => '该日乃是用来扫坟祭祖的，怀缅先辈的日子，不适宜搬家。',
                '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，寓意不吉。',
                '重阳节' => '当日有大事勿用的说法，忌讳搬家。',
                '寒衣节' => '寒衣节乃是传统的四大鬼节之一，寓意不吉。',
                '六合' => '六合，就是日、月与星宿相合的时日。六合当值日适宜会宾客、结婚姻、移徙搬家。',
                '人专' => '人专为三吉星之一，所值当日百事大吉，适宜搬家。',
                '贡煞' => '贡煞为三吉星之一，所值当日百事大吉，适宜搬家。',
                '三合' => '三合，是指不同位而同气。三合之日办事有利，适宜搬家。',
                '岁德' => '所谓岁德，就是岁中的德神。所理值之日适宜修建营造、移徙搬家。',
                '岁德合' => '岁德合，是岁德五合的天干。它所当值之日适宜搬家',
                '驿马' => '驿马又称驿骑，它表示当日适宜封赠官爵，诏命公卿，远行赴任，移徙迁居。',
                '月恩' => '月恩是指阳建所生的天干，子与母相从称月恩，适宜营造，婚姻，迁徙，搬家，赴任，纳财。',
                '天德' => '天德是指天的福德，占得天时，有三合旺气，是上等的吉日，寓意万福大吉。',
                '月德' => '月德是月的德神，它所当值之日适宜宴乐、上官赴任、移徙搬家。',
                '天德合' => '天德合是合德之神，所理值之日适宜施恩、祈福、移徙搬家。',
                '四相' => '四相，是指四季王相的时日。四相日适宜修营、起工、生财、栽植、移徙、搬家。',
                '时德' => '四时天德，是四季中的德神。四时天德日适宜宴乐、赏贺、移徙、拜官、搬家。',
                '月德合' => '月德合，五行的精符相会聚为合。所理值之日，百福并集，是非常适宜搬家的吉神。',
                '天愿' => '天愿，是月中的善神。它所理值之日，适宜嫁娶、纳财、搬家。',
                '天赦' => '天赦，是上天赦免、饶恕罪过的神。它所当值之日适宜施恩、兴造、移徙搬家。',
                '天马' => '天马，是天的坐骑，其当值之日适宜拜公卿、择贤良、宣布政事、远行出征、移徙搬家。',
                '民日' => '民日，是一月之内做事的良辰吉日，当日适宜上官赴任、临政亲民、移徙搬家。',
                '四离' => '离，是阴阳分、至节气之前一辰。该日忌讳出行、搬家、远行、出军、征伐。',
                '四绝' => '四绝，就是四立前一辰。当日忌讳搬家、远行、出军。',
                '四废' => '四废，是四季衰败凋谢的时日。五行之气到此没有生机，福德亦受阻，该日大事勿用。',
                '劫煞' => '劫煞，是一年的阴气，主管杀害。当值之日忌讳成亲、行军、出行、搬家、兴造。',
                '灾煞' => '灾煞，主管灾难、疾病、困厄之事。当值之日禁忌搬家、出行、营造。',
                '月煞' => '月煞又叫月杀，是指月内的杀神。它当值之日禁忌宴请、搬家、营造等。',
                '大时' => '大时（大败），是月建、三合、五行沐浴的时日，因为五行到这里就败绝，是最凶时辰，所以叫做大凶之时。当日禁忌搬家、筑室、会亲。',
                '天吏' => '天吏（致死），指的是天上的凶吏，全没有生机的意思。当值之日禁忌赴任、远行、搬家。',
                '月厌' => '月厌，是厌魅之神。他的性情暗暧私邪不正，因此要忌讳他。当日忌讳搬家、移徙、婚嫁。',
                '归忌' => '归忌，是月内的凶神。当值之日，忌讳归家、娶妇、搬家。',
                '五墓' => '五墓，代表五行的旺气临于墓地，寓意不吉。所值之日，忌营造、嫁娶、搬动等事宜。',
                '往亡' => '往就是去，亡就是没有。往亡就是往而不返的意思。该日忌讳拜官上任、远行归家、出军征讨、移徙搬家。',
                '月破' => '月破，是月建所冲之日，它所理值之日大事不宜。',
                '月刑' => '月刑有刑伤之意，因而该日多主不吉，犯之则多有争斗。',
                '气往亡' => '气往亡指的是与节气有关的往亡日，往就是去，亡就是没有。往亡就是往而不返的意思。该日忌讳拜官上任、远行归家、出军征讨、移徙搬家。',
                '受死' => '受死日是没有办法制化的，所以它是大忌，办事时需要避开该日。',
                '十恶大败' => '十恶意思是不赦重罪，大败意思是表示消减，是一种极凶的征兆。它所当值之日大事勿用。',
                '岁破' => '岁破是太岁的所冲之日，它所当值之日大事皆忌。',
                '杨公忌日' => '杨公忌日又叫杨公十三忌，在杨公忌日里做事情都较为不顺，容易招致灾祸，办事理应避开此日。',
                '天刑' => '天刑黑道，天刑星，该日利于出师，不宜其他动作。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '白虎' => '白虎黑道，天杀星，宜出师畋猎祭祀。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '朱雀' => '朱雀黑道，天讼星，利用公事，常人凶，谨防争讼。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '天牢' => '天牢黑道，镇神星，阴人用事皆吉，其余皆不利。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '玄武' => '玄武黑道，天狱星，君子用之吉，小人用之凶，忌词讼博戏。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '勾陈' => '勾陈黑道，地狱星，此时所作一切事，易有始无终，不利攸往。如果当日有适宜搬家的吉神时，则可以进行搬家。',
                '青龙' => '青龙黄道，天乙和天贵星。大吉之星，所谓必成，万事顺意。',
                '明堂' => '明堂黄道，贵人星，明辅星，传统习俗中的吉利日子，寓意贵人相助，事情必定成功。',
                '金匮' => '金匮黄道，福德星，月仙星，传统习俗中的吉利日子，寓意行事一帆风顺。',
                '宝光' => '天德（宝光）黄道，宝光星，天德星，传统习俗中的吉利日子，作事有成，利于办事。',
                '玉堂' => '玉堂黄道，少微星，天开星，百事吉，求事成，出行有财。传统习俗中的吉利日子，做事情都会比较顺利。',
                '司命' => '司命黄道，凤辇星，月仙星，当天用事大吉，寓意做事都能成功。但忌讳夜间宴请宾客。',
                '王日' => '王日是四季中正王的时日，是四正的方位，有帝王之气象，寓意大吉，适合搬家。',
                '官日' => '官日是四季中临官的时日，有诸王侯之寓意，乃月内良辰吉日，适合有所动作，搬家大吉。',
                '守日' => '守日是四季胎、绝的时日，无气能够自守，守而等待将来，当值之日利于搬家。',
                '相日' => '相日是四季中官日相生的，是相气的时日，有宰相之象。他当值之日适宜搬家。',

            ];
        }
        return $list[$str] ?? '';
    }

    /**
     * 过滤黄历算法中的指定吉凶神
     * @param array $arr
     * @param int $type 0吉神 1凶神
     * @return array
     */
    protected function filterJXShen(array $arr, int $type = 0): array
    {
        // 过滤掉黄历的吉凶神
        if ($type) {
            $listNo = ['月厌', '归忌', '往亡', '月破', '四废', '劫煞', '灾煞', '大时', '大败', '咸池', '天吏', '致死', '五墓', '月刑', '月煞'];//凶
        } else {
            $listNo = ['月恩', '驿马', '四相', '时德', '民日', '天赦', '天马', '天愿', '天德', '月德', '天德合', '月德合', '王日', '官日', '守日', '相日'];//吉
        }
        if ($listNo) {
            foreach ($arr as $k => $v) {
                if (in_array($v, $listNo)) {
                    unset($arr[$k]);
                }
            }
        }
        return array_values($arr);
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $sxBase = new SxBase();
                $sx = $sxBase->getsxByDz($k);
                $str = $k . $sx;
                break;
            }
        }
        return $str;
    }

    /**
     * 获得年份内所有节气
     * @param $year
     * @return array
     */
    private function getJieqiByYear($year): array
    {
        $arr = $this->jieQiYear;
        if (isset($arr[$year])) {
            return $arr[$year];
        }
        $jArr = SolarTerm::getAllJieQi($year);
        $jieqiArr = [];
        foreach ($jArr as $k => $v) {
            $jTmp = date('Y-m-d', strtotime($v));
            $jieqiArr[$jTmp] = $k;
        }
        $this->jieQiYear[$year] = $jieqiArr;
        return $jieqiArr;
    }

    /**
     * 获得用户能用到的所有时辰
     * @return array
     */
    protected function getHourUse(): array
    {
        $ygz = $this->lunar1->getLunarGanzhiYear();
        $ydzU = $ygz[1];
        $list = [
            '子' => ['丑', '辰', '申', '丑', '亥'], '丑' => ['子', '巳', '酉', '子', '亥'], '寅' => ['亥', '午', '戌', '卯', '辰'], '卯' => ['戌', '亥', '未', '辰', '寅'],
            '辰' => ['酉', '子', '申', '寅', '卯'], '巳' => ['申', '酉', '丑', '午', '未'], '午' => ['未', '寅', '戌', '巳', '未'], '未' => ['午', '亥', '卯', '巳', '午'],
            '申' => ['巳', '辰', '子', '酉', '戌'], '酉' => ['辰', '巳', '丑', '戌', '申'], '戌' => ['卯', '午', '寅', '申', '酉'], '亥' => ['寅', '卯', '未', '子', '丑'],
        ];
        $listU = $list[$ydzU];
        if ($this->lunar2) {
            $ygz2 = $this->lunar2->getLunarGanzhiYear();
            $ydzU = $ygz2[1];
            $listU = array_unique(array_merge($listU, $list[$ydzU]));
        }
        $result = [];
        $no = ['子', '丑', '寅'];
        $listHourInfo = $this->getHourInfo();
        foreach ($list as $k => $v) {
            $tmpArr = [];
            $useList = array_unique(array_merge($listU, $v));
            $useList = Utils::sortbyGz($useList);
            foreach ($useList as $k1 => $dz) {
                // 去除夜间 跟用户年支日支相刑的时辰
                if (in_array($dz, $no) || in_array($dz, $this->chongXingDz['chong']) || in_array($dz, $this->chongXingDz['xing'])) {
                    continue;
                }
                if (BaziCommon::getXianChong($k . $dz) || BaziCommon::getXianXin($k . $dz)) {
                    continue;
                }
                $tmpArr[$dz] = $listHourInfo[$dz];
            }
            $result[$k] = $tmpArr;
        }
        return $result;
    }

    /**
     * 获得时辰详细信息
     * @return array
     */
    protected function getHourInfo(): array
    {
        $list = Calendar::DI_ZHI;
        // 时辰对应的时区间
        $listHour = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $sxBase = new SxBase();
        $result = [];
        foreach ($list as $k => $dz) {
            $chongDz = BaziCommon::getChongDz($dz);
            $chongSx = $sxBase->getsxByDz($chongDz);
            $result[$dz] = [
                'dz' => $dz,
                't' => $listHour[$dz],
                'chong' => $chongSx,
            ];
        }
        return $result;
    }
}
