<?php
// +----------------------------------------------------------------------
// | Dinghunjiri.订婚吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Dinghunjiri
{
    use JiRiCheckTraits;

    /**
     * 数据
     * @var array
     */
    protected array $orginData;

    /**
     * 男方生日类
     * @var Ex
     */
    protected Ex $lunarM;

    /**
     * 女方生日类
     * @var Ex
     */
    protected Ex $lunarF;

    /**
     * 气往亡
     * @var array
     */
    protected array $qiWangwan = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男方生日
            'mtime' => input('mtime', '', 'trim'),
            // 女方生日
            'ftime' => input('ftime', '', 'trim'),
            // 订单时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
            // 男方父亲出生年份
            'mfather' => input('mfather', '', 'trim'),
            // 男方母亲的出生年份
            'mmon' => input('mmon', '', 'trim'),
            // 女方父亲出生年份
            'ffather' => input('ffather', '', 'trim'),
            // 女方母亲的出生年份
            'fmon' => input('fmon', '', 'trim'),
            // 限定的天数
            'limit' => input('limit', 0, 'intval'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'mtime|男出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'ftime|女出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'mfather|男方父亲出年年份' => ['between:1930,2020'],
                'mmon|男方母亲出年年份' => ['between:1930,2020'],
                'ffather|女方父亲出年年份' => ['number', 'between:1930,2020'],
                'fmon|女方母亲出年年份' => ['between:1930,2020'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'limit|查看天数' => ['require', 'between:1,720'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunarM = Ex::date($data['mtime'])->sex(0);
        $this->lunarF = Ex::date($data['ftime'])->sex(1);
        $this->orginData = $data;
        $jiRes = $this->getJiri();
        $result = [
            'm' => [
                'base' => $this->lunarM->getLunarByBetween(),
                'god' => $this->lunarM->getGod(),
                '_god' => $this->lunarM->_getGod(),
                // 纳音
                'nayin' => $this->lunarM->getNayin(),
                // 地势
                'terrain' => $this->lunarM->getTerrain(),
                'like_god' => $data['source'] == 'wnl' ? $this->lunarM->getLikeGod2() : $this->lunarM->getLikeGod(),
                // 阳气
                'yangqi' => $this->getYangQi(),
            ],
            'f' => [
                'base' => $this->lunarF->getLunarByBetween(),
                'god' => $this->lunarF->getGod(),
                '_god' => $this->lunarF->_getGod(),
                'nayin' => $this->lunarF->getNayin(),
                // 地势
                'terrain' => $this->lunarF->getTerrain(),
                'like_god' => $data['source'] == 'wnl' ? $this->lunarF->getLikeGod2() : $this->lunarF->getLikeGod(),
                // 阴胎天干地支
                'yintai' => $this->getYinTai(),
            ],
            'ji' => $jiRes['list'],
            'explain' => $jiRes['explain'],
        ];
        return $result;
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = $this->orginData['otime'];
        $limit = $this->orginData['limit'];
        // 破日 月份+破日
        $listPo = ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'];
        // 往亡
        $listWangWang = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        // 归忌
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        // 受死 农历月份+日支
        $listShouSi = ['1|戌', '2|辰', '3|亥', '4|巳', '5|子', '6|午', '7|丑', '8|未', '9|寅', '10|申', '11|卯', '12|酉'];
        // 小红沙日 农历月份+日支
        $listHongSha = ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'];
        // 杨公忌日 农历月份+日
        $listYangGong = ['1-13', '2-11', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        // 煞向
        $listShaXian = [
            '子' => '南', '丑' => '东', '寅' => '北', '卯' => '西', '辰' => '南', '巳' => '东',
            '午' => '北', '未' => '西', '申' => '南', '酉' => '东', '戌' => '北', '亥' => '西',
        ];
        // 不利月  翁姑月 父母月 妨夫月 妨妇月
        $listBuliYue = [
            '子' => [2, 3, 4, 11], '丑' => [9, 2, 1, 12], '寅' => [4, 5, 6, 1], '卯' => [5, 4, 3, 2],
            '辰' => [6, 1, 2, 3], '巳' => [1, 6, 5, 4], '午' => [8, 9, 10, 5], '未' => [3, 8, 7, 6],
            '申' => [10, 11, 12, 7], '酉' => [11, 10, 9, 8], '戌' => [12, 7, 8, 9], '亥' => [7, 12, 11, 10],
        ];
        // 父母生冲干支
        $zhengChong = $this->getZhenChong();
        $yearGz = $this->lunarM->getLunarGanzhiYear();
        $yearGz1 = $this->lunarF->getLunarGanzhiYear();
        $jiNianF = $this->lunarF->getLunarTganDzhi();
        $jiNianM = $this->lunarM->getLunarTganDzhi();
        $userDz = [
            $jiNianM['y'][1], $jiNianM['d'][1], $jiNianF['y'][1], $jiNianF['d'][1],
        ];
        $nvMing = $this->getNvMing($jiNianF['d'][0]);
        $listBuliYue1 = $listBuliYue[$yearGz1[1]];
        $sx1 = $this->lunarF->getZodiac();
        // 大利月
        $daliYue = $this->getDaLiYue($sx1);
        // 小利月
        $xiaoLiYue = $this->getXiaoLiYue($sx1);
        $result = [];
        $explain = [
            'jc' => [],
            'shen' => [],
            'sha' => [],
        ];
        for ($i = 1; $i <= $limit; $i++) {
            $time = strtotime("{$startTime} +{$i} day");
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $base = $huangli->getLunarByBetween();
            $jiNian = $base['jinian'];
            $nongliYueNum = $base['_nongli']['m'];
            $mzDz = $jiNian['m'][1] . $jiNian['d'][1];
            $rgz = implode('', $jiNian['d']);
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            $listNo = [
                '7_15' => '七月半',
                '9_9' => '重阳',
                '10_1' => '寒衣',
            ];
            $bool = true;
            $noList = [];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_15', '9_9', '10_1'])) {
                $noList[] = $listNo[$nongliNumberStr];
                $bool = false;
            }
            // 彭祖忌 亥日例：所有地支为亥的日子都去掉
            if ($jiNian['d'][1] === '亥') {
                $noList[] = '彭祖百忌';
                $bool = false;
            }
            $zhiRi = $huangli->getZhiRi();
            $shenSha = $zhiRi['shen_sha'];
            if ($zhiRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            // 四立两分两至（反目日） 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至
            $jieQi = Huangli::isJieQi($time);
            if ($jieQi && in_array($jieQi, $list2)) {
                $noList[] = $jieQi;
                $bool = false;
            }
            if ($jieQi == '清明') {
                $noList[] = '清明';
                $bool = false;
            }
            // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉 和十五
            if (($base['_nongli']['d'] == 1 || $base['_nongli']['d'] == 15) && $nongliNumberStr != '10_1') {
                $noList[] = '朔日';
                $bool = false;
            }
            // 去除亢金龙
            $xingSu = $huangli->getXingSu();
            if ($xingSu[0] === '亢金龙') {
                $bool = false;
            }
            // 去除破日+往亡+归忌
            if (in_array($mzDz, $listPo)) {
                $noList[] = '岁破日';
                $bool = false;
            }
            if (in_array($mzDz, $listWangWang)) {
                $noList[] = '往亡日';
                $bool = false;
            }
            if (in_array($mzDz, $listGuiJi)) {
                $noList[] = '归忌日';
                $bool = false;
            }
            // 受死+小红沙日
            $strShouSi = $base['_nongli']['m'] . '|' . $jiNian['d'][1];
            if (in_array($strShouSi, $listShouSi)) {
                $noList[] = '受死日';
                $bool = false;
            }
            if (in_array($strShouSi, $listHongSha)) {
                $noList[] = '红沙日';
                $bool = false;
            }
            $strYangGong = $base['_nongli']['m'] . '-' . $base['_nongli']['d'];
            // 杨公忌日
            if (in_array($strYangGong, $listYangGong)) {
                $noList[] = '杨公忌日';
                $bool = false;
            }
            // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天
            $tomorrowJieQi = Huangli::isJieQi($time + 86400);
            if ($tomorrowJieQi && in_array($tomorrowJieQi, $list2)) {
                if (in_array($tomorrowJieQi, ['立春', '立夏', '立秋', '立冬'])) {
                    //四绝
                    $noList[] = '四绝日';
                } else {
                    $noList[] = '四离日';
                }
                $bool = false;
            }
            // 干支
            $tmpGZ = [
                implode('', $jiNian['y']), implode('', $jiNian['m']), implode('', $jiNian['d']),
            ];
            // 正冲
            if (in_array($rgz, $zhengChong)) {
                $noList[] = '正冲';
                $bool = false;
            }
            foreach ($userDz as $v1) {
                if (BaziCommon::getXianChong($v1 . $jiNian['d'][1])) {
                    $bool = false;
                    break;
                }
            }
            foreach ($userDz as $v1) {
                if (BaziCommon::getXianXin($jiNian['d'][1] . $v1)) {
                    $bool = false;
                    break;
                }
            }
            $jianChu = $huangli->getJianChu();
            $jcType = 1;
            if (in_array($jianChu, ['危', '破', '闭', '执'])) {
                $jcType = 0;
            }
            $rdz = $jiNian['d'][1];
            $jiNianTmp = $jiNian;
            unset($jiNianTmp['h']);
            $jiXiongYiJi = $huangli->getJiXiong();
            // 去掉刑冲
            $fenxi = $this->getLiuGx($jiNianTmp);
            if ($fenxi) {
                $bool = false;
            }
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']) ? 1 : 0,
                // 生肖
                'sx' => $base['shengxiao'],
                // 农历
                'nongli' => $base['nongli'],
                // 农历数字
                '_nongli' => $base['_nongli'],
                // 纪年
                'jinian' => $jiNianTmp,
                // 凶时
                'hour' => [],
                // 正冲
                'zheng_chong' => BaziCommon::getChongBygz($rgz),
                // 煞向
                'sha_xian' => $listShaXian[$jiNian['d'][1]],
                // 建除
                'jianchu' => $jianChu,
                // 宜忌
                'type' => 'buyi',
                // 黄道 1 黑道 0
                'hl_type' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                // 建除 吉 1 不吉 0
                'jc_type' => $jcType,
                // 星神结果
                'shensha' => $shenSha,
                'fenxi' => [],
                'jishen' => $jiXiongYiJi['jishen'],
                'xiong' => $jiXiongYiJi['xiong'],
                'reason' => [],
            ];
            $nongliMonthNum = (int)$base['_nongli']['m'];
            $keyRes = $base['_nongli']['y'] . '年' . $base['_nongli']['m'] . '月(农历)';
            $numByMonth = $result[$keyRes]['num'] ?? 0;
            $monthTitle = '';
            $titleDes = '';
            $dxLiYue = 0;
            if (in_array($nongliYueNum, $daliYue)) {
                $monthTitle = '大利月';
                $titleDes = '女方大利月，本月吉日为上上之选。';
                $dxLiYue = 1;
            } elseif (in_array($nongliYueNum, $xiaoLiYue)) {
                $monthTitle = '小利月';
                $titleDes = '女方小利月，比大利月吉日较次，可做备选日。';
                $dxLiYue = 1;
            } elseif ($nongliYueNum == $listBuliYue1[0]) {
                $monthTitle = '翁姑月';
                $titleDes = '本月订婚吉日当天若不是天月岁德合日等，新人进家门，翁姑暂避则吉。';
            } elseif ($nongliYueNum == $listBuliYue1[1]) {
                $monthTitle = '父母月';
                $titleDes = '本月订婚吉日当天若不是天月岁德合日等，女方离开自家时，父母勿出送则吉。';
            } elseif ($nongliYueNum == $listBuliYue1[2]) {
                $monthTitle = '妨夫月';
                $titleDes = '不利男方运势的月份，若吉神多，可权用。';
            } elseif ($nongliYueNum == $listBuliYue1[3]) {
                $monthTitle = '妨妇月';
                $titleDes = '不利女方运势的月份，若吉神多，可权用。';
            }
            if ($bool) {
                if (!$jcType) {
                    $tmpRes['jianchu'] = '';
                }
                $hourDetail = $huangli->getHourDetail();
                $tmpRes['hour'] = $this->getHourByDz($jiNianTmp['d'][1], $hourDetail, $shenSha);
                $zhongJi = 0;
                $daJi = 0;
                $reason = [];
                if ($this->checkBuJian($nongliMonthNum, $tmpGZ[2])) {
                    $reason[] = '不将';
                    $zhongJi = 1;
                }
                if ($this->checkJiFen($nongliMonthNum, $tmpGZ[2])) {
                    $reason[] = '季分';
                }
                if ($this->checkSuiDe($jiNian['y'][0] . $jiNian['d'][0])) {
                    $reason[] = '岁德';
                }
                if ($this->checkSuiDeHe($jiNian['y'][0] . $jiNian['d'][0])) {
                    $reason[] = '岁德合';
                }
                $strmzrz = $jiNian['m'][1] . $jiNian['d'][1];
                if ($this->checkHongLuanTianXi($strmzrz)) {
                    $reason[] = '红鸾天喜';
                }
                if ($this->getSanHeByDz($strmzrz)) {
                    $reason[] = '三合';
                    $zhongJi = 1;
                }
                $strmzrg = $jiNian['m'][1] . $jiNian['d'][0];// 月支+日干
                if ($this->checkTianDe($strmzrg) || $this->checkTianDe($strmzrz)) {
                    $reasion[] = '天德';
                    $daJi = 1;
                }
                if ($this->checkTianDeHe($strmzrg) || $this->checkTianDeHe($strmzrz)) {
                    $reason[] = '天德合';
                    $daJi = 1;
                }
                if ($this->checkYueDe($strmzrz)) {
                    $reason[] = '月德';
                    $daJi = 1;
                }
                if ($this->checkYueDeHe($strmzrg) || $this->checkYueDeHe($strmzrz)) {
                    $reason[] = '月德合';
                    $daJi = 1;
                }
                if ($this->checkTianSe($jiNian['m'][1], $tmpGZ[2])) {
                    $reason[] = '天赦';
                }
                if ($this->checkTianYuan($jiNian['m'][1] . $tmpGZ[2])) {
                    $reason[] = '天愿';
                }
                if ($this->checkYueEn($strmzrg)) {
                    $reason[] = '月恩';
                }
                if ($this->checkSiXian($jiNian['m'][1], $jiNian['d'][0])) {
                    $reason[] = '四相';
                }
                if ($this->checkShiDe($strmzrz)) {
                    $reason[] = '时德';
                }
                if ($this->checkXianXing($nongliMonthNum, $tmpGZ[2])) {
                    $reason[] = '显星';
                }
                if ($this->checkQuXing($nongliMonthNum, $tmpGZ[2])) {
                    $reason[] = '曲星';
                }
                if ($this->checkChuanXing($nongliMonthNum, $tmpGZ[2])) {
                    $reason[] = '传星';
                }
                if ($jiNian['m'][1] === $jiNian['d'][1]) {
                    $reason[] = '天帝';
                }
                $tmpReason = $reason;
                $keySanHe = array_search('三合', $tmpReason);
                if (false !== $keySanHe) {
                    unset($tmpReason[$keySanHe]);
                }
                $jiShenArr = $jiXiongYiJi['jishen'];
                $jiShenArr = array_values(array_unique(array_merge($jiShenArr, $tmpReason)));
                $tmpRes['jishen'] = $jiShenArr;
                if ($dxLiYue && $daJi) {
                    $tmpRes['type'] = 'da';
                } elseif ($zhongJi) {
                    $tmpRes['type'] = 'xiao';
                } else {
                    $tmpRes['type'] = 'ping';
                }
                $tmpRes['reason'] = $reason;
                $numByMonth++;
            } else {
                if ($tmpRes['hl_type'] == 1) {
                    $tmpRes['shensha'] = '';
                }
                if ($jcType) {
                    $tmpRes['jianchu'] = '';
                }
                $tmpRes['fenxi'] = $fenxi;
                $tmpRes['reason'] = $noList;
            }
            foreach ($tmpRes['reason'] as $k1 => $v1) {
                if (isset($explain['shen'][$v1])) {
                    continue;
                }
                $reasonRes = $this->getExplain($v1);
                if (empty($reasonRes)) {
                    unset($tmpRes['reason'][$k1]);
                    continue;
                }
                $explain['shen'][$v1] = $reasonRes;
            }
            $tmpRes['reason'] = array_values($tmpRes['reason']);
            if (!empty($tmpRes['jianchu']) && !isset($explain['jc'][$jianChu])) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if (!empty($tmpRes['shensha']) && !isset($explain['sha'][$shenSha])) {
                $explain['sha'][$shenSha] = $this->getShaRes($shenSha);
            }
            $result[$keyRes]['ym'] = $keyRes;
            $result[$keyRes]['liyue'] = $monthTitle;
            $result[$keyRes]['liyue_des'] = $titleDes;
            $result[$keyRes]['num'] = $numByMonth;
            $result[$keyRes]['info'][] = $tmpRes;
        }
        return [
            'explain' => $explain,
            'list' => array_values($result),
        ];
    }

    /**
     * 根据日干获得不能用的天干地支
     * @param string $tg
     * @return array
     */
    protected function getNvMing(string $tg): array
    {
        $list = [
            '甲' => ['丁丑', '壬申'], '乙' => ['丙戌', '癸巳'], '丙' => ['己亥', '甲辰'], '丁' => ['戊辰', '乙卯'], '戊' => ['辛酉', '丙寅'],
            '己' => ['庚辰', '丁丑'], '庚' => ['癸巳', '戊子'], '辛' => ['壬寅', '己亥'], '壬' => ['乙卯', '庚戌'], '癸' => ['甲子', '辛酉'],
        ];
        return $list[$tg] ?? $list['甲'];
    }

    /**
     * 根据日天干获得妻星和天官
     * @param string $tg
     * @return array
     */
    protected function getManQiAndGuan(string $tg): array
    {
        // 妻星    天官
        $list = [
            '甲' => ['己巳', '辛未'], '乙' => ['戊寅', '辛未'], '丙' => ['辛卯', '癸巳'], '丁' => ['庚戌', '壬寅'], '戊' => ['癸亥', '乙卯'],
            '己' => ['壬申', '甲戌'], '庚' => ['乙酉', '丁亥'], '辛' => ['甲午', '丙申'], '壬' => ['丁未', '己酉'], '癸' => ['丙辰', '戊午'],
        ];
        $data = $list[$tg];
        return [
            'qi' => $data[0],
            'tian' => $data[1],
        ];
    }

    /**
     * 根据日天干获得夫星和天嗣
     * @param string $tg
     * @return array
     */
    protected function getFuAndTian(string $tg): array
    {
        $list = [
            '甲' => ['辛未', '丙寅'], '乙' => ['庚辰', '丁亥'], '丙' => ['癸巳', '戊戌'], '丁' => ['壬寅', '己酉'], '戊' => ['乙卯', '庚申'],
            '己' => ['甲戌', '辛未'], '庚' => ['丁亥', '壬午'], '辛' => ['丙申', '癸巳'], '壬' => ['己酉', '甲辰'], '癸' => ['戊午', '乙卯'],
        ];
        $data = $list[$tg];
        return [
            'qi' => $data[0],
            'tian' => $data[1],
        ];
    }

    /**
     * 获得阳气干支
     * @return array
     * @throws \Exception
     */
    protected function getYangQi(): array
    {
        $list = [
            '甲' => '乙', '乙' => '丙', '丙' => '丁',
            '丁' => '戊', '戊' => '己', '己' => '庚',
            '庚' => '辛', '辛' => '壬', '壬' => '癸',
            '癸' => '甲',
        ];
        $list1 = [
            '子' => '卯', '丑' => '辰', '寅' => '巳',
            '卯' => '午', '辰' => '未', '巳' => '申',
            '午' => '酉', '未' => '戌', '申' => '亥',
            '酉' => '子', '戌' => '丑', '亥' => '寅',
        ];
        $gz = $this->lunarM->getLunarGanzhiMonth();
        return [
            $list[$gz[0]], $list1[$gz[1]],
        ];
    }

    /**
     * 根据女方月干支获得阴胎干支
     * @return array
     * @throws \Exception
     */
    protected function getYinTai(): array
    {
        $list = [
            '甲' => '乙', '乙' => '丙', '丙' => '丁',
            '丁' => '戊', '戊' => '己', '己' => '庚',
            '庚' => '辛', '辛' => '壬', '壬' => '癸',
            '癸' => '甲',
        ];
        $list1 = [
            '子' => '卯', '丑' => '辰', '寅' => '巳',
            '卯' => '午', '辰' => '未', '巳' => '申',
            '午' => '酉', '未' => '戌', '申' => '亥',
            '酉' => '子', '戌' => '丑', '亥' => '寅',
        ];
        $gz = $this->lunarF->getLunarGanzhiMonth();
        return [
            $list[$gz[0]], $list1[$gz[1]],
        ];
    }

    /**
     * 根据年份获得气往亡日期
     * @param $year
     * @return array
     */
    protected function getQiWangwang($year): array
    {
        if (isset($this->qiWangwan[$year])) {
            return $this->qiWangwan[$year];
        }
        $jieQiAll = SolarTerm::getAllJieQi($year);
        // 立春后第七日；惊蛰后十四日；清明后二十一日；立夏后第八日；芒种后十六日；小暑后二十四日；立秋后第九日；白露后十八日；
        // 寒露后二十七日；立冬后十日；大雪后二十日；小寒后三十日。比如说，2018年4月5日是清明，21天后的4月26日为天门日犯往亡煞。
        $result = [
            date('Y-m-d', strtotime("{$jieQiAll['立春']} +7 day")),
            date('Y-m-d', strtotime("{$jieQiAll['惊蛰']} +14 day")),
            date('Y-m-d', strtotime("{$jieQiAll['清明']} +21 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立夏']} +8 day")),
            date('Y-m-d', strtotime("{$jieQiAll['芒种']} +16 day")),
            date('Y-m-d', strtotime("{$jieQiAll['小暑']} +24 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立秋']} +9 day")),
            date('Y-m-d', strtotime("{$jieQiAll['白露']} +18 day")),
            date('Y-m-d', strtotime("{$jieQiAll['寒露']} +27 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立冬']} +10 day")),
            date('Y-m-d', strtotime("{$jieQiAll['大雪']} +20 day")),
            date('Y-m-d', strtotime("{$jieQiAll['小寒']} +30 day")),
        ];
        $this->qiWangwan[$year] = $result;
        return $result;
    }

    /**
     *月支和日支检查对冲日。
     * @param string $mdz 月支
     * @param string $rdz 日支
     * @return bool
     */
    protected function checkDuiChong(string $mdz, string $rdz): bool
    {
        $list = [
            '子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申',
        ];
        if (in_array($mdz . $rdz, $list)) {
            return true;
        }
        return false;
    }

    /**
     * 根据父母出生年份求出正冲干支
     * @return  array
     */
    protected function getZhenChong(): array
    {
        $list = [
            '甲子' => '庚午', '乙丑' => '辛未', '丙寅' => '壬申', '丁卯' => '癸酉', '戊辰' => '甲戌', '己巳' => '乙亥',
            '庚午' => '丙子', '辛未' => '丁丑', '壬申' => '戊寅', '癸酉' => '己卯', '甲戌' => '庚辰', '乙亥' => '辛巳',
            '丙子' => '壬午', '丁丑' => '癸未', '戊寅' => '甲申', '己卯' => '乙酉', '庚辰' => '丙戌', '辛巳' => '丁亥',
            '壬午' => '戊子', '癸未' => '己丑', '甲申' => '庚寅', '乙酉' => '辛卯', '丙戌' => '壬辰', '丁亥' => '癸巳',
            '戊子' => '甲午', '己丑' => '乙未', '庚寅' => '丙申', '辛卯' => '丁酉', '壬辰' => '戊戌', '癸巳' => '己亥',
            '甲午' => '庚子', '乙未' => '辛丑', '丙申' => '壬寅', '丁酉' => '癸卯', '戊戌' => '甲辰', '己亥' => '乙巳',
            '庚子' => '丙午', '辛丑' => '丁未', '壬寅' => '戊申', '癸卯' => '己酉', '甲辰' => '庚戌', '乙巳' => '辛亥',
            '丙午' => '壬子', '丁未' => '癸丑', '戊申' => '甲寅', '己酉' => '乙卯', '庚戌' => '丙辰', '辛亥' => '丁巳',
            '壬子' => '戊午', '癸丑' => '己未', '甲寅' => '庚申', '乙卯' => '辛酉', '丙辰' => '壬戌', '丁巳' => '癸亥',
            '戊午' => '甲子', '己未' => '乙丑', '庚申' => '丙寅', '辛酉' => '丁卯', '壬戌' => '戊辰', '癸亥' => '己巳',
        ];
        $yearList = [
            $this->orginData['mfather'], $this->orginData['mmon'], $this->orginData['ffather'], $this->orginData['fmon'],
        ];
        $yearList = array_unique($yearList);
        $result = [];
        foreach ($yearList as $v) {
            if ($v < 1920 || $v > 2050) {
                continue;
            }
            $gz = BaziExt::getGanZhi($v);
            $gzStr = implode('', $gz);
            $result[] = $list[$gzStr];
        }
        return array_unique($result);
    }

    /**
     * 流日和用户关系
     * @param array $jiNian
     * @return array
     */
    protected function getLiuGx($jiNian): array
    {
        $jiNianM = $this->lunarM->getLunarTganDzhi();
        $jiNianF = $this->lunarF->getLunarTganDzhi();
        $ydzM = $jiNianM['y'][1];
        $ddzM = $jiNianM['d'][1];
        $ydzF = $jiNianF['y'][1];
        $ddzF = $jiNianF['d'][1];
        $ddz = $jiNian['d'][1];
        $list = [
            '男' => [
                's' => [], 'y' => [], 't' => [],
            ],
            '女' => [
                's' => [], 'y' => [], 't' => [],
            ],
        ];
        $listChong = [
            ['男', '年', $ydzM . $ddz],
            ['男', '日', $ddzM . $ddz],
            ['女', '年', $ydzF . $ddz],
            ['女', '日', $ddzF . $ddz],
        ];
        foreach ($listChong as $k => $v) {
            $tKey = $v[0];
            if (BaziCommon::getXianChong($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '冲';
            }
            if (BaziCommon::getXianXin($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '刑';
            }
        }
        $result = [];
        foreach ($list as $k => $v) {
            if (empty($v['t'])) {
                continue;
            }
            $result[] = [
                't' => implode('', array_unique($v['t'])) . $k,
                'y' => array_unique($v['y']),
            ];
        }
        return $result;
    }

    /**
     * 获得符合时辰
     * @param string $dz 日支
     * @param array $hourDetail
     * @param string $shenSha 煞神
     * @return array
     */
    protected function getHourByDz(string $dz, array $hourDetail, string $shenSha = ''): array
    {
        $list = [
            '子' => ['丑', '辰', '申', '亥'],
            '丑' => ['子', '巳', '酉', '亥'],
            '寅' => ['亥', '午', '戌', '卯', '辰'],
            '卯' => ['戌', '亥', '未', '辰', '寅'],
            '辰' => ['酉', '子', '申', '寅', '卯'],
            '巳' => ['申', '酉', '丑', '午', '未'],
            '午' => ['未', '寅', '戌', '巳'],
            '未' => ['午', '亥', '卯', '巳'],
            '申' => ['巳', '辰', '子', '酉', '戌'],
            '酉' => ['辰', '巳', '丑', '戌', '申'],
            '戌' => ['卯', '午', '寅', '申', '酉'],
            '亥' => ['寅', '卯', '未', '子', '丑'],
        ];
        $listShi = [
            '午' => '午时（中午11点至13点）', '未' => '未时（下午13点至15点）', '申' => '申时（下午15点至17点）', '酉' => '酉时（下午17点至19点）',
            '戌' => '戌时（晚上19点至21点）', '亥' => '亥时（晚上21点至23点）', '子' => '子时（晚上23点至1点）', '丑' => '丑时（凌晨1点至3点）',
            '寅' => '寅时（凌晨3点至5点）', '卯' => '卯时（早上5点至7点）', '辰' => '辰时（早上7点至9点）', '巳' => '巳时（早上9点至11点）',
        ];
        $list1 = ['寅', '卯', '辰', '巳', '午', '未', '申'];
        // 卯辰巳午未申
        $ygz = $this->lunarM->getLunarGanzhiYear();
        $ygz1 = $this->lunarF->getLunarGanzhiYear();
        $mydz = $ygz[1];
        $fydz = $ygz1[1];
        $list10 = $list[$dz];
        $result = [];
        foreach ($list10 as $v) {
            if ($shenSha == '司命' && !in_array($v, $list1)) {
                continue;
            }
            if (
                BaziCommon::getXianChong($mydz . $v) ||
                BaziCommon::getXianChong($fydz . $v) ||
                BaziCommon::getXianXin($mydz . $v) ||
                BaziCommon::getXianXin($fydz . $v)
            ) {
                continue;
            }
            $result[] = $listShi[$v];
        }
        if (empty($result)) {
            foreach ($hourDetail as $item) {
                $tmpDz = $item['h'][1];
                if ($item['jix'] == '吉' && in_array($tmpDz, ['卯', '辰', '巳', '午', '未', '申'])) {
                    $result[] = $listShi[$tmpDz];
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * 解释
     * @param $str
     * @return string
     */
    protected function getExplain($str)
    {
        $list = [
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定。宜采纳、商贾、拜访、考试，婚嫁，移居等。',
            '满' => '满日在吉日中有“圆满”的含义，对于订婚来说，自然有着比较好的寓意。',
            '建' => '对于事情上来说，该日有着起始的说法，寓意较好。',
            '成' => '凡事有所成，因而该日诸事皆可办理。',
            '开' => '寓意着顺利开始，乃是一个好日子，订婚可选。',
            '危' => '危日象征危机，是危险的一天，忌讳诸多喜事。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日有指破裂，冲破的含义，忌办一切喜凶事。',
            '平' => '此日平平，无吉无凶，吉神相伴可用。',
            '收' => '此日有利于收获之事，但不利于开始的事情。当日忌出行、搬迁等喜庆之事。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进的意思。这一日有小破耗的说法，需规避。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，当日订婚不适合。',
            '重阳' => '当日有喜事勿用的说法，因而忌订婚嫁娶。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '彭祖百忌' => '彭祖百忌中有“亥不嫁娶，不利新郎”，该日嫁娶，不利新郎。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来订婚有些不合时宜。',
            '朔日' => '此系恶鬼聚拢之辰，忌订婚进宅会客作乐。',
            '亢金龙' => '订婚乃大喜之事，需吉星降临，而该日并无吉星庇佑，最好避开该日。',
            '岁破日' => '日值岁破，大事不宜。破日有破败之意，日月相冲，是为大耗，故不宜订婚。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '归忌日' => '该日寓意不好，该日有忌远行归家、娶妻嫁女的说法。',
            '受死日' => '该日有晦气风流，大事勿用的说法。而订婚乃是大喜之日，不宜被冲，故避开此日。',
            '红沙日' => '红沙日有诸事不宜的说法，而婚姻对人生乃是十分重要，因而需要注意避开。',
            '杨公忌日' => '这日诸事不宜，结婚需喜庆，注意规避该日。',
            '四绝日' => '古话有云，四绝日订婚嫁娶，犯之不顺。',
            '四离日' => '日值四离，大事勿用。',
            '四废日' => '日值四废，作事不易成功，容易有始无终。',
            '三娘煞' => '在婚姻上该日属于大忌之日，古话有，迎亲嫁娶无男女。',
            '气往亡' => '气往亡',
            '生肖相冲' => '该日天干地支和新人生肖相冲，故而需要规避。',
            '地支相冲' => '该日天干地支和女命的天干地支相冲，故而需要规避。',
            '日支刑冲' => '日支刑冲男女双方生肖，因而不适合订婚。',
            '正冲' => '该日天干地支正冲父母天干地支，需要规避。',
            '不将' => '是嫁娶订婚的传统吉日，当日订婚，寓意新人以后的生活平平顺顺。',
            '季分' => '嫁娶、订婚传统吉日，寓意幸福。',
            '岁德' => '德神护佑的吉日，积福之日，福气汇聚。',
            '岁德合' => '德神护佑的吉日，有福气之日，惊喜相随。',
            '红鸾天喜' => '有利嫁娶的吉日，红鸾报喜，夫妻恩爱。',
            '三合' => '寓意新人互生互利的吉日，可用作订婚。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，属于有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜进行入宅、订婚等事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，订婚大吉。',
            '四相' => '拥有四时王相贵气的日子，纯粹的小吉日，诸喜事皆宜。',
            '时德' => '得到天地舒畅之气，得到四时之气的祝福，小吉日。',
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，夫妻恩爱。',
            '曲星' => '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄，万事称心的寓意。',
            '天帝' => '先天卦数中，蕴含日月旺气，均衡融合的吉日。',
            '相冲' => '用户年支或日支与当日相冲，需规避。',
            '相刑' => '用户年支或日支与当日相刑，需规避。',

        ];
        return $list[$str] ?? '';
    }

    /**
     * 获得十二星神解释
     * @param $str
     * @return string
     */
    protected function getShaRes($str): string
    {
        $list = [
            '青龙' => '又叫天乙星，天贵星，此星利于行事，所作必成，所求皆得。',
            '明堂' => '传统习俗中的吉利日子，寓意贵人相助，事情必定成功，能够恩爱有加。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克。该日万事皆忌，不宜有大动作。',
            '朱雀' => '朱雀黑道凶日天讼星，利用公事，常人凶，喜事忌用，谨防远行动作。',
            '金匮' => '传统习俗中的吉利日子，用于订婚乃是大吉。',
            '白虎' => '天杀星，诸喜事不宜。',
            '玉堂' => '传统习俗中的吉利日子，订婚大吉。',
            '天牢' => '镇神星，阴人用事皆吉，其余都不利。',
            '玄武' => '该神属天狱星，比较容易触犯到小人。',
            '司命' => '当天用事大吉，红喜事可选。（此日从寅至申时用事大吉，从酉至丑时用事不利，即晚上忌用。）',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '天德' => '天德黄道吉日占得天时，有三合旺气，是上等的吉日。',
        ];
        return $list[$str] ?? '';
    }
}
