<?php
// +----------------------------------------------------------------------
// | 八字起名
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use api\ApiResult;
use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\lib\WxAttr;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\Ming3;
use app\model\baobaoqm\Xing;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use Overtrue\Pinyin\Pinyin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;

class Bzqm
{
    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 旺度
     * @var string
     */
    protected string $wangDu = '';

    /**
     * 喜用忌闲仇
     * @var array
     */
    protected array $xy = [];

    /**
     * 姓结果
     * @var array
     */
    protected array $xing = [];

    /**
     * 字信息
     * @var array
     */
    protected array $arrZi = [];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @return ApiResult
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            'xing' => Request::param('xing', '', 'trim'),
            // 性别 男为0，女为1 2未知
            'sex' => Request::param('sex', -1, 'intval'),
            // 出生日期
            'time' => Request::param('time', '', 'trim'),
            // 要获取的名字数
            'total' => Request::param('total', 40, 'intval'),
            // 出生状态 0 未出生 1 已出生
            'born' => Request::param('born', 0, 'intval'),
            // 订单时间
            'otime' => Request::param('otime', date('Y-m-d'), 'trim'),
            // 名字ID
            'ids' => Request::param('ids', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓' => ['require', 'chs', function ($data) {
                    $listXing = $this->getXingArr();
                    if (!in_array($data, $listXing)) {
                        return '暂不支持这个姓';
                    }
                    return true;
                }],
                'born|出生状态' => ['require', 'in:0,1'],
                'time|用户生日' => ['require', 'isDateOrTime:用户生日'],
                'sex|性别' => ['require', 'in:0,1,2'],
                'total|要取的名字数' => ['require', 'egt:10'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        $this->orginData = $data;
        $sex = $data['sex'];
        if ($sex == 2) {
            $sex = 0;
        }
        $this->lunar = Ex::date($data['time'])->sex($sex);
        $this->shaShen = new ShaShen();
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $baziEx = new BaziEx($this->lunar);
        $this->xy = $baziEx->getXiyongJi4();
        $this->wangDu = $baziEx->getWangDu();
        $this->xing = $this->getXingInfo();
        // 名字列表
        $namesList = $this->getXingAndNameList();
        // 大运
        $dayun = $this->lunar->getDayun(false, 8);
        $dayun = $this->getDayunHandle($dayun);
        $endYear = end($dayun['dayun']['eight']['year']) + 20;

        // 神煞
        $shenShaArr = $this->shaShen->detail($jiNian, $this->lunar->sex);

        $result = [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 日元
            'day_attr' => $this->lunar->getDayAttr(),
            // 大运
            'dayun' => [
                // 完整大运
                'wz' => $dayun['dayun'],
                // 当前大运序号
                'num' => $dayun['curr'],
                // 当前大运详情
                'info' => $dayun['info'],
            ],
            // 五行占比
            'wx' => $this->getWxPersent(),
            // 喜用神
            'like_god' => $this->lunar->getLikeGod(),
            // 喜用忌闲仇
            'xy' => $this->xy['xy'],
            // 个性分析
            'gexing' => $this->getGeXing(),
            // 主要特质
            'tezhi' => $this->getTeZhi(),
            // 潜力
            'qianli' => $this->getQianli(),
            // 合化
            'hehua' => $this->getGuanxi($base['jinian']),
            // 名字列表
            'names' => $namesList,
            // 神煞
            'shensha' => [
                // 四柱神煞
                'sz' => $shenShaArr,
            ],
            // 流年排盘
            'liunian_paipan' => $this->getliuNianPaipan($endYear),
        ];
        return ApiResult::success($result);
    }

    /**
     * 排盘流年
     * @param int $endYear
     * @return array
     */
    protected function getliuNianPaipan(int $endYear): array
    {
        $result = [];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $dtg = $jiNian['d'][0];
        $userYear = (int)$this->lunar->dateTime->format('Y');
        $maxI = $endYear - $userYear;
        // 地势表
        $terrainArr = Ex::getTerrainData();
        $mDetail = [];
        for ($i = 0; $i <= $maxI; $i++) {
            $year = $userYear + $i;
            $age = $year - $userYear + 1;
            $gz = BaziExt::getGanZhi($year);
            $gzStr = implode('', $gz);
            $listM = $this->getMonthList($gz[0]);
            foreach ($listM as $k => $v) {
                $mgz = Utils::mbStrSplit($v);
                if (isset($mDetail[$v])) {
                    continue;
                }
                $mDetail[$v] = [
                    'm' => ($k + 1),
                    'gz' => $v,
                    // 纳音
                    'na_yin' => $this->lunar->getNaYinByGz($v),
                    // 地势
                    'terrain' => $terrainArr[$dtg . $mgz[1]],
                    'godt' => BaziCommon::getGodName($dtg . $mgz[0]),
                    'godz' => BaziCommon::getGodName($dtg . $mgz[1]),
                ];
            }
            $result[] = [
                'y' => $year, 'gz' => implode('', $gz),
                'age' => $age,
                // 纳音
                'na_yin' => $this->lunar->getNaYinByGz($gzStr),
                // 地势
                'terrain' => $terrainArr[$dtg . $gz[1]],
                // 十神
                'godt' => BaziCommon::getGodName($dtg . $gz[0]),
                'godz' => BaziCommon::getGodName($dtg . $gz[1]),
                'mlist' => $listM,
            ];
        }
        return ['y' => $result, 'm' => $mDetail];
    }

    /**
     * 根据年干获得月份干支
     * @param string $tg 年干
     * @return array
     */
    protected function getMonthList(string $tg): array
    {
        $list = [
            ['丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑'],
            ['戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑'],
            ['庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑'],
            ['壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑'],
            ['甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥', '甲子', '乙丑'],
        ];
        $list1 = [
            ['甲', '己'], ['乙', '庚'], ['丙', '辛'], ['丁', '壬'], ['戊', '癸'],
        ];
        $res = [];
        foreach ($list1 as $k => $v) {
            if (in_array($tg, $v)) {
                $res = $list[$k];
                break;
            }
        }
        return $res;
    }

    /**
     * 大运处理
     * @param array $dayun 大运
     * @return array
     */
    protected function getDayunHandle(array $dayun): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $otime = strtotime($this->orginData['otime']);
        $yearC = date('Y', $otime);
        $i = BaziExt::getKeyWithArray($yearC, $dayun['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $dayun['curr'] = $i;
        // 当前大运详情
        $currInfo = [];
        $eightGz = $dayun['eight']['chronlogy_year'];
        // 日天干地支
        $dgz = $this->lunar->getLunarGanzhiDay();
        foreach ($eightGz as $k => $v) {
            $tmpArr = Utils::mbStrSplit($v);
            $info = $this->getBasePan($tmpArr);
            $info['year'] = $dayun['eight']['year'][$k];
            $info['age'] = $dayun['eight']['age'][$k];
            // 地支主气十神
            $dayun['eight']['godz'][$k] = $info['godz'];
            // 纳音
            $dayun['eight']['na_yin'][$k] = $this->lunar->getNaYinByGz($v);
            // 神煞
            $dayun['eight']['sha'][$k] = $this->shaShen->liuNianToBazi($tmpArr, $jiNian);
            // 自坐(十二长生)
            $dayun['eight']['zi_zuo'][$k] = $info['zi_zuo'];
            // 十二长生(地势)
            $dayun['eight']['terrain'][$k] = $info['terrain'];
            // 空亡
            $dayun['eight']['kongwang'][$k] = $info['kongwang'];
            // 判断是否当前大运
            if ($i == $k) {
                $currInfo = $info;
            }
        }
        // 换运年份尾数
        $lastNumber = (int)$dayun['eight']['year'][0] % 10;
        $lastArr = [$lastNumber - 1, $lastNumber];
        if ($lastNumber < 1) {
            $lastArr = [9, 0];
        }
        $dayun['wei'] = $lastArr;

        // 换运年份（生肖+十神）
        $startYear = $dayun['eight']['year'][$i];
        $sGz = BaziExt::getGanZhi($yearC);
        $endYear = $startYear + 9;
        $endGz = BaziExt::getGanZhi($endYear);
        $dayun['huan'] = [
            // 当前流年 生肖+十神
            's' => [$yearC, implode('', $sGz), $this->lunar->getZodiac($sGz[1]), BaziCommon::getGodName($dgz[0] . $sGz[0])],
            // 换运流年 生肖+十神
            'e' => [$endYear, implode('', $endGz), $this->lunar->getZodiac($endGz[1]), BaziCommon::getGodName($dgz[0] . $endGz[0])],
        ];
        return [
            'curr' => $i,
            'info' => $currInfo,
            'dayun' => $dayun,
        ];
    }

    /**
     * 根据干支获得排盘数据
     * @param array $gz 干支
     * @return array
     */
    protected function getBasePan(array $gz): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $dtg = $jiNian['d'][0];
        $str = implode('', $gz);
        $terrainArr = Ex::getTerrainData();
        return [
            // 干支
            'gz' => $gz,
            // 天干十神
            'god' => BaziCommon::getGodName($dtg . $gz[0]),
            // 地支主气十神
            'godz' => BaziCommon::getGodName($dtg . $gz[1]),
            // 藏干
            '_god' => BaziCommon::getHideGod($gz[1], $dtg),
            // 纳音
            'nayin' => $this->lunar->getNaYinByGz($str),
            // 十二长生(地势)
            'terrain' => $terrainArr[$dtg . $gz[1]] ?? '',
            // 自坐(十二长生)
            'zi_zuo' => $terrainArr[$str],
            // 空亡
            'kongwang' => Huangli::getKongWangbyGz($str),
            // 神煞
            'shensha' => $this->shaShen->liuNianToBazi($gz, $jiNian),
        ];
    }

    /**
     * 获得名字列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getXingAndNameList(): array
    {
        $query = $this->orginData;
        $yong = $this->xy['xy']['yong'];
        $xi = $this->xy['xy']['xi'];
        $yongNum = WxAttr::getWxToNum($yong);
        $xiNum = WxAttr::getWxToNum($xi);
        $used1 = [$yongNum, $xiNum];
        $used2 = [$yongNum . $xiNum, $yongNum . $yongNum, $xiNum . $yongNum, $xiNum . $xiNum];
        if ($query['ids']) {
            $idsArr = explode('|', $query['ids']);
            $list1 = Ming3::where('id', 'in', $idsArr)
                ->cache(600)
                ->column('id,wx,zi,zi2', 'id');
            $this->arrZi = array_merge($this->arrZi, $this->getZiArr($list1));
            $list = [];
            foreach ($idsArr as $id) {
                if (!isset($list1[$id])) {
                    continue;
                }
                $tmp = $list1[$id];
                $info = [];
                $info[] = $this->getZiInfo($tmp['zi']);
                if ($tmp['zi2']) {
                    $info[] = $this->getZiInfo($tmp['zi2']);
                }
                $list[] = [
                    'id' => $id,
                    'info' => $info,
                ];
            }
        } else {
            $sex = $query['sex'];
            $total = $query['total'];
            $total1 = (int)($total / 2);
            $total2 = $total - $total1;
            $list1 = $this->getNameBysex($used2, $sex, $total1);
            $list2 = $this->getNameBysex($used1, $sex, $total2);
            $list = array_merge($list1, $list2);
        }
        $list2 = [];
        foreach ($list as $v) {
            $list2[count($v['info'])][] = $v;
        }
        return [
            'xing' => $this->xing,
            'list' => $list,
            'list2' => $list2,
        ];
    }

    /**
     * 从数据库查的数据中获得所有用字
     * @param array $list
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getZiArr(array $list): array
    {
        $arrZi = [];
        foreach ($list as $v) {
            $arrZi[$v['zi']] = $v['zi'];
            if ($v['zi2']) {
                $arrZi[$v['zi2']] = $v['zi2'];
            }
        }
        $arrZi = array_values($arrZi);
        return Cnword::getInfo2($arrZi);
    }

    /**
     * 获得名字列表
     * @param array $used 使用的五行
     * @param int $sex 性别
     * @param int $number 要取的名字数
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNameBysex(array $used, int $sex, int $number = 40): array
    {
        $utime = $this->lunar->dateTime->getTimestamp();
        $otimetime = (int)date('YmdHi', strtotime($this->orginData['otime']));
        $timeNum = $otimetime + (int)date('Y', $utime) + (date('m', $utime) * date('d', $utime));
        $xingStr = $this->orginData['xing'];
        $listXing = Xing::getlists();
        $listXing = array_flip($listXing);
        $xingId = $listXing[$xingStr];
        if ($sex == 2) {
            $genderArr = [0, 1, 2];
        } else {
            $genderArr = [$sex, 2];
        }
        $list = Ming3::lists($used, $genderArr, $timeNum, 3000);
        foreach ($list as $k => $v) {
            $stype = $v['stype'];
            $tmp = trim($v['xing'], '|');
            if (empty($tmp)) {
                $stype = 2;
            }
            $tmpArr = explode('|', $tmp);
            if ($stype == 1) {
                if (in_array($xingId, $tmpArr)) {
                    unset($list[$k]);
                    continue;
                }
            } elseif ($stype == 0) {
                if (!in_array($xingId, $tmpArr)) {
                    unset($list[$k]);
                    continue;
                }
            }
            unset($v['xing']);
            $list[$k] = $v;
        }
        shuffle($list);
        $this->arrZi = array_merge($this->arrZi, $this->getZiArr($list));
        $useNum1 = mb_strlen($used[0]);
        $list = $this->sortNames($list, $useNum1);
        $result = [];
        $i = 0;
        $listZi = [];
        $xingPY = $this->getXingFilter();
        $endX = end($this->xing);
        $xPyInfo = $this->getPyInfo($endX['py']);
        $xPy = array_column($xPyInfo, 'py_no');
        $xPyYm = array_column($xPyInfo, 'ym');
        $listOne = [];
        foreach ($list as $v) {
            if ($i >= $number) {
                break;
            }
            $zi = $v['zi'];
            $zi2 = $v['zi2'];
            $ziNum = $listZi[$zi] ?? 0;
            $ziNum2 = 0;
            if ($zi2) {
                $ziNum2 = $listZi[$zi2] ?? 0;
            }
            if ($ziNum >= 2 || $ziNum2 >= 2) {
                continue;
            }
            $ziInfo = $this->getZiInfo($zi);
            $pyInfo = $this->getPyInfo($ziInfo['py']);
            $pyNo = array_column($pyInfo, 'py_no');
            $pyYm = array_column($pyInfo, 'ym');
            if (array_intersect($xPyYm, $pyYm)) {
                continue;
            }
            $pyInfo2 = [];
            $pyNo2 = [];
            $ziInfo2 = [];
            $pyYm2 = [];
            if ($zi2) {
                $ziInfo2 = $this->getZiInfo($zi2);
                $pyInfo2 = $this->getPyInfo($ziInfo2['py']);
                $pyNo2 = array_column($pyInfo2, 'py_no');
                $pyYm2 = array_column($pyInfo2, 'ym');
                if (array_intersect($xPyYm, $pyYm2)) {
                    continue;
                }
            }
            $bool = false;
            $arr1 = [];
            foreach ($pyNo as $v10) {
                if (in_array($v10, $xingPY)) {
                    $bool = true;
                }
                if ($pyNo2) {
                    foreach ($pyNo2 as $v11) {
                        if (in_array($v11, $xingPY)) {
                            $bool = true;
                        }
                        $arr1[] = $v10 . $v11;
                    }
                } else {
                    $arr1[] = $v10;
                }
            }
            if (!$bool && $arr1) {
                foreach ($xPy as $v10) {
                    foreach ($arr1 as $v11) {
                        $str = $v10 . $v11;

                        if ($this->checkPyFilter($str)) {
                            $bool = true;
                            break 2;
                        }
                    }
                }
            }
            if ($useNum1 == 1) {
                if (array_intersect($listOne, $pyNo)) {
                    $bool = true;
                }
            }
            if ($bool) {
                continue;
            }
            if ($useNum1 == 1) {
                $listOne = array_merge($listOne, $pyNo);
            }
            $info = [];
            $info[] = $ziInfo;
            $ziNum++;
            $listZi[$zi] = $ziNum;
            if ($zi2) {
                $ziNum2++;
                $listZi[$zi2] = $ziNum2;
                $info[] = $ziInfo2;
            }
            $result[] = [
                'id' => $v['id'],
                'info' => $info,
            ];
            $i++;
        }
        return $result;
    }

    /**
     * 数据排序处理
     * @param array $list
     * @param int $num
     * @return array
     */
    protected function sortNames(array $list, int $num): array
    {
        $listSort = [
            '金木火' => ['木木', '火'], '金水木' => ['水水', '水'], '金火土' => ['土土', '土'],
            // '金金水'=>['',''],
            // '金土金'=>['',''],
            '木金水' => ['水水', '水'], '木火土' => ['火火', '火'], '木土金' => ['土土', '土'],
            // '木木火'=>['',''],
            // '木水木'=>['',''],
            '水木火' => ['木木', '木'], '水火土' => ['土土', '土'], '水土金' => ['金金', '金'],
            // '水水木'=>['',''],
            // '水金水'=>['',''],
            '火金水' => ['金金', '金'], '火水木' => ['木木', '木'], '火土金' => ['土土', '土'],
            // '火木火'=>['',''],
            // '火火土'=>['',''],
            '土金水' => ['金金', '金'], '土木火' => ['火火', '火'], '土水木' => ['木木', '木'],
            // '土火土'=>['',''],
            // '土土金'=>['',''],
        ];
        $xing = end($this->xing);
        $yong = $this->xy['xy']['yong'];
        $xi = $this->xy['xy']['xi'];
        $str = $xing['wx'] . $yong . $xi;
        if (!isset($listSort[$str])) {
            return $list;
        }
        $key = ($num == 2) ? 0 : 1;
        $wxStr = $listSort[$str][$key];
        $wxStrNum = WxAttr::getWxToNum($wxStr);
        $list1 = [];
        $list2 = [];
        foreach ($list as $v) {
            if ($v['wx'] == $wxStrNum) {
                $list1[] = $v;
                continue;
            }
            $list2[] = $v;
        }
        return array_merge($list1, $list2);
    }

    /**
     * 获得姓 字列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getXingInfo(): array
    {
        $xing = $this->orginData['xing'];
        $xingArr = preg_split('/(?<!^)(?!$)/u', $xing);
        $xingCn = [];
        foreach ($xingArr as $v) {
            $xingCn[] = $this->getZiInfo($v);
        }
        return $xingCn;
    }

    /**
     * 过滤拼音
     * @param string $str
     * @return bool
     */
    protected function checkPyFilter(string $str): bool
    {
        $st1 = 'yuanhu|sharen|huanghe|dijia|saren|juyuan|huangquan|nanfeng|xilan|xinlang|xinlan|lajiao|sigui|geying|geyin|wuqing|qingwu|feizhu|zhufei|feizu|zufei|baimei|meibai|sijue|juedi|juesi|diji|dichao|aiku|aiji|fankong|kongfan|jimo|zhiyuan|yuanjiu|yuanlai|zuoer|meimiao|youyong|jiji|jijing|meipin|pinmei|meiwei|weimei|baiyi|yibai|yixiang|xiangyi|hongpin|pinhong|juge|geju|yimei|meiyi|yishang|shangyi|hongxian|xianhong|hongxian|xianhong|yige|geyi|xianxiang|xiangxian|weishang|shangwei|feiji|feifei|bangfei|qianlian|meimen|huamei|yaqian|longya|lingyi|jingsong|heqi|fandong|yihe|dongxi|xike|taifei|yibai|taixi|yitai|xidong|lingyi|huaiyi|yinyu|meidian|taomi|piku|huaiyun|huaiyu|feiren|honghong|fufu|chengjing|jiangqing|jinghua|chengqing|shengshun|shunsheng|chengming|shengxin|mingshi|mingjin|chenghong|shengming|caihong|xintai|fanxin|xinfang|hongxin|shunfu|ronglun|lunrong|chunji|boji|geji|yinsen|xiqi|xihan|xifan|kongfa|loulan|yiqi|chunmeng|senchun|qiyi|jingyi|yige|yuyin|yizhi|menglang|weinan|nanqi|nanguai|chunmeng|yingsen|guguai|femgle|lefeng|yinglang|yinghe|guiguai|fangxia|nanshou|guiyu|yingyu|qiyu|feiyi|pingliu|liuyi|yibo|kaya|taomi|piku|kapi|jinan|jigu|huagu|qiji|meidian|dianmei|peiqi|lanmei|huadian|guwu|guji|lanqi|jiasha|jiaru|jiaer|shaya|erlong|aiya|nuozhi|luozhi|huangquan|jianren|weini|lingtang|youyu|nitian|xiaoxin|shuncon|xinhuan|beixin|quxing|taoxin|mihuan|fubo|jingtai|wenshun|jieyu|yuanhon|shitai|quanyu|shunshi|quanshi|taiquan|yuanquan|chengfu|fujing|yuanjing|fushi|futai|shijing|honmi|xiaomi|qiming|jiayan|yiming|lianming|mingyan|qilai|qiyi|mingnian|nianling|lingyi|yuanling|mamu|jiabei|feiwen|mubei|luomu|baipai|fengliu|bofu|jiaji|fanxing|wanyi|geju|gekai|zundina|dianxi|lingfu|shengtang|dishen|hehong|hefu|hehe|nikang|kangjia|huihui|huihe|duole|duoyi|shengyuan|longyang|huichen|youwang|maoyi|tianjia|quanjia|maoyi|liuhuan|lingyi|huaiyi|yinyu|meidian|taomi|piku|huaiyun|huaiyu|zhengqi|zhidao|yiyu|zhizhi|zizi|xinxiantaifeng|yibao|yijia|daoguan|daojia|daojia|lanlang|zhenjia|helan|linlan|duoyi|qingsong|zhizhi|jibo|boyi|feibiao|aiya|aifei|jiage|jiayuan|jiameng|jiahui|boqi|yingming|yingyuan|mengqi|zhihui|huiyuan|mengyou|jiaqi|jiayou|jiaming|jiaze|jiaxin|xinjia|zhujiao|yiqing|jiaoluo|changzhen|jiqiao|xinyi|boqi|runhua|huarun|qinghua|lianxi|jiemeng|jiaji|jiaojia|jiaorong|jiju|jiaju|zhenjia|helan|taifeng|fengge|daoguanjiubai|baijiu|yanren|renyan|yatuo|tuoya|heshang|shanghe|sanhe|hesan|shanhe|heshanhuangquan|quanhuang|huanquan|quanhuan|zhangyang|zhanyan|zhangyan|zanyan|zangyang';
        $arr = explode('|', $st1);
        $bool = false;
        foreach ($arr as $v) {
            if (str_contains($str, $v)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 要过滤所有的姓氏拼音
     * @return array
     */
    private function getXingFilter(): array
    {
        $list = $this->xing;
        $res = [];
        foreach ($list as $v) {
            $pyArr = $this->strToArr($v['py']);
            foreach ($pyArr as $v1) {
                if (empty($v1)) {
                    continue;
                }
                $py = Utils::formatPinYin($v1);
                $arr = [
                    $this->getHunXiaoYin($py['sm']),
                    $this->getHunXiaoYin($py['ym']),
                ];
                $res = array_merge($res, Utils::dikaer($arr));
            }
        }
        return array_values(array_filter($res));
    }

    /**
     * 字符串转字符
     * @param string $str
     * @return array
     */
    private function strToArr(string $str): array
    {
        if (empty($str)) {
            return [];
        }
        $str = str_replace(['，', ' ', '、'], ',', $str);
        $pyArr = explode(',', $str);
        return array_values(array_filter($pyArr));
    }

    /**
     * 获得混淆音
     * @param string $str
     * @return string[]
     */
    private function getHunXiaoYin(string $str): array
    {
        $res = [$str];
        $list1 = [
            ['z', 'zh'], ['c', 'ch'], ['s', 'sh'], ['f', 'h'], ['l', 'n'], ['an', 'ang'], ['en', 'eng'], ['in', 'ing'], ['ian', 'iang'], ['uan', 'uang'], ['u', 'iu'], ['i', 'v'], ['u', 'u'], ['u', 'v'], ['iao', 'ao'], ['iao', 'ia'], ['ao', 'an'], ['ao', 'ang'],
        ];
        foreach ($list1 as $v) {
            if (in_array($str, $v)) {
                $res = $v;
                break;
            }
        }
        return $res;
    }

    /**
     * 获得拼音
     * @param string $py
     * @return array
     */
    private function getPyInfo(string $py): array
    {
        $pyArr = $this->strToArr($py);
        $res = [];
        foreach ($pyArr as $v1) {
            $tmpPy = Utils::formatPinYin($v1);
            $res[] = $tmpPy;
        }
        return $res;
    }

    /**
     * 获得字详情
     * @param string $zi
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getZiInfo(string $zi = ''): array
    {
        if (empty($zi)) {
            return [];
        }
        $tmpData = $this->arrZi[$zi] ?? Cnword::info($zi);
        if (!empty($tmpData)) {
            return [
                'zi' => $zi,
                'py' => $tmpData['py2'],
                'wx' => $tmpData['wx'],
                'detail' => $tmpData['detail3']['mean'] ?? '',
            ];
        }
        return [
            'zi' => $zi,
            'py' => Pinyin::sentence($zi, 'none')->join(''),
            'wx' => '-',
            'detail' => '',
        ];
    }

    /**
     * 五行占比
     * @return array
     */
    protected function getWxPersent(): array
    {
        $wx = $this->lunar->getWuxingNum();
        $res = [];
        $total = array_sum($wx);
        foreach ($wx as $k => $v) {
            $res[] = [
                'wx' => $k,
                'per' => number_format($v * 100 / $total, 2),
            ];
        }
        return $res;
    }

    /**
     * 合化关系
     * @param $jiNian
     * @return array
     */
    protected function getGuanxi($jiNian)
    {
        $listHehua = [
            ['土' => ['甲己', '己甲'], '金' => ['乙庚', '庚乙'], '水' => ['丙辛', '辛丙'], '木' => ['丁壬', '壬丁'], '火' => ['戊癸', '癸戊']],
            ['土' => ['子丑', '丑子', '午未', '未午'], '木' => ['寅亥', '亥寅'], '火' => ['卯戌', '戌卯'], '金' => ['辰酉', '酉辰'], '水' => ['巳申', '申巳']],
        ];
        $tgArr = array_column($jiNian, 0);
        $dzArr = array_column($jiNian, 1);
        $tgCom = Utils::combination($tgArr, 2);
        $dzCom = Utils::combination($dzArr, 2);
        $dzCom3 = Utils::combination($dzArr, 3);
        $dzChong = [];
        $dzPo = [];
        $dzHai = [];
        $dzXing = [];
        foreach ($dzCom as $v) {
            $tmp = implode('', $v);
            if (BaziExt::getXianChongDz($v[0], $v[1])) {
                $dzChong[$tmp] = $tmp;
            }
            if (BaziExt::getXianPoDz($v[0], $v[1])) {
                $dzPo[$tmp] = $tmp;
            }
            if (BaziExt::getXianHaiDz($v[0], $v[1])) {
                $dzHai[$tmp] = $tmp;
            }
            if (BaziExt::getXianXinDz($v[0], $v[1])) {
                $dzXing[$tmp] = $tmp;
            }
        }
        $result = [
            'hehua_tg' => $this->getHehua($tgCom, $listHehua[0]),
            'liu_he' => $this->getHehua($dzCom, $listHehua[1]),
            'san_he' => $this->sanHeDz($dzCom3),
            'san_hui' => $this->sanHeDz($dzCom3, 1),
            'chong' => array_values($dzChong),
            'po' => array_values($dzPo),
            'hai' => array_values($dzHai),
            'xing' => array_values($dzXing),
        ];
        return $result;
    }

    /**
     * 判断组合数据是否
     * @param array $arr 天干或地支组合生成的数据
     * @param array $arr2 对比数据
     * @return array
     */
    protected function getHehua($arr, $arr2): array
    {
        $array = [];
        foreach ($arr as $v) {
            $array[] = implode('', $v);
        }
        $result = [];
        foreach ($arr2 as $k => $v) {
            $tmp = array_intersect($v, $array);
            if (empty($tmp)) {
                continue;
            }
            foreach ($tmp as $v1) {
                $result[] = "{$v1}合化为{$k}";
            }
        }
        if (empty($result)) {
            $result[] = '无合关系';
        }
        return $result;
    }

    /**
     * 三合或三会判断
     * @param array $arr 地支数组
     * @param int $bool 0为三合 1为三会
     * @return array
     */
    protected function sanHeDz($arr, $bool = 0): array
    {
        $listSanHe = ['水' => ['子', '辰', '申'], '金' => ['丑', '巳', '酉'], '火' => ['寅', '午', '戌'], '木' => ['卯', '亥', '未']];
        $listSanHui = ['水' => ['子', '丑', '亥'], '木' => ['寅', '卯', '辰'], '火' => ['巳', '午', '未'], '金' => ['申', '酉', '戌']];
        $list = $bool ? $listSanHui : $listSanHe;
        $name = $bool ? '三会' : '三合';
        $result = [];
        foreach ($arr as $v) {
            foreach ($list as $k1 => $v1) {
                $tmp = array_unique(array_intersect($v, $v1));
                $num = count($tmp);
                $str = implode('', $tmp);
                switch ($num) {
                    case 3:
                        $result[$str] = [
                            '地支' . $str . $name . '成局', $k1,
                        ];
                        break;
                    case 2:
                        $result[$str] = [
                            '地支' . $str . '半' . $name, $k1,
                        ];
                        break;
                }
            }
        }
        return array_values($result);
    }

    /**
     * 根据日柱获得个性分析
     * @return array
     */
    protected function getGeXing()
    {
        $jiNianDay = implode('', $this->lunar->getLunarGanzhiDay());
        $list = [
            '甲子' => '汉光武帝、岳飞、南怀瑾、宋庆龄',
            '甲寅' => '郭子仪、王永庆、朱熹、郭德纲、陆放翁',
            '甲辰' => '郭富城、黎明、张学友、林俊杰',
            '甲午' => '罗斯福夫人、拿破仑、索罗斯、许晋亨',
            '甲申' => '猫王、李嘉诚、刘涛、吴奇隆',
            '甲戌' => '钟楚红、林志玲、佘诗曼、古天乐、谢霆锋、林志玲、汪东城',
            '乙丑' => '克林顿、慈禧、甘地夫人、丘逢甲	',
            '乙卯' => '萧蔷、韩信、莎士比亚、忽必烈、刘伯温、王菲、欧阳修',
            '乙巳' => '钟镇涛、罗杰斯、温世仁',
            '乙未' => '黛安娜王妃、崇祯皇帝、王安',
            '乙酉' => '华盛顿、星云法师、陈英士',
            '乙亥' => '李鸿章',
            '丙子' => '诺贝尔、李小龙、汪小菲、梁咏琪、贾乃亮、赵小兰',
            '丙寅' => '郑成功、田亮、郭晶晶',
            '丙辰' => '关羽、达尔文、曾国藩、戈巴契夫、蔡少芬、熊黛林',
            '丙午' => '左宗棠、宋高宗、胡歌、刘亦菲',
            '丙申' => '爱因斯坦、刘嘉玲、杨钰莹、梁朝伟',
            '丙戌' => '普京、曾恺玹、孙燕姿、梅艳芳、纪晓岚',
            '丁丑' => '爱迪生、胡适、朱元璋、景甜、张杰',
            '丁卯' => '蒋经国、周恩来、吴宗宪、霍建华、陈嘉桦',
            '丁巳' => '李自成、文天祥、袁世凯、布莱尔、叶璇、田馥甄',
            '丁未' => '里根、章子怡、小S、汤唯、张歆艺、陈翔、朴灿烈',
            '丁酉' => '孙中山、赵匡胤、雍正帝、梅兰芳、张柏芝、魏晨、范冰冰	',
            '丁亥' => '方东美、光绪帝、刘恺威	',
            '戊子' => '列宁、姚明',
            '戊寅' => '钱穆、斯大林、张大千、俾斯麦、张菲、倪敏然',
            '戊辰' => '铁木真、马克思、蔡琴、艾森豪威尔、谢霆锋',
            '戊午' => '费曼、六祖惠能、李世民、李政道、李昌钰、杜鲁门、赵朴初',
            '戊申' => '康熙帝、辜振甫	',
            '戊戌' => '蔡康永',
            '己丑' => '太虚法师',
            '己卯' => '华盛顿夫人、林百里、洪晓蕾	',
            '己巳' => '罗斯福、蒋介石、汉武帝、李白、戚继光、乾隆帝',
            '己未' => '林肯、周恩来、欧纳西斯、龚如心',
            '己酉' => '周杰伦、梁洛施、奥黛丽赫本、赵孟頫',
            '己亥' => '顾维钧',
            '庚子' => '梁实秋、孔子、叶一茜',
            '庚寅' => '尼克松、苏芮、施崇棠',
            '庚辰' => '邓丽君、宋子文、刘邦、广欣法师、伊丽莎白女王、武则天',
            '庚午' => '撒切尔夫人、霍启刚、李嘉诚',
            '庚申' => '汤恩比、范蠡',
            '庚戌' => '范仲淹、文雅丽	',
            '辛丑' => '司马光',
            '辛卯' => '丘吉尔、迈克尔乔丹、证严法师、张爱玲',
            '辛巳' => '阿拉法特、华罗庚、贾静雯',
            '辛未' => '杜甫、李宗仁、李敖、肯尼迪',
            '辛酉' => '玛丽莲梦露、张居正',
            '辛亥' => '侯佩岑、李小璐、陈晓旭	',
            '壬子' => '巴菲特、张学良、朱德、李安',
            '壬寅' => '胡茵梦、刘翔',
            '壬辰' => '李光耀',
            '壬午' => '琼瑶',
            '壬申' => '宋美龄',
            '壬戌' => '老布什、卓别林、洪秀全、比尔盖茨、张清芳',
            '癸丑' => '玄奘、诸葛亮',
            '癸卯' => '唐三藏、杨贵妃、章太炎、李宇春、刘晓庆',
            '癸巳' => '成龙',
            '癸未' => '李昂、王安石、三毛	',
            '癸酉' => '张艾嘉、李嘉欣、韩愈',
            '癸亥' => '苏轼、王阳明、林青霞、刘德华',
        ];
        $list2 = [
            '急脾气' => ['type' => '爱憎分明型', 'info' => '这个孩子属于爱憎分明型，脾气来得快去得也快，是个急性子，由于拥有较强的主观意识，对于自己看不顺眼的都喜欢立刻指出来，因此性格大多偏外向，是个热心肠，但容易因嘴快得罪人。'],
            '暴脾气' => ['type' => '直来直往型', 'info' => '这个孩子属于直来直往型，性格刚烈，比较在意别人对自己的看法，自尊心很强，不喜欢被别人当面批评。偏爱简单直接的表达方式，不太懂得委婉，容易忽略细节之处，天生是有原则的人。'],
            '好脾气' => ['type' => '乐观自律型', 'info' => '这个孩子属于乐观自律型，个性温和善良，亲和力比较强，不会将自己的意愿强加在别人身上，天生没有对立意识，大多时候都是抱着好人主义思想，非常有爱心，喜欢帮助身边的人，脾气相对较好。'],
            '慢脾气' => ['type' => '随性自在型', 'info' => '这个孩子属于随性自在型，是个慢性子，缺乏主动竞争的意识，可能会给人不合群的印象，其实只是不喜欢跟人计较，做事不紧不慢，很容易沉浸在自己的小世界中，不喜欢待在太热闹的地方。'],
            '犟脾气' => ['type' => '顽强固执型', 'info' => '这个孩子属于顽强固执型，脾气犟，固执己见，考虑问题不会拐弯，为人处事缺乏认真细致的思考和观察，常常意气用事，不撞南墙不回头，最容易受到身边朋友的影响。'],
            '倔脾气' => ['type' => '主观自信型', 'info' => '这个孩子属于主观自信型，不会轻易为别人改变自己，非常自信，坚信自己的判断，很难接受来自旁人的意见和建议，不够稳重，思维模式属于主观型的，甚至有点自以为是的倾向。'],
        ];
        $str = '';
        if (in_array($jiNianDay, ['甲午', '庚子', '乙巳', '辛亥', '丙辰', '丙戌', '丁未', '丁丑', '戊申', '壬寅', '己酉', '癸卯'])) {
            $str = '急脾气';
        } elseif (in_array($jiNianDay, ['甲申', '乙酉', '戊寅', '己卯', '壬辰', '壬戌', '癸未', '癸丑'])) {
            $str = '暴脾气';
        } elseif (in_array($jiNianDay, ['丙子', '丁亥', '庚午', '辛巳', '甲子', '乙亥', '戊午', '己巳'])) {
            $str = '好脾气';
        } elseif (in_array($jiNianDay, ['丙寅', '丁卯', '庚辰', '庚戌', '辛丑', '辛未', '壬申', '癸酉'])) {
            $str = '慢脾气';
        } elseif (in_array($jiNianDay, ['甲寅', '乙卯', '丙午', '丁巳', '戊辰', '己未', '庚申', '辛酉', '壬子', '癸亥', '戊戌', '己丑'])) {
            $str = '犟脾气';
        } else {
            $str = '倔脾气';
        }
        $result = $list2[$str] ?? $list2['急脾气'];
        $result['ren'] = isset($list[$jiNianDay]) ? trim($list[$jiNianDay]) : $list['甲子'];
        return $result;
    }

    /**
     * 根据年柱地支获得特质
     * @return array
     */
    protected function getTeZhi()
    {
        $jiNianYear = $this->lunar->getLunarGanzhiYear();
        $sex = $this->lunar->sex;
        $diZhi = $jiNianYear[1];
        $list = [
            '子' => [
                [
                    'you' => '他精力充沛，个性活跃，好奇心很强，多给他了解不同领域知识的机会，他会从中找到适合自己的发展方向，成为优秀的专业型人才。另一方面，他富有想象力，同时天性乐观，不太适合刻板、沉闷的钻研，可以多鼓励他参加户外活动或接触文化艺术类兴趣课程，让他能一展所长，扩宽他的知识面。',
                    'que' => '他聪明却喜欢投机取巧，耍些小聪明，可以试着让他接触传统文化知识，或是由家长进行正确的引导，以身作则。另一方面，他有时明明是自己错了也不愿意承认，固执到底，父母应该多给他锻炼的机会，鼓励他勇于承担责任，就算做错了也不要一味地责备他，而要让他认识到错误的危害。',
                ],
                [
                    'you' => '她机智聪明，伶俐乖巧，愿意接触各方面的知识，多给她提供学习机会，让她能在广阔的学习空间里找到属于自己的发展方向。另一方面，她伶俐乖巧，亲和力较高，很讨人喜爱，可以多鼓励她参加演讲、表演等在公众面前展现才艺的活动，增强她的勇气和自信心，为未来的发展积累好人缘。',
                    'que' => '她性格不够大方沉稳，为了改善她胆怯的一面，在安全的环境中尽量鼓励她按照自己的意愿做事，独立自主地去选择，并且自己尝试去解决问题，而不是事事都是家长代劳。还可以多创造孩子与外界交往的机会，多带她出去旅游，增长见闻，同时家长也应该做个好榜样，以大方的态度接人待物。',
                ],
            ],
            '丑' => [
                [
                    'you' => '他很有耐心，在采取行动之前，往往都有自己的小计划，而且做事情有始有终，不会推卸责任，多让他独立完成力所能及的事情，哪怕只是很小的事情，并且在他顺利完成后给他鼓励，增强他的信心。他有上进心，有自己的理想，父母可以帮助他树立正确的世界观、人生观、价值观，让他学会乐观地看待未来。',
                    'que' => '他固执己见，不善于变通，有时表现得太过刻板，可以多让他参加一些有趣的活动，鼓励他尝试不同的思维方式思考问题。另一方面，他不善言辞，容易一个人钻牛角尖，父母在生活中应该多与他交流，了解他内心的想法，而不是单一地责备他的不足，减少他的顾虑。',
                ],
                [
                    'you' => '她的点子特别多，想象力丰富，父母可以帮助她记下奇思妙想，或者与她一起完成“小发明”，让她在实践中体会成功和失败的不同滋味，有利于行动力和创造力的培养。她会在父母的引导下收拾好玩具，家长可以为她提供独立做决定的机会，帮助她养成自立的习惯，有利于坚毅性格的养成。',
                    'que' => '她会利用自己的小聪明做些投机取巧的事情，父母在发现后要正确评价孩子的行为，对聪明给予赞扬，对投机取巧给予批评，让孩子懂得哪些该做哪些不该做。另外，她的目光短浅，考虑问题只考虑当前的或自己的，容易忽视身边人的感受，可以让她试着学会自己分配零食，感受时间和数量变化带来的影响，养成计划的意识。',
                ],
            ],
            '寅' => [
                [
                    'you' => '他对自己充满信心，敢于大胆表达自己的想法，父母要尊重他的意见，不要吝啬表扬和称赞，可以鼓励他多与同龄的孩子玩耍，增强社交能力。他很乐于接受挑战，在他无法独立完成事情的时候，父母可以适度给予间接帮助，完成一件他认为很难的事情，会让他信心倍增。',
                    'que' => '当他表现出叛逆倾向的时候，与其以命令的语气告诉他什么不要做，不如告诉他怎么做比较好，找出他抗拒的心理原因，再想办法解决。当他表现出霸道的缺点时，首先要理解他的感受，给他一定的自主权，不要用父母的标准约束他，尝试以不同的角度和孩子沟通交流，或让孩子体验他自己的要求。',
                ],
                [
                    'you' => '她有自己的想法，不会跟风人云亦云，信念坚定，父母在需要为孩子做决定时最好表现出尊重她看法的一面，并且在小事情上让她自己做决定，体会自主的感受。她做事不会半途而废，有耐心达成自己设定的目标，父母在她遇到困难的时候可以给予提示，但是不要帮她做好，在她完成后适时给予鼓励，增强她的自信心。',
                    'que' => '她内心固执，不容易接受别人的建议，父母不要总是在她面前强调当第一的重要性，最好潜移默化地让她学会取舍，学会接纳他人的意见，或商量出更好更实用的解决方法。同时父母也要注意自己的言行，在孩子面前不要太固执己见，更不要当着孩子的面与人争吵，以免给她做出坏榜样。',
                ],
            ],
            '卯' => [
                [
                    'you' => '他在与同龄人一起玩的时候，愿意将自己的愿望与集体相结合，可以多培养他的交往能力，在力所能及的范围里，教会他帮助身边的弱者，增强孩子的同情心。另一方面，可以逐渐培养他的自觉性，让他明白每一步行动的目的和意义，养成按照一定的要求约束自己的好习惯。',
                    'que' => '他的思维决断力可能比较弱，面对稍微复杂的选择，就会表现出犹豫不决的样子，父母可以在确保安全的前提下，为他提供一个自由玩耍的空间，在这个空间里，尽量不要干涉他，让他掌握主动权，试着自己做决定。同时也要包容他犯的小错误，不要因为一些无心之失的错误就盲目批评他，比如不小心弄坏玩具等。',
                ],
                [
                    'you' => '她个性温柔，心思细腻，能与其他小朋友和谐共处，父母可以着重培养她的人际交往能力。另一方面，她在语言方面有天赋，可以为她创造接触这方面知识的机会，开发孩子的潜在兴趣点。在孩子犯错时，父母可以引导她自己指出不足之处，鼓励她改正缺点的同时，也能锻炼孩子的语言表达能力。',
                    'que' => '她在决断力方面表现较差，面对选择时显得唯唯诺诺，父母在孩子还小时，不宜给予过多的选择权，也不要强硬地将责任硬塞给孩子，有目的地引导，让孩子慢慢学习选择为宜。可以利用一些简单的小游戏锻炼她的思维反应能力，提高孩子的灵活性。',
                ],
            ],
            '辰' => [
                [
                    'you' => '他凡事不愿服输，自我意识强烈，渴望独立自主，可以在保证安全的前提下给予他一定的自主权，让他感觉到自己受到尊重，同时可以为他安排一些静态的活动，让他学会冷静。他做事追求个人表现，会为了得到父母的赞赏而表现出努力的一面，父母可以培养他学习绘画或音乐以平衡情绪，并教导他不要太过计较得失。',
                    'que' => '他不喜欢做没有完美结果的事情，禁不起挫折和考验，缺乏坚韧不拔的个性，父母首先要尊重孩子的看法，引导他从多角度思考事情，学会弹性思维，不要只注重事情结果，也要关注过程。此外，还要鼓励他按照自己的兴趣建立生活目标，避免在生活中为他带来太大压力。',
                ],
                [
                    'you' => '她精力充沛，热情活泼，父母可以多为她创造展示自我的机会，有利于帮助她建立自信乐观的性格。由于她具有较强的自我意识，拥有不服输的意志，在独自完成小事情的过程中能够有始有终，父母要肯定孩子在过程中的努力，有意识地培养她的责任感。',
                    'que' => '她才华出众，容易产生傲慢自负的心理，看不起弱者，父母可以多让她接触优秀的人，在工作之余也可以带她去体验一些不一样的“苦”生活，有助于扩宽孩子的眼界和心胸。除此之外，在家里要掌握对孩子行为赞扬和批评的程度，及时让孩子认识到不足和缺点，引导她改正。',
                ],
            ],
            '巳' => [
                [
                    'you' => '他思考问题的方式多从感性出发，天生感知能力较强，父母可以重点培养他的创造力和想象力，避免过度干涉和保护孩子，视具体情况可以让孩子接触绘画或音乐等艺术方面，为他营造能发挥长处的成长环境。他感情丰富，父母在给予他奖励的时候不要仅仅停留在物质上，也要在精神层面多多关怀他的内心。',
                    'que' => '他的情绪变化比较大，不擅长表达内心感受，遇到压力有回避倾向，可以试着多给他一些鼓励，不要吝啬赞美的话语，引导他的性格往乐观大方的方向发展，并且在生活中可以鼓励他自己做决定，比如自己挑选玩具等，教他表达自己的想法。除此之外，父母也不要太过保护他，容易让他养成依懒性过强的坏习惯。',
                ],
                [
                    'you' => '她冷静沉着，思维灵敏，做事不会虎头蛇尾，多为她创造团队活动的机会，她会从中学会与其他人分工协作的技巧，强化对事物的观察和判断力。另一方面，她对精神生活的要求较高，心思细腻，也很适合往科学研究的方向发展，可以多鼓励她阅读科学画报等儿童科普读物，多带她出去认识大自然，锻炼她的洞察力。',
                    'que' => '她占有欲较强，个性柔弱但是不容易表露真心，有爱慕虚荣的表现，不易与朋友和谐相处，父母在家中就要有意识地培养孩子平等相处的观念，在分配东西时，不要只让孩子一人独享，引导孩子改善独享食物或玩具的想法，避免用命令的语气强迫孩子与他人分享。',
                ],
            ],
            '午' => [
                [
                    'you' => '他做事积极，领悟力较强，与其他小朋友相处起来也很融洽，喜欢接触新奇的玩具，父母可以带孩子参加一些寓教于乐的活动，激发他的好奇心和创造力，游玩的地点也可以选择科技馆等能增长见识的地方，同时也要加强他的责任感培养，困难的事情可以帮助他完成，但是千万不要替他完成，让他懂得有始有终。',
                    'que' => '他做事容易半途而废，主观性较强，随心所欲，讨厌被管束，父母不要因此就在他面前总是强调这些缺点，为他贴上负面标签，避免给他带来过多压力和伤害，可以在他能力所及范围内给他多一点“权力”，让他有自己做主的机会，在遇到困难的事情时，父母可以与他一起完成，让他体会成就感。',
                ],
                [
                    'you' => '她性格开朗，做事积极不容易拖延，多给她不同的学习机会，她会从中发现自己的兴趣所在，找到努力的方向。另一方面，她悟性高，思维敏捷，学习能力较强，可以根据她的兴趣引导她思考、学习，为她提供独立思考的空间，锻炼她的思维能力。',
                    'que' => '她喜欢热闹，好面子，爱慕虚荣，喜爱听到别人的赞美，不愿意接受别人的异议，主观意识较强，容易因为缺点被指出而心情低落。家长的一言一行都会对孩子的观念造成影响，无论家里经济条件好坏，都应该让孩子养成节俭的习惯，不盲目攀比，改善孩子的虚荣心需要一段很长的时间，切勿急躁。',
                ],
            ],
            '未' => [
                [
                    'you' => '他求知欲旺盛，喜欢接受新鲜的知识，爱观察身边的事物，并且问各种问题，父母可以有空多带他出去亲近大自然，让他有接触更多未知事物的机会，不可过分压抑他的好奇心，要知道孩子的好奇是了解世界的一种方式，可以让他养成自己查阅书籍解答疑惑的习惯，借此引导他涉猎更多的知识。',
                    'que' => '他显得有点羞怯，这是社交能力较弱的表现，家长不可为他贴上害羞的标签，要鼓励他与人交往，在旁人面前肯定他的看法，如果他表现出害怕的情绪，父母可以陪着他认识新的朋友，慢慢帮助他建立新的人际联系。在他想要安静的时候，也不要强迫他改正内敛的缺点，以免他产生自卑的心理。',
                ],
                [
                    'you' => '她个性温柔，会照顾身边的人，待人亲切，可以多鼓励她阅读有正面教育意义的故事书，多让她与同龄人相处，有助于孩子好性格的养成。她热爱大自然，父母应多带她出去了解世界，引导她观察自然，诸如四季植物的变化等，锻炼她的观察能力和思考能力。',
                    'que' => '她个性柔弱胆小，在人多的场合会害羞，甚至有点悲观的情绪，平时多带她参加户外活动，适当的体育运动也是不可缺少的，有助于培养孩子的自信心。另一方面，她个性固执，害怕犯错，不愿意轻易改变自己的决定，父母应多鼓励她尝试多种解决问题的方法，就算她做错或失败了也不要严厉批评她。',
                ],
            ],
            '申' => [
                [
                    'you' => '他反应快，聪明伶俐，有较强的表现欲，可以让他参加演讲等能满足自我表现欲的活动，鼓励他往自己感兴趣的领域发展。他记忆力很好，头脑灵活，在为他挑选兴趣班或购买玩具时，尽量不要选择需要长时间思考，过于沉闷的类型，以能激起他的兴趣并能让他感受到快乐为宜。',
                    'que' => '他在做事的时候表现得比较急，缺乏耐心，喜新厌旧，对父母的依赖心较重，在他寻求家长帮助的时候，父母可以先鼓励他自己思考，而不是立刻帮他做好。另一方面，这种表现也可能是他缺乏安全感导致的，父母最好每周都能抽出时间陪他玩，让他感受到家庭的温暖，慢慢帮助他建立内心的安全感。',
                ],
                [
                    'you' => '她头脑灵活，求知欲很强，做事不容易钻牛角尖，喜欢接触各种新鲜有趣的知识，可以为她提供一些内容有趣的书籍，或寓教于乐的故事书，有助于她知识的积累。另一方面，她能言善道，表现欲望较强，适合演讲、辩论，可以引导她自己阅读故事书，并复述出来，锻炼她的语言表达能力和临场发挥能力。',
                    'que' => '她有点爱说大话，有时会有撒谎的行为，令人对她产生不好的印象，导致她不容易有知心朋友，可以多与她沟通交流，了解她撒谎的原因，引导她用正确的方式与人交往。另一方面，她依赖心很重，做事缺乏持久的毅力，有点三分钟热度，父母应鼓励她独立做好一些简单的事情，在完成后要及时给予称赞，并指出不足以便孩子改正。',
                ],
            ],
            '酉' => [
                [
                    'you' => '他天生对色彩的感觉有独到之处，多给他提供接触不同色彩的机会，有利于找出他的兴趣点，为他准备的书籍也可以多以彩色绘本为主。他头脑灵敏，同时也有深思熟虑的一面，可以鼓励他学习辩论，锻炼他的勇气和语言表达能力。',
                    'que' => '他做事容易三分钟热度，情绪变化较快，缺乏意志力，可以让他在安全范围内逐渐习惯自己做好自己的事，在孩子书籍的选择上可以偏向寓言类故事，帮助孩子心灵的成长。另一方面，他坚持己见，不愿意听人劝告，父母应引导他学会与别人和睦共处，正确表达自己的建议。',
                ],
                [
                    'you' => '她的脑筋转的很快，对色彩的变化感觉独到，多为她提供色彩相关的兴趣活动，帮助她寻找自己感兴趣的方向。另一方面她个性专注，善于结交朋友，在语言表达和人际交往方面潜质颇大，适合思考和协作，可以多鼓励她参加团体活动，锻炼她与同龄人相处的能力。',
                    'que' => '她的情绪变化很快，有时热情开朗，有时却很冷漠，令人误解她脾气不好，可以让她多阅读一些心灵成长方面的书，或者让她参加一些合适的运动。另一方面，她说话不保留，容易忽视别人的感受，父母应教会她换不同的角度考虑问题，培养她的同情心和对弱者的关注。',
                ],
            ],
            '戌' => [
                [
                    'you' => '他勇敢富有正义感，会主动帮助弱者，鼓励他参加体育活动，他会在强健体魄的过程中养成坚毅有责任感的性格，成为正直的男子汉。另一方面，他诚实友善，直觉敏锐，能明辨是非，适合当领导者，可以多鼓励他参加班干部的选拔，锻炼他的领导能力和组织能力。',
                    'que' => '他情绪起伏较大，易怒，性格倔强逞强，不会主动承认错误，却喜欢批评别人，可以让他适当感受挫折，父母应让他体会到家庭的温暖，不仅在物质上，也能在精神层面给予他关心，让他明白承认自己不足也是变强大的一种方式，并引导他控制自己的情绪，在愤怒时学会疏导怒气。',
                ],
                [
                    'you' => '她个性直率，诚实友善没心机，多以温和的态度和她沟通，尊重她的意见，使她在充满正能量的环境中成长，有利于正面性格的养成。另外，她思维敏捷，头脑反应较快，可以多鼓励她思考简单问题，循序渐进，锻炼她的思维能力，扩展分析问题的思路。',
                    'que' => '她的感情起伏大，有时会莫名的沉默，有时却热情积极，可以让她多阅读一些心灵成长方面的书，或者让她参加一些合适的运动。另一方面，她过于重视理论，缺乏实践能力，父母可以为她多提供一些生活实践的机会，鼓励她参与，从父母陪伴她完成慢慢过渡到独立完成，锻炼她的行动力和判断力。',
                ],
            ],
            '亥' => [
                [
                    'you' => '他天性乐观，做事认真，求知欲强，多为他提供不同内容的绘本和故事书，也可以适当让孩子看看科普类儿童书籍，给他创造了解不同领域知识的机会，有助于借助他的好奇心培养兴趣。另外，他对待朋友慷慨大方，可以多鼓励他参加团体活动，培养他人际交往的能力，帮助他获得好人缘。',
                    'que' => '他对人毫无疑心，缺乏安全观念，可以让他适当看一些培养安全意识的书籍或视频，让他懂得保护自己。除此之外，他做事缺乏变通，有点一根筋的倾向，父母应引导他思考问题时多想想其他解决方法，也可以试着通过寓言或童话故事教会他条条大路通罗马的道理，培养他的多向思维方式。',
                ],
                [
                    'you' => '她天性乐观，率直真诚，求知欲强，可以适当让孩子看看科普类儿童书籍，给她创造了解不同领域知识的机会，有助于借助她的好奇心培养兴趣。另外，她对待朋友慷慨大方，可以多鼓励她参加团体活动，培养她人际交往的能力，帮助她获得好人缘。',
                    'que' => '她缺乏变通协调的精神，习惯于采用刻板的方式解决问题，遇到突发情况容易自乱阵脚，茫然无措。父母可以多培养她提供独立处理事情的能力，引导孩子从不同的角度思考问题的解决方法，避免机械性的填鸭式教育，尝试慢慢让她自己提出做法，失败了也不要急于责备她，有利于培养她的自立能力。',
                ],
            ],

        ];
        return $list[$diZhi][$sex] ?? $list['子'][$sex];
    }

    /**
     * 获得先天潜力
     * @return array
     * @throws Exception
     */
    protected function getQianli(): array
    {
        $wangDu = $this->wangDu;
        // 行动力 创造力 领导力 思考力 表达力 应变力
        $baseFen = [
            55, 55, 55, 55, 55, 55,
        ];
        if (in_array($wangDu, ['身旺格', '从弱格'])) {
            $baseFen = [
                70, 70, 70, 70, 70, 70,
            ];
        }
        $listShen = $this->xy['shen'];
        // 比肩 劫财 食神 伤官 偏财 正财 七杀 正官 偏印 正印
        $list2 = [
            '比劫' => ['比肩', '劫财'], '印枭' => ['正印', '偏印'],
            '官杀' => ['正官', '七杀'], '才财' => ['正财', '偏财'], '食伤' => ['食神', '伤官'],
        ];
        $list3 = [
            '正官' => [6, 2, 18, 5, 9, 11],
            '七杀' => [18, 6, 11, 2, 5, 9],
            '正印' => [5, 2, 9, 18, 6, 11],
            '偏印' => [6, 18, 5, 11, 2, 9],
            '比肩' => [11, 2, 5, 9, 6, 18],
            '劫财' => [18, 5, 2, 11, 6, 9],
            '正财' => [6, 2, 5, 9, 11, 18],
            '偏财' => [9, 2, 6, 5, 18, 11],
            '食神' => [2, 11, 9, 18, 5, 6],
            '伤官' => [6, 18, 5, 9, 2, 11],
        ];
        $list4 = [
            '正官' => [8, 4, 20, 7, 11, 13],
            '七杀' => [20, 8, 13, 4, 7, 11],
            '正印' => [7, 4, 11, 20, 8, 13],
            '偏印' => [8, 20, 7, 13, 4, 11],
            '比肩' => [13, 4, 7, 11, 8, 20],
            '劫财' => [20, 7, 4, 13, 8, 11],
            '正财' => [8, 4, 7, 11, 13, 20],
            '偏财' => [11, 4, 8, 7, 20, 13],
            '食神' => [4, 13, 11, 20, 7, 8],
            '伤官' => [8, 20, 7, 11, 4, 13],
        ];
        $yongArr = $list2[$listShen['yong']];
        $jiArr = $list2[$listShen['ji']];
        foreach ($yongArr as $v) {
            $tmp = $list3[$v];
            $baseFen = [
                $baseFen[0] + $tmp[0],
                $baseFen[1] + $tmp[1],
                $baseFen[2] + $tmp[2],
                $baseFen[3] + $tmp[3],
                $baseFen[4] + $tmp[4],
                $baseFen[5] + $tmp[5],
            ];
        }
        foreach ($jiArr as $v) {
            $tmp = $list4[$v];
            $baseFen = [
                $baseFen[0] - $tmp[0],
                $baseFen[1] - $tmp[1],
                $baseFen[2] - $tmp[2],
                $baseFen[3] - $tmp[3],
                $baseFen[4] - $tmp[4],
                $baseFen[5] - $tmp[5],
            ];
        }
        $list5 = [
            // 行动力
            [
                [
                    'star' => 4.5,
                    'fx' => '孩子的行动力较好，可以按时完成自己制定的小计划，拖拉现象出现的频率很低，在父母的帮助下勇于面对困难的事情。',
                    'suggest' => '不要对孩子吝于奖励和称赞，只要孩子完成一项任务就应该及时给予夸奖，也可以告诉孩子积累了一定数量的夸奖就能在周末的时候去游乐场玩，这样孩子在努力的时候会比较有动力。孩子的教育过程辛苦而漫长，父母一定要有耐心。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子具有自我约束力，但是面对难度较大的事情会显得想要退缩或逃避，或通过耍小聪明的方法来走捷径，行动力不算特别优秀。',
                    'suggest' => '在孩子表现出较好的行动力时，父母应给予真诚的鼓励和赞美，让孩子从父母身上获得积极和正面的能量。在孩子表现出散漫、逃避的态度时，父母不要总是责骂，而应该细心观察了解问题的症结所在，才能帮助孩子改变不好的习惯。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子在生活中会出现做事拖拉的情况但并不是很严重，在父母适当督促下也能慢慢学着完成洗漱、穿衣等小事情。',
                    'suggest' => '孩子行动力可能与家庭环境有关系，父母最好不要经常性地否定孩子的善意行为，比如主动叠衣服时被指责叠得不整齐，避免孩子由于想减少父母对自己的否定而变得做什么事都缺乏干劲，影响行动力发展。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子在完成事情的过程中容易被玩具、电视等吸引注意力，做完一件事情需要比别的孩子花更多的时间，行动力难以得到体现。',
                    'suggest' => '孩子无法集中注意力可能是身边有太多因素影响了行动力，有条件的父母可以为孩子创造一个安静的环境，或者尝试让孩子从独立完成简单的小事情开始，一步一步引导孩子保持专注。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '孩子在生活中可能不同程度地出现拖拉磨蹭的表现，比如早晨赖床，做事需要反复催促等，缺少时间观念，行动力不太理想。',
                    'suggest' => '孩子的思维模式与大人不同，脑海里总是有各种想法，但是计划永远只是停留在纸面上，这时候就需要父母的帮助，可以尝试将孩子的计划分解成一个一个容易做到的小目标，让孩子逐步尝试着实现，帮助孩子体会到成功的快乐，培养行动力。',
                ],
            ],
            // 创造力
            [
                [
                    'star' => 4.5,
                    'fx' => '孩子有向权威思想挑战的勇气，会质疑书本上的内容，并渴望将自己的小发现告诉父母，希望得到家人的认可。',
                    'suggest' => '培养孩子创造力的同时，也要顾及解决问题能力的培养，在可控的安全范围内让孩子多自己动手、动脑去解决问题，就算偶尔失败，孩子也能在获取经验与教训的过程中获得成长的机会，避免孩子的创造力仅仅只是空想。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子对各种事情表现出好奇心，并渴望了解未知的事物，有追根究底的倾向，会主动向父母提出各种各样的问题。',
                    'suggest' => '家庭环境是培养孩子创造力的重要条件，父母可以多带孩子去参加户外活动，与孩子一起玩耍，在和谐、宽松的氛围中，培养孩子的创造力，并在安全的范围内信任孩子的能力，使孩子树立起探索知识的勇气和信心。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子会将想象中的事物画出来或者表现出想要拆解小物品的想法，但是受各方面条件限制，不太敢表现出自己的想法。',
                    'suggest' => '孩子总喜欢产生一些不合常理的想法，对于这些想法，家长切不可以“胡闹”等指责加以否定，这样不利于孩子的创造力发展，父母可以运用多种形式激发孩子的想象力和创造力，如讲故事、听音乐等。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子虽然偶尔也会表现出想象力丰富的一面，但是在父母面前还是习惯于规行矩步，不敢对书本等权威提出质疑。',
                    'suggest' => '孩子对事物产生好奇，应鼓励孩子提出，引导孩子积极思考，父母也要善待孩子的提问，认真解答并尽量回答得生动具体，让孩子从小就能享受到探索事物奥妙的乐趣，有利于培养孩子的创造力。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '孩子在同龄人中显得有些平庸，做事总是非常规矩，虽然听话乖巧，却显得缺乏创意和解决小问题的能力。',
                    'suggest' => '父母不要将注意力只集中在量化知识上，比如识字量、外语单词量等，而忽视了对于创造力的培养，适当允许孩子问些稀奇古怪的事情，与孩子一起尝试各种简单的生活小实验，都是能培养孩子创造力特质的方法。',
                ],
            ],
            // 领导力
            [
                [
                    'star' => 4.5,
                    'fx' => '孩子在小团队中经常表现出领袖的才能，会同情并帮助其他小朋友，并能调动身边的小伙伴与自己一起完成某件事情。',
                    'suggest' => '父母可以策划一些小活动，锻炼孩子的领导力，如让孩子自己组织一场小型生日会，安排家庭扫除分工等，逐渐从父母引导过渡到让孩子自己做决定。让孩子了解人员调配和工作分工的概念，有利于领导力的培养。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子在团队游戏中表现较好，有个人魅力和气质，肯听取别人的意见，并能赢得别的小朋友的信任。',
                    'suggest' => '父母在教育孩子的时候，也应该注意自身的言传身教，孩子最好的榜样就是身边的亲人，如果你想让孩子成为具备领导力的人，自己也应该在孩子面前展现如何有效地在团队中工作，可以从家务或家庭活动开始。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子能独立完成一些简单的小任务，但是在与其他小朋友交流和团队活动中还表现出缺点和不足，缺少自信，情绪多变。',
                    'suggest' => '鼓励孩子多参加团体活动，为孩子提供可以接触到新朋友的机会，慢慢学会与人打交道的技巧，也让孩子有机会学习集体交往的规则，积累领导经验，提升领袖气质，并适当鼓励孩子，帮助孩子建立自信。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子在与其他小朋友玩耍时比较低调，不会自己拿主意，更愿意遵从其他孩子的意愿行动，显得没什么主见。',
                    'suggest' => '父母不要强迫孩子服从自己，要尊重孩子的建议，给予孩子自己做决定的机会，比如给孩子买衣服，可以尝试与其商量颜色、款式等，让孩子从小事情的决定做起，这可以培养孩子的潜在领导力。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '当孩子处于陌生的同龄人群中，表现得不是很自在，甚至会哭着找父母，或是对别的小朋友的友善接近表现出排斥。',
                    'suggest' => '孩子没表现出领导力，也许是缺乏自信的原因。父母可以循序渐进的帮助孩子建立信心，鼓励孩子与别的小朋友建立友好的人际关系，培养孩子的沟通技巧，帮助孩子学会与他人相处。',
                ],
            ],
            // 思考力
            [
                [
                    'star' => 4.5,
                    'fx' => '对于自己弄不懂的问题，孩子会找相关的书籍学习，并且在遇到困难时独立思考解决的方法，哪怕这些方法显得不太有用。',
                    'suggest' => '在培养孩子思考力的同时，也要注意父母自己的思考力表现，在孩子面前起到榜样的作用，如果父母经常不加思考就做出决定，或者总是跟随其他人的方法做事，会让孩子产生不思考也能解决问题的错误印象。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子会主动根据阅读过的故事书或绘本提出自己的疑问，并且能在父母的引导下，思考更多的问题，表达自己的看法。',
                    'suggest' => '遇到和儿童有关的话题不妨与孩子讨论一些简单的问题，询问孩子的观点，让孩子学会表达和组织语言，也可以试着让孩子思考一些简单问题的更好解决方法，如果孩子的想法有创意，不要吝啬给予夸奖。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子有思考的能力，但是对身边事物的关注度不够，遇到稍微困难的问题就立刻选择放弃，明明自己能做的事也不会主动动手去做。',
                    'suggest' => '父母不仅要鼓励孩子进行思考，必要的时候也要与孩子一起解决困惑，激发孩子学习多种思维方式，帮助孩子认识到事情不仅仅只有一种可能，处理问题的方法也可以有很多种，想问题的时候不能太绝对。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子在学习知识的过程中会产生一些疑问，但是注意力容易被其他事情转移，无法做到专注思考，也很难顺利地独立得出问题的答案，需要父母的帮助。',
                    'suggest' => '孩子天生拥有强烈的好奇心，父母不要因为工作忙碌就在孩子面前表现出不耐烦，尊重和保护孩子的好奇心对于培养思考力尤为重要，好奇是引发思考的原因，自己的提问被认真对待，孩子也会愿意去探求现象背后的深层知识。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '孩子遇到再小的事情也都是优先找父母解决，自己很少去思考如何完成一件事情，习惯于听从别人的指示行动，缺乏深度思考的能力。',
                    'suggest' => '父母应该多为孩子提供自己分析问题的机会，不要事事都替孩子想好，可以多用疑问句引导孩子自己思考，可能孩子一开始无法马上回答，需要家长慢慢启发，逐渐让孩子习惯独立思考。',
                ],
            ],
            // 表达力
            [
                [
                    'star' => 4.5,
                    'fx' => '无论是在家里还是户外，孩子表现得较为外向，有问题会主动询问父母，用语言表达自己意愿的能力比较强，能在听完故事后复述主要内容。',
                    'suggest' => '父母有必要筛选孩子接触的电视节目，避免孩子在质量参差不一的节目上过早地学会不规范用语，影响语言的表达能力发展。在尊重孩子语言发育规律前提下，打好表达能力基础。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子语言表达比较清晰，顺利掌握一部分生活中的常用词汇，与父母的交流较多，在父母在场的情况下能与陌生人进行简单的对话。',
                    'suggest' => '生活中，父母应该多观察孩子的兴趣，如果孩子对故事书的内容失去兴趣，不妨将话题转移到孩子感兴趣的事物上，引导孩子多去观察、探求、思考，在学习中积累词汇量，慢慢学会表达的技巧。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子基本可以通过语言表达出内心的想法，但是有时会出现词不达意的情况，或是有错误发音的现象，个别字吐字不清晰。',
                    'suggest' => '父母可以让孩子从小就养成读书的习惯，最初从小卡片开始，逐渐发展到带有彩色图片的绘本，等孩子年龄大一点，再慢慢过渡到文字多一些的故事书，引导孩子思考问题并给出答案，鼓励孩子用自己的语言复述故事。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子说话不太清楚或词不达意，很难清晰地表达出自己想要表达的意思，不擅于与身边的人交流，遇到陌生人几乎不说话。',
                    'suggest' => '父母要想办法激发孩子说话的欲望，要让孩子学会用语言表达出内心的需求，如孩子在用手势或眼神表达饿了的时候，父母可以进一步询问，引导孩子说出内心想法后，再给予，日常生活中别急着替孩子说话。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '孩子表达自己意愿的欲望很低，表达方式缺乏条理，或因为害怕说不好而被责骂、笑话，而隐藏自己的真实想法。',
                    'suggest' => '父母在孩子学习语言表达的过程中，不要过于急躁，与孩子说话时，可以放慢语速，让孩子有个理解语言的过程，多与孩子说话，给予孩子倾听的机会，听得多了，孩子的表达能力就会慢慢好起来。',
                ],
            ],
            // 应变力
            [
                [
                    'star' => 4.5,
                    'fx' => '孩子有能力自己处理一些小问题，遇到困难不会过度慌乱、哭泣，与小朋友闹矛盾也能好好解决，这正是应变能力高的表现。',
                    'suggest' => '孩子自信、坚毅的性格离不开生活环境的影响和父母的教育，过分溺爱只会让孩子缺乏独立意识，凡事只想到依赖别人或者逃避现实。充分利用父母的榜样作用，在此基础上给予孩子正确的引导，孩子自然会变得越来越机智。',
                ],
                [
                    'star' => 4,
                    'fx' => '孩子能独立完成简单的事情，在遇到突发状况时，会寻找身边的人帮助，模仿书籍或电视节目中的方法进行应对。',
                    'suggest' => '父母应该从小培养孩子自己的事情自己做的习惯，让孩子在经验和教训中成长，切勿过度保护，在力所能及的范围内让孩子独立完成事情，或许孩子一开始会有些迷茫，父母可以给一些提示帮助孩子思考，提高孩子解决问题的能力。',
                ],
                [
                    'star' => 3.5,
                    'fx' => '孩子遇到突发事情时虽然表现出惊慌，但会立刻寻找父母或身边人的帮助，能慢慢适应陌生的环境，愿意与同龄的小朋友沟通交流。',
                    'suggest' => '每个人在解决问题时都有自己的一套模式，父母最好不要将自己的经验让孩子生搬硬套，而是启发孩子去思考更多的解决方法，让孩子意识到同一个问题可以有多种方法解决，明白小问题只要冷静应对其实并不可怕。',
                ],
                [
                    'star' => 3,
                    'fx' => '孩子在慌张害怕的时候会寻求父母的帮助，但是对陌生人表现出的善意帮助显得迷茫无助，不知所措，应变能力较差。',
                    'suggest' => '父母可以借助孩子的好奇心和模仿力带来的良好学习能力，引导孩子学习独立解决问题的能力，比如在看到电视上发生火灾的报道时，让孩子了解在紧急情况下自救的知识，通过提问的方式让孩子说出自己的看法。',
                ],
                [
                    'star' => 2.5,
                    'fx' => '孩子遇到突发事情或面对陌生环境时表现得非常慌张害怕，适应环境变化的速度比较慢，习惯于用哭泣等行为进行逃避。',
                    'suggest' => '父母可以为孩子多提供一些锻炼的机会，比如参加一些具有挑战性的活动，引导孩子在游戏中通过思考或动手解决问题，必要时可以亲自示范教会孩子，孩子的应变力会在锻炼中逐渐增强。',
                ],
            ],
        ];
        $list6 = ['x', 'c', 'l', 's', 'b', 'y'];
        $result = [];
        foreach ($list5 as $k => $v) {
            $tmpFen = $baseFen[$k];
            $tmpRes = [];
            if ($tmpFen > 80) {
                $tmpRes = $v[0];
            } elseif ($tmpFen > 70) {
                $tmpRes = $v[1];
            } elseif ($tmpFen > 50) {
                $tmpRes = $v[2];
            } elseif ($tmpFen > 40) {
                $tmpRes = $v[3];
            } else {
                $tmpRes = $v[4];
            }
            $tmpRes['feng'] = $tmpFen;
            $result[$list6[$k]] = $tmpRes;
        }
        return $result;
    }

    /**
     * 获得姓氏列表
     * @return array
     */
    protected function getXingArr(): array
    {
        return Xing::cache(600)->column('title');
    }
}
