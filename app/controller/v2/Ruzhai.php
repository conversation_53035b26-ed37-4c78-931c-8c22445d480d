<?php
// +----------------------------------------------------------------------
// | Ruzhai
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\lib\Utils;
use app\traits\jiri\JiRiCheckBadTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Ruzhai
{
    use JiRiCheckTraits;
    use JiRiCheckBadTraits;

    /**
     * 日历基础
     * @var Ex
     */
    protected Ex $lunar1;

    /**
     * @var Ex|null
     */
    protected ?Ex $lunar2 = null;

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户冲刑地支
     * @var array
     */
    protected array $chongXingDz = [];

    /**
     * 年份对应的节气信息
     * @var array
     */
    protected array $jieQiYear = [];

    /**
     * 开业吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time1' => input('time1', '', 'trim'),
            'sex1' => input('sex1', 0, 'intval'),
            // 出生时间2
            'time2' => input('time2', '', 'trim'),
            'sex2' => input('sex2', 1, 'intval'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 默认3个月
            'longs' => input('longs', 3, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time1|出生时间1' => ['require', 'isDateOrTime:出生时间1'],
                'sex1|性别1' => ['require', 'in:0,1'],
                'time2|出生时间2' => ['isDateOrTime:出生时间2'],
                'sex2|性别2' => ['in:0,1'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'longs|时间区间' => ['require', 'between:1,25'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar1 = Ex::date($data['time1'])->sex($data['sex1']);
        $pan = [
            $this->getBazi($this->lunar1),
        ];
        if (!empty($data['time2'])) {
            $this->lunar2 = Ex::date($data['time2'])->sex($data['sex2']);
            $pan[] = $this->getBazi($this->lunar2);
        }
        $this->chongXingDz = $this->getChongXing();
        $result = [
            'pan' => $pan,
            'chong_xing' => $this->chongXingDz['all'],
            'ji' => $this->getDayList(),
        ];
        return $result;
    }

    /**
     * 八字基础信息
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBazi(Ex $lunar): array
    {
        $base = $lunar->getLunarByBetween();
        $year = (int)$lunar->dateTime->format('Y');
        $jq = SolarTerm::getAllJieQi($year);
        $liChun = $jq['立春'];
        $res = [
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
            'lichun' => $liChun,
        ];
        $res = array_merge($base, $res);
        return $res;
    }

    /**
     * 获得跟用户年柱冲刑地支
     * @return array
     */
    protected function getChongXing(): array
    {
        $ygz = $this->lunar1->getLunarGanzhiYear();
        $ydz = [$ygz[1]];
        if ($this->lunar2) {
            $ygz = $this->lunar2->getLunarGanzhiYear();
            $ydz[] = $ygz[1];
        }
        $resChong = [];
        $resXing = [];
        foreach ($ydz as $dz) {
            $chongDz = BaziCommon::getChongDz($dz);
            $resChong[$chongDz] = $chongDz;
            $xing = BaziCommon::getXingByDz($dz);
            foreach ($xing as $v) {
                $resXing[$v] = $v;
            }
        }
        $resAll = array_values(array_merge($resChong, $resXing));
        return [
            'chong' => array_values($resChong),
            'xing' => array_values($resXing),
            'all' => $resAll,
        ];
    }

    /**
     * 获得日子列表
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        $otime = strtotime($this->orginData['otime']);
        $long = $this->orginData['longs'];
        $maxTime = strtotime("+{$long}month", $otime);
        $limitNum = round(($maxTime - $otime) / 86400);
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳节',
            '10_1' => '寒衣节',
        ];
        $result = [];
        $explain = [];
        $changeExplain = [];
        $chongXinDz = $this->chongXingDz;
        $listHour = $this->getHourUse();
        $jiNianU = $this->lunar1->getLunarTganDzhi();
        $ydzU = [$jiNianU['y'][1]];
        $noHourSiMing = ['酉', '戌', '亥'];
        if ($this->lunar2) {
            $jiNianU2 = $this->lunar2->getLunarTganDzhi();
            $ydzU[] = $jiNianU2['y'][1];
        }
        for ($i = 0; $i < $limitNum; $i++) {
            $curTime = $otime + ($i * 86400);
            $cYear = date('Y', $curTime);
            $cGongli = date('Y-m-d', $curTime);
            $keyStr = date('Y-m', $curTime);
            $jieqiArr = $this->getJieqiByYear($cYear);
            $jieQi = $jieqiArr[$cGongli] ?? '';
            $huangli = Huangli::date($cGongli);
            $base = $huangli->getLunarByBetween();
            $week = Huangli::getWeekChs($curTime);
            $jxArr = $huangli->getJiXiong();
            $jiShenHl = $this->filterJXShen($jxArr['jishen']);
            $xiongHl = $this->filterJXShen($jxArr['xiong'], 1);
            $zhiRi = $huangli->getZhiRi();
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $hdType = $zhiRi['huan_dao'] == '黑道' ? 0 : 1;
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $mdNum = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            $ydz = $jiNian['y'][1];
            $mdz = $jiNian['m'][1];
            $ddz = $jiNian['d'][1];
            $dtg = $jiNian['d'][0];
            $ytg = $jiNian['y'][0];
            $dzMd = $mdz . $ddz;
            $dgz = implode('', $jiNian['d']);
            $reason = [];
            $reasonJr = [];
            $xiongRes = [];
            $xingChongArr = [];
            if (in_array($ddz, $chongXinDz['chong'])) {
                $xingChongArr[] = '冲';
            }
            if (in_array($ddz, $chongXinDz['xing'])) {
                $xingChongArr[] = '刑';
            }
            if ($this->checkYangGongOnly($mdNum)) {
                $reasonJr[] = '杨公忌日';
            }
            if ($jieQi == '清明') {
                $reasonJr[] = '清明节';
            }
            foreach ($listNo as $k => $v) {
                if ($k == $mdNum) {
                    $reasonJr[] = $v;
                }
            }
            if ($this->checkSuiPo($ydz . $ddz)) {
                $reason[] = '岁破';
            }
            if ($this->checkShouSi($dzMd)) {
                $reason[] = '受死';
            }
            //不可变凶神月厌、归忌、往亡、月破、四废
            //            if ($this->checkYueYan($mdz, $ddz)) {
            //                $xiongRes[] = '月厌';
            //            }
            if ($this->checkGuiJi($mdz, $ddz)) {
                $xiongRes[] = '归忌';
            }
            if ($this->checkWangWang($mdz, $ddz)) {
                $xiongRes[] = '往亡';
            }
            if ($this->checkYuePo($dzMd)) {
                $xiongRes[] = '月破';
            }
            if ($this->checkSiFei($mdz, $dgz)) {
                $xiongRes[] = '四废';
            }
            if ($this->checkTianZei($mdz, $ddz)) {
                $xiongRes[] = '天贼';
            }
            $tomorrow = date('Y-m-d', $curTime + 86400);
            $jqNext = $jieqiArr[$tomorrow] ?? '';
            if (in_array($jqNext, ['立春', '立夏', '立秋', '立冬'])) {
                // 四绝
                $reason[] = '四绝';
            } elseif (in_array($jqNext, ['春分', '秋分', '夏至', '冬至'])) {
                $reason[] = '四离';
            }
            $tianDe = $this->checkTianDe($dzMd) || $this->checkTianDe($mdz . $dtg);
            $yueDe = $this->checkYueDe($dzMd) || $this->checkYueDe($mdz . $dtg);
            $tianDeHe = $this->checkTianDeHe($dzMd) || $this->checkTianDeHe($mdz . $dtg);
            $yueDeHe = $this->checkYueDeHe($dzMd) || $this->checkYueDeHe($mdz . $dtg);
            $tianYuan = $this->checkTianYuan($mdz . $dgz);
            $deArray = [];
            if ($tianDe) {
                $deArray[] = '天德';
            }
            if ($yueDe) {
                $deArray[] = '月德';
            }
            if ($tianDeHe) {
                $deArray[] = '天德合';
            }
            if ($yueDeHe) {
                $deArray[] = '月德合';
            }
            $jieSha2 = $this->checkJieSha2($mdz, $deArray) ? 1 : 0;
            $zaiSha2 = $this->checkZaiSha2($mdz, $deArray) ? 1 : 0;
            $daShi2 = $this->checkDaShi2($mdz, $deArray) ? 1 : 0;
            $tianLi2 = $this->checkTianLi2($mdz, $deArray) ? 1 : 0;
            $wuMu2 = $this->checkWuMu2($mdz, $deArray) ? 1 : 0;
            $yueXing2 = $this->checkYueXin2($mdz, ($yueDe || $tianDeHe || $tianYuan)) ? 1 : 0;
            $yueSha2 = $this->checkYueSha2($mdz, $deArray) ? 1 : 0;
            // 0 普通  1 可变
            $change = [0 => [], 1 => []];
            if ($this->checkJieSha($mdz, $ddz)) {
                $change[$jieSha2][] = '劫煞';
            }
            if ($this->checkZaiSha($mdz, $ddz)) {
                $change[$zaiSha2][] = '灾煞';
            }
            // 大时 大败 咸池
            if ($this->checkDaShi($mdz, $ddz)) {
                $change[$daShi2][] = '大时';
            }
            // 致死（天吏）
            if ($this->checkTianLi($mdz, $ddz)) {
                $change[$tianLi2][] = '天吏';
            }
            //            if ($this->checkWuMu($mdz, $dgz)) {
            //                $change[$wuMu2][] = '五墓';
            //            }
            //            if ($this->checkYueXin($mdz, $ddz)) {
            //                $change[$yueXing2][] = '月刑';
            //            }
            if ($this->checkYueSha($mdz, $ddz)) {
                $change[$yueSha2][] = '月煞';
            }
            $xiongRes = array_merge($xiongRes, $change[0]);
            $reason = array_merge($reason, $xiongRes);
            $bool = $xingChongArr || $reason || $reasonJr;
            $sanSha = Huangli::getSanSha($ddz);
            $chongDz = BaziCommon::getChongDz($ddz);
            $chongSx = $this->lunar1->getZodiac($chongDz);
            // $a[$cGongli] = implode(',', $reason);
            $gongsha = $this->huiTouGongShaLiu($jiNian);
            // 去掉回头贡杀
            if (in_array($gongsha, $ydzU)) {
                $bool = true;
            }
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $curTime),
                    'm' => (int)date('m', $curTime),
                    'd' => (int)date('d', $curTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']),
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                // 0 不合 1 符合
                'type' => $bool ? 0 : 1,
                // 'type_cn' => $bool ? '不宜' : '平',
                // 黄道 1 黑道 0
                'type_hl' => $hdType,
                'position' => $huangli->getPosition(),
                // 建除
                'jianchu' => $huangli->getJianChu(),
                // 值日神煞
                'shensha' => $shenSha,
                // 煞向
                'sha_xian' => str_replace('煞', '', $sanSha[0]),
                // 日冲
                'chong_sx' => $chongSx,
                // 与用户相冲或相刑结果
                'chong_x_u' => $xingChongArr,
                // 原因
                'reason' => $reason,
                'change' => [],
                'gongsha' => $gongsha,
                // 农历节日过滤
                'fest' => $reasonJr,
            ];
            $jiShenRes = $deArray;
            $dayHour = [];
            if (!$bool) {
                $type = 1;
                $tmpRes['change'] = $change[1];
                if ($hdType || $change[1]) {
                    $type = 2;
                }
                if ($this->checkYueEn($mdz . $dtg)) {
                    $jiShenRes[] = '月恩';
                }
                if ($tianYuan) {
                    $jiShenRes[] = '天愿';
                }
                if ($this->checkTianSe($mdz, $dtg)) {
                    $jiShenRes[] = '天赦';
                }
                if ($jiShenRes) {
                    $type = $hdType ? 4 : 3;
                }
                $reasonZhong = [];
                // 驿马 四相 民日 天马 六合 三合 岁德 岁德合 王日 官日 守日 相日 民日 时德 驿马 人专 贡煞 母仓
                if ($this->checkYiMa($mdz, $ddz)) {
                    $reasonZhong[] = '驿马';
                }
                if ($this->checkSiXian($mdz, $dtg)) {
                    $reasonZhong[] = '四相';
                }
                if ($this->checkMingRi($mdz, $ddz)) {
                    $reasonZhong[] = '民日';
                }
                if ($this->checkTianMa($mdz, $ddz)) {
                    $reasonZhong[] = '天马';
                }
                if (BaziCommon::liuHeDz($mdz . $ddz)) {
                    $reasonZhong[] = '六合';
                }
                $sanHeDz = [
                    [$ydz, $mdz, $ddz],
                ];
                $sanHe = BaziCommon::getSanHeDz2($sanHeDz);
                if (!empty($sanHe[3])) {
                    $reasonZhong[] = '三合';
                }
                if ($this->checkSuiDe($ytg . $dtg)) {
                    $reasonZhong[] = '岁德';
                }
                if ($this->checkSuiDeHe($ytg . $dtg)) {
                    $reasonZhong[] = '岁德合';
                }
                if ($this->checkWangRi($mdz, $ddz)) {
                    $reasonZhong[] = '王日';
                }
                if ($this->checkGuanRi($mdz, $ddz)) {
                    $reasonZhong[] = '官日';
                }
                if ($this->checkShouRi1($mdz, $ddz)) {
                    $reasonZhong[] = '守日';
                }
                if ($this->checkXiangRi($mdz, $ddz)) {
                    $reasonZhong[] = '相日';
                }
                if ($this->checkShiDe($dzMd)) {
                    $jiShenRes[] = '时德';
                }
                $nongliMNum = $base['_nongli']['m'];
                if ($this->checkRenZhuan($nongliMNum, $dgz)) {
                    $jiShenRes[] = '人专';
                }
                if ($this->checkShaGong($nongliMNum, $dgz)) {
                    $jiShenRes[] = '贡煞';
                }
                if ($this->checkMuCang($mdz, $ddz)) {
                    $jiShenRes[] = '母仓';
                }
                if ($reasonZhong) {
                    $jiShenRes = array_merge($jiShenRes, $reasonZhong);
                    if ($type != 4) {
                        $type = 3;
                    }
                }
                $dayHour = $listHour[$ddz];
                if ($shenSha == '司命') {
                    foreach ($noHourSiMing as $dzNo) {
                        unset($dayHour[$dzNo]);
                    }
                }
                $tmpRes['type'] = $type;
                $tmpRes['reason'] = $jiShenRes;
            }
            $tmpRes['shen'] = [
                'jishen' => array_values(array_unique(array_merge($jiShenRes, $jiShenHl))),
                'xiong' => array_values(array_unique(array_merge($xiongRes, $xiongHl))),
            ];
            $tmpRes['hour'] = $dayHour;
            $num = $result[$keyStr]['num'] ?? 0;
            if (!$bool) {
                $num++;
            }
            $result[$keyStr]['num'] = $num;
            $explain[$shenSha] = $this->getExplain($shenSha);
            foreach ($tmpRes['fest'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['reason'] as $v) {
                $explain[$v] = $this->getExplain($v);
            }
            foreach ($tmpRes['change'] as $v) {
                $changeExplain[$v] = $this->getExplain($v, true);
            }
            $result[$keyStr]['info'][] = $tmpRes;
        }
        return [
            'list' => array_values($result),
            'explain' => $explain,
            'change' => $changeExplain,
            'type_cn' => [
                0 => '不宜', 1 => '平', 2 => '小吉', 3 => '中吉', 4 => '大吉',
            ],
        ];
    }

    /**
     * 过滤黄历算法中的指定吉凶神
     * @param array $arr
     * @param int $type 0吉神 1凶神
     * @return array
     */
    protected function filterJXShen(array $arr, int $type = 0): array
    {
        // 过滤掉黄历的吉凶神
        if ($type) {
            // 凶
            $listNo = [
                '四离', '四绝', '四废', '归忌', '往亡', '月破', '天贼', '劫煞', '灾煞', '大时', '天吏', '月刑', '月煞',
            ];
        } else {
            // 吉
            $listNo = [
                '驿马', '四相', '时德', '民日', '天马', '民日', '驿马', '六合', '人专', '贡煞', '三合', '岁德', '岁德合',
                '王日', '官日', '守日', '相日', '母仓', '天德', '月德', '天德合', '天愿', '月恩', '月德合', '天赦',
            ];
        }
        if ($listNo) {
            foreach ($arr as $k => $v) {
                if (in_array($v, $listNo)) {
                    unset($arr[$k]);
                }
            }
        }
        return array_values($arr);
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $sxBase = new SxBase();
                $sx = $sxBase->getsxByDz($k);
                $str = $k . $sx;
                break;
            }
        }
        return $str;
    }

    /**
     * 获得年份内所有节气
     * @param $year
     * @return array
     */
    private function getJieqiByYear($year): array
    {
        $arr = $this->jieQiYear;
        if (isset($arr[$year])) {
            return $arr[$year];
        }
        $jArr = SolarTerm::getAllJieQi($year);
        $jieqiArr = [];
        foreach ($jArr as $k => $v) {
            $jTmp = date('Y-m-d', strtotime($v));
            $jieqiArr[$jTmp] = $k;
        }
        $this->jieQiYear[$year] = $jieqiArr;
        return $jieqiArr;
    }

    /**
     * 获得用户能用到的所有时辰
     * @return array
     */
    protected function getHourUse(): array
    {
        $ygz = $this->lunar1->getLunarGanzhiYear();
        $ydzU = $ygz[1];
        $list = [
            '子' => ['丑', '辰', '申', '丑', '亥'], '丑' => ['子', '巳', '酉', '子', '亥'], '寅' => ['亥', '午', '戌', '卯', '辰'], '卯' => ['戌', '亥', '未', '辰', '寅'],
            '辰' => ['酉', '子', '申', '寅', '卯'], '巳' => ['申', '酉', '丑', '午', '未'], '午' => ['未', '寅', '戌', '巳', '未'], '未' => ['午', '亥', '卯', '巳', '午'],
            '申' => ['巳', '辰', '子', '酉', '戌'], '酉' => ['辰', '巳', '丑', '戌', '申'], '戌' => ['卯', '午', '寅', '申', '酉'], '亥' => ['寅', '卯', '未', '子', '丑'],
        ];
        $listU = $list[$ydzU];
        if ($this->lunar2) {
            $ygz2 = $this->lunar2->getLunarGanzhiYear();
            $ydzU = $ygz2[1];
            $listU = array_unique(array_merge($listU, $list[$ydzU]));
        }
        $result = [];
        $no = ['子', '丑', '寅'];
        $listHourInfo = $this->getHourInfo();
        foreach ($list as $k => $v) {
            $tmpArr = [];
            $useList = array_unique(array_merge($listU, $v));
            $useList = Utils::sortbyGz($useList);
            foreach ($useList as $k1 => $dz) {
                // 去除夜间 跟用户年支日支相刑的时辰
                if (in_array($dz, $no) || in_array($dz, $this->chongXingDz['chong']) || in_array($dz, $this->chongXingDz['xing'])) {
                    continue;
                }
                if (BaziCommon::getXianChong($k . $dz) || BaziCommon::getXianXin($k . $dz)) {
                    continue;
                }
                $tmpArr[$dz] = $listHourInfo[$dz];
            }
            $result[$k] = $tmpArr;
        }
        return $result;
    }

    /**
     * 获得时辰详细信息
     * @return array
     */
    protected function getHourInfo(): array
    {
        $list = Calendar::DI_ZHI;
        // 时辰对应的时区间
        $listHour = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $sxBase = new SxBase();
        $result = [];
        foreach ($list as $k => $dz) {
            $chongDz = BaziCommon::getChongDz($dz);
            $chongSx = $sxBase->getsxByDz($chongDz);
            $result[$dz] = [
                'dz' => $dz,
                't' => $listHour[$dz],
                'chong' => $chongSx,
            ];
        }
        return $result;
    }

    /**
     * 解释
     * @param string $str
     * @param bool $isChange
     * @return string
     */
    protected function getExplain(string $str, bool $isChange = false): string
    {
        if ($isChange) {
            $list = [
                '劫煞' => '劫煞是一年的阴气，主管杀伐和伤害，寓意不吉。当值之日忌讳成亲、行军、出行、搬家、入宅。但当它与天德，月德，天德合，月德合和三合其中之一并临时，只忌安抚边境、选将训兵、出师、求医疗病，余者都不忌讳。',
                '灾煞' => '灾煞主管灾难、疾病、困厄之事。当值之日禁忌入宅、兴造、出行。但它遇到天、月德或天、月德合时，则不忌入宅。',
                '大时' => '大败又称大时，属于绝败之日。因为五行到此无生机，是属于较凶的日子。所值当日禁忌入宅、筑室、会亲。但是当它遇到天、月德、天、月德合、官日、六合之一时，则不忌入宅。',
                '天吏' => '致死又称天吏，指的是天上的凶吏，全没有生机的意思。当值之日禁忌赴任、远行、入宅。当它遇到天、月德或天、月德合大吉神时，凶性被化，不忌入宅。',
                '月刑' => '月刑所在之处，应忌讳入宅、动土、兴工，但当它遇到月德或天德合大吉神时，凶性被化，不忌入宅。',
                '月煞' => '月煞又叫月杀，是指月内的杀神。它当值之日禁忌宴请、入宅、营造等。但是当它在（卯月、酉月）遇到（月德、天愿）时，则不忌入宅。',
                '四离' => '离是阴阳分、至节气之前一辰。该日忌讳出行、搬家、远行、入宅、出征，但遇到有所适宜的吉神时则不忌讳。',
                '四绝' => '四绝就是四立前一辰。当日忌讳搬家、远行、入宅，但遇到有所适宜的吉神时则不忌讳。',
            ];
        } else {
            $list = [
                '天德' => '天德是指天的福德，占得天时，有三合旺气，是上等的吉日，寓意万事大吉，宜用于入宅。',
                '月德' => '月德是月的德神，遇凶可以逢凶化吉，所值之日宜入宅，宴乐，上官赴任。',
                '天德合' => '天德合是合德之神，有贵人相助，它所值之日适宜入宅。',
                '月德合' => '月德合是与月德相合的天干，所值之日百福并集，诸事顺利，有利于入宅。',
                '天恩' => '天恩是施仁德的吉日，它所值之日有利于入宅。',
                '月恩' => '月恩的产生是地干生天支，犹如母生子，因此其所值之日宜营造、婚姻、入宅、祭祀、上官、赴任、纳财。',
                '天赦' => '天赦是上天赦免、饶恕罪过的神，它当值之日适宜入宅。',
                '母仓' => '母仓是五行当王所生成的，它所理值之日宜养育群畜，栽植种莳，移徙入宅。',
                '天愿' => '天愿是月中的善神，它所理值之日适宜嫁娶、纳财、入宅。',
                '月财' => '此黄帝招财之地，若起造、出行、入宅，令人得财。',
                '驿马' => '驿马又称驿骑，它表示当日适宜诏命公卿，远行赴任，移徙入宅。',
                '四相' => '四相是指四季王相的时日。四相日适宜入宅、生财、栽植、移徙、远行。',
                '时德' => '四时天德，是四季中的德神，所值之日宜入宅、庆赐、宴乐、拜官、赏贺。',
                '天马' => '天马是天的坐骑，当值之日适宜入宅，宣布政事，远行出征。',
                '岁德' => '所谓岁德就是岁中的德神，它当值之日宜入宅。',
                '岁德合' => '岁德合是岁德五合的天干，与岁德都属于吉日，有宜没有忌，所值之日宜入宅。',
                '王日' => '王日是四季中正王的时日，是四正的方位，有帝王之气象，它所值之日宜入宅。',
                '官日' => '官日是四季中临官的时日，有诸侯之象，它所值之日宜入宅。',
                '守日' => '守日是四季胎、绝的时日，无气能够自守，守而等待将来，它所值之日宜入宅。',
                '相日' => '相日是四季中官日相生的，是相气的时日，有宰相之象，它所值之日宜入宅。',
                '民日' => '民日是百姓所用的时日，寓意办事顺利，它所值之日宜入宅。',
                '六合' => '六合是指月建与月将相合，所值之日宜入宅、会宾客、结婚。',
                '三合' => '三合是指不同位而同气，适宜入宅、修营起土，结姻亲。',
                '人专' => '人专是指六阴金堂符入宅，所值之日宜嫁娶、开张、移徙、上官、入宅。',
                '贡煞' => '贡煞是指青龙天德符入宅，所值之日宜嫁娶、入宅、移徙、上官、赴任。',
                '黄道' => '黄道是指月内的天黄道之日当值时，适宜兴动各种事务，包括入宅。',
                '劫煞' => '劫煞是劫害的时日，寓意不吉，当值之日忌讳纳礼成亲、临官视事、移徙入宅。',
                '灾煞' => '灾煞主管灾难、疾病、困厄之事。当值之日禁忌入宅、出行、营造。',
                '大时' => '大败（大时）是月建、三合、五行沐浴的时日，因为五行到这里就败绝，是最凶时辰，所以也叫做大凶之时。当日禁忌入宅、筑室、会亲。',
                '天吏' => '致死（天吏）指的是天上的凶吏，全没有生机的意思。当值之日禁忌赴任、远行、入宅。',
                '月刑' => '月刑所在之处，应忌讳入宅、动土、兴工。',
                '月煞' => '月煞又叫月杀，是指月内的杀神。它当值之日禁忌宴请、入宅、营造等。',
                '四离' => '离是阴阳分、至节气之前一辰。该日忌讳出行、搬家、远行、入宅、出征。',
                '四绝' => '四绝就是四立前一辰，寓意办事不吉，当日忌讳搬家、远行、入宅。',
                '四废' => '四废是四季衰败凋谢的时日。五行之气到此没有生机，福德亦受阻，该日大事勿用。',
                '归忌' => '归忌是月内的凶神。当值之日忌讳归家、娶妇、入宅。',
                '往亡' => '往就是去，亡就是没有。往亡就是往而不返的意思。该日忌讳拜官上任、远行归家、移徙入宅。',
                '天贼' => '天贼，是月中的盗神，主盗窃和丢失。其当值之日禁忌远行，入新宅。',
                '月破' => '月破日有破败之意，日月相冲，是为大耗，斗柄相冲相向必破坏的日子，因此大事不宜。',
                '岁破' => '岁破是太岁的所冲之辰，主破坏和不顺。它所值之日不能兴动、入宅、嫁娶。',
                '杨公忌日' => '杨公忌日又叫杨公十三忌，在杨公忌日里做事情都较为不顺，容易招致灾祸，办事理应避开此日。',
                '受死' => '受死日是没有办法制化的，所以它是大忌，办事时需要避开该日。',
                '十恶大败' => '十恶意思是不赦重罪，大败意思是表示消减，是一种极凶的征兆。它所当值之日大事勿用。',
                '清明节' => '该日乃是用来扫坟祭祖的，怀缅先辈的日子，不适宜入宅。',
                '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，寓意不吉。',
                '重阳节' => '当日有大事勿用的说法，忌讳入宅。',
                '寒衣节' => '寒衣节乃是传统的四大鬼节之一，寓意不吉。',
                '天刑' => '天刑黑道，天刑星，该日只利于出师，其他事项皆不宜用。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '白虎' => '白虎黑道，天杀星，只宜出师畋猎祭祀，其余的事项都不利。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '朱雀' => '朱雀黑道，天讼星，利用公事，常人凶，诸事忌用。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '天牢' => '天牢黑道，镇神星，阴人用事皆吉，其余都不利。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '玄武' => '玄武黑道，天狱星，君子用之吉，小人用之凶，忌词讼博戏。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '勾陈' => '勾陈黑道，地狱星，此时所作一切事，有始无终，先喜后悲，不利攸往。如果当日有适宜入宅的吉神时，则可以进行入宅。',
                '青龙' => '青龙黄道，天乙和天贵星。大吉之星，所做必能成，万事皆顺意。',
                '明堂' => '明堂黄道，贵人星，明辅星，传统习俗中的吉利日子，寓意贵人相助，事情必定成功。',
                '金匮' => '金匮黄道，福德星，月仙星，传统习俗中的吉利日子，寓意行事一帆风顺。',
                '宝光' => '天德黄道，宝光星，天德星，传统习俗中的吉利日子，作事有成，利于办事。',
                '玉堂' => '玉堂黄道，少微星，天开星，百事吉，求事成，出行有财。传统习俗中的吉利日子，做事情都会比较顺利。',
                '司命' => '司命黄道，凤辇星，月仙星，当天用事大吉，寓意做事都能成功。但忌讳夜间宴请宾客。',
            ];
        }
        return $list[$str] ?? '';
    }
}
