<?php
// +----------------------------------------------------------------------
// | Lingzhengjiri.领证吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Lingzhengjiri
{
    use JiRiCheckTraits;

    /**
     * 男日历相关
     * @var Ex
     */
    protected Ex $mlunar;

    /**
     * 女日历相关
     * @var Ex
     */
    protected Ex $flunar;

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @return array
     * @throws Exception
     * @throws \think\Exception
     */
    public function index()
    {
        $data = [
            'mtime' => input('mtime', '', 'trim'),
            'ftime' => input('ftime', '', 'trim'),
            'otime' => input('otime', '', 'trim'),
            'limit' => input('limit', 0, 'intval'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'mtime|男方出生时间' => ['require', 'isDateOrTime:男方出生时间'],
                'ftime|女方出生时间' => ['require', 'isDateOrTime:女方出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'limit|查看天数' => ['require', 'between:1,720'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->mlunar = Ex::date($data['mtime'])->sex(0);
        $this->flunar = Ex::date($data['ftime'])->sex(1);
        $xiYong = BaziExt::getxiYongJi($this->mlunar);
        $xiYong2 = BaziExt::getxiYongJi($this->flunar);
        $jiRes = $this->getJiri();
        return [
            'm' => [
                'base' => $this->mlunar->getLunarByBetween(),
                'god' => $this->mlunar->getGod(),
                '_god' => $this->mlunar->_getGod(),
                'na_yin' => $this->mlunar->getNayin(),
                'like_god' => $data['source'] == 'wnl' ? $this->mlunar->getLikeGod2() : $this->mlunar->getLikeGod(),
                // 用神五行
                'yongshen' => $xiYong['wx'][1],
            ],
            'f' => [
                'base' => $this->flunar->getLunarByBetween(),
                'god' => $this->flunar->getGod(),
                '_god' => $this->flunar->_getGod(),
                'na_yin' => $this->flunar->getNayin(),
                'like_god' => $data['source'] == 'wnl' ? $this->flunar->getLikeGod2() : $this->flunar->getLikeGod(),
                // 用神五行
                'yongshen' => $xiYong2['wx'][1],
            ],
            'ji' => $jiRes['list'],
            'explain' => $jiRes['explain'],
        ];
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = $this->orginData['otime'];
        $limit = $this->orginData['limit'];
        // 破日 月份+破日
        $listPo = ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'];
        $listWangWang = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        // 归忌
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        // 受死 农历月份+日支
        $listShouSi = ['1|戌', '2|辰', '3|亥', '4|巳', '5|子', '6|午', '7|丑', '8|未', '9|寅', '10|申', '11|卯', '12|酉'];
        // 小红沙日 农历月份+日支
        $listHongSha = ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'];
        // 杨公忌日 农历月份+日
        $listYangGong = ['1_13', '2_11', '3_9', '4_7', '5_5', '6_3', '7_1', '7_29', '8_27', '9_25', '10_23', '11_21', '12_19'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        // 相冲日 相刑日 年支+流日支
        $listChongXin = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
            '子卯', '丑未', '寅巳', '卯子', '辰辰', '巳申', '午午', '未戌', '申寅', '酉酉', '戌丑', '亥亥',
        ];
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳',
            '10_10' => '寒衣',
        ];
        $jiNianM = $this->mlunar->getLunarTganDzhi();
        $jiNianF = $this->flunar->getLunarTganDzhi();
        $yearGz = $jiNianM['y'];
        $yearGz1 = $jiNianF['y'];
        $userDz = [
            $jiNianM['y'][1], $jiNianM['d'][1], $jiNianF['y'][1], $jiNianF['d'][1],
        ];
        $result = [];
        $explain = [
            'jc' => [],
            'shen' => [],
            'sha' => [],
        ];
        for ($i = 1; $i <= $limit; $i++) {
            $time = strtotime("{$startTime} +{$i} day");
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $zhiRi = $huangli->getZhiRi();
            $shaShen = $zhiRi['shen_sha'];
            $noList = [];
            $bool = true;
            if ($zhiRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            $base = $huangli->getLunarByBetween();
            $jiNian = $base['jinian'];
            $mzDz = $jiNian['m'][1] . $jiNian['d'][1];
            // 干支
            $tmpGZ = [
                implode('', $jiNian['y']), implode('', $jiNian['m']), implode('', $jiNian['d']),
            ];
            $isJie = 0;
            $jianChu = $huangli->getJianChu();
            if ($this->checkJiaRi($time)) {
                $noList[] = '节假日';
                $isJie = 1;
                $bool = false;
            } else {
                $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
                // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
                if (isset($listNo[$nongliNumberStr])) {
                    $noList[] = $listNo[$nongliNumberStr];
                    $bool = false;
                }
                // 彭祖忌 亥日例：所有地支为亥的日子都去掉
                if ($jiNian['d'][1] === '亥') {
                    $noList[] = '彭祖百忌';
                    $bool = false;
                }
                // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉
                if ($base['_nongli']['d'] == 1) {
                    $noList[] = '朔日';
                    $bool = false;
                }
                // 四立两分两至（反目日） 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至
                $jieQi = Huangli::isJieQi($time);
                if ($jieQi && in_array($jieQi, $list2)) {
                    $noList[] = $jieQi;
                    $bool = false;
                }
                // 清明、
                if ($jieQi === '清明') {
                    $noList[] = '清明';
                    $bool = false;
                }
                // 去除破日+往亡+归忌
                if (in_array($mzDz, $listPo)) {
                    $noList[] = '岁破日';
                    $bool = false;
                }
                if (in_array($mzDz, $listWangWang)) {
                    $noList[] = '往亡日';
                    $bool = false;
                }
                if (in_array($mzDz, $listGuiJi)) {
                    $noList[] = '归忌日';
                    $bool = false;
                }
                // 受死+小红沙日
                $strShouSi = $base['_nongli']['m'] . '|' . $jiNian['d'][1];
                if (in_array($strShouSi, $listShouSi)) {
                    $noList[] = '受死日';
                    $bool = false;
                }
                if (in_array($strShouSi, $listHongSha)) {
                    $noList[] = '小红沙日';
                    $bool = false;
                }
                // 杨公忌日
                if (in_array($nongliNumberStr, $listYangGong)) {
                    $noList[] = '杨公忌日';
                    $bool = false;
                }
                // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天
                $tomorrowJieQi = Huangli::isJieQi($time + 86400);
                if ($tomorrowJieQi && in_array($tomorrowJieQi, $list2)) {
                    if (in_array($tomorrowJieQi, ['立春', '立夏', '立秋', '立冬'])) {
                        // 四绝
                        $noList[] = '四绝日';
                    } else {
                        $noList[] = '四离日';
                    }
                    $bool = false;
                }
                // 正四废日
                if ($this->checkZhengSiFeiRi($jiNian['m'][1], $tmpGZ[2])) {
                    $noList[] = '四废日';
                    $bool = false;
                }
                // 当日正冲年份正好是新人出生年干支则去除该日
                if ($this->checkZhengChong($tmpGZ[2], implode('', $yearGz)) || $this->checkZhengChong($tmpGZ[2], implode('', $yearGz1))) {
                    $noList[] = '正冲';
                    $bool = false;
                }
                // 根据男女双方生年分别去掉相冲、相刑日。 年支+流日支
                foreach ($userDz as $v1) {
                    if (BaziCommon::getXianChong($v1 . $jiNian['d'][1])) {
                        $bool = false;
                        break;
                    }
                }
                foreach ($userDz as $v1) {
                    if (BaziCommon::getXianXin($jiNian['d'][1] . $v1)) {
                        $bool = false;
                        break;
                    }
                }
            }
            // 建除为'危', '破', '闭', '执'的日子
            $jcType = 1;
            if (in_array($jianChu, ['危', '破', '闭', '执'])) {
                $jcType = 0;
            }
            $rdz = $jiNian['d'][1];
            $xiJongYiJi = $huangli->getJiXiong();
            $jiNianTmp = $jiNian;
            unset($jiNianTmp['h']);
            $rgz = implode('', $jiNian['d']);

            $sanSha = Huangli::getSanSha($jiNian['d'][1]);
            $tmpRes = [
                'gongli' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'week' => $week,
                'zhoumo' => (in_array($week, ['星期六', '星期日']) ? 1 : 0),
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jiNian' => $jiNianTmp,
                // 正冲
                'zheng_chong' => BaziCommon::getChongBygz($rgz),
                // 煞向
                'sha_xian' => str_replace('煞', '', $sanSha[0]),
                // 宜忌
                'type' => 'buyi',
                // 黄道 1 黑道 0
                'hl_type' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                'jishen' => $xiJongYiJi['jishen'],
                'xiong' => $xiJongYiJi['xiong'],
                'hour' => [],
                // 建除
                'jianchu' => $isJie ? '' : $jianChu,
                // 神煞
                'shensha' => $isJie ? '' : $shaShen,
                // 分析
                'fenxi' => [],

            ];
            $keyRes = $base['_nongli']['y'] . '年' . $base['_nongli']['m'] . '月(农历)';
            $jiNum = $result[$keyRes]['num'] ?? 0;
            if ($bool) {
                $jiNum++;
                $hourArr = $this->getJiRiHour($jiNianTmp['d'][0], $jiNianTmp['d'][1]);
                if ($shaShen === '司命') {
                    foreach ($hourArr as $kh => $vh) {
                        if (!in_array($vh['dz'], ['寅', '卯', '辰', '巳', '午', '未', '申'])) {
                            unset($hourArr[$kh]);
                            continue;
                        }
                    }
                    $hourArr = array_values($hourArr);
                }
                $tmpRes['hour'] = $hourArr;
                $detail = [];
                if ($this->checkBuJian($base['_nongli']['m'], $tmpGZ[2])) {
                    $detail[] = '不将';
                }
                if ($this->checkJiFen($base['_nongli']['m'], $tmpGZ[2])) {
                    $detail[] = '季分';
                }
                if ($this->checkSuiDe($jiNian['y'][0] . $jiNian['d'][0])) {
                    $detail[] = '岁德';
                }
                if ($this->checkSuiDeHe($jiNian['y'][0] . $jiNian['d'][0])) {
                    $detail[] = '岁德合';
                }
                $strmzrz = $jiNian['m'][1] . $jiNian['d'][1];
                if ($this->checkHongLuanTianXi($strmzrz)) {
                    $detail[] = '红鸾天喜';
                }
                if ($this->getSanHeByDz($strmzrz)) {
                    $detail[] = '三合';
                }
                // 月支+日干
                $strmzrg = $jiNian['m'][1] . $jiNian['d'][0];
                if ($this->checkTianDe($strmzrg) || $this->checkTianDe($strmzrz)) {
                    $detail[] = '天德';
                }
                if ($this->checkTianDeHe($strmzrg) || $this->checkTianDeHe($strmzrz)) {
                    $detail[] = '天德合';
                }
                if ($this->checkYueDeHe($strmzrg) || $this->checkYueDeHe($strmzrz)) {
                    $detail[] = '月德合';
                }
                if ($this->checkTianSe($jiNian['m'][1], $tmpGZ[2])) {
                    $detail[] = '天赦';
                }
                if ($this->checkTianYuan($jiNian['m'][1] . $tmpGZ[2])) {
                    $detail[] = '天愿';
                }
                if ($this->checkYueEn($strmzrg)) {
                    $detail[] = '月恩';
                }
                if ($this->checkSiXian($jiNian['m'][1], $jiNian['d'][1])) {
                    $detail[] = '四相';
                }
                if ($this->checkShiDe($strmzrz)) {
                    $detail[] = '时德';
                }
                if ($this->checkXianXing($base['_nongli']['m'], $tmpGZ[2])) {
                    $detail[] = '显星';
                }
                if ($this->checkQuXing($base['_nongli']['m'], $tmpGZ[2])) {
                    $detail[] = '曲星';
                }
                if ($this->checkChuanXing($base['_nongli']['m'], $tmpGZ[2])) {
                    $detail[] = '传星';
                }
                $jishen = $xiJongYiJi['jishen'];
                $xiong = $xiJongYiJi['xiong'];
                if (array_intersect($detail, ['月德', '天德合', '月德合', '显星', '曲星', '传星']) || $shaShen == '天德') {
                    $tmpRes['type'] = 'da';
                } elseif (count($jishen) > count($xiong)) {
                    $tmpRes['type'] = 'xiao';
                } else {
                    $tmpRes['type'] = 'ping';
                }
                $jiShenArr = $xiJongYiJi['jishen'];
                $jiShenArr = array_merge($jiShenArr, $detail);
                $tmpRes['jishen'] = $jiShenArr;
                $tmpRes['reason'] = $detail;
                if (!$jcType) {
                    $tmpRes['jianchu'] = '';
                }
            } else {
                if ($tmpRes['hl_type'] == 1) {
                    $tmpRes['shensha'] = '';
                }
                $tmpRes['fenxi'] = $this->getLiuGx($jiNianTmp);
                $tmpRes['reason'] = $noList;
                if ($jcType) {
                    $tmpRes['jianchu'] = '';
                }
            }
            foreach ($tmpRes['reason'] as $k1 => $v1) {
                if (isset($explain['shen'][$v1])) {
                    continue;
                }
                $tmpEXp = $this->getExplain($v1);;
                if (empty($tmpEXp)) {
                    unset($tmpRes['reason'][$k1]);
                }
                $explain['shen'][$v1] = $tmpEXp;
            }
            $tmpRes['reason'] = array_values($tmpRes['reason']);
            if (!empty($tmpRes['jianchu']) && !isset($explain['jc'][$jianChu])) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if (!empty($tmpRes['shensha']) && !isset($explain['sha'][$shaShen])) {
                $explain['sha'][$shaShen] = $this->getShaRes($shaShen);
            }
            $result[$keyRes]['title'] = $keyRes;
            $result[$keyRes]['num'] = $jiNum;
            $result[$keyRes]['info'][] = $tmpRes;
        }
        return [
            'explain' => $explain,
            'list' => array_values($result),
        ];
    }

    /**
     * 正四废日
     * @param string $monthDz 月地支
     * @param string $daygz 日干支
     * @return bool
     */
    private function checkZhengSiFeiRi(string $monthDz, string $daygz): bool
    {
        switch ($monthDz) {
            case '寅':
            case '卯':
            case '辰':
                $bool = in_array($daygz, ['庚申', '辛酉']);
                break;
            case '巳':
            case '午':
            case '未':
                $bool = in_array($daygz, ['壬子', '癸亥']);
                break;
            case '申':
            case '酉':
            case '戌':
                $bool = in_array($daygz, ['甲寅', '乙卯']);
                break;
            default:
                $bool = in_array($daygz, ['丙午', '丁巳']);
                break;
        }
        return $bool;
    }

    /**
     * 当日正冲年份正好是新人出生年干支则去除该日
     * @param string $liuRi 日干支
     * @param string $yeargz 年干支
     * @return bool
     */
    protected function checkZhengChong(string $liuRi, string $yeargz): bool
    {
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        $str = $list[$liuRi] ?? '';
        $bool = false;
        if ($str === $yeargz) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 流日和用户关系
     * @param array $jiNian
     * @return array
     */
    protected function getLiuGx($jiNian): array
    {
        $jiNianM = $this->mlunar->getLunarTganDzhi();
        $jiNianF = $this->flunar->getLunarTganDzhi();
        $ydzM = $jiNianM['y'][1];
        $ddzM = $jiNianM['d'][1];
        $ydzF = $jiNianF['y'][1];
        $ddzF = $jiNianF['d'][1];
        $ddz = $jiNian['d'][1];
        $list = [
            '男' => [
                's' => [], 'y' => [], 't' => [],
            ],
            '女' => [
                's' => [], 'y' => [], 't' => [],
            ],
        ];
        $listChong = [
            ['男', '年', $ydzM . $ddz],
            ['男', '日', $ddzM . $ddz],
            ['女', '年', $ydzF . $ddz],
            ['女', '日', $ddzF . $ddz],
        ];
        foreach ($listChong as $k => $v) {
            $tKey = $v[0];
            if (BaziCommon::getXianChong($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '冲';
            }
            if (BaziCommon::getXianXin($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '刑';
            }
        }
        $result = [];
        foreach ($list as $k => $v) {
            if (empty($v['t'])) {
                continue;
            }
            $result[] = [
                't' => implode('', array_unique($v['t'])) . $k,
                'y' => array_unique($v['y']),
            ];
        }
        return $result;
    }

    /**
     * 每日吉时
     * @param string $tg 日干
     * @param string $dz 日支
     * @return array
     */
    protected function getJiRiHour(string $tg, string $dz): array
    {
        $list = [
            '辰' => [
                'xc' => ['辰', '戌'],
                'd' => ['酉', '子', '申', '寅', '卯'],
            ],
            '巳' => [
                'xc' => ['申', '亥'],
                'd' => ['申', '酉', '丑', '午', '未'],
            ],
            '未' => [
                'xc' => ['丑'],
                'd' => ['午', '亥', '卯', '巳', '午'],
            ],
            '申' => [
                'xc' => ['寅'],
                'd' => ['巳', '辰', '子', '酉', '戌'],
            ],
            '酉' => [
                'xc' => ['酉', '卯'],
                'd' => ['辰', '巳', '丑', '戌', '申'],
            ],
            '午' => [
                'xc' => ['午', '子'],
                'd' => ['未', '寅', '戌', '巳', '未'],
            ],
        ];
        $mYgz = $this->mlunar->getLunarGanzhiYear();
        $fYgz = $this->flunar->getLunarGanzhiYear();
        $mydz = $mYgz[1];
        $fydz = $fYgz[1];
        $result = [];
        foreach ($list as $k => $v) {
            if (!in_array($dz, $v['d'])) {
                continue;
            }
            if (in_array($mydz, $v['xc']) || in_array($fydz, $v['xc'])) {
                continue;
            }
            if (BaziCommon::getXianChong($k . $mydz) || BaziCommon::getXianChong($k . $fydz)) {
                continue;
            }
            $result[] = $this->getHourDetail($tg, $k);
        }
        if (!empty($result)) {
            return $result;
        }
        foreach ($list as $k => $v) {
            if (BaziCommon::getXianChong($k . $mydz) || BaziCommon::getXianChong($k . $fydz)) {
                continue;
            }
            if (in_array($mydz, $v['d']) || in_array($mydz, $v['d'])) {
                $result[] = $this->getHourDetail($tg, $k);
            }
        }
        return $result;
    }

    /**
     * 获得时详情
     * @param string $dtg 日天干
     * @param string $dz 时支
     * @return array
     */
    protected function getHourDetail(string $dtg, string $dz): array
    {
        $listShi = [
            '午' => '中午11点至13点', '未' => '下午13点至15点', '申' => '下午15点至17点', '酉' => '下午17点至19点',
            '戌' => '晚上19点至21点', '亥' => '晚上21点至23点', '子' => '晚上23点至1点', '丑' => '凌晨1点至3点',
            '寅' => '凌晨3点至5点', '卯' => '早上5点至7点', '辰' => '早上7点至9点', '巳' => '早上9点至11点',
        ];
        $gz = Huangli::getGanzhiHour($dtg, $dz);
        $gzStr = implode('', $gz);
        $zc = BaziCommon::getChongBygz($gzStr);
        $sxBase = new SxBase();
        $sx = $sxBase->getsxByDz($dz);
        return [
            'dz' => $dz,
            'tg' => $gz[0],
            'h' => $listShi[$dz],
            'chong' => $sxBase->getChong($sx),
            'zc' => $zc,
        ];
    }

    /**
     * 检查是否是放假时间
     * @param $time
     * @return bool true 放假
     */
    protected function checkJiaRi($time): bool
    {
        $list = [
            2021 => [
                ['05-01', 5], ['06-12', 3], ['09-19', 3], ['10-01', 7], ['12-31', 1],
            ],
            2022 => [
                ['01-01', 2], ['01-31', 7], ['04-03', 3], ['04-30', 3], ['06-03', 3], ['09-09', 3], ['10-01', 7],
            ],
        ];
        $list1 = [
            2021 => ['05-08', '09-18', '09-26', '10-09'],
            2022 => [],
        ];
        $year = date('Y', $time);
        $jiaList = $list[$year] ?? [];
        $buList = $list1[$year] ?? [];
        $bool = false;
        foreach ($jiaList as $v) {
            $start = strtotime($year . '-' . $v[0]);
            $end = $start + ($v[1] * 86400);
            if ($time >= $start && $time < $end) {
                $bool = true;
                break;
            }
        }
        $weekNum = date('N', $time);
        $yd = date('m-d', $time);
        // 周六和周日 和不在补休时间内
        if (in_array($weekNum, [6, 7]) && !in_array($yd, $buList)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 根据建除或吉凶神获得解释
     * @param $str
     * @return string
     */
    protected function getExplain($str): string
    {
        $list = [
            '不将' => '有利嫁娶的传统吉日，当日领证，寓意新人以后的生活平平顺顺。',
            '季分' => '传统嫁娶吉日，当日领证，寓意福泽绵长。',
            '三合' => '寓意新人互生互利的吉日，可作为领证的日子选用。',
            '岁德' => '德神护佑的吉日，积福之日，当日领证大吉。',
            '岁德合' => '德神护佑的吉日，积福之日，福气汇聚。',
            '红鸾天喜' => '有利嫁娶的吉日，红鸾报喜，当日领证大吉，象征生生世世恩爱。',
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '曲星' => '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜进行入宅、祭祖等事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，领证可用。',
            '四相' => '拥有四时王相贵气的日子，纯粹的小吉日。',
            '时德' => '得到天地舒畅之气，得到四时之气的祝福，小吉日。',
            '岁破日' => '岁破有大事勿用，难有圆满的说法。',
            '小红沙日' => '红沙日有诸事不宜，喜事勿用的说法。',
            '受死日' => '该日有晦气风流，大事勿用的说法。',
            '披麻日' => '披麻主孝丧之事，寓意不吉，需规避。',
            '月破' => '日值岁破，大事不宜。破日有破败之意，日月相冲，是为大耗。',
            '孤鸾日' => '孤鸾日有犯之婚姻凶，难长久的说法。',
            '阴差阳错日' => '阴差阳错有着事与愿违，难有结果的说法。',
            '朔日' => '此系恶鬼聚拢之辰，忌结婚进宅会客作乐。',
            '四离日' => '日值四离，大事勿用。',
            '四绝日' => '古话有云，四绝日结婚嫁娶，犯之不顺。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '归忌日' => '该日寓意不好，该日有忌远行归家、移徙移动、娶妇嫁女的说法。',
            '四废日' => '日值四废，作事不易成功，容易有始无终。',
            '彭祖百忌' => '彭祖百忌中有“亥不嫁娶，不利新郎”，解释为不利婚姻。',
            '杨公忌日' => '这日有诸事不宜，大事勿用的说法。',
            '三娘煞' => '在婚姻上该日属于大忌之日，古话有，迎亲嫁娶无男女，因而需要避开该日。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来领证有些不合时宜且当日民政局也放假。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，选择该日领证不合时宜。',
            '重阳' => '该日一般都是用来缅怀亡人，用作喜事有些不吉。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定。宜采纳、商贾、拜访、考试，婚嫁，移居等。',
            '满' => '满日在吉日中有“圆满”的含义，有着比较好的寓意。',
            '建' => '对于事情上来说，该日有着开始的说法，寓意较好。',
            '成' => '凡事有所成，因而该日诸事皆可办理。',
            '开' => '寓意着开通顺利，乃是一个好日子。',
            '平' => '平常、平分的日子，无吉无凶，领证可选。',
            '收' => '有收成，收获的意思，该日领证大吉。',
            '危' => '危日象征危机，是危险的一天，忌讳诸多喜事。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日有指破裂，冲破的含义。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进，有小破耗的说法。',
            '节假日' => '今日是国家法定非工作日，民政局一般不上班，具体的上班时间以当地民政局的上班时间为主。',
            '正冲' => '正冲年份正好是新人出生年干支',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 获得神煞解释
     * @param $str
     * @return string
     */
    protected function getShaRes($str): string
    {
        $list = [
            '青龙' => '传统习俗中的吉利日子，寓意成功，幸福。',
            '明堂' => '传统习俗中的吉利日子，寓意贵人相助，事情必定成功。',
            '金匮' => '传统习俗中的吉利日子，用于嫁娶大吉。',
            '玉堂' => '传统习俗中的吉利日子，用于领证为上选，当日一切顺利。',
            '司命' => '白天用事吉，夜晚则不宜，因此可用于领证，但夜间宴请宾客慎用。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克。该日万事皆忌，不宜有大动作。',
            '朱雀' => '天讼星，利用公事，常人凶，喜事忌用。',
            '白虎' => '天杀星，宜出师、畋猎、祭祀皆吉，其余都不利。',
            '天牢' => '镇神星，阴人用事皆吉，领证乃是大吉之事，故而不利。',
            '玄武' => '该神属天狱星，在一些大事上略有机会，有多小人嘴舌之兆。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满，故不宜出行。',
            '天德' => '天德黄道吉日占得天时，有三合旺气，是上等的吉日。',
        ];
        return $list[$str] ?? '';
    }
}
