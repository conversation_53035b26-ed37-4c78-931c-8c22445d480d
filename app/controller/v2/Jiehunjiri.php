<?php
// +----------------------------------------------------------------------
// | Jiehunjiri. 结婚吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Jiehunjiri
{
    /**
     * @var string[]
     */
    protected array $sxList = ['', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

    /**
     * @var Ex
     */
    protected Ex $mLunar;

    /**
     * @var Ex
     */
    protected Ex $fLunar;

    /**
     * 天干
     * @var string[]
     */
    protected array $tg = ['', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

    /**
     * 地支
     * @var string[]
     */
    protected array $dz = ['', '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * 月份
     * @var string[]
     */
    protected array $dz2 = ['', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];

    /**
     * 生肖相冲
     * @var string[]
     */
    protected array $sxChongList = ['', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳'];

    /**
     * 要排除的生肖
     * @var array
     */
    protected array $sxNumList = [];

    /**
     * 行嫁不将日
     * @var array[]
     */
    protected array $jiRiList = [
        '正月' => ['庚寅', '丙寅', '丁亥', '庚子', '丁卯', '辛丑', '己卯', '己丑'],
        '二月' => ['庚戌', '庚子', '庚寅', '己亥', '己丑', '丁亥', '乙亥', '丙戌', '乙丑', '丙寅'],
        '三月' => ['己酉', '己亥', '己丑', '丁酉', '丁亥', '甲戌', '丙戌', '甲子', '乙酉', '乙亥'],
        '四月' => ['丁酉', '丁亥', '丙申', '丙戌', '戊子', '乙酉', '乙亥', '乙丑', '甲戌', '甲子'],
        '五月' => ['丙申', '丙戌', '乙未', '乙酉', '乙亥', '甲申', '甲戌', '癸酉', '戊戌', '癸未'],
        '六月' => ['乙酉', '乙未', '甲午', '甲申', '甲戌', '癸未', '癸酉', '壬午', '壬申'],
        '七月' => ['乙未', '乙酉', '甲午', '甲申', '壬午', '壬申', '癸未', '癸酉'],
        '八月' => ['甲辰', '甲午', '甲申', '戊辰', '辛己', '辛未', '癸未', '壬申', '壬午'],
        '九月' => ['己卯', '己未', '己巳', '庚午', '壬午', '癸卯', '辛巳', '辛未', '庚辰'],
        '十月' => ['癸卯', '癸未', '己巳', '壬午', '己卯', '己巳', '庚寅', '庚辰', '庚午'],
        '十一月' => ['辛丑', '丁卯', '辛巳', '庚寅', '庚辰', '己丑', '己卯', '己巳'],
        '十二月' => ['辛丑', '丁卯', '庚子', '庚辰', '庚寅', '己丑', '己卯', '丙辰', '丙寅'],
    ];

    /**
     * 订单时间
     * @var array
     */
    protected array $oLunar;

    /**
     * 结婚吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男姓名
            // 'malename' => I('malename', '', 'trim'),
            // 男出生时间
            'maletime' => input('maletime', '', 'trim'),
            // 男父生肖
            'malefsx' => input('malefsx', 0, 'intval'),
            // 男母生肖
            'malemsx' => input('malemsx', 0, 'intval'),
            // 女姓名
            //'femalename' => I('femalename', '', 'trim'),
            // 女出生时间
            'femaletime' => input('femaletime', '', 'trim'),
            // 女父生肖
            'femalefsx' => input('femalefsx', 0, 'intval'),
            // 女母生肖
            'femalemsx' => input('femalemsx', 0, 'intval'),
            // 起始时间
            'otime' => input('otime', time(), 'trim'),
            // 月分
            'plan_time' => input('plan_time', 24, 'intval'),

        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'maletime|男方出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'malefsx|男父生肖' => ['require', 'between:0,12'],
                'malemsx|男母生肖' => ['require', 'between:0,12'],
                'femaletime|女方出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'femalefsx|女父生肖' => ['require', 'between:0,12'],
                'femalemsx|女母生肖' => ['require', 'between:0,12'],
                'otime|订单时间' => ['require', 'isDateOrTime:订单时间'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->mLunar = (new Ex($data['maletime']))->sex(0);
        $myear = $this->mLunar->dateTime->format('Y');
        $this->fLunar = (new Ex($data['femaletime']))->sex(1);
        $fyear = $this->fLunar->dateTime->format('Y');
        $oLunar = Ex::date($data['otime']);
        $data['otime'] = $oLunar->dateTime->getTimestamp();
        $this->oLunar = [
            'jinian' => $oLunar->getLunarTganDzhi(),
            'month' => $oLunar->getLunarYearMonth(),
            'year' => $oLunar->getLunarYear(),
            'day' => $oLunar->getLunarYearDay(),
            'y' => date('Y', $data['otime']),
            't' => $data['otime'],
            'end' => strtotime("+{$data['plan_time']} month", $data['otime']),
        ];
        $sxNumList = [
            $data['malefsx'], $data['malemsx'], $data['femalefsx'], $data['femalemsx'],
            array_search($this->mLunar->getZodiac(), $this->sxList),
            array_search($this->fLunar->getZodiac(), $this->sxList),
        ];
        foreach ($sxNumList as $v) {
            if ($v > 0) {
                $this->sxNumList[] = $v;
            }
        }
        $maleBazi = $this->mLunar->getLunarByBetween();
        $maleBazi['god'] = $this->mLunar->getGod();
        $maleBazi['_god'] = $this->mLunar->_getGod();
        $femaleBazi = $this->fLunar->getLunarByBetween();
        $femaleBazi['god'] = $this->fLunar->getGod();
        $femaleBazi['_god'] = $this->fLunar->_getGod();
        $res = [
            'male' => $maleBazi,
            'femail' => $femaleBazi,
            'gua' => BaziExt::getMingGua($myear, $fyear),
            'dali' => $this->getDaLiYue(),
            'xiaoli' => $this->getXiaoLiYue(),
            'jiri' => $this->getJiri2(),
        ];
        return $res;
    }

    /**
     * 吉日列表根据生肖剃除
     * @return array
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = strtotime(date('Y-m-d', $this->oLunar['t']));
        $endTime = $this->oLunar['end'];
        // 农历日子排除
        $list1 = ['清明'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        $list3 = ["乙巳", "丁巳", "辛亥", "戊申", "壬寅", "戊午", "壬子", "丙午", "丙午", "丙子", "丁未", "丁丑", "戊申", "戊寅", "辛酉", "辛卯", "壬戌", "壬辰", "癸巳", "癸亥"];
        $list4 = [
            '次吉' => '剔除凶日，避免与双方及其家人相冲的日子，能有效避免家庭之间发生冲突，促进家庭和睦。',
            '吉' => '五行圆满，干支平衡，调和阴阳，能有效减少婚姻灾劫，避免与公公婆婆互相妨害。',
            '中吉' => '事从权宜，趋吉避凶，以吉为用，能够避免婆媳之间的矛盾，让婚后生活更加幸福圆满。',
            '大吉' => '吉神相助，家宅顺遂，百事主吉。大吉之日，用之琴瑟调和，福泽绵延。',
        ];
        // 月建（月建破、危、闭）
        $list5 = [
            '正月' => ['申', '酉', '丑'],
            '二月' => ['寅', '酉', '戌'],
            '三月' => ['卯', '戌', '亥'],
            '四月' => ['辰', '亥', '子'],
            '五月' => ['巳', '子', '丑'],
            '六月' => ['寅', '午', '丑'],
            '七月' => ['寅', '卯', '未'],
            '八月' => ['卯', '辰', '申'],
            '九月' => ['辰', '巳', '酉'],
            '十月' => ['己', '午', '戌'],
            '冬月' => ['午', '未', '亥'],
            '腊月' => ['未', '申', '子'],
        ];
        // 黑道日
        $heidaoList = [
            '正月' => ["寅", "卯", "午", "申", "酉", "亥"],
            '二月' => ["辰", "巳", "申", "戌", "亥", "丑"],
            '三月' => ["午", "未", "戌", "子", "丑", "卯"],
            '四月' => ["申", "酉", "子", "寅", "卯", "巳"],
            '五月' => ["戌", "亥", "寅", "辰", "巳", "未"],
            '六月' => ["子", "丑", "辰", "午", "未", "酉"],
            '七月' => ["寅", "卯", "午", "申", "酉", "亥"],
            '八月' => ["辰", "巳", "申", "戌", "亥", "丑"],
            '九月' => ["午", "未", "戌", "子", "丑", "卯"],
            '十月' => ["申", "酉", "子", "寅", "卯", "巳"],
            '冬月' => ["戌", "亥", "寅", "辰", "巳", "未"],
            '腊月' => ["子", "丑", "辰", "午", "未", "酉"],
        ];
        // 吉日对应的吉时
        $shiList = [
            '子' => '申',
            '丑' => '戌',
            '寅' => '子',
            '卯' => '寅',
            '辰' => '辰',
            '巳' => '午',
            '午' => '申',
            '未' => '戌',
            '申' => '子',
            '酉' => '寅',
            '戌' => '辰',
            '亥' => '午',
        ];
        $res = [];
        // 生肖相冲
        $sxChongList = $this->sxChongList;
        // 相冲生肖
        $sxChong = [];
        foreach ($this->sxNumList as $v1) {
            $sxChong[] = $sxChongList[$v1];
        }
        $liYue = array_merge($this->getDaLiYue(), $this->getXiaoLiYue());
        for ($i = 0; $i < 730; $i++) {
            $tmpTime = $startTime + $i * 86400;
            if ($tmpTime > $endTime) {
                break;
            }
            $huangli = Huangli::date($tmpTime);
            $jieQi = Huangli::isJieQi($tmpTime);
            $tomorrowJieQi = Huangli::isJieQi($tmpTime + 86400);
            $nongli = $huangli->getNongLi();
            $base = [
                'nongli' => [
                    'y' => $nongli['cn_y'],
                    'm' => $nongli['cn_m'],
                    'd' => $nongli['cn_d'],
                ],
                '_nongli' => [
                    'y' => (int)$nongli['y'],
                    'm' => (int)$nongli['m'],
                    'd' => (int)$nongli['d'],
                ],
                'jinian' => $huangli->getLunarTganDzhi(),
            ];
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            if (in_array($jieQi, $list1)) {
                continue;
            }
            if (in_array($tomorrowJieQi, $list2)) {
                continue;
            }
            $tmp = [
                'time' => $tmpTime,
                'gongli' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'week' => Huangli::getWeekChs($tmpTime),
                'nongli' => implode('', $base['nongli']),
                'hour' => '',
                'title' => '次吉',
                'detail' => $list4['次吉'],
            ];
            // 干支
            $tmpGZ = [
                implode('', $base['jinian']['y']), implode('', $base['jinian']['m']), implode('', $base['jinian']['d']),
            ];
            // 农历月
            $nongliYue = str_replace('闰', '', $base['nongli']['m']);//去除闰字
            $rgz = $base['jinian']['d'];
            // 去除孤鸾日和阴差阳错日
            if (in_array($tmpGZ[2], $list3)) {
                continue;
            }
            // 判断生肖是否相冲
            if ($this->filterData3($tmpGZ[2], $sxChong)) {
                continue;
            }
            // 去除月建破、危、闭
            if (in_array($rgz[1], $heidaoList[$nongliYue])) {
                continue;
            }
            // 判断是否是黑道日
            if (in_array($rgz[1], $list5[$nongliYue])) {
                continue;
            }
            if (in_array($nongliYue, $liYue)) {
                $tmp['title'] = '吉';
                $tmp['detail'] = $list4['吉'];
                // 符合行嫁不将日
                if (in_array($tmpGZ[2], $this->jiRiList[$nongliYue])) {
                    $tmp['title'] = '中吉';
                    $tmp['detail'] = $list4['中吉'];
                }
                // 筛选完的日子找一个三合、六合、天月德的大吉日
                if ($this->filterData2($nongliYue, $rgz[1])) {
                    $tmp['title'] = '大吉';
                    $tmp['detail'] = $list4['大吉'];
                }
            }
            $tmp['hour'] = $shiList[$rgz[1]];
            $res[] = $tmp;
        }
        return $res;
    }

    /**
     * 判断生肖是否相冲
     * @param $from
     * @param $filterList
     * @return bool
     */
    protected function filterData3($from, $filterList): bool
    {
        $res = false;
        foreach ($filterList as $v2) {
            if (str_contains($from, $v2)) {
                $res = true;
                break;
            }
        }
        return $res;
    }

    /**
     * 判断日子找一个三合、六合、天月德的大吉日
     * @param $month string 农历月份
     * @param $rdz string 日地支
     * @return bool
     */
    protected function filterData2($month, $rdz): bool
    {
        $res = false;
        $hList = [
            '正月' => ['午', '戌', '亥', '丁', '壬', '丙', '辛'],
            '二月' => ['亥', '未', '戌', '申', '巳', '甲', '己'],
            '三月' => ['申', '子', '酉', '壬', '丁'],
            '四月' => ['酉', '丑', '申', '辛', '丙', '庚', '乙'],
            '五月' => ['寅', '戌', '未', '亥', '丙', '辛'],
            '六月' => ['亥', '卯', '午', '甲', '己'],
            '七月' => ['子', '辰', '巳', '癸', '戊', '壬', '丁'],
            '八月' => ['丑', '巳', '辰', '寅', '亥', '庚', '乙'],
            '九月' => ['午', '寅', '卯', '丙', '辛', '丙'],
            '十月' => ['卯', '未', '寅', '乙', '庚', '甲', '己'],
            '十一月' => ['申', '辰', '丑', '巳', '壬', '丁'],
            '十二月' => ['巳', '酉', '子', '庚', '乙'],
        ];
        if (in_array($rdz, $hList[$month])) {
            $res = true;
        }
        return $res;
    }

    /**
     * 大利月
     * @return array
     */
    protected function getDaLiYue(): array
    {
        $sx = $this->fLunar->getZodiac();
        $list = [
            '鼠' => ['十二月', '六月'],
            '牛' => ['十一月', '五月'],
            '虎' => ['八月', '二月'],
            '兔' => ['正月', '七月'],
            '龙' => ['四月', '十月'],
            '蛇' => ['三月', '九月'],
            '马' => ['十二月', '六月'],
            '羊' => ['十一月', '五月'],
            '猴' => ['八月', '二月'],
            '鸡' => ['正月', '七月'],
            '狗' => ['四月', '十月'],
            '猪' => ['三月', '九月'],
        ];
        return $list[$sx];
    }

    /**
     * 小利月
     * @return array
     */
    protected function getXiaoLiYue(): array
    {
        $sx = $this->fLunar->getZodiac();
        $list = [
            '鼠' => ['三月', '九月'],
            '牛' => ['二月', '八月'],
            '虎' => ['五月', '十一月'],
            '兔' => ['四月', '十月'],
            '龙' => ['正月', '七月'],
            '蛇' => ['六月', '十二月'],
            '马' => ['三月', '九月'],
            '羊' => ['二月', '八月'],
            '猴' => ['五月', '十一月'],
            '鸡' => ['四月', '十月'],
            '狗' => ['正月', '七月'],
            '猪' => ['六月', '十二月'],
        ];
        return $list[$sx];
    }

    /**
     * 字符串逐个字符转数组
     * @param $str
     * @return string[]
     */
    private function ch2Arr($str)
    {
        preg_match_all("/./u", $str, $arr);
        return $arr[0];
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    private function getJiri2(): array
    {
        $list = $this->getJiri();
        $hours = [
            '子' => '23:00-00:59',
            '丑' => '01:00-02:59',
            '寅' => '03:00-04:59',
            '卯' => '05:00-06:59',
            '辰' => '7：00-8：59',
            '巳' => '9：00-10：59',
            '午' => '11：00-12：59',
            '未' => '13：00-14：59',
            '申' => '15：00-16：59 ',
            '酉' => '17：00-18：59',
            '戌' => '19：00-20：59',
            '亥' => '21：00-22：59',
        ];
        $res = [
            '大吉' => [], '中吉' => [], '吉' => [], '次吉' => [],
        ];
        foreach ($list as $v) {
            $v['hours'] = $hours[$v['hour']];
            $res[$v['title']][] = $v;
        }
        $res = array_filter($res);
        return $res;
    }
}
