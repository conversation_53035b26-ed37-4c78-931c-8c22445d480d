<?php
// +----------------------------------------------------------------------
// | Rili.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\model\rili\Holiday;
use app\traits\BaseJieRiTraits;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Rili
{
    use BaseJieRiTraits;

    /**
     * 万年历信息
     * @return array
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time' => input('time', '2017-01-01', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'time|时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '时间超出范围';
                    }
                    return true;
                }],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        [$year, $month, $day] = explode('-', $data['time']);
        $huangli = Huangli::date($data['time']);

        $res = $huangli->getLunarWithJieRi();
        $res['gongli'] = $data['time'];
        $res['week'] = Huangli::getWeekChs($huangli->dateTime->getTimestamp());
        // 24节气
        $res['jieqi'] = $huangli->getJieQiCur();
        $jieQiList = SolarTerm::getAllJieQi($huangli->dateTime->format('Y'));
        // 方位
        $res['position'] = $huangli->getPosition();
        // 八门方位
        $res['bamen'] = $huangli->getBaMenPosition();
        $res['peng_zu'] = $huangli->getPengZuBaiJi();
        // 建除十二神
        $res['jianchu'] = $huangli->getJianChu();
        // 今日合害
        $res['hehai'] = $huangli->getTodayHeHai();
        // 纳音
        $res['na_yin'] = $huangli->getNayin();
        // 星宿
        $res['xing_su'] = $huangli->getXingSu();
        // 空亡
        $res['kong_wang'] = $huangli->getKongWang();
        // 值日
        $res['zhi_ri'] = $huangli->getZhiRi();
        // 三煞
        $res['san_sha'] = [
            'y' => $huangli->getSanSha($res['jinian']['y'][1]),
            'm' => $huangli->getSanSha($res['jinian']['m'][1]),
            'd' => $huangli->getSanSha($res['jinian']['d'][1]),
        ];
        // 星座
        $res['xing_zuo'] = $huangli->getXingZuo();
        // 吉凶宜忌
        $res['yi_ji'] = $huangli->getJiXiong();
        // 胎神
        $res['tai_shen'] = $huangli->getTaiShen();
        // 时辰详情
        $res['h_detail'] = $huangli->getHourDetail();
        // 三伏数九
        $res['sanshu'] = $huangli->getSanShu();
        return $res;
    }

    /**
     * 选择时间内宜忌的日子
     * @return array
     * @throws Exception
     */
    public function dayyi()
    {
        $data = [
            // 出生时间
            'start' => input('start', date('Y-m-d'), 'trim'),
            'end' => input('end', date('Y-m-d', strtotime('+30 day')), 'trim'),
            'text' => input('text', '', 'trim'),
            // 判断是否只显示周末 0为全部
            's' => input('show', 0, 'intval'),
            // 宜忌选择 0为宜 其他为忌
            'yj' => input('yj', 0, 'intval'),
            'stime' => input('stime', date('Y-m-d', time()), 'trim'),
        ];
        $list = [
            '祭祀' => '即拜祭祖先或祭拜神明等',
            '入学' => '应聘中的报名，开学典礼',
            '沐浴' => '祈福、许愿而沐浴斋戒，洗澡',
            '订婚' => '同纳采。受授聘金，可用于合婚订婚',
            '嫁娶' => '男婚女嫁，举行结婚大典的吉日',
            '疗病' => '治疗疾病',
            '破土' => '仅指埋葬用的破土，属阴宅',
            '安葬' => '举行埋葬等仪式',
            '会友' => '与亲戚朋友联络交往',
            '上梁' => '装上建筑物屋顶的梁木，同架马',
            '出行' => '外出旅游，观光游览',
            '上官' => '走马上任，上官赴任',
            '拆屋' => '拆掉房屋或建筑物',
            '作灶' => '安修厨灶、厨炉移位',
            '动土' => '指阳宅（即住房）之建造与修理',
            '祈福' => '祈求神明降福或设醮还愿之事',
            '求嗣' => '向神明祈求后嗣（子孙）之意',
            '求医' => '就医，请大夫看病',
            '装修' => '指阳宅之建造与修理',
            '剃头' => '剃胎发或削发出家。可用于理发、做发型',
            '破屋' => '拆屋或局部改造',
            '安床' => '指安置睡床卧铺之意',
            '打井' => '开凿水井，安装取水设备',
            '渡水' => '乘船通过江河',
            '栽种' => '指种植植物、接枝等农事',
            '平道' => '指铺平道路等工事',
            '捕捉' => '即扑灭农作物害虫或生物',
            '取鱼' => '指把养好的鱼取出来卖',
            '领养' => '指收纳养子养女',
            '修补' => '修理破损之物使之完好',
            '开渠' => '开挖水渠，兴修水利',
            '伐木' => '指砍伐树木，修剪花木花枝',
            '裁衣' => '指婚前准备新衣、钗环等。现指买新衣服。',
            '坏垣' => '拆除围墙',
            '求财' => '指祈求通过投资等活动赚取博得财富',
            '置业' => '指购买建成的房子',
            '考试' => '参加考核、面试等',
            '签约' => '签订合同、订立契约等商业、法律行为',
            '出货' => '商家之发货、出货、销货的意思',
            '交易' => '订立各种契约互相买卖之事，同立券',
            '开市' => '开业之意。新设店铺商行或新厂开幕等事',
            '立契' => '签订合同，订立契约等商业、法律行为',
            '收购' => '指购置产业、进货、收租、讨债、五谷入仓等',
            '买卖' => '订立各种契约互相买卖之事',
            '开仓' => '指打开粮仓货仓，取粮，出货',
            '诉讼' => '打官司，起诉状',
            '放债' => '借钱给人以收取利息',
            '入宅' => '指搬入新房子而非二手房',
            '搬家' => '搬家，指搬入旧屋（非新宅）',
            '大事勿用' => '一切大事都不要在这一天进行。',
        ];
        $validate = Validate::rule(
            [
                'start|开始时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '开始时间超出范围';
                    }
                    return true;
                }],
                'end|结束时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '结束时间超出范围';
                    }
                    return true;
                }],
                'text|类型' => ['require', 'chs', function ($value) {
                    $list = [
                        '祭祀', '入学', '沐浴', '开市', '交易', '诸事不宜', '会友', '订婚', '上梁', '安葬', '求财',
                        '置业', '考试', '签约', '祈福', '求嗣', '出行', '上官', '嫁娶', '求医', '装修', '出货', '入宅',
                        '立契', '平道', '搬家', '收购', '疗病', '破屋', '坏垣', '剃头', '破土', '安床', '买卖',
                        '捕捉', '拆屋', '取鱼', '修补', '无', '伐木', '裁衣', '开仓', '诸事不利', '作灶', '动土', '诉讼',
                        '放债', '打井', '渡水', '栽种', '领养', '移徒', '开渠',
                    ];
                    if (in_array($value, $list)) {
                        return true;
                    }
                    return '该类型不存在';
                }],
                'stime|手机时间' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $start = strtotime($data['start']);
        $end = strtotime($data['end']);
        if ($end <= $start) {
            return ['status' => 0, 'msg' => '结束时间不能小于开始时间'];
        }
        $diff = (int)(($end - $start) / 86400);
        if ($diff > 180) {
            return ['status' => 0, 'msg' => '时间最长为180天'];
        }
        $time = strtotime($data['stime']);
        $cacheKeyName = "tool/rili/dayyi_{$data['text']}_{$start}_{$end}_{$data['yj']}_{$data['stime']}";
        $res = cache($cacheKeyName);
        if (empty($res)) {
            $res = [];
            for ($i = 0; $i <= $diff; $i++) {
                $tmp = strtotime("+{$i} day", $start);
                $huangli = Huangli::date($tmp);
                $jiXiong = $huangli->getJiXiong();
                $tmpData = $huangli->getLunarByBetween();
                $tmpData['week'] = Huangli::getWeekChs($tmp);
                $tmpData['time'] = [
                    'y' => date('Y', $tmp),
                    'm' => date('m', $tmp),
                    'd' => date('d', $tmp),
                ];
                $tmpData['xing_su'] = $huangli->getXingSu()[0];
                // 值日
                $tmpData['zhi_ri'] = $huangli->getZhiRi();
                // 建除十二神
                $tmpData['jian_chu'] = $huangli->getJianChu();
                $tmpData['tian'] = (int)(($tmp - $time) / 86400);
                if ($data['yj']) {
                    // 忌
                    if (in_array($data['text'], $jiXiong['ji'])) {
                        $res[] = $tmpData;
                    }
                } else {
                    // 宜
                    if (in_array($data['text'], $jiXiong['yi'])) {
                        $res[] = $tmpData;
                    }
                }
            }
            cache($cacheKeyName, $res, 300);
        }
        if ($data['s']) {
            $tmp = [];
            foreach ($res as $k => $v) {
                if (in_array($v['week'], ['星期六', '星期日'])) {
                    $tmp[] = $v;
                }
            }
            $res = $tmp;
        }
        return [
            'title' => $list[$data['text']] ?? '',
            'num' => (int)count($res),
            'detail' => $res,
        ];
    }

    /**
     * 节气
     * @return array
     */
    public function jieqi()
    {
        $time1 = input('time', date('Y-m-d', time()), 'trim');
        if (false === strtotime($time1)) {
            return ['status' => 0, 'msg' => '时间格式出错'];
        }
        $time = strtotime($time1);
        $year = (int)date('Y', $time);
        $jieqi = SolarTerm::getAllJieQi($year);
        $liChunTime = strtotime($jieqi['立春']);
        $res = [];
        if ($liChunTime < $time) {
            $jieqi2 = SolarTerm::getAllJieQi($year + 1);
            $res = $jieqi;
            $res['小寒'] = $jieqi2['小寒'];
            $res['大寒'] = $jieqi2['大寒'];
        } else {
            $jieqi2 = SolarTerm::getAllJieQi($year - 1);
            $res = $jieqi2;
            $res['小寒'] = $jieqi['小寒'];
            $res['大寒'] = $jieqi['大寒'];
        }
        asort($res);
        $tmp = [];
        foreach ($res as $k => $v) {
            $tmp[] = [
                'title' => $k,
                'time' => $v,
            ];
        }
        return $tmp;
    }


    /**
     * 节日
     * @return array
     * @throws Exception
     */
    public function jieri()
    {
        $res = [];
        $time1 = input('time', date('Y-m-d', time()), 'trim');
        if (false === strtotime($time1)) {
            return ['status' => 0, 'msg' => '时间格式出错'];
        }
        $time = strtotime($time1);
        $year = (int)date('Y', $time);

        $list1 = $this->getJieRiListByYear($year);
        $list2 = $this->getJieRiListByYear($year + 1);

        for ($i = 0; $i <= 365; $i++) {
            $tmp = $time + 86400 * $i;
            $tmpYear = (int)date('Y', $tmp);
            $list3 = $list1;
            if ($tmpYear != $year) {
                $list3 = $list2;
            }
            if (isset($list3['jie'][$tmp])) {
                $resTmp = [
                    'd' => date('Y年m月d日', $tmp),
                    'w' => Huangli::getWeekChs($tmp),
                    'j' => $list3['jie'][$tmp][0]['title'],
                    'l' => $i,
                    'day' => '',
                    'num' => '',
                    'title' => [],
                ];
                foreach ($list3['jie'][$tmp] as $jieRi) {
                    $resTmp['title'][] = $jieRi['title'];
                    if (empty($jieRi['start_time']) || empty($jieRi['end_time'])) {
                        continue;
                    }
                    $startTime = strtotime($jieRi['start_time']);
                    $endTime = strtotime($jieRi['end_time']);
                    $resTmp['j'] = $jieRi['title'];
                    $resTmp['day'] = date('m月d日', $startTime) . '-' . date('m月d日', $endTime);
                    $resTmp['num'] = (string)(((int)($endTime - $startTime) / 86400) + 1);
                }
                $res[] = $resTmp;
            }
        }
        return $res;
    }

    /**
     * 现代文解释
     * @return array
     * @throws Exception
     */
    public function detail()
    {
        $data = [
            // 出生时间
            'time' => input('time', '2017-01-01', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'time|时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '时间超出范围';
                    }
                    return true;
                }],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        [$year, $month, $day] = explode('-', $data['time']);
        $huangli = Huangli::date($data['time']);
        $res = $huangli->getDetail();
        return $res;
    }

    /**
     * 分类
     * @return array
     */
    public function category()
    {
        $list = [
            [
                'title' => '常用',
                'detail' => ['嫁娶', '入宅', '搬家', '订婚', '会亲友', '开市', '装修', '出行'],
            ],
            [
                'title' => '婚姻',
                'detail' => ['订婚', '嫁娶', '安床', '领养'],
            ],
            [
                'title' => '生活',
                'detail' => ['入学', '求医', '疗病', '会亲友', '出行', '搬家', '剃头', '渡水', '栽种', '捕捉', '修补', '会友', '伐木', '考试', '开仓', '诉讼'],
            ],
            [
                'title' => '建筑',
                'detail' => ['上梁', '拆屋', '作灶', '开渠', '动土', '装修', '坏垣', '破屋', '打井', '平道'],
            ],
            [
                'title' => '工商',
                'detail' => ['上官', '取鱼', '置业', '求财', '签约', '出货', '交易', '开市', '立契', '收购', '买卖', '放债'],
            ],
            [
                'title' => '祭祀',
                'detail' => ['祭祀', '祈福', '求嗣', '安葬', '破土', '沐浴'],
            ],
        ];
        return $list;
    }

    /**
     * 节假日
     * @param string $time 日期 Y-m-d
     * @param int $type
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function jiejiari(string $time, int $type = 1)
    {
        if (false === strtotime($time)) {
            return ['status' => 0, 'msg' => '时间格式出错'];
        }
        $result = [];
        if ($type) {
            $year = date('Y', strtotime($time));
            $list = Holiday::infoByYear($year);
        } else {
            $endTime = date('Y-m-d', strtotime($time) + 86400 * 365);
            $list = Holiday::where('d', 'between', [$time, $endTime])->select();
        }
        foreach ($list as $k => $v) {
            $tmp = strtotime($v['d']);
            $numberDay = (int)(strtotime($v['end_time']) - strtotime($v['start_time'])) / 86400;
            $result[] = [
                'd' => $v['d'],
                'w' => Huangli::getWeekChs($tmp),
                'j' => $v['title'],
                'l' => (int)((($tmp - strtotime($time)) / 86400)) + 1,
                'num' => $numberDay + 1,
                'day' => $numberDay ? "{$v['start_time']}-{$v['end_time']}" : '',
            ];
        }
        return $result;
    }

    /**
     * 获得三伏数九详情
     * @return array
     */
    public function info()
    {
        $data = [
            'year' => input('year', date('Y'), 'intval'),
            'title' => input('title', '', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'year|时间' => ['require', 'number', 'between:1900,2099'],
                'title|节日名' => ['require'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $sanFulist = Huangli::getSanFuList($data['year']);
        $tmp = $data['year'] . '-';
        $nextYear = $data['year'] + 1;
        $list = [
            '三伏' => [
                'title' => '三伏',
                'start_time' => date('Y年m月d日', strtotime($tmp . $sanFulist[0])),
                'end_time' => date('Y年m月d日', strtotime($tmp . $sanFulist[3]) - 86400),
                'time_desc' => '阳历7月中旬到8月中旬',
                'description' => '“三伏”是初伏、中伏和末伏的统称，是一年中最热的时节。每年出现在阳历7月中旬到8月中旬。其气候特点是气温高、气压低、湿度大、风速小。“伏”表示阴气受阳气所迫藏伏地下。按我国阴历（农历）气候规律，前人早有规定：“夏至后第三个庚日开始为头伏（初伏），第四个庚日为中伏（二伏），立秋后第一个庚日为末伏（三伏），头伏和末伏各十天，中伏十天或二十天，“三伏”共三十天或四十天。每年夏至日到立秋日之间如果有四个庚日，中伏就为十天，如果有五个庚日，中伏为二十天，大多数年份中伏都为二十天。',
                'youlai' => [
                    '（一）“三伏天”是按照我国古代的“干支纪日法”确定的。每年夏至以后第三个庚日（指干支纪日中带有“庚”字的日子）为初伏，第四个庚日为中伏，立秋后第一个庚日为末伏，合起来称为三伏。',
                    '俗话说“热在三伏”。三伏是指初伏、中伏和末伏三个连续时段。夏至后第三个庚日为初伏始日，第四个庚日为中伏始日，立秋后的第一个庚日为末伏始日，每伏10天，但有些年份中伏为20天，比如2005年。',
                    '一般说来，“三伏天”中又以“中伏”的平均气温最高。当然，古人的这种推算方法多少有些出入，并不一定与当年的气象实际紧密结合，各地全年中的极端最高气温也不一定出现在“中伏”，甚至不一定在“三伏”。但不管怎么说，“三伏天”确实是盛夏酷暑的时候，要注意防暑降温。',
                    '（二）秦汉时盛行“五行相生相克”的说法，认为最热的夏天日子属火，而庚属金，金怕火烧熔（火克金），所以到庚日，金必伏藏。于是古人规定：',
                    '从夏至后的第三个庚日起为初伏（有10天），从夏至后的第四个庚日起为中伏（有的年是10天，有的年是20天），立秋后的第一个庚日起为末伏，也称终伏（有10天），总称为三伏。',
                    '每年入伏的时间不固定，中伏的长短也不相同，需要查历书计算，简单地可以用“夏至三庚”这4字口诀来表示入伏的日期，即从夏至后第3个“庚”日算起，初伏为10天，中伏为10天或20天，末伏为10天。',
                    '我国古代流行“干支纪日法”，用10个天干与12个地支相配而成的60组不同的名称来记日子，循环使用。每逢有庚字的日子叫庚日。庚日的“庚”字是“甲、乙、丙、丁、戊、己、庚、辛、壬、癸”10个天干中的第7个字，庚日每10天重复一次。',
                    '从夏至开始，依照干支纪日的排列，第3个庚日为初伏，第4个庚日为中伏，立秋后第1个庚日为末伏。如果立秋日及其后两天出现庚日，则中伏为10天，否则中伏为20天，于是出现了有些年份伏天30天，有些年份伏天40天的情况，且大部分年份都是40天，下一个三伏共计30天的年份是2031年。',
                ],
                'xisu' => [
                    '1.北方地区：头伏饺子二伏面，三伏烙饼卷鸡蛋',
                    '在北方地区有着“头伏饺子二伏面，三伏烙饼卷鸡蛋”的说法。',
                    '那么，头伏为什么要吃饺子呢？一种说法是饺子做得像元宝，寓意福气，而“伏”和“福”同音，所以就吃饺子。',
                    '入伏这天吃饺子，是希望能平安度过夏季，祝愿祈福之意。',
                    '另一种说法就是伏天里，人的胃口不好，吃不下去东西，而饺子在中国人的传统习俗里正是开胃解馋的食物。',
                    '山东有的地方会吃生黄瓜和煮鸡蛋来治苦夏，入伏的早晨吃鸡蛋，不吃别的食物。',
                    '二伏吃面，这一习俗至少在三国时期就已经开始了。为什么在热天里吃热面？南朝梁宗懔《荆楚岁时记》中说：“六月伏日食汤饼，名为辟恶。”五月是恶月，六月亦沾恶月的边儿，故也应“辟恶”。',
                    '这是迷信说法，用新小麦磨成面粉煮汤吃，吃后出一身汗，新粮营养丰富，发汗可以驱病，六月食汤饼是有科学道理的。',
                    '三伏要吃烙饼卷鸡蛋，这样的做法突出了一个“补”字。',
                    '在三伏当中，中伏的时间往往较为漫长。到了三伏的时候，人体消耗已经到了一定程度，该瘦的人都已经消瘦了很多。',
                    '所以，这个时候要适当多补充营养。烙饼摊鸡蛋中有油，有高蛋白的鸡蛋，很适合这时候的人来吃。',
                    '2.上海：头伏馄饨二伏茶',
                    '上海人总是会说头伏馄饨，二伏茶，因为三伏天气温很高，很容易影响人的食欲，而用一些酱料凉拌煮熟的馄饨是很开胃的方式。',
                    '可以很好的振奋食欲，避免三伏天食欲不振导致身体受损的情况出现。',
                    '而喝茶就很好理解了，在三伏天人会大量的流汗，导致体内水分迅速消失，而通过喝茶，能够帮助我们迅速补充身体内部的水分，并且喝茶还有利于排尿，可以将身体之中的暑气、暑热、暑湿通过尿液排出。',
                    '3.杭州：头伏火腿二伏鸡',
                    '杭州人“头伏火腿二伏鸡”。中医认为，火腿有健脾开胃、生津益血的功效。',
                    '火腿一年四季都可以吃，但是夏季吃为最佳。夏天人们往往食欲不振，营养不足。',
                    '火腿营养丰富，食之不腻，能增进食欲，和冬瓜烧汤，既有营养，又防暑祛病。',
                    '4.长沙：头伏狗二伏鸡三伏吃甲鱼',
                    '“伏狗”“伏鸡”是长沙人在三伏天最常吃的食物，因为这两种动物的肉能够补虚助阳，温里散寒。',
                    '对于阳气虚而畏惧寒冷的人来说，可以起到很好的温补作用，特别符合冬病夏治的理念。',
                    '此外，甲鱼有着滋阴清热、健脾开胃的功效，在三伏天食用尤为适宜，当然了，有肝病或者肠胃疾病的朋友，最好不要吃甲鱼，防止身体遭受损伤哦。',
                    '5.南昌：起伏吃只鸡',
                    '南昌有着在三伏天吃鸡的说法，并且这种吃法还有一定的规矩，如到了三伏，很多南昌人会将鸡做好，然后将门窗关闭，接着穿上长袖衣物，将鞋袜穿着整齐，裤脚则用绳子扎紧，也不开空调或者风扇，只是倒上一杯小酒，然后慢慢食用吃的自己大汗淋漓才可以。',
                    '因为许多南昌的老人认为，这样做能够将身体内部的湿气、寒气排出，让自己一整年都不生病。',
                    '6.徐州：伏羊一碗汤不用开药方',
                    '在徐州等地区人们认为羊经过春冬季节养殖后，正是膘肥肉嫩的时候，此时用羊肉炖出的汤味道醇厚，膻味很小，并且可以帮助人们发汗，排出身体内部的积热，减少这些热气暑气可能导致的疾病。',
                    '俗话说“冬养三九补品旺，夏治三伏行针忙”，冬病夏治由来已久。',
                    '冬病夏治方法很多，除了三伏贴，还有三伏天扎针灸，三伏天拔火罐、穴位注射、隔姜灸、艾灸等等。医生根据病人的病症不同，在这些方法中选择一两种使用。',
                    '很多慢性疾病都可以冬病夏治。包括哮喘、过敏性鼻炎、慢性咽炎、扁桃体炎、支气管炎、小儿感冒等，以及一些虚寒性的疾病，如胃痛、关节炎、肾虚引起的腰痛。',
                    '专家推荐两种目前很流行的冬病夏治方法，三伏贴和三伏天拔火罐。',
                    '之所以介绍这两种方法是因为它们具备4大优点：高效、安全、经济、副作用小。冬病夏治一般以30天为一个疗程，一般需连续3年，也就是3个疗程。为巩固疗效，病人应于冬至日到医院复诊。',
                ],
                'houdong' => [],
            ],
            '数九' => [
                'title' => '数九',
                'start_time' => "{$data['year']}年12月22日",
                'end_time' => "{$nextYear}年3月11日",
                'time_desc' => '每年冬至开始的81天',
                'description' => '从冬至之日起，中国即进入了数九寒天。天文专家介绍说，“九九”是我国北方特别是黄河中下游地区更为适用的一种杂节气。它从冬至那一天开始算起，进入“数九”，俗称“交九”，以后每九天为一个单位，谓之“九”，过了九个“九”，刚好八十一天，即为“出九”，那时就春暖花开了。\n中国传统文化中，九为极数，乃最大、最多、最长久的概念。九个九即八十一更是“最大不过”之数。古代中国人民认为过了冬至日的九九八十一日，春天肯定已经到来。',
                'youlai' => [
                    '中国民间有这么说法：“一九二九不出手，三九四九冰上茁超五九六九迅哂看柳，七九河开，八九雁来,九尽杨花开！”',
                    '数九又称冬九九，是一种中国民间节气。数九从每年阳历12月下旬初冬至开始。数九习俗起源于何时，现在还没有确切的资料。不过，至少在南北朝时已经流行。梁代宗懔《荆楚岁时记》中就写道：“俗用冬至日数及九九八十一日，为寒尽。”数九寒天，就是从冬至算起，每九天算一“九”，一直数到“九九”八十一天，“九尽桃花开”，天气就暖和了。实际上，是“九九又一九，耕牛遍地走”——整整90天，数九计日。',
                ],
                'xisu' => [
                    '画九：明代出现了“画九”的习俗。所谓的画，实则是冬至后计算春暖日期的图。明代《帝京景物略》载：“冬至日，画素梅一枝，为瓣八十有一。日染一瓣，瓣尽而九九出，则春深矣，曰九九消寒图。”此外，清代还有“九九消寒诗图”，图中每九天四句，共三十六句，内容从远古时代的“三皇治世”到本朝代的“大清坐金銮”，称得上是一部富有雅趣的历史“大事记”。',
                    '写九：继“画九”后，清代又出现了“写九”的习俗。“写九”的文化味也是很浓的，往往用“亭前垂柳珍重待春风”或“春前庭柏风送香盈室”九字（当然是繁体），先双钩成幅，从头九第一天开始填写（类似书法练习中的“描红”）。用粗毛笔着黑色，每字九笔，每笔一天，九字填完正好八十一天。有意思的是，每天填完一笔后，还要用细毛笔着白色在笔画上记录当日天气情况，所以，一行“写九”字幅，也是九九天里较详细的气象资料。',
                ],
                'houdong' => [
                    '顾禄《清嘉录》上记载有一首“数九歌”，十分风趣地描写了古代人们例冬的请形：',
                    '一九二九，相唤弗出手(手都因怕冷而缩在面袍里);',
                    '三九二十坡超篱头吹觱篥(寒风刮在篱笆头上，像吹觱篥一般，觱篥：bìlì 亦作觱栗，汉朝古代的一种管乐器,形似喇叭,以芦苇作嘴,以竹做管。);',
                    '四九三十六，夜眠如露宿(睡在被窝里像睡在野地里一样);',
                    '五九四十五，穷汉街头舞。不要舞，不要舞，还有春寒四十五(穷人衣薄，起舞取暖);',
                    '六九五十四，苍蝇垛屋茨(偷露出一点暖意了);',
                    '七九六十三，布袖两肩摊(天暖，厚衣服可以披在肩上了);',
                    '八九七十二，猫狗躺凉地;',
                    '九九八十一，穷汉受罪毕，刚要伸脚寐超蚊虫跳蚤出。',
                    '在古代社会里，穷人过冬尤其受罪。',
                ],
            ],
        ];
        if (!isset($list[$data['title']])) {
            return ['status' => 0, 'msg' => '没有相关数据'];
        }
        return $list[$data['title']];
    }
}
