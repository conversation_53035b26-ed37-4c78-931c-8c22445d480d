<?php
// +----------------------------------------------------------------------
// | Baziqiming.福缘殿有使用
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\lib\qumingdafen\Bihua;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\Ming3;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use Overtrue\Pinyin\Pinyin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Baziqiming
{
    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户生日实例
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 名字列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 姓
            'xing' => input('xing', '', 'trim'),
            // 性别 0 男 1 女
            'sex' => input('sex', '', 'trim'),
            // 生日
            'time' => input('time', '', 'trim'),
            // 字1的五行
            'zi' => input('zi', 0, 'intval'),
            // 字2的五行
            'zi2' => input('zi2', 0, 'intval'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 限制字数
            'limit' => input('limit', 30, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓' => ['require', 'chs'],
                'sex|性别' => ['require', 'in:0,1'],
                'time|生日' => ['require', 'isDateOrTime:出生时间'],
                'otime|测算时间' => ['require', 'date'],
                'zi|第一个字五行' => ['require', 'between:0,5'],
                'zi2|第二个字五行' => ['require', 'between:0,5'],
                'limit|限制名字数' => ['require', 'between:10,200'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->lunar = $lunar;
        $xiYongList = BaziExt::getxiYongJi($lunar);

        $xingInfo = $this->getXingInfo($data['xing']);
        $base = $lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $wuXingAttr = $lunar->wuXingAttr;
        $jnWx = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        foreach ($jiNian as $k => $v) {
            $tmp1 = $wuXingAttr[$v[0]];
            $tmp2 = $wuXingAttr[$v[1]];
            $jnWx[$tmp1]++;
            $jnWx[$tmp2]++;
        }
        $jnWx1 = [];
        foreach ($jnWx as $k => $v) {
            $jnWx1[] = [$k, $v];
        }
        // 名字列表
        $names = $this->getNameList();
        $result = [
            'bases' => $base,
            // 姓信息
            'xing' => $xingInfo,
            // 喜神
            'likegod' => $xiYongList['wx'][0],
            // 纪年五行
            'jnwx' => $jnWx1,
            // 名字列表
            'names' => $names,
        ];
        return $result;
    }

    /**
     * 获得名字列表
     * @return array
     */
    protected function getNameList(): array
    {
        $sex = $this->orginData['sex'];
        $zi = $this->orginData['zi'];
        $zi2 = $this->orginData['zi2'];
        $otime = strtotime($this->orginData['otime']) + $this->lunar->dateTime->getTimestamp();
        $limit = $this->orginData['limit'];
        if ($zi && $zi2) {
            $wxArr = [$zi . $zi2];
        } elseif ($zi) {
            $wxArr = [$zi . '1', $zi . '2', $zi . '3', $zi . '4', $zi . '5'];
        } elseif ($zi2) {
            $wxArr = ["1{$zi2}", "2{$zi2}", "3{$zi2}", "4{$zi2}", "5{$zi2}"];
        } else {
            $wxArr = [11, 12, 13, 14, 15, 21, 22, 23, 24, 25, 31, 32, 33, 34, 35, 41, 42, 43, 44, 45, 51, 52, 53, 54, 55];
        }
        $genderArr = [$sex];
        $list = Ming3::lists($wxArr, $genderArr, $otime, $limit);
        $result = [];
        foreach ($list as $v) {
            $result[] = $v['zi'] . $v['zi2'];
        }
        return $result;
    }

    /**
     * 获得姓五行和笔画
     * @param $xing
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getXingInfo($xing): array
    {
        $wx = '火';
        $bihua = 10;
        $xingArr = $this->getZiInfo($xing);
        if (count($xingArr) == 1) {
            $bihua = (int)$xingArr[0]['bihua'];
            $wx = $xingArr[0]['wx'];
        } else {
            $bihua = (int)array_sum(array_column($xingArr, 'bihua'));
            $wx = $this->getWuXing($bihua);
        }
        return [
            'xing' => $xing,
            'bihua' => $bihua,
            'wx' => $wx,
        ];
    }

    /**
     * 获得字相关拼音笔画
     * @param $str
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getZiInfo($str): array
    {
        $modelBihua = new Bihua();
        $res = [];
        preg_match_all("/./u", $str, $arr);
        foreach ($arr[0] as $v) {
            $v = $this->replacestr($v);
            $data = Cnword::info($v);
            if (empty($data)) {
                $res[] = [
                    'name' => $v,
                    'big5' => $v,
                    'bihua' => $modelBihua->find($v),
                    'pinyin' => Pinyin::sentence($v, 'none')->join(''),
                    'wx' => '火',
                ];
            } else {
                $res[] = [
                    'name' => $v,
                    'big5' => $data['big5'],
                    'bihua' => $data['bihua2'],
                    'pinyin' => $data['py2'],
                    'wx' => $data['wx'] ?: '火',
                ];
            }
        }
        return $res;
    }

    /**
     * 字体替换
     * @param $data
     * @return string
     */
    private function replacestr($data): string
    {
        $array = [
            '㯋' => '颖', '麹' => '曲',
        ];
        $res = $data;
        if (isset($array[$data])) {
            $res = $array[$data];
        }
        return $res;
    }

    /**
     * 获得五行属性
     * @param int $bihua
     * @return string
     */
    private function getWuXing($bihua): string
    {
        $num = $bihua % 10;
        $list = [
            0 => '水', 1 => '木', 2 => '木', 3 => '火', 4 => '火', 5 => '土',
            6 => '土', 7 => '金', 8 => '金', 9 => '水',
        ];
        return $list[$num];
    }
}
