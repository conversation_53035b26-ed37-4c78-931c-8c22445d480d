<?php
// +----------------------------------------------------------------------
// | 提车吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\jiri\JiRiBaseTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Tichejiri
{
    use JiRiBaseTraits;

    /**
     * 输入数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 五行属性
     * @var array
     */
    protected array $wuXingAttr;

    /**
     * 动工吉日
     * @param string $time
     * @param string $sex
     * @param string $otime
     * @param int $limit
     * @return array
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public function index($time = '', $sex = '', $otime = '', $limit = 365)
    {
        $data = [
            // 用户生日
            'time' => $time,
            // 性别 0男 1女
            'sex' => $sex,
            // 订单时间
            'otime' => $otime,
            // 限制天数
            'limit' => $limit,
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'sex|性别' => ['require', 'in:0,1'],
                'limit|限制天数' => ['require', 'number', 'between:10,731'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $dayList = $this->getDayList();
        $chongArr = [
            $this->getChongDz($base['jinian']['y'][1]), $this->getChongDz($base['jinian']['d'][1]),
        ];
        $result = [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 用神五行
            'yongshen' => $xiYong['wx'][1],
            // 喜神五行
            'xishen' => $xiYong['wx'][0],
            'chong' => array_values(array_unique($chongArr)),
            // 吉日
            'ji' => $dayList,
        ];
        return $result;
    }

    /**
     * 获得日期列表
     * @return array
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    protected function getDayList(): array
    {
        // 岁破 年+日
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        // 五墓 月+日干+日支
        $listWumu = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        // 四废 月+日干+日支
        $listShiFei = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
        ];
        // 相冲 或时破
        $listChongDz = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        // 五不遇时 流日天干	流时地支
        $listWuBu = [
            '甲午', '乙巳', '丙辰', '丁卯', '戊寅', '己丑', '庚子', '辛酉', '壬申', '癸未',
        ];
        $listDes = [
            'xiao' => [
                '三会' => '五行力量统一，融合有力，避免与自身运势相冲。',
                '三合' => '五行聚合，有情相生，趋吉避凶。',
                '半三合' => '事从权宜，提车吉，可按需采用。',
                '六合' => '贵人助力，暗中帮扶，五行力量不明显。',
                '旺' => '符合四季旺衰变化规律，四时之力处于旺盛状态。',
                '长生' => '万物始生，出行平安，提车吉。',
                '冠带' => '万物渐荣，寓意吉祥，提车可用。',
                '临官' => '身禄近贵，五行为用，小吉之日。',
                '帝旺' => '逢时强盛，五行调和，提车主吉。',
            ],
            'da' => [
                '三会' => '吉神照拂，干支有力，对福主有生助作用，提车主吉。',
                '三合' => '五行力量聚合的日子，相扶相生，有趋吉避凶的作用。',
                '半三合' => '五行力量稍弱，但是对福主有助益作用，事从权宜，提车可用。',
                '六合' => '五行力量稍弱，但有贵人暗中帮扶的意象，可用于提车之日。',
                '旺' => '当季五行力量较强，对福主运势有提升的作用。',
                '长生' => '处悠闲之地，五行始生，欣欣向荣，有开拓进取的意象。',
                '冠带' => '处喜庆之地，五行逐渐兴荣，有小成、小贵的意象。',
                '临官' => '处拼搏之地，利官近贵，有付出能够得到回报的意象。',
                '帝旺' => '处荣发之地，主吉，利禄进财，有达到最旺盛时期的意象。',
            ],
        ];
        $listHourRes = [
            '木' => [
                '子' => ['23时33分', '23时58分', '0时8分'],
                '丑' => ['1时33分', '2时8分'],
                '寅' => ['3时18分', '3时33分', '4时18分'],
                '卯' => ['5时8分', '5时58分', '6时18分', '6时58分'],
                '辰' => ['7时58分', '8时18分', '8时58分'],
                '巳' => ['9时8分', '9时18分', '9时58分', '10时18分'],
                '午' => ['11时33分', '11时58分', '12时8分'],
                '未' => ['13时33分', '13时58分', '14时18分'],
                '申' => ['16时8分', '16时18分'],
                '酉' => ['17时8分', '17时18分', '17时58分', '18时18分'],
                '戌' => ['19时8分', '19时18分'],
                '亥' => ['21时8分', '21时13分'],
            ],
            '火' => [
                '子' => ['23时37分', '0时12分'],
                '丑' => ['1时37分', '2时12分'],
                '寅' => ['3时37分'],
                '卯' => ['5时52分', '6时52分', '6时57分'],
                '辰' => ['7时12分', '7时32分', '8时12分', '8时52分'],
                '巳' => ['9时17分', '9时22分', '10时17分', '10时22分'],
                '午' => ['11时37分', '11时52分', '12时7分'],
                '未' => ['13时37分', '14时7分', '14时22分'],
                '申' => ['16时2分', '16时17分'],
                '酉' => ['17时17分', '17时22分', '18时17分', '18时22分'],
                '戌' => ['19时12分', '19时20分'],
                '亥' => ['21时2分', '21时12分'],
            ],
            '土' => [
                '子' => ['0时5分'],
                '丑' => ['1时35分'],
                '寅' => ['3时25分'],
                '卯' => ['5时10分', '5时50分', '6时20分', '6时50分'],
                '辰' => ['7时10分', '7时35分', '7时50分', '8时30分'],
                '巳' => ['9时15分', '9时30分', '10时25分'],
                '午' => ['11时35分', '11时50分', '12时10分'],
                '未' => ['13时35分', '13时50分'],
                '申' => ['16时15分', '16时20分'],
                '酉' => ['17时15分', '17时30分', '18时25分'],
                '戌' => ['19时10分'],
                '亥' => ['21时10分'],
            ],
            '金' => [
                '子' => ['23时39分', '0时9分'],
                '丑' => ['1时39分', '2时9分'],
                '寅' => ['3时19分'],
                '卯' => ['5时9分', '5时59分', '6时19分', '6时59分'],
                '辰' => ['7时9分', '7时59分', '8时19分', '8时59分'],
                '巳' => ['9时9分', '9时19分', '9时59分', '10时19分'],
                '午' => ['11时39分', '11时59分', '12时9分'],
                '未' => ['13时39分', '13时59分'],
                '申' => ['16时9分', '16时29分'],
                '酉' => ['17时9分', '17时19分', '17时59分', '18时19分'],
                '戌' => ['19时9分', '19时19分'],
                '亥' => ['21时9分'],
            ],
            '水' => [
                '子' => ['23时36分', '23时51分', '0时6分'],
                '丑' => ['1时36分', '2时6分'],
                '寅' => ['3时16分', '3时36分', '4时16分'],
                '卯' => ['5时6分', '5时56分', '6时16分', '6时56分'],
                '辰' => ['7时6分', '7时36分', '8时16分', '8时36分'],
                '巳' => ['9时6分', '9时36分', '9时56分', '10时16分'],
                '午' => ['11时31分', '11时56分', '12时6分'],
                '未' => ['13时31分', '13时56分', '14时16分'],
                '申' => ['16时1分', '16时16分'],
                '酉' => ['17时6分', '17时36分', '17时56分', '18时16分'],
                '戌' => ['19时6分', '19时16分'],
                '亥' => ['21时6分'],
            ],
        ];
        $listJiShen = [
            '煞贡', '人专', '直星', '天德', '天德合', '月德', '月德合', '天赦', '天愿', '月恩', '四相', '时德', '不将',
        ];
        $listSi = [
            '四绝' => ['立春', '立夏', '立秋', '立冬'],
            '四离' => ['春分', '夏至', '秋分', '冬至'],
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $ydz = $jiNian['y'][1];
        $ddz = $jiNian['d'][1];
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $result = [];
        $explain = [
            'jc' => [],
            'shen' => [],
            'sha' => [],
        ];
        for ($i = 1; $i <= $this->orginData['limit']; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $yesterday = $time + 86400;
            $yesterdayStr = date('Y-m-d', $yesterday);

            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $jiXiong = $huangli->getJiXiong();
            $base1 = $huangli->getLunarByBetween();
            $jiNianTmp = $base1['jinian'];
            $ddz10 = $jiNianTmp['d'][1];
            $ydz1 = $jiNianTmp['y'][1];
            $dtg1 = $jiNianTmp['m'][0];
            $mdz1 = $jiNianTmp['m'][1];
            $ddz1 = $jiNianTmp['d'][1];
            $dgz1 = implode('', $jiNianTmp['d']);
            $bool = true;
            $noList = [];
            $fxStr1 = '';
            $fxStr2 = '';
            if (BaziCommon::getXianXin($ddz10 . $ydz)) {
                $fxStr1 .= '刑';
                $bool = false;
            }
            if (BaziCommon::getXianChong($ddz10 . $ydz)) {
                $fxStr1 .= '冲';
                $bool = false;
            }
            if (BaziCommon::getXianXin($ddz10 . $ddz)) {
                $fxStr2 .= '刑';
                $bool = false;
            }
            if (BaziCommon::getXianChong($ddz10 . $ddz)) {
                $fxStr2 .= '冲';
                $bool = false;
            }
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 每年的固定日期
            if (in_array($nongliNumberStr, ['1_13', '2_21', '3-9', '4_7', '5_5', '6_3', '7_1', '7_29', '8_27', '9_20', '10_23', '11_21', '12_19'])) {
                $noList[] = '杨公忌日';
                $bool = false;
            }
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            $jianchu = $huangli->getJianChu();
            // 过滤 执日
            $jcType = 1;
            if (in_array($jianchu, ['危', '破', '闭', '执'])) {
                $jcType = 0;
                //$bool = false;
            }
            $jiNianDay = implode('', $jiNianTmp['d']);
            // 岁破过滤
            if ($listSuiPo[$jiNianTmp['y'][1]] == $jiNianTmp['d'][1]) {
                $noList[] = '岁破';
                $bool = false;
            }
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            $yesJieqi = $this->getJieQi($yesterdayStr);
            if (in_array($yesJieqi, $listSi['四绝'])) {
                $noList[] = '四绝';
                $bool = false;
            } elseif (in_array($yesJieqi, $listSi['四离'])) {
                $noList[] = '四离';
                $bool = false;
            }
            $yueRi2 = $jiNianTmp['m'][1] . $jiNianTmp['d'][0] . $jiNianTmp['d'][1];
            // 五墓+四废
            if (in_array($yueRi2, $listWumu)) {
                $noList[] = '五墓';
                $bool = false;
            }
            if (in_array($yueRi2, $listShiFei)) {
                $noList[] = '四废';
                $bool = false;
            }
            // 四耗   四穷日
            if ($this->checkSiHao($yueRi2)) {
                $noList[] = '四耗';
                $bool = false;
            }
            if ($this->checkSiQiong($yueRi2)) {
                $noList[] = '四穷';
                $bool = false;
            }

            $hourList = $huangli->getHourDetail();
            $jieqiArr = $huangli->getJieQiCur();
            $jieqiCur = $jieqiArr['current'][0];
            foreach ($hourList as $k1 => $v1) {
                // 流日地支	流时地支
                $hourTmp1 = $jiNianTmp['d'][1] . $v1['h'][1];
                $hourTmp2 = $jiNianTmp['d'][0] . $v1['h'][1];
                if (in_array($v1['h'][1], ['戌', '亥', '子', '丑', '寅', '卯'])) {
                    unset($hourList[$k1]);
                    continue;
                }
                if (in_array($hourTmp1, $listChongDz)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 流日地支	流时地支
                if (BaziExt::getXianXinDz($jiNianTmp['d'][1], $v1['h'][1])) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 五不遇时 流日天干+流时地支
                if (in_array($hourTmp2, $listWuBu)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 日干+时干支
                $hourTmp3 = $jiNianTmp['d'][0] . $v1['h'][0] . $v1['h'][1];
                if ($this->getTianMen($jieqiCur, $hourTmp3)) {
                    continue;
                }
                // 日支+时支
                if ($this->getHuanDaoJiShi($hourTmp1)) {
                    continue;
                }
                // 日干+时干支
                if ($this->getHuanDaoJiShi($hourTmp3)) {
                    continue;
                }
                unset($hourList[$k1]);
            }
            $tmpWxNum = 0;
            $jiNianTmp1 = $jiNianTmp;
            unset($jiNianTmp1['h']);
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德') ? '宝光' : $shenSha;
            $sxBase = new SxBase();
            $fenxiArr = [];
            if ($fxStr1) {
                $fenxiArr[] = ['y' => '年', 'd' => $fxStr1];
            }
            if ($fxStr2) {
                $fenxiArr[] = ['y' => '日', 'd' => $fxStr2];
            }
            $tmpRes = [
                'type' => 'buyi',
                'gongli' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'sx' => $base1['shengxiao'],
                'nongli' => $base1['nongli'],
                'jinian' => $jiNianTmp1,
                'jishen' => $jiXiong['jishen'],
                'xiong' => $jiXiong['xiong'],
                'type_hd' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                'week' => $week,
                // 煞向
                'sha_xian' => $this->getShaXian($ddz1),
                'chong' => $this->getDzChongSx($ddz1),
                'sx_chong' => $this->getDzChongSx($jiNianTmp1['d'][1]),
                'shensha' => $shenSha,
                'jianchu' => $jianchu,
                'reason' => $noList,
                'fenxi' => $fenxiArr,
            ];
            $tmpRes1 = [];
            if ($bool) {
                $hourArr = $this->getJiRiHour($jiNianTmp['d'][0], $jiNianTmp['d'][1]);
                if ($shenSha === '司命') {
                    foreach ($hourArr as $kh => $vh) {
                        if (!in_array($vh['dz'], ['寅', '卯', '辰', '巳', '午', '未', '申', '酉'])) {
                            unset($hourArr[$kh]);
                            continue;
                        }
                    }
                    $hourArr = array_values($hourArr);
                }
                $tmpDes = '';
                $type = 'ping';
                foreach ($hourList as $v1) {
                    $tmpJiNian = $jiNianTmp;
                    $tmpJiNian['h'] = $v1['h'];
                    $chenJuWanDe = $this->getChenJuWanDe($tmpJiNian);
                    if (empty($chenJuWanDe)) {
                        continue;
                    }
                    // 五行数值
                    $wxNumList = BaziExt::getWuxingNum($tmpJiNian);
                    arsort($wxNumList);
                    $wxLiu = key($wxNumList);
                    $maxWxNum = current($wxNumList);
                    // 喜神、用神、仇神、忌神、闲神
                    if (in_array($wxLiu, [$xiYong['wx'][0], $xiYong['wx'][1]])) {
                        $type = 'da';
                    } elseif ($wxLiu == $xiYong['wx'][4]) {
                        $type = 'xiao';
                    } else {
                        $type = 'ping';
                    }
                    if ($maxWxNum <= $tmpWxNum) {
                        continue;
                    }
                    $tmpWxNum = $maxWxNum;
                    $tmpResTimeList = $listHourRes[$xiYong['wx'][1]][$v1['h'][1]];
                    $count = count($tmpResTimeList);
                    $timeKey = $time % $count;
                    $tmpDes = '该日不忌出行，可做权宜之用。所值吉神，有逢凶化吉的寓意，易获得贵人帮助。';
                    if ($type != 'ping') {
                        $tmpDes = $listDes[$type][$chenJuWanDe] ?? $listDes[$type]['三会'];
                    }
                }
                $tmpRes1 = [
                    'type' => $type,
                    'gongli' => [
                        'y' => (int)date('Y', $time),
                        'm' => (int)date('m', $time),
                        'd' => (int)date('d', $time),
                    ],
                    'nongli' => $base1['nongli'],
                    'sx' => $base1['shengxiao'],
                    'jinian' => $jiNianTmp1,
                    'jishen' => $jiXiong['jishen'],
                    'xiong' => $jiXiong['xiong'],
                    'type_hd' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                    'week' => $week,
                    'sha_xian' => $this->getShaXian($ddz1),
                    'chong' => $this->getDzChongSx($ddz1),
                    'des' => $tmpDes,
                    //'time' => $this->getShiInfo($tmpResTimeList[$timeKey]),
                    'hour' => $hourArr,
                    'sx_chong' => $this->getDzChongSx($jiNianTmp1['d'][1]),
                    'pos' => $this->getDongGongPos($jiNianTmp1),
                    'shensha' => $shenSha,
                    'jianchu' => $jianchu,
                    'reason' => [],
                ];
            }
            $resType = $tmpRes['gongli']['y'] . '年' . $tmpRes['gongli']['m'] . '月';
            $goodNum = $result[$resType]['num'] ?? 0;
            if ($tmpRes1) {
                $tmpRes = $tmpRes1;
                $goodNum++;
            } else {
                if ($bool) {
                    $tmpRes['reason'] = [
                        '时辰不合',
                    ];
                }
            }
            if ($tmpRes['type'] === 'buyi') {
                if ($jcType) {
                    $tmpRes['jianchu'] = '';
                }
                if ($tmpRes['type_hd'] === 1) {
                    $tmpRes['shensha'] = '';
                }
            } else {
                if (!$jcType) {
                    $tmpRes['jianchu'] = '';
                }
                if ($tmpRes['type_hd'] === 0) {
                    $tmpRes['shensha'] = '';
                }
                $tmpRes['reason'] = array_intersect($jiXiong['jishen'], $listJiShen);
            }
            foreach ($tmpRes['reason'] as $v1) {
                if (isset($explain['shen'][$v1])) {
                    continue;
                }
                $explain['shen'][$v1] = $this->getExplain($v1);
            }
            if (!empty($tmpRes['jianchu']) && !isset($explain['jc'][$jianchu])) {
                $explain['jc'][$jianchu] = $this->getExplain($jianchu);
            }
            if (!empty($tmpRes['shensha']) && !isset($explain['sha'][$shenSha])) {
                $explain['sha'][$shenSha] = $this->getShaRes($shenSha);
            }
            $result[$resType]['title'] = $resType;
            $result[$resType]['num'] = $goodNum;
            $result[$resType]['info'][] = $tmpRes;
        }
        return [
            'explain' => $explain,
            'list' => array_values($result),
        ];
    }

    /**
     * 日干不旺靠时禄补
     * @param string $str 日干+时干支
     * @return bool
     */
    protected function getLuGui(string $str): bool
    {
        $list = [
            '甲丙寅', '乙己卯', '丙癸巳', '丁丙午', '戊丁巳', '己庚午', '庚甲申', '辛丁酉', '壬辛亥', '癸壬子',
        ];
        return in_array($str, $list);
    }

    /**
     * 获得地支相冲生肖
     * @param string $dz
     * @return string
     */
    protected function getDzChongSx(string $dz): string
    {
        $list = [
            '子' => '马', '丑' => '羊', '寅' => '猴', '卯' => '鸡', '辰' => '狗', '巳' => '猪',
            '午' => '鼠', '未' => '牛', '申' => '虎', '酉' => '兔', '戌' => '龙', '亥' => '蛇',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 判断四耗日
     * @param string $string 月支+流日柱干支
     * @return bool
     */
    protected function checkSiHao(string $string): bool
    {
        $list = [
            '子辛酉', '丑辛酉', '寅壬子', '卯壬子', '辰壬子', '巳乙卯', '午乙卯', '未乙卯', '申戊午', '酉戊午', '戌戊午', '亥辛酉',
        ];
        return in_array($string, $list);
    }

    /**
     * 四穷日
     * @param string $string 月支+流日柱干支
     * @return bool
     */
    protected function checkSiQiong(string $string): bool
    {
        $list = [
            '寅乙亥', '卯乙亥', '辰乙亥', '巳丁亥', '午丁亥', '未丁亥', '申辛亥', '酉辛亥', '戌辛亥', '亥癸亥', '子癸亥', '丑癸亥',
        ];
        return in_array($string, $list);
    }

    /**
     * 坏的结果
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '破' => '此日万事不利，只能做破垣坏屋之事，忌办一切喜凶事。',
            '危' => '危日象征危机，是危险的一天，忌讳诸多喜事。',
            '平' => '平常、平分的日子，无吉无凶，提车可选。',
            '收' => '有收成，收获的意思，该日提车工大吉。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进的意思。这一日有小破耗的说法，需规避。',
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定，寓意吉祥，该日可选。',
            '成' => '凡事有所成，因而该日诸事皆可办理。',
            '开' => '寓意着顺利开始，乃是一个好日子，提车可选。',
            '满' => '满日在吉日中有“圆满”的含义，利于提车。',
            '建' => '对于事情上来说，该日有着起始的说法，寓意较好。',
            '四废' => '四废有五行无气，福德不临之日的意思。',
            '五墓' => '五墓日是一种忌日，适合祭祖，不适合其他喜事。',
            '归忌' => '该日寓意不好，有凶日的说法，最好规避。',
            '受死' => '该日寓意大凶，因而有不宜诸吉事的说法。',
            '往亡' => '古话有云，往亡煞临世，动必有险厄。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来提车有些不合时宜。',
            '劫煞' => '劫煞为打劫之星，主路遇不安，土匪劫道，有红伤血光之灾。',
            '灾煞' => '灾煞为灾星，主牢狱凶灾，健康受损，六畜不安。',
            '月刑' => '该日有刑伤之意，而且对于一些喜事上并不太适合。',
            '月厌' => '该日又称大祸日，寓意不吉。',
            '大时' => '该日标识表示精光、消减。对运势有些影响，需规避。',
            '天吏' => '天吏的解释为天子的官吏，有奉天命而之人罪的寓意。',
            '红沙' => '红沙日有诸事不宜的说法。',
            '杨公忌日' => '这日诸事不宜，忌讳喜庆之事。',
            '黄历忌' => '该日黄历不宜提车，最好规避。',
            '时辰不合' => '该日中没有适合提车的时辰',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，选择该日提车有些不太适合。',
            '岁煞' => '岁煞是当头太岁，太岁头上动土，招惹是非口舌，官司牢狱破财之事。',
            '重阳' => '当日有大事勿用的说法，因而忌交易、纳财。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '岁破' => '日值岁破，大事不宜。',
            '四穷' => '“四穷日”顾名思义贫穷!它是八字中的凶煞，代表财产、资源匮乏',
            '四耗' => '四耗为破财凶日，事多不宜，需避开。',
            '四绝' => '古话有云，四绝日有诸事不宜，喜事勿用的说法。',
            '四离' => '日值四离，大事勿用。',
            '披麻' => '披麻主孝丧之事，该日提车大有不吉，需避讳一下。',
            '月破' => '日值月破，大事不宜。破日有破败之意，日月相冲，是为大耗。',
            '煞贡' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '人专' => '三皇吉星之一，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '直星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子，适合提车。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德，该日有步步高升的寓意。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜进行入宅、提车等事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事，提车可选。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，提车大吉。',
            '四相' => '拥有四时王相贵气的日子，纯粹的小吉日，适合诸多喜事。',
            '时德' => '得到天地舒畅之气，得到四时之气的祝福，是个不错的吉日。',
            '不将' => '有喜庆的传统吉日，当日提车，寓意一路顺生，心想事成。',
            '驿马' => '驿马主走动、外出、旅行、出差、提车、转职等与移动有关的事象。',
            '季分' => '宜提车、提车的传统吉日，寓意幸福。',
            '岁德' => '德神护佑的吉日，积福之日，福气汇聚。象征提车大吉，万事如意。',
            '岁德合' => '出门有福神庇佑，可逢凶化吉，提车有贵人相助。',
            '显星' => '三皇吉星之一，该日有步步高升的寓意。',
            '曲星' => '三皇吉星之一，该日对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄，万事称心的寓意。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 获得节气名
     * @param string $timeStr 时间字符串 格式 Y-m-d
     * @return int|string
     */
    protected function getJieQi($timeStr)
    {
        $time = strtotime($timeStr);
        $year = (int)date('Y', $time);
        $jieqi = $this->getAllJieQi($year);
        $res = '';
        foreach ($jieqi as $k => $v) {
            $tmp = strtotime($v);
            $tmpStr = date('Y-m-d', $tmp);
            if ($tmpStr == $timeStr) {
                $res = $k;
                break;
            }
        }
        return $res;
    }

    /**
     * 获得煞神结果
     * @param string $str
     * @return string
     */
    protected function getShaRes(string $str): string
    {
        $listShaShen = [
            '青龙' => '此日是贵人出现之日，提车逢凶化吉，遇贵人。',
            '明堂' => '该日是明辅星的日子，万事大吉，利于百事。',
            '金匮' => '传统习俗中的吉利日子，用于提车乃是大吉。',
            '玉堂' => '传统习俗中的吉利日子，用于提车为上选，当日一切顺利。',
            '司命' => '当天用事大吉，因此可用于提车，寓意一路顺风。',
            '天刑' => '天刑属火，凶星，主刑夭孤克，寓意不吉。',
            '朱雀' => '天讼星，利用公事，常人凶，喜事忌用，谨防远行动作。',
            '白虎' => '天杀星，寓意不吉，提车最好规避。',
            '天牢' => '镇神星，阴人用事皆吉，其余都不利。',
            '玄武' => '天狱星，有多小人嘴舌之兆，提车寓意不吉。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '宝光' => '该日大利提车，所做的事情容易成功，有大吉之相。',
        ];
        return $listShaShen[$str] ?? $str;
    }

    /**
     * 根据地支获得对应的地支
     * @param string $dz
     * @return string
     */
    private function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 获得煞向
     * @param string $dz 日地支
     * @return string
     */
    protected function getShaXian(string $dz): string
    {
        $list = [
            '子' => '南', '丑' => '东', '寅' => '北', '卯' => '西', '辰' => '南', '巳' => '东',
            '午' => '北', '未' => '西', '申' => '南', '酉' => '东', '戌' => '北', '亥' => '西',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 每日吉时
     * @param string $tg 日干
     * @param string $dz 日支
     * @return array
     */
    protected function getJiRiHour(string $tg, string $dz): array
    {
        $list = [
            '辰' => [
                'xc' => ['辰', '戌'],
                'd' => ['酉', '子', '申', '寅', '卯'],
            ],
            '巳' => [
                'xc' => ['申', '亥'],
                'd' => ['申', '酉', '丑', '午', '未'],
            ],
            '未' => [
                'xc' => ['丑'],
                'd' => ['午', '亥', '卯', '巳', '午'],
            ],
            '申' => [
                'xc' => ['寅'],
                'd' => ['巳', '辰', '子', '酉', '戌'],
            ],
            '酉' => [
                'xc' => ['酉', '卯'],
                'd' => ['辰', '巳', '丑', '戌', '申'],
            ],
            '午' => [
                'xc' => ['午', '子'],
                'd' => ['未', '寅', '戌', '巳', '未'],
            ],
        ];
        $mYgz = $this->lunar->getLunarGanzhiYear();
        $mydz = $mYgz[1];
        $result = [];
        foreach ($list as $k => $v) {
            if (!in_array($dz, $v['d'])) {
                continue;
            }
            if (in_array($mydz, $v['xc'])) {
                continue;
            }
            if (BaziCommon::getXianChong($k . $mydz)) {
                continue;
            }
            $result[] = $this->getHourDetail($tg, $k);
        }
        if (!empty($result)) {
            return $result;
        }
        foreach ($list as $k => $v) {
            if (BaziCommon::getXianChong($k . $mydz)) {
                continue;
            }
            if (in_array($mydz, $v['d']) || in_array($mydz, $v['d'])) {
                $result[] = $this->getHourDetail($tg, $k);
            }
        }
        return $result;
    }

    /**
     * 获得时详情
     * @param string $dtg 日天干
     * @param string $dz 时支
     * @return array
     */
    protected function getHourDetail(string $dtg, string $dz): array
    {
        $listShi = [
            '午' => '中午11点至13点', '未' => '下午13点至15点', '申' => '下午15点至17点', '酉' => '下午17点至19点',
            '戌' => '晚上19点至21点', '亥' => '晚上21点至23点', '子' => '晚上23点至1点', '丑' => '凌晨1点至3点',
            '寅' => '凌晨3点至5点', '卯' => '早上5点至7点', '辰' => '早上7点至9点', '巳' => '早上9点至11点',
        ];
        $gz = Huangli::getGanzhiHour($dtg, $dz);
        $sxBase = new SxBase();
        $sx = $sxBase->getsxByDz($dz);
        $chongArr = $sxBase->getChong($sx);
        return [
            'dz' => $dz,
            'tg' => $gz[0],
            'h' => $listShi[$dz],
            'chong' => $chongArr['name'],
            // 'zc' => $zc,
        ];
    }
}
