<?php
// +----------------------------------------------------------------------
// | Huanxiyuanjia.欢喜冤家
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v2;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;

class Huanxiyuanjia
{
    /**
     * 五行
     * @var
     */
    protected array $wuxing = [];

    /**
     * 生态表
     * @var array
     */
    protected array $ecology = [
        '甲子' => ['甲木生于子月', '甲生子月'], '乙丑' => ['乙木生于丑月', '乙生丑月'], '丙寅' => ['丙火生于寅月', '丙生寅月'], '丁卯' => ['丁火生于卯月', '丁生卯月'],
        '戊辰' => ['戊土生于辰月', '戊生辰月'], '己巳' => ['巳土生于巳月', '巳生巳月'], '庚午' => ['庚金生于午月', '庚生午月'], '辛未' => ['辛金生于未月', '辛生未月'],
        '壬申' => ['壬水生于申月', '壬生申月'], '癸酉' => ['癸水生于酉月', '癸生酉月'], '甲戌' => ['甲木生月戌月', '甲生戌月'], '乙亥' => ['乙木生于亥月', '乙生亥月'],
        '丙子' => ['丙火生于子月', '丙生子月'], '丁丑' => ['丁火生于丑月', '丁生丑月'], '戊寅' => ['戊土生于寅月', '戊生寅月'], '己卯' => ['己土生于卯月', '己生卯月'],
        '庚辰' => ['庚金生于辰月', '庚生辰月'], '辛巳' => ['辛金生于巳月', '辛生巳月'], '壬午' => ['壬水生于午月', '壬生午月'], '癸未' => ['癸水生于未月', '癸生未月'],
        '甲申' => ['甲木生于申月', '甲生申月'], '乙酉' => ['乙木生于酉月', '乙生酉月'], '丙戌' => ['丙火生于戌月', '丙生戌月'], '丁亥' => ['丁火生于亥月', '丁生亥月'],
        '戊子' => ['戌土生于子月', '戌生子月'], '己丑' => ['己土生于丑月', '己生丑月'], '庚寅' => ['庚金生于寅月', '庚生寅月'], '辛卯' => ['辛金生于卯月', '辛生卯月'],
        '壬辰' => ['壬水生于辰月', '壬生辰月'], '癸巳' => ['癸水生于巳月', '癸生巳月'], '甲午' => ['甲木生于午月', '甲生午月'], '乙未' => ['乙木生于未月', '乙生未月'],
        '丙申' => ['丙火生于申月', '丙生申月'], '丁酉' => ['丁火生于酉月', '丁生酉月'], '戊戌' => ['戊土生于戌月', '戊生戌月'], '己亥' => ['己土生于亥月', '己生亥月'],
        '庚子' => ['庚金生于子月', '庚生子月'], '辛丑' => ['辛金生于丑月', '辛生丑月'], '壬寅' => ['壬水生于寅月', '壬生寅月'], '癸卯' => ['癸水生于卯月', '癸生卯月'],
        '甲辰' => ['甲木生于辰月', '甲生辰月'], '乙巳' => ['乙木生于巳月', '乙生巳月'], '丙午' => ['丙火生于午月', '丙生午月'], '丁未' => ['丁火生于未月', '丁生未月'],
        '戊申' => ['戊土生于申月', '戊生申月'], '己酉' => ['己土生于酉月', '己生酉月'], '庚戌' => ['庚金生于戌月', '庚生戌月'], '辛亥' => ['辛金生于亥月', '辛生亥月'],
        '壬子' => ['壬水生于子月', '壬生子月'], '癸丑' => ['癸水生于丑月', '癸生丑月'], '甲寅' => ['甲木生于寅月', '甲生寅月'], '乙卯' => ['乙木生于卯月', '乙生卯月'],
        '丙辰' => ['丙火生于辰月', '丙生辰月'], '丁巳' => ['丁火生于巳月', '丁生巳月'], '戊午' => ['戊土生于午月', '戊生午月'], '己未' => ['己土生于未月', '己生未月'],
        '庚申' => ['庚金生于申月', '庚生申月'], '辛酉' => ['辛金生于酉月', '辛生酉月'], '壬戌' => ['壬水生于戌月', '壬生戌月'], '癸亥' => ['癸水生于亥月', '癸生亥月'],
    ];

    /**
     * 男八字类
     * @var Ex
     */
    protected Ex $maleLunar;

    /**
     * 女八字类
     * @var Ex
     */
    protected Ex $femaleLunar;

    /**
     * 喜 用 list
     * @var array
     */
    protected array $xiYongList = [];

    /**
     * 相合相害
     * @var array
     */
    protected array $listXianHe = [
        ['己甲', '乙庚', '丙辛', '丁壬', '戊癸'],
        ['丑子', '亥寅', '卯戌', '辰酉', '巳申', '午未'],
        '子申辰-丑巳酉-午寅戌-亥卯未',
        ['午子', '丑未', '寅申', '卯酉', '戌辰', '亥巳', '寅巳', '巳申', '寅申', '丑戌', '戌未', '卯子', '午午', '酉酉', '亥亥', '子未', '丑午', '寅巳', '卯辰', '亥申', '戌酉'],
    ];

    /**
     * 克制表
     * @var array
     */
    protected array $kezhi = [
        '金' => [
            '金' => ['同', '扶助'],
            '木' => ['克', '克制'],
            '水' => ['生', '生助'],
            '火' => ['被克', '宣泄'],
            '土' => ['被生', '损耗'],
        ],
        '木' => [
            '金' => ['被克', '宣泄'],
            '木' => ['同', '扶助'],
            '水' => ['被生', '损耗'],
            '火' => ['生', '生助'],
            '土' => ['克', '克制'],
        ],
        '水' => [
            '金' => ['被生', '损耗'],
            '木' => ['生', '生助'],
            '水' => ['同', '扶助'],
            '火' => ['克', '克制'],
            '土' => ['被克', '宣泄'],
        ],
        '火' => [
            '金' => ['克', '克制'],
            '木' => ['被生', '损耗'],
            '水' => ['被克', '宣泄'],
            '火' => ['同', '扶助'],
            '土' => ['生', '生助'],
        ],
        '土' => [
            '金' => ['生', '生助'],
            '木' => ['被克', '宣泄'],
            '水' => ['克', '克制'],
            '火' => ['被生', '损耗'],
            '土' => ['同', '扶助'],
        ],
    ];

    /**
     * 用户数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 获取信息
     * @return  array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男出生时间
            'maletime' => input('maletime', '', 'trim'),
            // 女出生时间
            'femaletime' => input('femaletime', '', 'trim'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
            // 订单创建时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'maletime|男出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'femaletime|女出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->maleLunar = (new Ex($data['maletime']))->sex(0);
        $this->wuxing = $this->maleLunar->wuXingAttr;
        $this->femaleLunar = (new Ex($data['femaletime']))->sex(1);
        $this->orginData = $data;
        $jiNianM = $this->maleLunar->getLunarTganDzhi();
        $jiNianF = $this->femaleLunar->getLunarTganDzhi();
        $mWd = BaziExt::getWangDu($jiNianM);
        $fWd = BaziExt::getWangDu($jiNianF);
        $this->xiYongList = [
            'ge' => [$mWd, $fWd],
        ];
        if (in_array($mWd, ['身弱格', '从旺格'])) {
            $this->xiYongList['m']['y'] = ['正印', '偏印', '比肩', '劫财'];
            $this->xiYongList['m']['j'] = ['正官', '七杀', '正财', '偏财', '食神', '伤官'];
        } else {
            $this->xiYongList['m']['y'] = ['正官', '七杀', '正财', '偏财', '食神', '伤官'];
            $this->xiYongList['m']['j'] = ['正印', '偏印', '比肩', '劫财'];
        }
        if (in_array($fWd, ['身弱格', '从旺格'])) {
            $this->xiYongList['f']['y'] = ['正印', '偏印', '比肩', '劫财'];
            $this->xiYongList['f']['j'] = ['正官', '七杀', '正财', '偏财', '食神', '伤官'];
        } else {
            $this->xiYongList['f']['y'] = ['正官', '七杀', '正财', '偏财', '食神', '伤官'];
            $this->xiYongList['f']['j'] = ['正印', '偏印', '比肩', '劫财'];
        }
        $res = [
            'minpan' => [
                'male' => $this->getMinPan($this->maleLunar),
                'female' => $this->getMinPan($this->femaleLunar),
            ],
            'xiangfu' => $this->getXiangFu(),
            'happy' => $this->getHappay(),
            'poxi' => $this->getPoxi(),
            'chugui' => $this->getChuGui(),
            'zinv' => $this->getZiNv(),
            'health' => $this->getHealth(),
            'guankou' => $this->getImportant(),
        ];
        return $res;
    }

    /**
     * 命盘
     * @param Ex $Lunar 对象
     * @return  array
     * @throws Exception
     */
    private function getMinPan($Lunar): array
    {
        $lunar = $Lunar->getLunarByBetween();
        $datTtime = $Lunar->dateTime->getTimestamp();
        // 性别 0男 1女
        $sex = $Lunar->sex;
        $jiNianMonthD = $lunar['jinian']['m'][1];
        $jiNianMonthT = $lunar['jinian']['m'][0];
        $jiNianDayT = $lunar['jinian']['d'][0];
        $arr = [
            '甲' => '甲木',
            '乙' => '乙木',
            '丙' => '丙火',
            '丁' => '丁火',
            '戊' => '戊土',
            '己' => '己土',
            '庚' => '庚金',
            '辛' => '辛金',
            '壬' => '壬水',
            '癸' => '癸水',
        ];
        $list = [
            '甲' => '甲木参天，脱胎要火，春不容金，秋不容土，火炽乘龙，水宕骑虎，地润天和，植立千古。',
            '乙' => '乙木虽柔，刲羊解牛，怀丁抱丙，跨凤乘猴，虚湿之地，骑马亦忧，藤罗系甲，可春可秋。',
            '丙' => '丙火猛烈，欺霜侮雪。能煅庚金，逢辛反怯。土众成慈。水猖显节，虎马犬乡，甲来成灭。',
            '丁' => '丁火柔中，内性昭融，抱乙而孝，合壬而忠，旺而不烈，衰而不穷，如有嫡母，可秋可冬。',
            '戊' => '戊土固重，既中且正，静翕动辟，万物司命，水润物生，火燥物病，若在艮坤，怕冲宜静。',
            '己' => '己土卑湿，中正蓄藏，不愁木盛，不畏水狂，火少火晦，金多金光，若要物旺，宜助宜帮。',
            '庚' => '庚金带煞，刚健为最，得水而清，得火而锐，土润则生，土干则脆，能赢甲兄，输于乙妹。',
            '辛' => '辛金软弱，温润而清，畏土之叠，乐水之盈，能扶社稷，能救生灵，热则喜母，寒则喜丁。',
            '壬' => '壬水通河，能泄金气，刚中之德，周流不滞，通根透癸，冲天奔地，化则有情，从则相济。',
            '癸' => '癸水至弱，达于天津，得龙而运，功化斯神，不愁火土，不论庚辛，合戊见火，化象斯真。',
        ];
        $tianGan = $Lunar->getArrayShuiMove(Calendar::TIAN_GAN, 6);
        $diZhi = $Lunar->getArrayShuiMove(Calendar::DI_ZHI, 8);
        $likeGod = $this->orginData['source'] == 'wnl' ? $Lunar->getLikeGod2() : $Lunar->getLikeGod();
        return [
            // 公历时间
            //'calendar'=>date('Y年m月d日',$Lunar->dateTime),
            // 阴历时间
            'lunar' => $lunar,
            // 本命
            'benming' => $arr[$jiNianDayT],
            // 天干十神
            'god' => $Lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $Lunar->_getGod(),
            // 纳音
            'na_yin' => $Lunar->getNayin(),
            // 地势
            'terrain' => $Lunar->getTerrain(),
            // 旺相休囚死
            'wxxqs' => $Lunar->getWxxqs(),
            // 喜用神
            'like_god' => $likeGod,
            // 胎元
            'fetus' => $tianGan[(array_search($jiNianMonthT, $tianGan) + 1) % 10] . $diZhi[(array_search($jiNianMonthD, $diZhi) + 3) % 12],
            'guaxiang' => $this->getGuaXiang($datTtime),
            'xingsu' => BaziExt::getXingSu($lunar['_nongli']['m'], $lunar['_nongli']['d']),
            'day_attr' => $jiNianDayT . $this->wuxing[$jiNianDayT], //日元
            // 日空
            'day_empty' => $Lunar->getEmptyDay(),
            // 生态  和  调侯
            'ecology' => $this->ecology[$jiNianMonthT . $jiNianMonthD],
            // 性格
            'xingge' => $this->getXingGe(implode('', $lunar['jinian']['d']), $sex),
            // 滴天髓
            'ditiansui' => $list[$lunar['jinian']['d'][0]],
        ];
    }

    /**
     * 性格解析
     * @param string $str 日柱
     * @param int $sex 性别 0男 1女
     * @return string
     */
    private function getXingGe(string $str, int $sex = 0): string
    {
        $list = [
            '甲子' => [
                '你的性格中有许多值得称道的特质，能够与伴侣和谐相处，理智地应对感情问题。在家庭中，你擅长缓解矛盾，减少冲突的发生。你在感情上偏于保守，不善于甜言蜜语，但一旦投入感情，便期望与对方携手一生。然而，在行羊的大运流年时，要警惕因财生灾或与有夫之妇的感情纠葛，以免结局不尽人意。',
                '你重感情，能设身处地为他人着想，对伴侣非常理解和体谅。同时，对方也给予你深切的关爱，甚至超过你对他的付出。但请注意，恋爱不宜过久，过长的恋爱期可能滋生变故。只要彼此坦诚相待，便有望获得美满的姻缘。在感情方面，你处理得游刃有余，因此你们的感情能够长久保鲜。在命理中，你属于拥有幸福婚姻的命运。',
            ],
            '乙丑' => [
                '乙木坐丑土，丑土为湿土能滋养乙木。你性格善良、勤奋，在理财方面有独到见解。外表温和，看似不温不火，但内心却具有暗黑潜质，有时会给人冷漠、难以接近的感觉，甚至被误认为城府深沉。这种对立性格可能导致极端行为，好时极度疼爱对方，不好时则擅长冷暴力。',
                '在选择伴侣时，你可能更看重对方的经济条件，喜欢管束对方。然而你的性格并非强势类型，若对方同样脾气暴躁，你可能难以驾驭。感情生活中，过度的管束可能引起伴侣的不满。给予彼此适当的空间有助于感情发展，对你们的关系更有益。',
            ],
            '丙寅' => [
                '你思维敏捷，应变能力强，主意多但为人本分善良。在选择伴侣时往往通过亲友介绍认识，在相互理解的过程中一旦认定对方就会尽快稳定关系。然而过长的恋爱期可能滋生变故，婚后要注意长辈对婚姻生活的干涉可能导致的矛盾。学会互相理解和体谅是维系婚姻的关键。',
                '你给人的印象通常是憨厚老实但实际上聪明过人清楚自己的需求。在选择伴侣时你更看重有经济头脑或专业技能的人。在感情生活中你渴望得到伴侣的关心和爱护但过多的干涉会让你感到烦躁和束缚。建议学会接受和适应伴侣的关心方式以维护双方的感情。',
            ],
            '丁卯' => [
                '你性格憨厚善良与长辈相处融洽因此多得长辈帮助。在感情上容易吸引心理成熟的女性因此与年龄稍大或思想成熟的伴侣相处融洽。你重视家庭生活偏袒母亲因此伴侣需要学会与长辈相处之道以维护家庭和谐。年月中木火较旺的话申酉时定能富贵但可能晚婚早年恋爱多难成。',
                '女命坐下为印与长辈沟通顺畅关系和谐能得到长辈帮助内心充实有文化涵养。若命中食伤不重则个性大大咧咧不拘小节不记仇是感情中的好伴侣。若月份或时辰带壬或亥则与伴侣感情深厚。',
            ],
            '戊辰' => [
                '你有天赋和才能婚后生活会逐渐富足。你的伴侣个性温柔婉约感情生活相对美满尤其是癸丑时出生的你们更是难舍难分。你喜欢听好话否则情绪会受影响变得顽固不讲理因此伴侣不宜过于耿直以免感情生活不宁。生于寅卯时的人较难管教容易闯祸；壬戌时也不佳虽大气但婚姻不稳；癸亥时则异性缘佳需注意婚后收敛自己。',
                '你为人稳重处事圆滑对自己有严格要求富有上进心总体来说是个踏实稳健的人。在感情中你不容易吃醋对伴侣有信心但有时多疑和猜忌会让自己自讨苦吃。若其他柱上官杀不多则伴侣通常是恋家的人喜欢呆在家里感情生活相对和美。',
            ],
            '己巳' => [
                '你个性温和善良富有同情心穿着打扮随性不喜欢抢风头但缺乏辨别虚伪的能力容易轻信别人在恋爱过程中可能遭遇欺骗。你是很好的倾听者喜欢事业心强的女性但在婚恋道路上可能会遇到与母亲发生矛盾的伴侣需特别注意处理婆媳关系以维护家庭安宁。',
                '己土坐巳火火生土己土能源充足你精力充沛活力四射。你在优雅文静的生活环境中成长造就了温和善良的性格不喜欢争抢具有包容心与长辈合得来亲和力强。在感情中你通常是妥协的一方与婆婆相处融洽但渴望更多自由。若希望感情和美需平衡好自由与束缚的关系避免因此与伴侣发生矛盾。',
            ],
            '庚午' => [
                '你对事物持淡然态度不会主动为难别人这种大胆对你的事业和感情都有积极影响。你的感情生活多姿多彩充满冲动和热情但可能让伴侣缺乏安全感感情易变。若想在激情与安稳中找到平衡点需稍微收敛自己的大胆行为这样对你的感情和婚姻更有益。',
                '你有强烈的事业心为人正直处事公道身边多结交有能力事业有成的成功人士选择伴侣的标准也类似。这可能让你的眼界变得更高在感情中容易出现恋爱难定婚姻动摇的迹象尤其容易吸引放纵浪荡的人。因此你需要找到一个能动能静平衡得很好的伴侣。',
            ],
            '辛未' => [
                '你心性宽厚仁义但内心固执倔强具有强烈的自我保护意识在某些环境下会有支配或领导他人的意识。这种性格对事业有利但在感情中可能导致问题。你不喜欢表达感情也不愿敞开心扉自我保护意识过强让伴侣无法了解你内心想法缺乏安全感从而引发矛盾。',
                '你身具许多优点但凡事过于理想化与现实常有出入。在感情方面你眼光较高婚姻不易成恋爱道路坎坷。你希望恋情或婚姻符合自己理想的样子但在相处过程中需要敞开心扉多沟通让伴侣感受你的想法这样感情才能更顺利发展。',
            ],
            '壬申' => [
                '你内心充满矛盾，既追求清高的自我满足，又渴望被他人理解，情感世界丰富而复杂。在感情上，你可能历经千辛万苦，最终牵手的人却并不如你所愿。恋爱时，建议避免轻率提及分手。你深谙人情世故，但有时过于固执己见，难以妥协。若能稍微放宽标准，以平常心看待伴侣，感情生活将更加和谐。',
                '你天赋聪颖，性格温和，但内心坚韧不拔。这种特质让你在异性中颇受欢迎，拥有良好的异性缘。婚后，你将得到配偶的支持与关爱，但也可能因为误解而忽略对方的好意，导致幸福感降低。学会感恩与满足，与伴侣建立亲密关系，将提升你的幸福感。',
            ],
            '癸酉' => [
                '你自尊心强烈，但在决策时往往犹豫不决，容易在感情上陷入纠结。你适合长期恋爱，但面对婚姻可能显得不太适应。一旦与心仪对象确定关系，你会全心投入，但婚后生活可能因琐事而产生抱怨，影响运势。建议寻找性格互补的伴侣，以包容和理解为基础，让爱情持久保鲜。',
                '你自尊心强且易犹豫不决，这种性格在感情上可能导致你失去某些珍贵的东西。你擅长长期恋爱，但对于婚姻生活可能感到挑战。婚后，尽管有时会遇到不顺，但你仍能为伴侣带来助益。若能勤俭持家，知足常乐，晚年将享有幸福。',
            ],
            '甲戌' => [
                '你重视金钱且节俭，但不贪婪，追求事事完美。在面对重大事件时，你可能会因处理不当而影响情绪，甚至将负面情绪发泄给亲近的人。在感情生活中，这种情况可能导致与伴侣产生矛盾。建议调整自己的期望和要求，避免迁怒于他人，特别是家人。你与妻子或女儿关系紧密，能在她们的帮助下更好地处理人际关系。',
                '你与伴侣可能通过介绍相识并结婚，这对你的感情生活来说可能是个不错的选择。然而，你在生活中倾向于控制对方，对伴侣的猜疑可能引发争吵。同时，金钱问题也可能成为你们之间的敏感点。为了减少不必要的矛盾，建议你在金钱方面保持更开放的心态。',
            ],
            '乙亥' => [
                '你外表随和，但内心有些许懒惰，对他人依赖较强。理想伴侣是体贴、付出并能助你一臂之力的女性。然而，现实中你可能更吸引那些具有母性情怀、喜欢照顾人但有时也喜欢指责别人的女性。尽管你们之间会有小争执，但你的宽容和健忘能让关系持久。需留意的是，你的伴侣可能与你母亲有较大矛盾，需要你巧妙平衡。', '你心地善良，生活态度随性，开心就好。若命中印星多，你会更宽容但也可能更懒。在感情中，你适合找一个能照顾你的伴侣，因为她会是个操心的人，喜欢管理家庭琐事。只要房子不缺，你们的日子就能过得安稳。家庭和亲情对你非常重要，常请长辈来家聚会能增进家庭氛围并可能带来好处。',],
            '丙子' => [
                '你自律严谨，从小就是听话乖顺的孩子。异性缘佳，吸引正直善良的伴侣。婚后可能受桃花干扰，需与其他异性保持适当距离，以维护婚姻稳定。', '你容易吸引男性的追求，性格正直且习惯依赖伴侣。感情生活中，你习惯被约束和管制，即使有时感到不适。你会为家庭付出很多，是个非常贤惠的女性。',],
            '丁丑' => [
                '你追求精神层面的满足，注重感受，对感情不太看重世俗规矩。一生感情经历丰富，婚后夫妻感情好，但异性缘更佳。若八字中官印轻或无，婚姻可能因过分追求自由而不稳定。', '你注重感受，享受生活，花钱大方，追求有情调的生活方式。由于日坐食神克七杀，感情方面可能一波三折。建议平常心对待，降低要求，以平淡生活为好。',],
            '戊寅' => [
                '你有理想有抱负，保守且固执，从不示弱。在感情上习惯强势，让对方感到难以沟通。需要寻找能理解、包容你的伴侣。若生在申月、申时，可能因妻子脾气不佳而影响婚姻稳定。', '你做人做事有分寸，对弱者有同情心，风趣优雅且能吃苦耐劳。在感情中既能独立思考也能听取意见，与伴侣感情深厚且为对方着想。感情之路顺遂，配偶优良但可能有婚恋史。',],
            '己卯' => [
                '你外表出众，追求者众多，但选择的伴侣可能脾气不佳。你的感情幻想和追求可能受到对方批评和嘲讽，导致矛盾产生。建议收敛感情，避免随意撩拨异性以保持生活平静。', '你的女性魅力吸引众多异性青睐和保护欲望强烈的伴侣。在选择伴侣时需擦亮眼睛并做好考察以避免专制思想的丈夫。你工作能力出色且具有女性气质受同事欣赏，容易与有工作关系的异性结婚但需避免草率决定。',],
            '庚辰' => [
                '你性格中有魁罡刚强的一面给人难以相处的感觉但实际上你尊敬老人爱护小孩为人谦虚谨慎懂得体贴她人有人情味。这样的性格在感情中会疼惜另一半且居家意识强喜欢幻想和浪漫懂得珍爱伴侣因此两人感情通常非常好。', '你天生聪慧但不喜出风头喜欢把事情藏在心里实际上很懂得体贴她人为他人着想只是不善表达而已。这样的性格在感情中也会疼惜另一半与伴侣的感情相对来说会比较好。建议不要遇事闷在心里及时沟通解决矛盾无论是生活还是感情上的问题。',],
            '辛巳' => [
                '你思维敏捷性格开朗重情义且非常看重与他人之间的感情对情感有一种特殊的依赖性会主动向伴侣示好。一旦确定恋爱关系或婚姻关系就会对伴侣非常好是个居家意识强不会沾染烂桃花的传统类型因此感情也会相对和谐。', '你敏感而重情义对感情的依赖和服从让你经常处于被动状态缺乏主动性因此在感情中你经常是默默付出的那一个。你本质上很传统是个居家型女人对暧昧很排斥因此不会让自己有机会去沾染外面的桃花。建议不要在感情生活中太压抑自己适当的时候主动一点会有不错的事情发生。',],
            '壬午' => [
                '你性情不定时而温顺时而神经过敏因此经常自讨苦吃。桃花做宅落在夫妻宫代表桃花旺盛夫妻恩爱感情美满但需小心招惹烂桃花导致感情或婚姻受灾。', '你重情重义忠于家庭与伴侣感情较好但缺乏防备心容易被骗需小心谨慎。若是结婚能旺家宅晚年旺子但桃花偏旺需注意言行举止避免暧昧以伤害感情生活。',],
            '癸未' => [
                '你在感情中通常属于弱势一方经常被对方管制但其实心里明白对方大多为自己着想。生活平淡带点刺激吵吵闹闹不可避免但重要的是你的态度通常你会先败下阵来。', '你是个有才能的人心思敏捷才华横溢对男性有奉献的诚心但内心不定容易受外界影响改变主意有时会为感情问题劳心劳力。你在家中贤惠勤俭持家但在感情中较难找到真正喜欢的人因此常退而求其次付出较多处于被领导被管制的地位。',],
            '甲申' => [
                '你一表人才内心隐藏叛逆情绪而你的伴侣自我意识可能比你更强外表出众内心独立自我。在感情生活中需要有人先服软做好准备挨骂挨打否则双方会交战不休影响感情。', '你自身条件好外表出众阳光活泼气质豪爽吸引性格叛逆脾气不太好的异性。若能接受伴侣的强势性格则相安无事否则两虎相斗必有一伤感情生活难以平静。',],
            '乙酉' => [
                '你警戒心强对感情异常敏感常有莫名危机感和不信任感导致感情不稳定。你性格内向多疑小心眼记仇容易使用“冷暴力”导致生活经常闹腾伴侣可能因此闹分手或离婚。', '你对待感情敏感且重视内向多疑缺乏安全感容易引起男性保护欲吸引他们接近。在感情中你非常依赖对方但又经常疑神疑鬼怀疑对方这容易引起对方的不满和裂痕因此需要包容性强的异性来走进你的内心。',],
            '丙戌' => [
                '你性格好喜欢交友对伴侣体贴乐于付出讲究情调追求心灵感受因此选择的伴侣形象不会太差。在感情中你容易投入不会轻易认输一旦发生争执可能固执己见毫不妥协。建议稍微收敛倔脾气以利感情发展。', '你待人热情为对方着想但有时过度热情让对方不自在甚至害怕。你有些才气表面处事圆滑随和但内心固执不服输在感情中不会让自己太吃亏。若投入感情没人会质疑你的真心但对伴侣的爱过多或管束过分可能是感情杀手需注意平衡。',],
            '丁亥' => [
                '你事业心强，对家庭也极为重视，是个家庭观念深重的人。尽管你有追求，但私心也较重，为人偏向保守。这可能导致你在事业与家庭间产生矛盾，影响事业发展。然而，在感情方面，你表现出色，是个值得信赖的伴侣。你的感情生活和谐美满，少有烂桃花的困扰。你重视家庭，对另一半关怀备至，让对方感到安心。', '你对待感情慎重而专一，虽然追求事业，但始终把家庭放在首位。你恋家，甚至可能为了家庭放弃事业发展。在感情中，你特别重视伴侣，夫妻关系和睦恩爱。你展现出典型的传统女性特质。但请注意，若你出生在土月或土时，个性可能变得叛逆，对伴侣的管束也可能加强。',],
            '戊子' => [
                '你一生中能得到长辈的赏识，异性缘极佳，桃花旺盛，从不缺乏异性的追求。由于癸水藏气，你与戊土、子水之间有着深厚的夫妻情谊，因此你脾气温和，注重家庭和感情生活。你善于照顾另一半，珍惜家庭和感情，即使在面对矛盾时也能冷静处理，寻求解决方法。', '你是个非常注重家庭和亲情的人，总是愿意为家人和伴侣付出一切。你敏感细腻，善于察觉别人的需求和感受，因此在人际关系中也非常受欢迎。在处理感情问题时，你通常能够保持冷静和理智，不会因为一时冲动而做出不理智的决定。你的感情生活平稳和谐，充满了温暖和幸福。',],
            '己丑' => [
                '你个性沉稳，处事有条不紊，总是考虑周全。面对挫折，你不会轻易放弃，具有强大的内心力量。你平凡的外表下隐藏着巨大的潜能，有时能一鸣惊人。在处理感情时，你细腻且专注，情商高，能吸引到节俭持家的女性。你的感情生活平稳而持久。', '你性格沉稳可靠，是值得信赖的伴侣。你办事有条理，不慌不忙，既不自负也不易气馁。然而，在感情方面，你可能会遇到一些困扰。由于夫宫有己土比肩的影响，你在恋爱或婚姻中可能会面临感情上的竞争和挑战。建议你保持冷静和理智，多沟通理解对方的需求和感受，以维护感情的稳定。',],
            '庚寅' => [
                '你天资聪明但性格极端，要么温和懂人情，要么冷漠无情且占有欲强。在感情上，你也表现出两个极端：要么与伴侣和谐共度一生，要么感情生活充满争执。建议你收敛脾气，对伴侣要求不要过高，给彼此一些空间和自由，反而对感情更有益。', '你性格直率爽朗，缺乏感性情绪，有时表现得像男生一样。你在感情中比较强势，需要居家型男人来互补。建议你多关心对方感受，避免得理不饶人导致感情不和。学会让步和包容对你们的感情会有很大帮助。',],
            '辛卯' => [
                '你有能力有想法但自尊心极强，可能导致脾气不太好且说话直接。在感情中容易得罪人而不自知，也不太会关心另一半的需求和感受。这可能导致感情出现裂痕。建议你多理解对方感受并表达关心和爱意来维护感情的稳定。', '你性格泼辣直率并勇于争取自己想要的爱情和生活品质。你容易吸引到气质好、身材佳的异性并与之发生感情纠葛。建议你明确自己的感情需求和期望并寻找合适的伴侣来共同创造美好的生活。',],
            '壬辰' => [
                '你性格豪爽勇敢且独立性强，面对困难能迎难而上并接受他人意见但不喜欢被指手画脚。在感情中比较专制和大男子主义倾向适合找脾气温和且顺从的女性为伴，以避免感情中的隐患和冲突。', '你是个勇敢直接且气度不凡的女性，在感情中表现出强烈的独立性和专制倾向。你喜欢掌控一切并希望伴侣顺从自己的意愿，这可能导致感情生活充满挑战和变数。建议你学会尊重和理解伴侣的需求和感受以维护感情的稳定。',],
            '癸巳' => [
                '你个性温和敏感，善于察觉身边事物并考虑周到，情商相对较高且偏向感性。在感情中你能体贴入微地与伴侣沟通交流并避免大男子主义倾向，因此感情生活相对稳定和谐。你的另一半可能比较独立自强且家庭条件不错能在一定程度上帮助到你。', '你性情温柔感性且注重细节，在处理感情和家务事方面表现出色。你对伴侣偶尔撒娇任性希望得到认可和赞美，并容易找到处事冷静或性格好静的男性为夫。两人宜有年龄差距以日久弥坚地维系感情。',],
            '甲午' => [
                '你聪明灵秀但略带清高和傲气，有才华且学习新事物进展迅速，口才佳并善于表达自己。在感情中你直接坦率并体贴伴侣，但婚后可能面临妻子与母亲关系不和的问题需要你在中间起到调解作用以维护家庭和谐。', '你内心傲气并喜欢指导他人导致脾气不太好，在家中容易得理不饶人特别是对有对象之后更是如此。适合你的是能够包容迁就你的伴侣特别是走官印运时更易找到年龄比你小的男人为夫，建议在感情中收敛脾气特别是不要把最坏的一面留给最亲密的人。',],
            '乙未' => [
                '你对另一半的家境有较高要求若财多则可能是上门女婿或到女方家乡发展，在感情中你处于较强势一方喜欢管束对方并多有挑剔，容易吸引到婚恋史丰富的女性且家中常有小摩擦需要宽容和沟通来化解。', '你缺乏安全感并对另一半看得很紧喜欢黏在一起说教，但往往能找到经济条件不错的对象并容易受到感情困扰，看重钱财若能向钱看齐则能减轻伤痛，需要深入沟通让对方明白你的想法并避免过度管制以保持感情稳定。',],
            '丙申' => [
                '你重视钱财但也讲究浪漫诗意，若身强则喜欢控制他人按自己意愿办事但若有水克丙申则反而处于弱势一方，你的感情生活相对稳定没有大风大浪对感情影响不大。', '你拥有许多知心朋友并分享事迹但在感情方面可能一波三折甚至恋上有妇之夫，精神享受对你最重要但若局中有不利因素则可能影响感情和财运，需要找到具备经济能力和浪漫情怀的男士为伴并注意维护感情稳定。',],
            '丁酉' => [
                '你独立性强且注重大局观会为追求更好生活而努力，在找对象时看重对方经济条件但忌身旺以免给人不舒服感觉导致自私占有欲强，若身弱则聪明淡泊名利对感情有利，身旺则对另一半管束较多需配合弱势伴侣以维护感情稳定。', '你在选择伴侣时看重经济条件但若月时支五行为水则懂得付出并舍得为男人事业花费对钱财看轻些，长相漂亮对对象要求高但在感情中要避免过高要求以免产生怨言影响双方关系。',],
            '戊戌' => [
                '你追求完美，对自己要求严格，对他人却宽容理解，能顾及他人感受。但在感情中，你可能会因对伴侣的期望过高而产生不满，甚至有时会觉得遇见的其他人比自己的伴侣更优秀。这种心态可能导致你在感情上的不忠。然而，这种情况需结合整体命局和大运流年来判断。建议你珍惜眼前的伴侣，不要过分比较和挑剔。', '你对伴侣的要求可能过高，容易对感情产生不满。你希望感情能如你所愿，却忽略了实际情况，这可能对伴侣造成无形的压力。长期下去，你们之间的感情可能会受到影响。因此，建议你适当调整对感情和婚姻的期望，学会在生活中制造激情和新鲜感，这对你们的感情会更有益。',],
            '己亥' => [
                '你做事认真，待人有礼，这种性格容易吸引异性的注意，给你的感情生活带来好运。你对生活要求不高，容易满足，对感情也顺其自然，不会过分苛求。因此，你的感情生活通常平淡而幸福。', '你性格活泼可爱，充满幻想和进取心，这种性格很吸引异性。在感情中，你很容易满足，所以与伴侣的感情往往很好。你的伴侣可能是个处事冷静、生活节俭、有思想深度的人，与你非常相配。你们可能会一起过上幸福安稳的生活。',],
            '庚子' => [
                '你有强烈的自尊心，希望在家庭或群体中占据主导地位。这种性格可能导致你在感情中过于强势。如果伴侣性格软弱，你们的关系可能较平稳；但如果伴侣也强势，你们的感情可能会不稳定。建议你在感情中学会迁就和包容，降低自己的要求。', '你的自尊心非常强，对异性的要求也很苛刻。这可能导致你的伴侣对你产生不满，影响你们之间的感情。如果身旺，你可能喜欢批评他人而无法接受别人的意见；如果身弱，你可能多疑心、挑剔。建议你多听取别人的意见，关心伴侣的感受，这对增进你们的感情有好处。',],
            '辛丑' => [
                '你性格乐观随和，没有太大的野心，追求平静安稳的生活。你是个很恋家的人，对感情非常忠诚。在与伴侣相处时，你唯一的问题可能是猜疑心过重。建议你给伴侣多一些信任和包容，这样你们的感情才能更长久。', '你性格安静沉稳，追求平稳的生活，没有太大的追求。你非常注重家庭生活，总是把家打理得井井有条，让伴侣感受到家的温暖。只要保持对伴侣的信任和包容，你们的感情就能长久发展。',],
            '壬寅' => [
                '你心直口快，性格中有些阴霾之气，遇事容易急躁。这种性格可能导致你在感情中错失良机或付出更多。建议你学会控制情绪，对伴侣多一些包容和迁就。', '你个性独立，有才能和想法，对伴侣的要求也比较高。你希望伴侣能够独立、有想法并关心自己。在这段感情中，你可能会付出很多但也要注意平衡双方的关系。',],
            '癸卯' => [
                '你举止优雅、气质不凡、待人亲切这些特质让你有很好的异性缘。在感情中你对伴侣非常好且专一但如果食神过重可能会有出轨的情况。建议你珍惜眼前的感情不要轻易背叛。', '你给人的感觉很亲切具备了亲和力因此人缘很好异性也喜欢与你相处。在感情中你会真诚地对待伴侣并乐于分享自己的快乐。你们的感情会在相处中逐渐升温并且你对伴侣非常专一。但如果食神过重则要注意可能会有出轨的风险。',],
            '甲辰' => [
                '你涉猎广泛、慷慨风流、多情且能力强喜欢与有权有势的人打交道。这些特质让你在感情关系中占据主导地位是强势的一方。另外你很有可能不是妻子的初恋。', '由于辰是辛金之墓因此你遇到的人可能能力不足或年纪较小。在恋爱中你可能会有倒贴的倾向但在家庭地位中你仍然会占据领导地位。其实你更适合找年龄较大的男性作为伴侣但在实际中你却更容易钟情于年龄较小的男生。',],
            '乙巳' => [
                '伤官正官交织，思维跳跃难稳定，易显傲慢之态。心思细腻且聪慧，才华横溢之人也。情感敏锐，偏好年轻貌美、活泼灵动之女子，一旦倾心便疼爱备至，甘愿付出所有。然须防异性之诱惑，自我控制至关重要。', '对伴侣深情款款，非凌驾其上之强势爱意，乃小鸟依人之柔情。竭尽所能以求伴侣之宠爱，却常感安全感不足。默契交流乃二人关系之基石，需避免自以为是，注意言辞艺术及交友之道。',],
            '丙午' => [
                '天生热情如火，性急风风火火，大男子主义倾向明显。伴侣性格亦强，各持己见，难免摩擦争执。建议多一份耐心，倾听对方心声。异地恋或许更适合你！', '感情中你争强好胜，家中易与伴侣起口舌之争。若双方性格皆刚，争吵难免。平衡感情至关重要，否则难以顺利。命中印星重则通情达理、涵养深厚。建议平稳沟通，或尝试“距离产生美”之恋爱模式。',],
            '丁未' => [
                '个性急躁易怒，抨击异见者，攻击性强且胆大妄为。然秋冬生人则朴实能干，常有独到见解。感情中呵护伴侣，擅长厨艺，但或因事业等原因分居两地。', '你独立自强，习惯独当一面。若命中水土不旺，伴侣难成依靠，需自立更生。冲动型思维，追求时尚不甘落后。感情中女强人形象让伴侣难以捉摸，感情路略显坎坷。',],
            '戊申' => [
                '性格开朗思维快，主见强烈处事稳。理想伴侣应大气端庄、上得厅堂下得厨房。你在感情中对伴侣极好，几乎无争吵之事。', '你文静秀气内心善，处事稳重有原则。若厨艺佳更易赢得伴侣心。感情中你较被动，多经人介绍结识伴侣。注意恋爱不宜过长以免感情淡化。你对伴侣体贴入微鲜少争吵。',],
            '己酉' => [
                '你热爱生活，对美食有独特的鉴赏力，并追求高品质的生活方式。你浪漫多情，因此桃花运旺盛，容易在情感上冲动行事。你的伴侣同样优雅且富有生活情趣，能为你提供多方面的支持。为了维护感情的稳定，你需要谨慎处理周围的异性关系，坚定自己的选择。',
                '你是一位追求生活品味的雅致之士，机智而乐观，自然吸引着众多倾慕者。在感情世界里，通过他人介绍认识的伴侣更可能与你缔结美满姻缘。相比之下，自己直接结识的对象往往难以修成正果。你倾向于平和的沟通方式，即使面对情感问题也能保持冷静，通过理智的交谈来寻求解决之道。然而，仍需警惕身边的桃花，以免给稳定的感情关系带来不必要的风险。',
            ],
            '庚戌' => [
                '你比较知足常乐，不爱名利，有着思想上的追求，喜欢一些宗教或是书画技艺类的传统文化，这样性格的人自然会吸引异性的目光。在感情上你可能也会在大多数的时候沉浸在自己的小世界当中，会给对方一种神秘感甚至是看不透的感觉，这样有利有弊，真正喜欢你的人会因为这个而更加爱你，不够喜欢你的人会日渐疲惫。但从婚姻的角度上来说，还是很美好的，能够与另一半有美好的姻缘。', '你是一个比较容易满足的人，追求是的思想上的升华，对于名利反而不太看重，对于传统文化的一些技艺却能引起你很大的兴趣，在感情中，你能够吸引到许多异性的目光，但容易沉迷在自己的世界中自得其乐，而忽略他人，对于志同道合的人来说，自然能够有着共同的话题和追求，但对于其他人来说会相处之间的交流越来越少，而导致感情淡泊。但从婚姻的角度上来说，还是很美好的，能够与另一半有美好的姻缘。',],
            '辛亥' => [
                '你聪明，给人一种聪明儒雅的感觉，你很敏感，也很知性。对于任何事情你都不以为苦，有付出的精神，在感情中，你对于你的另一半很好，对方能够感受到你的爱意，你能够在各方面都帮助到你的另一半，是个完美的恋人以及爱人。建议你在感情中能够多多与另一半沟通，有的时候你会给人太傲的感觉，你的另一半可能不太能够看懂你的心。', '你敏感而知性，在为人处世中能够照顾到他人的感受，也有为他人付出的精神，在与人相处中会给人以亲切的感觉，在感情中，你对于你的另一半很体贴，在许多的方面都能够帮助带对方，让对方能够感受到你的关心爱护，是个完美的恋人以及爱人。在感情交往中能够多多与另一半沟通交流，有的时候你会给人太过于淡然的感觉，你的另一半可能不太能够看懂你的心。',],
            '壬子' => [
                '你有着强烈的自信心，在许多方面都会自信心满满，也会因为这个而莽撞行事，对于事情多余懈怠，从而造成行事上的失败。感情方面上，你经常会忙碌辛劳却徒劳无功，很多时候会觉得有心无力，所以，在感情方面有着诸多的波折，任重而道远，需要正确地看待自身，清楚地理解自己身上的优缺点，认清什么是自信而什么是自负，过度的自信就自负了。', '你在很多地方的表现都是很自信的，但也常常因为这个而轻举妄动，造成行事上的失败，容易怠慢。感情方面可能会让你有劳心劳力的感觉，很多时候会让你有心无力，因此，你的感情道路可谓艰难。建议正确看待自己的优缺点，自信是可以但是过分自信甚至自负就不太好了。',],
            '癸丑' => [
                '你心思活泼，有着很强的推理能力，能够见微知著，在处事圆滑，长袖善舞，懂得见什么人说什么话，所以能够结交到许多的朋友，在你圆滑的外表下，内心却无比的固执，有着却对不能触碰的底线，在感情生活中，你的情感则易生波折，你的另一半通常都是有熟人帮忙介绍的，若是恋爱期过长，则容易出现问题，你的另一半在许多方面都能帮助到你，你们之间的感情也会非常的甜蜜。',
                '你有先见之明，有很强的生活能力，特别善于社交，懂得见什么人说什么话，你表面上很圆滑内心中却有别人不能碰触的，并且自己不会让步的底线，有个顽固的性子。这样性子的人在恋爱道路上可能不会那么的顺利，因此你不适合爱情长跑。最好是与经由别人介绍认识的异性结婚。你容易嫁给能够帮助自己的丈夫，与之感情也是非常好的。',
            ],
            '甲寅' => [
                '你性格大胆且性急，容易受冲动驱使，但若身体条件稍弱，脾气会相对温和。在感情上，你更适合寻找能包容你性格的伴侣，这样的人往往并非你的初恋，而可能是有过感情经历的女性。你们志同道合，兴趣相投，但由于个性鲜明，也可能产生不少争执。', '你属于行事冲动的类型，风风火火，不考虑后果。若身体条件较弱，则能更加控制脾气，少发火。在感情上，你需要一个能包容你冲动性格的伴侣，对方需要具备良好的耐心和温和的性情。你在感情中占据主导地位，需要引导对方，只有相互扶持，你们的感情才能走得更远。',],
            '乙卯' => [
                '你追求个性，随机应变，不受传统束缚，但内心深处仍渴望他人的理解和安慰。由于妻宫是比肩，感情路可能不太顺畅。你与伴侣多通过介绍相识，初期志同道合，话题共鸣，但随时间推移，争执可能增多。因此，在感情中学会适时退让至关重要。', '你个性鲜明，吸引众人目光。然而，你也有温柔的一面，处事精明多变，不拘泥于成规。夫宫是比肩，意味着丈夫能在一定程度上给予你帮助。你容易晚婚，且需防范第三者介入你的感情生活。',],
            '丙辰' => [
                '你性格朴实、善良、内心强大，目标清晰且充满希望，能克服任何困难，赢得众多人的帮助和赞赏。在感情上，你擅长处理两人间的问题，全心全意为伴侣付出，甚至超越对方的爱。你们相处和谐，互相理解、迁就。', '你内心丰富，依靠强大的内心世界支撑自己。你善良、有才华，能吸引他人的注意。性格朴实可爱，情商高，处理感情游刃有余。你们彼此深爱对方，相互迁就，你的付出甚至超越伴侣对你的爱。',],
            '丁巳' => [
                '你心直口快，直率坦诚，但言辞可能让人尴尬或反感。在感情上，你的冲动个性可能引发诸多麻烦，一时的口无遮拦可能导致伴侣的不满和争吵。建议三思而后行，小心祸从口出。', '你个性单纯、坚强、直率，喜欢直接表达想法，不顾及他人感受，容易相信别人且缺乏防范心理。在感情上，你可能因冲动和固执己见而遭遇困难。当双方意见不合时，争吵频发。建议多思考、少冲动，以避免不必要的冲突。',],
            '戊午' => [
                '你自尊心强烈，喜欢领导他人，具备坚定的勇气。在事业上可能是领导者，在感情中也会表现出强势的一面。虽然与伴侣有和睦相处的时候，但大男子主义可能成为感情隐患。建议多听取对方意见，学会迁就和包容。', '你个性要强，具备坚持到底的勇气。在事业中可能是领导者，但在感情中过于强势，习惯指导伴侣。若对方不顺从，你可能感到自尊心受损。建议在日常相处中多沟通、多包容对方，以维护感情的长久发展。',],
            '己未' => [
                '你性格理智且实际，善于精打细算，尤其在金钱方面十分看重。在选择伴侣时，你会首先关注对方的家庭背景和经济条件，认为稳固的经济基础是感情发展的前提。然而，这种过分看重物质的态度可能会导致感情生活的不和谐，增加日常摩擦和争吵。建议你在感情中不要过分追求金钱，而是平衡物质与情感的关系。', '你倾向于控制伴侣，并且对经济利益非常看重。在选择感情对象时，经济条件往往成为你的首要考量。这种以物质为重的态度可能会让你忽略真正的情感需求，导致感情生活的不稳定和不和谐。建议你在追求感情时，不要只看重金钱，而是更多地关注彼此的情感连接和共同价值观。',],
            '庚申' => [
                '你渴望成名，对名利有着强烈的追求，但往往面临机遇上的挑战。这种怀才不遇的心态可能会让你显得烦躁和心浮气躁，难以与他人亲近。然而，在感情方面，你却展现出完全不同的一面，对伴侣温柔体贴，感情和谐融洽。但请注意，在心情烦闷时，你的态度可能会对伴侣造成伤害。建议你在面对挑战时保持冷静和耐心，以维护感情的稳定。', '你急于求成，总渴望一举成名，但往往因缺乏机遇而感到郁闷。这种急功近利的心态可能会影响你的人际关系，让你显得难以亲近。然而，在感情方面，你却能给予伴侣深厚的关爱和支持，感情十分和谐。但请注意，在情绪低落时，要避免将负面情绪波及到伴侣身上。学会平衡自己的心态，以更好地维护感情。',],
            '辛酉' => [
                '你思想传统且保守，做事有条不紊，但不喜欢受到他人的约束。这种性格可能导致你与他人发生矛盾和冲突。在感情上，你属于反应较迟钝的类型，对感情变化不够敏感，可能需要伴侣更多地包容你的不成熟行为。这会对你们的感情造成一定的威胁，使感情之路充满挑战。建议你在日常生活中更加关心伴侣的需求和感受，努力成熟起来以维护感情的稳定。', '你个性固执且急躁，喜欢按照自己的方式行事，但有时会显得过于死板。在感情上，你对伴侣的依赖程度不高，开窍较晚，思想成熟也相对较慢。这可能会让伴侣觉得需要花费更多耐心来包容你的孩子气行为。长时间下去容易造成感情上的潜在危机和波折。建议你在平时更加关注伴侣的需求和感受，努力提升自己的成熟度以维系稳定的感情关系。',],
            '壬戌' => [
                '你性格活泼开朗且善良正直，在人群中总能起到调节气氛的作用。然而在你外向的外表下隐藏着一颗固执的心。幸运的是你的伴侣通常都温柔体贴、善解人意且具有良好脾气能够包容你。因此尽管在感情中偶尔会有委屈的时刻但由于对方的包容和理解你们之间的感情依然能够保持和谐稳定。', '你外表开朗内心却十分固执己见但幸运的是你的理想伴侣是一位仁厚温和且处事稳重的人。在感情上即使偶尔钻牛角尖对方也能够以平和的态度理解并安抚你使得双方之间不会有太大的波折。你们之间的感情非常深厚能够一直保持和谐稳定的状态相敬如宾地共度每一个美好时光。',],
            '癸亥' => [
                '你天生聪慧且性格温和通情达理但又带有一丝倔强。在生活中你表现得比较强势喜欢掌控一切而你的伴侣往往是你自己主动结识并相恋的。你渴望浪漫的爱情因此你的另一半通常是性情单纯且富有浪漫情趣的女性。你们的感情基础相对较好但需要注意的是你有时可能会情绪化这可能会对你们的稳定关系造成一定影响。', '你心思细腻且聪慧过人善于处理人际关系和把握分寸。在选择伴侣时你有着自己的标准和主见因此你的另一半常常是你自己主动追求的。然而感情的道路并非一帆风顺可能会遇到诸多困难和挑战需要你们共同面对。你有时容易情绪化这可能会成为你们感情中的隐患需要特别注意和改进。',],

        ];
        return $list[$str][$sex] ?? $list['甲子'][$sex];
    }

    /**
     * 幸福指数
     * @return array
     * @throws Exception
     */
    protected function getHappay(): array
    {
        // 相邻
        $list2 = ['y' => ['m'], 'm' => ['y', 'd'], 'd' => ['m', 'h'], 'h' => ['y']];
        $list = [
            [5, '夫贵妻荣，天长地久'],
            [4.5, '夫贵妻荣，多有矛盾'], //1
            [4, '共同奋斗，白头偕老'],
            [3.5, '互敬互爱，感情很好'], //3
            [3, '看是美满，实则虚幻'], //4
            [2.5, '意志不坚，感情不稳'],
            [2, '婚姻失衡，其一受制'],
        ];
        // 克制表
        $kezhi = $this->kezhi;
        //        $list2 = ['year' => 'month', 'month' => 'day', 'day' => 'hour', 'hour' => 'year'];
        $res = [3, '不吉的婚姻'];
        $mWd = $this->xiYongList['ge'][0];
        $fWd = $this->xiYongList['ge'][1];
        // 先判断正官和七杀在哪个柱上
        $pos = $this->getHappayPos($this->femaleLunar, $fWd, ['正官', '七杀']);
        $posSex = 'f';
        $bazi = $this->femaleLunar->getLunarByBetween()['jinian'];
        if (false == $pos) {
            $bazi = $this->maleLunar->getLunarByBetween()['jinian'];
            $pos = $this->getHappayPos($this->maleLunar, $mWd, ['正财', '偏财']);
            $posSex = 'm';
        }
        if (false == $pos) {
            return $list[4];
        }
        // 判断用和忌
        $isYong = 0;
        if (in_array($pos[2], $this->xiYongList[$posSex]['y'])) {
            $isYong = 1;
        }
        // 五行属性
        $wuXing = $this->wuxing;
        $cur = $wuXing[$bazi[$pos[0]][$pos[1]]];
        $xianglin = [0 => $wuXing[$bazi[$pos[0]][1]]];
        if ($pos[1]) {
            $xianglin[0] = $wuXing[$bazi[$pos[0]][0]];
        }
        foreach ($list2[$pos[0]] as $v) {
            array_push($xianglin, $wuXing[$bazi[$v][1]]);
        }
        $shengNum = 0;
        $keNum = 0;
        foreach ($xianglin as $v1) {
            if (in_array($kezhi[$cur][$v1][0], ['同', '生'])) {
                $shengNum++;
            } else {
                $keNum++;
            }
        }
        if ($isYong) {
            if (count($xianglin) == $shengNum) {
                return $list[0];
            }
            if ($keNum > 1) {
                return $list[2];
            }
            return $list[1];
        } else {
            if ($shengNum > 1) {
                return $list[6];
            }
            if ($shengNum == 1) {
                return $list[5];
            }
            if ($keNum > 1) {
                return $list[3];
            }
            return $list[4];
        }
    }

    /**
     * 判断女官星或男财星所在位置
     * @param Ex $lunar 日期实例
     * @param string $wd 旺度
     * @param array $arr 符合的条件
     * @return array|bool
     */
    private function getHappayPos($lunar, $wd, $arr)
    {
        $list = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $god = $lunar->getGod();
        $_god = $lunar->_getGod();
        $pos = [];
        foreach ($god as $k => $v) {
            if (in_array($v, $arr)) {
                $pos = [$list[$k], 0, $v];// 柱，天干
                break;
            }
        }
        if (empty($pos)) {
            foreach ($_god as $k => $v) {
                foreach ($v['god'] as $v2) {
                    if (in_array($v2, $arr)) {
                        $pos = [$list[$k], 1, $v2];// 柱，地支
                        break 2;
                    }
                }
            }
        }
        if (empty($pos)) {
            return false;
        }
        return $pos;
    }

    /**
     * 相扶相旺
     * @return array
     */
    private function getXiangFu(): array
    {
        $list = [
            [
                '男命中存在对妻子健康不利的信息，他可能并不总能真心疼爱妻子。在很多情况下，妻子付出颇多，但他可能视为理所当然。因此，很难说这样的男命能够旺妻。',
                '男命内心深处可能对妻子并不顺从，但表面上却表现得顺从。生活中，他可能会受到妻子的拖累，或者自己感情不专，导致求财困难。甚至妻子的健康也可能因他而受损。因此，他难以被称为旺妻之命。然而，如果他的命局弱而受制，无根无气，力量微小或失去制衡之力，并且远离日主，这样的男命反而有可能旺妻。',
                '男命对婚姻有自己独特的见解和态度。他通常对婚姻持乐观、积极、向上的看法，对妻子的行为满意度较高。因此，他会主动维护妻子，多站在她的立场考虑问题，处理问题，倾向于支持妻子。这样的男命定然能够旺妻。',
                '男命对妻子应该既有批评也有保护，这样的情感关系才更加美好。在日常生活中，这意味着他既要严肃地指出妻子的错误，也要给予挽救和慰留。所谓‘惩前毖后，治病救人’就是这个道理。这样的男命当然能够旺妻。',
                '男命对自己所深爱的妻子向来疼爱有加，能够助力妻子，无论喜忌如何。当然，如果妻子是他的喜用神，那么这种助力会更加显著，信息也会更加明确。',
                '男命的八字能够对女方的八字产生补益作用。在生活中，这意味着他能够源源不断地为妻子提供帮助。如果这种关系不被破坏，那么大多数情况下可以判定为旺妻之命！',
            ],
            [
                '女命以官星为用神，在婚姻中更能享受到用神所带来的帮助。她往往能够得到丈夫的疼爱与关怀。此外，她还能在运势方面为丈夫带来持续的旺夫气场，有助于丈夫在人际关系和财运方面取得好成果。',
                '女命若官星为忌神，在婚姻中可能会面临较多困难。丈夫的心思可能不完全在她身上。在婚姻中，她更多地需要依靠自己，坚定立场。她的求财之路可能不会那么顺利，因此也就难以谈到旺夫。然而，如果官星无根无气且远离日柱，那么她对丈夫的影响将是无伤无害的。这种情况下，丈夫反而可能旺她，对她情深意重。',
                '女命对婚姻有自己独特的想法和理解，通常持乐观积极的态度。她对另一半的各方面条件满意度很高。因此，在生活中她会主动照顾丈夫，生活也会很美满。无论是处理问题还是解决问题，她都能为丈夫提供帮助，定能旺夫。',
                '女命对婚姻的满意程度可能不高，另一半可能会给她带来很多麻烦。然而这并不是绝对的。如果女命对丈夫既有保护又有批评，就可以使生活和谐美满。对于丈夫的一些小问题，‘赏罚分明’才是正确的做法。这样自然而然会对丈夫有所帮助，当然也就能够旺夫。',
                '女命对丈夫非常喜爱，视如心头肉。她自然会在各方面都助力丈夫，使生活更加美满。',
                '女命的八字能够对男命的八字产生补益作用。在生活中这意味着女命能够为丈夫提供帮助并克制另一方忌讳的大运。如果这种关系不被破坏那么就可以判定为旺夫之命。',
            ],
        ];
        $mYongList = $this->xiYongList['m']['y'];
        $mJiList = $this->xiYongList['m']['j'];
        $fYongList = $this->xiYongList['f']['y'];
        $fJiList = $this->xiYongList['f']['j'];

        $mGod = $this->maleLunar->getGod();
        $mZGod = $this->maleLunar->_getGod();
        $fGod = $this->femaleLunar->getGod();
        $fZGod = $this->femaleLunar->_getGod();
        $mrzGod = $mZGod['day']['god'][0];
        $frzGod = $fZGod['day']['god'][0];
        $mnum = 0;
        $fnum = 0;
        $mWxNum = $this->maleLunar->getWuxingNum();
        $fWxNum = $this->femaleLunar->getWuxingNum();
        arsort($mWxNum);
        asort($fWxNum);
        if (key($mWxNum) == key($fWxNum)) {
            $mnum = 6;
            $fnum = 6;
        }
        if (in_array($mrzGod, ['食神', '伤官'])) {
            $mnum = 5;
        } else {
            if (in_array($mrzGod, $mYongList)) {
                $mnum = 3;
            } elseif (in_array($mrzGod, $mJiList)) {
                $mnum = 4;
            } else {
                if (array_intersect($mGod, $mYongList)) {
                    $mnum = 1;
                } elseif (array_intersect($mGod, $mJiList)) {
                    $mnum = 2;
                }
            }
        }
        if (in_array($frzGod, ['正官', '七杀'])) {
            $fnum = 5;
        } else {
            if (in_array($frzGod, $fYongList)) {
                $fnum = 3;
            } elseif (in_array($frzGod, $fJiList)) {
                $fnum = 4;
            } else {
                if (array_intersect($fGod, $fYongList)) {
                    $fnum = 1;
                } elseif (array_intersect($fGod, $fJiList)) {
                    $fnum = 2;
                }
            }
        }
        $res = [
            'male' => '',
            'female' => '',
            'none' => '',
        ];
        if ($mnum > 0) {
            $res['male'] = $list[0][$mnum - 1];
        }
        if ($fnum > 0) {
            $res['female'] = $list[1][$fnum - 1];
        }
        if (empty($res['male']) && empty($res['female'])) {
            $res['none'] = '无论男命或是女命对于另一半没有非常的满意当然也没有非常不满，处于相敬如宾的状态，生活平淡，虽然在生活上会有些许的帮助，但是对于另一半可能没有那么大的帮扶作用。';
        }
        return $res;
    }

    /**
     * 获得卦象
     * @param int $times 阳历时间戳
     * @return string
     */
    private function getGuaXiang($times): string
    {
        // 东西 西四
        $fate = [
            '1' => '坎', '2' => '坤', '3' => '震', '4' => '巽', '6' => '乾', '7' => '兑', '8' => '艮', '9' => '离',
            '5' => ['坤', '艮'], // 男为坤  女为艮
        ];
        $num = date('Y', $times) >= 2000 ? abs((99 - date('y', $times)) % 9) : abs((100 - date('y', $times)) % 9);
        $num = $num == 0 ? 9 : $num;
        $guaxiang = $num == 5 ? $fate[5][0] : $fate[$num];
        return $guaxiang;
    }

    /**
     * 婆媳关系
     * @return string
     * @throws Exception
     */
    protected function getPoxi(): string
    {
        $list = [
            '你们八字中年上见伤官，暗示婆媳关系可能不太和谐，常常会有言语上的冲突和不满。在这种情况下，家中需要有一个明智的角色来调解和缓和她们之间的关系。',
            '你们八字中日坐伤官，表明婆媳关系可能会成为家庭中的一大难题。弱势一方在面临困难时，可能难以得到亲朋好友的支持和帮助。',
            '男方八字中月日两支相冲，揭示了婆媳关系可能受到冲击，导致相处困难。男人在这种情况下可能会感到左右为难。如有必要，可以考虑让母亲和妻子分开居住，以减少彼此之间的摩擦。',
            '男方八字中出现财印交战，预示着婆媳关系可能不太融洽。男方需要在事业上努力拼搏，以避免因经济问题而加剧家庭矛盾。',
            '女方八字中同时遇到劫财与正财，表明婆媳关系可能会出现问题。尽快生育孩子有时能够成为化解矛盾和恨意的纽带。',
            '女方月带羊刃，暗示婆婆可能性格火爆，容易导致婆媳之间的冲突和口角。在这种情况下，最好请周围善于沟通的人来出面调解，以化解矛盾。',
            '你们的八字中并没有显示出明显的婆媳关系问题，这意味着在日常相处中出现问题的概率较低。然而，由于婆媳来自不同的家庭和年龄层，观念上难免会有分歧。双方都应该尊重对方的选择，给予彼此足够的空间，以维持家庭生活中的基本和睦。',
        ];
        // 羊刃算法
        $yueRen = [
            '甲' => '卯',
            '丙' => '午',
            '戊' => '午',
            '庚' => '酉',
            '壬' => '子',
        ];
        // 相冲
        $chong = ['子午', '丑未', '寅申', '卯酉', '辰戌', '午子', '未丑', '申寅', '酉卯', '戌辰'];
        $num = 6;
        $jiNianMan = $this->maleLunar->getLunarByBetween()['jinian'];
        $jiNianWoman = $this->femaleLunar->getLunarByBetween()['jinian'];
        $mGod = $this->maleLunar->getGod();
        $mZGod = $this->maleLunar->_getGod();
        $fGod = $this->femaleLunar->getGod();
        $fZGod = $this->femaleLunar->_getGod();
        // 年支上的神
        $listArr = [$mGod['year'], $fGod['year']];
        $listArr = array_merge($listArr, $mZGod['year']['god']);
        $listArr = array_merge($listArr, $fZGod['year']['god']);
        // 女方月支带羊刃
        if (isset($yueRen[$jiNianWoman['d'][0]]) && $yueRen[$jiNianWoman['d'][0]] == $jiNianWoman['m'][1]) {
            $num = 5;
        } elseif (in_array('劫财', $fGod) && in_array('正财', $fGod)) {
            // 女方八字天干中同时出现劫财正财
            $num = 4;
        } elseif (in_array('正财', $fGod) && in_array('正印', $fGod)) {
            // 男方八字天干中同时出现正财正印
            $num = 3;
        } elseif (in_array($jiNianMan['m'][1] . $jiNianMan['d'][1], $chong)) {
            // 男方八字中月日两支相冲
            $num = 2;
        } elseif (in_array('伤官', $fZGod['day']['god']) || in_array('伤官', $mZGod['day']['god'])) {
            // 男女任意一方八字日支上出现伤官
            $num = 1;
        } elseif (in_array('伤官', $listArr)) {
            $num = 0;
        } else {
            $num = 6;
        }
        return $list[$num];
    }

    /**
     * 出轨信息
     * @return string
     * @throws Exception
     */
    protected function getChuGui(): string
    {
        // 墙外桃花
        $qianWaiTaoHuaList = ['申酉', '子酉', '辰酉', '寅卯', '午卯', '戌卯', '丑午', '巳午', '酉午', '亥子', '卯子', '未子'];
        $lunarM = $this->maleLunar->getLunarByBetween();
        $lunarF = $this->femaleLunar->getLunarByBetween();
        $mNum = 0;
        $fNum = 0;
        // 判断是否有墙外桃花
        if (in_array($lunarM['jinian']['d'][1] . $lunarM['jinian']['h'][1], $qianWaiTaoHuaList)) {
            $mNum++;
        }
        if (in_array($lunarF['jinian']['d'][1] . $lunarF['jinian']['h'][1], $qianWaiTaoHuaList)) {
            $fNum++;
        }
        // 男八字中日支为偏财，正财星在其他干支。 女八字中日支七杀，正官星在其他干支
        if ($this->getIsBazi($this->maleLunar)) {
            $mNum++;
        }
        if ($this->getIsBazi($this->femaleLunar)) {
            $fNum++;
        }
        if ($this->getIsBaziXingNum($this->maleLunar, ['正财', '偏财'])) {
            $mNum++;
        }
        if ($this->getIsBaziXingNum($this->femaleLunar, ['正官', '七杀'])) {
            $fNum++;
        }
        $listData = [
            '10' => '男方的八字中出现了墙外桃花，意味着他个性外向，容易吸引异性目光，不喜欢束缚。即便已有伴侣，也可能寻求其他异性的关注。他人缘广泛，若运用得当，对事业大有裨益，但需谨慎处理感情关系。',
            '01' => '女方的八字中墙外桃花显现，暗示她在生活中容易与异性产生交集。这种交集或许无心，但需谨慎把握界限，以防给家庭带来伤害。',
            '20' => '男方日支代表妻宫，日支坐偏财可能表明他内心还有别的女人的影子。这种情况下出轨风险较高，但若正财足够稳固，则风险降低。换言之，女方若自信坚定，最终能取代他心中的旧影。',
            '02' => '女方日支代表夫宫和内心，日支坐偏官揭示她内心可能被他人占据。这意味着丈夫可能不是她的最爱。尽管这种内心情感波动可能影响两人关系，但若男方以爱包容，且其他干支正官星得力，则感情能够稳固。',
            '30' => '男方感情丰富，虽不至于滥情，但对异性朋友的抵抗力较弱，容易越界。若正财本气通根，则能增强自我约束力。',
            '03' => '女方八字中夫星多位，显示异性缘佳，有出轨的潜在条件。然而，若八字中食神能克制七杀，则能降低出轨风险。',
            '11' => '你们双方的八字均透露出不甘于家庭束缚的信息。你们各自拥有自己的社交圈，彼此依赖程度适中。这样的生活或许自在惬意，但在与异性交往中需保持谨慎，避免有心人破坏家庭和谐。',
            '22' => '你们双方的八字均显示出一定的独立性和社交需求。在享受个人空间的同时，务必注意维护家庭的稳定。与异性交往时要把握好分寸，防止误解和不必要的麻烦。',
            '33' => '你们双方的八字均显示出一定的外向倾向和社交能力。在追求个人自由和独立的同时，不要忽视对伴侣的关注和家庭的责任。与异性保持适当距离，是维护家庭和谐的关键。',
            '00' => '你们双方的八字中未出现明显的出轨征兆，婚内生活相对稳定。你们能够对自己的行为负责，不易受到外界诱惑的干扰。感情虽趋于平淡，但注入新鲜元素可让生活更加丰富多彩。',
        ];
        $res = $listData[$mNum . $fNum] ?? $listData['00'];
        return $res;
    }

    /**
     * 重要关口
     * @return array
     * @throws Exception
     */
    protected function getImportant(): array
    {
        $man = $this->getAiqing($this->maleLunar, 0);
        $woman = $this->getAiqing($this->femaleLunar, 1);
        $res = array_values(array_intersect($man, $woman));
        $year = $this->maleLunar->dateTime->format('Y');
        $yearF = $this->femaleLunar->dateTime->format('Y');
        foreach ($res as $k => $v) {
            if (($v - $year) < 29) {
                unset($res[$k]);
            }
        }
        $res = array_values($res);
        if (empty($res)) {
            foreach ($woman as $k1 => $v1) {
                if (($v1 - $yearF) > 25 && $v1 % 5 < 3 && count($res) < 6) {
                    $res[] = $v1;
                }
            }
        }
        if (count($res) > 6) {
            $i = (count($res) / 2) > 6 ? 4 : 3;
            foreach ($res as $k => $v) {
                if ($k % 7 < $i) {
                    unset($res[$k]);
                }
            }
            $res = array_values($res);
        }
        return $res;
    }

    /**
     * 爱情
     * @param $Lunar
     * @param $sex
     * @return array
     * @throws Exception
     */
    private function getAiqing($Lunar, $sex): array
    {
        $lunar = $Lunar->getLunarByBetween();
        $listHunXai = [
            '1011' => '多培养感情，战胜竞争对手',
            '1001' => '需防有他人介入你们的生活',
            '0111' => '易有婚灾，甚至可能离婚',
            '0110' => '易出现夫妻不和争吵',
            '0101' => '切莫因摩擦导致感情危机',
            '0011' => '提防可能会有的伤病灾',
        ];
        $listXianHe = $this->listXianHe;
        //日地支
        $rdz = $lunar['jinian']['d'][1];
        $rtg = $lunar['jinian']['d'][0];
        $yearUser = $Lunar->dateTime->format('Y');
        $year = date('Y', time());
        if ($year - $yearUser < 16) {
            $year = $yearUser + 16;
        }
        $tmpData = [];
        $tmpData2 = [];
        for ($i = 0; $i < 40; $i++) {
            $tmpyear = $year + $i;
            $tgz = BaziExt::getGanZhi($tmpyear);
            $tmpArr0 = [$rdz, $tgz[1]];
            sort($tmpArr0);
            $tmp0 = implode('', $tmpArr0);
            $tmpStr = '';
            if (in_array($tmp0, $listXianHe[1]) || str_contains($listXianHe[2], $tmp0)) {
                $tmpStr .= '1';
            } else {
                $tmpStr .= '0';
            }
            if (in_array($tmp0, $listXianHe[3])) {
                $tmpStr .= '1';
            } else {
                $tmpStr .= '0';
            }
            $tmpStr .= $this->getXianHai($sex, $tgz, $Lunar);
            $tmpData2[$tmpyear] = $tmpStr;
            if (isset($listHunXai[$tmpStr])) {
                $tmpData[$tmpyear] = $listHunXai[$tmpStr];
            }
        }
        if (empty($tmpData)) {
            foreach ($tmpData2 as $k => $v) {
                if ($v == '0100') {
                    $tmpData[$k] = $k % 2 ? '易出现夫妻不和争吵' : '提防可能会有的伤病灾';
                }
            }
        }
        return array_keys($tmpData);
    }

    /**
     * 相害
     * @param int $sex 性别
     * @param array $gz 流年干支
     * @param Ex $Lunar
     * @return string
     * @throws Exception
     */
    private function getXianHai($sex, $gz, $Lunar): string
    {
        $list = [
            ['正财', '偏财'],
            ['正官', '七杀'],
        ];
        $list2 = [
            ['比肩', '劫财'],
            ['比肩', '劫财', '伤官', '食神'],
        ];
        $lunar = $Lunar->getLunarByBetween();
        $listXianHe = $this->listXianHe;
        $rdz = $lunar['jinian']['d'][1];
        $rtg = $lunar['jinian']['d'][0];
        $baziGod = $Lunar->getGod();
        $baziGod2 = $Lunar->_getGod();
        foreach ($baziGod2 as $v) {
            array_push($baziGod, $v['god'][0]);
        }
        $tmpStr = '';
        if (in_array($list[$sex][0], $baziGod) || in_array($list[$sex][1], $baziGod)) {
            $tmpGod = $Lunar->getGodNameByTg($rtg, $gz[0]);
            if (in_array($tmpGod, $list2[$sex])) {
                $tmpStr .= '1';
            } else {
                $tmpStr .= '0';
            }
            $tmpArr = [$rtg, $gz[0]];
            $tmpArr1 = [$rdz, $gz[1]];
            sort($tmpArr);
            sort($tmpArr1);
            $tmptg = implode('', $tmpArr);
            $tmpdz = implode('', $tmpArr1);
            if (in_array($tmptg, $listXianHe[0]) || in_array($tmpdz, $listXianHe[1]) || str_contains($listXianHe[2], $tmpdz) || in_array($tmpdz, $listXianHe[3])) {
                $tmpStr .= '1';
            } else {
                $tmpStr .= '0';
            }
        } else {
            $tmpStr = '00';
        }
        return $tmpStr;
    }

    /**
     * 男八字中日支为偏财，正财星在其他干支。 女八字中日支七杀，正官星在其他干支
     * @param Ex $Lunar
     * @return  bool
     */
    private function getIsBazi($Lunar): bool
    {
        $list = [
            ['偏财', '正财'], ['七杀', '正官'],
        ];
        $sex = $Lunar->sex;
        $god1 = $list[$sex][0];
        $god2 = $list[$sex][1];
        $god = $Lunar->getGod();
        $zGod = $Lunar->_getGod();
        if (!in_array($god1, $zGod['day']['god'])) {
            return false;
        }
        if (in_array($god2, $god)) {
            return true;
        }
        foreach ($zGod as $k => $v) {
            if ($k == 'day') {
                continue;
            }
            if (in_array($god2, $v['god'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * (男)八字中财星在3个以上（女）八字中官星在3个以上
     * @param Ex $Lunar
     * @param $arr
     * @return bool
     */
    private function getIsBaziXingNum($Lunar, $arr): bool
    {
        $num = 0;
        $god = $Lunar->getGod();
        $zGod = $Lunar->_getGod();
        foreach ($god as $v) {
            if (in_array($v, $arr)) {
                $num++;
            }
        }
        foreach ($zGod as $v) {
            foreach ($v['god'] as $v2) {
                if (in_array($v2, $arr)) {
                    $num++;
                }
            }
        }
        if ($num > 2) {
            return true;
        }
        return false;
    }

    /**
     * 子女状况
     * @return array
     * @throws Exception
     */
    protected function getZiNv(): array
    {
        $jinianF = $this->femaleLunar->getLunarByBetween()['jinian'];
        $jinianM = $this->maleLunar->getLunarByBetween()['jinian'];
        $wuxing = $this->wuxing;
        $res = [];
        // 时干生日干或时干与日干相合
        if ($this->getIsXianhe($wuxing[$jinianF['h'][0]], $wuxing[$jinianF['d'][0]]) || $this->getIsXianhe($wuxing[$jinianM['h'][0]], $wuxing[$jinianM['d'][0]])) {
            $res[] = '时柱与日柱天地相合、相生，说明子女对父母情深意重，感恩心情很重，成家立业之后依然会与你们的家庭保持良好的关系，这样的子女当然会尽心照料你们。';
        }
        // 月支+时干
        $list = [
            '子' => ['巳', '壬'], '丑' => ['庚', '庚'], '寅' => ['丁', '丙'], '卯' => ['申', '甲'],
            '辰' => ['壬', '壬'], '巳' => ['辛', '庚'], '午' => ['亥', '丙'], '未' => ['甲', '甲'],
            '申' => ['癸', '壬'], '酉' => ['寅', '庚'], '戌' => ['丙', '丙'], '亥' => ['乙', '甲'],
        ];
        if (isset($list[$jinianF['m'][1]][$jinianF['h'][0]]) || isset($list[$jinianM['m'][1]][$jinianM['h'][0]])) {
            $res[] = '子女宫居天月二德，你们的子女必然具有人伦之情，孝顺父母必然是顺理成章的事情，所以你们子女的品行定然不差。';
        }
        $mYongList = $this->xiYongList['m']['y'];
        $fyongList = $this->xiYongList['f']['y'];
        $fGod = $this->femaleLunar->getGod();
        $mGod = $this->maleLunar->getGod();
        if (empty($res)) {
            // 条件4 时干
            $list4 = [
                '11' => '你们能得子女之力，由此可见你们的儿女多是孝顺之人。',
                '01' => '儿女孝心是有，但是会偏向父母其中一方，与另一方会有矛盾。',
                '10' => '儿女孝心是有，但是会偏向父母其中一方，与另一方会有矛盾。',
                '00' => '双方的子女星皆为忌神，十有八九子女是不怎么孝顺的。',
            ];
            $shiYongM = '0';
            $shiYongF = '0';
            if (in_array($fGod['hour'], $fyongList)) {
                $shiYongF = '1';
            }
            if (in_array($mGod['hour'], $mYongList)) {
                $shiYongM = '1';
            }
            $res[] = $list4[$shiYongM . $shiYongF];
        }
        $yongM = '0';
        $yongF = '0';
        if ($this->getIsYong($this->maleLunar)) {
            $yongM = '1';
        }
        if ($this->getIsYong($this->femaleLunar)) {
            $yongF = '1';
        }
        $list3 = [
            '11' => '子女聪明能干，事业有所成就，晚年能够享儿女的福气。',
            '01' => '子女多为老实之人，能力一般，可能需要父母的帮助。',
            '10' => '子女多为老实之人，能力一般，可能需要父母的帮助。',
            '00' => '子女发展不好，难成大器，将来可能被子女拖累。',
        ];
        $res[] = $list3[$yongM . $yongF];
        return $res;
    }

    /**
     * 判断是否相合
     * @param $str
     * @param $str2
     * @return bool
     */
    private function getIsXianhe($str, $str2): bool
    {
        $kezhi = $this->kezhi;
        if (in_array($kezhi[$str][$str2][0], ['同', '生'])) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为用神
     * @param Ex $Lunar
     * @return  bool
     */
    private function getIsYong($Lunar): bool
    {
        $list = [
            ['七杀', '正官'], ['食神', '伤官'],
        ];
        $sex = $Lunar->sex;
        $yongList = $this->xiYongList['m']['y'];
        if ($sex) {
            $yongList = $this->xiYongList['f']['y'];
        }
        $god = $Lunar->getGod();
        $zGod = $Lunar->_getGod();
        foreach ($god as $v) {
            if (in_array($v, $list[$sex]) && in_array($v, $yongList)) {
                return true;
            }
        }
        foreach ($zGod as $v) {
            foreach ($v['god'] as $v1) {
                if (in_array($v1, $list[$sex]) && in_array($v1, $yongList)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 健康
     * @return array
     * @throws Exception
     */
    protected function getHealth(): array
    {
        $list = [
            [
                '金' => ['身体素质强健，天生少病。然而，仍需关注肺部及大肠健康，做好日常保养。', '天生体质佳，疾病鲜少。日常应重视肺部与大肠的调养。', '蛙肉、黄羊肉可养肺补气，白萝卜、鸭梨、杏仁、银耳等食物有助于补肺益气、润肠通便，日常饮食可多加摄取。'],
                '木' => ['身体素质好，但需注意肝胆健康。平时应关注这方面可能的疾病。', '虽然身体底子好，还是不能忽视肝胆的保养。', '牛肉、牛肝、养肝等食物有益于肝胆，黄瓜、青椒、西蓝花等蔬菜也有助于肝脏健康。日常饮食可多选择这类食物。'],
                '水' => ['身体底子极好，但需留意肾脏保养。日常生活中应注意相关健康问题。', '身体素质上佳，但仍需注重肾脏的日常调养。', '狗肉、羊肉、甲鱼等食物有助于补肾养血，黑色食物如黑豆、黑芝麻、紫菜等也对肾脏有益。日常可适量食用。'],
                '火' => ['身体健康状况良好，但需关注血液循环和心脏健康。日常保养不可忽视。', '虽然少病，但血液循环和心脏的保养仍需重视。', '牛羊肉、乌鸡、蛋类等食物有助于补心养血，红枣、西红柿、樱桃等红色食品对心脏健康也有益。日常可多选择这类食物。'],
                '土' => ['身体强健，但需注意肠胃健康。脾胃的保养在日常生活中尤为重要。', '身体底子好，肠胃的调养却不能忽视。', '猪肚、鸡肉、鸭肉等食物有健脾养胃的作用，黄色的食物如香蕉、南瓜、黄豆等也有助于肠胃健康。日常饮食可适当选择。'],

            ],
            [
                '金' => ['可能存在肺部健康问题，如鼻子过敏、流涕、支气管炎等。日常需特别注意肺部和大肠的保养。', '日常易出现咳痰、气管炎等呼吸系统疾病，应重视肺部和大肠的健康调养。', '蛙肉、黄羊肉、白萝卜等食物有助于养肺润肠，日常可适量食用以改善相关症状。'],
                '木' => ['需留意肝胆健康，可能出现肝炎、胆囊炎等问题。同时，心悸、头晕目眩等症状也需关注。', '肝胆疾病和心神不宁等问题需引起重视，内外都要注意保养。', '牛肉、牛肝、养肝以及黄瓜、青椒等蔬果对肝胆健康有益，日常饮食可多加选择。'],
                '水' => ['在生殖和泌尿系统方面可能存在健康隐患，如遗精、肾虚等。日常应注重肾脏保养。', '肾脏健康需特别关注，生殖和泌尿系统的问题不容忽视。', '狗肉、羊肉、甲鱼等食物有助于补肾固精，黑色食物如黑豆、黑芝麻等也有助于肾脏健康。日常可适量食用。'],
                '火' => ['血液循环系统可能存在问题，如心脏衰弱、心律不齐等。日常保养需特别注意。', '心脏健康需引起重视，血液循环问题不容忽视。', '牛羊肉、乌鸡、蛋类等食物有助于补心养血，红枣、西红柿等红色食品也有助于改善心脏健康。日常饮食可适当选择。'],
                '土' => ['肠胃健康需特别关注，可能出现脾胃不和、胃溃疡等问题。日常应注重脾胃的保养。', '肠胃疾病的发生需引起重视，脾胃的调养在日常生活中尤为关键。', '猪肚、鸡肉等食物有助于健脾养胃，黄色的食物如香蕉、南瓜等也有助于改善肠胃健康。日常饮食可适当选择。'],

            ],
        ];
        $wuxing = $this->wuxing;
        $jinianF = $this->femaleLunar->getLunarByBetween()['jinian'];
        $jinianM = $this->maleLunar->getLunarByBetween()['jinian'];

        $numM = $this->getDayFuNum($jinianM);
        $numF = $this->getDayFuNum($jinianF);;
        $dayWuXingMT = $wuxing[$jinianM['d'][0]];
        $dayWuXingFT = $wuxing[$jinianF['d'][0]];
        $res = [
            [$list[$numM][$dayWuXingMT][0], $list[$numM][$dayWuXingMT][2]],
            [$list[$numF][$dayWuXingFT][1], $list[$numM][$dayWuXingFT][2]],
        ];
        return $res;
    }

    /**
     * 日元得帮扶多而旺 求个数  日干年干，日干时干，日干日支（生，同）和日元秉月令之气
     * @param array $jinian 八字
     * @return int 0或1
     */
    private function getDayFuNum($jinian): int
    {
        $res = 1;
        $wuxing = $this->wuxing;
        $num = 0;
        // 日干五行
        $dayWuXingT = $wuxing[$jinian['d'][0]];
        // 日干年干
        if ($this->getIsXianhe($dayWuXingT, $wuxing[$jinian['y'][0]])) {
            $num++;
        }
        // 日干时干
        if ($this->getIsXianhe($dayWuXingT, $wuxing[$jinian['h'][0]])) {
            $num++;
        }
        // 日干日支
        if ($this->getIsXianhe($dayWuXingT, $wuxing[$jinian['d'][1]])) {
            $num++;
        }
        // 日干月支 日元秉月令之气
        if ($this->getIsXianhe($dayWuXingT, $wuxing[$jinian['m'][1]])) {
            if ($num > 0) {
                $res = 0;
            }
        } else {
            if ($num == 3) {
                $res = 0;
            }
        }
        return $res;
    }
}
