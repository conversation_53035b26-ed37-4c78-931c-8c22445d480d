<?php
// +----------------------------------------------------------------------
// | Dingziqm.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\General;
use app\model\baobaoqm\Cnword;
use app\validate\ValidateBase;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class Dingziqm
{
    /**
     * 名字列表
     * @var array
     */
    protected array $mzList = [
        '生' => [5, '金'], '小' => [3, '金'], '春' => [9, '金'], '少' => [4, '金'], '仁' => [4, '金'], '青' => [8, '金'], '敬' => [13, '木'], '才' => [4, '金'], '臣' => [6, '金'],
        '诚' => [14, '金'], '佳' => [8, '木'], '双' => [18, '金'], '善' => [12, '金'], '楚' => [13, '金'], '秀' => [7, '金'], '梓' => [11, '木'], '纯' => [10, '金'], '尚' => [8, '金'],
        '杨' => [13, '木'], '三' => [3, '金'], '献' => [20, '木'], '前' => [9, '金'], '周' => [8, '金'], '驹' => [15, '木'], '兰' => [23, '木'], '关' => [19, '木'], '儒' => [16, '金'],
        '舒' => [12, '金'], '又' => [2, '土'], '笑' => [10, '金'], '宙' => [8, '金'], '舟' => [6, '金'], '棋' => [12, '木'], '祈' => [9, '木'], '贡' => [10, '木'], '舸' => [11, '木'],
        '稳' => [19, '土'], '身' => [7, '金'], '穆' => [16, '金'], '集' => [12, '木'], '席' => [10, '金'], '查' => [9, '金'], '樾' => [16, '木'], '精' => [14, '金'], '倪' => [10, '金'],
        '倩' => [10, '金'], '常' => [11, '金'], '荷' => [13, '木'], '慈' => [14, '金'], '梦' => [14, '木'], '节' => [15, '金'], '忱' => [8, '金'], '童' => [12, '金'], '飞' => [9, '水'],
        '思' => [9, '金'], '昌' => [8, '金'], '超' => [12, '金'], '绍' => [11, '金'], '升' => [4, '金'], '骏' => [17, '金'], '声' => [17, '金'], '喜' => [12, '水'], '朝' => [12, '金'],
        '然' => [12, '金'], '晨' => [11, '金'], '如' => [6, '金'], '时' => [10, '金'], '盛' => [12, '金'], '韶' => [14, '金'], '赤' => [7, '金'], '想' => [13, '金'], '译' => [20, '金'],
        '醒' => [16, '金'], '爽' => [11, '金'], '上' => [3, '金'], '再' => [6, '金'], '翼' => [18, '金'], '璨' => [18, '金'], '愈' => [13, '金'], '曾' => [12, '金'], '七' => [2, '金'],
        '语' => [14, '木'], '而' => [6, '金'], '速' => [14, '金'], '玉' => [5, '木'], '祥' => [11, '金'], '宇' => [6, '土'], '信' => [9, '金'], '磊' => [15, '土'], '培' => [11, '土'],
        '崇' => [11, '金'], '裕' => [13, '金'], '堂' => [11, '土'], '守' => [6, '金'], '崴' => [12, '土'], '州' => [6, '金'], '融' => [16, '土'], '懿' => [22, '土'], '予' => [4, '土'],
        '珠' => [11, '金'], '嵘' => [17, '土'], '韵' => [19, '土'], '实' => [14, '金'], '羲' => [17, '金'], '途' => [14, '金'], '戌' => [6, '土'], '成' => [7, '金'], '新' => [13, '金'],
        '刚' => [10, '金'], '金' => [8, '金'], '正' => [5, '金'], '剑' => [15, '金'], '瑞' => [14, '金'], '克' => [7, '木'], '胜' => [12, '金'], '锋' => [15, '金'], '铭' => [14, '金'],
        '宗' => [8, '金'], '书' => [10, '金'], '秋' => [9, '金'], '鑫' => [24, '金'], '先' => [6, '金'], '铁' => [21, '金'], '镇' => [18, '金'], '钧' => [12, '金'], '锡' => [16, '金'],
        '祖' => [10, '金'], '石' => [5, '金'], '钢' => [16, '金'], '申' => [5, '金'], '承' => [8, '金'], '寿' => [14, '金'], '银' => [14, '金'], '革' => [9, '木'], '仕' => [5, '金'],
        '捷' => [12, '金'], '作' => [7, '金'], '靖' => [13, '金'], '峻' => [10, '金'], '庚' => [8, '金'], '佐' => [7, '金'], '帅' => [9, '金'], '初' => [7, '金'], '财' => [10, '金'],
        '瑜' => [14, '金'], '钦' => [12, '金'], '壮' => [7, '金'], '琛' => [13, '金'], '则' => [9, '金'], '齐' => [14, '金'], '铖' => [14, '金'], '修' => [10, '金'], '鉴' => [22, '金'],
        '铎' => [21, '金'], '铮' => [16, '金'], '钟' => [20, '金'], '钊' => [10, '金'], '钰' => [13, '金'], '真' => [10, '金'], '列' => [6, '金'], '斯' => [12, '金'], '辛' => [7, '金'],
        '戈' => [4, '金'], '占' => [5, '火'], '持' => [10, '金'], '仓' => [10, '金'], '骁' => [22, '金'], '祚' => [10, '金'], '社' => [8, '金'], '聚' => [14, '金'], '受' => [8, '金'],
        '得' => [11, '金'], '白' => [5, '水'], '选' => [19, '金'], '战' => [16, '金'], '尊' => [12, '金'], '掌' => [12, '金'], '仙' => [5, '金'], '邢' => [11, '金'], '赛' => [17, '金'],
        '酉' => [7, '金'], '叙' => [9, '金'], '键' => [17, '金'], '钒' => [11, '金'], '珊' => [10, '金'], '镭' => [21, '金'], '司' => [5, '金'], '市' => [5, '金'], '主' => [5, '金'],
        '创' => [12, '金'], '劭' => [7, '金'], '续' => [21, '金'], '拾' => [10, '金'], '铧' => [20, '金'], '式' => [6, '金'], '铸' => [22, '金'], '总' => [17, '金'], '须' => [12, '金'],
        '问' => [11, '金'], '环' => [18, '金'], '瑄' => [14, '火'], '设' => [11, '金'], '铨' => [14, '金'], '载' => [13, '金'], '铃' => [13, '金'], '钜' => [13, '火'], '素' => [10, '金'],
        '鋆' => [15, '火'], '紫' => [11, '金'], '銮' => [27, '金'], '夕' => [3, '金'], '勍' => [10, '火'], '神' => [10, '金'], '韧' => [12, '金'], '锴' => [17, '金'], '叔' => [8, '金'],
        '洪' => [10, '水'], '顺' => [12, '金'], '川' => [3, '金'], '行' => [6, '水'], '夫' => [4, '水'], '慧' => [15, '水'], '聪' => [17, '金'], '非' => [8, '水'], '运' => [16, '土'],
        '睿' => [14, '金'], '凤' => [14, '水'], '复' => [12, '水'], '存' => [6, '金'], '妙' => [7, '水'], '沧' => [14, '水'], '人' => [2, '金'], '任' => [6, '金'], '丞' => [6, '金'],
        '宵' => [10, '金'], '静' => [16, '金'], '巧' => [5, '木'], '鲲' => [19, '木'], '沐' => [8, '水'], '诗' => [13, '金'], '霙' => [17, '火'], '浪' => [11, '水'], '官' => [8, '木'],
        '数' => [15, '金'], '项' => [12, '水'], '扶' => [8, '水'], '细' => [11, '金'], '潭' => [16, '水'], '漳' => [15, '水'], '渤' => [13, '水'], '儿' => [8, '金'], '芬' => [10, '木'],
        '翠' => [14, '金'], '施' => [9, '金'], '靓' => [15, '金'], '柔' => [9, '金'], '嫦' => [14, '金'], '笔' => [12, '木'], '俏' => [9, '木'], '莱' => [14, '木'], '束' => [7, '金'],
        '馨' => [20, '金'], '悦' => [11, '金'], '彩' => [11, '金'], '歆' => [13, '金'], '宣' => [9, '金'], '翾' => [19, '金'], '谕' => [16, '金'], '惜' => [12, '金'], '晰' => [12, '金'],
        '千' => [3, '金'], '姗' => [8, '金'], '羡' => [12, '金'], '琤' => [13, '金'], '旋' => [11, '金'], '仟' => [5, '金'], '庄' => [13, '金'], '序' => [7, '金'], '殊' => [10, '金'],
        '铠' => [18, '金'], '蜀' => [13, '金'], '侃' => [8, '金'], '兮' => [4, '金'], '璁' => [16, '金'], '舜' => [12, '金'], '色' => [6, '金'], '琍' => [12, '火'], '钿' => [13, '金'],
        '钫' => [12, '金'], '愫' => [14, '金'], '禅' => [17, '金'], '滢' => [19, '水'], '玫' => [9, '水'], '沂' => [8, '水'], '米' => [6, '水'], '汐' => [7, '水'], '鲜' => [17, '金'],
        '淼' => [12, '水'], '净' => [12, '金'], '沁' => [8, '水'], '慎' => [14, '金'], '轩' => [10, '土'], '幼' => [5, '土'], '寅' => [11, '土'], '叶' => [15, '土'], '祎' => [14, '土'],
        '优' => [17, '土'], '应' => [17, '土'], '以' => [5, '土'], '旺' => [8, '土'], '亦' => [6, '土'], '迎' => [11, '土'], '昀' => [8, '土'], '引' => [4, '土'], '维' => [14, '土'],
        '远' => [17, '土'], '恩' => [10, '土'], '基' => [11, '土'], '有' => [6, '土'], '奎' => [9, '土'], '坤' => [8, '土'], '为' => [12, '土'], '勋' => [16, '土'], '辰' => [7, '土'],
        '育' => [10, '土'], '佑' => [7, '土'], '均' => [7, '土'], '圣' => [13, '土'], '延' => [7, '土'], '玮' => [14, '土'], '巍' => [21, '土'], '允' => [4, '土'], '增' => [15, '土'],
        '怡' => [9, '土'], '贻' => [12, '土'], '坦' => [8, '土'], '容' => [10, '土'], '纬' => [15, '土'], '豫' => [16, '土'], '韦' => [9, '土'], '禹' => [9, '土'], '岷' => [8, '土'],
        '爱' => [13, '土'], '也' => [3, '土'], '填' => [13, '土'], '岐' => [7, '土'], '宴' => [10, '土'], '唯' => [11, '土'], '园' => [13, '土'], '围' => [12, '土'], '缘' => [15, '土'],
        '阿' => [13, '土'], '惟' => [12, '土'], '垚' => [9, '土'], '威' => [9, '土'], '岩' => [8, '土'], '永' => [5, '土'], '益' => [10, '土'], '温' => [14, '土'], '冶' => [7, '土'],
        '乙' => [1, '土'], '意' => [13, '土'], '璟' => [17, '木'], '瑛' => [14, '土'], '晏' => [10, '土'], '岚' => [12, '土'], '娴' => [15, '土'], '娅' => [11, '土'], '圆' => [13, '土'],
        '衣' => [6, '土'], '慰' => [15, '土'], '夷' => [6, '土'], '尹' => [4, '土'], '玥' => [9, '土'], '林' => [8, '木'], '良' => [7, '火'], '忠' => [8, '火'], '长' => [8, '火'],
        '贤' => [15, '木'], '继' => [20, '木'], '康' => [11, '木'], '和' => [8, '水'], '欣' => [8, '木'], '友' => [4, '土'], '奇' => [8, '木'], '若' => [11, '木'], '枫' => [13, '木'],
        '标' => [15, '木'], '来' => [8, '火'], '可' => [5, '木'], '棠' => [12, '木'], '本' => [5, '木'], '季' => [8, '木'], '程' => [12, '火'], '纪' => [9, '木'], '艺' => [21, '木'],
        '孔' => [4, '木'], '琪' => [13, '木'], '柯' => [9, '木'], '禾' => [5, '水'], '业' => [13, '木'], '李' => [7, '木'], '相' => [9, '木'], '蔚' => [17, '木'], '弟' => [7, '火'],
        '蓝' => [20, '木'], '冀' => [16, '木'], '觉' => [20, '木'], '秦' => [10, '火'], '纲' => [14, '木'], '骥' => [27, '金'], '亭' => [9, '火'], '积' => [16, '火'], '梁' => [11, '火'],
        '仰' => [6, '木'], '让' => [24, '火'], '苏' => [22, '木'], '隽' => [13, '木'], '豆' => [7, '火'], '枢' => [15, '木'], '端' => [14, '火'], '宾' => [14, '水'], '桉' => [10, '木'],
        '伊' => [6, '土'], '志' => [7, '火'], '晓' => [16, '火'], '光' => [6, '火'], '俊' => [9, '火'], '立' => [5, '火'], '振' => [11, '火'], '耀' => [20, '火'], '大' => [3, '火'],
        '亮' => [9, '火'], '达' => [16, '火'], '阳' => [17, '土'], '乐' => [15, '火'], '智' => [12, '火'], '南' => [9, '火'], '传' => [13, '火'], '进' => [15, '火'], '力' => [2, '火'],
        '炳' => [9, '火'], '哲' => [10, '火'], '炜' => [13, '火'], '星' => [9, '金'], '年' => [6, '火'], '礼' => [18, '火'], '麟' => [23, '火'], '伦' => [10, '火'], '兆' => [6, '火'],
        '扬' => [13, '火'], '章' => [11, '火'], '迪' => [12, '火'], '之' => [4, '火'], '同' => [6, '火'], '晋' => [10, '火'], '灿' => [17, '火'], '连' => [14, '火'], '乃' => [2, '火'],
        '丹' => [4, '火'], '卓' => [8, '火'], '腾' => [20, '火'], '鲁' => [15, '火'], '昊' => [8, '火'], '丰' => [18, '火'], '令' => [5, '火'], '炎' => [8, '火'], '煜' => [13, '火'],
        '虹' => [9, '木'], '焕' => [13, '火'], '心' => [4, '金'], '显' => [23, '火'], '日' => [4, '火'], '易' => [8, '火'], '诺' => [16, '火'], '黎' => [15, '火'], '殿' => [13, '火'],
        '隆' => [17, '火'], '彤' => [7, '火'], '煌' => [13, '火'], '召' => [5, '火'], '昕' => [8, '火'], '临' => [17, '火'], '念' => [8, '火'], '自' => [6, '火'], '熹' => [16, '火'],
        '夏' => [10, '火'], '焰' => [12, '火'], '致' => [9, '火'], '恺' => [14, '火'], '烈' => [10, '火'], '通' => [14, '火'], '理' => [12, '火'], '臻' => [16, '火'], '燊' => [16, '火'],
        '昆' => [8, '火'], '斗' => [4, '火'], '灵' => [24, '火'], '展' => [10, '火'], '晶' => [12, '火'], '焜' => [12, '火'], '曦' => [20, '火'], '炯' => [9, '火'], '颂' => [13, '木'],
        '曙' => [18, '火'], '烨' => [16, '火'], '纶' => [14, '火'], '昭' => [9, '火'], '朗' => [11, '火'], '灼' => [7, '火'], '鼎' => [13, '火'], '登' => [12, '火'], '驰' => [13, '火'],
        '郎' => [14, '火'], '午' => [4, '火'], '早' => [6, '金'], '飘' => [20, '火'], '典' => [8, '火'], '特' => [10, '火'], '赫' => [14, '木'], '煊' => [13, '火'], '多' => [6, '火'],
        '欢' => [22, '水'], '迅' => [10, '火'], '二' => [2, '火'], '龄' => [20, '火'], '至' => [6, '火'], '男' => [7, '火'], '丁' => [2, '火'], '照' => [13, '火'], '图' => [14, '火'],
        '冉' => [5, '火'], '佰' => [8, '火'], '吕' => [7, '火'], '响' => [22, '水'], '太' => [4, '火'], '彻' => [15, '火'], '旻' => [8, '火'], '旅' => [10, '火'], '动' => [11, '火'],
        '骊' => [29, '火'], '焘' => [18, '火'], '劳' => [12, '火'], '熔' => [14, '火'], '炅' => [8, '火'], '仑' => [8, '火'], '襄' => [17, '火'], '璘' => [17, '火'], '郑' => [19, '火'],
        '祝' => [10, '火'], '老' => [6, '火'], '呈' => [7, '火'], '贺' => [12, '水'], '炫' => [9, '火'], '耿' => [10, '火'], '追' => [13, '火'], '玲' => [10, '火'], '璋' => [16, '火'],
        '晟' => [11, '火'], '议' => [20, '木'], '火' => [4, '火'], '论' => [15, '火'], '喆' => [12, '火'], '翎' => [11, '火'], '电' => [13, '火'], '挺' => [11, '火'], '丽' => [19, '火'],
        '晔' => [16, '火'], '娜' => [10, '火'], '轮' => [15, '火'], '招' => [9, '火'], '烁' => [19, '火'], '尔' => [14, '火'], '轶' => [12, '火'], '量' => [12, '火'], '代' => [5, '火'],
        '励' => [17, '火'], '骋' => [17, '火'], '畅' => [14, '火'], '皑' => [15, '火'], '琰' => [13, '火'], '罗' => [20, '火'], '炉' => [20, '火'], '质' => [15, '火'], '阅' => [15, '土'],
        '炽' => [16, '火'], '晴' => [12, '火'], '顶' => [11, '火'], '旦' => [5, '火'], '遂' => [16, '火'], '峰' => [10, '水'], '龙' => [16, '火'], '德' => [15, '火'], '中' => [4, '火'],
        '宝' => [20, '火'], '卫' => [15, '土'], '全' => [6, '火'], '廷' => [7, '火'], '仲' => [6, '火'], '田' => [5, '火'], '庭' => [10, '火'], '岳' => [17, '木'], '羽' => [6, '土'],
        '定' => [8, '火'], '余' => [7, '土'], '岭' => [17, '土'], '野' => [11, '土'], '岱' => [8, '火'], '球' => [12, '木'], '农' => [13, '火'], '留' => [10, '火'], '伍' => [6, '土'], '祯' => [14, '火'], '岛' => [10, '火'], '粤' => [12, '土'], '陶' => [16, '火'], '毓' => [13, '火'], '坡' => [8, '土'], '尘' => [14, '火'], '位' => [7, '土'], '在' => [6, '金'], '圻' => [7, '土'], '盾' => [9, '火'], '路' => [13, '火'], '土' => [3, '土'], '硕' => [14, '土'], '重' => [9, '火'], '岗' => [11, '土'], '团' => [14, '火'], '台' => [14, '火'], '天' => [4, '火'], '勇' => [9, '土'], '世' => [5, '金'], '利' => [7, '火'], '锦' => [16, '金'], '政' => [8, '火'], '士' => [3, '金'], '征' => [8, '土'], '西' => [6, '金'], '四' => [5, '金'], '争' => [8, '火'], '镜' => [19, '金'], '绪' => [15, '金'], '左' => [5, '火'], '赐' => [15, '金'], '肃' => [13, '金'], '玺' => [19, '火'], '猛' => [12, '水'], '锐' => [15, '金'], '述' => [12, '金'], '险' => [21, '金'], '将' => [11, '火'], '珍' => [10, '火'], '璇' => [16, '火'], '忍' => [7, '金'], '爵' => [18, '火'], '解' => [13, '木'], '帝' => [9, '火'], '文' => [4, '水'], '宁' => [14, '火'], '泽' => [17, '水'], '道' => [16, '火'], '万' => [15, '水'], '冬' => [5, '火'], '泰' => [9, '水'], '湘' => [13, '水'], '风' => [9, '水'], '翰' => [16, '水'], '凌' => [10, '火'], '步' => [7, '水'], '禄' => [13, '火'], '弘' => [5, '水'], '冲' => [8, '金'], '霄' => [15, '水'], '里' => [13, '火'], '美' => [9, '水'], '朔' => [10, '火'], '朋' => [7, '水'], '港' => [13, '水'], '付' => [5, '水'], '能' => [12, '火'], '寰' => [16, '水'], '享' => [8, '水'], '牧' => [8, '水'], '觅' => [11, '水'], '屏' => [9, '水'], '弢' => [8, '火'], '凉' => [10, '火'], '韬' => [19, '火'], '沙' => [8, '水'], '还' => [20, '水'], '汤' => [13, '水'], '婷' => [12, '火'], '娟' => [10, '木'], '媛' => [12, '火'], '蓉' => [16, '木'], '菊' => [14, '木'], '筠' => [13, '木'], '瑾' => [16, '火'], '贞' => [9, '火'], '娣' => [10, '火'], '颜' => [18, '木'], '菡' => [14, '木'], '梨' => [11, '火'], '荃' => [12, '木'], '妮' => [8, '火'], '瑶' => [15, '火'], '姿' => [9, '金'], '羚' => [11, '火'], '采' => [7, '火'], '俪' => [21, '火'], '俐' => [9, '火'], '焱' => [12, '火'], '丙' => [5, '火'], '腊' => [21, '火'], '对' => [14, '火'], '琅' => [12, '火'], '焦' => [12, '火'], '炼' => [13, '火'], '瞳' => [17, '火'], '珣' => [11, '火'], '晃' => [10, '火'], '囡' => [6, '火'], '当' => [13, '火'], '映' => [9, '火'], '遥' => [17, '火'], '嫣' => [14, '土'], '适' => [18, '火'], '蝶' => [15, '火'], '尼' => [5, '火'], '珞' => [11, '火'], '燃' => [16, '火'], '彰' => [14, '火'], '伶' => [7, '火'], '拉' => [9, '火'], '曌' => [16, '火'], '碧' => [14, '水'], '璐' => [17, '火'], '婉' => [11, '土'], '甜' => [11, '火'], '悠' => [11, '土'], '颐' => [15, '土'], '陆' => [16, '火'], '嬿' => [19, '土'], '珏' => [9, '火'], '宸' => [10, '金'], '琢' => [13, '火'], '刘' => [15, '火'], '弋' => [3, '火'], '媚' => [12, '水'], '池' => [7, '水'], '黛' => [17, '火'], '流' => [10, '水'], '耐' => [9, '火'], '珉' => [10, '水'], '女' => [3, '火'], '孜' => [7, '金'], '国' => [11, '木'], '建' => [9, '木'], '东' => [8, '木'], '强' => [12, '木'], '家' => [10, '木'], '健' => [11, '木'], '松' => [8, '木'], '嘉' => [14, '木'], '广' => [15, '木'], '义' => [13, '木'], '启' => [11, '木'], '英' => [11, '木'], '凯' => [12, '木'], '森' => [12, '木'], '毅' => [15, '木'], '茂' => [11, '木'], '树' => [16, '木'], '柏' => [9, '木'], '芳' => [10, '木'], '彬' => [11, '木'], '劲' => [9, '木'], '京' => [8, '木'], '栋' => [12, '木'], '君' => [7, '木'], '冠' => [9, '木'], '楠' => [13, '木'], '权' => [22, '木'], '贵' => [12, '木'], '彦' => [9, '木'], '根' => [10, '木'], '高' => [10, '木'], '开' => [12, '木'], '吉' => [6, '木'], '奕' => [9, '木'], '其' => [8, '木'], '尧' => [12, '木'], '谦' => [17, '木'], '琦' => [13, '木'], '琳' => [13, '木'], '柱' => [9, '木'], '勤' => [13, '木'], '麒' => [19, '木'], '雁' => [12, '木'], '桥' => [16, '木'], '科' => [9, '木'], '经' => [13, '木'], '廉' => [13, '木'], '桂' => [10, '木'], '木' => [4, '木'], '原' => [10, '木'], '祺' => [13, '木'], '功' => [5, '木'], '桐' => [10, '木'], '颖' => [16, '木'], '萌' => [14, '木'], '宽' => [15, '木'], '巨' => [5, '木'], '耕' => [10, '木'], '公' => [4, '木'], '楷' => [13, '木'], '魁' => [14, '木'], '菁' => [14, '木'], '卿' => [11, '木'], '格' => [10, '木'], '歌' => [14, '木'], '棣' => [12, '木'], '乔' => [12, '木'], '旗' => [14, '木'], '菘' => [14, '木'], '宜' => [8, '木'], '甲' => [5, '木'], '吾' => [7, '木'], '恪' => [10, '木'], '材' => [7, '木'], '葆' => [15, '木'], '菲' => [14, '木'], '蛟' => [12, '木'], '俭' => [15, '木'], '懋' => [17, '木'], '笙' => [11, '木'], '樟' => [15, '木'], '介' => [4, '木'], '植' => [12, '木'], '荫' => [17, '木'], '雅' => [12, '木'], '芝' => [10, '木'], '见' => [7, '木'], '村' => [7, '木'], '加' => [5, '木'], '杉' => [7, '木'], '稷' => [15, '木'], '葵' => [15, '木'], '抗' => [8, '木'], '房' => [8, '水'], '交' => [6, '木'], '珈' => [10, '木'], '模' => [15, '木'], '古' => [5, '木'], '谨' => [18, '木'], '侨' => [14, '木'], '佼' => [8, '木'], '榜' => [14, '木'], '椒' => [12, '木'], '稼' => [15, '木'], '休' => [6, '水'], '近' => [11, '木'], '莽' => [14, '木'], '果' => [8, '木'], '谷' => [7, '木'], '榕' => [14, '木'], '愿' => [14, '木'], '葱' => [15, '木'], '栎' => [19, '木'], '竹' => [6, '木'], '琴' => [13, '木'], '笃' => [16, '木'], '栩' => [10, '木'], '骑' => [18, '木'], '县' => [16, '木'], '蕾' => [19, '木'], '藤' => [21, '木'], '轲' => [12, '木'], '茁' => [11, '木'], '珺' => [12, '木'], '共' => [6, '木'], '芃' => [9, '木'], '梅' => [11, '木'], '居' => [8, '木'], '观' => [25, '木'], '芹' => [10, '木'], '箭' => [15, '木'], '蓬' => [17, '木'], '贯' => [11, '木'], '厦' => [12, '木'], '巩' => [15, '木'], '虔' => [10, '木'], '仪' => [15, '木'], '严' => [20, '木'], '桦' => [16, '木'], '弓' => [3, '木'], '今' => [4, '木'], '杰' => [12, '木'], '荣' => [14, '木'], '庆' => [15, '木'], '跃' => [21, '土'], '旭' => [6, '木'], '景' => [12, '木'], '皓' => [12, '木'], '咏' => [8, '土'], '越' => [12, '土'], '起' => [10, '木'], '联' => [17, '木'], '啸' => [15, '木'], '熙' => [13, '水'], '琨' => [13, '木'], '言' => [7, '木'], '更' => [7, '木'], '翊' => [11, '木'], '已' => [3, '木'], '营' => [17, '木'], '谚' => [16, '木'], '谊' => [15, '木'], '骄' => [22, '木'], '音' => [9, '土'], '傲' => [13, '土'], '艳' => [24, '土'], '亚' => [8, '土'], '山' => [3, '土'], '翔' => [12, '土'], '坚' => [11, '木'], '城' => [10, '土'], '逸' => [15, '土'], '燕' => [16, '土'], '峥' => [11, '土'], '鸥' => [22, '土'], '庸' => [11, '土'], '用' => [5, '土'], '依' => [8, '土'], '五' => [4, '木'], '屹' => [6, '土'], '崎' => [11, '土'], '愚' => [13, '木'], '约' => [9, '土'], '敖' => [11, '土'], '黄' => [12, '土'], '宥' => [9, '土'], '牛' => [4, '木'], '峡' => [10, '土'], '羊' => [6, '土'], '军' => [9, '木'], '元' => [4, '木'], '鹰' => [23, '木'], '肇' => [14, '木'], '难' => [19, '木'], '乾' => [11, '土'], '戎' => [6, '木'], '玖' => [8, '木'], '闯' => [18, '水'], '研' => [11, '木'], '群' => [13, '木'], '鸿' => [17, '水'], '富' => [12, '水'], '向' => [6, '水'], '震' => [15, '水'], '惠' => [12, '水'], '洁' => [16, '水'], '航' => [10, '水'], '河' => [9, '水'], '久' => [3, '木'], '月' => [4, '木'], '泳' => [9, '水'], '九' => [2, '木'], '谋' => [16, '水'], '澜' => [21, '水'], '亨' => [7, '水'], '胤' => [11, '木'], '洛' => [10, '水'], '溢' => [14, '水'], '湛' => [13, '水'], '术' => [11, '木'], '洺' => [10, '火'], '奂' => [9, '水'], '奋' => [16, '水'], '溯' => [14, '水'], '莉' => [13, '木'], '薇' => [19, '木'], '莲' => [17, '木'], '莎' => [13, '木'], '琼' => [20, '木'], '茜' => [12, '木'], '芸' => [10, '木'], '花' => [10, '木'], '蓓' => [16, '木'], '茵' => [12, '木'], '娇' => [15, '木'], '婕' => [11, '木'], '蕙' => [18, '木'], '蕊' => [18, '木'], '婧' => [11, '木'], '杏' => [7, '木'], '萱' => [15, '木'], '筱' => [13, '木'], '茹' => [12, '木'], '荟' => [19, '木'], '芊' => [9, '木'], '笛' => [11, '木'], '筝' => [14, '木'], '姣' => [9, '木'], '柳' => [9, '木'], '卉' => [5, '木'], '芷' => [10, '木'], '荔' => [12, '木'], '葳' => [15, '木'], '蘅' => [22, '木'], '薪' => [19, '木'], '萃' => [14, '木'], '苒' => [11, '木'], '姬' => [9, '木'], '棉' => [12, '木'], '桃' => [10, '木'], '鹃' => [18, '木'], '嬅' => [15, '火'], '竞' => [19, '木'], '蕴' => [22, '木'], '企' => [6, '木'], '舰' => [20, '木'], '菱' => [14, '木'], '苾' => [11, '木'], '桓' => [10, '木'], '慕' => [15, '水'], '帼' => [14, '木'], '蒂' => [15, '木'], '芽' => [10, '木'], '何' => [7, '木'], '楣' => [13, '木'], '肖' => [9, '木'], '菀' => [14, '木'], '桑' => [10, '木'], '苑' => [11, '木'], '倚' => [10, '木'], '蒲' => [16, '木'], '蔼' => [22, '木'], '旎' => [11, '木'], '芫' => [10, '木'], '檬' => [18, '木'], '芯' => [10, '木'], '苓' => [11, '木'], '霞' => [17, '水'], '莹' => [15, '木'], '莺' => [21, '木'], '绮' => [14, '木'], '骞' => [20, '木'], '宛' => [8, '土'], '丫' => [3, '土'], '岑' => [7, '木'], '玛' => [15, '水'], '霓' => [16, '水'], '盼' => [9, '水'], '湄' => [13, '水'], '汕' => [7, '水'], '华' => [14, '水'], '斌' => [11, '水'], '希' => [7, '水'], '方' => [4, '水'], '帆' => [6, '水'], '骅' => [22, '水'], '秉' => [8, '水'], '阁' => [14, '水'], '藩' => [21, '水'], '穗' => [17, '水'], '服' => [7, '水'], '荆' => [12, '水'], '范' => [15, '水'], '萍' => [14, '水'], '苗' => [11, '水'], '明' => [8, '火'], '辉' => [15, '水'], '兴' => [15, '水'], '红' => [9, '水'], '豪' => [14, '水'], '鸣' => [14, '水'], '晖' => [13, '水'], '普' => [12, '水'], '马' => [10, '水'], '香' => [9, '水'], '昶' => [9, '土'], '禧' => [17, '水'], '忻' => [8, '水'], '号' => [13, '水'], '现' => [12, '水'], '伟' => [11, '土'], '安' => [6, '土'], '衡' => [16, '土'], '雍' => [13, '土'], '冈' => [8, '水'], '逵' => [15, '土'], '欧' => [15, '土'], '武' => [8, '水'], '雄' => [12, '水'], '兵' => [7, '水'], '虎' => [8, '水'], '彪' => [11, '水'], '佩' => [8, '金'], '豹' => [10, '水'], '皇' => [9, '水'], '琥' => [13, '水'], '勐' => [10, '水'], '平' => [5, '水'], '海' => [11, '水'], '民' => [5, '水'], '波' => [9, '水'], '浩' => [11, '水'], '子' => [3, '水'], '宏' => [7, '水'], '一' => [1, '土'], '江' => [7, '水'], '福' => [14, '水'], '学' => [16, '水'], '涛' => [18, '水'], '云' => [12, '水'], '鹏' => [19, '水'], '汉' => [15, '水'], '敏' => [11, '水'], '清' => [12, '水'], '泉' => [9, '水'], '凡' => [3, '水'], '博' => [12, '水'], '霖' => [16, '水'], '保' => [9, '水'], '雷' => [13, '水'], '源' => [14, '水'], '雨' => [8, '水'], '润' => [16, '水'], '滨' => [18, '水'], '邦' => [11, '水'], '恒' => [10, '水'], '水' => [4, '水'], '厚' => [9, '水'], '洋' => [10, '水'], '汝' => [7, '水'], '孟' => [8, '水'], '潮' => [16, '水'], '冰' => [6, '水'], '雪' => [11, '水'], '怀' => [20, '水'], '满' => [15, '水'], '发' => [12, '水'], '伯' => [7, '水'], '洲' => [10, '水'], '鹤' => [21, '水'], '化' => [4, '水'], '百' => [6, '水'], '法' => [9, '水'], '会' => [13, '水'], '孝' => [7, '水'], '治' => [9, '水'], '名' => [6, '水'], '甫' => [7, '水'], '宪' => [16, '水'], '渊' => [12, '水'], '品' => [9, '水'], '印' => [6, '水'], '澄' => [16, '水'], '沛' => [8, '水'], '涵' => [12, '水'], '淳' => [12, '水'], '放' => [8, '水'], '淇' => [12, '水'], '浚' => [11, '水'], '津' => [10, '水'], '泓' => [9, '水'], '衍' => [9, '水'], '添' => [12, '水'], '寒' => [12, '水'], '游' => [13, '水'], '望' => [11, '水'], '挥' => [13, '水'], '后' => [6, '水'], '协' => [8, '水'], '露' => [20, '水'], '瀚' => [20, '水'], '合' => [6, '水'], '渭' => [13, '水'], '北' => [5, '水'], '濂' => [17, '水'], '滔' => [14, '水'], '渝' => [13, '水'], '好' => [6, '水'], '效' => [10, '水'], '霆' => [15, '水'], '深' => [12, '水'], '鱼' => [11, '水'], '比' => [4, '水'], '门' => [8, '水'], '泗' => [9, '水'], '沅' => [8, '水'], '丕' => [5, '水'], '班' => [11, '水'], '微' => [13, '水'], '淞' => [12, '水'], '汇' => [13, '水'], '沪' => [15, '水'], '于' => [3, '土'], '伏' => [6, '水'], '收' => [6, '水'], '澔' => [16, '火'], '湖' => [13, '水'], '济' => [18, '水'], '并' => [8, '水'], '浦' => [11, '水'], '幸' => [8, '水'], '曼' => [11, '水'], '访' => [11, '水'], '澍' => [16, '水'], '赟' => [16, '火'], '勃' => [9, '水'], '备' => [12, '水'], '汛' => [7, '水'], '孩' => [9, '水'], '雯' => [12, '水'], '耘' => [10, '水'], '溪' => [14, '金'], '眉' => [9, '水'], '郁' => [13, '水'], '贝' => [7, '水'], '濠' => [18, '水'], '彭' => [12, '水'], '冯' => [12, '水'], '涌' => [11, '水'], '弼' => [12, '水'], '灏' => [25, '水'], '巴' => [4, '水'], '默' => [16, '水'], '潇' => [20, '水'], '汶' => [8, '水'], '弦' => [8, '水'], '淑' => [12, '水'], '末' => [5, '水'], '艾' => [8, '土'], '影' => [15, '土'], '盈' => [9, '水'], '忆' => [17, '土'], '圃' => [10, '水'], '妍' => [7, '水'], '娥' => [10, '水'], '妃' => [6, '水'], '霏' => [16, '水'], '妹' => [8, '水'], '含' => [7, '水'], '凝' => [16, '水'], '妤' => [7, '水'], '漫' => [15, '水'], '霜' => [17, '水'], '咪' => [9, '水'], '墨' => [15, '水'], '蜜' => [14, '水'], '霁' => [22, '水'], '函' => [8, '水'], '沫' => [9, '水'], '绵' => [14, '水'], '频' => [16, '水'], '滟' => [32, '水'], '幻' => [4, '水'], '浣' => [11, '水'], '璠' => [17, '水'], '迈' => [20, '水'], '玄' => [5, '水'], '娆' => [15, '水'], '娉' => [10, '水'],
    ];

    /**
     * 定字起名
     * @return array
     */
    public function index()
    {
        $data = [
            'xing' => input('xing', 'trim'),
            'zi' => input('zi', 'trim'),
            'type' => input('type', 1, 'trim,intval'),
            'page' => input('page', 1, 'trim,intval'),
            'limit' => input('limit', 36, 'trim,intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓氏' => ['require', 'chs', 'length:1,2'],
                'zi|字' => ['require', 'chs', 'length:1'],
                'type|定字类型' => ['require', 'number', 'length:1,2'],
            ]
        );
        // 校验
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        try {
            // 全部姓名列表
            $list = [];
            $offset = ($data['page'] - 1) * $data['limit'];
            // 分页
            $mzList = array_slice(array_keys($this->mzList), $offset, $data['limit']);
            foreach ($mzList as $each) {
                // 组合姓名
                if ($data['type'] == 1) {
                    $mingzi = $data['zi'] . $each;
                } else {
                    $mingzi = $each . $data['zi'];
                }
                $res = $this->getXmdf($data['xing'], $mingzi);
                $list[] = [
                    'xm' => $data['xing'] . $mingzi,
                    'fs' => $res,
                ];
            }
            return $list;
        } catch (Exception $e) {
            return ['status' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获得得分
     * @param string $xing
     * @param string $ming
     * @return float|int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getXmdf($xing, $ming)
    {
        $xingArr = $this->getNameFx($xing);
        $mingArr = $this->getNameFx($ming);
        $general = new General();
        $renge1 = $xingArr[0]['bihua'];
        if (count($xingArr) > 1) {
            $tiange = array_sum(array_column($xingArr, 'bihua')); // $data2['bihua'] + $data1['bihua'];
            $tiangee = $tiange;
            $renge1 = $xingArr[1]['bihua'];
        } else {
            $tiange = $xingArr[0]['bihua'] + 1;
            $tiangee = $xingArr[0]['bihua'] + 1;
        }
        $renge2 = $mingArr[0]['bihua'];
        if (count($mingArr) > 1) {
            $dige = array_sum(array_column($mingArr, 'bihua'));
            $digee = $dige;
        } else {
            $dige = 0;
            $digee = 0;
        }
        $zhongge = array_sum(array_column($xingArr, 'bihua')) + array_sum(array_column($mingArr, 'bihua'));
        $zhonggee = $zhongge;
        $zgjx = 0.01;
        $zgfs = $general->getpf($zgjx);
        // 计算三才
        $renge = $renge1 + $renge2;
        $rengee = $renge1 + $renge2;
        $waige = $zhongge - $renge;
        $waigee = $zhonggee - $rengee;
        if (count($xingArr) == 1) {
            $waige = $waige + 1;
            $waigee = $waigee + 1;
        }
        if (count($mingArr) == 1) {
            $waige = $waige + 1;
            $waigee = $waigee + 1;
        }
        // 天格
        $tiangearr = $general->getBayiInfo($tiangee);
        $tgjx = $tiangearr["jx"];
        $tgfs = $general->getpf($tgjx);
        // 人格
        $rengearr = $general->getBayiInfo($rengee);
        $rgjx = $rengearr["jx"];
        $rgfs = $general->getpf($rgjx);
        // 地格
        $digearr = $general->getBayiInfo($digee);
        $dgjx = $digearr["jx"];
        $dgfs = $general->getpf($dgjx);
        // 外格
        $waigearr = $general->getBayiInfo($waigee);
        $wgjx = $waigearr["jx"];
        $wgfs = $general->getpf($wgjx);
        // 三才吉凶
        $sancai = $general->getsancai($tiange) . $general->getsancai($renge) . $general->getsancai($dige);
        $rssancai = $general->getSanCaiInfo($sancai);
        $scjx = $rssancai["jx"];
        $jcyjx = $rssancai["jx1"];
        $cgyjx = $rssancai["jx1"];
        $rjgxjx = $rssancai["jx3"];
        // 姓名总分
        $xmdf = $tgfs / 10 + $rgfs + $dgfs + $zgfs + $wgfs / 10 + $general->getpf($scjx) / 4 + $general->getpf($jcyjx) / 4 + $general->getpf($cgyjx) / 4 + $general->getpf($rjgxjx) / 4;
        if ($zhonggee > 60) {
            $xmdf = $xmdf - 4;
        }
        $xmdf = 51 + $xmdf;
        return $xmdf;
    }

    /**
     * 获得字五行和笔画
     * @param string $str 汉字
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNameFx(string $str): array
    {
        $arr = preg_split('/(?<!^)(?!$)/u', $str);
        $result = [];
        $ziBihua = $this->mzList;
        foreach ($arr as $v) {
            if (isset($ziBihua[$v])) {
                $result[] = [
                    'bihua' => $ziBihua[$v][0],
                    'wx' => $ziBihua[$v][1],
                ];
                continue;
            }
            $info = Cnword::info($v);
            if (empty($info)) {
                $result[] = [
                    'bihua' => 10,
                    'wx' => '火',
                ];
                continue;
            } else {
                $result[] = [
                    'bihua' => $info['bihua2'],
                    'wx' => $info['wx'],
                ];
            }
        }
        return $result;
    }
}
