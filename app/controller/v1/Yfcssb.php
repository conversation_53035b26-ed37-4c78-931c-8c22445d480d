<?php
// +----------------------------------------------------------------------
// | Yfcssb.缘分测试-手百春节合作
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;

class Yfcssb
{
    /**
     * 缘分测试
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 生日
            'time' => input('time', '', 'trim'),
            // 性别 0 为男 1 为女
            'sex' => input('sex', 0, 'intval'),
            // 生日2
            'time1' => input('time1', '', 'trim'),
            // 性别2 0 为男 1 为女
            'sex1' => input('sex1', 0, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'time1|出生时间2' => ['require', 'isDateOrTime:出生时间格2'],
                'sex1|性别2' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        // 八字对象
        $bazi1 = Ex::date($data['time'])->sex($data['sex']);
        $rg1 = $bazi1->getLunarGanzhiDay()[0];
        $year1 = $bazi1->dateTime->format('Y');
        $bazi2 = Ex::date($data['time1'])->sex($data['sex']);
        $rg2 = $bazi2->getLunarGanzhiDay()[0];
        $year2 = $bazi2->dateTime->format('Y');
        // 十神
        $shiShen = BaziExt::getGodName($rg1, $rg2);

        $str = $data['sex'] . $data['sex1'];
        $res = [];
        switch ($shiShen) {
            case '正官':
                if (in_array($str, ['00', '01'])) {
                    $res = ["你的职场状态会受到对方影响，如何处理双方关系至关重要。", 75];
                } else {
                    $res = ["TA会是你情场上的指路人，助你砍除烂桃花，找到正桃花。", 71];
                }
                break;
            case '七杀':
                if ($str == '10') {
                    $res = ["天禧之神会降临在你们身边，让你们的感情生活发生变化。", 82];
                } else {
                    $res = ["TA会是你商场上的贵人，助你在生意场上叱咤风云、无往不利。", 80];
                }
                break;
            case '正财':
                if (in_array($str, ['10', '11'])) {
                    $res = ["互为财运贵人的你们，共同把握好生财时机会有意外之财。", 80];
                } else {
                    $res = ["TA会是你的喜气之神，不仅可带来财运，单身者在Ta影响下还有机会脱单。", 90];
                }
                break;
            case '偏财':
                if ($str == '10') {
                    $res = ["处理好你们的关系，对方会成为你在事业上的坚强后盾。", 79];
                } else {
                    $res = ["TA会是你的财运之神，让你2018年挣个盆满钵满。", 85];
                }
                break;
            case '正印':
                if (abs($year1 - $year2) < 6) {
                    $res = ["对方在感情方面会对你有所助益，但也可能好心办坏事。", 82];
                } else {
                    $res = ["TA会是你的贵人，在思想上引导你，事业上提拔你。", 76];
                }
                break;
            case '偏印':
                if (abs($year1 - $year2) < 6) {
                    $res = ["在脑袋空空时不妨寻求对方帮助，或许能激发自己的灵感。", 78];
                } else {
                    $res = ["TA会是引领你成长的人，是教会你领略人生百味的贵人。", 70];
                }
                break;
            case '伤官':
                if (in_array($str, ['00', '11'])) {
                    $res = ["TA会是你的竞争者，需要增强业务能力和危机意识。", 65];
                } else {
                    $res = ["TA会是你的精神伴侣，与你一起学习成长，共同进步。", 88];
                }
                break;
            case '食神':
                if (abs($year1 - $year2) < 6) {
                    $res = ["TA会是你的福气之神，让你今年的生活喜乐安康。", 90];
                } else {
                    $res = ["TA需要你的理解和帮助，你会是他成长路上的贵人。", 74];
                }
                break;
            case '比肩':
                if (in_array($str, ['01', '10'])) {
                    $res = ["TA会是你的亲密战友，互帮互助，共同进步。", 71];
                } else {
                    $res = ["TA会是你志同道合的友人，相互扶持，共生双赢。", 88];
                }
                break;
            case '劫财':
                if (in_array($str, ['00', '11'])) {
                    $res = ["TA会成为你的竞争对手，相互牵制，亦敌亦友。", 68];
                } else {
                    $res = ["TA会是你最强劲的对手，恰似棋逢对手，惺惺相惜。", 67];
                }
                break;
        }
        $res[] = $shiShen;
        return $res;
    }
}
