<?php
// +----------------------------------------------------------------------
// | Sizhuluncai 四柱论财，八字排盘 Bzpaipan
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use api\ApiResult;
use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\bazipaipan\LiuNian;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\SolarTerm;
use calendar\traits\ArrayMove;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Request;
use think\helper\Str;

class Sizhuluncai
{
    use ArrayMove;

    /**
     * @var array
     */
    protected array $orginData;

    /**
     * 基础
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 通过天干判断是什么五行关系表
     * @var string[]
     */
    protected array $tgpdwx = [
        '甲' => '木', '乙' => '木', '丙' => '火', '丁' => '火', '戊' => '土',
        '己' => '土', '庚' => '金', '辛' => '金', '壬' => '水', '癸' => '水',
    ];

    /**
     * 五行相生，只有判断同类使用
     * @var string[]
     */
    protected array $wxxs = [
        '水' => '金',
        '木' => '水',
        '火' => '木',
        '土' => '火',
        '金' => '土',
    ];

    /**
     * 天干转阴阳五行
     * @var string[]
     */
    protected array $tgZyywx = [
        '甲' => '阳木', '乙' => '阴木', '丙' => '阳火', '丁' => '阴火', '戊' => '阳土',
        '己' => '阴土', '庚' => '阳金', '辛' => '阴金', '壬' => '阳水', '癸' => '阴水',
    ];

    /**
     * 五行在每个月的旺度系数表（李氏强度表）
     * @var array
     */
    protected array $wdxsb = [
        // 0木、1火、2土、3金、4水
        '寅' => [1.571, 1.548, 0.924, 0.716, 0.862],
        '卯' => [2, 1.414, 0.5, 0.707, 1],
        '辰' => [1.166, 1.074, 1.421, 1.161, 0.8],
        '巳' => [0.862, 1.571, 1.548, 0.924, 1.716],
        '午' => [0.912, 1.7, 1.59, 0.774, 0.645],
        '未' => [0.924, 1.341, 1.674, 1.069, 0.612],
        '申' => [0.795, 0.674, 1.012, 1.641, 1.498],
        '酉' => [0.5, 0.707, 1, 2, 1.414],
        '戌' => [0.674, 1.012, 1.641, 1.498, 0.795],
        '亥' => [1.59, 0.774, 0.645, 0.912, 1.7],
        '子' => [1.414, 0.5, 0.707, 1, 2],
        '丑' => [0.898, 0.821, 1.512, 1.348, 1.041],
    ];

    /**
     * 各个地支藏干的分数表（李氏强度表）
     * @var int[]
     */
    protected array $dzcgfsb = [
        '子癸' => 100, '丑己' => 60, '寅甲' => 60, '卯乙' => 100, '辰戊' => 60, '巳丙' => 60, '午丁' => 70, '未己' => 60, '申庚' => 60,
        '酉辛' => 100, '戌戊' => 60, '亥壬' => 70, '丑癸' => 30, '寅丙' => 30, '辰乙' => 30, '巳戊' => 30, '午己' => 30, '未丁' => 30,
        '申壬' => 30, '戌辛' => 30, '亥甲' => 30, '丑辛' => 10, '寅戊' => 10, '辰癸' => 10, '巳庚' => 10, '未乙' => 10, '申戊' => 10,
        '戌丁' => 10,
    ];

    /**
     * 组合十神数据表，五行十神
     * @var array[]
     */
    protected array $zhssb = [
        '金' => ['金' => '比劫', '木' => '才财', '水' => '食伤', '火' => '官杀', '土' => '印枭',],
        '木' => ['金' => '官杀', '木' => '比劫', '水' => '印枭', '火' => '食伤', '土' => '才财',],
        '水' => ['金' => '印枭', '木' => '食伤', '水' => '比劫', '火' => '才财', '土' => '官杀',],
        '火' => ['金' => '才财', '木' => '印枭', '水' => '官杀', '火' => '比劫', '土' => '食伤',],
        '土' => ['金' => '食伤', '木' => '官杀', '水' => '才财', '火' => '印枭', '土' => '比劫',],
    ];

    /**
     * 四柱论财
     * @return ApiResult
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 用户生日，年月日，新历
            'time' => Request::param('time', '', 'trim'),
            // 性别
            'sex' => Request::param('sex', 0, 'intval'),
            // 测算订单时间
            'otime' => Request::param('otime', '', 'trim'),
            // 喜用忌闲仇五行
            'xy' => Request::param('xy', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|测试日期' => ['require', 'isDateOrTime:测试日期'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }

        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);

        $sex = $this->lunar->sex;
        // 以订单创建时间获取
        $lunar2 = Ex::date($data['otime'])->sex($sex);
        // 八字基础数据
        $base = $this->lunar->getLunarByBetween();
        // 纪年
        $jiNian = $base['jinian'];
        // 订单创建时间的纪年
        $jiNianOtime = $lunar2->getLunarTganDzhi();
        // 纪年五行
        $jnWx = $this->getJnwx($jiNian);
        // 地支十神 以及 藏干
        $godHide = $this->lunar->_getGod();
        // 十神
        $godT = $this->lunar->getGod();
        // 藏干十神带上气值
        $_god = $this->getHideGodWithQi($godHide);
        // 喜用神（喜用忌闲仇）
        $baziEx = new BaziEx($this->lunar);
        $xys = $baziEx->getXiyongJi4();
        if (!empty($xys)) {
            $arr = Utils::mbStrSplit($data['xy']);
            if (count($arr) == 5) {
                $xys['xy'] = [
                    'yong' => $arr[0], 'xi' => $arr[1], 'xian' => $arr[4], 'qiu' => $arr[3], 'ji' => $arr[2],
                ];
            }
        }
        // 大运
        $dayun = $this->getDayunWith($xys);
        // 神煞
        $shensha = (new ShaShen())->detail($jiNian, $sex);
        // 流年，当前流年
        $liunian = $this->getBasePan($jiNianOtime['y'], $jiNian);
        // 流年，所有流年
        $liunianYears = $this->getLiunianYears($xys);

        // 结果
        $result = [
            // 基础数据
            'base' => $base,
            // 纪年五行
            'jnwx' => $jnWx,
            // 纪年阴阳
            'jnyy' => $this->getJnYy($jiNian),
            // 天干十神
            'god' => $godT,
            // 藏干十神带上气值
            '_god' => $_god,
            // 喜用神（喜用忌闲仇）
            'xys' => $xys,
            // 纳音
            'nayin' => $this->lunar->getNayin(),
            // 旺相休囚死
            'wxxqs' => $this->lunar->getWxxqs(),
            // 大运，Bzpaipan里的fate
            'dayun' => $dayun,
            // 流年，当前流年
            'liunian' => $liunian,
            // 流年，所有流年
            'liunian_years' => $liunianYears,
            // 神煞
            'shensha' => $shensha,
            // 调侯
            'diaohou' => $this->diaohou($jiNian, $_god),
            // 流通
            'liutong' => $this->liutong($jiNian, $jnWx),
            // 求财方式
            'qcfs' => [
                // 能量度表
                'nldb' => [
                    // 五行分配
                    'wxfp' => $this->qcfsNldbWxfp(),
                    // 六亲形式
                    'lqxs' => $this->qcfsNldbLqxs($jnWx['d']['wx'][0]),
                    // 阴阳分配
                    'yyfp' => $this->qcfsNldbYyfp($jiNian),
                    // 五行个数分配
                    'wxgsfp' => $this->qcfsNldbWxgsfp($jiNian),
                    // 阴阳五行形势
                    'yywxxs' => $this->qcfsNldbYywxxs($jiNian),
                    // 十神形势
                    'ssxs' => $this->qcfsNldbSsxs($jiNian),
                    // 同异类分配
                    'tylfp' => $this->qcfsNldbTylfp($jnWx),
                ],
                // 能量度表-李氏
                'nldb_li' => [
                    // 五行分配
                    'wxfp' => $this->qcfsNldbLiWxfp($jiNian, $jnWx, $_god),
                    // 六亲形式
                    'lqxs' => $this->qcfsNldbLiLqxs($jnWx['d']['wx'][0], $jiNian, $jnWx, $_god),
                    // 阴阳分配
                    'yyfp' => $this->qcfsNldbLiYyfp($jiNian, $jnWx, $_god),
                    // 五行个数分配，未做
                    'wxgsfp' => [],
                    // 阴阳五行形势
                    'yywxxs' => $this->qcfsNldbLiYywxxs($jiNian, $_god),
                    // 十神形势
                    'ssxs' => $this->qcfsNldbLiSsxs($jiNian, $jnWx, $_god),
                    // 同异类分配
                    'tylfp' => $this->qcfsNldbLiTylfp($jiNian, $jnWx, $_god),
                ],
            ],
            // 财富所属
            'cfss' => $this->cfss($jiNian, $godT, $_god, $xys),
            // 财源多样性
            'cfdyx' => $this->cfdyx($godT, $_god),
            // 风险偏好
            'fxph' => $this->fxph($godT, $_god),
            // 事业与财富贵人
            'syycfgr' => $this->syycfgr($jiNian, $jnWx),
            // 大财横财机会
            'dchcjh' => $this->dchcjh($jiNian, $jnWx, $_god, $xys, $liunianYears, $dayun),
            // 事业财富相关神煞
            'sycfxgss' => $this->sycfxgss($jiNian, $shensha),
            // 未来大运流年评估（30年）
            'wldylnpg' => $this->wldylnpg($lunar2, $liunianYears, $dayun),
            // 最佳流年
            // 谨慎流年
            // 适合楼层
            'shlc' => $this->shlc($xys),
        ];
        // 担财能力
        $result['dcnl'] = $this->dcnl($result['qcfs']);
        // 求财方式结果
        $result['qcfs_jg'] = $this->qcfsJg($result['qcfs']);
        // 求财意向结果
        $result['qcyx_jg'] = $this->qcyxJg($result['qcfs'], $jnWx, $_god);
        // 推荐发展地域
        $result['tjfzdy'] = $this->tjfzdy($result['qcfs'], $xys);
        return ApiResult::success($result);
    }

    /**
     * 适合楼层
     * @param array $xys 喜用神
     * @return array
     */
    protected function shlc(array $xys)
    {
        // 适合楼层结果表
        $shlcjgb = [
            '水' => [
                'yllc' => '尾数是1、6的楼层，如1楼、6楼、11楼、16楼、21楼、26楼。',
                'ylyy' => '水向低流，有润下之性，喜水的人宜住低层，且楼层五行属水，有助于增强命理中的水元素。',
                'bllc' => '尾数为2、7的楼层，如2楼、7楼、12楼、17楼、22楼、27楼。',
                'blyy' => '高层楼层五行属火，可能克制命理中的水元素；过于干燥的楼层也不利于喜水之人。',
            ],
            '火' => [
                'yllc' => '尾数为2、7的楼层，如2楼、7楼、12楼、17楼、22楼、27楼。',
                'ylyy' => '火向上升，有炎上之性，喜火的人宜住高层，且楼层五行属火，与命理五行相契合。',
                'bllc' => '尾数是1、6的楼层，如1楼、6楼、11楼、16楼、21楼、26楼。',
                'blyy' => '水克火，低层楼层五行属水，可能克制命理中的火元素；采光不足的楼层也不利于火的生发。',
            ],
            '木' => [
                'yllc' => '尾数为3、8的楼层，如3楼、8楼、13楼、18楼、23楼、28楼。',
                'ylyy' => '木向外生长，有升腾之性，适宜住中高层，且楼层五行属木，有助于命理五行的平衡与增强。',
                'bllc' => '尾数4或9的楼层，如4楼、9楼、14楼、19楼、24楼、29楼。',
                'blyy' => '过于阴暗潮湿的楼层，以及可能受到金属物品过多影响的楼层。',
            ],
            '金' => [
                'yllc' => '尾数为4、9的楼层，如4楼、9楼、14楼、19楼、24楼、29楼。',
                'ylyy' => '金忌压，住得太低会有被埋压之感，因此适合选择中等或稍高楼层，且楼层五行属金，与命理五行相配。',
                'bllc' => '尾数为3、8的楼层，如3楼、8楼、13楼、18楼、23楼、28楼。',
                'blyy' => '中高层楼层五行属木，可能克制命理中的金元素；火源过多也可能对金元素造成不利影响（火克金）。',
            ],
            '土' => [
                'yllc' => '尾数为5、0的楼层，如5楼、10楼、15楼、20楼、25楼、30楼。',
                'ylyy' => '土如坚固之城，有藏纳之性，适宜住在中底层，且楼层五行属土，与命理五行相符。',
                'bllc' => '尾数为1或6的低层楼层（属水）如1楼、6楼、11楼、16楼等。',
                'blyy' => '过多水气可能影响命理中的土元素；嘈杂的环境也不利于土的安定。',
            ],
        ];
        $result = [
            'yong_wx' => $xys['xy']['yong'],
            'yong' => $shlcjgb[$xys['xy']['yong']],
            'xi_wx' => $xys['xy']['xi'],
            'xi' => $shlcjgb[$xys['xy']['xi']],
        ];
        return $result;
    }

    /**
     * 未来大运流年评估（30年）
     * @param Ex $lunar2 订单创建时间
     * @param array $liunianYears
     * @param array $dayun
     * @return array
     */
    protected function wldylnpg(Ex $lunar2, array $liunianYears, array $dayun)
    {
        // 当前订单创建年份
        $orderY = (int)$lunar2->dateTime->format('Y');
        // 大运30年数据
        $dy30 = [];
        foreach ($liunianYears['y'] as $year => $item) {
            if ($item['y'] >= $orderY && $item['y'] <= ($orderY + 30)) {
                $dy30[] = [
                    'y' => $item['y'],
                    'fen' => $item['fen'],
                    'xx_fen' => $item['xx_fen'] ?? 0,
                    'act_fen' => $item['act_fen'] ?? 0,
                ];
            }
        }
        // 大运总评
        $dyzp = [];
        // 大运30年结束最大年份
        $maxY = $orderY + 30 + 10 - ($orderY + 30) % 10;
        foreach ($dayun['dyzp'] as $item) {
            if (
                // 加9年后小于订单创建年份不要
                $item['year'] + 9 < $orderY ||
                // 超过30年不要
                $item['year'] > $maxY
            ) {
                continue;
            }
            $dyzp[] = [
                'fen' => round(array_sum($item['fen']) / 3, 1),
                'tgdz' => $item['tgdz'],
                'year' => [$item['year'], $item['year'] + 9],
                'wx' => $item['wx'],
                'yun' => $item['yun'],
            ];
        }
        $result = [
            'dy_years' => $dy30,
            '30dyzp' => $dyzp,
        ];
        return $result;
    }

    /**
     * 风险偏好
     * @param array $godT 天干十神
     * @param array $_god 藏干十神
     * @return array
     */
    protected function fxph(array $godT, array $_god)
    {
        $result = [];
        // 十神风险分数表
        $ssfxfsb = [
            '正印' => 2, '正财' => 1, '食神' => 0, '正官' => 1, '偏印' => 0.5,
            '劫财' => -1, '伤官' => -2, '七杀' => -2, '比肩' => 0, '偏财' => -1.5,
        ];
        // 等级结果表
        $djjgb = [
            '保守型' => '根据命局十神配比，命主的风险偏好类型为<b>保守型</b>。不想承担任何风险，投资理财的目的在于保值，适合谋取稳定职业，或者购买银行储蓄、货币基金、国债等产品。',
            '积极型' => '根据命局十神配比，命主的风险偏好类型为<b>积极型</b>。倾向于有风险高收益的理财投资，对风险并不惧怕，适合创业，或者尝试股票或偏股基金等投资方式。',
            '平衡型' => '根据命局十神配比，命主的风险偏好类型为<b>平衡型</b>。会综合考虑风险和收益，风险承受能力适中，适合谋取稳定职业，或者尝试货币基金+股票/外汇等组合方式投资。',
            '稳健型' => '根据命局十神配比，命主的风险偏好类型为<b>稳健型</b>。较害怕风险，但是又希望保本的基础上有一定的收益，适合谋取稳定职业，或者买债券、银行中短期理财产品等。',
        ];

        // 第一步，统计十神相关个数
        // 原局十神四柱个数统计
        $yjsssztj = [
            'year' => [
                $godT['year'] => 1,
            ],
            'month' => [
                $godT['month'] => 1,
            ],
            'day' => [
                '命主' => 0,
            ],
            'hour' => [
                $godT['hour'] => 1,
            ],
        ];
        // 原局十神个数统计
        $yjsstj = [];
        foreach ($godT as $key => $name) {
            if (isset($yjsstj[$name])) {
                $yjsstj[$name]++;
            } else {
                $yjsstj[$name] = 1;
            }
        }
        foreach ($_god as $key => $item) {
            if (isset($yjsssztj[$key][$item['god'][0]])) {
                $yjsssztj[$key][$item['god'][0]]++;
            } else {
                $yjsssztj[$key][$item['god'][0]] = 1;
            }
            if (isset($yjsstj[$item['god'][0]])) {
                $yjsstj[$item['god'][0]]++;
            } else {
                $yjsstj[$item['god'][0]] = 1;
            }
        }

        // 第二步，根据 十神风险分数表 计算总分
        $fxfs = 0;
        foreach ($yjsstj as $name => $num) {
            if ($name == '命主') {
                continue;
            }
            $fxfs += $ssfxfsb[$name] * $num;
        }

        // 结果解释
        $djjs = '';
        // 保守型：得分在4及以上【大于等于4】
        if ($fxfs >= 4) {
            $djjs = $djjgb['保守型'];
        } elseif ($fxfs >= 1 && $fxfs < 4) { // 稳健型（稳定型）：得分在1-3之间【等于大于1且小于等于3】
            $djjs = $djjgb['稳健型'];
        } elseif ($fxfs > -2 && $fxfs < 1) { // 平衡型：得分在-1到1之间【大于-2 且小于1】
            $djjs = $djjgb['平衡型'];
        } elseif ($fxfs <= -2) { // 积极型：得分在-2及以下【小于等于-2】
            $djjs = $djjgb['积极型'];
        }
        foreach ($yjsssztj as $type => $item) {
            $arr = array_keys($item);
            foreach ($arr as $k => $name) {
                if (in_array($name, ['七杀', '伤官', '偏财', '劫财'])) {
                    $arr[$k] = "<b class=\"god\">{$name}</b>";
                }
            }
            $result['god'][$type] = $arr;
        }
        $result['djjs'] = $djjs;
        return $result;
    }

    /**
     * 推荐发展地域
     * @param array $qcfs 求财方式算法
     * @param array $xys 喜用神
     * @return array
     */
    protected function tjfzdy(array $qcfs, array $xys)
    {
        $result = [];
        // 五行与发展地区关系表
        $wxyfzdyb = [
            '火' => '或者选择环境较为稳定，工作节奏适中，具有熟人社会特点的城市发展，将更有利于事业与财运。',
            '木' => '或者选择产业链配套较为完善，贸易发达，中小企业蓬勃发展的区域商业中心，如：上海、深圳、广州、苏州、杭州等城市发展，将更有利于事业与财运。',
            '土' => '或者选择充满创业氛围、经济活力的城市，如：深圳、北京、杭州、成都、长沙等城市发展，将更有利于事业与财运。',
            '水' => '或者选择经济发展较为成熟，资源配套比较齐全的中心城市，如首都、直辖市、省会等城市发展，将更有利于事业与财运。',
            '金' => '或者选择商业气息浓厚，民间资本蓬勃发展的城市，如长江中下游、珠三角、东南沿海地区的城市发展，将更有利于事业与财运。',
        ];
        // 求财方式-李氏-五行分配
        $wxfp = $qcfs['nldb_li']['wxfp'];
        uasort($wxfp, function ($a, $b) {
            if ($a['bfb'] == $b['bfb']) {
                return 0;
            }
            return ($a['bfb'] > $b['bfb']) ? -1 : 1;
        });
        $wxfpBfyMax = key($wxfp);

        $js = "综合该命局的喜用五行及性格特质，推荐命主优先选取五行性质偏“<b class=\"tjfzdy1\">{$xys['xy']['yong']}</b>”或“<b class=\"tjfzdy2\">{$xys['xy']['xi']}</b>”的城市，";
        $js .= $wxyfzdyb[$wxfpBfyMax];
        $result['xys'] = [
            $xys['xy']['yong'],
            $xys['xy']['xi'],
        ];
        $result['js'] = $js;
        return $result;
    }

    /**
     * 大财横财机会
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @param array $_god 藏干十神
     * @param array $xys 喜用神
     * @param array $liunianYears 所有流年列表
     * @param array $dayun 大运
     * @return array
     */
    protected function dchcjh(array $jiNian, array $jnWx, array $_god, array $xys, array $liunianYears, array $dayun)
    {
        // 定义相冲关系
        $xcgxb = [
            '丑' => '未',
            '辰' => '戌',
            '未' => '丑',
            '戌' => '辰',
        ];
        // 是否喜用神为才财
        $xyscc = $xys['shen']['yong'] == '才财' || $xys['shen']['xi'] == '才财';
        // 判断十神是否为财星
        $sscxList = [
            'y' => 0,
            'm' => 0,
            'd' => 0,
            'h' => 0,
        ];
        $sscxNameList = [
            'y' => '',
            'm' => '',
            'd' => '',
            'h' => '',
        ];
        foreach ($_god as $k => $item) {
            foreach ($item['god'] as $k2 => $name) {
                if (in_array($name, ['正财', '偏财'])) {
                    $km = match ($k) {
                        'year' => 'y',
                        'month' => 'm',
                        'day' => 'd',
                        'hour' => 'h',
                    };
                    $sscxList[$km] = 1;
                    $sscxNameList[$km] = $name;
                }
            }
        }
        // 纪年里是否有财库地支
        $jnckdzList = [];
        foreach ($jiNian as $k => $item) {
            if (in_array($item[1], ['辰', '戌', '丑', '未'])) {
                $jnckdzList[$k] = $item[1];
            }
        }
        // 当前岁数>=22的时候，就是从当前时间年份开始
        $isDy22 = date('Y') - $this->lunar->dateTime->format('Y') >= 22;
        // 从所有流年里找出22~60的干支数据
        $lngz = [];
        foreach ($liunianYears['y'] as $nian => $item) {
            $nj = $nian - $this->lunar->dateTime->format('Y');
            if ($isDy22) {
                if ($nian >= date('Y') && $nj <= 60) {
                    $lngz[$nian] = [
                        'gz' => $item['gz'],
                        'yun' => $item['yun'],
                    ];
                }
            } else {
                if ($nj >= 22 && $nj <= 60) {
                    $lngz[$nian] = [
                        'gz' => $item['gz'],
                        'yun' => $item['yun'],
                    ];
                }
            }
        }
        // 相冲(原局地支和流年地支)的地支
        $xcdz = [];
        foreach ($jiNian as $k => $item) {
            // 只要一组
            if (!empty($xcdz)) {
                break;
            }
            // 确定纪年的地支是否在相冲表，不在就不处理
            if (!isset($xcgxb[$item[1]])) {
                continue;
            }
            foreach ($lngz as $nian => $item2) {
                // 流年里的干支1和相冲关系表一样，就是相冲
                if ($item2['gz'][1] == $xcgxb[$item[1]]) {
                    $xcdz = [
                        'liunian' => $item2['gz'],
                        'liunian_yun' => $item2['yun'],
                        'jinian_name' => $item[1],
                        'jinian_type' => $k,
                        'jinian_typename' => match ($k) {
                            'y' => '年',
                            'm' => '月',
                            'd' => '日',
                            'h' => '时',
                        },
                    ];
                    break;
                }
            }
        }
        $result = [];
        // 结果解释
        $jgjs = [];
        // 情况1，财星为喜用且，年支无财星，且月日时有相冲
        if ($xyscc && $sscxList['y'] == 0 && !empty($xcdz)) {
            $jgjs[] = [
                "该命局有财库<b>{$xcdz['jinian_name']}{$jnWx[$xcdz['jinian_type']]['wx'][1]}</b>，坐落于<b>{$xcdz['jinian_typename']}</b>支。",
                '财库就是指财星的库地，是财待时而发的地方。命中有财库的人，更容易有着较多的积蓄，以及发大财的可能。原局或者大运、流年见辰土可冲开财库。',
            ];
        }
        // 情况2，财星为喜用，且年支有财星，且月日时支有冲
        if ($xyscc && $sscxList['y'] && !empty($xcdz)) {
            $jgjs[] = [
                "该命局有财库<b>{$xcdz['jinian_name']}{$jnWx[$xcdz['jinian_type']]['wx'][1]}</b>，坐落于<b>{$xcdz['jinian_typename']}</b>支。",
                '财库就是指财星的库地，是财待时而发的地方。命中有财库的人，更容易有着较多的积蓄，以及发大财的可能。原局或者大运、流年见辰土可冲开财库。',
            ];
            $jgjs[] = [
                "该命局年柱见<b>{$sscxNameList['y']}</b>，且为喜用神。",
                '年柱的财代表祖上之财，或者是大财。如果年柱财为喜且不被破坏者，则代表命主财泽深厚，容易出生富贵之家，或早年得父祖之辈助力。',
            ];
        }
        // 情况3，财星为喜用，且年支有财星，且月日时支无冲
        if ($xyscc && $sscxList['y'] && empty($xcdz)) {
            $jgjs[] = [
                "该命局年柱见<b>{$sscxNameList['y']}</b>，且为喜用神。",
                '年柱的财代表祖上之财，或者是大财。如果年柱财为喜且不被破坏者，则代表命主财泽深厚，容易出生富贵之家，或早年得父祖之辈助力。',
            ];
        }
        if (empty($jgjs)) {
            $jgjs[] = [
                '在您的命局中，并未显现出明显的财库特征，这并不意味着财运不佳，而是提示您财富积累的方式可能较为特别，需注重开源节流（一点点的积累，非必要不支出），通过智慧与努力来开创财富之路。建议多关注行业动态，把握机遇，同时注重个人能力的培养与提升，以稳健的步伐逐步积累财富。',
            ];
        }
        if (empty($xcdz)) {
            $result['liunian'] = [];
            $result['dayun'] = [];
        } else {
            $result['liunian'] = $xcdz['liunian'];
            $result['dayun'] = preg_split('/(?<!^)(?!$)/u', $dayun['eight']['chronlogy_year'][$xcdz['liunian_yun']]);
        }
        $result['jgjs'] = $jgjs;
        return $result;
    }

    /**
     * 担财能力
     * @param array $qcfs 求财方式算法
     * @return array
     */
    protected function dcnl(array $qcfs)
    {
        // 同类异类
        $tylfp = $qcfs['nldb']['tylfp'];
        // 同类综合
        $tongZhong = $tylfp['tong_zhong'];
        // 异类综合
        $yiZhong = $tylfp['yi_zhong'];
        // 差值=同类的百分百-异类的百分百
        $diff = round(bcsub($tongZhong['bfb'], $yiZhong['bfb'], 2), 2);

        $result = [
            // 旺衰
            'ws' => '',
            // 担财等级
            'dcdj' => '',
            // 解释
            'js' => [],
        ];
        // 大于等于60
        if ($diff >= 60) {
            $result = [
                'ws' => '极强',
                'dcdj' => '很强',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富的过程中，遇到合适的时机，可以当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。',
                ],
            ];
        } elseif ($diff <= -60) { // 小于等于-60
            $result = [
                'ws' => '极弱',
                'dcdj' => '较弱',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富的过程中，遇到合适的时机，可以当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。',
                ],
            ];
        } elseif ($diff > 15 && $diff < 30) { // 大于14-小于30
            $result = [
                'ws' => '偏强',
                'dcdj' => '较强',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富的过程中，遇到合适的时机，可以当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。',
                ],
            ];
        } elseif ($diff <= -14 && $diff > -30) { // 小于等于负-14到大于负-30
            $result = [
                'ws' => '偏弱',
                'dcdj' => '一般',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富过程中，多借力平台、团队与伙伴的力量；在面对机会时，可以通过听取多方建议，提升出击的把握和信心。',
                ],
            ];
        } elseif ($diff >= -14 && $diff <= 14) { // 等于大于负14~小于等于14
            $result = [
                'ws' => '平和',
                'dcdj' => '较强',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富的过程中，遇到合适的时机，可以当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。',
                ],
            ];
        } elseif ($diff >= 30 && $diff < 60) { // 大于等于30-60
            $result = [
                'ws' => '身强',
                'dcdj' => '很强',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富的过程中，遇到合适的时机，可以当机立断地迅抓住机遇，让事业更上一层楼。此外也要克服自大、武断等心理，避免因人际因素而受到损失。',
                ],
            ];
        } elseif ($diff <= -30 && $diff > -60) { // 小于等于-30，大于-60
            $result = [
                'ws' => '身弱',
                'dcdj' => '较弱',
                'js' => [
                    '命局的身旺弱代表着命局的担财能力。对与普通格来说，在命局中财富确定的情况下，身越旺，越可担财，特殊格不受该限制。',
                    '基于上述，建议命主在追求事业与财富过程中，多借力平台、团队与伙伴的力量；在面对机会时，可以通过听取多方建议，提升出击的把握和信心。',
                ],
            ];
        }
        return $result;
    }

    /**
     * 事业财富相关神煞
     * @param array $jiNian 纪年
     * @param array $shensha 神煞
     * @return array
     */
    protected function sycfxgss(array $jiNian, array $shensha)
    {
        // 空亡表
        $kwb = [
            // 地支 => 每一柱的天干地支，也叫六十甲子
            '戌,亥' => ['甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉'],
            '申,酉' => ['甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未'],
            '午,未' => ['甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳'],
            '辰,巳' => ['甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯'],
            '寅,卯' => ['甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑'],
            '子,丑' => ['甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥'],
        ];
        // 金舆表
        $jinyub = [
            // 年天干+日柱天干 => 地支(不限那一柱)
            '甲' => '辰', '乙' => '巳', '丙' => '未', '戊' => '未', '丁' => '申',
            '己' => '申', '庚' => '戌', '辛' => '亥', '壬' => '丑', '癸' => '寅',
        ];

        // 第一步，去查找 "午,未" 2个子分别在纪年的哪个位置，并且查找每一柱的天干地支是否有匹配的，匹配规则是 “天干+地支”
        // 匹配到的空亡位置
        $kwwz = [];
        foreach ($kwb as $str => $item) {
            $strArr = explode(',', $str);
            // 匹配
            foreach ($jiNian as $k => $v) {
                if (in_array($v[1], $strArr) && (in_array("{$jiNian['y'][0]}{$jiNian['y'][1]}", $item) || in_array("{$jiNian['d'][0]}{$jiNian['d'][1]}", $item))) {
                    $kwwz[] = $k;
                }
            }
        }
        // 没匹配到，不用输出
        if (empty($kwwz)) {
            return [];
        }
        // 第二步，找到对应神煞，增加一组“空亡”，多组直接去重合并
        // 汇总后的神煞列表
        $sslist = ['空亡'];
        foreach ($kwwz as $k) {
            $sslist = array_merge($sslist, $shensha[$k]);
        }
        // 判断是不是要加入 金舆
        // 拿年天干去金舆表查找，找到了判断对应的值是不是和日地支相等
        // 拿日天干去金舆表查找，找到了判断对应的值是不是和日地支相等
        if (
            (isset($jinyub[$jiNian['y'][0]]) && $jiNian['y'][0] == $jiNian['d'][1]) ||
            (isset($jinyub[$jiNian['d'][0]]) && $jiNian['d'][0] == $jiNian['d'][1])
        ) {
            $sslist[] = '金舆';
        }
        // 第三步，匹配文案
        $result = [];
        foreach ($sslist as $name) {
            if (in_array($name, ['将星'])) {
                $result[1] = [
                    'id' => 1,
                    'name' => [$name],
                    'wa' => '代表命主易掌握权势，有老板或主管之命，具有领导力、威慑力、易被人信赖。',
                    'wz' => "该命局带<b>{$name}</b>，代表命主易掌握权势，有老板或主管之命，具有领导力、威慑力、易被人信赖。",
                ];
            }
            if (in_array($name, ['金舆'])) {
                $result[2] = [
                    'id' => 2,
                    'name' => [$name],
                    'wa' => '代表命主能得配偶或异性之助。',
                    'wz' => "该命局带<b>{$name}</b>，代表命主能得配偶或异性之助。",
                ];
            }
            if (in_array($name, ['空亡', '华盖'])) {
                $result[3] = [
                    'id' => 3,
                    'name' => array_merge($result[3]['name'] ?? [], [$name]),
                    'wa' => '代表命主聪明、孤独、有才华，在玄学、宗教方面有较大的天赋和兴趣。',
                ];
                $nameStr = implode('、', $result[3]['name']);
                $result[3]['wz'] = "该命局带<b>{$nameStr}</b>，代表命主聪明、孤独、有才华，在玄学、宗教方面有较大的天赋和兴趣。";
            }
            if (in_array($name, ['太极贵人', '文昌贵人', '德秀贵人', '词馆', '三奇贵人'])) {
                $result[4] = [
                    'id' => 4,
                    'name' => array_merge($result[4]['name'] ?? [], [$name]),
                    'wa' => '代表命主在才华和学识方面天赋较佳，易于在创作上扬名。',
                ];
                $nameStr = implode('、', $result[4]['name']);
                $result[4]['wz'] = "该命局带<b>{$nameStr}</b>，代表命主在才华和学识方面天赋较佳，易于在创作上扬名。";
            }
            if (in_array($name, ['驿马'])) {
                $result[5] = [
                    'id' => 5,
                    'name' => [$name],
                    'wa' => '代表命主事业奔波和调动较多，可能会向远方发展。',
                    'wz' => "该命局带<b>{$name}</b>，代表命主事业奔波和调动较多，可能会向远方发展。",
                ];
            }
            if (in_array($name, ['福星贵人', '天乙贵人', '月德贵人', '天德贵人'])) {
                $result[6] = [
                    'id' => 6,
                    'name' => array_merge($result[6]['name'] ?? [], [$name]),
                    'wa' => '代表命主一生人缘佳，有福气，遇事有人解救危难，化险为夷。',
                ];
                $nameStr = implode('、', $result[6]['name']);
                $result[6]['wz'] = "该命局带<b>{$nameStr}</b>，代表命主一生人缘佳，有福气，遇事有人解救危难，化险为夷。";
            }
        }
        $result = array_values($result);
        return $result;
    }

    /**
     * 财富所属
     * @param array $jiNian 纪年
     * @param array $godT 天干十神
     * @param array $_god 藏干十神
     * @param array $xys 喜用神
     * @return array
     */
    protected function cfss(array $jiNian, array $godT, array $_god, array $xys)
    {
        // 第一步，判断是否需要输出整块财富所属
        // 喜神
        $xs = $xys['shen']['xi'];
        // 用神
        $yong = $xys['shen']['yong'];
        // 两个都不带才财就不输出
        if (
            ($xs != '才财' && $yong != '才财') ||
            // 天干十神都不包含财星
            (
                !in_array($godT['year'], ['正财', '偏财']) && !in_array($godT['month'], ['正财', '偏财']) && !in_array($godT['day'], ['正财', '偏财']) && !in_array($godT['hour'], ['正财', '偏财']) &&
                !array_intersect(['正财', '偏财'], $_god['year']['god']) && !array_intersect(['正财', '偏财'], $_god['month']['god']) && !array_intersect(['正财', '偏财'], $_god['day']['god']) && !array_intersect(['正财', '偏财'], $_god['hour']['god'])
            )
        ) {
            return [];
        }
        // 第二步，判断十神是否有财星
        $szcx = [
            // 年 => [天干十神、地支十神]
            'year' => [in_array($godT['year'], ['正财', '偏财']) ? $godT['year'] : '', implode('', array_intersect(['正财', '偏财'], $_god['year']['god']))],
            'month' => [in_array($godT['month'], ['正财', '偏财']) ? $godT['month'] : '', implode('', array_intersect(['正财', '偏财'], $_god['month']['god']))],
            'day' => ['', implode('', array_intersect(['正财', '偏财'], $_god['day']['god']))],
            'hour' => [in_array($godT['hour'], ['正财', '偏财']) ? $godT['hour'] : '', implode('', array_intersect(['正财', '偏财'], $_god['hour']['god']))],
        ];

        // 第三步，处理财星透藏
        // 藏数量
        $canSum = 0;
        // 透数量
        $touSum = 0;
        // 计算财星数量
        $cxSumArr = [];
        // 取得各地支所属气
        $dzQiArr = [];
        foreach ($szcx as $k => $list) {
            // 藏
            if ($list[1]) {
                $canSum++;
            }
            // 透
            if ($list[0]) {
                $touSum++;
            }
            // 财星数量
            foreach ($list as $k2 => $name) {
                $cxSumArr[$k][$k2] = $name ? 1 : 0;
            }
            // 只有十神里财星才显示对应的所属气的位置
            if (array_intersect(['正财', '偏财'], $_god[$k]['god'])) {
                $godKey = array_search($list[1], $_god[$k]['god']);
                $dzQiArr[$k] = $_god[$k]['qi'][$godKey];
            } else {
                $dzQiArr[$k] = '';
            }
        }

        // 判断财星坐落位置，同时得到几组
        $zlwz = [];
        foreach ($szcx as $k => $item) {
            // 类型名称
            $typeName = match ($k) {
                'year' => '年',
                'month' => '月',
                'day' => '日',
                'hour' => '时',
            };
            // 纪年key名称
            $keyJnianName = match ($k) {
                'year' => 'y',
                'month' => 'm',
                'day' => 'd',
                'hour' => 'h',
            };
            // 判断天干位置
            if (in_array($item[0], ['正财', '偏财'])) {
                $zlwz[] = "{$typeName}干";
            }
            // 判断地支位置
            if (in_array($item[1], ['正财', '偏财'])) {
                // 财库
                $caiku = '';
                if (in_array($jiNian[$keyJnianName][1], ['辰', '戌', '丑', '未']) && in_array($item[1], ['正财', '偏财'])) {
                    $caiku = $jiNian[$keyJnianName][1];
                }
                // 如果是主气，不用显示“的x气”
                if ($dzQiArr[$k] == '主') {
                    $yuqi = '';
                } else {
                    $yuqi = "的{$dzQiArr[$k]}气";
                }
                $zlwz[] = "{$typeName}支{$caiku}{$yuqi}";
            }
        }
        // 财星透藏和坐落的位置 文案
        $cxtczlwz = '命局财星';
        if ($touSum) {
            $cxtczlwz .= "{$touSum}透";
        }
        if ($canSum) {
            $cxtczlwz .= "{$canSum}藏";
        }
        $zlwzStr = implode('、', $zlwz);
        $cxtczlwz .= "，坐落于{$zlwzStr}。";

        // 第四步，命局总结
        $mjzj = "在该命局中，财星坐落于{$zlwzStr}，";
        $mjzjA = '';
        $mjzjB = '';

        // 情况1：年干=1，或者年支=1并且年气=主，输出：代表命主财富来源于祖辈或国家体制内
        if (
            $cxSumArr['year'][0] == 1 ||
            ($cxSumArr['year'][1] == 1 && $dzQiArr['year'] == '主')
        ) {
            $mjzjA = '代表命主财富来源于祖辈或国家体制内，';
            // 年干=1或 年支=1且为年气=主气   且 月日时干=0,月 且 月日时地支=0且 月日时气=空
            if (
                ($cxSumArr['year'][0] == 1 || ($cxSumArr['year'][1] == 1 && $dzQiArr['year'] == '主')) &&
                $cxSumArr['month'][0] == 0 && $cxSumArr['day'][0] == 0 && $cxSumArr['hour'][0] == 0 &&
                $cxSumArr['month'][1] == 0 && $cxSumArr['day'][1] == 0 && $cxSumArr['hour'][1] == 0 &&
                $dzQiArr['month'] == '' && $dzQiArr['day'] == '' && $dzQiArr['hour'] == ''
            ) {
                $mjzjB = '并在人生的早年已拼搏而来。';
            }
            // 年干=1（年支=1且为年气=主气）且 月日时干=0,   且月日时地支求和大于等于1 且 月日时气=中或余
            if (
                ($cxSumArr['year'][0] == 1 || ($cxSumArr['year'][1] == 1 && $dzQiArr['year'] == '主')) &&
                $cxSumArr['month'][0] == 0 && $cxSumArr['day'][0] == 0 && $cxSumArr['hour'][0] == 0 &&
                ($cxSumArr['month'][1] + $cxSumArr['day'][1] + $cxSumArr['hour'][1]) >= 1 &&
                (in_array($dzQiArr['month'], ['中', '余']) || in_array($dzQiArr['day'], ['中', '余']) || in_array($dzQiArr['hour'], ['中', '余']))
            ) {
                $mjzjB = '或者在人生的早年已拼搏而来，且较有可能是大财。';
            }
            // （年干=1 || 年支=1且为年气=主气）&& (月日时干=1 || (月日时干=0 && 月日时地支=1且月日时气=主))
            if (
                ($cxSumArr['year'][0] == 1 || ($cxSumArr['year'][1] == 1 && $dzQiArr['year'] == '主')) &&
                (
                    ($cxSumArr['month'][0] == 1 && $cxSumArr['day'][0] == 1 && $cxSumArr['hour'][0] == 1) ||
                    (
                        ($cxSumArr['month'][0] == 0 && $cxSumArr['day'][0] == 0 && $cxSumArr['hour'][0] == 0) &&
                        ($cxSumArr['month'][1] == 1 || $cxSumArr['day'][1] == 1 || $cxSumArr['hour'][1] == 1) &&
                        ($dzQiArr['month'] == '主' || $dzQiArr['day'] == '主' || $dzQiArr['hour'] == '主')
                    )
                )
            ) {
                $mjzjB = '或在人生的早年已拼搏而来，并能在晚年享有财富。';
            }
        } else {
            // 情况2：月干=1，或者月支=1且月气=主，输出：代表命主财富来源于原生家庭
            if (
                $cxSumArr['month'][0] == 1 ||
                ($cxSumArr['month'][1] == 1 && $dzQiArr['month'] == '主')
            ) {
                $mjzjA = '代表命主财富来源于原生家庭，';
                $mjzjB = '或者在人生的早年已拼搏而来。';
            } else {
                // 情况3：，输出：代表命主财富来源于自身
                if (
                    (
                        $cxSumArr['day'][0] == 1 ||
                        ($cxSumArr['day'][1] == 1 && $dzQiArr['day'] == '主')
                    ) ||
                    (
                        $cxSumArr['hour'][0] == 1 ||
                        ($cxSumArr['hour'][1] == 1 && $dzQiArr['hour'] == '主')
                    )
                ) {
                    $mjzjA = '代表命主财富来源于自身，';
                    $mjzjB = '并在人生的中年以后拼搏而来。';
                }
            }
        }
        // 情况4：年月日时干（所有天干）=空0，年月日时地支=为空或中气或者余气，对所有财星地支求和<=4，输出：财星藏而不透，需要被大运流年所引动，才可显现出其特性。
        if (
            // 天干财星都是0
            $cxSumArr['year'][0] == 0 && $cxSumArr['month'][0] == 0 && $cxSumArr['day'][0] == 0 && $cxSumArr['hour'][0] == 0 &&
            // 年月日时地支=中气或者余气（不为主气）
            in_array($dzQiArr['year'], ['中', '余', '']) && in_array($dzQiArr['month'], ['中', '余', '']) && in_array($dzQiArr['day'], ['中', '余', '']) && in_array($dzQiArr['hour'], ['中', '余', '']) &&
            // 对所有财星地支求和<=4
            ($cxSumArr['year'][1] + $cxSumArr['month'][1] + $cxSumArr['day'][1] + $cxSumArr['hour'][1]) <= 4

        ) {
            $mjzjA = '财星藏而不透，需要被大运流年所引动，才可显现出其特性。';
        }

        $mjzj .= $mjzjA . $mjzjB;

        // 第五步，判断四柱财星里面的地支十神，财星是主气（在十神位置里的第一个就是主气），不是主气就加个东西
        foreach ($_god as $k => $item) {
            $zhuqi = $item['god'][0];
            if ($szcx[$k][1] && $szcx[$k][1] != $zhuqi) {
                $szcx[$k][1] .= '<b class="' . $k . '1">(藏)</b>';
            }
        }

        $result = [
            // 四柱财星
            'szcx' => $szcx,
            // 财星透藏和坐落的位置
            'cxtczlwz' => $cxtczlwz,
            // 命局总结
            'mjzj' => $mjzj,
            // 调试
            // '$cxSumArr' => $cxSumArr,
            // '$dzQiArr' => $dzQiArr,
        ];
        return $result;
    }

    /**
     * 事业与财富贵人
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @return array
     */
    protected function syycfgr(array $jiNian, array $jnWx)
    {
        // 地支三合表
        $dzshb = [
            '子' => ['辰', '申'],
            '丑' => ['巳', '酉'],
            '寅' => ['午', '戌'],
            '卯' => ['亥', '未'],
            '辰' => ['子', '申'],
            '巳' => ['酉', '丑'],
            '午' => ['寅', '戌'],
            '未' => ['亥', '卯'],
            '申' => ['辰', '子'],
            '酉' => ['巳', '丑'],
            '戌' => ['午', '寅'],
            '亥' => ['卯', '未'],
        ];
        // 地支六合表
        $dzlhb = [
            '子' => '丑', '丑' => '子', '寅' => '亥', '卯' => '戌', '辰' => '酉', '巳' => '申',
            '午' => '未', '未' => '午', '申' => '巳', '酉' => '辰', '戌' => '卯', '亥' => '寅',
        ];
        $a1 = $jiNian['m'][1] . $jnWx['m']['wx'][1];
        $a2 = $dzlhb[$jiNian['m'][1]] . '、' . implode('、', $dzshb[$jiNian['m'][1]]);
        return [
            'sh' => $dzshb[$jiNian['m'][1]],
            'lh' => $dzlhb[$jiNian['m'][1]],
            'js' => "该命局事业宫为{$a1}，所以事业贵人可以从两个方面寻找：<br/>1)以{$a1}为喜之人<br/>2){$a2}月出生之人(原因：事业宫相合)<br/>可以留意符合上述要求的长辈、领导及合作伙伴。",
        ];
    }

    /**
     * 财源多样性算法和结果
     * @param array $godT 十神
     * @param array $_god 十神，带上气值
     * @return array
     */
    protected function cfdyx(array $godT, array $_god)
    {
        $result = [];
        $ssList = [
            $godT['year'],
            $godT['month'],
            $godT['hour'],
        ];
        foreach ($_god as $k => $v) {
            $ssList[] = $v['god'][0];
        }
        // 正财，偏财分组统计
        $zcpcfz = [
            'pc' => 0,
            'zc' => 0,
        ];
        foreach ($ssList as $name) {
            if (in_array($name, ['偏财'])) {
                $zcpcfz['pc']++;
            }
            if (in_array($name, ['正财'])) {
                $zcpcfz['zc']++;
            }
        }
        // 结果
        if (
            ($zcpcfz['pc'] > 0 && $zcpcfz['zc'] == 0) ||
            ($zcpcfz['pc'] = 0 && $zcpcfz['zc'] > 0)
        ) {
            // 财星清纯
            $result[] = '该命局<b>财星清纯</b>，说明命主财富来源比较集中，容易专注在一个领域成就价值并获得财富，且不容易变换职业。';
        } elseif ($zcpcfz['pc'] > 0 && $zcpcfz['zc'] > 0) {
            // 正偏财混杂
            $result[] = '该命局<b>财星驳杂</b>，说明命主不会只具有单一财源，容易有副业收入，或者是财源方式较为广泛，容易变换职业。';
            $result[] = '基于上述，建议命主在恰当把握财富机会的同时，也要注意提升在事业上的专注度，构建自身的能力长板及事业护城河。';
        } elseif ($zcpcfz['pc'] == 0 && $zcpcfz['zc'] == 0) {
            // 财星不现
            $result[] = '该命局<b>财星不现</b>，需要综合其他技法判断财源多样性状态。';
        }
        return $result;
    }

    /**
     * 求财意向结果
     * @param array $qcfs 求财方式
     * @param array $jnWx 纪年五行
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcyxJg(array $qcfs, array $jnWx, array $_god)
    {
        // 求财意向结果
        $qcyxb = [
            '印枭' => '该八字事业宫(月令)为<b>印枭</b>，代表最终的成就方向和事业归属为获得学识与体面。',
            '比劫' => '该八字事业宫(月令)为<b>比劫</b>，代表在实现人生目标中，强调自我意志的贯彻。',
            '才财' => '该八字事业宫(月令)为<b>才财</b>，代表最终的成就方向和事业归属为获取财富。',
            '官杀' => '该八字事业宫(月令)为<b>官杀</b>，代表最终的成就方向和事业归属为获取权力与地位。',
            '食伤' => '该八字事业宫(月令)为<b>食伤</b>，代表最终的成就方向和事业归属为获得才华的实现。',
        ];
        // 取得组合十神
        $zhssList = $this->zhssb[$jnWx['d']['wx'][0]];
        // 得到五行
        $wx = $zhssList[$this->tgpdwx[$_god['month']['hide'][0]]];
        // 财星强度等级计算
        $ccBfb = $qcfs['nldb']['lqxs']['才财']['bfb'] ?? 0;
        if ($ccBfb < 10) {
            $cxqd = ['nldj' => '较弱', 'yxdj' => '不高', 'wa' => "财星在命局中能量占比<b>{$ccBfb}%</b>,财星能量较弱，求财意向不高。"];
        } elseif ($ccBfb <= 20) {
            $cxqd = ['nldj' => '中等', 'yxdj' => '适中', 'wa' => "财星在命局中能量占比<b>{$ccBfb}%</b>,财星能量中等，求财意向适中。"];
        } else {
            $cxqd = ['nldj' => '旺盛', 'yxdj' => '较强', 'wa' => "财星在命局中能量占比<b>{$ccBfb}%</b>,财星能量旺盛，求财意向较强。"];
        }
        $result = [
            'yx' => $qcyxb[$wx],
            'cxqd' => $cxqd,
        ];
        return $result;
    }

    /**
     * 求财方式结果
     * 根据李氏六亲形势中：最大十神+次大十神调用结果：如最大是官杀，次大印绶： 调整官杀+印绶的行结果；（到职业）
     * 调六亲最弱十神【大于等于10%时不输出】《如：最弱比劫：K列到N列对应的行结果
     * @param array $qcfs
     * @return string[]
     */
    protected function qcfsJg(array $qcfs)
    {
        // 最大十神+次大十神 结果表
        $zdsscdssjgb = [
            '官杀印枭' => [
                'sczh' => '官杀与印绶',
                'db' => '代表命主：对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。处事沉稳，目标感强，并善于坚持。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于以事业和权力获取，较有可能从事稳定性职业。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业；科研人员、心理咨询师、公务员等需要较多耐心及沉淀的职业。',
            ],
            '官杀才财' => [
                'sczh' => '官杀与财星',
                'db' => '代表命主：对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。<br/>善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财偏向于以事业和权力获取，对金钱、数字比较敏感，擅长精细耕耘。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业；公共关系、人力资源、经商等需要交际广泛及情绪输出的职业。',
            ],
            '比劫食伤' => [
                'sczh' => '比劫与食伤',
                'db' => '代表命主：进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。对固有的规章制度和文化较为抗拒，重视自我才华与意志的舒展。',
                'px' => '求财方式激进、大胆，且偏向兴趣驱动，重视个人喜好和舒适度大于重视实际利益。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '股票投资、个体经营、一线销售等需要强执行力和开拓力的职业；广告创意、设计师、自由职业者等需要较多创意输出与应变的职业。',
            ],
            '比劫印枭' => [
                'sczh' => '比劫与印绶',
                'db' => '代表命主：进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。',
                'px' => '求财偏向于从事稳定性职业来获取，对名誉需求大于财富需求。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '科研人员、心理咨询师、公务员、教职人员、公司法务、行政文职等需要较多耐心及沉淀的职业。',
            ],
            '比劫官杀' => [
                'sczh' => '比劫与官杀',
                'db' => '代表命主：进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于以事业和权力获取，方式较为激进、大胆。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '股票投资、个体经营、一线销售等需要强执行力和开拓力的职业；公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业。',
            ],
            '官杀食伤' => [
                'sczh' => '官杀与食伤',
                'db' => '代表命主：对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。<br/>善于挑战旧有的规章制度和文化，并突破常规打开新的局面，成为不容忽视的革新力量。',
                'px' => '求财偏向于以事业和权力获取，且强调自我意志的主张。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公检法人员、投资人、职业经理人、自主创业、审查员、行业分析师等需要较强管理或破局能力的职业。',
            ],
            '才财比劫' => [
                'sczh' => '财星与比劫',
                'db' => '代表命主：交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。<br/>善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。',
                'px' => '求财偏向于通过资源运作等生意形式获取。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公共关系、人力资源、经商等需要交际广泛及情绪输出的职业；股票投资、个体经营、一线销售等需要强执行力和开拓力的职业。',
            ],
            '食伤比劫' => [
                'sczh' => '食伤与比劫',
                'db' => '代表命主：有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。',
                'zz' => '',
                'xh' => '喜欢新奇和变化的事物，对固有的规章制度和文化较为抗拒，重视自我才华的自由舒展。',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。',
                'px' => '求财偏向兴趣驱动，且方式激进、大胆。重视个人喜好和舒适度大于重视实际利益。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '广告创意、设计师、自由职业者等需要较多创意输出与应变的职业；股票投资、个体经营、一线销售等需要强执行力和开拓力的职业。',
            ],
            '印枭比劫' => [
                'sczh' => '印绶与比劫',
                'db' => '代表命主：处事沉稳，目标感强，并善于坚持。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。能抗下艰巨的任务，并在此过程中享受竞争带来的动力。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。求财偏向于从事稳定性职业来获取，对名誉需求大于财富需求。',
                'px' => '',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '科研人员、心理咨询师、公务员、教职人员、公司法务、行政文职等需要较多耐心及沉淀的职业。',
            ],
            '才财官杀' => [
                'sczh' => '财星与官杀',
                'db' => '代表命主：交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。<br/>善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于通过资源运作等生意形式获取，且较为注重权利与地位的获得。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公共关系、人力资源、经商等需要交际广泛及情绪输出的职业；公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业。',
            ],
            '才财印枭' => [
                'sczh' => '财星与印绶',
                'db' => '代表命主：交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财偏向于通过资源运作等生意形式获取，对名誉和地位有较高需求。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公共关系、人力资源、经商、经纪人、用户运营、客户服务等需要交际广泛及情绪输出的职业。',
            ],
            '印枭官杀' => [
                'sczh' => '印绶与官杀',
                'db' => '代表命主：处事沉稳，目标感强，并善于坚持。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。对自我发展有着较高的要求，能不断精进自己，并以结果导向，对产出负责。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于从事稳定性职业来获取，较为重视权利与地位，对名誉需求大于财富需求。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '科研人员、心理咨询师、公务员等需要较多耐心及沉淀的职业；公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业。',
            ],
            '印枭才财' => [
                'sczh' => '印绶与财星',
                'db' => '代表命主：处事沉稳，目标感强，并善于坚持。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财偏向于通过资源运作等生意形式获取，对名誉和地位有较高需求。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公共关系、人力资源、经商、经纪人、用户运营、客户服务等需要交际广泛及情绪输出的职业。',
            ],
            '比劫才财' => [
                'sczh' => '比劫与财星',
                'db' => '代表命主：进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。并能在团队中迅速凝结人气，被团队成员所拥护。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财方式激进、大胆，偏向于通过资源运作等生意形式获取。对金钱、数字比较敏感。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '股票投资、个体经营、一线销售等需要强执行力和开拓力的职业；公共关系、人力资源、经商等需要交际广泛及情绪输出的职业。',
            ],
            '才财食伤' => [
                'sczh' => '财星与食伤',
                'db' => '代表命主：交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。',
                'zz' => '',
                'xh' => '喜欢新奇和变化的事物，对固有的规章制度和文化较为抗拒，重视自我才华的自由舒展。',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财偏向于通过资源运作等生意形式获取，某种程度上被兴趣所驱动。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公共关系、人力资源、经商等需要交际广泛及情绪输出的职业；广告创意、设计师、自由职业者等需要较多创意输出与应变的职业。',
            ],
            '食伤才财' => [
                'sczh' => '食伤与财星',
                'db' => '代表命主：有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。交际广泛，朋友众多，能维护复杂的人际关系，并善于与他人交换资源、建立合作、互通有无。',
                'zz' => '',
                'xh' => '喜欢新奇和变化的事物，对固有的规章制度和文化较为抗拒，重视自我才华的自由舒展。',
                'sy' => '善于体察他人需求，对数据、咨询、金钱财务具有想当高的敏锐度，并能快速捕捉市场的动向，在时机把握上有较好的见解。',
                'px' => '求财偏向兴趣驱动，重视个人喜好和舒适度大于重视实际利益。对金钱、数字比较敏感，擅长精细耕耘。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '广告创意、设计师、自由职业者等需要较多创意输出与应变的职业；公共关系、人力资源、经商等需要交际广泛及情绪输出的职业。',
            ],
            '食伤官杀' => [
                'sczh' => '食伤与官杀',
                'db' => '代表命主：有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于挑战旧有的规章制度和文化，并突破常规打开新的局面，成为不容忽视的革新力量。<br/>善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于以事业和权力获取，且强调自我意志的主张。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公检法人员、投资人、职业经理人、自主创业、审查员、行业分析师等需要较强管理或破局能力的职业。',
            ],
            '官杀比劫' => [
                'sczh' => '官杀与比劫',
                'db' => '代表命主：对自我发展有着较高的要求，能不断精进自己，设立长远目标，并以结果导向，对产出负责。进取心较强，有强大的意志力和行动力，能抗下艰巨的任务，并以誓不罢休的精神达成目标，并在此过程中享受竞争带来的动力。',
                'zz' => '',
                'xh' => '',
                'sy' => '善于调动人力资源，能在团队中迅速凝结人气，被团队成员所拥护。<br/>善于分析问题，拆解思路，并为事情发展设立中长期发展战略，并在强大理性驱动下，整合组织与团队力量，作出亮眼的成绩。',
                'px' => '求财偏向于以事业和权力获取，方式较为激进、大胆。重视权力地位与名誉大于重视实际利益，求名甚于求利。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '公检法人员、工程师、规划师等需要强逻辑性和缜密性的职业；股票投资、个体经营、一线销售等需要强执行力和开拓力的职业。',
            ],
            '食伤印枭' => [
                'sczh' => '食伤与印绶',
                'db' => '代表命主：有着强烈的好奇心与求知欲，对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。具有较强的目标感与韧性，可参与和经营长周期的项目与事业，等待收获的来临。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '',
                'px' => '求财某种程度上受兴趣驱动，且易从事稳定性职业，对名誉需求大于财富需求。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '广告创意、设计师、自由职业者、市场营销、网络博主、主持人等需要较多创意输出与应变的职业。',
            ],
            '印枭食伤' => [
                'sczh' => '印绶与食伤',
                'db' => '代表命主：处事沉稳，目标感强，并善于坚持。具有强大的耐心与韧性，可参与和经营长周期的项目与事业，等待收获的来临。对于爱好有钻研精神，能够迅速切入新领域，并产出具有创意性的成果。',
                'zz' => '注重团队和谐的氛围，能给团队安心与信任，成为团队中受人信赖的成员和后盾支持。',
                'xh' => '',
                'sy' => '',
                'px' => '求财偏向于从事稳定性职业来获取，某种程度上受兴趣驱动，对名誉需求大于财富需求。',
                'jy' => '基于以上，命主从事以下相似属性的职业，能更大的发挥才能。',
                'tjzylx' => '广告创意、设计师、自由职业者、市场营销、网络博主、主持人等需要较多创意输出与应变的职业。',
            ],
        ];
        // 弱十神 结果表
        $rssjgb = [
            '比劫' => [
                'mjjr' => '根据命局较弱十神为：比劫，所以命主容易自信心、进取心不够，面对机会和挑战时略微显得被动。在推进事情过程中，也容易因上述原因，丧失本属于自己的机会，或者错失事业最好的机遇期。',
                'xy' => '需要在成长过程中，逐渐克服此缺点，或者有意识地规避弱势领域，才能更好成就事业与财富。',
                'jyhb' => '建议回避需要强执行力和开拓力的职业。',
            ],
            '才财' => [
                'mjjr' => '根据命局较弱十神为：财星，所以命主容易在人际交往、情绪感知上缺乏足够出的感知和应变能力，显得“共情不够”。在事业中，对待金钱上较为粗线条，不擅长投资理财上的规划运作，难以发挥出金钱应有的性价比。',
                'xy' => '需要在成长过程中，逐渐克服此缺点，或者有意识地规避弱势领域，才能更好成就事业与财富。',
                'jyhb' => '建议回避需要交际广泛及情绪输出的职业。',
            ],
            '官杀' => [
                'mjjr' => '根据命局较弱十神为：官杀，所以命主容易对理性、规划、目标的把控能力不足，做事容易凭着感觉走，缺乏对现实大局的冷静分析。在推进事情的过程中，也容易降低目标，略显随意。',
                'xy' => '需要在成长过程中，逐渐克服此缺点，或者有意识地规避弱势领域，才能更好成就事业与财富。',
                'jyhb' => '建议回避需要强逻辑性和缜密性的职业。',
            ],
            '食伤' => [
                'mjjr' => '根据命局较弱十神为：食伤，所以命主容易在创意、潮流和自我表达方面，缺乏足够的敏感度和积极性。在推进事情的过程中，也容易略显按部就班，不轻易作出突破和创新，过于“陈旧保守”。',
                'xy' => '需要在成长过程中，逐渐克服此缺点，或者有意识地规避弱势领域，才能更好成就事业与财富。',
                'jyhb' => '建议回避需要较多创意输出与应变的职业。',
            ],
            '印枭' => [
                'mjjr' => '根据命局较弱十神为：印绶，所以命主容易对耐心、韧性的重要性意识不够，对事情略显浮躁和急于求成。在推进事情的过程中，也不容易布局长远，放长眼光，耐心等待收获的来临。',
                'xy' => '需要在成长过程中，逐渐克服此缺点，或者有意识地规避弱势领域，才能更好成就事业与财富。',
                'jyhb' => '建议回避需要较多耐心及沉淀的职业。',
            ],
        ];

        // 李氏六亲形势
        $lqxs = [];
        foreach ($qcfs['nldb_li']['lqxs'] as $name => $var) {
            $var['name'] = $name;
            $lqxs[] = $var;
        }
        // 取得李氏六亲形势中：最大十神+次大十神
        usort($lqxs, function ($a, $b) {
            if ($a['num'] == $b['num']) {
                return 0;
            }
            return ($a['num'] > $b['num']) ? -1 : 1;
        });
        $zdzcKey = "{$lqxs[0]['name']}{$lqxs[1]['name']}";
        $result = [
            'zdab' => $zdsscdssjgb[$zdzcKey],
            'zr' => '',
        ];
        if (end($lqxs)['bfb'] < 10) {
            // 取得最弱十神
            $zrKey = end($lqxs)['name'];
            $result['zr'] = $rssjgb[$zrKey];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-同异类分配
     * @param array $jnWx 纪年五行
     * @return array[]
     */
    protected function qcfsNldbTylfp(array $jnWx)
    {
        // 五行数值表
        $wuxingNum = $this->lunar->getWuxingNum();
        // 总分值
        $sum = array_sum($wuxingNum);
        $result = [
            'tong' => [
                $jnWx['d']['wx'][0] => [
                    'num' => $wuxingNum[$jnWx['d']['wx'][0]],
                    'bfb' => round(($wuxingNum[$jnWx['d']['wx'][0]] / $sum) * 100, 2),
                ],
                $this->wxxs[$jnWx['d']['wx'][0]] => [
                    'num' => $wuxingNum[$this->wxxs[$jnWx['d']['wx'][0]]],
                    'bfb' => round(($wuxingNum[$this->wxxs[$jnWx['d']['wx'][0]]] / $sum) * 100, 2),
                ],
            ],
            'tong_zhong' => [],
            'yi' => [],
            'yi_zhong' => [],
        ];
        $result['tong_zhong']['num'] = round(bcadd($wuxingNum[$jnWx['d']['wx'][0]], $wuxingNum[$this->wxxs[$jnWx['d']['wx'][0]]], 2), 2);
        $result['tong_zhong']['bfb'] = round(($result['tong_zhong']['num'] / $sum) * 100, 2);
        $yiZhongNum = 0;
        foreach ($wuxingNum as $k => $v) {
            // 不在同类的，就是异类
            if (!isset($result['tong'][$k])) {
                $result['yi'][$k] = [
                    'num' => $wuxingNum[$k],
                    'bfb' => round(($wuxingNum[$k] / $sum) * 100, 2),
                ];
                $yiZhongNum = bcadd($wuxingNum[$k], $yiZhongNum, 2);
            }
        }
        $result['yi_zhong']['num'] = round($yiZhongNum, 2);
        $result['yi_zhong']['bfb'] = round(($result['yi_zhong']['num'] / $sum) * 100, 2);
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-同异类分配
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcfsNldbLiTylfp(array $jiNian, array $jnWx, array $_god)
    {
        // 李氏五行分配结果
        $number = $this->qcfsNldbLiWxfp($jiNian, $jnWx, $_god);
        // 总分
        $sum = 0;
        foreach ($number as $k => $v) {
            $sum = bcadd($v['num'], $sum, 2);
        }
        $sum = round($sum, 2);
        // 第一步，找出同类
        $result = [
            'tong' => [
                $jnWx['d']['wx'][0] => [
                    'num' => $number[$jnWx['d']['wx'][0]]['num'],
                    'bfb' => round(($number[$jnWx['d']['wx'][0]]['num'] / $sum) * 100, 2),
                ],
                $this->wxxs[$jnWx['d']['wx'][0]] => [
                    'num' => $number[$this->wxxs[$jnWx['d']['wx'][0]]]['num'],
                    'bfb' => round(($number[$this->wxxs[$jnWx['d']['wx'][0]]]['num'] / $sum) * 100, 2),
                ],
            ],
            'tong_zhong' => [],
            'yi' => [],
            'yi_zhong' => [],
        ];
        $result['tong_zhong']['num'] = round(bcadd($number[$jnWx['d']['wx'][0]]['num'], $number[$this->wxxs[$jnWx['d']['wx'][0]]]['num'], 2), 2);
        $result['tong_zhong']['bfb'] = round(($result['tong_zhong']['num'] / $sum) * 100, 2);
        // 第二步，找出异类，不在同类就是异类
        $yiZhongNum = 0;
        foreach ($number as $k => $v) {
            if (!isset($result['tong'][$k])) {
                $result['yi'][$k] = [
                    'num' => $v['num'],
                    'bfb' => round(($v['num'] / $sum) * 100, 2),
                ];
                $yiZhongNum += $v['num'];
            }
        }
        $result['yi_zhong']['num'] = round($yiZhongNum, 2);
        $result['yi_zhong']['bfb'] = round(($result['yi_zhong']['num'] / $sum) * 100, 2);
        return $result;
    }

    /**
     * 求财方式-能量度表-十神形势
     * @param array $jiNian 纪年
     * @return array[]
     */
    protected function qcfsNldbSsxs(array $jiNian)
    {
        // 天干地支强度
        $tgdzqdb = $this->tgdzqdb($jiNian);
        // 合并一样
        $tgdzqdb2 = [];
        foreach ($tgdzqdb as $tgdz => $num) {
            // 去掉纪年下面的月柱地支这个字
            $qd = str_replace($jiNian['m'][1], '', $tgdz);
            $tgdzqdb2[$qd] = round(bcadd($tgdzqdb2[$qd] ?? 0, $num, 2), 2);
        }
        $result = [];
        // 天干地支强度表名称转换成十神
        foreach ($tgdzqdb2 as $tgdz => $num) {
            $tgss = $this->lunar->getGodNameByTg($jiNian['d'][0], $tgdz);
            $result[$tgss] = $num;
        }
        $sum = array_sum($result);
        foreach ($result as $key => $value) {
            $result[$key] = ['num' => round($value, 2), 'bfb' => round(($value / $sum) * 100, 2)];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-十神形势
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcfsNldbLiSsxs(array $jiNian, array $jnWx, array $_god)
    {
        // 阴阳五行转十神表
        $yywxzssb = [
            '阳木' => [
                '阳木' => '比肩', '阴木' => '劫财', '阳火' => '食神', '阴火' => '伤官', '阳土' => '偏财',
                '阴土' => '正财', '阳金' => '七杀', '阴金' => '正官', '阳水' => '偏印', '阴水' => '正印',
            ],
            '阴木' => [
                '阳木' => '劫财', '阴木' => '比肩', '阳火' => '伤官', '阴火' => '食神', '阳土' => '正财',
                '阴土' => '偏财', '阳金' => '正官', '阴金' => '七杀', '阳水' => '正印', '阴水' => '偏印',
            ],
            '阳火' => [
                '阳木' => '偏印', '阴木' => '正印', '阳火' => '比肩', '阴火' => '劫财', '阳土' => '食神',
                '阴土' => '伤官', '阳金' => '偏财', '阴金' => '正财', '阳水' => '七杀', '阴水' => '正官',
            ],
            '阴火' => [
                '阳木' => '正印', '阴木' => '偏印', '阳火' => '劫财', '阴火' => '比肩', '阳土' => '伤官',
                '阴土' => '食神', '阳金' => '正财', '阴金' => '偏财', '阳水' => '正官', '阴水' => '七杀',
            ],
            '阳土' => [
                '阳木' => '七杀', '阴木' => '正官', '阳火' => '偏印', '阴火' => '正印', '阳土' => '比肩',
                '阴土' => '劫财', '阳金' => '食神', '阴金' => '伤官', '阳水' => '偏财', '阴水' => '正财',
            ],
            '阴土' => [
                '阳木' => '正官', '阴木' => '七杀', '阳火' => '正印', '阴火' => '偏印', '阳土' => '劫财',
                '阴土' => '比肩', '阳金' => '伤官', '阴金' => '食神', '阳水' => '正财', '阴水' => '偏财',
            ],
            '阳金' => [
                '阳木' => '偏财', '阴木' => '正财', '阳火' => '七杀', '阴火' => '正官', '阳土' => '偏印',
                '阴土' => '正印', '阳金' => '比肩', '阴金' => '劫财', '阳水' => '食神', '阴水' => '伤官',
            ],
            '阴金' => [
                '阳木' => '正财', '阴木' => '偏财', '阳火' => '正官', '阴火' => '七杀', '阳土' => '正印',
                '阴土' => '偏印', '阳金' => '劫财', '阴金' => '比肩', '阳水' => '伤官', '阴水' => '食神',
            ],
            '阳水' => [
                '阳木' => '食神', '阴木' => '伤官', '阳火' => '偏财', '阴火' => '正财', '阳土' => '七杀',
                '阴土' => '正官', '阳金' => '偏印', '阴金' => '正印', '阳水' => '比肩', '阴水' => '劫财',
            ],
            '阴水' => [
                '阳木' => '伤官', '阴木' => '食神', '阳火' => '正财', '阴火' => '偏财', '阳土' => '正官',
                '阴土' => '七杀', '阳金' => '正印', '阴金' => '偏印', '阳水' => '劫财', '阴水' => '比肩',
            ],
        ];

        $result = [];
        // 第一步，取得阴阳五行形势结果
        $yywxxs = $this->qcfsNldbLiYywxxs($jiNian, $_god);
        // 第二步，转换成十神
        $yywxzssList = $yywxzssb["{$jnWx['d']['yy'][0]}{$jnWx['d']['wx'][0]}"];
        foreach ($yywxxs as $k => $v) {
            $result[$yywxzssList[$k]] = $v;
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-阴阳五行形势
     * @param array $jiNian 纪年
     * @return array
     */
    protected function qcfsNldbYywxxs(array $jiNian)
    {
        // 天干地支强度
        $tgdzqdb = $this->tgdzqdb($jiNian);
        // 合并一样
        $tgdzqdb2 = [];
        foreach ($tgdzqdb as $tgdz => $num) {
            // 去掉纪年下面的月柱地支这个字
            $qd = str_replace($jiNian['m'][1], '', $tgdz);
            $tgdzqdb2[$qd] = round(bcadd($tgdzqdb2[$qd] ?? 0, $num, 2), 2);
        }
        $result = [];
        // 天干地支强度表名称转换成阴阳五行
        foreach ($tgdzqdb2 as $tgdzqd => $fen) {
            $result[$this->tgZyywx[$tgdzqd]] = $fen;
        }
        $sum = array_sum($result);
        foreach ($result as $key => $value) {
            $result[$key] = ['num' => round($value, 2), 'bfb' => round(($value / $sum) * 100, 2)];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-阴阳五行形势
     * @param array $jiNian 纪年
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcfsNldbLiYywxxs(array $jiNian, array $_god)
    {
        $result = [];

        // 第一步，计算4个天干，每个固定100分
        foreach ($jiNian as $k => $v) {
            if (isset($result[$v[0]])) {
                $result[$v[0]] += 100;
            } else {
                $result[$v[0]] = 100;
            }
        }
        // 第二步，天干转阴阳五行
        foreach ($result as $key => $value) {
            $name = $this->tgZyywx[$key];
            unset($result[$key]);
            $result[$name] = $value;
        }
        // 第三步，计算藏干组合  纪年的地支jinian.y.1+地支藏干_god.year.hide[]
        foreach ($jiNian as $k => $v) {
            $key = "{$v[1]}";
            // 循环藏干组合
            $cgKey = match ($k) {
                'y' => 'year',
                'm' => 'month',
                'd' => 'day',
                'h' => 'hour',
            };
            foreach ($_god[$cgKey]['hide'] as $name) {
                $keyName = "{$key}{$name}";

                // 取得地支藏干的分数表
                $fenzhi = $this->dzcgfsb[$keyName];
                // 把名字切割成一个字，去匹配 $tgpdwx 得到五行
                $keyNameArr = preg_split('/(?<!^)(?!$)/u', $keyName);
                foreach ($keyNameArr as $va) {
                    if (isset($this->tgZyywx[$va])) {
                        $wx = $this->tgZyywx[$va];
                        if (isset($result[$wx])) {
                            $result[$wx] += $fenzhi;
                        } else {
                            $result[$wx] = $fenzhi;
                        }
                        break;
                    }
                }
            }
        }
        // 第四步，根据每个月的旺度系数，纪年的地支jinian.m.1
        foreach ($result as $name => $num) {
            $wdxsbList = $this->wdxsb[$jiNian['m'][1]];
            $wdName = str_replace(['阴', '阳'], '', $name);
            $keyId = match ($wdName) {
                '木' => 0,
                '火' => 1,
                '土' => 2,
                '金' => 3,
                '水' => 4,
            };
            $fen = round(bcmul($wdxsbList[$keyId], $num, 2), 2);
            $result[$name] = $fen;
        }
        // 第五步，百分百
        $sum = array_sum($result);
        foreach ($result as $name => $value) {
            $result[$name] = ['num' => $value, 'bfb' => round(($value / $sum) * 100, 2)];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-五行个数分配
     * @param array $jiNian 纪年
     * @return array
     * @throws Exception
     */
    protected function qcfsNldbWxgsfp(array $jiNian)
    {
        $_god = $this->lunar->_getGod();
        $jnNum = $jnNum1 = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $jnNum[$tgWx]++;
            $jnNum1[$tgWx]++;
            $jnNum[$dzWx]++;
        }
        foreach ($_god as $v) {
            foreach ($v['hide'] as $k1 => $v1) {
                $wx = $wuXingAttr[$v1];
                $jnNum1[$wx]++;
            }
        }
        $result = [
            'chang' => $jnNum,
            'chang_bfb' => [],
            'quan' => $jnNum1,
            'quan_bfb' => [],
        ];
        $changZong = array_sum($result['chang']);
        $quanZong = array_sum($result['quan']);
        foreach ($result['chang'] as $name => $num) {
            $result['chang_bfb'][$name] = round(($num / $changZong) * 100, 2);
        }
        foreach ($result['quan'] as $name => $num) {
            $result['quan_bfb'][(string)$name] = round(($num / $quanZong) * 100, 2);
        }
        return $result;
    }

    /**
     * 根据纪年，获取天干地支强度
     * @param array $jiNian 纪年
     * @return array
     */
    protected function tgdzqdb(array $jiNian): array
    {
        // 天干强度数值表
        $calenT = [
            '寅甲' => '1.14', '寅乙' => '1.14', '寅丙' => '1.2', '寅丁' => '1.2', '寅戊' => '1.06', '寅己' => '1.06', '寅庚' => '1', '寅辛' => '1',
            '寅壬' => '1', '寅癸' => '1', '卯甲' => '1.2', '卯乙' => '1.2', '卯丙' => '1.2', '卯丁' => '1.2', '卯戊' => '1', '卯己' => '1', '卯庚' => '1',
            '卯辛' => '1', '卯壬' => '1', '卯癸' => '1', '辰甲' => '1.1', '辰乙' => '1.1', '辰丙' => '1.06', '辰丁' => '1.06', '辰戊' => '1.1',
            '辰己' => '1.1', '辰庚' => '1.1', '辰辛' => '1.1', '辰壬' => '1.04', '辰癸' => '1.04', '巳甲' => '1', '巳乙' => '1', '巳丙' => '1.14',
            '巳丁' => '1.14', '巳戊' => '1.14', '巳己' => '1.14', '巳庚' => '1.06', '巳辛' => '1.06', '巳壬' => '1.06', '巳癸' => '1.06', '午甲' => '1',
            '午乙' => '1', '午丙' => '1.2', '午丁' => '1.2', '午戊' => '1.2', '午己' => '1.2', '午庚' => '1', '午辛' => '1', '午壬' => '1', '午癸' => '1',
            '未甲' => '1.04', '未乙' => '1.04', '未丙' => '1.1', '未丁' => '1.1', '未戊' => '1.16', '未己' => '1.16', '未庚' => '1.1', '未辛' => '1.1',
            '未壬' => '1', '未癸' => '1', '申甲' => '1.06', '申乙' => '1.06', '申丙' => '1', '申丁' => '1', '申戊' => '1', '申己' => '1', '申庚' => '1.14',
            '申辛' => '1.14', '申壬' => '1.2', '申癸' => '1.2', '酉甲' => '1', '酉乙' => '1', '酉丙' => '1', '酉丁' => '1', '酉戊' => '1', '酉己' => '1',
            '酉庚' => '1.2', '酉辛' => '1.2', '酉壬' => '1.2', '酉癸' => '1.2', '戌甲' => '1', '戌乙' => '1', '戌丙' => '1.04', '戌丁' => '1.04',
            '戌戊' => '1.14', '戌己' => '1.14', '戌庚' => '1.16', '戌辛' => '1.16', '戌壬' => '1.06', '戌癸' => '1.06', '亥甲' => '1.2', '亥乙' => '1.2',
            '亥丙' => '1', '亥丁' => '1', '亥戊' => '1', '亥己' => '1', '亥庚' => '1', '亥辛' => '1', '亥壬' => '1.14', '亥癸' => '1.14', '子甲' => '1.2',
            '子乙' => '1.2', '子丙' => '1', '子丁' => '1', '子戊' => '1', '子己' => '1', '子庚' => '1', '子辛' => '1', '子壬' => '1.2', '子癸' => '1.2',
            '丑甲' => '1.06', '丑乙' => '1.06', '丑丙' => '1', '丑丁' => '1', '丑戊' => '1.1', '丑己' => '1.1', '丑庚' => '1.14', '丑辛' => '1.14', '丑壬' => '1.1', '丑癸' => '1.1',
        ];
        // 地支强度数值表
        $calenD = [
            '寅' => [
                '子' => ['癸寅' => 1],
                '丑' => ['癸寅' => 0.3, '辛寅' => '0.2', '己寅' => '0.53'],
                '寅' => ['丙寅' => 0.36, '甲寅' => '0.798'],
                '卯' => ['乙寅' => 1.14],
                '辰' => ['乙寅' => 0.342, '癸寅' => '0.2', '戊寅' => '0.53'],
                '巳' => ['庚寅' => 0.3, '丙寅' => '0.84'],
                '午' => ['丁寅' => 1.2],
                '未' => ['丁寅' => 0.36, '乙寅' => '0.228', '己寅' => '0.53'],
                '申' => ['壬寅' => 0.3, '庚寅' => '0.7'],
                '酉' => ['辛寅' => 1],
                '戌' => ['辛寅' => 0.3, '丁寅' => '0.24', '戊寅' => '0.53'],
                '亥' => ['甲寅' => 0.342, '壬寅' => '0.7'],
            ],
            '卯' => [
                '子' => ['癸卯' => 1],
                '丑' => ['癸卯' => 0.3, '辛卯' => '0.2', '己卯' => '0.5'],
                '寅' => ['丙卯' => 0.36, '甲卯' => '0.84'],
                '卯' => ['乙卯' => 1.2],
                '辰' => ['乙卯' => 0.36, '癸卯' => '0.2', '戊卯' => '0.5'],
                '巳' => ['庚卯' => 0.3, '丙卯' => '0.84'],
                '午' => ['丁卯' => 1.2],
                '未' => ['丁卯' => 0.36, '乙卯' => '0.24', '己卯' => '0.5'],
                '申' => ['壬卯' => 0.3, '庚卯' => '0.7'],
                '酉' => ['辛卯' => 1],
                '戌' => ['辛卯' => 0.3, '丁卯' => '0.24', '戊卯' => '0.5'],
                '亥' => ['甲卯' => 0.36, '壬卯' => '0.7'],
            ],
            '辰' => [
                '子' => ['癸辰' => 1.04],
                '丑' => ['癸辰' => 0.312, '辛辰' => '0.23', '己辰' => '0.55'],
                '寅' => ['丙辰' => 0.318, '甲辰' => '0.77'],
                '卯' => ['乙辰' => 1.1],
                '辰' => ['乙辰' => 0.33, '癸辰' => '0.208', '戊辰' => '0.55'],
                '巳' => ['庚辰' => 0.33, '丙辰' => '0.742'],
                '午' => ['丁辰' => 1.06],
                '未' => ['丁辰' => 0.318, '乙辰' => '0.22', '己辰' => '0.55'],
                '申' => ['壬辰' => 0.312, '庚辰' => '0.77'],
                '酉' => ['辛辰' => 1.1],
                '戌' => ['辛辰' => 0.33, '丁辰' => '0.212', '戊辰' => '0.55'],
                '亥' => ['甲辰' => 0.33, '壬辰' => '0.728'],
            ],
            '巳' => [
                '子' => ['癸巳' => 1.06],
                '丑' => ['癸巳' => 0.318, '辛巳' => '0.212', '己巳' => '0.57'],
                '寅' => ['丙巳' => 0.342, '甲巳' => '0.7'],
                '卯' => ['乙巳' => 1],
                '辰' => ['乙巳' => 0.3, '癸巳' => '0.2', '戊巳' => '0.6'],
                '巳' => ['庚巳' => 0.3, '丙巳' => '0.84'],
                '午' => ['丁巳' => 1.14],
                '未' => ['丁巳' => 0.342, '乙巳' => '0.2', '己巳' => '0.57'],
                '申' => ['壬巳' => 0.318, '庚巳' => '0.742'],
                '酉' => ['辛巳' => 1.06],
                '戌' => ['辛巳' => 0.318, '丁巳' => '0.228', '戊巳' => '0.57'],
                '亥' => ['甲巳' => 0.3, '壬巳' => '0.742'],
            ],
            '午' => [
                '子' => ['癸午' => 1],
                '丑' => ['癸午' => 0.3, '辛午' => '0.2', '己午' => '0.6'],
                '寅' => ['丙午' => 0.36, '甲午' => '0.7'],
                '卯' => ['乙午' => 1],
                '辰' => ['乙午' => 0.3, '癸午' => '0.2', '戊午' => '0.6'],
                '巳' => ['庚午' => 0.3, '丙午' => '0.84'],
                '午' => ['丁午' => 1.2],
                '未' => ['丁午' => 0.36, '乙午' => '0.2', '己午' => '0.6'],
                '申' => ['壬午' => 0.3, '庚午' => '0.7'],
                '酉' => ['辛午' => 1],
                '戌' => ['辛午' => 0.3, '丁午' => '0.24', '戊午' => '0.6'],
                '亥' => ['甲午' => 0.3, '壬午' => '0.7'],
            ],
            '未' => [
                '子' => ['癸未' => 1],
                '丑' => ['癸未' => 0.3, '辛未' => '0.22', '己未' => '0.58'],
                '寅' => ['丙未' => 0.33, '甲未' => '0.728'],
                '卯' => ['乙未' => 1.04],
                '辰' => ['乙未' => 0.312, '癸未' => '0.2', '戊未' => '0.58'],
                '巳' => ['庚未' => 0.33, '丙未' => '0.798'],
                '午' => ['丁未' => 1.1],
                '未' => ['丁未' => 0.33, '乙未' => '0.208', '己未' => '0.58'],
                '申' => ['壬未' => 0.3, '庚未' => '0.77'],
                '酉' => ['辛未' => 1.1],
                '戌' => ['辛未' => 0.33, '丁未' => '0.22', '戊未' => '0.58'],
                '亥' => ['甲未' => 0.312, '壬未' => '0.7'],
            ],
            '申' => [
                '子' => ['癸申' => 1.2],
                '丑' => ['癸申' => 0.36, '辛申' => '0.228', '己申' => '0.5'],
                '寅' => ['丙申' => 0.3, '甲申' => '0.742'],
                '卯' => ['乙申' => 1.06],
                '辰' => ['乙申' => 0.318, '癸申' => '0.24', '戊申' => '0.5'],
                '巳' => ['庚申' => 0.342, '丙申' => '0.7'],
                '午' => ['丁申' => 1],
                '未' => ['丁申' => 0.3, '乙申' => '0.212', '己申' => '0.5'],
                '申' => ['壬申' => 0.36, '庚申' => '0.798'],
                '酉' => ['辛申' => 1.14],
                '戌' => ['辛申' => 0.342, '丁申' => '0.2', '戊申' => '5'],
                '亥' => ['甲申' => 0.318, '壬申' => '0.84'],
            ],
            '酉' => [
                '子' => ['癸酉' => 1.2],
                '丑' => ['癸酉' => 0.36, '辛酉' => '0.248', '己酉' => '0.5'],
                '寅' => ['丙酉' => 0.3, '甲酉' => '0.7'],
                '卯' => ['乙酉' => 1],
                '辰' => ['乙酉' => 0.3, '癸酉' => '0.24', '戊酉' => '0.5'],
                '巳' => ['庚酉' => 0.36, '丙酉' => '0.7'],
                '午' => ['丁酉' => 1],
                '未' => ['丁酉' => 0.3, '乙酉' => '0.2', '己酉' => '0.5'],
                '申' => ['壬酉' => 0.36, '庚酉' => '0.84'],
                '酉' => ['辛酉' => 1.2],
                '戌' => ['辛酉' => 0.36, '丁酉' => '0.2', '戊酉' => '0.5'],
                '亥' => ['甲酉' => 0.3, '壬酉' => '0.84'],
            ],
            '戌' => [
                '子' => ['癸戌' => 1.06],
                '丑' => ['癸戌' => 0.318, '辛戌' => '0.232', '己戌' => '0.57'],
                '寅' => ['丙戌' => 0.342, '甲戌' => '0.7'],
                '卯' => ['乙戌' => 1],
                '辰' => ['乙戌' => 0.3, '癸戌' => '0.212', '戊戌' => '0.57'],
                '巳' => ['庚戌' => 0.348, '丙戌' => '0.728'],
                '午' => ['丁戌' => 1.04],
                '未' => ['丁戌' => 0.312, '乙戌' => '0.2', '己戌' => '0.57'],
                '申' => ['壬戌' => 0.318, '庚戌' => '0.812'],
                '酉' => ['辛戌' => 1.16],
                '戌' => ['辛戌' => 0.348, '丁戌' => '0.208', '戊戌' => '0.57'],
                '亥' => ['甲戌' => 0.3, '壬戌' => '0.724']],
            '亥' => [
                '子' => ['癸亥' => 1.14],
                '丑' => ['癸亥' => 0.342, '辛亥' => '0.2', '己亥' => '0.5'],
                '寅' => ['丙亥' => 0.318, '甲亥' => '0.84'],
                '卯' => ['乙亥' => 1.2],
                '辰' => ['乙亥' => 0.36, '癸亥' => '0.228', '戊亥' => '0.5'],
                '巳' => ['庚亥' => 0.3, '丙亥' => '0.742'],
                '午' => ['丁亥' => 1.06],
                '未' => ['丁亥' => 0.318, '乙亥' => '0.24', '己亥' => '0.5'],
                '申' => ['壬亥' => 0.342, '庚亥' => '0.7'],
                '酉' => ['辛亥' => 1],
                '戌' => ['辛亥' => 0.3, '丁亥' => '0.212', '戊亥' => '0.5'],
                '亥' => ['甲亥' => 0.36, '壬亥' => '0.798'],
            ],
            '子' => [
                '子' => ['癸子' => 1.2],
                '丑' => ['癸子' => 0.36, '辛子' => '0.2', '己子' => '0.5'],
                '寅' => ['丙子' => 0.3, '甲子' => '0.84'],
                '卯' => ['乙子' => 1.2],
                '辰' => ['乙子' => 0.36, '癸子' => '0.24', '戊子' => '0.5'],
                '巳' => ['庚子' => 0.3, '丙子' => '0.7'],
                '午' => ['丁子' => 1],
                '未' => ['丁子' => 0.3, '乙子' => '0.24', '己子' => '0.5'],
                '申' => ['壬子' => 0.36, '庚子' => '0.7'],
                '酉' => ['辛子' => 1],
                '戌' => ['辛子' => 0.3, '丁子' => '0.2', '戊子' => '0.5'],
                '亥' => ['甲子' => 0.36, '壬子' => '0.84'],
            ],
            '丑' => [
                '子' => ['癸丑' => 1.1],
                '丑' => ['癸丑' => 0.33, '辛丑' => '0.228', '己丑' => '0.55'],
                '寅' => ['丙丑' => 0.3, '甲丑' => '0.742'],
                '卯' => ['乙丑' => 1.06],
                '辰' => ['乙丑' => 0.318, '癸丑' => '0.22', '戊丑' => '0.55'],
                '巳' => ['庚丑' => 0.342, '丙丑' => '0.7'],
                '午' => ['丁丑' => 1],
                '未' => ['丁丑' => 0.3, '乙丑' => '0.212', '己丑' => '0.55'],
                '申' => ['壬丑' => 0.33, '庚丑' => '0.798'],
                '酉' => ['辛丑' => 1.14],
                '戌' => ['辛丑' => 0.342, '丁丑' => '0.2', '戊丑' => '0.55'],
                '亥' => ['甲丑' => 0.318, '壬丑' => '0.77'],
            ],
        ];

        // 天干强度数值
        $tgList = [
            "{$jiNian['m'][1]}{$jiNian['y'][0]}" => $calenT["{$jiNian['m'][1]}{$jiNian['y'][0]}"],
            "{$jiNian['m'][1]}{$jiNian['m'][0]}" => $calenT["{$jiNian['m'][1]}{$jiNian['m'][0]}"],
            "{$jiNian['m'][1]}{$jiNian['d'][0]}" => $calenT["{$jiNian['m'][1]}{$jiNian['d'][0]}"],
            "{$jiNian['m'][1]}{$jiNian['h'][0]}" => $calenT["{$jiNian['m'][1]}{$jiNian['h'][0]}"],
        ];
        // 地支强度数值
        $dzList = [
            $calenD[$jiNian['m'][1]] [$jiNian['y'][1]],
            $calenD[$jiNian['m'][1]] [$jiNian['m'][1]],
            $calenD[$jiNian['m'][1]] [$jiNian['d'][1]],
            $calenD[$jiNian['m'][1]] [$jiNian['h'][1]],
        ];

        // 合并
        $mergeList = [];
        foreach (array_merge($tgList, $dzList) as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $k => $v) {
                    $mergeList[$k] = ($mergeList[$k] ?? 0) + $v;
                }
            } else {
                $mergeList[$key] = ($mergeList[$key] ?? 0) + $value;
            }
        }
        return $mergeList;
    }

    /**
     * 求财方式-能量度表-阴阳分配
     * @param array $jiNian 纪年
     * @return array
     */
    protected function qcfsNldbYyfp(array $jiNian)
    {
        $result = [
            'yang' => 0,
            'ying' => 0,
        ];
        foreach ($this->tgdzqdb($jiNian) as $key => $value) {
            // 判断是否为
            if (Str::contains($key, ['甲', '丙', '戊', '庚', '壬'])) {
                // $result['yang_zi'][] = $key;
                $result['yang'] = bcadd(($result['yang'] ?? 0), $value, 2);
            } else {
                // $result['ying_zi'][] = $key;
                $result['ying'] = bcadd(($result['ying'] ?? 0), $value, 2);
            }
        }
        $sum = array_sum([$result['yang'], $result['ying']]);
        foreach ($result as $key => $value) {
            $result[$key] = ['num' => round($value, 2), 'bfb' => round(($value / $sum) * 100, 2)];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-阴阳分配
     * @param array $jiNian 纪年
     * @return array
     */
    protected function qcfsNldbLiYyfp(array $jiNian, array $jnWx, array $_god)
    {
        $result = [];

        // 第一步，计算4个天干，每个固定100分
        foreach ($jiNian as $k => $v) {
            if (isset($result[$v[0]])) {
                $result[$v[0]] += 100;
            } else {
                $result[$v[0]] = 100;
            }
        }
        // 第二步，天干转阴阳五行
        foreach ($result as $key => $value) {
            $name = $this->tgZyywx[$key];
            unset($result[$key]);
            $result[$name] = $value;
        }
        // 第三步，计算藏干组合  纪年的地支jinian.y.1+地支藏干_god.year.hide[]
        foreach ($jiNian as $k => $v) {
            $key = "{$v[1]}";
            // 循环藏干组合
            $cgKey = match ($k) {
                'y' => 'year',
                'm' => 'month',
                'd' => 'day',
                'h' => 'hour',
            };
            foreach ($_god[$cgKey]['hide'] as $name) {
                $keyName = "{$key}{$name}";

                // 取得地支藏干的分数表
                $fenzhi = $this->dzcgfsb[$keyName];
                // 把名字切割成一个字，去匹配 $tgpdwx 得到五行
                $keyNameArr = preg_split('/(?<!^)(?!$)/u', $keyName);
                foreach ($keyNameArr as $va) {
                    if (isset($this->tgZyywx[$va])) {
                        $wx = $this->tgZyywx[$va];
                        if (isset($result[$wx])) {
                            $result[$wx] += $fenzhi;
                        } else {
                            $result[$wx] = $fenzhi;
                        }
                        break;
                    }
                }
            }
        }
        // 第四步，分组，阴一组，阳一组，金木水火土
        $result2 = $result;
        $result = [];
        foreach ($result2 as $name => $value) {
            if (Str::contains($name, '阳')) {
                $na = str_replace('阳', '', $name);
                $result['yang'][$na] = $value;
            } else {
                $na = str_replace('阴', '', $name);
                $result['ying'][$na] = $value;
            }
        }
        // 第五步，根据每个月的旺度系数，纪年的地支jinian.m.1
        // 总分
        $sum = 0;
        foreach ($result as $type => $list) {
            foreach ($list as $name => $num) {
                $wdxsbList = $this->wdxsb[$jiNian['m'][1]];
                $keyId = match ($name) {
                    '木' => 0,
                    '火' => 1,
                    '土' => 2,
                    '金' => 3,
                    '水' => 4,
                };
                $fen = round(bcmul($wdxsbList[$keyId], $num, 2), 2);
                $result[$type][$name] = $fen;
                $sum = round(bcadd($fen, $sum, 2), 2);
            }
        }
        // 第六步，计算总分和占比
        foreach ($result as $type => $list) {
            // 各阴阳总分
            $sum2 = 0;
            foreach ($list as $name => $num) {
                $sum2 = round(bcadd($num, $sum2, 2), 2);
            }
            $result[$type] = ['num' => $sum2, 'bfb' => round(($sum2 / $sum) * 100, 2)];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-六亲形式
     * @param string $jiNianWx 纪年日干五行
     * @return array
     */
    protected function qcfsNldbLqxs($jiNianWx)
    {
        $wxfp = $this->qcfsNldbWxfp();
        $result = [];
        foreach ($wxfp as $k => $v) {
            $result[$this->zhssb[$jiNianWx][$k]] = $v;
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-六亲形式
     * @param string $jiNianWx 纪年日干五行
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcfsNldbLiLqxs($jiNianWx, array $jiNian, array $jnWx, array $_god)
    {
        $wxfp = $this->qcfsNldbLiWxfp($jiNian, $jnWx, $_god);
        $result = [];
        foreach ($wxfp as $k => $v) {
            $result[$this->zhssb[$jiNianWx][$k]] = $v;
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-五行分配
     * @return array
     */
    protected function qcfsNldbWxfp()
    {
        $wuxingNum = $this->lunar->getWuxingNum();
        $zongNum = array_sum($wuxingNum);
        $result = [];
        foreach ($wuxingNum as $k => $num) {
            $result[$k] = [
                'num' => $num,
                'bfb' => round(($num / $zongNum) * 100, 2),
            ];
        }
        return $result;
    }

    /**
     * 求财方式-能量度表-李氏-五行分配
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @param array $_god 十神、藏干
     * @return array
     */
    protected function qcfsNldbLiWxfp(array $jiNian, array $jnWx, array $_god)
    {
        // 初始分数
        $number = [
            '金' => 0,
            '木' => 0,
            '水' => 0,
            '火' => 0,
            '土' => 0,
        ];
        // 第一步，计算4个天干，每个固定100分
        $number[$jnWx['y']['wx'][0]] += 100;
        $number[$jnWx['m']['wx'][0]] += 100;
        $number[$jnWx['d']['wx'][0]] += 100;
        $number[$jnWx['h']['wx'][0]] += 100;
        // 第二步，计算藏干组合  纪年的地支jinian.y.1+地支藏干_god.year.hide[]
        foreach ($jiNian as $k => $v) {
            $key = "{$v[1]}";
            // 循环藏干组合
            $cgKey = match ($k) {
                'y' => 'year',
                'm' => 'month',
                'd' => 'day',
                'h' => 'hour',
            };
            foreach ($_god[$cgKey]['hide'] as $name) {
                $keyName = "{$key}{$name}";

                // 取得地支藏干的分数表
                $fenzhi = $this->dzcgfsb[$keyName];
                // 把名字切割成一个字，去匹配 $tgpdwx 得到五行
                $keyNameArr = preg_split('/(?<!^)(?!$)/u', $keyName);
                foreach ($keyNameArr as $va) {
                    if (isset($this->tgpdwx[$va])) {
                        $wx = $this->tgpdwx[$va];
                        $number[$wx] += $fenzhi;
                        break;
                    }
                }
            }
        }
        // 第三步，获取五行在每个月的旺度系数，纪年的地支jinian.m.1
        $wdxsbList = $this->wdxsb[$jiNian['m'][1]];
        $number['金'] = round(bcmul($number['金'], $wdxsbList[3], 2), 2);
        $number['木'] = round(bcmul($number['木'], $wdxsbList[0], 2), 2);
        $number['水'] = round(bcmul($number['水'], $wdxsbList[4], 2), 2);
        $number['火'] = round(bcmul($number['火'], $wdxsbList[1], 2), 2);
        $number['土'] = round(bcmul($number['土'], $wdxsbList[2], 2), 2);
        // 第四步，百分百
        $sum = array_sum($number);
        foreach ($number as $key => $value) {
            $number[$key] = ['num' => $value, 'bfb' => round(($value / $sum) * 100, 2)];
        }
        return $number;
    }

    /**
     * 流通算法
     * @param array $jiNian 纪年
     * @param array $jnWx 纪年五行
     * @return array
     */
    protected function liutong(array $jiNian, array $jnWx): array
    {
        // 同柱：1.年柱天干与年柱地支  2.月柱天干与月柱地支  3.日柱天干与日柱地支  4.时柱天干与时柱地支
        // 不同柱：5.年柱天干与月柱天干  6.月柱天干与日柱天干 7.日柱天干与时柱天干   8.年柱地支与月柱地支  9.月柱地支与日柱地支    10.日柱地支与时柱地支

        // 五行通阻作用表
        $wxtzList = [
            '木木' => '通',
            '木火' => '通',
            '木土' => '克',
            '木金' => '克',
            '木水' => '通',
            '火木' => '通',
            '火火' => '通',
            '火土' => '通',
            '火金' => '克',
            '火水' => '克',
            '土木' => '克',
            '土火' => '通',
            '土土' => '通',
            '土金' => '通',
            '土水' => '克',
            '金木' => '克',
            '金火' => '克',
            '金土' => '通',
            '金金' => '通',
            '金水' => '通',
            '水木' => '通',
            '水火' => '克',
            '水土' => '克',
            '水金' => '通',
            '水水' => '通',
        ];

        // 天干关系汇总表
        $txgxList = [
            '甲己', '己甲',
            '乙庚', '庚乙',
            '丙辛', '辛丙',
            '丁壬', '壬丁',
            '戊癸', '癸戊',
            '己甲', '甲己',
            '庚乙', '乙庚',
            '辛丙', '丙辛',
            '壬丁', '丁壬',
            '癸戊', '戊癸',
        ];

        // 地支关系汇总表
        $dzgxList = [
            '子午' => '冲', '子酉' => '破', '子未' => '害', '子卯' => '刑',
            '丑未' => '冲', '丑辰' => '破', '丑午' => '害', '丑戌' => '刑',
            '寅申' => '冲', '寅亥' => '破', '寅巳' => '害', '寅巳' => '刑',
            '卯酉' => '冲', '卯午' => '破', '卯辰' => '害', '卯子' => '刑',
            '辰戌' => '冲', '辰丑' => '破', '辰卯' => '害', '辰辰' => '刑',
            '巳亥' => '冲', '巳申' => '破', '巳寅' => '害', '巳申' => '刑',
            '午子' => '冲', '午卯' => '破', '午丑' => '害', '午午' => '刑',
            '未丑' => '冲', '未戌' => '破', '未子' => '害', '未丑' => '刑',
            '申寅' => '冲', '申巳' => '破', '申亥' => '害', '申寅' => '刑',
            '酉卯' => '冲', '酉子' => '破', '酉戌' => '害', '酉酉' => '刑',
            '戌辰' => '冲', '戌未' => '破', '戌酉' => '害', '戌未' => '刑',
            '亥巳' => '冲', '亥寅' => '破', '亥申' => '害', '亥亥' => '刑',
        ];

        // 地支六合
        $dzlhList = [
            '子丑',
            '丑子',
            '寅亥',
            '卯戌',
            '辰酉',
            '巳申',
            '午未',
            '未午',
            '申巳',
            '酉辰',
            '戌卯',
            '亥寅',
        ];

        $ltNum = 0;
        $zaNum = 0;
        $zaGx = [];

        // 同一柱的情况下，1、2、3、4
        $tzList = [
            // 年柱天干与年柱地支
            1 => $jnWx['y']['wx'],
            // 月柱天干与月柱地支
            2 => $jnWx['m']['wx'],
            // 日柱天干与日柱地支
            3 => $jnWx['d']['wx'],
            // 时柱天干与时柱地支
            4 => $jnWx['h']['wx'],
        ];
        foreach ($tzList as $k => $v) {
            // 五行相同(五行字一样) || 五行相生(五行通阻作用表)
            $kv = implode('', $v);
            if ($v[0] == $v[1] || (isset($wxtzList[$kv]) && $wxtzList[$kv] == '通')) {
                $ltNum++;
            } else {
                $zaNum++;
                $zaGx[$k] = $wxtzList[$kv];
            }
        }

        // 不同柱 5、6、7、8、9、10
        $btzList = [
            // 年柱天干与月柱天干
            5 => [
                'tgdz' => [$jiNian['y'][1], $jiNian['m'][1]],
                'wx' => [$jnWx['y']['wx'][0], $jnWx['m']['wx'][0]],
            ],
            // 月柱天干与日柱天干
            6 => [
                'tgdz' => [$jiNian['m'][1], $jiNian['d'][1]],
                'wx' => [$jnWx['m']['wx'][0], $jnWx['d']['wx'][0]],
            ],
            // 日柱天干与时柱天干
            7 => [
                'tgdz' => [$jiNian['d'][1], $jiNian['h'][1]],
                'wx' => [$jnWx['d']['wx'][0], $jnWx['h']['wx'][0]],
            ],
            // 年柱地支与月柱地支
            8 => [
                'tgdz' => [$jiNian['y'][1], $jiNian['m'][1]],
                'wx' => [$jnWx['y']['wx'][1], $jnWx['m']['wx'][1]],
            ],
            // 月柱地支与日柱地支
            9 => [
                'tgdz' => [$jiNian['m'][1], $jiNian['d'][1]],
                'wx' => [$jnWx['m']['wx'][1], $jnWx['d']['wx'][1]],
            ],
            // 日柱地支与时柱地支
            10 => [
                'tgdz' => [$jiNian['d'][1], $jiNian['h'][1]],
                'wx' => [$jnWx['d']['wx'][1], $jnWx['h']['wx'][1]],
            ],
        ];

        foreach ($btzList as $k => $v) {
            $wx = $v['wx'];
            $tgdz = $v['tgdz'];
            $wxkv = implode('', $wx);
            $txdzkv = implode('', $tgdz);
            // 都属于天干的情况
            if (in_array($k, [5, 6, 7])) {
                if (
                    (
                        // 五行相同(五行字一样)
                        $wx[0] == $wx[1] ||
                        // 五行相生(五行通阻作用表)
                        (isset($wxtzList[$wxkv]) && $wxtzList[$wxkv] == '通')
                    ) ||
                    // 合化
                    in_array($wxkv, $txgxList)
                ) {
                    $ltNum++;
                } else {
                    $zaNum++;
                    $zaGx[$k] = $wxtzList[$wxkv];
                }
            } else {
                // 都属于地支的情况
                // 能在 地支关系汇总表 找到关系的就属于阻碍
                if (isset($dzgxList[$txdzkv])) {
                    // 如果是六合数据就是流通
                    if (isset($dzlhList[$txdzkv])) {
                        $ltNum++;
                    } else {
                        $zaNum++;
                        $zaGx[$k] = $dzgxList[$txdzkv];
                    }
                } else {
                    // 地支关系表存在就是阻碍
                    if (isset($wxtzList[$wxkv]) && $wxtzList[$wxkv] == '克') {
                        $zaNum++;
                        $zaGx[$k] = $wxtzList[$wxkv];
                    } else {
                        $ltNum++;

                    }
                }
            }
        }

        return [
            // 流通数
            'lt_num' => $ltNum,
            // 阻碍数
            'za' => [
                'num' => $zaNum,
                'gx' => $zaGx,
            ],
            // 流通性
            'ltx' => match ($ltNum) {
                0, 1, 2, 3, 4, 5 => '不佳',
                6, 7, 8 => '中等',
                9, 10 => '极佳',
            },
        ];
    }

    /**
     * 调侯算法
     * @param array $jiNian 纪年
     * @param array $god 藏干十神带上气值
     * @return array
     */
    protected function diaohou(array $jiNian, array $god)
    {
        $yueZhi = $jiNian['m'][1];
        $riGan = $jiNian['d'][0];

        // 逐月调用用神
        $yueShenList = [
            '寅' => [
                '甲' => '丙 癸',
                '乙' => '丙 癸',
                '丙' => '壬 庚',
                '丁' => '甲 庚',
                '戊' => '丙 甲 癸',
                '己' => '丙 庚 甲',
                '庚' => '戊 甲 丙 丁 壬',
                '辛' => '己 壬 庚',
                '壬' => '庚 丙 戊',
                '癸' => '辛 丙',
            ],
            '卯' => [
                '甲' => '庚 丙 丁 戊 己',
                '乙' => '丙 癸',
                '丙' => '壬 己',
                '丁' => '甲 庚',
                '戊' => '丙 甲 癸',
                '己' => '甲 癸 丙',
                '庚' => '丁 甲 庚 丙',
                '辛' => '壬 甲',
                '壬' => '戊 辛 庚',
                '癸' => '庚 辛',
            ],
            '辰' => [
                '甲' => '庚 丁 壬',
                '乙' => '癸 丙 戊',
                '丙' => '壬 甲',
                '丁' => '庚 甲',
                '戊' => '甲 丙 癸',
                '己' => '丙 癸 甲',
                '庚' => '甲 丁 壬 癸',
                '辛' => '壬 甲',
                '壬' => '甲 庚',
                '癸' => '丙 辛 甲',
            ],
            '巳' => [
                '甲' => '癸 丁 庚',
                '乙' => '癸',
                '丙' => '壬 癸 庚',
                '丁' => '甲 庚',
                '戊' => '甲 丙 癸',
                '己' => '癸 丙',
                '庚' => '壬 戊 丙 丁',
                '辛' => '壬 甲 癸',
                '壬' => '壬 辛 庚 癸',
                '癸' => '辛',
            ],
            '午' => [
                '甲' => '癸 庚 丁',
                '乙' => '癸 丙',
                '丙' => '壬 庚',
                '丁' => '壬 庚 癸',
                '戊' => '壬 甲 丙',
                '己' => '癸 丙',
                '庚' => '壬 癸',
                '辛' => '壬 己 癸',
                '壬' => '癸 庚 辛',
                '癸' => '庚 辛 壬 癸',
            ],
            '未' => [
                '甲' => '癸 庚 丁',
                '乙' => '癸 丙',
                '丙' => '壬 戊',
                '丁' => '甲 壬 庚',
                '戊' => '癸 丙 甲',
                '己' => '癸 丙',
                '庚' => '丁 甲',
                '辛' => '壬 庚 甲',
                '壬' => '辛 甲',
                '癸' => '庚 辛 壬 癸',
            ],
            '申' => [
                '甲' => '庚 丁 壬',
                '乙' => '丙 癸 己',
                '丙' => '壬 癸',
                '丁' => '甲 庚 丙 戊',
                '戊' => '丙 癸 甲',
                '己' => '丙 癸',
                '庚' => '丁 甲',
                '辛' => '壬 甲 戊',
                '壬' => '戊 丁',
                '癸' => '丁',
            ],
            '酉' => [
                '甲' => '庚 丁 丙',
                '乙' => '癸 丙 丁',
                '丙' => '壬 癸',
                '丁' => '甲 庚 丙 戊',
                '戊' => '丙 癸',
                '己' => '丙 癸',
                '庚' => '丁 甲 丙',
                '辛' => '壬 甲',
                '壬' => '甲 庚',
                '癸' => '辛 丙',
            ],
            '戌' => [
                '甲' => '庚 甲 丁 壬 癸',
                '乙' => '癸 辛',
                '丙' => '甲 壬',
                '丁' => '甲 庚 戊',
                '戊' => '甲 丙 癸',
                '己' => '甲 丙 癸',
                '庚' => '甲 壬',
                '辛' => '壬 甲',
                '壬' => '甲 丙',
                '癸' => '辛 甲 壬 癸',
            ],
            '亥' => [
                '甲' => '庚 丁 丙 戊',
                '乙' => '丙 戊',
                '丙' => '甲 戊 庚 壬',
                '丁' => '甲 庚',
                '戊' => '甲 丙',
                '己' => '丙 甲 戊',
                '庚' => '丁 丙',
                '辛' => '壬 丙',
                '壬' => '戊 丙 庚',
                '癸' => '庚 辛 戊 丁',
            ],
            '子' => [
                '甲' => '丁 庚 丙',
                '乙' => '丙',
                '丙' => '壬 戊 己',
                '丁' => '甲 庚',
                '戊' => '丙 甲',
                '己' => '丙 甲 戊',
                '庚' => '丁 甲 丙',
                '辛' => '丙 戊 壬 申',
                '壬' => '戊 丙',
                '癸' => '丙 辛',
            ],
            '丑' => [
                '甲' => '丁 庚 丙',
                '乙' => '丙',
                '丙' => '壬 甲',
                '丁' => '甲 庚',
                '戊' => '丙 甲',
                '己' => '丙 甲 戊',
                '庚' => '丙 丁 甲',
                '辛' => '丙 壬 戊 己',
                '壬' => '丙 丁 甲',
                '癸' => '丙 丁',
            ],
        ];
        // 调侯用神
        $yongShen = explode(' ', $yueShenList[$yueZhi][$riGan]);

        // 天干列表，包含藏干十神
        $tgList = [];
        $cgssList = [];
        foreach ($jiNian as $type => $item) {
            $godkey = match ($type) {
                'y' => 'year',
                'm' => 'month',
                'd' => 'day',
                'h' => 'hour',
            };
            $tgList[] = $item[0];
            $cgss = implode('', array_merge($god[$godkey]['hide'], $god[$godkey]['god'], $god[$godkey]['qi']));
            $cgssList = array_merge($cgssList, preg_split('/(?<!^)(?!$)/u', $cgss));
        }
        $bzc = [
            // '$cgss' => $cgss,
            // '$cgssList' => $cgssList,
        ];
        foreach ($yongShen as $value) {
            // 判断藏还是透
            // 判断是否在天干里存在
            if (in_array($value, $tgList)) {
                $bzc['tou'][] = $value;
            }
            if (in_array($value, $cgssList)) {
                $bzc['chang'][] = $value;
            }
        }

        return [
            'ys' => $yongShen,
            'bzc' => $bzc,
            'sp' => '写死，不佳，还没做',
        ];
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnwx(array $jiNian): array
    {
        $result = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        return $result;
    }

    /**
     * 纪年阴阳
     * @param array $jiNian
     * @return array
     */
    protected function getJnYy(array $jiNian): array
    {
        $result = [];
        foreach ($jiNian as $k => $v) {
            $result[$k] = [
                BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                BaziExt::getYinYang($v[1]) ? '阳' : '阴',
            ];
        }
        return $result;
    }

    /**
     * 藏干十神带上气值
     * @param array $godHide
     * @return array
     */
    protected function getHideGodWithQi(array $godHide): array
    {
        foreach ($godHide as $k => $v) {
            $num = count($v['god']);
            $godHide[$k]['qi'] = $this->getGodQi($num);
        }
        return $godHide;
    }

    /**
     * 获得地支十神气值
     * @param int $number
     * @return string[]
     */
    protected function getGodQi(int $number): array
    {
        $listQi = [
            1 => ['主'], 2 => ['主', '余'], 3 => ['主', '中', '余'],
        ];
        return $listQi[$number] ?? $listQi[3];
    }

    /**
     * 组合盘数据
     * @param array $gz
     * @param array $jiNian
     * @return array
     */
    protected function getBasePan(array $gz, array $jiNian): array
    {
        $res = BaziCommon::getBasePan($gz, $jiNian);
        $nuber = count($res['_god']['god']);
        $res['_god']['qi'] = $this->getGodQi($nuber);
        return $res;
    }

    /**
     * 获取所有流年
     * @param array $xys 喜用忌闲仇
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getLiunianYears(array $xys)
    {
        $liuNian = new LiuNian($this->lunar, $this->orginData['otime']);
        $liuNian->setXy($xys);
        return $liuNian->getLiuNian();
    }

    /**
     * 大运
     * @param bool $isliu 是否显示流年数据
     * @return array
     * @throws \Exception
     */
    protected function getDayun(bool $isliu = false): array
    {
        // 纪年
        $jiNian = $this->lunar->getLunarTganDzhi();
        $sex = $this->lunar->sex;
        $time = $this->lunar->dateTime->getTimestamp();
        $_tianGan = [
            0 => '庚', -9 => '辛', -8 => '壬', -7 => '癸', -6 => '甲',
            -5 => '乙', -4 => '丙', -3 => '丁', -2 => '戊', -1 => '己',
        ];
        $_diZhi = [
            0 => '申', -11 => '酉', -10 => '戌', -9 => '亥', -8 => '子', -7 => '丑',
            -6 => '寅', -5 => '卯', -4 => '辰', -3 => '巳', -2 => '午', -1 => '未',
        ];

        // 判断大运天干顺序，如果是阳男或阴女就是走正序，如果是阴男或阳女就是走逆序
        // 男阳 女阴图    年柱天干
        if (in_array($jiNian['y'][0], ['甲', '丙', '戊', '庚', '壬'])) {// 判断是否为阳
            // 如果等于0    说明是顺时针，  如果等于 1  说明是逆时针
            $isYY = $sex == 0 ? 0 : 1;
        } else {
            $isYY = $sex == 1 ? 0 : 1;
        }

        // 获取节气(整月)，current当前节气,next下一个节气
        $jieQi = $this->lunar->getJieQi();
        $timezone = new \DateTimeZone('PRC');
        $dt1 = new \DateTime($jieQi['next'][1], $timezone);
        $dt2 = new \DateTime($jieQi['current'][1], $timezone);

        // 算出等于多少秒
        $m = $isYY == 0 ? ($dt1->getTimestamp() - $time) : ($time - $dt2->getTimestamp());
        // 计算几岁   三天计1岁
        $year = floor($m / (24 * 3600 * 3));
        $m = $m - $year * 24 * 3600 * 3;
        // 计算几月  一个时辰按计10天计
        $month = floor($m / (3600 * 6));
        $m = $m - $month * 3600 * 6;
        // 计算几天 一分钟按2小时即一个时辰
        $day = floor($m / (60 * 12));
        $m = $m - $day * 60 * 12;
        // 计算几个时辰
        $shi = floor($m / 60);

        $result = [
            // 大运岁数、起大运周岁、交运周岁、起运
            'age' => [
                'year' => $year,
                'month' => $month,
                'day' => $day,
                'hour' => $shi,
            ],
            // 大运的岁数、年份
            'eight' => [],
        ];

        $j = $isYY == 0 ? 1 : -1;
        $tmp = '+' . $year . ' year +' . $month . ' month +' . $day . ' day +' . $shi . ' hour';
        $firstTime = strtotime($tmp, $time);

        // 第一次大运的阳历年份
        $firstYear = date('Y', $firstTime);
        $uYear = (int)$this->lunar->dateTime->format('Y');
        $jieQi = SolarTerm::getAllJieQi($firstYear);

        // 立春
        $timeLiChun = strtotime($jieQi['立春']);
        if ($timeLiChun > $firstTime && $firstYear > $uYear) {
            $firstYear--;
        }

        // 虚岁
        $firstAge = $firstYear - $uYear + 1;
        $tianGanArr = $this->getArrayShuiMove(Calendar::TIAN_GAN, 6);
        $diZhiArr = $this->getArrayShuiMove(Calendar::DI_ZHI, 8);
        $terrainArr = Ex::getTerrainData();
        for ($i = 0; $i < 10; $i++) {
            // 大运几年
            $offestT = array_search($jiNian['m'][0], $tianGanArr) + $j;
            $offestD = array_search($jiNian['m'][1], $diZhiArr) + $j;

            $chronlogyT = $offestT < 0 ? $_tianGan[$offestT % 10] : $tianGanArr[$offestT % 10];
            $chronlogyD = $offestD < 0 ? $_diZhi[$offestD % 12] : $diZhiArr[$offestD % 12];

            // 获取岁数
            $result['eight']['age'][] = $i * 10 + $firstAge;

            // 获取年
            $result['eight']['year'][] = $firstYear + $i * 10;
            // 大运的天干地支
            $result['eight']['chronlogy_year'][] = $chronlogyT . $chronlogyD;
            // 大运的天干十神
            $result['eight']['_god'][] = $this->lunar->getGodNameByTg($jiNian['d'][0], $chronlogyT);
            // 大运的地支十神
            $result['eight']['_god2'][] = BaziCommon::getGodName($jiNian['d'][0] . $chronlogyD);

            if ($isliu) {
                $result['eight']['terrain'][] = $terrainArr[$jiNian['d'][0] . $chronlogyD] ?? '';
                $result['eight']['na_yin'][] = $this->lunar->getNaYinByGz($chronlogyT . $chronlogyD);
                // 流年
                $addYear = $firstYear + $i * 10;
                for ($y = 0; $y < 10; $y++) {
                    $result['liunian'][$addYear][] = $tianGanArr[($addYear + $y) % 10] . $diZhiArr[($addYear + $y) % 12];
                }
            }
            $j = $isYY == 0 ? $j + 1 : $j - 1;
        }
        return $result;
    }

    /**
     * 对大运增加纳音、神煞、十二长生
     * @param array $xys 喜用神
     * @return array
     * @throws \Exception
     */
    protected function getDayunWith(array $xys): array
    {
        // 大运
        $dayun = $this->getDayun();
        $eightGz = $dayun['eight']['chronlogy_year'];
        $eightYear = $dayun['eight']['year'];
        $listAge = $dayun['eight']['age'];
        // 天干纪年
        $jiNian = $this->lunar->getLunarTganDzhi();
        // 日天干地支
        $jiNianDay = $this->lunar->getLunarGanzhiDay();
        // 五行属性
        $wxAttr = $this->lunar->wuXingAttr;
        // 大运运势分表
        $dyysfb = [
            //【前5年，后5年，整体】
            'yong_xi' => [90, 81, 82], 'yong_yong' => [97, 97, 97], 'yong_ji' => [68, 32, 36], 'yong_qiu' => [75, 47, 50], 'yong_xian' => [83, 64, 66],
            'xi_xi' => [75, 75, 75], 'xi_yong' => [81, 90, 89], 'xi_ji' => [53, 25, 28], 'xi_qiu' => [59, 41, 43], 'xi_xian' => [67, 57, 58],
            'ji_xi' => [25, 53, 50], 'ji_yong' => [32, 68, 64], 'ji_ji' => [3, 3, 3], 'ji_qiu' => [10, 19, 18], 'ji_xian' => [17, 36, 34],
            'qiu_xi' => [41, 59, 57], 'qiu_yong' => [47, 75, 72], 'qiu_ji' => [19, 10, 11], 'qiu_qiu' => [25, 25, 25], 'qiu_xian' => [33, 43, 42],
            'xian_xi' => [57, 67, 66], 'xian_yong' => [64, 83, 80], 'xian_ji' => [36, 17, 20], 'xian_qiu' => [43, 33, 34], 'xian_xian' => [50, 50, 50],
        ];
        // 喜用神名称转换
        $xysmczh = [
            'yong' => '用', 'xi' => '喜', 'ji' => '忌', 'qiu' => '仇', 'xian' => '闲',
        ];
        // 地势数据表
        $terrainData = Ex::getTerrainData();

        // 用订单创建时间获取
        $otime = strtotime($this->orginData['otime']);
        $yearC = date('Y', $otime);
        $i = BaziExt::getKeyWithArray($yearC, $dayun['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        // 当前所在大运(当前所在流年)位置，指的是 eight.year的第几个位置
        $dayun['dqdy_ey_key'] = $i;

        // 神煞
        $shenSha = new ShaShen();
        // 大运的纳音
        $naYinArr = [];
        // 大运的神煞
        $shaArr = [];
        // 大运的十二长生
        $terrainArr = [];
        // 大运总评
        $dyzp = [];
        foreach ($eightGz as $k => $v) {
            // 大运天干地支
            $dyTgdz = Utils::mbStrSplit($v);
            // 日天干+大运地支
            $rtgDydz = $jiNianDay[0] . $dyTgdz[1];

            $terrainArr[] = $terrainData[$rtgDydz] ?? '';
            $naYinArr[] = $this->lunar->getNaYinByGz($v);
            $shaArr[] = $shenSha->liuNianToBazi($dyTgdz, $jiNian);

            $tgWx = $wxAttr[$dyTgdz[0]];
            $dzWx = $wxAttr[$dyTgdz[1]];
            $tgK1 = array_search($tgWx, $xys['xy']);
            $dzK1 = array_search($dzWx, $xys['xy']);

            // 大运总评
            $str = "{$tgK1}_{$dzK1}";
            $fenArr = $dyysfb[$str] ?? $dyysfb['yong_xi'];
            $age = $listAge[$k];
            $fen1 = $fenArr[2];
            $fen2 = 0;
            $age2 = $listAge[$k - 1] ?? 0;
            if ($k >= 1) {
                $fen2 = $resFenxi[$k - 1]['fen'][2] ?? 0;
            }
            $tgShenStr = $xysmczh[$tgK1] ?? '用';
            $shenDzStr = $xysmczh[$dzK1] ?? '用';
            $yunRes = $this->getLiuNianYunShi($age, $str, $fen1, $fen2, $age2);
            $dyzp[] = [
                'fen' => $fenArr,
                'tgdz' => $dyTgdz,
                'year' => $eightYear[$k],
                'wx' => [$dyTgdz[0] . $tgWx . $tgShenStr . '神', $dyTgdz[1] . $dzWx . $shenDzStr . '神'],
                'yun' => $yunRes,
            ];
        }
        // 大运的纳音
        $dayun['eight']['nayin'] = $naYinArr;
        // 大运的神煞
        $dayun['eight']['shensha'] = $shaArr;
        // 大运的十二长生
        $dayun['eight']['terrain'] = $terrainArr;
        // 大运总评
        $dayun['dyzp'] = $dyzp;
        return $dayun;
    }

    /**
     * 获得大运解释
     * @param int $age 年龄
     * @param string $key
     * @param int $fen 当前大运分
     * @param int $fen1 前一大运分
     * @param int $age1 前一大运年龄
     * @return string
     */
    protected function getLiuNianYunShi(int $age, string $key, int $fen, int $fen1, int $age1): string
    {
        if ($age < 19) {
            $list = [
                'yong_xi' => [
                    '本段大运受喜用神的影响，人生的各方面都会比较顺利。你的心态会变得积极起来，面对问题时都能够想出较好的解决办法。学业上也会有所进步，自身的能力会慢慢提高，而昂扬的斗志，也很容易让你有着较好的成绩，但切忌好高骛远，仍然需要脚踏实地的去学习。',
                    '本大运和前一个大运较为相似，总体来说各方面依然比较顺利。人际关系中，不仅能和朋友和睦相处，还能认识一些新朋友。学业上也能取得较大的进步，遇到问题时也会得到家人朋友的帮助。建议在这段大运里，保持一个谦虚的心态，不骄傲不自大，这样才能更上一层楼。',
                ],
                'yong_yong' => [
                    '在该段大运中，你的整体运势都有着上升的趋势。人际关系会变得不错，能够结识一些志同道合的朋友，这些朋友也会带给你很大的帮助。学业上，可能会遇到一些难题，但这些难题都是你前进的垫脚石，不仅能够轻松解决，而且能提升你的能力。人生的各方面都能够蒸蒸日上，但也要注意盛极必衰，不要过度骄傲，免得适得其反。',
                    '本大运和前一个大运相差无几，整体运势都较为不错，各方面都会受到积极的影响。你的心态会变得更乐观，学习时也更有干劲，勤奋的努力能使你获得理想的成绩。情感生活上，一些想法可能会遭到父母的反对，需要你更有耐心的去沟通和交流。',
                ],
                'yong_ji' => [
                    '该段大运中，你的整体运势起伏可能会比较大。学业上阻碍重重，虽然付出了很大的努力，但总是难以达到预期的效果。人际关系也较为不佳，容易与身边的人产生矛盾，影响自身的心态。建议在这段大运里，保持积极向上的心态，继续脚踏实地的学习，虚心听取他人的建议，这样才能平稳的前进。',
                    '对于这个阶段来说，整体运势还是会有一些起伏。对于学习上来说，你容易陷入一种迷局之中。往往都容易产生',
                ],
                'yong_qiu' => [
                    '在这个阶段中，你的运势会处于一种上升的状态，所以在学业上面表现的还算是比较积极，想到什么都会去做，将想法落实到行动上面，也会努力的去完成需要完成的事情。这样的状态下不会遇到太多的困境，能有着不错的收获，好好的利用自己的自信，可能不仅学业，也会带给其他方面一些帮助。',
                    '本阶段的大运和之前的大运相差不多，但在学习上还是会出现一些波动，可能是因为升学和新的环境让你一时难以接收，因而对于成绩方面来说这个阶段可能会有一些小小的波动，但只要能够及时领悟，还是能够很快便回到正轨上来。',
                ],
                'yong_xian' => [
                    '在这个阶段中，你的运势会处于一种上升的状态，所以在学业上面表现的还算是比较积极，想到什么都会去做，将想法落实到行动上面，也会努力的去完成需要完成的事情。这样的状态下不会遇到太多的困境，能有着不错的收获，好好的利用自己的自信，可能不仅学业，也会带给其他方面一些帮助。',
                    '本阶段的大运和之前的大运相差不多，但在学习上还是会出现一些波动，可能是因为升学和新的环境让你一时难以接收，因而对于成绩方面来说这个阶段可能会有一些小小的波动，但只要能够及时领悟，还是能够很快便回到正轨上来。',
                ],
                'xi_xi' => [
                    '在这个阶段中，你的整体运势都较为不错。学业上能够取得较大的进步，可能会遇到一些难题，但都会因为平日里自己助人为乐积攒的好人品，给自己带来一些帮助。同时你也保持着积极向上的态度，对待学习非常的认真，这样的学习态度容易给你带来一个理想的成绩。',
                    '本大运和前一个大运较为相似，人生的各方面都会比较顺利。学业上可能会遇到自身无法突破瓶颈，但会得到同学或师长的帮助，前期的成绩波动还是会比较大，后期方才渐渐进入到一个平稳的阶段中。人际关系会变得良好，容易结识新的朋友。',
                ],
                'xi_yong' => [
                    '这段大运受到喜神的影响，你的整体运势都有着上升的趋势。此时的学习运势较为不错，勤奋努力能够事半功倍，只要脚踏实地，就能得不错的成绩。人际关系也会变得不错，你够给人留下很好的印象，身边的人都愿意和你做朋友，也都把你当成是很信任的朋友，在你遇到困境的时候也愿意伸出援手帮助你。',
                    '在这个阶段里，受喜用神的影响，你的整体运势都较为不错。人际关系会变得不错，乐观向上的你会受到其他人的欢迎，也有可能会结识几个不错的朋友。学业上也能够蒸蒸日上，遇到问题时都能够相处较好的解决办法。但切忌眼高手低，还是需要脚踏实地的走好每一步。',
                ],
                'xi_ji' => [
                    '这段大运受忌神的影响，整体运势都较差。生活中可能会和家人产生一些摩擦，或许是来自父母的唠叨，也或许是你冲动的某个行为。学业上也难以突破瓶颈，你可能会产生一些迷茫的情绪，导致自己止步不前。建议在这段大运中，放宽自己的心态，多和家人进行交流和沟通，这样才能得到别人的理解。此外，学习也要制定合理的计划，脚踏实地才能有所成就。',
                    '在这个运势中，可能会有一些不好的事情发生，特别是在成绩上往往容易让你产生一种无力之感，甚至在某一个阶段中可能会呈现下滑的趋势，而且在情绪上也容易产生一些茫然的状态，喜欢轻易作一些决定而导致影响到后面的事情，需注意多调节一下。',
                ],
                'xi_qiu' => [
                    '在这个阶段总体来说较为平稳，人生不会遇到太大的起伏。人际关系上，你可能会无意间得罪一些人，要注意自己的言行举止。学业能够平稳的前进，但还是会有一些问题需要你勇敢去面对。此外，遇到难以解决的问题时，建议向朋友和家人寻求帮助，他们能够为你提供不错的建议。',
                    '',
                ],
                'xi_xian' => [
                    '在该段大运中，你的整体运势较为平稳，人生的各方面都喜忧参半。人际关系上，能结识不错的朋友，但也容易和朋友发生不愉快，甚至会听到别人对自己的议论。学业上，虽无困难，但想要获得一个好成绩需要付出比往日更多的努力。建议在这段大运里，放宽心态，积极面对他人的非议，虚心听取他人的建议，学会做真正的自己。',
                    '对于这一个运势来说，整体的状态会有一个起伏的时期。而且人生也容易有一些选择，其中最大的莫过于是升学给你带来的一些变化，往往也容易因为身边朋友的变化而感到会有一些无力之感，喜欢轻易做出一些决定，因而容易和家中长辈产生一些争执和隔阂。',
                ],
                'ji_xi' => [
                    '本大运受忌神的影响，你的整体运势都较为一般，甚至有着下降的趋势。学习如果没有合理的计划和阶段性的目标，就不容易进步，你需要摆正自己的心态，为自己制定合理的规划，一步一步去完成。生活中容易和朋友亲人发生争吵，此时你需要主动伸出橄榄枝，主动去解决问题，才能够增进你们的感情。',
                    '在这一步大运中，你的运势上相较于之前的运势并没有太大的一个变化，在学业上也能够合理规划。只是在面对升学后身边的人员变化会让你还感到有一些无力的感觉，甚至是对成绩产生一些影响。但只要放平心态，还是很快便能转变过来。',
                ],
                'ji_yong' => [
                    '在这个阶段中，你的整体运势有着上升的趋势，学业、人际、感情都会比较顺利。此时正是你有发挥空间的一段时期，所以在学习上面需要更加的努力，或者是抓住机会，能够得到较大的提升，或许生活状态会变得十分的忙碌，但是你会变的更加乐观，处于很自在的一种状态，有如鱼得水的感觉。',
                    '在这一个大运中，有用神相助，因而运势上会有一种上升的趋势。在学习上你也许会感到有些吃力，但总的来说最后的成绩还是能够对得起你的努力。只是在交际方面，你可能容易因为和异性走得过近而产生了一些误会，需多注意下距离，免得会有早恋的事情发生。',
                ],
                'ji_ji' => [
                    '在这个阶段中，你整体运势都较差，人生可能会产生较大的起伏。生活中可能会遇到难以掌控问题，会使得你的心态变得消极。人际关系上，也总是得不到亲人朋友的帮助，产生孤独的感觉。建议在这段大运里，多做一些自己喜欢的事情，保持一个良好心态，虚心学习，不断提升自身的能力，这样即可平稳度过该阶段。',
                    '对于这个阶段的你来说，在学习上会陷入一种努力过后却无法达到自己预期的效果，而且对于人际交往上也容易和同学或者朋友产生一些争执。在学校中容易对一些异性产生好感，若是没有加以控制，容易和对方冲动表达自己的内心。',
                ],
                'ji_qiu' => [
                    '在这段大运里，你的整体运势都有着下降的趋势。学业上可能会更为吃力，付出很多努力，但总是难以达到自己预期的目标。生活中，也容易和朋友产生隔阂，并且会受到一些不好的影响，心态会变得浮躁。建议在这段时期，静下心来认清自己的目标，切忌好高骛远，只要脚踏实地，就一定进步。',
                    '身处这个阶段的你会发现不少事情似乎都容易形成一种有心无力的感觉，在学业上更是如此，往往都容易形成一种无法控制的局面。面对升学也倍感压力，甚至父母也容易无法理解你的情绪。因而可能会有些心浮气躁，但及时把控之后还是能够回到正轨。',
                ],
                'ji_xian' => ['该阶段中，你的整体运势都较为平稳。学业运平顺，不会呈现大起大落的起伏，努力学习的同时，也要注意休息，劳逸结合才能事半功倍。这段时期的你喜欢有机会就和朋友出去玩，所以跟朋友之间的关系也非常的和谐，但是和家人的关系也就显得没有那么的让人满意，会有一些日常生活的争吵发生，或许是父母的唠叨，也或许是你的叛逆，但也不会带来太大的影响，沟通过后能够和好如初。', '对于这一步大运来说，你比较大的波动都在学业方面。其中的烦恼可能是升学带来的，更多的是和父母之间的意见差生了一些偏差，甚至会因升学的事情而和他们起一些争执或者冲突。和朋友之间亦是如此，容易在换了学校之后而产生一些疏远以及减少了联系。'],
                'qiu_xi' => ['该段大运整体较为平稳，人生各方面能够在稳中前进。这个阶段是你的学习能够有所收获的时期，但是这并不是意味着你可以坐享其成，只有付出才能获得回报。你需要更努力的去学习，去汲取新的知识，这段时期才能不断的提升自己，为以后的路打好基础。人际关系中，也要更用心去对待别人，才能换回别人的真心。', '对于这步运势来说，相对于前一步大运可能会比较波折一些，在学习上也容易从之前的主动学习变成被动学习，甚至有段时间还会对学习产生一种厌倦，需注意及时调整。而在人际关系中，可能会有一些无力的感觉，因为升学的缘故，容易和一些昔日的好友产生一些隔阂和远离。'],
                'qiu_yong' => ['在该段大运中，你的整体运势较为不错，各方面都呈现良好的状态。脚踏实地能让你在学业上能够取得一定的进步，你心态因此也会更加乐观，遇到问题时都能够很好的处理。生活中，可能会和朋友产生一些分歧，不过这些分歧不会影响你们的感情，反而是让你们彼此进行反思的一个机会，会使你们彼此的感情变得更好。', '对于这个阶段的运势来说并没有太大的波折，无论是在学业上还是在其他方面都和之前大运没有太大的区别。整体来说，在学习上可能会稍微不如意之前，甚至会有厌学的情况出现。不过在和他人的交往中却懂得了收敛自己的心绪，很少随意和同学，朋友或长辈发生一些争执以及冲突。'],
                'qiu_ji' => ['这个阶段由于忌神和仇神的缘故，人生的各方面都会产生不好的影响。学习运势不佳，没有好的学习方法，就没办法提高学习的效率，长期的低效率学习也会让自己心态变差。人际关系中，也容易朋友产生矛盾。建议你花点时间找到适合自己的学习方法，遇到问题可以向老师和同学求助，同时保持良好的心态，和朋友产生矛盾时，积极沟通解决。', '在这一个运势中，对于学习来说会有一些不一样的变化。在学习方式上能够改变之前的一些观念，找到一些比较合理的方法，懂得向他人寻求一些必要的帮助。在人际中，也懂得收敛脾性，不会随意和身边的人产生一些争执和矛盾。'],
                'qiu_qiu' => ['在该段大运中，你的整体运势不太理想。你可能会变得过于自信，适当的自信可以带来一些帮助，但是过于自信是自负的一种表现。学业上，你可能会有所松懈，学习成绩有着下滑的趋势。生活中，也因为自负而容易受到别人的质疑。建议你在这个阶段要虚心学习，维持平稳的心态，避免骄傲自大，这样才能更上一层楼。', '对于这一阶段的运势来说，比起上一个大运可能会有一些小的改变，其中在学习上会有一些上升的趋势，但想要获得对应的成功，还是需要不断的付出努力才行。而在心态上，这个大运中也会产生一些变化，不似之前那般容易动怒以及和他人结怨。'],
                'qiu_xian' => ['在这个阶段中，你的整体运势有着下降的趋势。学习运势较差，学习成绩有下滑的倾向，而自己在学习时更有消极的情绪出现，会产生放弃的想法。人际关系时好时坏，一些不经意的言语就可能会得罪别人，叛逆的心理也会导致和父母发生矛盾。建议在这段大运里，少说多做，多去了解被人的看法和建议，虚心接受别人的批评，这样才能进一步提升自我。', '在本段大运中，你的运势和之前的运势没有太大的差别，只是在学习上的收获比起之前会朝着好的方面发展。但仍需要付出更多的努力才能达到你的预期，而相对于之前的叛逆，这一个运势中会有所收敛，也让家中的长辈和学校的老师能够有所放心。'],
                'xian_xi' => ['这段大运中，你的整体运势平稳，人生各方面都不会有太大的起伏。学业上，所有的付出都能够得到相应的回报，即使在遇到问题，也会得到贵人的鼎力相助。人际关系也较为良好，不仅能认识一些不错的朋友，而且这些朋友也会为你带来一些好运。但生活中，容易和父母意见不合，自己一些决定和想法可能会受到父母的否定。', '这一个大运相对于面前一个来说还是会有着一些不同之处，但总体来说会偏向于一种平稳的状态。只是在学习上你可能会感觉到有一些吃力，人际交往上也往往容易达不到自己的意愿，甚至和身边的朋友产生一些误解和不必要的争执。需要注意收敛心绪，将更多的心思花在学习上。'],
                'xian_yong' => ['该段大运受到用神的影响，整体运势较佳，人生的各方面都产生了积极影响。学业上顺风顺水，你会感觉得心应手，比较容易就能取得理想的成绩。人际关系中，积极向上的你也会受到别人的欢迎。但要注意物极必反，过度的顺利会让你产生懈怠的心理。建议你保持谦虚的心态，避免自大。', '对于这一阶段的运势来说，比起之前的运势会有一些下滑的趋势。但却也没有太大的影响，身边的关系还是能够一如既往。在学习上也能够比较主动，无论面对中高考都能有一种平常的心态，在考试的时候往往也能够超常发挥，取得比较不错的成绩。'],
                'xian_ji' => ['在这段大运里，你的生活可能会遇到较大的起伏，甚至会面对一些影响未来的决定，这使得你的心态变得焦虑。在处理人际关系时也会变得粗心，无意间就会让别人不开心。另外，学习上也可能会受到别人的批评和质疑。建议在这段大运里，保持积极的心态，做事时更加细心谨慎，能够用宽广的心胸面对别人的评判。', '对于这一个阶段的运势来说，在起伏上会得到一些收敛，一些之前一直都焦虑的事情也能得到放下。而之前让你比较感到焦虑的事情也能在这个大运中得到化解，但在学习上依旧还是让你感觉到会比较糟心，甚至还能有种付出后没有得到自己预期的感觉。'],
                'xian_qiu' => ['在这段大运里，你的整体运势都有着下滑的趋势，在一些方面可能会略有波折和阻碍，甚至会让你感觉到迷茫。学业也止步不前，成绩总是不够理想，身边的人也可能会因为你的直言直语而远离你。好在父母总是会在背后支持着你，建议你在这段大运里，多向长辈寻求建议，为自己定一个合理的目标，并且努力去实现。', '对于这一个大运来说，相较于之前的运势会有一些上升的趋势。而且在学业上也懂得了需要自主学习，能够制定出一些计划。身边的朋友和家庭也不会有太大的波折和变化，但还是比较建议多和家中长辈以及身边的朋友多交流和沟通一下。'],
                'xian_xian' => ['该段大运总体来说较为平稳，人生的各方面都不会出现太大的波折。学习运势较为一般，努力学习的同时，也要注意休息，充足的睡眠才能够提高学习效率。人际交往中，可能会结识一些意气相投的朋友，但在和朋友相处的时候容易固执已见，尤其是意见产生分歧的时候无法虚心听取别人的建议，久而久之就会影响你们的关系。建议你保持一个谦虚的心态，坚持自我的同时，也要学习别人的优点。', '对于这一段大运来说，和前面的大运没有太大的区别。在学习上可能会有一些不一样，不像之前那般会感觉到吃力或者有种有心无力的感觉。在交往中，你会认识更多的朋友以及同学，他们在和你交往中能够给你或多或少的建议或帮。当然也会影响到你的心境，需要注意区分好坏，不可随波逐流，受到不良的影响。
'],
            ];
        } elseif ($age > 59) {
            $list = [
                'yong_xi' => ['该段大运受喜用神的影响，整体运势较为良好。财运收获颇丰，在投资理财时独到的眼光让你有无往不利的感觉，即便是这样也仍然要注意把控投资风险，避免空欢喜一场。健康运势较佳，但还是需要锻炼身体，增强自身的体质。家庭关系也比较和谐，彼此都能够互相理解，亲朋好友的来访，也会使得你心情舒畅。', '这个大运中，整体的运势和前面没有过大的区别。对于晚年的生活，还是容易比较和谐的，多数时候都能和子女相安无事。只是偶有感到孤独无助。毕竟子女有自己的家庭以及事业要忙碌，无法给你长久的陪伴。'],
                'yong_yong' => ['在这段大运里，你的整体运势都有着上升的趋势。感情上，与家人之间的相处都会比较愉快，虽会发生小争吵，但及时沟通都能够解决。这一段时间内财运亨通，有着许多赚钱的机会，在投资理财方面能够有着不错的收获，但也要选择合理的投资方式，切忌盲目投资。健康运势比较稳定，按时吃早餐，并且有规律的运动，才能够更好的提高自身的免疫力。', '本大运中的一些运势和前面的运势一般没有太大的搬动，无论是在感情上还是其他一些运势上似乎都比较平顺。只是在和子女的沟通上可能会有一些不足，因而会导致产生一些误会，不过最后还是能够合理解决。但更多的应该要关注下自己以及伴侣的健康，合理调节饮食，定期体检。'],
                'yong_ji' => ['在这个阶段里，你的整体运势较为一般。财运不太好，无论是理财投资还是生活开支都可能出现动荡，赚得多花得更多，收支无法平衡。在和人交往中，容易和别人发生分歧，同时子女的琐事也会让你身心疲惫，从而产生消极的心态。建议在这段大运里，保持良好的心态，避免进行不必要的投资，多和家人沟通交流，这样才能互相理解。', '对于这个大运之中，你的运势比起上一个运势多少都还是有些突破。只是对于理财来说，多少都还是应该要收一收比较好，需要注意支出和收入的平衡。对于家庭来说，需要注意一下和子女以及叛侣之间的沟通方式。不可因是长者而拿大，更不能对子女儿孙颐指气使。'],
                'yong_qiu' => ['在这段大运里，整体运势都较为平稳，人生不会产生较大的变化。生活开支会有所增加，要提前做好储蓄，以备不时之需。感情运趋于稳定，没有太大的起伏，生活在一个比较快乐的氛围当中，和家人之间可能会有所争吵，但彼此都能够相互理解。另外，要注意加强锻炼身体，提高自身的免疫力。', '这个阶段中，你的运势和上一个运势没有太大的区别。只是在和子女的交往中需要多注意一下沟通，不可以自己的一些经历或者见识而去强加给女子的身上。其余其他方面来说，更多应该是就是要注意一下自己以及老伴的一些健康运势，做好定期体检，合理安排生活的习惯。'],
                'yong_xian' => ['在该阶段中，各方面运势都比较平稳，健康、感情、钱财运都不会产生较大的变化。感情上，能和家人和睦的相处，子女也会虚心听取长辈的教诲。健康运势较为平稳，虽不会产生太大的起伏，但仍然需要加强锻炼，多吃水果蔬菜来提高免疫力。', '对于这一段运势来说，你的总体运势还是比较平稳的。所关注的地方往往也都是在自己的子女身上，或者对于你来说比较重要的可能是自己以及伴侣的身体健康。但也容易因女子工作繁忙缺少陪伴而会感到有些孤单，这方面需注意多多调节。'],
                'xi_xi' => ['在这个阶段中，整体运势都都较为不错。感情上，平辈之间的关系都相对和睦，家庭生活也是和和气气，虽然生活中还是少不了一些摩擦，但这也是生活的调剂品，只会让家人的感情越来越好。钱财上也不会欠缺，生活也不会为钱所困惑，只是需要多注意一下自身以及老伴的健康。', '在这十年中，对于你的运势来说还是会比较平稳的，而你更多的关注点会放回到家庭之中。重视的多是自己以及伴侣的健康，但在这方面最好谨小慎微一些，合理改善伙食，避开一些因年龄带来的疾病。在和子女的沟通上需要注意方式方法，不可倚老卖老，把道理和经历放在第一位。'],
                'xi_yong' => ['这个阶段由于喜用神的影响，人生的各方面都会比较顺利，家庭和睦、身体安康。虽然各方面都不会出现太大的问题，但依然要小心物极必反，过度的松懈可能会为你招来麻烦。你需要保持一个良好的心态，有规律有计划的做事，方可顺顺利利。', '本大运和前一个大运类似，都能受到喜用神的影响，对于各个方面来说也还是会比较顺利，特别是对于家庭来说，更是显得和睦平顺，自己和伴侣之前的身体也一如既往的健康，只是需要防范一些小病小痛，合理的安排作息和饮食规律即可。'],
                'xi_ji' => ['在这段大运里，你的整体运势比较低落。生活的琐事可能会导致你心情郁结，容易与身边的人发生争吵。财运也较为一般，各方面的花销可能会有所增大，要注意收支平衡。建议你在这段大运里保持一个良好的心态，多做一些自己喜欢的事情，丰富精神世界。', '在这段大运里，你的整体运势比较低落。生活的琐事可能会导致你心情郁结，容易与身边的人发生争吵。在金钱上可能会因为一些意外而产生比较大的花销，要注意收支平衡。健康上也需要注意一下，因保持定期去医院体检的好习惯。'],
                'xi_qiu' => ['在这段大运里，你的总体运势较为一般，有着缓慢下降的趋势。感情方面可能会因为你的固执而出现一些问题，或是与另一半的争吵，或是子女的叛逆，使得你心烦意乱。人际交往中，也可能会受到别人的非议，但你身边还是有不少重情重义的朋友支持你。建议在这段大运里放宽自己的心胸，认真倾听家人朋友的建议，与别人发生矛盾时及时沟通交流。', '这个阶段中比起上一个阶段会有所缓和，各个方面的运势也会趋近于平稳的状态。只是需要多关注一下自身以及老伴的健康，在财运上若是有理财，可暂定投入。没有理财，则需要做出合理的规划。在与儿孙的沟通上不可过于强迫，记得多聆听聆听。'],
                'xi_xian' => ['在这个阶段中，你的整体运势都较为平稳，人生的各方面都平稳有序的进行着。在金钱上需多多注意，可能会因自己一时失算而导致入不敷出。感情运较为平稳，与家人朋友都能够和谐相处，但要避免口舌之争。健康运势一般，虽不会出现大问题，但仍然需要加强锻炼来提高自身的体质。', '这个十年之中，你的运势可能会有一些变化。在金钱上虽是达到了不缺的程度，但有时候难免会有一种局促和危机之感，甚至是抓襟见肘。至于健康方面，这几年中需多多注意，不可乱了规律的作息以及饮食。和子女的接触也需注意方式，免得产生一些隔阂。'],
                'ji_xi' => ['本大运受忌神的影响，整体运势较为一般，人生的各方面都好坏参半。感情上，容易与亲人发生矛盾，但大都能及时解决。财运平平，不过好歹也是不缺。健康运势一般，你的身体状态还不错，但还是要规律饮食，继续加强锻炼来保证健康的体质。', '在这个大运中，运势相较于前一个大运会有一些上升。但人到这个阶段，许多精力几乎都打了折扣。钱财上没了之前那种冲动劲，喜欢细水长流。注意点更多的是关心子女的成长，自己以及老伴的身体健康。'],
                'ji_yong' => ['在这段大运里，你的整体运势较好。感情上，和家人之间关系和睦，能够互相理解，也能够及时的沟通，所以很多问题在演变为较大的矛盾时都能及时的解决。钱财上因年轻时存下的积蓄，倒也没有过多的焦虑。只是这段时期的健康状况为佳，但是平常生活中也要多注意和水有关的东西，可能会有被水烫伤的情况出现。', '整个大运看来，你的各种运势都处于一种比较平衡的状态。只是可能会多为儿女的事情操心，但也不必过于担忧，毕竟儿孙自有儿孙福。而在和他们的沟通上也需要注意方法，更多的精力应该在自己和另一半的健康运势上，有条件的话，可出门旅行散心。'],
                'ji_ji' => ['这个阶段里，你的整体运势都较差，人生各方面的起伏可能会较大。生活上可能会发生一些脱离你掌控的事情，使你的心态变得消极。你的一些决定也得不到家人朋友的支持，使你感觉到迷茫。建议在这段大运里保持乐观的心态，让自己沉淀下来，并且有计划的去做事，这样才能平稳有序的前进。', '对于这个大运来说，你的运势可能会有些低落。特别是在家庭的关系上会显得比较复杂，也许是子女无法理解你的过度关心，也许是你的关心方式有些欠考虑，无法理解年轻人会有一些比较新奇的想法。而对于老伴的提点，也容易置若罔闻，甚至被家中之人孤立一边。'],
                'ji_qiu' => ['本大运受忌神和仇神的影响，整体运势有着下降的趋势。你可能会变得消极。生活中容易与别人意见不合而产生矛盾，好在你的家人还是会一如既往的支持你。建议在这段大运里，多做一些自己喜欢的，有意义的事情。不但要保持良好的心态，而且要及时解决问题，切忌拖延。', '在这个大运中，你的运势可能会有一些起伏。特别是在和子女的以及亲近之人的关系上会显得有些僵局出现，许多时候是因为女子无法理解你的良苦用心。但更多时候可能是你和他们沟通的方式有些过于着急而伤害到对方，但需要记住，越亲近的人，越渴望被温暖以待。'],
                'ji_xian' => ['在这个阶段里，你的总体运势较为一般。生活中可能会遇到一些棘手的问题，靠自己的力量难以解决。财运不佳，盲目的投资会有破财的风险。健康运势不佳，如果不注意防范恐会有一些疾病出现。建议在这段大运里，遇到问题时可以向家人朋友求助，虚心听取别人的建议能让你更上一层楼。除此之外，也要合理饮食，加强锻炼，增强自身的体质。', '这个阶段中，你的运势没有太大的波折。但身边却也面临着一些变化，甚至会让你感到有些不乐观，以至于会有迷茫的情绪。而产生这些情绪无非就是发现身边的好友突然离世，少了年轻时的斗志和精力，以及子女长时间忙于自己的工作不陪伴自己。'],
                'qiu_xi' => ['在这段大运里，你的整体运势较为一般，人生各方面都会相对平静，但依然要小心平稳生活中暗藏着变动。金钱上不会局促，甚至因年轻时的积蓄而显得有富余。感情运势一般，家人之间虽然和睦相处，但仍会出现子女叛逆的情况。健康运势一般，虽不会产生大问题，但可能会因为睡眠不足而出现身心疲惫的状态。', '在这个运势里面，你会发现有较大的波动。虽然看似平静，但其中还是会有不少的不顺出现。在健康上，这个大运中需得多多注意，合理调节饮食作息规律。在和好友交往上，可多联系，但切忌论长论短。在和家人的交往中，更是需要注意方式方法，特别是对女子的事情，最好莫要插手过多。'],
                'qiu_yong' => ['在这个阶段里，你的整体运势都相对不错，人生的各方面都会比较顺利。不会为钱财焦虑。感情上，与家人朋友之间的相处也会相对和谐，虽会发生小矛盾，但都能很快化解。生活节奏会逐渐放缓，但是要小心以往辛苦操劳给身体健康埋下的隐患，最好定期做体检，日常不要因懒惰而懈怠运动。', '对于整个大运来说，总体的运势是在平稳中前进的。而且在家庭中，你的子女在你多年的悉心教导下也能在工作上独当一面，无论是财运还是其他一些运势方面总能稳步前进，让你不至于有过多的操心和担忧。只是在健康运上需要多注意一二，不可有不良习惯而拖垮身体。'],
                'qiu_ji' => ['在这段大运里，由于忌仇神的缘故，在人生各方面都会略有波折。钱财上有着一些变数，生活的开销相对来说会增大，如果没有计划就去投资，可能会有破财的风险。感情运势不佳，尤其是在同辈人之间的关系相对比较微妙，很可能因为某些小事而出现以及不合的情况，进而出现争端。建议在这段大运里保持乐观心态，有计划的去做事，和别人发生矛盾时能够主动的沟通解决。', '这一步大运中，主要的一些问题还是在你的心态上。而这些往往是一些外物造成。其中支出的增加容易让你入不敷出，却又没了年轻时的斗志。在和儿女的沟通中，他们往往也难以理解你的良苦用心。在健康上，这一个大运中可能会有一些波折，需多注意保养。'],
                'qiu_qiu' => ['在这段大运里，由于仇神的缘故，你的整体运势都不太理想。人生可能会有一些起伏，日常的生活也可能会变得枯燥乏味。钱财方面，需多注意防范，免得被一些有心之人骗取。健康运势也较为一般，可能会因为饮食不当而导致肠胃不适，建议多吃蔬菜和水果，养成规律的饮食习惯。', '整个大运中，运势并无明显的波动。但可能会出现一些不太理想事情，算是喜中有忧。在金钱的方面，你得多注意防范一二。不可轻易听信他人的投资，对一些不是很相熟之人不可随意听信所谓的投资理财之事。而在和儿女的交往中，亦是不可矫枉过正，因给他们留出足够的发展空间。'],
                'qiu_xian' => ['在这个阶段里，你的整体运势一般，各方面都不会出现太大的波折。生活上，你可能会变得缺乏激情，没有目标，从而心情郁闷。财运也较为一般，需要注意一些没有必要支出。建议在这段大运里，可以和家人一起出去旅行，或进行些娱乐活动，调节你的心态，让生活更有动力。', '对于这个运势来说，你的运势呈现出一种平稳的状态。对于哥哥方来说，似乎也没有太大的变动。但可能多数时间都会陷入到一种情绪低落之中，这些原因可能是因为和女子之间产生了一些隔阂，觉得他们不再对你言听计从。这个大运中，建议以调节心情为主。可适当去一些公园和同龄人交流娱乐，或者出去旅游散心。'],
                'xian_xi' => ['在这段大运里，你的整体运势比较平稳，且有着上升的趋势。感情运势不错，家庭关系比较和谐，遇到的问题都能够及时的沟通，而彼此都能够互相理解。在钱财上，需谨防被一些不法之徒欺骗。这段时期的身体状况较好，但是在进行剧烈运动，或者是外出旅游的时候，也要注意有摔伤的可能。', '对于这个大运来说，你的运势会属于比较平常。在家庭之中，你也一直都会必现有话语权，伴侣在大小事情上也容易听取你的意见。女子方面，他们能够有自己的规划，让你不至于很操心。但美中不足的是在健康方面，你需得多多注意和保养，不可让作息时间产生紊乱。'],
                'xian_yong' => ['本大运受用神的影响，你各方面的运势都较佳。感情上，家人之间都能够和睦相处，子女也比较孝顺，虽会出现意见不合的情况，但通过沟通都能及时解决。建议在这段大运里，顺风顺水的同时也不要得意忘形，要谦虚的面对生活和未来才能越来越顺。', '对于这一阶段的运势来说，你的各个方面都还是比较可以的，甚至有种上升的趋势。钱财上不仅仅有自己的积蓄，子女也会定期给你寄钱，逢年过节也会有礼物，晚年生活能够得到他们的赡养。但需要注意到是身体健康，应该保持良好心态，做好定期体检。'],
                'xian_ji' => ['在这个阶段里，你的整体运势较为低落。生活中可能会经历一些不如意的事情，或可能出现一些意料之外的事情打乱你的计划，但不要过度担心，你的家人和朋友都会给予你帮助。财运方面，容易入不敷出，甚至会被他人所骗。建议在这段时期内，保持一个积极的心态，多关注下自己的健康。', '对于这个大运来说，你会发现身边似乎出现了不少的变化。随着步入晚年的退休生活，你容易发现生活中会有一些恶变化。特别是老友的离去，子女因为忙碌的疏远而让你觉得许多事情都有心无力起来。建议在这个大运中，有条件可多出去散心，不宜过多参合子女的事情。'],
                'xian_qiu' => ['在这个阶段里，你的整体运势较为一般，而且有着下降趋势。这段时期你和别人相处的过程中可能会出现不少的矛盾，不管是亲戚朋友，还是陌生人，都会有争吵的可能。健康运势也欠佳，生活中的琐事会让你应接不暇，加深你的身心负担，从而导致心情烦躁郁结。建议你在这段大运里少说多做，虚心听取别人的建议，同时要注意劳逸结合，保持身心健康。', '这个大运之中，你容易有一些烦恼的事情。而心态上亦是会有一些变化。其中对于钱财上来说，虽是已没有年轻之时那种挣钱的激情，但对于这方面还是会比较容易看重，需小心不可轻信他人所谓的理财。而和子女的沟通中，不可矫枉过正。'],
                'xian_xian' => ['在这段大运里，你的整体运势都较为平稳，人生各方面都能在稳定中慢慢前进。在钱财方面，你不会因为突然的开销而产生抓襟见肘的窘迫，毕竟年轻之时的积蓄让你不用为钱发愁。生活相对来说也比较顺利，没有较大的起伏，但是随着年纪渐长，要注意自己和配偶的身体，可以适当加强锻炼，增强体质。', '对于这个大运来说，你需要注意的是自己以及伴侣的健康方面，需要对生活饮食上做出合理的调整。但对于心态上，你可能会感到有些失落的感觉，因为女子可能会因为忙碌自己的事情而不在你的身边陪伴你，让你多少都有些感到失落和孤单。'],
            ];
        } else {
            // 19-59
            $list = [
                'yong_xi' => ['在这段大运里，你的人生会比较顺利，各方面都会得到好运的相助。人际关系会变得良好，容易结识新的朋友，遇到困难时也会得到朋友的相助。事业上会有一些的上升机会，需要你好好把握，不可持宠而娇。除此之外，感情也会受到好运的影响，变得平稳顺利。', '本大运和前一个大运的运势相差无几，并无太大的波澜起伏。对于一些人际关系亦是如此，能结识到帮助你的贵人。事业上也有着蒸蒸日上的发展，无论是感情还是健康在这个阶段中都无太大的波动，趋于一种平稳。'],
                'yong_yong' => ['该阶段中，大运用神到位，各个方面在受到大运的影响后都显示出积极的影响。在性格上你会变得自信，对许多事情都能向往美好，也能解决各种烦恼。无论是在事业上，还是感情上，都是欣欣向荣。但同时也得注意盛极必衰，切莫去做一些投机取巧之事，免得适得其反。', '本大运和前一个大运几乎相差无几，对于各方面的运势也算是有着更为积极向上的态度。无论是在人际关系还是在其他方面都能顺心顺意，但需要注意月满则亏，不可因此而放松警惕。因乘势而上，正所谓好风凭借力，送我上青云。'],
                'yong_ji' => ['这步大运中因忌神的缘故，在一些方面上可能会略有一些波折和阻碍，甚至有时候会让你感到陷入一种困顿当中。而在心绪上也可能会有一些迷茫，身边的人对你可能会有一些似是而非的议论，甚至和亲密无间的人也会略有一些隔阂。建议这段时间里，在做事上更加谨慎细心一些，保持良好的心态。', '行到这个大运，各个方面应该都会有所改善，但依然不能放弃向上攀升的念头。对于一些人际关系上或许会有被孤立甚至是和他人争吵的征兆，工作和感情上也会有添堵的迹象。建议在做事上更加谨慎一些，保持脚踏实地，厚积薄发。'],
                'yong_qiu' => ['在这段大运里，总体来说比较平稳，人生的各方面都是喜忧参半。事业上的机遇很多，但遇到的难题也会不少。感情生活也起伏不定，可能会因为一些小事而摩擦不断。不过事在人为，建议在这段大运里，努力的去提升自己，将所有的难题都当作自己的前进动力。', '这个大运和前面的大运相差无几，总体来说还是会比较平顺的。只是在工作上可能会有不平衡的时候，毕竟投入得多，收入却是达不到预期。身边的小摩小擦也不会停，所面对的难题也没有减少。建议注意把握机会，多学专业知识，为今后厚积薄发做准备。'],
                'yong_xian' => ['该阶段受用神的影响，整体运势良好。你的交际圈可能会有所扩大，认识一些志同道合的朋友，而且这些朋友也能为你带来一些好运。事业运势也较为不错，但是并不代表着是一帆风顺，可能也会经历一些坎坷，不过大多能化险为夷。感情上容易出现一些小摩擦，但那也只是生活的调剂品，只要用心包容、理解对方，就能获得幸福美满的结局。', '这个阶段受到上一个阶段的影响，整体的运势并无太大的变动。在交际方面依旧能和一些志同道合的伙伴相敬如宾，而且他们能在你迷茫的时候带给你一些好运以及转机。在事业和学习中可能会遇到一些阻碍，不过大多数也都能平安度过。感情上另一半和你会争争吵吵，但原因无非是你最近有些忽略对方。'],
                'xi_xi' => ['本大运中，你的各个方面都有朝着良好方便发展的趋势，因为大运有喜神相助，因而在运势各个方面上都有着向上延伸的趋势。但部分流年中还是会有着一些下滑的景象，建议多多观察形势。无论是在感情方面还是在事业方面，都需要有谨小慎微的态度，只要明确方向，便能有所收获。', '在这个阶段中，运势方面比起前面的运势会更加出色，因为有喜神相助的缘故，在气运上会所有改变。对于心态上来说，做事能够更加果断，对局势的判断也能更加准确。无论是亲情还是爱情上，这个阶段都没有太大的波动，比起之前会更加亲密一些。'],
                'xi_yong' => ['该阶段由于喜神和用神的影响，你的运势有着上升的趋势。感情顺风顺水，事业蒸蒸日上，凡是你用心去经营的，都会比较容易获得可观的回报。财运较佳，求小利大多数都能水到渠成，而且在朋友或者同事的帮助下，更容易促进合作的达成。只是凡事进展平缓，一切以稳步发展为主，切勿急躁。', '对于本大运来说，你的一切都趋近于一种平衡了，因为有喜用神的相助，并没有太大的波折和不顺降临在你的身上。但还是需要注意一下某些年份的可能会有一些突出的问题出现，在那些不顺的年份中，最好能够谨小慎微一些，不能因过于焦躁而导致出现一些错误，让问题变大化。'],
                'xi_ji' => ['在这个阶段里，你的整体运势比较低落，可能会在人生的各个方面产生一些不利的影响。事业起伏较大，谋利多有不顺，做事有可能多败少成。你的心绪也会因此受到影响，导致感情生活出现磕绊。建议你尽量恪守本业，少做激进盲目的尝试，可保证期间运势多些平稳。如果遇上难以避免的抉择，需要慎重选择实行的时机。', '在本大运中，也许你的运势会有所改变，但依旧是吉凶参半，对于各个方面来说可能都会有一些不利于的情况出现。但好在这些不利情况会在适时出现转机。因而在这个大运中，相对于想要在工作或事业上有所成就，更多的应该是多学习工作上的技能，这样才能厚积薄发。'],
                'xi_qiu' => ['在这段大运里，你的整体运势有所下降，人生的各个方面都好坏参半。生活中可能会不经意就得罪到别人，你需要多听少说，尽量避免和别人发生争执。健康运势不太理想，一些恼人的琐事可能会使你心态变差，从而影响身心健康。建议你在这段大运里，保持心情舒畅，注意身体健康，不要给自己太大的压力，避免抑郁烦躁。', '在该段大运里，你的运势会有所缓和，生活会比较稳定。工作上可能会遇到一些转机，但要认清自己的实力，尽力而为，避免出错。交际中容易和别人发生一些矛盾，你需要放宽心胸，倾听别人的想法。另外，感情生活平稳，和另一半能够和谐相处。'],
                'xi_xian' => ['在这段大运里，你的整体运势有所上升。受到喜神的影响，你的心态会变得积极起来，遇到问题大多能够迎刃而解。事业上可能会遇到很多机遇，但是要注意能否把握得当，以免得不偿失。财运也不错，但钱来的快，用的也快，要注意收支平衡，合理地规划好每一分钱的投入产出。此外，要注意避免过度劳累，加强身体锻炼，提高自身抵抗力。', '该段大运和前段大运较为相似，各方面都呈现良好的状态。你的心态会变得更为积极，做事也比较得心应手，但需要合理的规划，这样才避免出错。感情生活也十分顺利，彼此之间能够相互包容和理解。'],
                'ji_xi' => ['在这个阶段里，你的各方面都较为平稳有序。人生可能会风平浪静，但是依然要小心藏在平静下的风暴。工作如果缺乏明确的目标，就会变得难以前行，你需要摆正自己的态度，找到合适的目标来努力。生活中可能会和别人发生一些小摩擦，如果用心倾听，并且处理得当，就不会造成太大的影响。', '这段大运和前段大运相差无几，各方面运势平稳，事业、感情、财运都较为稳定。所以这个时候不能好高骛远，需要脚踏实地的做事，积累经验。这样才能稳定有序的前进，遇到机遇时才能够更好的把握。'],
                'ji_yong' => ['在这段大运中，你的人生可能会遇到一些转机，但是需要注意是否能够把握好，并且让事态朝着你想要进取的方向前进。此外，你做事时也会充满精力，会努力将想法落实到行动上，但要避免过度自信。感情状况也总体平稳，面对一些矛盾时，总有一方能够先做出让步。', '在这段大运里，你的感情运和事业运都朝着好的方向发展。交际上，你可能会遇到一些志同道合的朋友，这些朋友会为你带来一些好运。工作中也会遇到不少上升的机会，需要你好好把握，但切忌过度自信，以免错失机会。'],
                'ji_ji' => ['这段大运中可能会让你觉得有一些迷茫，人生也会面临一些选择和变化，对事态的把控可能也无法达到自己的预期，各个方面也会受到一定的阻碍，财运上也多有波折。因此这个阶段要少在大事上做出决定，最好能够先沉淀自己，安定下稳定的情绪。多外出散心，学习知识，博积薄发，为以后打下基础。', '这段大运中，你的运势会有所缓和。面对人生的选择和变化，渐渐地寻找到了方向。对待失控的事态，也能会找到把握的机会，但生活中还是会有不会少的阻碍。你需要静下心来，脚踏实地的学习，提升自己的能力，为以后的发展做好充足的准备。'],
                'ji_qiu' => ['在这个阶段里，你的人生可能会较为曲折。在大运的影响下你可能会产生一些焦虑的情绪，在面对人生中的关键决定时，可能没有经过很慎重的考虑，就轻易做了决定，对未来的发展也产生了至关重要的影响。建议在这段大运里，脚踏实地，保持身心健康，规避忌神的不利影响，等待好运的到来。', '该段大运和前段大运较为相似，整体运势不太理想。一些焦虑的情绪导致自己的目标难以实现。事业和感情上，付出会大于收获，对未来会较为迷茫。这个阶段的道路充满曲折，你需要更加努力，才能平稳度过。'],
                'ji_xian' => ['在这段大运中，由于忌神的影响，你可能会遇到一些棘手的问题，这些问题甚至会给你的日常生活带来一些不利影响。感情上也可能会不顺心，容易磕磕碰碰。事业运势一般，所以不太适合在工作上面有太大的举动。', '在这段大运中，你的运势呈现上升的趋势。事业和感情都渐渐趋于平稳，可能会出现一些机遇，但都难以把握。财运也较为一般，这个时候不适合去做一些冒险的投资。你需要保持良好的心态，脚踏实地的做事，主动去解决问题。'],
                'qiu_xi' => ['这个阶段的运势总体来说比较平稳，人生不会出现太大的波折。感情上可能会出现一些小问题，但都在你的掌控之内。财运较为不错，可发些小财，但小财虽多却难以聚集成大财，这时除了保持冷静的头脑之外，也要维持平衡的心态，在进行商务投资时需要谨慎处理，如果放松警惕盲目进行投资，则会阻碍连连。', '该段大运和前一个大运相差无几，整体运势平稳，并且缓慢上升。事业上你需要主动去把握机会，才能够得到上级的重视。交际中，你可能会遇到几个不错的朋友，并且能为你带来一些好运。感情生活比较平淡，偶尔会发生矛盾，但都在掌控之中。'],
                'qiu_yong' => ['在这段大运里，你的感情和事业都会朝着好的方向发展。工作中你可能会遇到不少机会，如果能够把握时机，就可以获得一些进步。生活上，朋友和同事的关心支持会让你信心倍增，和异性的感情也可能会有不错的发展。但是做事要有计划，切忌拖延。', '在这个阶段中，由于用神的影响，人生的各方面都会朝着好的方向发展。你会变的更为自信，生活中和工作上，都能够得到同事和朋友的支持以及帮助。但要注意避免过于自信，得意忘形会让你遇到一些不必要的麻烦。'],
                'qiu_ji' => ['这个阶段由于仇神和忌神的缘故，人生可能会产生较大的起伏。事业运和财运都充满较大的变数，不利于自身的发展。遇事阻力也会增多，甚至有些问题难以解决，感情上也会出现一些波折。因此会产生一些低落的情绪，不利于各方面的发展。建议多做学习，等待转机过来。', '本大运和前一个大运较为相似，整体运势较差。你会觉得人生有些迷茫，没有目标和方向。而且生活中也会面临一些棘手的问题，难以把控事态的发展。好在你的亲人朋友都能够为你带来支持和帮助。建议你努力奋斗的同时，也要注意休息，保持身心健康，合理的规划未来。'],
                'qiu_qiu' => ['这个阶段由于仇神的缘故，你的整体运势不太理想。自己的一些想法和目标可能会难以实现，在事业上付出得多，收入得少，总是需要付出更多的努力才能获得合理的回报。而在感情生活中也容易和亲近的人发生矛盾，这个阶段的道路可能不太平坦，你需要加倍努力才能让自己平稳前进。', '在该段大运中，你的整体运势较为一般。事业运过于平稳，虽有上升的机会，但不好把握，难以突破上升的瓶颈。感情运也较差，容易与另一半意见不合，产生分歧。建议你多倾听别人的建议，虚心学习，少做盲目的尝试，即可平稳的前进。'],
                'qiu_xian' => ['在这个阶段中，各方面运势平稳，事业、感情、财运都不会有较大的变化。生活处于平稳的状态，所以这个时候不适合去做一些冒险的事情，或者是制定冒险的计划。最好合理的规划好某些事情，谨慎前行，这样才能稳定的向前发展。此外，无论做什么都要注意劳逸结合，保持良好身心健康才能够事半功倍。', '该段大运和前一个大运相差无几，整体运势平稳，人生各方面都有好有坏。事业运较好，容易出现一些上升机会。财运较差，不适合进行冒险的投资，以免得不偿失。感情运势较为平淡，偶尔会产生摩擦，但无大碍。建议你在平稳的状态下，多学习一些只是，丰富自己的人生。'],
                'xian_xi' => ['在这段大运里，你的整体运势平稳，人生各方面都不会有较大的起伏。在生活中可能会出现一些小问题，但都可以轻易解决。事业的运势较为一般，但就算没有贵人的帮助，没有其他人的支持，只要认真努力的去做事情，还是能够获得其他人的认可。遇到机遇时，你需要主动去把握，迎难而上才能追求到名利和财富。', '在该阶段中，受到喜神的影响，你的运势呈现上升的趋势。人生的各方面相对来说都会比较顺利，不会遇到太大的起伏。事业上可能会遇到一些贵人的帮助，能够更上一层楼。感情生活也比较顺利，能够得到亲人朋友的支持。但做事需要脚踏实地，张弛有度，这样才能顺风顺水。'],
                'xian_yong' => ['这个阶段的运势有着上升的趋势，各方面也会受到好运的影响。事业发展顺利，会出现很多机遇，而且遇到问题时也会有贵人相助。人际关系上也许会发生一些小矛盾，但很快就能化解。感情生活也十分顺利，彼此之间能够相互包容和理解。你的心态会因此而变好，只要脚踏实地的做事，就会有不错的收获。', '在这段大运里，受到用神的影响，整体运势都较为不错。事业和感情都比较顺利，遇到问题时，也能够轻松的解决。财运也较为不错，主要是正财方面，会稳定的获得收入，但凡是你用心去经营的，都会比较容易获得可观的收益。'],
                'xian_ji' => ['在这段大运里，你的整体运势有着下滑的趋势，人生的各方面都可能会受到一些不利影响。事业和感情上，你都会感觉比较迷茫无措，或者不知道未来的方向应该往何处走，甚至会遇到一些难以解决的问题。建议在这个阶段，做事要更加用心谨慎，保持良好的心态，不可急于求成，最好保持学习，厚积薄发。', '该段大运受到忌神的影响，整体运势较差。交际中，可能容易和别人发生争吵，你的心态也会受到影响，变得消极。事业也止步不前，迷失了方向。但感情运势较佳，会得到另一半的鼎力相助。建议在这段大运里，保持积极的心态，向身边优秀的人学习，提升自己的能力。'],
                'xian_qiu' => ['在这段大运里，你的整体运势较为一般。事业上，可能难以把握发展的机会，遇到的困难相对来说也会比较多。感情生活上，较为不顺，容易与身边的人发生争执，因此，你的情绪容易低落消沉、失去前进的动力，这就可能会使你走在一个相反的道路上。建议在这个阶段，遇到难题时不慌不忙，不急不躁，保持良好的心态，主动去解决难题。', '该段大运和上一段大运较为相似，但呈现着上升的趋势。事业上，会得到贵人的支持和帮助，但依然会有一些棘手的问题。感情运较为平淡，不会产生大风大浪。健康运较为一般，要劳逸结合，注意身心健康。建议在这个阶段，少做冒险的尝试，做好自己的本职工作，这样即可平稳有序的前进。'],
                'xian_xian' => ['在这段大运里，你的人生各方面都会比较平稳，并且在平稳中慢慢前进。你的事业可能需要自己主动去把握机会才能上升，同时情感生活也十分平淡，需要自己用心经营。在这段大运里，你需要为自己制定一些规划，才能更有干劲和冲劲。', '这个阶段总体来说较为平稳，人生不会遇到较大的起伏。事业中上升的机会较少，需要自己脚踏实地的积累经验，等待机会的到来。交际运不错，可能会结识一些志同道合的朋友。感情生活也较为良好，只要用心经营，就能够得到回报。'],
            ];
        }
        /**
         * 根据年龄所在区间获得序号
         * @param int $age
         * @return int
         */
        $getYunshiAgeIndex = function (int $age) {
            if ($age < 19) {
                return 1;
            } elseif ($age > 59) {
                return 3;
            }
            return 2;
        };
        $index1 = $getYunshiAgeIndex($age);
        $index2 = $getYunshiAgeIndex($age1);
        $arr = $list[$key] ?? $list['yong_xi'];
        return ($fen == $fen1 && $index1 == $index2) ? $arr[1] : $arr[0];
    }
}
