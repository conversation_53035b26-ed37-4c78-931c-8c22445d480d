<?php
// +----------------------------------------------------------------------
// | wxqs.五行缺什么
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use api\ApiResult;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\NineStar;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use think\facade\Request;

class Wxqs
{
    /**
     * 用户
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 创建时间
     * @var Ex
     */
    protected Ex $lunarO;

    /**
     * 纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * 喜用忌闲仇
     * @var array
     */
    protected array $xy = [
        'xy' => [],
        'shen' => [],
    ];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @return ApiResult
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public function index()
    {
        $data = [
            // 用户生日
            'time' => Request::param('time', '', 'trim'),
            // 性别
            'sex' => Request::param('sex', 0, 'intval'),
            // 测算结果
            'otime' => Request::param('otime', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        $oYear = (int)date('Y', strtotime($data['otime']));
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->lunarO = Ex::date($data['otime'])->sex($data['sex']);
        $this->orginData = $data;
        $this->shaShen = new ShaShen();
        // 五行分析
        $fenXi = $this->lunar->getWxFenxi();
        // 喜用忌闲仇
        $xy = [
            'xy' => $fenXi['xy'],
            'shen' => $fenXi['shen'],
        ];
        $this->xy = $xy;
        $base = $this->lunar->getLunarByBetween();
        // 生肖属性
        $base['shengxiao_sx'] = $this->getShengxiaoSx();
        $jiNian = $base['jinian'];
        // 大运
        $dayun = $this->lunar->getDayun(false, 8);
        $dayun = $this->getDayunHandle($dayun);
        // 交运
        $jiaoYun = $this->getJiaoYun($dayun['dayun']['age']);
        $wxFx = $this->getWxxBi();
        $wxZb = $wxFx['wx'];
        $wxZb1 = array_column($wxZb, 1, 0);
        asort($wxZb1);
        $minWx = current(array_keys($wxZb1));
        // 神煞
        $shenShaArr = $this->shaShen->detail($jiNian, $this->lunar->sex);
        $result = [
            'base' => $base,
            'bz_title' => $data['sex'] ? '坤' : '乾',
            // 天干十神
            'god' => $this->lunar->getGod(),
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 纪年阴阳
            'jnyy' => $this->getJnYy($jiNian),
            // 日元
            'day_attr' => $this->lunar->getDayAttr(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 空亡
            'kongwang' => [
                'y' => str_replace(',', '', Huangli::getKongWangbyGz(implode('', $jiNian['y']))),
                'm' => str_replace(',', '', Huangli::getKongWangbyGz(implode('', $jiNian['m']))),
                'd' => str_replace(',', '', Huangli::getKongWangbyGz(implode('', $jiNian['d']))),
                'h' => str_replace(',', '', Huangli::getKongWangbyGz(implode('', $jiNian['h']))),
            ],
            // 节气
            'jieqi' => $this->lunar->getJieQi(),
            // 星宿
            'xingsu' => $this->getXingSu($base['_nongli']['m'], $base['_nongli']['d']),
            // 生态和调侯
            'ecology' => $this->getEcology(),
            // 旺相休囚死
            'wxxqs' => $this->lunar->getWxxqs(),
            // 流年数据
            'liuyear' => $this->getliuYear(),
            // 大运
            'dayun' => [
                // 完整大运
                'wz' => $dayun['dayun'],
                // 当前大运序号
                'num' => $dayun['curr'],
                // 当前大运详情
                'info' => $dayun['info'],
            ],
            // 交运
            'jiaoyun' => $jiaoYun,
            // 测算年数据
            'liu' => [
                'y' => $oYear,
                'gz' => implode('', BaziExt::getGanZhi($oYear)),
            ],
            // 五行分析
            'wx_fx' => $wxFx,
            // 最小五行名
            'min_wx' => $minWx,
            // 性格
            'xingge' => $this->getxingGe($wxZb1),
            // 未来
            'future' => $this->getFuture($minWx),
            // 五行缺结果
            'que' => $this->getWxQueRes($wxZb1),
            // 九宫飞星
            'jiuxing' => $this->getJxfp(),
            // 四柱自坐
            'zizuo' => $this->getZiZuo(),
            // 神煞
            'shensha' => [
                // 四柱神煞
                'sz' => $shenShaArr,
            ],
        ];
        return ApiResult::success($result);
    }

    /**
     * 四柱自坐
     * @return array
     */
    protected function getZiZuo()
    {
        $terrainData = Ex::getTerrainData();
        $jiNian = $this->lunar->getLunarTganDzhi();
        return [
            'y' => $terrainData[implode('', $jiNian['y'])],
            'm' => $terrainData[implode('', $jiNian['m'])],
            'd' => $terrainData[implode('', $jiNian['d'])],
            'h' => $terrainData[implode('', $jiNian['h'])],
        ];
    }

    /**
     * 获取生肖属性
     * @return string
     */
    protected function getShengxiaoSx(): string
    {
        $list = [
            '甲子' => '屋上之鼠',
            '乙丑' => '海内之牛',
            '丙寅' => '山林之虎',
            '丁卯' => '望月之兔',
            '戊辰' => '温情之龙',
            '己巳' => '福气之蛇',
            '庚午' => '堂内之马',
            '辛未' => '得禄之羊',
            '壬申' => '清秀之猴',
            '癸酉' => '栖宿之鸡',
            '甲戌' => '安身之狗',
            '乙亥' => '过往之猪',
            '丙子' => '田内之鼠',
            '丁丑' => '湖内之牛',
            '戊寅' => '过山之虎',
            '己卯' => '山林之兔',
            '庚辰' => '恕性质龙',
            '辛巳' => '冬藏之蛇',
            '壬午' => '军中之马',
            '癸未' => '群内之羊',
            '甲申' => '过树之猴',
            '乙酉' => '唱午之鸡',
            '丙戌' => '自眠之狗',
            '丁亥' => '过山之猪',
            '戊子' => '仓内之鼠',
            '己丑' => '栏内之牛',
            '庚寅' => '出山之虎',
            '辛卯' => '蟾窟之兔',
            '壬辰' => '行西之龙',
            '癸巳' => '草中之蛇',
            '甲午' => '云中之马',
            '乙未' => '敬重之羊',
            '丙申' => '山上之猴',
            '丁酉' => '独立之鸡',
            '戊戌' => '进山之狗',
            '己亥' => '道院之猪',
            '庚子' => '梁上之鼠',
            '辛丑' => '路途之牛',
            '壬寅' => '过林之虎',
            '癸卯' => '过林之兔',
            '甲辰' => '伏谭之龙',
            '乙巳' => '出穴之蛇',
            '丙午' => '行路之马',
            '丁未' => '失群之羊',
            '戊申' => '独立之猴',
            '己酉' => '报晓之鸡',
            '庚戌' => '寺观之狗',
            '辛亥' => '树林之猪',
            '壬子' => '山上之鼠',
            '癸丑' => '圈内之牛',
            '甲寅' => '立定之虎',
            '乙卯' => '得道之兔',
            '丙辰' => '天上之龙',
            '丁巳' => '塘内之蛇',
            '戊午' => '厩内之马',
            '己未' => '草野之羊',
            '庚申' => '食果之猴',
            '辛酉' => '笼藏之鸡',
            '壬戌' => '顾家之狗',
            '癸亥' => '林下之猪',
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $yStr = $jiNian['y'][0] . $jiNian['y'][1];
        return $list[$yStr] ?? '';
    }

    /**
     * 九宫飞星
     * @return array
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    protected function getJxfp()
    {
        // 订单年
        $year = (int)$this->lunarO->dateTime->format('Y');
        $nineStar = new NineStar($this->orginData['otime']);
        $fp = [];
        // 风水布局改命法
        $fsbj = [
            // 助旺财富运
            'cai' => [],
            // 助旺事业运
            'shiye' => [],
            // 助旺感情运
            'ganqing' => [],
            // 助旺健康运
            'jiankang' => [],
            // 助旺人际关系
            'renji' => [],
        ];
        foreach ($nineStar->getPiaipan() as $item) {
            unset($item['m'], $item['d'], $item['h']);
            $fp[] = $item;
            // 助旺财富运
            if ($item['y']['fswz'] == '正财位') {
                $fsbj['cai'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，{$item['y']['title']}{$item['y']['xuankong']}星所到，就是本年最旺的方位，也是本年流年的财位，旺财运、置业。想要助旺财运，有条件的可以在旺位，即{$item['pos']}方位宜开门、开窗，座位、卧床、炉灶；还可以张贴财神画像在{$item['pos']}方，或是摆放一些聚财白菜、聚宝盆、摇钱树等招财开运风水吉祥物，可以增强旺星的吉气，助旺财运。";
            }
            if ($item['y']['fswz'] == '偏财位') {
                $fsbj['cai'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，由于六白星是当运的退运星，因此，本年{$item['pos']}方并非吉位。凡{$item['pos']}方有杂物，不收敛很混乱的，本年主人运气容易有下滑的趋势，如果是座位、卧床在{$item['pos']}方的，应该考虑在条件允许的情况下，调换方位为佳。处在这样的风水环境中，不论是工作还是求财，都较为不顺，因此也要处处小心在意。不要在{$item['pos']}方摆放镜子、衣帽架、金属制品等。如要催旺{$item['pos']}方的风水，可在此方位张贴财神画像、福字等，可以增强旺星的吉气，助旺财运。";
            }
            if ($item['y']['fswz'] == '破财位') {
                $fsbj['cai'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，{$item['y']['title']}星也是当运的退运星，代表着破败、损失等，因此，本年度{$item['pos']}方也并非吉位。不宜将一些贵重物品、保险柜等放置于家中的{$item['pos']}方位置，否则容易出现失窃的情况，从而造成损失。其次，{$item['pos']}方亦不宜有杂物，太杂乱的，本年主人运气容易有下滑的趋势。如要催旺{$item['pos']}方的风水，亦可在此方位张贴财神画像、福字等，可以增强旺星的吉气，助旺财运。";
            }
            // 助旺事业运
            if ($item['y']['fswz'] == '文昌位') {
                $fsbj['shiye'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，四绿星旺文运、官位、事业。四绿星的方位是当年的风水文昌位，可以将读书孩子的书桌或是卧床摆放在{$item['pos']}方，能够提升学习效率。另外，四绿星同时也旺事业、官位，尤其对家中男主人，想要催旺四绿方，可以在{$item['pos']}方位摆放一个文昌塔，将有利于助旺事业。";
            }
            // 助旺感情运
            if ($item['y']['fswz'] == '桃花位') {
                $fsbj['ganqing'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞星<b>{$item['pos']}方</b>位，一白星是当运的生气之星，所到的方位是吉利的方位。贪狼星代表人的人缘、感情，因此有桃花星的趋向。对于想助旺感情运的人来说，要好好把握家中的{$item['pos']}方，如能催旺此方位，有助于感情运势的提升和人际关系的融洽发展。所以，想要催旺一白方位，可在{$item['pos']}方的位置摆放一个开光麒麟或铜葫芦。";
            }
            if ($item['y']['fswz'] == '喜神位') {
                $fsbj['ganqing'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞星<b>{$item['pos']}方</b>位，九紫星是当运的生气星，也是一颗喜庆之星，代表着各种喜事。如{$item['pos']}方位布置得好，本年家中喜事临门，结婚、人缘、订亲、添丁、喜庆、表彰这类的喜事增多。所以，想要催旺九紫方位，可以使{$item['pos']}方保持整洁、乾净，气场流通，光线充足，将有利于助旺感情、喜事临门。";
            }
            // 助旺健康运
            if ($item['y']['fswz'] == '病符位') {
                $fsbj['jiankang'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞到<b>{$item['pos']}方</b>位，二黑星又称病符星，所到之处，会带来疾病和伤痛。凡是座位、卧床以及炉灶在{$item['pos']}方位的住宅，家中主人患病的概率大增。若你家中碰巧是这样而又无法改变的，需要密切关注自己以及家人的身体健康方面的变化，尽早做个全面的体检，防微杜渐。想要助旺化解二黑病符星，{$item['pos']}方位就不要堆放杂物、垃圾，时常保持整洁，将有利于助旺健康。";
            }
            if ($item['y']['fswz'] == '五黄煞') {
                $fsbj['jiankang'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞星<b>{$item['pos']}方</b>位，五黄星又称「五黄煞」、「正关煞」，是一颗极凶之星，代表着意外、疾病、伤害、死亡等凶事，故{$item['pos']}方为最凶的方位。凡是座位、卧床、炉灶位于{$item['pos']}方的住宅，本年易招惹意外的凶祸，需要格外当心。今年家中{$item['pos']}方不宜动土。想要化解{$item['y']['title']}{$item['y']['xuankong']}星，{$item['pos']}方位就不要堆放杂物、垃圾，时常保持整洁，将有利于助旺健康。";
            }
            // 助旺人际关系
            if ($item['y']['fswz'] == '桃花位') {
                $fsbj['renji'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，贪狼星代表人的人缘、感情，因此想要助旺人际关系，可以催旺{$item['pos']}方。所以，想要催旺{$item['pos']}方位，可在{$item['pos']}方的位置摆放一个开光麒麟或铜葫芦。另外，{$item['pos']}所见的颜色愈鲜艳便愈佳，而且任何水种植物皆可加强人际关系。如果想在办公室布阵，只需要于桌面放一盆简单的水种小植物便可。";
            }
            if ($item['y']['fswz'] == '是非位') {
                $fsbj['renji'][] = "{$item['y']['title']}{$item['y']['xuankong']}星飞临<b>{$item['pos']}方</b>位，{$item['y']['title']}星为是非之星，主竞争、是非、矛盾、吵架，飞临之处，会影响主人的人际关系。想要化解影响，助旺人际关系，可以催旺{$item['pos']}方位。今年家中{$item['pos']}方不可动土、装修，不宜摆放流动性强的物品或经常搬动物品，否则很容易引发争吵冲突，影响人际关系；其次，适宜摆放红色物品，如红地毯、红窗帘等，亦有助减弱是非星的力量。";
            }
        }
        // 判断当前九星排盘处于那一年（根据otime）
        $isYear = 0;
        $oJieQi = BaziCommon::getGongliByJieQi($year);
        if (
            $this->lunarO->dateTime->getTimestamp() >= strtotime($oJieQi[0][0][1]) &&
            $this->lunarO->dateTime->getTimestamp() < strtotime($oJieQi[11][2][1])
        ) {
            $isYear = 1;
        }
        return [
            // 当前九星排盘处于那一年（根据otime）
            'current_year' => $isYear ? $year : $year - 1,
            'fp' => $fp,
            'fsbj' => $fsbj,
        ];
    }

    /**
     * 星宿
     * @param int $m
     * @param int $d
     * @return string
     */
    protected function getXingSu(int $m, int $d): string
    {
        $k1 = $m - 1;
        $d1 = $d - 1;
        $list = [
            [ //1
                '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿',
                '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿',
            ],
            [ //2
                '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿',
                '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿',
            ],
            [ //3
                '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿',
                '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿',
            ],
            [ //4
                '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿',
                '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿',
            ],
            [ //5
                '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿',
                '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿',
            ],
            [ //6
                '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿',
                '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿',
            ],
            [ //7
                '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿',
                '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿',
            ],
            [ //8
                '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿',
                '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿',
            ],
            [ //9
                '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿',
                '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿',
            ],
            [ //10
                '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿',
                '参宿', '井宿', '鬼宿', '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿',
            ],
            [ //11
                '斗宿', '女宿', '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿',
                '柳宿', '星宿', '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿',
            ],
            [ //12
                '虚宿', '危宿', '室宿', '壁宿', '奎宿', '娄宿', '胃宿', '昴宿', '毕宿', '觜宿', '参宿', '井宿', '鬼宿', '柳宿', '星宿',
                '张宿', '翼宿', '轸宿', '角宿', '亢宿', '氐宿', '房宿', '心宿', '尾宿', '箕宿', '斗宿', '女宿', '虚宿', '危宿', '室宿',
            ],
        ];
        return $list[$k1][$d1] ?? '';
    }

    /**
     * 流年数据
     * @return array
     */
    protected function getliuYear(): array
    {
        $year = (int)$this->lunar->dateTime->format('Y');
        $otime = strtotime($this->orginData['otime']);
        $oYear = (int)date('Y', $otime);
        $start = $oYear - 5;
        if ($start < $year) {
            $start = $year;
        }
        $result = [];
        for ($i = 0; $i < 15; $i++) {
            $tmpY = $start + $i;
            $gz = BaziExt::getGanZhi($tmpY);
            $isCur = $tmpY == $oYear ? 1 : 0;
            $result[] = [
                'y' => $tmpY,
                'is_cur' => $isCur,
                'age' => $tmpY - $year + 1,
                'gz' => $gz,
            ];
        }
        return $result;
    }

    /**
     * 纪年阴阳
     * @param array $jiNian
     * @return array
     */
    protected function getJnYy(array $jiNian): array
    {
        $result = [];
        foreach ($jiNian as $k => $v) {
            $result[$k] = [
                BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                BaziExt::getYinYang($v[1]) ? '阳' : '阴',
            ];
        }
        return $result;
    }

    /**
     * 大运处理
     * @param array $dayun 大运
     * @return array
     */
    protected function getDayunHandle(array $dayun): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $otime = strtotime($this->orginData['otime']);
        $yearC = date('Y', $otime);
        $i = BaziExt::getKeyWithArray($yearC, $dayun['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $dayun['curr'] = $i;
        // 当前大运详情
        $currInfo = [];
        $eightGz = $dayun['eight']['chronlogy_year'];
        // 日天干地支
        $dgz = $this->lunar->getLunarGanzhiDay();
        foreach ($eightGz as $k => $v) {
            $tmpArr = Utils::mbStrSplit($v);
            $info = $this->getBasePan($tmpArr);
            $info['year'] = $dayun['eight']['year'][$k];
            $info['age'] = $dayun['eight']['age'][$k];
            // 地支主气十神
            $dayun['eight']['godz'][$k] = $info['godz'];
            // 纳音
            $dayun['eight']['na_yin'][$k] = $this->lunar->getNaYinByGz($v);
            // 神煞
            $dayun['eight']['sha'][$k] = $this->shaShen->liuNianToBazi($tmpArr, $jiNian);
            // 自坐(十二长生)
            $dayun['eight']['zi_zuo'][$k] = $info['zi_zuo'];
            // 十二长生(地势)
            $dayun['eight']['terrain'][$k] = $info['terrain'];
            // 空亡
            $dayun['eight']['kongwang'][$k] = $info['kongwang'];
            // 判断是否当前大运
            if ($i == $k) {
                $currInfo = $info;
            }
        }
        // 换运年份尾数
        $lastNumber = (int)$dayun['eight']['year'][0] % 10;
        $lastArr = [$lastNumber - 1, $lastNumber];
        if ($lastNumber < 1) {
            $lastArr = [9, 0];
        }
        $dayun['wei'] = $lastArr;

        // 换运年份（生肖+十神）
        $startYear = $dayun['eight']['year'][$i];
        $sGz = BaziExt::getGanZhi($yearC);
        $endYear = $startYear + 9;
        $endGz = BaziExt::getGanZhi($endYear);
        $dayun['huan'] = [
            // 当前流年 生肖+十神
            's' => [$yearC, implode('', $sGz), $this->lunar->getZodiac($sGz[1]), BaziCommon::getGodName($dgz[0] . $sGz[0])],
            // 换运流年 生肖+十神
            'e' => [$endYear, implode('', $endGz), $this->lunar->getZodiac($endGz[1]), BaziCommon::getGodName($dgz[0] . $endGz[0])],
        ];
        return [
            'curr' => $i,
            'info' => $currInfo,
            'dayun' => $dayun,
        ];
    }

    /**
     * 根据干支获得排盘数据
     * @param array $gz 干支
     * @return array
     */
    protected function getBasePan(array $gz): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $dtg = $jiNian['d'][0];
        $str = implode('', $gz);
        $terrainArr = Ex::getTerrainData();
        return [
            // 干支
            'gz' => $gz,
            // 天干十神
            'god' => BaziCommon::getGodName($dtg . $gz[0]),
            // 地支主气十神
            'godz' => BaziCommon::getGodName($dtg . $gz[1]),
            // 藏干
            '_god' => BaziCommon::getHideGod($gz[1], $dtg),
            // 纳音
            'nayin' => $this->lunar->getNaYinByGz($str),
            // 十二长生(地势)
            'terrain' => $terrainArr[$dtg . $gz[1]] ?? '',
            // 自坐(十二长生)
            'zi_zuo' => $terrainArr[$str],
            // 空亡
            'kongwang' => Huangli::getKongWangbyGz($str),
            // 神煞
            'shensha' => $this->shaShen->liuNianToBazi($gz, $jiNian),
        ];
    }

    /**
     * 交运
     * @param array $fateAge
     * @return array
     * @throws Exception
     */
    protected function getJiaoYun(array $fateAge): array
    {
        $jiaoYunStr = "+{$fateAge['year']} year {$fateAge['month']} month {$fateAge['day']} day +{$fateAge['hour']} hour";
        $tmpJaoTime = strtotime($jiaoYunStr, $this->lunar->dateTime->getTimestamp());
        $time = date('Y-m-d H:00:00', $tmpJaoTime);
        $lunar = Ex::date($time);
        $base = $lunar->getLunarByBetween();
        return [
            'gongli' => [
                'y' => (int)$lunar->dateTime->format('Y'),
                'm' => (int)$lunar->dateTime->format('m'),
                'd' => (int)$lunar->dateTime->format('d'),
                'h' => (int)$lunar->dateTime->format('h'),
            ],
            'nongli' => $base['nongli'],
            '_nongli' => $base['_nongli'],
            'h' => $base['jinian']['h'][1],
        ];
    }

    /**
     * 五行分析
     * @return array
     */
    protected function getWxxBi(): array
    {
        $list = [
            '寅' => [1.571, 1.548, 0.924, 0.716, 0.862],
            '卯' => [2, 1.414, 0.5, 0.707, 1],
            '辰' => [1.166, 1.074, 1.421, 1.161, 0.8],
            '巳' => [0.862, 1.571, 1.548, 0.924, 1.716],
            '午' => [0.912, 1.7, 1.59, 0.774, 0.645],
            '未' => [0.924, 1.341, 1.674, 1.069, 0.612],
            '申' => [0.795, 0.674, 1.012, 1.641, 1.498],
            '酉' => [0.5, 0.707, 1, 2, 1.414],
            '戌' => [0.674, 1.012, 1.641, 1.498, 0.795],
            '亥' => [1.59, 0.774, 0.645, 0.912, 1.7],
            '子' => [1.414, 0.5, 0.707, 1, 2],
            '丑' => [0.898, 0.821, 1.512, 1.348, 1.041],
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $god2 = $this->lunar->_getGod();
        $mdz = $jiNian['m'][1];
        // $mtg = $jiNian['m'][0];
        $list1 = [
            '木' => $list[$mdz][0], '火' => $list[$mdz][1], '土' => $list[$mdz][2], '金' => $list[$mdz][3], '水' => $list[$mdz][4],
        ];
        // 藏干分数
        $list2 = [
            '子癸' => 100, '丑己' => 60, '寅甲' => 60, '卯乙' => 100, '辰戊' => 60, '巳丙' => 60, '午丁' => 70, '未己' => 60, '申庚' => 60,
            '酉辛' => 100, '戌戊' => 60, '亥壬' => 70, '丑癸' => 30, '寅丙' => 30, '辰乙' => 30, '巳戊' => 30, '午己' => 30, '未丁' => 30,
            '申壬' => 30, '戌辛' => 30, '亥甲' => 30, '丑辛' => 10, '寅戊' => 10, '辰癸' => 10, '巳庚' => 10, '未乙' => 10, '申戊' => 10, '戌丁' => 10,
        ];
        $wxAttr = $this->lunar->wuXingAttr;
        $wxFen = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $yinFen = 0;
        $yanFen = 0;
        foreach ($jiNian as $v) {
            $wx = $wxAttr[$v[0]];
            $yy = BaziExt::getYinYang($v[0]);
            $factor = $list1[$wx];
            $fen = bcmul(100, $factor, 3);
            if ($yy) {
                $yanFen = bcadd($yanFen, $fen, 3);
            } else {
                $yinFen = bcadd($yinFen, $fen, 3);
            }
            $wxFen[$wx] = bcadd($wxFen[$wx], $fen, 3);
        }
        foreach ($god2 as $key => $rs) {
            $k1 = substr($key, 0, 1);
            $dz1 = $jiNian[$k1][1];
            foreach ($rs['hide'] as $rs1) {
                $fen = $list2[$dz1 . $rs1] ?? 0;
                $wx = $wxAttr[$rs1];// 五行
                $yy = BaziExt::getYinYang($rs1);// 阴阳
                $factor = $list1[$wx];
                $fen = bcmul($fen, $factor, 3);
                if ($yy) {
                    $yanFen = bcadd($yanFen, $fen, 3);
                } else {
                    $yinFen = bcadd($yinFen, $fen, 3);
                }
                $wxFen[$wx] = bcadd($wxFen[$wx], $fen, 3);
            }
        }
        $totalFen = bcadd($yinFen, $yanFen, 3);
        $yanZb = bcmul(bcdiv($yanFen, $totalFen, 4), 100, 2);
        $yinZb = bcsub(100, $yanZb, 2);
        $wxZb = [];
        foreach ($wxFen as $k => $v) {
            $wxZb[] = [$k, bcdiv(bcmul($v, 100), $totalFen, 2)];
        }
        // $wxFen = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $wxZb[4][1] = number_format(100 - $wxZb[0][1] - $wxZb[1][1] - $wxZb[2][1] - $wxZb[3][1], 2);
        return [
            'yy' => [
                'yan' => $yanZb, 'yin' => $yinZb,
            ],
            'wx' => $wxZb,
            'wx_fen' => $wxFen,
            'yy_fen' => [
                'yin' => $yinFen, 'yan' => $yanFen,
            ],
        ];
    }

    /**
     * 五行缺结果
     * @param array $wxZb1 五比占比结果
     * @return array
     */
    protected function getWxQueRes(array $wxZb1): array
    {
        $list = [
            '水' => [
                'food' => '一、蔬果类，多食用白菜，芥菜，山竹，南瓜，黑木耳，栗子，海带，波菜，或川七等蔬果，平日可多喝莲花茶，冬瓜汤，豆腐汤，味增汤，苦瓜汤，莲子汤，黑豆茶，豆浆，牛奶，黑芝麻汤。二、水果类，可以多食用椪柑，桔子，椰子，西瓜，或八仙果等水果。',
                'number' => '您可以在您的电话号码、手机号码、车牌号码、门牌号码，以及幸运数字等身旁常用的数字当中，多使用1、6、13、14、19、31、41、91等数字。',
                'letter' => '请多使用i、j、s、t等字母，作为您的英文名、艺名、笔名，以及绰号，也能够产生相同的效果。',
                'color' => '黑色，白色，深蓝色，水蓝色等颜色，对于提升您的运气有很大的功效。',
                'sport' => 'spa，桑拿，按摩，泡澡，泡冷泉，洗冷水澡，药浴，划船，游泳，跳水，滑雪，浮潜，或各种水上，冰上活动等都属于水。',
            ],
            '金' => [
                'food' => '一、蔬果类，多食用白萝卜，山药，木耳，黄豆，白豆，平日可多喝菊花茶，蚕豆汤。二、水果类，可以多食用柚子，罗汉果，水梨，西瓜，或释迦等水果。',
                'number' => '您可以在您的电话号码、手机号码、车牌号码、门牌号码，以及幸运数字等身旁常用的数字当中，多使用4、6、7、9、26、49、62、67、68、76、78、86、87、94等数字。',
                'letter' => '请多使用g、h、q、r等字母，作为您的英文名、艺名、笔名，以及绰号，也能够产生相同的效果。',
                'color' => '白色，银色，银白色，金黄色等颜色，对于提升您的运气有很大的功效。',
                'sport' => '黄昏时候的运动属于金。用健身房的机器健身，跑步，或重量训练等运用到金属机械，属于金。慢跑，韵律操，有氧舞蹈，深呼吸等有氧活动训练到肺部，属于金。射击射箭，西洋剑，或中国武术等使用到金属器具，也属于金。',
            ],
            '火' => [
                'food' => '一、蔬果类，多食用芹菜，茄子，红萝卜，以及红叶类蔬菜，平日可多喝红豆汤，玫瑰花茶。此外，可以多食用一些姜，咖哩，辣椒，大蒜，芥末，香草，沙嗲，八角，花椒等香料颣也都能补足火气。二、水果类，可以多食用蕃茄，柿子，李子，荔枝，榴莲或火龙果等水果。如果喝果汁的话，蕃茄汁，胡萝卜汁，还有红枣茶都是非常好的。三、红皮颣，红葡萄连皮吃，红辣椒，红蕃茄，红皮洋葱，红皮苹果连皮吃。',
                'number' => '您可以在您的电话号码、手机号码、车牌号码、门牌号码，以及幸运数字等身旁常用的数字当中，多使用2、7、9、19、27、39、72、91、93等数字。',
                'letter' => '请多使用c、d、m、n、w、x等字母，作为您的英文名、艺名、笔名，以及绰号，也能够产生相同的效果。',
                'color' => '红色，紫色，紫红色，淡红色，粉红色，橘红色等颜色，对于提升您的运气有很大的功效。',
                'sport' => '如果您能每天晒一晒太阳，不需要很长时间，每天五分钟就可以了，多吸收阳光的能量，对您非常有益。此外，任何能让身体热起来的活动都对您很有帮助。太极，导引，静坐等气功类活动，属于木火。跆拳道，空手道，武术等技击类活动，也属于火。体操，瑜珈，踏青，登山，慢跑，有氧舞蹈等有氧类运动，也属于火。排球，羽球，篮球，回力球等剧烈运动，也属于火。',
            ],
            '土' => [
                'food' => '一、饮料类，平日可多喝向日葵花茶，牛篣茶，豆浆或花生汤。二、水果类，可以多食用枣子，梨子，龙眼或柿子等水果。三、肉类，宜少吃。',
                'number' => '您可以在您的电话号码、手机号码、车牌号码、门牌号码，以及幸运数字等身旁常用的数字当中，多使用2、5、6、8、26、28、62、68、78、82、86、87等数字。',
                'letter' => '请多使用e、f、o、p、y、z等字母，作为您的英文名、艺名、笔名，以及绰号，也能够产生相同的效果。',
                'color' => '橙色，橘色，明黄色，土黄色等颜色，对于提升您的运气有很大的功效。',
                'sport' => '健行，登山，踏青等接触大地的活动，属于土。玩沙堆沙，海滩排球，等海边活动，也属于土。跆拳道，空手道，武术等技击类活动，属于火土。体操，瑜珈，踏青，登山，慢跑，有氧舞蹈等有氧类运动，属于火土。排球，羽球，篮球，回力球等剧烈运动，也属于火土。',
            ],
            '木' => [
                'food' => '一、蔬菜类，例如番薯，芋头，莲藕，竹笋。平日可多喝洛神花茶，或喝绿豆汤，吃绿豆糕，还有毛豆也有增加木气的功效。二、水果类，可以多食用梅子，酸梅，桶柑，海梨柑，或枇杷等水果。',
                'number' => '您可以在您的电话号码、手机号码、车牌号码、门牌号码，以及幸运数字等身旁常用的数字当中，多使用3、4、8、13、14、31、34、39、41、43、93等数字。',
                'letter' => '请多使用a、b、k、l、u、v等字母，作为您的英文名、艺名、笔名，以及绰号，也能够产生相同的效果。',
                'color' => '青色，绿色，草绿色，碧绿色，蓝色，天蓝色，水蓝色等颜色，对于提升您的运气有很大的功效。',
                'sport' => '在早晨五点至七点的时间进行运动对您最有益。此外，早晨在公园运动，慢跑，去森林踏青，深呼吸，做森林浴，登山，健行等接触树木山林的活动，都属于木。瑜珈，体操，跑步等活动四肢的运动，也都属于木。太极，导引，静坐等气功类活动，属于木火。以上这些活动，都非常适合您。',
            ],
        ];
        $minWx = current(array_keys($wxZb1));
        $minNum = current($wxZb1);
        if ($minNum == 0) {
            $str = "有缺{$minWx}的表现，五行缺失属于正常现象，可以通过方法补足。";
        } elseif ($minNum <= 10) {
            $str = "有缺少{$minWx}的表现，五行缺失属于正常现象，可以通过方法补足。";
        } elseif ($minNum <= 15) {
            $str = "有轻微缺{$minWx}的表现，五行缺失属于正常现象，可以通过方法补足。";
        } else {
            $str = "五行相对平衡，{$minWx}五行相对较少。";
        }
        $result = $list[$minWx];
        $result['info'] = $str;
        return $result;
    }

    /**
     * 性格
     * @param array $wxZb1 五比占比结果
     * @return array
     */
    protected function getxingGe(array $wxZb1): array
    {
        $list = [
            '火' => [
                'info' => '该性格的人往往善于表达自己，具有煽动性，社交能力极强、积极乐观，热情幽默、口才好、说服力强、乐观和善、具同理心、善于察言观色、会打扮、懂得自我宣传，也很善于沟通。',
                'good' => '热情活泼，上进奋发，勇敢无畏；有创见，思维敏捷，擅长发明，表达力强；积极，乐观，很快就忘记了忧愁的事情，不记仇，易与别人融合；鼓动性、煽动性强，说话的时候有一种感染力。属高度外向型。',
                'bad' => '急躁、好争理、喜夸张、好虚荣、爱面子、骄傲好斗，好高鹜远。',
            ],
            '金' => [
                'info' => '该性格的人往往是强势的领导者，追求大局在握，掌控主动权，是高驱动者，强势的沟通者。他们胸怀大志、目标远大、勇于开创和冒险、敢于挑战未知、竞争性强、不妥协、积极自信、有敏锐的分析力、看问题直指核心。说话简洁并切中核心，具有果断、冒险、好胜的性格。',
                'good' => '刚毅，果断的、强势的，雷厉风行的，韧性非常强，敢于冒险，一旦下定了决心很难改变；目标性极强；强势的领导者，掌控能力强，高驱动、高支配型。讲话简洁并切中核心。这种人非常聪明，因为我们从八卦里可知道乾卦和兑卦都属金，特聪明。',
                'bad' => '太刚毅，不太容易变通，就像秋天一样的肃杀之气，给人一种寒冷的，一看就是不怒而威。容易钻牛角尖，不够灵活，过于果断就是刚愎自用。',
            ],
            '木' => [
                'info' => '这种人注重细节、重视条理，计划严密，不喜欢过多的口头表达、性格内敛，这一点像金；但公平正直、黑白分明，是非分明，有严格的个人标准，有完美主义倾向，这一点又像木。从一个管理者来说，这类人是一个完美主义者，小心谨慎，是可以信赖的，注重观察，传统，重隐私。他们的行事风格：对于是非黑白和公平，有严格的个人标准；对冒险的事情，十分小心；宁肯不做，也不要做错。而作为下属时，他们会尊重领导者的权威。',
                'good' => '正直、直率；有担当力，上进心，建功立业；有主意、能忍辱；温；仁德，恻隐之心，慈祥，济物利民，怜孤念寡，悲天悯人。属于外向型。',
                'bad' => '看不起别人；好抗上、不服人、顶撞；事情不理想时易忧愁，善变不稳定。',
            ],
            '水' => [
                'info' => '他们擅长整合内外资源、兼容并蓄，以合理化及中庸之道待人处世；会依据所处环境的不同随时调整自己，面临新环境或变化时能迅速充分地融入环境和适应变化。',
                'good' => '善于变化，协调能力强，顺时而动，随机而变；性柔和，温和，委婉，肯低矮就下，谦虚，不张扬，不炫耀。城府较深，偏于内向；足智多谋，心灵手巧，擅精艺术。外柔内刚，水就是坎卦，坎卦外面是阴，中间是阳，坎卦就是“含刚强于柔弱之中，寓申韩于黄老之内。',
                'bad' => '消沉，抑郁，自卑，多忧多虑，多愁善感；爱哭，生闷气；意志不够坚定，容易动摇，缺乏主见；上进心不够，得过且过。',
            ],
            '土' => [
                'info' => '他们的行事风格是：即使面对困境也能泰然自若，不只着眼短期的成绩，更重视长期的发展规划。除非外在有压力，他们通常不会强迫自己在组织中成为领导者。',
                'good' => '稳重，厚实，稳健和谐，厚道，勤恳实干，稳定持久，本分老实；心地温和，待人诚恳，宽宏大量，宽厚；具有责任感，信实、言必行，行必果，不喜欢趋炎附势，也不喜欢弄权玩势，合作互助；耐力强，持久性强。偏于内向型。',
                'bad' => '创造力不够，沉闷呆板，反应迟钝，固执，头脑僵化；疑心重，埋怨他人，一生多累。',
            ],
        ];
        $wxzbKeys = array_keys($wxZb1);
        $minWx = current($wxzbKeys);
        $result = $list[$minWx];
        $bad = $result['bad'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wxAttr = $this->lunar->wuXingAttr;
        $dtgWx = $wxAttr[$jiNian['d'][0]];
        $maxWx = end($wxzbKeys);
        $uWxNum = $wxZb1[$dtgWx];
        $shenWx = BaziCommon::getWxGuanxi($dtgWx, '泄');
        $shenNum = $wxZb1[$shenWx];
        if ($maxWx === $dtgWx) {
            $result = $list[$dtgWx];
        } elseif ($uWxNum <= 10) {
            if ($shenNum < 20) {
                $result = $list[$maxWx];
            }
        } elseif ($uWxNum <= 20) {
            if ($shenNum <= 15) {
                $result = $list[$dtgWx];
            }
        }
        $result['bad'] = $bad;
        return $result;
    }

    /**
     * 未来影响
     * @param string $minWx 五行
     * @return array
     */
    protected function getFuture(string $minWx): array
    {
        $list = [
            '土' => [
                "在人际关系方面，您若能表现出您的纯朴与善良，沉默与随和，但是谨记不要让人觉得您的表现有点过于木讷，这样您的人际关系一定会越来越圆满。",
                "在工作职场方面，您要表现出对公司高度的忠诚，以及做事负责任的态度，要让老板放心把工作交给您，并且要多做事、少说话，不要单打独斗，也不要有自我主义，这样您在工作方面一定会更得心应手，老板会更器重您，同事也会更欣赏您。",
                "在生活起居方面，您一定要三餐定时定量，不可暴饮暴食。身体要放轻松，精神要保持愉快，不要给自己过多的压力。只要您每天保持好心情，那您的命运将会有很大的改变，身体健康，财运顺利，事业圆满，家庭幸福。",
            ],
            '木' => [
                "在人际关系方面，您若能表现出您的善良与体贴，仁慈与宽厚，但是谨记不要让人觉得您的表现有点过于软弱，这样您的人际关系一定会越来越圆满。",
                "在工作职场方面，您要表现出憨厚，老实的一面，最好能默默工作，并且不求表现，这样您在工作方面一定会更得心应手，老板会更器重您，同事也会更欣赏您。",
                "在生活起居方面，您一定不能喝酒，喝酒会让您的运势低落，体能衰退。此外，如果您还能养成早睡早起的好习惯，再配合早晨去公园运动，那您的命运将会有很大的改变，身体健康，财运顺利，事业圆满，家庭幸福。",
            ],
            '火' => [
                "在人际关系方面，您若能表现出您的礼貌与风度，学识与涵养，但是谨记不要让人觉得您的表现有点过于虚假，这样您的人际关系一定会越来越圆满。",
                "在工作职场方面，您要表现出高度的eq与良好的人际关系，要懂得跟老板或同事社交，并且要保持礼貌，以及注重职场伦理，这样您在工作方面一定会更得心应手，老板会更器重您，同事也会更欣赏您。",
                "在生活起居方面，您一定要早睡早起，最好每天晚上十一点前就寝。此外，最好多做日光浴，多晒太阳，时间不用长，每天五分钟就足够了，但是要避开下午一点到三点阳光最烈的时间。只要您能持之以恒，那您的命运将会有很大的改变，身体健康，财运顺利，事业圆满，家庭幸福。",
            ],
            '金' => [
                "在人际关系方面，您若能表现出您的自信与魅力，果断与魄力，但是谨记不要让人觉得您的表现有点过于霸道，这样您的人际关系一定会越来越圆满。",
                "在工作职场方面，您要表现出您的工作能力，要有绩效，要能独当一面，不要光说不做，这样您在工作方面一定会更得心应手，老板会更器重您，同事也会更欣赏您。",
                "在生活起居方面，您最好能多练习腹式呼吸，此外，您要保持外围环境的空气新鲜，通风顺畅，尤其晚上睡觉的时候，卧房千万不可紧闭门窗。只要您体内的氧气充足，那您的命运将会有很大的改变，身体健康，财运顺利，事业圆满，家庭幸福。",
            ],
            '水' => [
                "在人际关系方面，您若能表现出您的智慧与想法，创意与才华，但是谨记不要让人觉得您的表现有点过于圆滑，这样您的人际关系一定会越来越圆满。",
                "在工作职场方面，您要表现出您的聪明才智，与才华创意，多提供您的建议与想法，多为公司出谋划策，这样您在工作方面一定会更得心应手，老板会更器重您，同事也会更欣赏您。",
                "在生活起居方面，您每天一定要留一些时间给自己思考，多想想平常没时间思考的问题，这样可以有助于您做出正确的决策。只要您做事能三思而后行，有正确的方向，再加上您的行动力，那您的命运将会有很大的改变，身体健康，财运顺利，事业圆满，家庭幸福。",
            ],
        ];
        return $list[$minWx];
    }

    /**
     * 生态和调候
     * @return array
     */
    protected function getEcology()
    {
        $tgdz = $this->lunar->getLunarTganDzhi();
        $dtg = $tgdz['d'][0];
        $mdz = $tgdz['m'][1];
        $wuXingAttr = $this->lunar->wuXingAttr;
        $list = [
            '甲寅' => ['丙', '癸'],
            '甲卯' => ['庚', '丙', '丁', '戊', '己'],
            '甲辰' => ['庚', '丁', '壬'],
            '甲巳' => ['癸', '庚', '丁'],
            '甲午' => ['癸', '庚', '丁'],
            '甲未' => ['癸', '庚', '丁'],
            '甲申' => ['庚', '丁', '壬'],
            '甲酉' => ['庚', '丁', '丙'],
            '甲戌' => ['庚', '甲', '丁', '壬', '癸'],
            '甲亥' => ['庚', '丁', '丙', '戊'],
            '甲子' => ['丁', '庚', '丙'],
            '甲丑' => ['丁', '庚', '丙'],
            '乙寅' => ['丙', '癸'],
            '乙卯' => ['丙', '癸'],
            '乙辰' => ['癸', '丙', '戊'],
            '乙巳' => ['癸'],
            '乙午' => ['癸', '丙'],
            '乙未' => ['癸', '丙'],
            '乙申' => ['丙', '癸', '己'],
            '乙酉' => ['癸', '丙', '丁'],
            '乙戌' => ['癸', '辛'],
            '乙亥' => ['丙', '戊'],
            '乙子' => ['丙'],
            '乙丑' => ['丙'],
            '丙寅' => ['壬', '庚'],
            '丙卯' => ['壬', '己'],
            '丙辰' => ['壬', '甲'],
            '丙巳' => ['壬', '癸', '庚'],
            '丙午' => ['壬', '庚'],
            '丙未' => ['壬', '庚'],
            '丙申' => ['壬', '戊'],
            '丙酉' => ['壬', '癸'],
            '丙戌' => ['甲', '壬'],
            '丙亥' => ['甲', '戊', '庚', '壬'],
            '丙子' => ['壬', '戊', '己'],
            '丙丑' => ['壬', '甲'],
            '丁寅' => ['甲', '庚'],
            '丁卯' => ['庚', '甲'],
            '丁辰' => ['甲', '庚'],
            '丁巳' => ['甲', '庚'],
            '丁午' => ['壬', '庚', '癸'],
            '丁未' => ['甲', '壬', '庚'],
            '丁申' => ['甲', '庚', '丙', '戊'],
            '丁酉' => ['甲', '庚', '丙', '戊'],
            '丁戌' => ['甲', '庚', '戊'],
            '丁亥' => ['甲', '庚'],
            '丁子' => ['甲', '庚'],
            '丁丑' => ['甲', '庚'],
            '戊寅' => ['丙', '甲', '癸'],
            '戊卯' => ['丙', '甲', '癸'],
            '戊辰' => ['甲', '丙', '癸'],
            '戊巳' => ['甲', '丙', '癸'],
            '戊午' => ['壬', '甲', '丙'],
            '戊未' => ['癸', '丙', '甲'],
            '戊申' => ['丙', '癸', '甲'],
            '戊酉' => ['丙', '癸'],
            '戊戌' => ['甲', '丙', '癸'],
            '戊亥' => ['甲', '丙'],
            '戊子' => ['丙', '甲'],
            '戊丑' => ['丙', '甲'],
            '己寅' => ['丙', '庚', '甲'],
            '己卯' => ['甲', '癸', '丙'],
            '己辰' => ['丙', '癸', '甲'],
            '己巳' => ['癸', '丙'],
            '己午' => ['癸', '丙'],
            '己未' => ['癸', '丙'],
            '己申' => ['丙', '癸'],
            '己酉' => ['丙', '癸'],
            '己戌' => ['甲', '丙', '癸'],
            '己亥' => ['丙', '甲', '戊'],
            '己子' => ['丙', '甲', '戊'],
            '己丑' => ['丙', '甲', '戊'],
            '庚寅' => ['戊', '甲', '壬', '丙', '丁'],
            '庚卯' => ['丁', '甲', '庚', '丙'],
            '庚辰' => ['甲', '丁', '壬', '癸'],
            '庚巳' => ['壬', '戊', '丙', '丁'],
            '庚午' => ['壬', '癸'],
            '庚未' => ['丁', '甲'],
            '庚申' => ['丁', '甲'],
            '庚酉' => ['丁', '甲', '丙'],
            '庚戌' => ['甲', '壬'],
            '庚亥' => ['丁', '丙'],
            '庚子' => ['丁', '甲', '丙'],
            '庚丑' => ['丙', '丁', '甲'],
            '辛寅' => ['己', '壬', '庚'],
            '辛卯' => ['壬', '甲'],
            '辛辰' => ['壬', '甲'],
            '辛巳' => ['壬', '甲', '癸'],
            '辛午' => ['壬', '己', '癸'],
            '辛未' => ['壬', '庚', '甲'],
            '辛申' => ['壬', '甲'],
            '辛酉' => ['壬', '甲'],
            '辛戌' => ['壬', '甲'],
            '辛亥' => ['壬'],
            '辛子' => ['丙', '戊', '壬', '申'],
            '辛丑' => ['壬', '戊', '己'],
            '壬寅' => ['庚', '丙', '戊'],
            '壬卯' => ['戊', '辛', '庚'],
            '壬辰' => ['甲', '庚'],
            '壬巳' => ['壬', '辛庚', '癸'],
            '壬午' => ['癸', '庚', '辛'],
            '壬未' => ['辛', '甲'],
            '壬申' => ['戊', '丁'],
            '壬酉' => ['甲', '庚'],
            '壬戌' => ['甲', '丙'],
            '壬亥' => ['戊', '丙', '庚'],
            '壬子' => ['戊', '丙'],
            '壬丑' => ['丙', '丁', '甲'],
            '癸寅' => ['辛', '丙'],
            '癸卯' => ['庚', '辛'],
            '癸辰' => ['丙', '辛', '甲'],
            '癸巳' => ['辛'],
            '癸午' => ['庚', '辛', '壬', '癸'],
            '癸未' => ['庚', '辛', '壬', '癸'],
            '癸申' => ['丁'],
            '癸酉' => ['辛', '丙'],
            '癸戌' => ['辛', '甲', '壬', '癸'],
            '癸亥' => ['庚', '辛', '戊', '丁'],
            '癸子' => ['丙', '辛'],
            '癸丑' => ['丙', '丁'],
        ];
        return [
            $dtg . $wuXingAttr[$dtg] . '生于' . $mdz . '月',
            $dtg . '生' . $mdz . '月',
            $list[$dtg . $mdz],
        ];
    }
}
