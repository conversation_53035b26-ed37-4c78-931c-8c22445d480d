<?php
// +----------------------------------------------------------------------
// | Jiuxing 九宫飞星、玄空飞星、河图数理、河图洛书
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1\qyapp;

use api\ApiResult;
use app\lib\new2021\NineStar;
use calendar\exceptions\Exception;
use calendar\traits\ArrayMove;
use think\facade\Request;
use think\facade\Validate;

class Jiuxing
{
    use ArrayMove;

    /**
     * 结果
     * @var array
     */
    protected array $result = [];

    /**
     * 九星
     * @return ApiResult
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public function jx()
    {
        $data = [
            // 时间-公历
            'time' => Request::param('time', date('Y-m-d H:i:s', time()), 'trim'),
        ];
        $validate = Validate::rule(
            [
                'time|时间' => ['require', 'dateFormat:Y-m-d H:i:s'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        $nineStar = new NineStar($data['time']);
        $this->result['base'] = $nineStar->base;
        $this->result['solar'] = $nineStar->solar;
        $this->result['pan'] = [
            'y' => $nineStar->getYearPan(),
            'm' => $nineStar->getMonthPan(),
            'd' => $nineStar->getDayPan(),
            'h' => $nineStar->getHourPan(),
        ];
        $jxFp = $nineStar->getPiaipan();
        // 完整九宫飞星
        $this->result['jiuxing'] = $jxFp;
        // 中宫九宫飞星
        $this->result['zhonggong'] = $this->getZhongGong($jxFp);
        return ApiResult::success($this->result);
    }

    /**
     * 中宫九宫飞星
     * @param array $jxFp 完整九宫飞星
     * @return array
     */
    protected function getZhongGong(array $jxFp): array
    {
        // 九宫飞星基础解释
        $jgfxjcjsb = [
            '一白' => '一白星在得令的时候，代表官升、名气、中状元、官运和财运。但此星失令的时候，此星为桃花劫，容易出现感情上的纠葛往往会引起不吉的事件发生。',
            '二黑' => '二黑星代表病符。此星在得令的时候并非病符，代表位列尊崇，能成霸业。但此星失令的时候，凶上加凶，往往会引起很不好的事件发生，与五黄星并列为大凶星;此星亦代表自身容易被不良磁场所影响。',
            '三碧' => '三碧星代表是非。此星在得令时代表因口才而成名，大利律师、法官等职。但此星失令的时候，需多多注意财物的保护和分配，工作上应谨小慎微，切勿好大喜功。',
            '四绿' => '文曲星在得令的时候，代表文化艺术、才华、文思敏捷。但失令时为桃花劫，容易出现感情上的纠葛。',
            '五黄' => '廉贞星得令时代表位处中极威崇无比，如皇帝一般。但此星失令的时候，称为五黄煞又名正关煞，容易对家庭及个人产生较大影响，需多注意身体，此星亦代表自身容易被不良磁场所影响。',
            '六白' => '六白是偏财星，与一白、八白合称三大财星。六白得令时丁财两旺，失令时，为失财星，需多注意人身和财产安全。',
            '七赤' => '七赤星当运的时候，大利以口才工作的人，包括歌星、演说家等，大利通讯传播。但七赤星退运的时候，代表火，需多注意消防安全，以及防范呼吸系统疾病。',
            '八白' => '八白星得令时为太白财星，能带来功名富贵。田宅科发，为九星中的大吉星。但此星失令的时候，需多注意财产安全，防范传染疾病。',
            '九紫' => '九紫星当令时为一级喜庆星及爱情星，代表桃花人缘及天乙贵人，大利置业及建筑。但此星失令的时候为桃花劫，容易出现感情上的纠葛;亦代表火，需多注意消防安全，以及防范呼吸系统疾病。',
        ];
        $zhongGong = $jxFp[4];
        $zhongGong['y']['jcjs'] = $jgfxjcjsb[$zhongGong['y']['title']];
        $zhongGong['m']['jcjs'] = $jgfxjcjsb[$zhongGong['m']['title']];
        $zhongGong['d']['jcjs'] = $jgfxjcjsb[$zhongGong['d']['title']];
        $zhongGong['h']['jcjs'] = $jgfxjcjsb[$zhongGong['h']['title']];
        return $zhongGong;
    }
}
