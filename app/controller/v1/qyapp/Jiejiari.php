<?php
// +----------------------------------------------------------------------
// | Jiejiari 节假日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1\qyapp;

use api\ApiResult;
use app\model\rili\Holiday;
use calendar\traits\ArrayMove;
use think\facade\Request;
use think\facade\Validate;

class Jiejiari
{
    use ArrayMove;

    /**
     * 结果
     * @var array
     */
    protected array $result = [];

    /**
     * 获取整年节假日
     * @return ApiResult
     */
    public function list()
    {
        $data = [
            // 年份-公历
            'year' => Request::param('year', date('Y'), 'trim'),
        ];
        $validate = Validate::rule(
            [
                'year|年份' => ['require'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        $yearList = explode(',', $data['year']);
        // 根据年份查询整年节假日
        foreach ($yearList as $year) {
            $list = Holiday::infoByYear($year);
            foreach ($list as $k => $v) {
                $this->result[$year][$v['d']][] = [
                    'date' => $v['d'],
                    'title' => $v['title'],
                ];
            }
        }
        return ApiResult::success($this->result);
    }
}
