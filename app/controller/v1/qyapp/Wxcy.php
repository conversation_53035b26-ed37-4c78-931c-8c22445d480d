<?php
// +----------------------------------------------------------------------
// | Wxcy 五行穿衣/每日穿衣指南/每日穿搭指南
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1\qyapp;

use api\ApiResult;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\exceptions\Exception;
use calendar\Huangli as CalendarHuangli;
use calendar\Lunar;
use calendar\plugin\WuXing;
use calendar\traits\ArrayMove;
use think\facade\Request;

class Wxcy
{
    use ArrayMove;

    /**
     * 结果
     * @var array
     */
    protected array $result = [];

    /**
     * 黄历对象
     * @var CalendarHuangli
     */
    protected CalendarHuangli $huangli;

    /**
     * @return ApiResult
     */
    public function index()
    {
        $data = [
            // 时间-公历
            'time' => Request::param('time', date('Y-m-d H:i:s', time()), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|时间' => ['require', 'isDateOrTime:时间'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }

        // 初始化黄历对象
        $this->huangli = CalendarHuangli::date($data['time']);
        // 时区
        $timezone = $this->huangli->dateTime->getTimezone();
        // 八字基础数据
        $base = $this->huangli->getLunarByBetween();
        // 公历
        $this->result['gongli'] = [
            // 日期
            'y' => (int)$this->huangli->dateTime->format('Y'),
            'm' => (int)$this->huangli->dateTime->format('m'),
            'd' => (int)$this->huangli->dateTime->format('d'),
            'cn_date' => $this->huangli->dateTime->format('Y年m月d日'),
            // 星期几
            'xqj' => (int)$this->huangli->dateTime->format('N'),
            'cn_xqj' => CalendarHuangli::getWeekChs($this->huangli->dateTime->getTimestamp()),
            // 今年第几周
            'jndjz' => (int)$this->huangli->dateTime->format('W'),
            // 星座
            'xingzuo' => $this->huangli->getXingZuo(),
            // 今天是今年第多少天
            'jn_days' => (int)date_diff(date_create("{$this->huangli->dateTime->format('Y')}-01-01", $timezone), $this->huangli->dateTime)->format("%a") + 1,
            // 今年有多少天
            'jn_y_days' => (int)$this->huangli->dateTime->format('Y') % 4 ? 365 : 366,
        ];
        // 农历，leap 是否闰月
        $nongli = $this->huangli->getNongLi();
        // 生肖
        $nongli['shengxiao'] = $base['shengxiao'];
        // 农历当月有多少天
        $nongli['n_days'] = $this->huangli->getLunarDays($nongli['y'], $nongli['m'], $nongli['leap']);
        // 当月是大月还是小月
        $nongli['cn_dyxy'] = $nongli['n_days'] >= 30 ? '大' : '小';
        // 农历当年有多少天
        $nongli['y_days'] = $this->getNongliDays($nongli['y']);
        // 农历
        $this->result['nongli'] = $nongli;
        // 皇帝纪元
        $this->result['hd_jiyuan'] = [
            // 年
            'y' => 2697 + $nongli['y'],
        ];
        // 纪年
        $jiNian = $base['jinian'];
        $this->result['jinian'] = $jiNian;
        // 纪年五行
        $jnWx = $this->getJnwx($jiNian);
        // 纪年五行
        $this->result['jnwx'] = $jnWx;
        // 纪年生肖
        $jnSx = [
            'y' => $this->huangli->getZodiac($jiNian['y'][1]),
            'm' => $this->huangli->getZodiac($jiNian['m'][1]),
            'd' => $this->huangli->getZodiac($jiNian['d'][1]),
            'h' => $this->huangli->getZodiac($jiNian['h'][1]),
        ];
        $this->result['jinian_sx'] = $jnSx;
        // 合害
        $this->result['hehai'] = $this->getHeHai($jiNian['d'][1]);
        // 五行穿衣
        $this->result['wxcy'] = $this->getWxcy($jnWx['d']['wx'][1]);
        // 五行原理说明
        $this->result['wxylsm'] = [
            '五行穿衣是根据五行学说指导每日穿衣搭配的方法，五行：木、火、土、金、水，都有自己独特的代表色。金代表白色系，木代表绿色系，水代表黑色系，火代表红色系，土代表黄色系。',
            '五行学说是中国古代哲学的核心理论之一，认为宇宙万物由五种基本元素（金、木、水、火、土）的相互作用构成，每种元素都代表着一种特定的属性和能量。',
            '生活中，服装穿搭本就是一门深奥的学问，不同的颜色间搭配往往能产生很多令人惊艳的变化。但如果穿搭色不合适，也可能对健康、运势、情感等造成不良影响。在考虑审美的同时，结合五行命理选择适合大环境、适合自己的穿搭色，是我们每个人必修的一门课，也是后天改运的一个切实可行的办法。',
            '“五行穿搭”，根据每日五行属性，搭配当天最适宜色彩，通过五行穿衣指南来帮助调节自身的能量及气场，从而做到天人合一，顺势而为，让每一天好运连连，称心如意。',
        ];

        return ApiResult::success($this->result);
    }

    /**
     * 五行穿衣
     * @param string $rzwx 日支五行
     * @return array
     */
    protected function getWxcy(string $rzwx): array
    {
        // 五行对应颜色表
        $wxdyysb = [
            '木' => [
                // 适合颜色
                'ys' => ['藏青', '暗绿', '绿色', '薄荷绿', '淡绿'],
                // HEX色值（16进制色值，用于趋势图表使用）
                'hex' => ['002E63', '004D00', '008000', '98FF98', '90EE90'],
                // 颜色系
                'wxsy' => '绿色系',
            ],
            '水' => [
                'ys' => ['黑色', '灰色', '象牙黑', '午夜蓝', '优雅黑'],
                'hex' => ['000000', '808080', '292421', '171E4A', '1A1A1A'],
                'wxsy' => '黑色系',
            ],
            '火' => [
                'ys' => ['紫色', '红色', '粉色', '橙红色', '深粉红'],
                'hex' => ['8C4797', 'DC133D', 'F27F7F', 'FE0000', 'FF1494'],
                'wxsy' => '红色系',
            ],
            '土' => [
                'ys' => ['金黄', '棕色', '黄铜色', '浅奶油黄', '淡黄色'],
                'hex' => ['FDD800', '703B11', 'B29A36', 'FFF3C1', 'F0E78C'],
                'wxsy' => '黄色系',
            ],
            '金' => [
                'ys' => ['浅灰', '白色', '银色', '雪白色', '米白色'],
                'hex' => ['EFEFEF', 'FFFFFF', 'E6E6E6', 'EEF0EF', 'F5F5DC'],
                'wxsy' => '白色系',
            ],
        ];
        // 五行关系对应结果表
        $wxgyjgb = [
            '生' => [
                // 等级
                'dj' => '大吉',
                // 等级评语
                'djpy' => '贵人色',
                // 趋势等级
                'qsdj' => '吉',
                // 单色等级
                'dsdj' => '佳',
                // 解释
                'js' => [
                    '五行相生，为贵人色，即大环境生你的意思，易招贵人。易获扶助异性缘会比平日增加，与整体环境磁场合一圆融。',
                    '被当日五行生。寓意容易得到贵人的帮助，事事顺心如意。人缘和异性缘也会变得非常好对身边的人来说显得格外有魅力。可以借助五行的影响，充分发挥自己的才能。',
                ],
            ],
            '扶' => [
                'dj' => '次吉',
                'djpy' => '合作色',
                'qsdj' => '次吉',
                'dsdj' => '合作',
                'js' => [
                    '五行相符，为幸运色，与他人合作利益时建议穿这类型颜色，如商务沟通、谈判等。',
                    '与当日五行同。寓意幸运眷顾，做事顺利，有助于合作和谈判的进行，实现共赢。这是一个很好的机会，不要犹豫，勇敢迈出你的步伐相信会有好的结果。',
                ],
            ],
            '克' => [
                'dj' => '平平',
                'djpy' => '招财色',
                'qsdj' => '平',
                'dsdj' => '一般',
                'js' => [
                    '五行相克，为奋斗进财色，你需要付出更多的努力，做事会较累，可以通过自身努力调节带旺，如果成功能得到较大的收获。',
                    '克当日五行。寓意辛勤努力会有回报，只要我们勤奋付出，就会得到丰厚的收获，有利于求财。不要放弃，坚持下去，你会看到自己的努力带来的成果。',
                ],
            ],
            '泄' => [
                'dj' => '慎用',
                'djpy' => '消耗色',
                'qsdj' => '较忌',
                'dsdj' => '较差',
                'js' => [
                    '生当日五行，即你要去生大环境的意思，当然万事会较累，适合内心强大的人挑战下。',
                    '生当日五行。寓意消耗过大，易导致精力不济。你需要提醒自己，避免因为精力不足而发生失误。适当休息，补充能量，做好调整，才能保持高效和准确。',
                ],
            ],
            '耗' => [
                'dj' => '忌用',
                'djpy' => '不利色',
                'qsdj' => '最忌',
                'dsdj' => '差',
                'js' => [
                    '即大环境克你，办事成效差，易导致运势低下，且易出现事倍功半徒劳无功之事发生。',
                    '被当日五行克。寓意面临重重困难和阻碍致进展缓慢，事倍功半。不要气馁，可以做些调整，静待时机，也可以向有智慧的人请教找到解决问题的方法。',
                ],
            ],
        ];
        // 五行关系作用表
        $wxgxzyb = [
            '木' => [
                '扶' => '木', '生' => '火', '克' => '土', '耗' => '金', '泄' => '水',
            ],
            '火' => [
                '泄' => '木', '扶' => '火', '生' => '土', '克' => '金', '耗' => '水',
            ],
            '土' => [
                '耗' => '木', '泄' => '火', '扶' => '土', '生' => '金', '克' => '水',
            ],
            '金' => [
                '克' => '木', '耗' => '火', '泄' => '土', '扶' => '金', '生' => '水',
            ],
            '水' => [
                '生' => '木', '克' => '火', '耗' => '土', '泄' => '金', '扶' => '水',
            ],
        ];
        $result = [];
        foreach ($wxgxzyb[$rzwx] as $name => $wxName) {
            $result[$name] = [
                'name' => $name,
                // 五行
                'wx' => $wxName,
                // 颜色系
                'wxsy' => $wxdyysb[$wxName]['wxsy'],
                // 适合颜色
                'ys' => $wxdyysb[$wxName]['ys'],
                // HEX色值
                'hex' => $wxdyysb[$wxName]['hex'],
                // 等级评语
                'djpy' => $wxgyjgb[$name]['djpy'],
                // 趋势等级
                'qsdj' => $wxgyjgb[$name]['qsdj'],
                // 单色等级
                'dsdj' => $wxgyjgb[$name]['dsdj'],
                // 解释
                'js' => $wxgyjgb[$name]['js'],
            ];
        }
        // 排序顺序
        $order = ['生', '扶', '克', '泄', '耗'];
        $orderedArr = [];
        // 按顺序筛选存在的键
        foreach ($order as $key) {
            if (isset($result[$key])) {
                $orderedArr[$key] = $result[$key];
                // 移除已处理键
                unset($result[$key]);
            }
        }
        // 合并剩余未在顺序中的键（可选）
        $orderedArr += $result;

        $result = array_values($orderedArr);
        return $result;
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian 纪年
     * @return array
     */
    protected function getJnwx(array $jiNian): array
    {
        $result = [];
        // 干支对应的五行
        $wuXingAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        return $result;
    }

    /**
     * 合害
     * @param string $dz 日地支
     * @return array
     */
    protected function getHeHai(string $dz): array
    {
        // 合 冲 破 害
        $list = [
            '子' => ['丑', '午', '酉', '未'], '丑' => ['子', '未', '辰', '午'], '寅' => ['亥', '申', '亥', '巳'], '卯' => ['戌', '酉', '午', '辰'],
            '辰' => ['酉', '戌', '丑', '卯'], '巳' => ['申', '亥', '申', '寅'], '午' => ['未', '子', '卯', '丑'], '未' => ['午', '丑', '戌', '子'],
            '申' => ['巳', '寅', '巳', '亥'], '酉' => ['辰', '卯', '子', '戌'], '戌' => ['卯', '辰', '未', '酉'], '亥' => ['寅', '巳', '寅', '申'],
        ];
        $data = $list[$dz];
        $sanHe = $this->getSanHeDz($dz);
        $xing = BaziCommon::getXingByDz($dz);
        $animals = array_combine(Calendar::DI_ZHI, Calendar::C_ZODIAC);
        $result = [
            'liu_he' => $animals[$data[0]],
            'liu_he_explain' => "今日与生肖{$animals[$data[0]]}六合。六合是一种暗合，该生肖是暗中帮助你的贵人。",
            'chong' => $animals[$data[1]],
            'po' => $animals[$data[2]],
            'hai' => $animals[$data[3]],
            'xing' => [],
            'san_he' => [
                $animals[$sanHe[0]], $animals[$sanHe[1]],
            ],
            'san_he_explain' => "今日与生肖{$animals[$sanHe[0]]}、{$animals[$sanHe[1]]}三合。三合是一种明合，光明正大地相合帮助你。",
        ];
        foreach ($xing as $v) {
            $result['xing'][] = $animals[$v];
        }
        return $result;
    }

    /**
     * 根据地支获得三合地支
     * @param string $dz 地支
     * @return string[]
     */
    protected function getSanHeDz(string $dz): array
    {
        $list = [
            '子' => ['辰', '申'], '丑' => ['巳', '酉'], '寅' => ['午', '戌'], '卯' => ['亥', '未'], '辰' => ['子', '申'], '巳' => ['酉', '丑'],
            '午' => ['寅', '戌'], '未' => ['亥', '卯'], '申' => ['辰', '子'], '酉' => ['巳', '丑'], '戌' => ['午', '寅'], '亥' => ['卯', '未'],
        ];
        return $list[$dz];
    }

    /**
     * 获得农历天数
     * @param int $y 农历年份
     * @return int
     * @throws Exception
     */
    protected function getNongliDays(int $y): int
    {
        $firstDay = $this->getLunarFirstDay($y);
        $firstDay2 = $this->getLunarFirstDay($y + 1);;
        if ($firstDay && $firstDay2) {
            $end = strtotime($firstDay2['gongli']);
            $start = strtotime($firstDay['gongli']);
            return (int)(($end - $start) / 86400);
        }
        return 0;
    }

    /**
     * 指定农历年分的正月初一对应的 公历和八字
     * @param int $y 农历年份
     * @return array
     * @throws Exception
     */
    protected function getLunarFirstDay(int $y): array
    {
        $calendar = new Calendar();
        $gongli = $calendar->lunar2Solar($y, 1, 1, false);
        if (false === $gongli) {
            return [];
        }
        $time = implode('-', $gongli);
        $lunar = Lunar::date($time);
        $jiNian = $lunar->getLunarTganDzhi();
        unset($jiNian['h']);
        $data = [
            'gongli' => $time,
            'jinian' => $jiNian,
        ];
        return $data;
    }
}
