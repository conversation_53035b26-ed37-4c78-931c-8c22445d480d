<?php
// +----------------------------------------------------------------------
// | Yiji 宜忌
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1\qyapp;

use api\ApiResult;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\traits\ArrayMove;
use think\facade\Request;
use think\facade\Validate;

class Yiji
{
    use ArrayMove;

    /**
     * 结果
     * @var array
     */
    protected array $result = [];

    /**
     * 宜忌名词白话解释
     * @var array|string[]
     */
    protected array $yijimcbhjsb = [
        '祭祀' => '宗教或传统仪式中向神灵或祖先献礼。',
        '入学' => '古时指入学堂，现代可用于应聘中的报名，进入学校学习，开学典礼。',
        '沐浴' => '洗澡，清洁身体。',
        '诸事不宜' => '不适合做任何重要的事情。',
        '会亲友' => '与朋友或亲戚相聚。',
        '订婚' => '古时指:缔结婚姻的仪式（同纳采）可受授聘金，可用于合婚订婚。',
        '纳采' => '古代婚礼中的一环，男方送礼物给女方家表示求婚。',
        '问名' => '古代婚礼中的一环，男方询问女方的姓名和生辰八字。',
        '解除' => '消除不幸或灾难，恢复常态。',
        '裁衣' => '指婚前准备新衣、钗环等。古时临近婚期前一个月，男方还要备酒、蓝布和金银钗环交给媒人送到女方家，谓之裁衣。现指逛街买新衣服。',
        '竖柱' => '建筑中立起柱子。',
        '上梁' => '建筑中安装屋顶的主梁。',
        '立券' => '订立契约、签合同等商业、法律行为。',
        '交易' => '买卖商品或服务，订立各种契约互相买卖之事,古时候做买卖都要写字据的，如买房有房契。买地有地契。把自己的权利通过契约这种方式写下来就叫做立券。',
        '纳财' => '指购置产业、进货、收帐、收租、讨债、贷款、收入钱财、财富增加、五谷入仓等。',
        '出货财' => '出售或分发财物。',
        '牧养' => '饲养牲畜，管理牧场。',
        '纳畜' => '买入或收养牲畜。',
        '安葬' => '埋葬死者，举行葬礼。',
        '启攒' => '打开坟墓，通常用于迁葬或祭祀。',
        '祈福' => '祈求神明降福或设醮还愿之事。',
        '求嗣' => '祈求子孙后代，延续香火。',
        '上册受封' => '接受册封，获得荣誉或地位。',
        '斋醮' => '道教中的祭祀仪式，祈求神灵保佑。',
        '旅游' => '外出游玩，观光游览。',
        '出行' => '指外出旅行、观光游览。',
        '上官赴任' => '去新职位上任，开始工作。',
        '领证' => '领取证件，如结婚证、驾驶证等。',
        '结婚' => '男娶女嫁，举行结婚大典的吉日，男女双方正式结为夫妻。',
        '嫁娶' => '同“结婚”，指男女婚配。',
        '搬家' => '黄历中的“移徙”，指搬家，迁移住所，搬迁入二手房、租房。',
        '移徙' => '同“搬家”，指迁移住所。',
        '入宅' => '搬入新居，开始居住。',
        '整手足甲' => '修剪手指甲和脚趾甲。',
        '求医疗病' => '寻求医疗帮助，治疗疾病。',
        '栽种' => '种植植物，如树木、花草等。',
        '装修' => '指阳宅(即住房)之建造与修理，修缮和装饰房屋。',
        '修造' => '修建或制造，如建造房屋、制作家具等。',
        '动土' => '建筑中开始挖掘土地，进行基础建设。',
        '修仓库' => '修缮仓库，保持其良好状态。',
        '扫舍宇' => '清扫房屋，保持整洁。',
        '经络' => '古时指安纺车、织布机，现泛指买车交车、安机器等事项。',
        '冠笄' => '古代男子二十岁时举行的成人礼，女子十五岁时举行的及笄礼。',
        '进人口' => '增添人口，如添丁进口。现代指收纳养子养女等。',
        '酝酿' => '酿酒或准备计划。',
        '开业' => '开始营业，如商店、公司等。',
        '开张' => '开业之意。商店行号开张做生意。同“开幕礼“开工”。包括:年头初开始营业或开工等事;新设店铺商行或新厂开幕等事。',
        '开工' => '开始工作，如工厂、工地等。',
        '开市' => '市场开始营业，进行买卖活动。',
        '捕捉' => '捕获动物或捉拿罪犯。',
        '破屋坏垣' => '拆除破旧的房屋或围墙。',
        '剃头' => '古时指初生婴儿剃胎头或削发出家。现代可用于日常的理发、做发型。',
        '取鱼' => '捕鱼或钓鱼。',
        '修置产室' => '黄历中的“修置产室”，指购买建成的房子。',
        '开渠' => '挖掘渠道，如灌溉渠、排水渠等。',
        '穿井' => '挖掘水井。',
        '安碓硙' => '安装或修缮碾盘和磨盘等农具。',
        '筑堤防' => '修建堤坝，防止水灾。',
        '安床' => '安置床铺，通常指新婚夫妇的床铺。',
        '补垣塞穴' => '修补墙壁和堵塞洞穴。',
        '修饰垣墙' => '修缮和装饰墙壁。',
        '大事勿用' => '这一天不适合做重要的事情。',
        '鼓铸' => '铸造钱币或器物。',
        '畋猎' => '打猎，捕获野兽。',
        '伐木' => '砍伐树木。',
        '乘船' => '乘坐船只出行。',
        '渡水' => '渡过河流或水域。',
        '苫盖' => '用草席或布等覆盖物体，以防风雨。',
        '远回' => '从远处返回,归乡等。',
        '疗目' => '治疗眼睛疾病。',
        '针刺' => '用针进行医疗治疗，如针灸。',
        '掘井' => '挖掘水井。',
    ];

    /**
     * 宜忌
     * @return ApiResult
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 开始时间-公历
            'start' => Request::param('start', date('Y-m-d'), 'trim'),
            // 结束时间-公历
            'end' => Request::param('end', date('Y-m-d', strtotime('+30 day')), 'trim'),
            // 宜忌名词
            'text' => Request::param('text', '', 'trim'),
            // 判断是否只显示周末 0为全部
            'is_weekend' => Request::param('is_weekend', 0, 'intval'),
            // 宜忌选择 0为宜 其他为忌
            'yj' => Request::param('yj', 0, 'intval'),
            // 手机当前时间-公历
            'stime' => Request::param('stime', date('Y-m-d'), 'trim'),
        ];
        $validate = Validate::rule(
            [
                'start|开始时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '开始时间超出范围';
                    }
                    return true;
                }],
                'end|结束时间' => ['require', 'dateFormat:Y-m-d', function ($times) {
                    $times = strtotime($times);
                    if ($times < -2209017600 || $times > 4102333200) {
                        return '结束时间超出范围';
                    }
                    return true;
                }],
                'text|类型' => ['chs', function ($value) {
                    if (isset($this->yijimcbhjsb[$value])) {
                        return true;
                    }
                    return "该类型不存在:{$value}";
                }],
                'stime|手机时间' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }
        // 判断结束时间是否合法
        $start = strtotime($data['start']);
        $end = strtotime($data['end']);
        $stime = strtotime($data['stime']);
        if ($end <= $start) {
            return ApiResult::error('结束时间不能小于开始时间');
        }
        // 开始和结束时间最大间隔
        $diff = (int)(($end - $start) / 86400);
        if ($diff > 180) {
            return ApiResult::error('时间最长为180天');
        }
        // 时支生肖相冲表
        $szsxxcb = [
            '子' => '冲马', '丑' => '冲羊', '寅' => '冲猴', '卯' => '冲鸡', '辰' => '冲狗', '巳' => '冲猪',
            '午' => '冲鼠', '未' => '冲牛', '申' => '冲虎', '酉' => '冲兔', '戌' => '冲龙', '亥' => '冲蛇',
        ];
        // 结果
        $this->result = [
            // 宜忌白话解释
            'title' => $this->yijimcbhjsb[$data['text']] ?? '',
        ];

        // 循环
        $dayList = [];
        for ($i = 0; $i <= $diff; $i++) {
            // 日期
            $nextDate = strtotime("+{$i} day", $start);
            // 黄历
            $huangli = Huangli::date($nextDate);
            // 吉凶宜忌
            $jiXiong = $huangli->getJiXiong();
            // 八字基础数据
            $base = $huangli->getLunarByBetween();

            // 当日数据
            $dayInfo = [];
            // 公历
            $dayInfo['gongli'] = [
                // 日期
                'y' => (int)$huangli->dateTime->format('Y'),
                'm' => (int)$huangli->dateTime->format('m'),
                'd' => (int)$huangli->dateTime->format('d'),
                'cn_date' => $huangli->dateTime->format('Y年m月d日'),
                // 星期几
                'xqj' => (int)$huangli->dateTime->format('N'),
                'cn_xqj' => Huangli::getWeekChs($huangli->dateTime->getTimestamp()),
            ];
            // 农历
            $dayInfo['nongli'] = $huangli->getNongLi();
            // 生肖
            $dayInfo['nongli']['shengxiao'] = $base['shengxiao'];
            // 纪年
            $dayInfo['jinian'] = $base['jinian'];
            // 星宿
            $dayInfo['xing_su'] = $huangli->getXingSu();
            // 值日
            $dayInfo['zhiri'] = $huangli->getZhiRi();
            // 建除十二神
            $dayInfo['jianchu'] = $huangli->getJianChu();
            // 距离开始时间多少天
            $dayInfo['tian'] = (int)(($nextDate - $stime) / 86400);
            // 日支生肖相冲
            $dayInfo['chong'] = $szsxxcb[$base['jinian']['d'][1]];

            // 宜忌输出判断， 0为宜 其他为忌
            if ($data['yj']) {
                // 忌
                if (in_array($data['text'], $jiXiong['ji'])) {
                    $dayList[] = $dayInfo;
                }
            } else {
                // 宜
                if (in_array($data['text'], $jiXiong['yi'])) {
                    $dayList[] = $dayInfo;
                }
            }
            unset($huangli);
        }
        // 判断是否只显示周末
        if ($data['is_weekend']) {
            $newDay = [];
            foreach ($dayList as $k => $v) {
                if (in_array($v['gongli']['cn_xqj'], ['星期六', '星期日'])) {
                    $newDay[] = $v;
                }
            }
            $dayList = $newDay;
        }
        $this->result['num'] = count($dayList);
        $this->result['day_list'] = $dayList;
        return ApiResult::success($this->result);
    }
}
