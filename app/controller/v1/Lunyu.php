<?php
// +----------------------------------------------------------------------
// |  Lunyu
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\Utils;
use app\lib\WxAttr;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\LunyuMing2;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;
use think\helper\Str;

class Lunyu
{
    /**
     * 论语起名名字列表
     * @return array|array[]
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $param = [
            // 名字id，多个用逗号隔开
            'mings' => input('mings', '', 'trim'),
            // 关键词
            'keyword' => input('keyword', '', 'trim'),
            // 0 男 1 女 2 不限
            'gender' => input('gender', '', 'trim'),
            // 五行
            'wx' => input('wx', '', 'trim'),
            // 空数据时输出默认数据
            'eshow' => input('eshow', 1, 'intval'),
            // 页码
            'page' => input('page', 1, 'intval'),
            // 每页显示数
            'limit' => input('limit', 50, 'intval'),
        ];
        $validate = Validate::rule(
            [
                'keyword|名字' => ['chs'],
                'sex|性别' => ['in:0,1,2'],
            ]
        );
        if (!$validate->check($param)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $param['wx'] = in_array($param['wx'], ['金', '木', '水', '火', '土']) ? $param['wx'] : '';
        $query = $this->getQuery($param);
        $order = ['jingxuan' => 'desc', 'listorder' => 'asc', 'gender' => 'asc'];
        $count = (clone $query)->cache(300)->count('id');
        $list = $query->with('detail')
            ->page($param['page'])
            ->limit($param['limit'])
            ->order($order)
            ->cache(300)
            ->select();
        if ($list->isEmpty() && $param['eshow']) {
            // 无数据时默认加载10条数据
            $param['mings'] = '';
            $param['keyword'] = '';
            $param['wx'] = '';
            $query = $this->getQuery($param);
            $list = $query->with('detail')
                ->page($param['page'])
                ->order($order)
                ->limit(10)
                ->cache(300)
                ->select();
        }
        $strZi = implode('', $list->column('ming'));
        $listZi = array_values(array_unique(Utils::mbStrSplit($strZi)));
        $listZiInfo = Cnword::getInfo2($listZi);
        $list1 = [];
        foreach ($list as $info) {
            $ziArr = Utils::mbStrSplit(trim($info['ming']));
            $arr = [];
            $arrEx = [];
            foreach ($ziArr as $zi) {
                if (empty($zi)) {
                    continue;
                }
                if (!isset($listZiInfo[$zi])) {
                    $arr[] = [
                        'zi' => $zi,
                        'big5' => '',
                        'bihua' => '',
                        'bihua2' => '',
                        'bushou' => '',
                        'jiegou' => '',
                        'py' => '',
                        'py2' => '',
                        'wx' => '',
                    ];
                    continue;
                }
                $tmpZi = $listZiInfo[$zi];
                $tmpZi['jiegou'] = Cnword::$listJieGou[$tmpZi['jiegou']] ?? $tmpZi['jiegou'];
                $tmpZi['py'] = $this->getOnePy($tmpZi['py']);
                $tmpZi['py2'] = $this->getOnePy($tmpZi['py2']);
                $arrEx[$zi] = [
                    'zi' => $zi,
                    'info' => $tmpZi['detail3']['mean'] ?? '',
                ];
                unset($tmpZi['zi2'], $tmpZi['detail3'], $tmpZi['detail']);
                $arr[] = $tmpZi;
            }
            $tmp = $info->hidden(['create_time', 'update_time'])->toArray();
            $tmp['ziArr'] = $arr;
            $tmp['ziExplain'] = array_values($arrEx);
            $tmp['detail'] = $this->dealDetail($tmp['detail'], $ziArr);
            //$tmp['source'] = $this->getStrongHtml($tmp['source'], $ziArr);
            $list1[] = $tmp;
        }
        return [
            'data' => [
                'count' => $count,
                'data' => $list1,
            ],
        ];
    }

    /**
     * 搜索所要的数据
     * @return array|array[]
     */
    public function search()
    {
        $data = [
            //关键词
            'keyword' => input('keyword', '', 'trim'),
            //0 男 1 女 2 不限
            'gender' => input('gender', '', 'trim'),
            //五行
            'wx' => input('wx', '', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'keyword|关键词' => ['chs'],
                'gender|性别' => ['in:0,1,2'],
                'wx|五行' => ['chs', 'in:金,木,水,火,土'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $query = LunyuMing2::where(1);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {

                case 'gender':
                    if (in_array($value, [0, 1])) {
                        $query->where('gender', $value);
                    }
                    break;
                case 'keyword':
                    $cnArr = Utils::mbStrSplit($value);
                    $query->where(function ($query1) use ($cnArr) {
                        foreach ($cnArr as $v) {
                            $query1->whereOr('ming', 'like', "%{$v}%");
                        }
                    });
                    break;
                case 'wx':
                    $wxNum = WxAttr::getWxToNum($value);
                    $query->whereLike('wx', "%{$wxNum}%");
                    break;
            }
        };
        foreach ($data as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        $count = $query->count('id');
        return [
            'data' => [
                'count' => $count,
            ],
        ];
    }

    /**
     * 获得查询条件
     * @param array $param
     * @return LunyuMing2
     */
    protected function getQuery(array $param)
    {
        $query = LunyuMing2::where(1);
        $fun = function ($key, $value) use ($query) {
            switch ($key) {
                case 'mings':
                    $query->whereIn('id', explode(',', $value));
                    break;
                case 'gender':
                    if (in_array($value, [0, 1])) {
                        $query->where('gender', $value);
                    }
                    break;
                case 'keyword':
                    $query->whereLike('ming', "%{$value}%");
                    break;
                case 'wx':
                    $wxNum = WxAttr::getWxToNum($value);
                    $query->whereLike('wx', "%{$wxNum}%");
                    break;
            }
        };
        foreach ($param as $key => $value) {
            if ($value != '') {
                $fun($key, $value);
            }
        }
        return $query;
    }

    /**
     * 根据字段输出html
     * @param string $str
     * @param array $ziArr
     * @return array|string|string[]
     */
    protected function getStrongHtml(string $str, array $ziArr)
    {
        $ziArr = array_unique($ziArr);
        foreach ($ziArr as $v) {
            $str = str_replace($v, "<strong>{$v}</strong>", $str);
        }
        return $str;
    }

    /**
     * 获得拼音
     * @param string $str
     * @return string
     */
    protected function getOnePy(string $str): string
    {
        if (empty($str)) {
            return '';
        }
        $str = str_replace(['，', ' ', '、'], ',', $str);
        $pyArr = explode(',', $str);
        return $pyArr[0];
    }

    /**
     * 处理详情
     * @param array $list
     * @param array $ziArr
     * @return array
     */
    protected function dealDetail(array $list, array $ziArr)
    {
        foreach ($list as &$info) {
            if (!Str::contains($info['source'], '<strong>')) {
                // 该字段没有加粗标签时，加粗指定文字
                $info['source'] = $this->getStrongHtml($info['source'], $ziArr);
            }
            unset($info['id'], $info['mid']);
        }
        return $list;
    }
}
