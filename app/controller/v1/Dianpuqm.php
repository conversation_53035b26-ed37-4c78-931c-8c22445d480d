<?php
// +----------------------------------------------------------------------
// | 店铺取名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\qumingdafen\Bihua;
use app\lib\Utils;
use app\model\baobaoqm\Cnword;
use app\model\dianpu\Shop;
use app\model\dianpu\ShopCat;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;
use Overtrue\Pinyin\Pinyin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

class Dianpuqm
{
    /**
     * 行业数据
     * @var array
     */
    protected array $industryData;

    /**
     * 传入的数据
     * @var array
     */
    protected array $parmData = [];

    /**
     * 用户生日类
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 喜用忌闲仇
     * @var array
     */
    protected array $xy = [];

    /**
     * 用字信息
     * @var array
     */
    protected array $arrZi = [];

    /**
     * 店铺取名
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 用户性别 0 男 1 女
            'sex' => input('sex', 0, 'intval'),
            // 生日
            'time' => input('time', '', 'trim'),
            // 城市
            'city' => input('city', '', 'trim'),
            // 行业
            'industry' => input('industry', '', 'trim'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 名字数
            'total' => input('total', 20, 'intval'),
            // 取名字规则
            'type' => input('type', 23, 'intval'),
            // 名字
            'list' => input('list', '', 'trim'),
            // 喜用忌闲仇
            'xy' => input('xy', '', 'trim'),
            // 日元旺弱
            'ry' => input('ry', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'sex|性别' => ['require', 'in:0,1'],
                'time|用户生日' => ['require', 'isDateOrTime:用户生日'],
                'city|城市' => ['chs'],
                'industry|行业' => ['require', 'number'],
                'otime|订单时间' => ['require', 'date'],
                'total|名字数' => ['require', 'number'],
                'type|取名方案' => ['require', 'in:2,3,23'],
            ]
        );
        // 校验
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->parmData = $data;
        $shopCat = ShopCat::where('id', '=', $data['industry'])
            ->cache(300)
            ->find();
        if (empty($shopCat)) {
            return ['status' => 0, 'msg' => '行业不存在'];
        }
        $this->industryData = $shopCat->append(['wx_num'])->toArray();
        $listIndLike = [
            '金' => ['土', '金'], '木' => ['水', '木'], '水' => ['金', '水'], '火' => ['木', '火'], '土' => ['火', '土'],
        ];
        $this->industryData['like'] = $listIndLike[$shopCat['wx']];
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $baziEx = new BaziEx($this->lunar);
        if ($data['xy']) {
            $arr = preg_split('/(?<!^)(?!$)/u', $data['xy']);
            if (count($arr) == 5) {
                $this->xy = [
                    // 喜用忌闲仇
                    'xi' => $arr[0], 'yong' => $arr[1], 'ji' => $arr[2], 'xian' => $arr[3], 'qiu' => $arr[4],
                ];
            }
        }
        if (empty($this->xy)) {
            $xiY = $baziEx->getXiyongJi3();
            $this->xy = $xiY['xy'];
        } else {
            $xiY = $this->xy;
        }
        return [
            'base' => $base,
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 纪年五行
            'jnwx' => $this->getJnWx($base['jinian']),
            // 喜用忌闲仇
            'xy' => $this->xy,
            'industry' => [
                'title' => $shopCat['title'],
                'wx' => $shopCat['wx'],
                'like' => $this->industryData['like'],
            ],
            // 同类异类
            'wx_fenxi' => $this->getBaziFenXin(),
            // 五行占比
            'wx' => $this->getWxPersent(),
            // 风水布局
            'fenshui' => $this->feipan($data['otime']),
            // 名字列表
            'names' => $this->getNameList(),
        ];
    }

    /**
     * 获得分类信息
     * @return array
     */
    public function cat(): array
    {
        // 0时获得最新数据
        $re = input('re', 1, 'intval');
        $bool = (bool)$re;
        return ShopCat::getLists($bool);
    }

    /**
     * 名字列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNameList(): array
    {
        $parmData = $this->parmData;
        // 查询用的名字五行五行
        $nWx = $this->getNameUsedWx();
        $time1 = (int)$this->lunar->dateTime->format('YmdH');
        $otime = strtotime($parmData['otime']);
        $timeNum = (int)date('YmdHi', $otime) + $time1;

        $total = (int)$parmData['total'];
        $strName = $this->parmData['list'];
        $hanWxNum = $this->industryData['wx_num'];

        $namePlan = $parmData['type'];
        $list = [];
        $numToWx = array_flip(Shop::$wxToNum);
        $result = [];
        if (!empty($strName)) {
            $wxUsed = $nWx[$namePlan];
            $arr = explode(',', $strName);
            $list = Shop::where('hanwx', '=', $hanWxNum)
                ->where('wx', 'in', $wxUsed)
                ->where('ming', 'in', $arr)
                ->cache(600)
                ->limit($total)
                ->column('id,wx,ming');
            $this->arrZi = $this->getZiArr(array_column($list, 'ming'));
            $list = array_column($list, null, 'ming');
            foreach ($arr as $v) {
                if (!isset($list[$v])) {
                    continue;
                }
                $tmpZiArr = preg_split('/(?<!^)(?!$)/u', $v);
                $tmpWxNum2 = preg_split('/(?<!^)(?!$)/u', $list[$v]['wx']);
                $tmp4 = [];
                foreach ($tmpZiArr as $k4 => $v4) {
                    $tmpWxN = $tmpWxNum2[$k4] ?? 0;
                    if ($tmpWxN < 1) {
                        $tmp4[] = [
                            'zi' => $v4, 'wx' => '',
                        ];
                    } else {
                        $tmp4[] = [
                            'zi' => $v4, 'wx' => $numToWx[$tmpWxNum2[$k4]] ?? '',
                        ];
                    }
                }
                $tmpRes = $this->getNameFenXi($tmp4);
                $result[] = $tmpRes;
            }
        } else {
            $totalZi = [];
            switch ($namePlan) {
                case 2:
                case 3:
                    $wxUsed = $nWx[$namePlan];
                    $list = $this->getNlist($hanWxNum, $wxUsed, $total, $timeNum);
                    $list4 = [];
                    foreach ($list as $v) {
                        $nameRes = $this->dealName($v);
                        if ($nameRes['jixian']['luck'] == '凶') {
                            continue;
                        }
                        $list4[] = $nameRes;
                    }
                    // $obj = Collection::make($list4);
                    // $list4 = $obj->order('cai_fen', 'desc')->toArray();
                    $num1 = 0;
                    $resnomal = [];
                    foreach ($list4 as $info) {
                        $bool = true;
                        if ($num1 >= $total) {
                            break;
                        }
                        $tmpArr = [];
                        foreach ($info['zi'] as $v1) {
                            $ziNum = $totalZi[$v1['zi']] ?? 0;
                            if ($ziNum >= $namePlan) {
                                $bool = false;
                                break;
                            }
                            $tmpArr[$v1['zi']] = $ziNum + 1;
                        }
                        if ($bool) {
                            $result[] = $info;
                            $totalZi = array_merge($totalZi, $tmpArr);
                            $num1++;
                        } else {
                            $resnomal[] = $info;
                        }
                    }
                    $result = array_merge($result, $resnomal);
                    break;
                case 23:
                    $total2 = (int)($total / 2);
                    $total3 = $total - $total2;
                    $list2 = $this->getNlist($hanWxNum, $nWx[2], $total2, $timeNum);
                    $list3 = $this->getNlist($hanWxNum, $nWx[3], $total3, $timeNum);
                    $list = array_merge($list2, $list3);
                    $res = [
                        2 => ['good' => [], 'bad' => []], 3 => ['good' => [], 'bad' => []],
                    ];
                    foreach ($list as $v) {
                        $nameRes = $this->dealName($v);
                        if ($nameRes['jixian']['luck'] == '凶') {
                            continue;
                        }
                        $fen = $nameRes['cai_fen'];
                        $number = count($nameRes['zi']);
                        if ($fen < 78) {
                            $res[$number]['bad'][] = $nameRes;
                        } else {
                            $bool = true;
                            $totalZi1 = [];
                            foreach ($nameRes['zi'] as $v1) {
                                $ziNum = $totalZi[$v1['zi']] ?? 0;
                                if ($ziNum >= 2) {
                                    $bool = false;
                                    break;
                                }
                                $totalZi1[$v1['zi']] = $ziNum + 1;
                            }
                            $gNum = $res[$number]['good'] ? count($res[$number]['good']) : 0;
                            $gNum2 = count($res[2]['good']);
                            $gNum3 = count($res[3]['good']);
                            if ($gNum2 >= $total2 && $gNum3 >= $total3) {
                                break;
                            }
                            $tmpNum11 = ($number == 2 ? $total2 : $total3);
                            if ($bool) {
                                if ($gNum >= $tmpNum11) {
                                    continue;
                                }
                                $totalZi = array_merge($totalZi, $totalZi1);
                                $res[$number]['good'][] = $nameRes;
                            } else {
                                $res[$number]['bad'][] = $nameRes;
                            }
                        }
                    }
                    $res2 = array_merge($res[2]['good'], $res[2]['bad']);
                    $res2 = array_slice($res2, 0, $total2);
                    $res3 = array_merge($res[3]['good'], $res[3]['bad']);
                    $res3 = array_slice($res3, 0, $total3);
                    $result = array_merge($res2, $res3);
                    break;
            }
        }
        return array_slice($result, 0, $total);
    }

    /**
     * 处理查到的名字返回名字分析
     * @param array $array
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function dealName(array $array): array
    {
        $tmpZiArr = preg_split('/(?<!^)(?!$)/u', $array['ming']);
        $tmpWxNum2 = preg_split('/(?<!^)(?!$)/u', $array['wx']);
        $tmp4 = [];
        foreach ($tmpZiArr as $k4 => $v4) {
            $tmpWxN = $tmpWxNum2[$k4] ?? 0;
            if ($tmpWxN < 1) {
                $tmp4[] = [
                    'zi' => $v4, 'wx' => '',
                ];
            } else {
                $tmp4[] = [
                    'zi' => $v4, 'wx' => $numToWx[$tmpWxNum2[$k4]] ?? '',
                ];
            }
        }
        return $this->getNameFenXi($tmp4);
    }

    /**
     * 名字解析
     * @param array $name
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNameFenXi(array $name = []): array
    {
        // 行业五行
        $indWx = $this->industryData['wx'];
        $ziInfo = [];
        $bihuaArr = [];
        $modelBihua = new Bihua();
        foreach ($name as $v) {
            $zi = $v['zi'];
            $wx = $v['wx'];
            $tmpData = $this->arrZi[$zi] ?? Cnword::info($zi);
            if (empty($wx)) {
                $wx = $tmpData['wx'] ?: $indWx;
            }
            if ($tmpData) {
                $tmpData1 = [
                    'zi' => $zi,
                    'py' => $tmpData['py2'] ?? '',
                    'wx' => $wx,
                    'detail' => $tmpData['detail'] ?? '',
                    'mean' => $tmpData['detail3']['mean'] ?? '',
                    'bihua' => $tmpData['bihua2'] ?? 10,
                ];
            } else {
                $tmpBiHua = (int)$modelBihua->find($v);
                $tmpBiHua = $tmpBiHua > 0 ? $tmpBiHua : 10;
                $tmpData1 = [
                    'zi' => $zi,
                    'py' => Pinyin::sentence($v, 'none')->join(''),
                    'wx' => $wx,
                    'detail' => '',
                    'mean' => '',
                    'bihua' => $tmpBiHua > 0 ? $tmpBiHua : 10,
                ];
            }
            $ziInfo[] = $tmpData1;
            $bihuaArr[] = (int)($tmpData1['bihua'] ?? 10);
        }
        $result = $this->getGuaInfo($bihuaArr);
        $result['zi'] = $ziInfo;
        $result['name'] = implode('', array_column($name, 'zi'));

        $ziWx1 = $ziInfo[0]['wx'];
        $ziWx2 = $ziInfo[1]['wx'];
        // 公司名字库中第一个字和第二个字的五行和行业所属五行相同，则输出100分。
        // 公司名字库中第一个字的五行和行业所属五行相同但第二个字不相同，则输出98分。
        // 公司名字库中第一个字的五行和行业所属五行不相同但第二个字的五行相同，则输出96分。
        // 公司名字库中第一个字的五行和行业所属五行不相同且第二个字的五行也不相同，则输出94分。
        if ($ziWx1 === $indWx && $ziWx2 === $indWx) {
            $wenHuaFen = 100;
        } elseif ($ziWx1 === $indWx) {
            $wenHuaFen = 98;
        } elseif ($ziWx2 === $indWx) {
            $wenHuaFen = 96;
        } else {
            $wenHuaFen = 94;
        }
        $result['wenfen'] = $wenHuaFen;
        $fen = ($wenHuaFen + (int)$result['jixian']['fen'] + (int)$result['cai_fen']) / 3;
        $result['fen_all'] = (int)$fen;
        return $result;
    }

    /**
     * 64填关系
     * @param array $biHua
     * @return array
     */
    protected function getGuaInfo($biHua): array
    {
        $list = [
            1 => ['id' => 1, 'gua' => '乾', 'xian' => '111', 'wx' => '金', 'gua2' => '天'],
            2 => ['id' => 2, 'gua' => '兑', 'xian' => '011', 'wx' => '金', 'gua2' => '泽'],
            3 => ['id' => 3, 'gua' => '离', 'xian' => '101', 'wx' => '火', 'gua2' => '火'],
            4 => ['id' => 4, 'gua' => '震', 'xian' => '001', 'wx' => '木', 'gua2' => '雷'],
            5 => ['id' => 5, 'gua' => '巽', 'xian' => '110', 'wx' => '木', 'gua2' => '风'],
            6 => ['id' => 6, 'gua' => '坎', 'xian' => '010', 'wx' => '水', 'gua2' => '水'],
            7 => ['id' => 7, 'gua' => '艮', 'xian' => '100', 'wx' => '土', 'gua2' => '山'],
            8 => ['id' => 8, 'gua' => '坤', 'xian' => '000', 'wx' => '土', 'gua2' => '地'],
        ];
        $listByXian = array_column($list, null, 'xian');
        // 吉祥度
        $list3 = [
            1 => ['info' => '（太极之数）太极之数，万物开泰，生发无穷，利禄亨通。', 'fen' => 100, 'luck' => '大吉'],
            2 => ['info' => '（两仪之数）两仪之数，混沌未开，进退保守，志望难达。', 'fen' => 58, 'luck' => '凶'],
            3 => ['info' => '（三才之数）三才之数，天地人和，大事大业，繁荣昌隆。', 'fen' => 96, 'luck' => '大吉'],
            4 => ['info' => '（四象之数）四象之数，待于生发，谨言慎行，终有善价。', 'fen' => 62, 'luck' => '凶'],
            5 => ['info' => '（五行之数）五行俱权，循环相生，圆通畅达，福祉无穷。', 'fen' => 98, 'luck' => '大吉'],
            6 => ['info' => '（六爻之数）六爻之数，发展变化，天赋美德，吉祥安泰。', 'fen' => 94, 'luck' => '吉'],
            7 => ['info' => '（七政之数）七政之数，精悍严谨，天赋之力，吉星照耀。', 'fen' => 100, 'luck' => '大吉'],
            8 => ['info' => '（八卦之数）八卦之数，乾坎艮震，巽离坤兑，无穷无尽。', 'fen' => 93, 'luck' => '吉'],
            9 => ['info' => '（大成之数）大成之数，吉中有凶。成败难定，不可掌控。', 'fen' => 63, 'luck' => '凶'],
            10 => ['info' => '（终结之数）进退迷茫，前途未卜，或成或败，一念之间。', 'fen' => 59, 'luck' => '凶'],
            11 => ['info' => '（旱苗逢雨）万物更新，调顺发达，恢弘泽世，繁荣富贵。', 'fen' => 98, 'luck' => '大吉'],
            12 => ['info' => '（掘井无泉）命有磨难，有心无力，虽有奋进，志向难酬。', 'fen' => 80, 'luck' => '凶'],
            13 => ['info' => '（春日牡丹）才艺多能，智谋奇略，忍柔当事，鸣奏大功。', 'fen' => 95, 'luck' => '大吉'],
            14 => ['info' => '（静待）人生不顺，多遭挫折，谋事少虑，难有功成。', 'fen' => 54, 'luck' => '凶'],
            15 => ['info' => '（福寿） 福寿圆满，富贵荣誉，涵养雅量，德高望重。', 'fen' => 100, 'luck' => '大吉'],
            16 => ['info' => '（厚重）厚重载德，安富尊荣，财官双美，功成名就。', 'fen' => 94, 'luck' => '吉'],
            17 => ['info' => '（刚强）权威刚强，突破万难，如能容忍，必获成功。', 'fen' => 98, 'luck' => '大吉'],
            18 => ['info' => '（铁镜重磨）权威显达，博得名利，且养柔德，功成名就。', 'fen' => 94, 'luck' => '吉'],
            19 => ['info' => '（多难）风雨欲来，前路有难，阻碍重重，事难有成。', 'fen' => 61, 'luck' => '凶'],
            20 => ['info' => '（屋下藏金）万事有度，时有逢难，进退无度，万事难成。', 'fen' => 63, 'luck' => '凶'],
            21 => ['info' => '（明月中天）光风霁月，万物确立，官运亨通，大搏名利。', 'fen' => 100, 'luck' => '大吉'],
            22 => ['info' => '（秋草逢霜）秋草逢霜，高山挡路，虽为豪杰，多有波折。', 'fen' => 54, 'luck' => '凶'],
            23 => ['info' => '（壮丽）旭日东升，壮丽壮观，权威旺盛，功名荣达。', 'fen' => 96, 'luck' => '大吉'],
            24 => ['info' => '（掘藏得金）家门余庆，金钱丰盈，白手成家，财源广进。', 'fen' => 94, 'luck' => '大吉'],
            25 => ['info' => '（荣俊） 资性英敏，才能奇特，涵养性情，可成大业。　', 'fen' => 93, 'luck' => '吉'],
            26 => ['info' => '（变怪）变化之数，多有磨难，言明律己，尚可成功', 'fen' => 53, 'luck' => '凶'],
            27 => ['info' => '（增长）好高骛远，恐难企及，断其妄想，可有所成。', 'fen' => 59, 'luck' => '凶'],
            28 => ['info' => '（阔水浮萍）偶有困顿，多有惶恐。屏除疑虑，柳暗花明。', 'fen' => 58, 'luck' => '凶'],
            29 => ['info' => '（智谋） 智谋优秀，财力归集，名闻海内，成就大业。　', 'fen' => 98, 'luck' => '大吉'],
            30 => ['info' => '（非运）惊喜参半，若明若暗。量力而行，逢凶化吉。', 'fen' => 57, 'luck' => '凶'],
            31 => ['info' => '（春日花开）智勇得志，博得名利，统领众人，繁荣富贵。', 'fen' => 97, 'luck' => '大吉'],
            32 => ['info' => '（宝马金鞍）侥幸多望，贵人得助，财帛如裕，繁荣至上。', 'fen' => 93, 'luck' => '吉'],
            33 => ['info' => '（旭日升天）旭日升天，鸾凤相会，名闻天下，隆昌至极。', 'fen' => 92, 'luck' => '吉'],
            34 => ['info' => '（辛苦）破家之身，见识短小，辛苦遭逢，灾祸难测。', 'fen' => 54, 'luck' => '凶'],
            35 => ['info' => '（高楼望月）温和平静，智达通畅，文昌技艺，奏功洋洋。', 'fen' => 91, 'luck' => '吉'],
            36 => ['info' => '（波澜重叠）盛衰交加，波澜起伏，侠肝义胆，直待功成。', 'fen' => 52, 'luck' => '凶'],
            37 => ['info' => '（猛虎出林）权威显达，热诚忠信，宜着雅量，终身荣富。', 'fen' => 92, 'luck' => '吉'],
            38 => ['info' => '（磨铁成针）意志薄弱，刻意经营，才识不凡，技艺有成。', 'fen' => 94, 'luck' => '吉'],
            39 => ['info' => '（富贵荣华）富贵荣华，财帛丰盈，暗藏险象，德泽四方。', 'fen' => 96, 'luck' => '吉'],
            40 => ['info' => '（退安）胆大无度，喜好投机，摒弃急功，定有说成。', 'fen' => 51, 'luck' => '凶'],
            41 => ['info' => '（有德）纯阳独秀，德高望重，和顺畅达，博得名利。', 'fen' => 97, 'luck' => '大吉'],
            42 => ['info' => '（寒蝉在柳）博识多能，精通世情，如能专心，尚可成功。', 'fen' => 95, 'luck' => '吉'],
            43 => ['info' => '（财来财去）行到水穷，事有不遂，散财破财，财来财去。', 'fen' => 53, 'luck' => '凶'],
            44 => ['info' => '（烦闷）急功近利，事不顺心，顺其自然，雨后现虹。', 'fen' => 54, 'luck' => '凶'],
            45 => ['info' => '（顺风）新生泰和，顺风扬帆，智谋经纬，富贵繁荣。', 'fen' => 97, 'luck' => '大吉'],
            46 => ['info' => '（浪里淘金）载宝沉舟，浪里淘金，大难尝尽，大功有成。', 'fen' => 56, 'luck' => '凶'],
            47 => ['info' => '（点石成金）花开之象，万事如意，祯祥吉庆，天赋幸福。', 'fen' => 96, 'luck' => '吉'],
            48 => ['info' => '（古松立鹤）智谋兼备，德量荣达，威望成师，洋洋大观。', 'fen' => 96, 'luck' => '吉'],
            49 => ['info' => '（转变）吉临则吉，凶来则凶，转凶为吉，配好三才。', 'fen' => 95, 'luck' => '吉'],
            50 => ['info' => '（小舟入海）成败不定，吉凶难断，苦中夹甜，起伏不定。', 'fen' => 58, 'luck' => '凶'],
            51 => ['info' => '（沉浮）盛衰交加，波澜重叠，如能慎始，必获成功。', 'fen' => 95, 'luck' => '吉'],
            52 => ['info' => '（达眼）卓识达眼，先见之明，智谋超群，名利双收。', 'fen' => 100, 'luck' => '大吉'],
            53 => ['info' => '（曲卷难星）外祥内患，外祸内安，心有难安，贫富难断。', 'fen' => 57, 'luck' => '凶'],
            54 => ['info' => '（石上栽花）缘木求鱼，海中寻针，忧闷繁多，辛苦不绝。', 'fen' => 55, 'luck' => '凶'],
            55 => ['info' => '（善恶）善善得恶，恶恶得善，善恶无度，反受其害。', 'fen' => 54, 'luck' => '凶'],
            56 => ['info' => '（浪里行舟）名利之地，多有障碍，苦难常伴，成事多坚。', 'fen' => 56, 'luck' => '凶'],
            57 => ['info' => '（日照春松）寒雪青松，夜莺吟春，必遭一过，繁荣白事。', 'fen' => 96, 'luck' => '吉'],
            58 => ['info' => '（晚行遇月）沉浮多端，先苦后甜，宽宏扬名，富贵繁荣。', 'fen' => 95, 'luck' => '吉'],
            59 => ['info' => '（寒蝉悲风）苦中寻乐，患中求稳，尚少耐性，多有困顿。', 'fen' => 51, 'luck' => '凶'],
            60 => ['info' => '（无谋）心似浮萍，方向不定，坚定本心，乘风波浪。', 'fen' => 57, 'luck' => '凶'],
            61 => ['info' => '（牡丹芙蓉）牡丹芙蓉，花开富贵，名利双收，定享天赋。', 'fen' => 97, 'luck' => '大吉'],
            62 => ['info' => '（衰败）内忧外患，多有不遂，自乱阵脚，或遭小难。', 'fen' => 56, 'luck' => '凶'],
            63 => ['info' => '（舟归平海）富贵荣华，身心安泰，雨露惠泽，万事亨通。', 'fen' => 96, 'luck' => '吉'],
            64 => ['info' => '（艰难）盛极则衰，内外不定。心若慌乱，成事遭阻。', 'fen' => 58, 'luck' => '凶'],
            65 => ['info' => '（巨流归海）天长地久，家运隆昌，福寿绵长，事事成就。', 'fen' => 97, 'luck' => '大吉'],
            66 => ['info' => '（岩头步马）进退维谷，艰难不堪，等待时机，一飞冲天。', 'fen' => 54, 'luck' => '凶'],
            67 => ['info' => '（顺风通达）天赋幸运，四通八达，家道繁昌，富贵东来。', 'fen' => 97, 'luck' => '吉'],
            68 => ['info' => '（顺风吹帆）智虑周密，集众信达，发明能智，拓展昂进。', 'fen' => 95, 'luck' => '吉'],
            69 => ['info' => '（非业）灾祸需尝，困惑难逃。空乏其身，大任必降。', 'fen' => 57, 'luck' => '凶'],
            70 => ['info' => '（残菊逢霜）屋漏风雨，事有不顺，处境不利，晚运患忧。', 'fen' => 51, 'luck' => '凶'],
            71 => ['info' => '（石上金花）石上金花，内心劳苦，贯彻始终，定可昌隆。', 'fen' => 100, 'luck' => '大吉'],
            72 => ['info' => '（劳苦）荣苦相依，阴云覆月，似水平静，实则波澜。', 'fen' => 52, 'luck' => '凶'],
            73 => ['info' => '（无勇）盛衰交加，徒有高志，天王福祉，终世平安。', 'fen' => 94, 'luck' => '半吉'],
            74 => ['info' => '（残菊经霜）风霜残青，雨打梨花，尚无大智，辛苦亦多。', 'fen' => 58, 'luck' => '凶'],
            75 => ['info' => '（退守）动辄得咎，保守谨慎，虽有吉象，有谋才成。', 'fen' => 56, 'luck' => '凶'],
            76 => ['info' => '（权衡）锦上添花，实吉带凶，看似平顺，实藏险峻。', 'fen' => 54, 'luck' => '凶'],
            77 => ['info' => '（半吉）家庭有悦，福祸相依，能获援护，陷落不幸。', 'fen' => 52, 'luck' => '凶'],
            78 => ['info' => '（中年发达）祸福参半，先天智能，中年发达，晚景欠佳。', 'fen' => 53, 'luck' => '凶'],
            79 => ['info' => '（云头望月）迷雾遮眼，难分虚实，英勇虽有，实则难成。', 'fen' => 58, 'luck' => '凶'],
            80 => ['info' => '（遁吉）辛苦不绝，早入隐遁，安心立命，化凶转吉。', 'fen' => 57, 'luck' => '凶'],
            81 => ['info' => '（万物回春）最吉之数，还本归元，吉祥重叠，富贵尊荣。', 'fen' => 93, 'luck' => '半吉'],
        ];
        $list2 = [
            111111 => [
                'num' => 1,
                'title' => '乾卦',
                'title2' => '乾为天',
                'good' => '上上卦',
                'fen' => 100,
                'star' => 50,
                'info' => '大吉大利，万事如意，心想事成，自有天佑，春风得意，事业如日中天。但阳气已达顶点，盛极必衰，务须提高警惕，小心谨慎。力戒骄傲，冷静处世，心境平和，如是则能充分发挥才智，保证事业成功。',
                'yao' => '111111',
            ],
            0 => [
                'num' => 2,
                'title' => '坤卦',
                'title2' => '坤为地',
                'good' => '上上卦',
                'fen' => 95,
                'star' => 45,
                'info' => '诸项事业可以成功，得到预想的结果，但开始出师不利，为困境所扰。切莫冒险急进，须小心谨言慎行，尤其不可单枪匹马，独断专行。取得朋友的关心和支持最为重要，在他人的合作下，共同完成事业。因此，应注重内心修养，积蓄养德，效法大地，容忍负重，宽厚大度，以直率、方正、含蓄为原则，不得贪功自傲，持之以恒，谋求事业的成功。',
                'yao' => '000000',
            ],
            10001 => [
                'num' => 3,
                'title' => '屯卦',
                'title2' => '水雷屯',
                'good' => '下下卦',
                'fen' => 83,
                'star' => 40,
                'info' => '起初多不利，必知难而进，小心翼翼，勇往直前，灵活机动，可望获得大的成功，时机到来时一定要抓住，却也不得操之太急，且仍有困难，务必有他人相助，故平时应多施恩惠。',
                'yao' => '010001',
            ],
            100010 => [
                'num' => 4,
                'title' => '蒙卦',
                'title2' => '山水蒙',
                'good' => '中下卦',
                'fen' => 50,
                'star' => 30,
                'info' => '事业开始，混乱无序，危机四伏，以勇敢坚毅的行动可以扭转局面。然而必须接受严格教育，培养这种奋发图强的精神。务必脚踏实地，最忌好高骛远，否则会陷入孤立无援的境地。',
                'yao' => '100010',
            ],
            10111 => [
                'num' => 5,
                'title' => '需卦',
                'title2' => '水天需',
                'good' => '中上卦',
                'fen' => 85,
                'star' => 45,
                'info' => '关键在于审时度势，耐心等待，事成于安祥，切勿冒险，欲速不达。自己要充满自信，临危不惧，坚守中正，必可化险为夷。情况有利时，仍得居安思危。',
                'yao' => '010111',
            ],
            111010 => [
                'num' => 6,
                'title' => '讼卦',
                'title2' => '天水讼',
                'good' => '中下卦',
                'fen' => 82,
                'star' => 40,
                'info' => '起初顺利，有利可图，继而受挫，务必警惕，慎之又慎，不得固执已见，极力避免介入诉讼纠纷的争执之中。与其这样，不如退而让人，求得化解，安于正理，可免除意外之灾。陷入争讼，即使获胜，最后还得失去，得不偿失。',
                'yao' => '111010',
            ],
            10 => [
                'num' => 7,
                'title' => '师卦',
                'title2' => '地水师',
                'good' => '中上卦',
                'fen' => 60,
                'star' => 30,
                'info' => '阻力很大，困难很多，处于激烈的竞争状态，必与他人密切合作，谨小慎微，行为果断，切忌盲目妄动，适度即可，注意保全自己。机动灵活，严于律已。从容沉着对付一切，必能成功。',
                'yao' => '000010',
            ],
            10000 => [
                'num' => 8,
                'title' => '比卦',
                'title2' => '水地比',
                'good' => '上上卦',
                'fen' => 100,
                'star' => 50,
                'info' => '顺利能够成功，向前发展，可以得到他人的帮助和辅佐，以诚实、信任的态度去做事。待人宽厚、正直，主动热情，向才德高尚的人士学习，听取建议。',
                'yao' => '010000',
            ],
            110111 => [
                'num' => 9,
                'title' => '小畜卦',
                'title2' => '风天小畜',
                'good' => '下下卦',
                'fen' => 80,
                'star' => 40,
                'info' => '时机尚不成熟，应当耐心而积极地积存力量，切不可冒险行动。遇到挫折不可灰心，鼓起勇气战胜困难，坚持原则，加强团结合作，提前做好各项准备，会有所成。',
                'yao' => '110111',
            ],
            111011 => [
                'num' => 10,
                'title' => '履卦',
                'title2' => '天泽履',
                'good' => '中上卦',
                'fen' => 76,
                'star' => 40,
                'info' => '起初很不顺利，受到种种威胁，若能提高警惕，谨小慎微，脚踏实地，逐个地去克服困难，不改变自己的决心，一定可以达到目的。办事有自知之明，务必量力而行，不可不顾实际，尤其不得逞强，急于求成。',
                'yao' => '111011',
            ],
            111 => [
                'num' => 11,
                'title' => '泰卦',
                'title2' => '地天泰',
                'good' => '中中卦',
                'fen' => 85,
                'star' => 45,
                'info' => '坚持由小而大，循序渐进的原则，事业已达到顺利的境地，更应小心从事，居安思危，积极寻求、开拓新的事业，方可继续前进，若因循守旧，不思进取，必遭失败。',
                'yao' => '000111',
            ],
            111000 => [
                'num' => 12,
                'title' => '否卦',
                'title2' => '天地否',
                'good' => '中中卦',
                'fen' => 57,
                'star' => 30,
                'info' => '陷入逆境，事业处于衰退阶段，多有不顺利。受小人干扰而不得志，爱挫折。应坚持正道，勿与小人合流，团结志同道合者，自保以等待时机。保持自信心，戒慎恐惧，形势定会好转，事业终能成功。',
                'yao' => '111000',
            ],
            111101 => [
                'num' => 13,
                'title' => '同人卦',
                'title2' => '天火同人',
                'good' => '中上卦',
                'fen' => 100,
                'star' => 50,
                'info' => '顺利、平安、尤其是在与他人的合作方面会十分成功，宜广泛开展人际活动，建立广泛的联系，克服狭隘的门户之见，照顾各方面的利益，求大同，存小异，坚持正确的原则，必能成就大事业。',
                'yao' => '111101',
            ],
            101111 => [
                'num' => 14,
                'title' => '大有卦',
                'title2' => '火天大有',
                'good' => '上上卦',
                'fen' => 98,
                'star' => 50,
                'info' => '事业已经取得一定的成就，最忌得意忘形，胡作非为。务必止恶扬善，坚守中止，交往正直的朋友，戒惧谨慎，兢兢业业，真正做到大而不盈，满而不溢，事业可望迈上新的台阶。',
                'yao' => '101111',
            ],
            100 => [
                'num' => 15,
                'title' => '谦卦',
                'title2' => '地山谦',
                'good' => '中中卦',
                'fen' => 87,
                'star' => 45,
                'info' => '尚未被人器重，但因品德高尚，终会为人发现。自己不必有意表现，尤其不可放弃谦虚的美德，埋头苦干，一定会得到他人的帮助，在事业上大有作为。',
                'yao' => '000100',
            ],
            1000 => [
                'num' => 16,
                'title' => '豫卦',
                'title2' => '雷地豫',
                'good' => '中中卦',
                'fen' => 100,
                'star' => 50,
                'info' => '十分顺利，事业可以获得成功，但必须符合实际，顺应潮流，且得自己努力奋斗，树立远大目光，尤其不可因事业的顺利而放松谨慎小心的态度，陷于懒散享乐。否则，必将后悔莫及。',
                'yao' => '001000',
            ],
            11001 => [
                'num' => 17,
                'title' => '随卦',
                'title2' => '泽雷随',
                'good' => '中中卦',
                'fen' => 90,
                'star' => 45,
                'info' => '对社会和人生有正确的估价，重视人际关系，善于同他人合作，事业会很顺利。为了保证取得更进一步的成功，不得贪图小利，向比自己优秀的人学习，择善而从，心存诚信、努力开拓事业。',
                'yao' => '011001',
            ],
            100110 => [
                'num' => 18,
                'title' => '蛊卦',
                'title2' => '山风蛊',
                'good' => '中中卦',
                'fen' => 50,
                'star' => 30,
                'info' => '运气处于不佳状态，或因外界条件，或因个人因素所致。唯有谨慎分析原因，找出弊端，坚决，大胆，不惜冒险，战胜阻力。这时一要头脑冷静，二要向高明的人请教，必能创造业绩。',
                'yao' => '100110',
            ],
            11 => [
                'num' => 19,
                'title' => '临卦',
                'title2' => '地泽临',
                'good' => '中上卦',
                'fen' => 89,
                'star' => 45,
                'info' => '正是成功的极好时机，务必抓紧，不可失去良机。但却不可就此满足，时运会很快消失，一定要从长计议，注意总结经验，团结他人，共同开拓新领域。',
                'yao' => '000011',
            ],
            110000 => [
                'num' => 20,
                'title' => '观卦',
                'title2' => '风地观',
                'good' => '中上卦',
                'fen' => 76,
                'star' => 40,
                'info' => '事业已出现不顺利的迹象，务必谦虚慎重，高瞻远瞩，尤忌短期行为，心胸开阔，注意搞好人际关系，团结他人，不宜轻率行动。必要时不妨投靠德行高的人，以图再度发展。',
                'yao' => '110000',
            ],
            101001 => [
                'num' => 21,
                'title' => '噬嗑卦',
                'title2' => '火雷噬嗑',
                'good' => '上上卦',
                'fen' => 60,
                'star' => 30,
                'info' => '困难与阻力非常大，应以坚强的意志，果敢的行为，公正无私的态度去战胜种种厄运，争取事态好转。为了早日化险为夷，必要时可采取强硬手段，甚至诉诸法律。',
                'yao' => '101001',
            ],
            100101 => [
                'num' => 22,
                'title' => '贲卦',
                'title2' => '山火贲',
                'good' => '中上卦',
                'fen' => 96,
                'star' => 45,
                'info' => '顺利，小有成绩，宜即时总结经验，图谋更大的发展。树立信心，不计较一时的得失。追求实质性的内容，慎重诸事，不得随波逐流，寻求有实力的人物提携自己。',
                'yao' => '100101',
            ],
            100000 => [
                'num' => 23,
                'title' => '剥卦',
                'title2' => '山地剥',
                'good' => '中下卦',
                'fen' => 68,
                'star' => 35,
                'info' => '时运不佳，乃大势所趋，个人只能顺应时势而暂停行动，静观待变，不可冒险，积极创造条件，增强实力，谨慎隐忍，勿与小人同流。时来运转，成就事业，为期不远。',
                'yao' => '100000',
            ],
            1 => [
                'num' => 24,
                'title' => '复卦',
                'title2' => '地雷复',
                'good' => '中中卦',
                'fen' => 87,
                'star' => 45,
                'info' => '已经渡过了困难时期，开始进入积极行动的阶段。但务必抓住时机，当机立断，却不可急躁，且应时常反省个人行动，严于修身，勇往直前，可望成功。',
                'yao' => '000001',
            ],
            111001 => [
                'num' => 25,
                'title' => '无妄卦',
                'title2' => '天雷无妄',
                'good' => '下下卦',
                'fen' => 79,
                'star' => 40,
                'info' => '贵自知之明，从个人实际出发，不抱非分之想，脚踏实地，勤奋努力，检点行为，防意外灾祸。不计较得失，诚心追求，待机而动，事业必成。',
                'yao' => '111001',
            ],
            100111 => [
                'num' => 26,
                'title' => '大畜卦',
                'title2' => '山天大畜',
                'good' => '中上卦',
                'fen' => 84,
                'star' => 40,
                'info' => '务必端正行为，滋养德行。行为应大胆，果断，但需适可而止，注意休整，决不可冒险闯进。宜注意吸收前人的经验教训，有所畏而不行，否则必有危害。再取得成就之后，应适可而止，不可贪图眼前小利而走向反面。',
                'yao' => '100111',
            ],
            100001 => [
                'num' => 27,
                'title' => '颐卦',
                'title2' => '山雷颐',
                'good' => '上上卦',
                'fen' => 78,
                'star' => 40,
                'info' => '凡事得靠个人追求，自己奋斗，而不能依赖他人。更不得用不光彩的手段谋求财物。只要遵循正道，自食其力，慎言敏行，一定会有好的机遇。这时，经过一番艰苦奋斗，能够成功。',
                'yao' => '100001',
            ],
            11110 => [
                'num' => 28,
                'title' => '大过卦',
                'title2' => '泽风大过',
                'good' => '中下卦',
                'fen' => 50,
                'star' => 30,
                'info' => '事业出现隐患，甚至危机四伏，务必十分小心谨慎，唯有坚守中道，以谦虚和悦的手段，胆大心细，求助他人相助。必要时可采取非常手段，不拘常规，予以冒险。',
                'yao' => '011110',
            ],
            10010 => [
                'num' => 29,
                'title' => '坎卦',
                'title2' => '坎为水',
                'good' => '下下卦',
                'fen' => 50,
                'star' => 30,
                'info' => '陷入重重艰难险阻之中，险况丛生。既不得冒险，也不可束手待毙，应以积极态度，努力创造条件，改变处境，化险为夷。务必实心实意，充满信心，不图侥幸，不辞艰险，宜静观待变，运用智慧，突破险境，转危为安。',
                'yao' => '010010',
            ],
            101101 => [
                'num' => 30,
                'title' => '离卦',
                'title2' => '离为火',
                'good' => '中上卦',
                'fen' => 95,
                'star' => 45,
                'info' => '已快进入顶点，盛极而衰，务必总结经验教训，趋善避邪，以顺自养，居危知危，激励志气，切勿妄动。尤应求助中正的人援助，以期重振事业。',
                'yao' => '101101',
            ],
            11100 => [
                'num' => 31,
                'title' => '咸卦',
                'title2' => '泽山咸',
                'good' => '中上卦',
                'fen' => 85,
                'star' => 45,
                'info' => '和为贵，和则万事兴，务以诚感人，以诚待人。广泛交往朋友，谦虚礼让。树立大志向，坚持主见，不可盲目随大流。不利时应安居待机，不可妄动强求。',
                'yao' => '011100',
            ],
            1110 => [
                'num' => 32,
                'title' => '恒卦',
                'title2' => '雷风恒',
                'good' => '中上卦',
                'fen' => 86,
                'star' => 45,
                'info' => '诸事无不成于恒，持之以恒，必有成效。恒乃成功之本。为此，务必动机纯正，守静而勿躁动，不可急于求成，也不可固守死道，应从个人实际出发。最忌人云亦云，最宜随机应变。如此，诸事顺而成。',
                'yao' => '001110',
            ],
            111100 => [
                'num' => 33,
                'title' => '遁卦',
                'title2' => '天山遁',
                'good' => '下下卦',
                'fen' => 72,
                'star' => 35,
                'info' => '时运不佳，应考虑退隐，勿与不正派的人同流合污。但不是消极逃避，而是为保存实力，总结经验，把握时机，待机而出。切忌贸然行事，造成不必要的损失。同时勿为眼前小利禄所诱惑。',
                'yao' => '111100',
            ],
            1111 => [
                'num' => 34,
                'title' => '大壮卦',
                'title2' => '雷天大壮',
                'good' => '中上卦',
                'fen' => 86,
                'star' => 45,
                'info' => '处世凭智不凭力，有勇更有谋，切忌蛮干，该守不守，自取其凶。不可逞强，否则自取凶险。对小人应有防犯，尤其不得忽视小人的奸诈。对事业应勇往直前，但切忌冒进。',
                'yao' => '001111',
            ],
            101000 => [
                'num' => 35,
                'title' => '晋卦',
                'title2' => '火地晋',
                'good' => '中上卦',
                'fen' => 95,
                'star' => 45,
                'info' => '顺利。应遵守正道，迎难而上，克敌制胜，因势利导。树立良好的人际关系，深得人心。全力以赴，不得有丝毫犹豫不决，更忌优柔寡断，而应败不馁，勇往直前。注意和衷共济，共同前进。',
                'yao' => '101000',
            ],
            101 => [
                'num' => 36,
                'title' => '明夷卦',
                'title2' => '地火明夷',
                'good' => '中下卦',
                'fen' => 69,
                'star' => 35,
                'info' => '处于不利的环境，宜心境坦然地处置险恶的条件，增强心理素质，承受各种压力。表面柔顺小心，内心洞察事理，当离去则离去，避免灾祸，脱离危险。防患于未然，坚持逆境中奋斗。',
                'yao' => '000101',
            ],
            110101 => [
                'num' => 37,
                'title' => '家人卦',
                'title2' => '风火家人',
                'good' => '下下卦',
                'fen' => 70,
                'star' => 35,
                'info' => '成功与否取决于家庭的情况。严格治家，防止“后院”起火出现意外事故，这是事业成功的先决条件。夫妇和睦，共同合作，必可脱贫致富。事业应由内而外，循序渐进，持之以恒，而后必有所成。',
                'yao' => '110101',
            ],
            101011 => [
                'num' => 38,
                'title' => '睽卦',
                'title2' => '火泽睽',
                'good' => '下下卦',
                'fen' => 50,
                'star' => 30,
                'info' => '事业开创困难，处境艰辛。如能志同道合，相互信任，目的可以达到。但起初成功的多是小事。小事积累可成大事。事成于协力合作，败于众志相异。应加强团结，委曲以求相通。最忌内部相互猜疑。',
                'yao' => '101011',
            ],
            10100 => [
                'num' => 39,
                'title' => '蹇卦',
                'title2' => '水山蹇',
                'good' => '下下卦',
                'fen' => 73,
                'star' => 35,
                'info' => '诸事开头难，坚持下去，会有好的结果。人生不易，首先得求诸自己，修德养性。志高自信，奋勇进取。逢到困难，得求友人相助，行动不可冒险，必以柔克刚，待机候时，量力而行。',
                'yao' => '010100',
            ],
            1010 => [
                'num' => 40,
                'title' => '解卦',
                'title2' => '雷水解',
                'good' => '中上卦',
                'fen' => 90,
                'star' => 45,
                'info' => '元气初复，宜休养生息，安静平易为佳，宜宽宜和。当准备就绪则应立即行动，争取主动，不可拖廷，所往必有功利，但切勿贪得无厌。克勤克俭，刚柔相济，以柔为主。远避小人，团结君子，相互依托，共成事业。',
                'yao' => '001010',
            ],
            100011 => [
                'num' => 41,
                'title' => '损卦',
                'title2' => '山泽损',
                'good' => '下下卦',
                'fen' => 85,
                'star' => 45,
                'info' => '开拓事业必有投入。投入多少应事先精心算计，力求损益得当。诸事皆应有节度，切忌欺诈、贪婪。天下事克已最难，务严格要求自己，刻苦奋斗，手段灵活，争取事业成功。',
                'yao' => '100011',
            ],
            110001 => [
                'num' => 42,
                'title' => '益卦',
                'title2' => '风雷益',
                'good' => '上上卦',
                'fen' => 90,
                'star' => 45,
                'info' => '大胆投入，勇于支持他人事业，必相得益彰。勇敢前进，敢作敢为。只要心地善良、纯洁、谦虚，事业必定与日俱增，前程无可限量。助人宜即时，要在应急，开拓事业，内部团结一致，人心所向，树立恒心，不怕冒险犯难。',
                'yao' => '110001',
            ],
            11111 => [
                'num' => 43,
                'title' => '夬卦',
                'title2' => '泽天夬',
                'good' => '上上卦',
                'fen' => 88,
                'star' => 45,
                'info' => '处在兴盛阶段，但已孕育着普遍的危险。务必施恩泽给他人，勿居功自傲，主动团结他人，不干冒险的事。时时提高警惕，防止小人的破坏。但不可莽撞，应持和缓的手段去决断小人，避免过犹不及。',
                'yao' => '011111',
            ],
            111110 => [
                'num' => 44,
                'title' => '姤卦',
                'title2' => '天风姤',
                'good' => '上卦',
                'fen' => 75,
                'star' => 40,
                'info' => '合时宜，循时序。但潜在着危机，阴阳不协调。务必依附于强者，多方联系，增强势力，防止小人渔利，争取形势好转。诸事宜及早准备，等待时机，一旦成熟立刻行动。',
                'yao' => '111110',
            ],
            11000 => [
                'num' => 45,
                'title' => '萃卦',
                'title2' => '泽地萃',
                'good' => '中上卦',
                'fen' => 85,
                'star' => 45,
                'info' => '处于平稳状态。既不宜贸然前进，也不必后退，而应以积极的态度努力进修，提高自己，充实个人实力，待机而起，将大有成就。最怕的是停顿不前，否则将会因不合时宜而被淘汰。',
                'yao' => '011000',
            ],
            110 => [
                'num' => 46,
                'title' => '升卦',
                'title2' => '地风升',
                'good' => '上上卦',
                'fen' => 98,
                'star' => 50,
                'info' => '十分顺利，不断发展，依时而进，逐步上升，前程远大，但应逐步发展，切莫因顺利而冒进。谨慎小心，修养德行，追随有德行的前辈，真诚对待事业。',
                'yao' => '000110',
            ],
            11010 => [
                'num' => 47,
                'title' => '困卦',
                'title2' => '泽水困',
                'good' => '中上卦',
                'fen' => 50,
                'star' => 30,
                'info' => '境况十分不佳，遭受到很大的困难。人生面临巨大的考验，如采取不正当的手段，会愈陷愈深。相反，如身陷困逆境地而不失节操，自勉自坚，泰然处之。不失其志。终能成事。',
                'yao' => '011010',
            ],
            10110 => [
                'num' => 48,
                'title' => '井卦',
                'title2' => '水风井',
                'good' => '上上卦',
                'fen' => 80,
                'star' => 40,
                'info' => '处于平稳状态。既不宜贸然前进，也不必后退，而应以积极的态度努力进修，提高自己，充实个人实力，待机而起，将大有成就。最怕的是停顿不前，否则将会因不合时宜而被淘汰。',
                'yao' => '010110',
            ],
            11101 => [
                'num' => 49,
                'title' => '革卦',
                'title2' => '泽火革',
                'good' => '上上卦',
                'fen' => 87,
                'star' => 45,
                'info' => '正处在转折的关键时刻，必须密切注意各种信息，认真思考。首先巩固自己的地位，完善个人的行为。时机成熟后，立即行动，积极进行变革。但应注意动机纯正，手段正当，不保守也不妄进，事业必定发达。',
                'yao' => '011101',
            ],
            101110 => [
                'num' => 50,
                'title' => '鼎卦',
                'title2' => '火风鼎',
                'good' => '中下卦',
                'fen' => 95,
                'star' => 45,
                'info' => '具备开拓事业的各种条件。耳聪目明，头脑冷静，应以端正的态度去为人处世，严于律已，慎终如始，刚柔兼备，与有才德的人合作，勿妄进失度，无往不利。',
                'yao' => '101110',
            ],
            1001 => [
                'num' => 51,
                'title' => '震卦',
                'title2' => '震为雷',
                'good' => '中上卦',
                'fen' => 66,
                'star' => 35,
                'info' => '处于不顺利、甚至面临危险的局面，对此务必提高警惕，有备方可无患，一旦出现突发事件，可以泰然处之，并且可以由此引吸取教训，一方面使损失减少，另一方面可以迅速复原，使事业迈上新的高度。',
                'yao' => '001001',
            ],
            100100 => [
                'num' => 52,
                'title' => '艮卦',
                'title2' => '艮为山',
                'good' => '中下卦',
                'fen' => 75,
                'star' => 40,
                'info' => '在经过一段发展后，应进行调整，暂时停止行动，总结经验教训。不可因贪名利而妄进。自我克制，自我约束，谨言慎行，不要盲目追求他人。经过休整后，在适当时机到来时，再大显身手。',
                'yao' => '100100',
            ],
            110100 => [
                'num' => 53,
                'title' => '渐卦',
                'title2' => '风山渐',
                'good' => '上上卦',
                'fen' => 85,
                'star' => 45,
                'info' => '在经过一个相对静止的时期后，重新开始了事业的新阶段。这时最怕的是急躁冒进，务必循序上进，脚踏实地。同是注意品德修养，以沉着、谦逊的态度对待事业，可无往而不利。',
                'yao' => '110100',
            ],
            1011 => [
                'num' => 54,
                'title' => '归妹卦',
                'title2' => '雷泽归妹',
                'good' => '下下卦',
                'fen' => 80,
                'star' => 40,
                'info' => '正在进入新的阶段，很快将会出现另一番景象。这时首先必须具备远大眼光，识破利弊和吉凶，有备始能无患。其次是坚守纯正的品德和正确的原则。',
                'yao' => '001011',
            ],
            1101 => [
                'num' => 55,
                'title' => '丰卦',
                'title2' => '雷火丰',
                'good' => '上上卦',
                'fen' => 98,
                'star' => 50,
                'info' => '处于十分顺利的鼎盛时期，各方面都很顺利，成就很大。但务必注意开始出现的衰败症状，防微杜渐，严防骄傲自满。同时，不必盲目追求不切实际的事情，全力保持当前状况的延长。',
                'yao' => '001101',
            ],
            101100 => [
                'num' => 56,
                'title' => '旅卦',
                'title2' => '火山旅',
                'good' => '下下卦',
                'fen' => 63,
                'star' => 30,
                'info' => '正处于创业、发展的阶段，各种困难很多。只要能坚持中正的方针，依正道办事，注意调查，处处小心谨慎，并根据情况的变化，采取应变措施，事业可顺利前进。',
                'yao' => '101100',
            ],
            110110 => [
                'num' => 57,
                'title' => '巽卦',
                'title2' => '巽为风',
                'good' => '中上卦',
                'fen' => 75,
                'star' => 40,
                'info' => '正在发展中，应本着勇往直前的精神，战胜一切困难和险阻，奋发向上。万事起头难，开始会受挫折，但在有才德和有力量的人物帮助下，会有理想的结果。',
                'yao' => '110110',
            ],
            11011 => [
                'num' => 58,
                'title' => '兑卦',
                'title2' => '兑为泽',
                'good' => '上上卦',
                'fen' => 100,
                'star' => 50,
                'info' => '由于善长人际关系，能团结他人，获得援助。因此，各项事业都十分顺利。只要本人坚持中正之道，动机纯正，是非分明，以诚心与人和悦，前途光明。',
                'yao' => '011011',
            ],
            110010 => [
                'num' => 59,
                'title' => '涣卦',
                'title2' => '风水涣',
                'good' => '下下卦',
                'fen' => 61,
                'star' => 30,
                'info' => '处于比较困难的地步，主要是因为人心不齐。要采取强有力的办法，求得安定团结。其中，最为重要的是坚持正道，秉持大公无私的精神，收人心以聚涣散。',
                'yao' => '110010',
            ],
            10011 => [
                'num' => 60,
                'title' => '节卦',
                'title2' => '水泽节',
                'good' => '上上卦',
                'fen' => 90,
                'star' => 45,
                'info' => '正处在发展时期，一定要注意切勿冒进。但更不应放弃良好的机遇，只要坚持遵利守义的原则，可大胆行动。这样，事业可以继续兴旺发达。',
                'yao' => '010011',
            ],
            110011 => [
                'num' => 61,
                'title' => '中孚卦',
                'title2' => '风泽中孚',
                'good' => '下下卦',
                'fen' => 82,
                'star' => 40,
                'info' => '以诚实忠信的态度对待事业，获得重大的成就，发展顺利。必须继续遵循正道的原则，不可掉以轻心，也不可过分地相信人，尤其不可以孤傲的性格自我封闭。否则事业将走向失败。',
                'yao' => '110011',
            ],
            1100 => [
                'num' => 62,
                'title' => '小过卦',
                'title2' => '雷山小过',
                'good' => '中上卦',
                'fen' => 80,
                'star' => 40,
                'info' => '行动宜谨慎小心，切勿冒险，尤其不得钻空子。但只要坚持正当的行为，可勇敢向前，努力奋斗，开拓事业。不过，一定要把握尺度，不要好高骛远，以免招致灾祸。随机应变是关键。',
                'yao' => '001100',
            ],
            10101 => [
                'num' => 63,
                'title' => '既济卦',
                'title2' => '水火既济',
                'good' => '中上卦',
                'fen' => 90,
                'star' => 45,
                'info' => '处在顺利和兴旺的阶段，甚至大功已经告成。如何才能保住这种势头，是问题的关键。必须认识盛极将衰的真理，必思患预防，防微杜渐，有备可无患。同时，一定要密切注意形势，抓住有利时机，继续奋发向上，推动事业发展。',
                'yao' => '010101',
            ],
            101010 => [
                'num' => 64,
                'title' => '未济卦',
                'title2' => '火水未济',
                'good' => '中下卦',
                'fen' => 90,
                'star' => 45,
                'info' => '处于最后的关键时刻，成功与否就在眼前。因此务必不可掉以轻心。自我节制非常重要，坚持到底，必要时也可能慎重地进行适度冒险，以成就事业。',
                'yao' => '101010',
            ],
        ];
        $count = count($biHua);
        $shanI = (int)($count / 2);
        $shanTotal = array_sum(array_slice($biHua, 0, $shanI));
        $xiaTotal = array_sum(array_slice($biHua, $shanI));
        $bihuaTotal = array_sum($biHua);
        $jxIndex = $bihuaTotal % 81;
        $jxIndex = $jxIndex > 0 ? $jxIndex : 81;
        // 上卦
        $guaTopIndex = $this->getGuaKey($shanTotal);
        // 下卦
        $guaBotIndex = $this->getGuaKey($xiaTotal);
        // 动爻
        $dongIndex = $this->getGuaKey(array_sum($biHua), 6);
        $guaTop = $list[$guaTopIndex];
        $guaBot = $list[$guaBotIndex];
        $guaBenIndex = (string)($guaTop['xian'] . $guaBot['xian']);
        $guaBenArr = str_split($guaBenIndex);
        // 本卦
        $guaBen = $list2[(int)$guaBenIndex];
        $key1 = 6 - $dongIndex;
        $guaBenArr1 = $guaBenArr;
        $guaBenArr1[$key1] = $guaBenArr1[$key1] ? 0 : 1;
        $bianIndex = implode('', $guaBenArr1);
        // 变卦
        $guaBian = $list2[(int)$bianIndex];
        // 互卦
        $huTopStr = substr($guaBenIndex, 1, 3);
        $huBotStr = substr($guaBenIndex, 2, 3);
        $guaHuIndex = $huTopStr . $huBotStr;
        // 变卦
        $guaHu = $list2[(int)$guaHuIndex];

        $guaYong = $dongIndex > 3 ? $guaTop : $guaBot;
        $guaTi = $dongIndex > 3 ? $guaBot : $guaTop;
        $tiGuaWx = $guaTi['wx'];
        $yongGuaWx = $guaYong['wx'];
        // 体卦五行关系
        $tiGuaWxOther = $this->wxGuanxi($tiGuaWx);
        // 互卦和变卦五行
        $bianTop = substr($bianIndex, 0, 3);
        $bianBot = substr($bianIndex, 3, 3);

        $bianTopInfo = $listByXian[$bianTop] ?? $listByXian['111'];
        $bianBotInfo = $listByXian[$bianBot] ?? $listByXian['111'];
        $huTopInfo = $listByXian[$huTopStr] ?? $listByXian['111'];
        $huBotInfo = $listByXian[$huBotStr] ?? $listByXian['111'];
        $wxArr = [
            $bianTopInfo['wx'], $bianBotInfo['wx'], $huTopInfo['wx'], $huBotInfo['wx'],
        ];
        $wxArrCount = array_count_values($wxArr);
        $tiYong = 'fu';
        $gxCn = [
            'fu' => '和体卦相同',
            'shen' => '被体卦生',
            'ke' => '被体卦克',
            'xie' => '被体卦泄',
            'hao' => '被体卦耗',
        ];
        $list4 = [
            'fu' => '体卦和用卦五行相同',
            'shen' => '体卦生用卦五行',
            'ke' => '体卦克用卦五行',
            'xie' => '体卦泄用卦五行',
            'hao' => '体卦耗用卦五行',
        ];
        $bianHuArr = [];
        foreach ($tiGuaWxOther as $k1 => $v1) {
            if ($v1 == $yongGuaWx) {
                $tiYong = $k1;
            }
            if (isset($wxArrCount[$v1])) {
                $bianHuArr[] = $wxArrCount[$v1] . "个{$v1}" . $gxCn[$k1];
            }
        }
        $guaDetail = [
            'ben' => [
                'title' => $guaBen['title'],
                'title2' => $guaBen['title2'],
                'yao' => $guaBen['yao'],
                'top' => $guaTop['gua'] . $guaTop['wx'],
                'bot' => $guaBot['gua'] . $guaBot['wx'],
                'des_t' => $dongIndex > 3 ? '用卦' : '体卦',
                'des_b' => $dongIndex > 3 ? '体卦' : '用卦',
            ],
            'bian' => [
                'title' => $guaBian['title'],
                'title2' => $guaBian['title2'],
                'yao' => $guaBian['yao'],
                'top' => $bianTopInfo['gua'] . $bianTopInfo['wx'],
                'bot' => $bianBotInfo['gua'] . $bianBotInfo['wx'],
            ],
            'hu' => [
                'title' => $guaHu['title'],
                'title2' => $guaHu['title2'],
                'yao' => $guaHu['yao'],
                'top' => $huTopInfo['gua'] . $huTopInfo['wx'],
                'bot' => $huBotInfo['gua'] . $huBotInfo['wx'],
            ],
        ];
        $jiXianArr = $list3[$jxIndex];
        $jiXianArr['num'] = $jxIndex;
        $result = [
            'jixian' => $jiXianArr,
            'jiexi' => [
                'ben' => "{$guaBen['title']}({$guaBen['title2']})",
                'bian' => "{$guaBian['title']}({$guaBian['title2']})",
                'ti' => $guaTi['gua'] . $guaTi['wx'],
                'yong' => $guaYong['gua'] . $guaYong['wx'],
                'gx' => $list4[$tiYong],
                'bian_hu' => implode('，', $bianHuArr),
            ],
            // 挂详情
            'gua' => $guaDetail,
            // 财运解析
            'cai_yun' => $guaBian['info'],
            'cai_fen' => $guaBian['fen'],
            'star' => $guaBian['star'],
        ];
        return $result;
    }

    /**
     * 获得
     * @param $num
     * @param int $chuShu
     * @return int
     */
    protected function getGuaKey($num, $chuShu = 8): int
    {
        $res = $num % $chuShu;
        if ($res == 0) {
            $res = $chuShu;
        }
        return $res;
    }

    /**
     * 获得名字列表
     * @param int $hanWxNum 汉字五行数字
     * @param array $wxUsed 名字使用数组
     * @param int $num 要取的名字数
     * @param int $timeNum 时间基数
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getNlist(int $hanWxNum, array $wxUsed, int $num = 120, int $timeNum = 0): array
    {
        $where = [
            ['hanwx', '=', $hanWxNum],
            ['wx', 'in', $wxUsed],
        ];
        $num1 = $num * 5;
        $num2 = 1000;
        $hanIdStr = "|" . $this->industryData['id'] . "|";
        $map1 = [
            ['y_type', '=', 0],
            ['yi', 'like', "%$hanIdStr%"],
        ];
        $map2 = [
            ['y_type', '=', 1],
            ['yi', 'not like', "%$hanIdStr%"],
        ];
        $map3 = [
            ['y_type', '=', 2],
        ];
        $cacheKeyNameNum = md5($hanWxNum . implode('|', $wxUsed) . $hanIdStr);
        $count = Cache::get($cacheKeyNameNum, false);
        if (false === $count) {
            $count = Shop::where($where)
                ->where(
                    function ($query) use ($map1, $map2, $map3) {
                        $query->whereOr([$map1, $map2, $map3]);
                    }
                )
                ->count('id');
            Cache::set($cacheKeyNameNum, $count, 600);
        }

        $maxPage = ceil($count / $num2);
        $diff = $count % $num2;
        if ($diff < $num * 3) {
            $maxPage--;
        }
        if ($maxPage == 0) {
            $minID = 1;
        } else {
            $minID = ($timeNum % $maxPage) + 1;
        }
        $cacheKeyName = md5($hanWxNum . implode('|', $wxUsed) . $hanIdStr) . '_' . $minID;
        $result = Cache::get($cacheKeyName, false);
        if (false === $result) {
            $result = Shop::where($where)
                ->where(
                    function ($query) use ($map1, $map2, $map3) {
                        $query->whereOr([$map1, $map2, $map3]);
                    }
                )
                ->page($minID, $num2)
                ->order('id asc')
                ->column('id,wx,ming');
            Cache::set($cacheKeyName, $result, 1800);
        }
        shuffle($result);
        $this->arrZi = array_merge($this->arrZi, $this->getZiArr(array_column($result, 'ming')));
        return $result;
    }

    /**
     * 获得字信息
     * @param array $list
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getZiArr(array $list): array
    {
        $arr = [];
        foreach ($list as $v) {
            $arr1 = Utils::mbStrSplit($v);
            foreach ($arr1 as $v1) {
                $arr[$v1] = $v1;
            }
        }
        $arr = array_values($arr);
        return Cnword::getInfo2($arr);
    }

    /**
     * 获得纪年五行
     * @param $jiNian
     * @return array
     */
    protected function getJnWx($jiNian): array
    {
        $wxCount = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        $jnWx = [];
        $wxAttr = BaziExt::$wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tmpWx1 = $wxAttr[$v[0]];
            $wxCount[$tmpWx1]++;
            $tmpWx2 = $wxAttr[$v[1]];
            $wxCount[$tmpWx2]++;
            $jnWx[$k] = [
                $tmpWx1, $tmpWx2,
            ];
        }
        $resCount = [];
        foreach ($wxCount as $k => $v) {
            $resCount[] = [
                't' => $k, 'num' => $v,
            ];
        }
        return [
            'detail' => $jnWx,
            'num' => $resCount,
        ];
    }

    /**
     * 八字同类和异类
     * @return array
     */
    protected function getBaziFenXin(): array
    {
        $temp = [
            '金' => [['土'], ['水', '火', '木']],
            '木' => [['水'], ['金', '火', '土']],
            '水' => [['金'], ['火', '木', '土']],
            '火' => [['木'], ['土', '水', '金']],
            '土' => [['火'], ['水', '木', '金']],
        ];
        // 五行数值表
        $likeGodNum = $this->lunar->getWuxingNum();
        $jiNian = $this->lunar->getLunarTganDzhi();
        // 异类
        $unKing = [];
        $likeGodDay = $this->lunar->wuXingAttr[$jiNian['d'][0]];

        $unKingTotal = 0;
        foreach ($temp[$likeGodDay][1] as $val) {
            $unKing[$val] = [
                'wx' => $val,
                'fen' => round($likeGodNum[$val], 3),
            ];
            $unKingTotal += round($likeGodNum[$val], 3);
        }
        $unKingTotal = round($unKingTotal, 3);
        $str2 = $temp[$likeGodDay][0][0];
        $kingTotal = round($likeGodNum[$likeGodDay] + $likeGodNum[$str2], 3);
        $differ = $kingTotal - $unKingTotal;
        if ($differ > 0) {
            $differTitle = '偏旺';
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
        }
        $tmp2 = [];
        foreach ($likeGodNum as $k => $v) {
            $tmp2[] = [$k, $v];
        }
        $arr2 = array_column($tmp2, 1, 0);
        arsort($arr2);
        $arr3 = array_keys($arr2);
        return [
            'fraction' => $tmp2,
            // 同类
            'king' => [
                'info' => [
                    ['wx' => $likeGodDay, 'fen' => $arr2[$likeGodDay]],
                    ['wx' => $str2, 'fen' => $arr2[$str2]],
                ],
                'fen' => $kingTotal,
            ],
            'unking' => [
                'info' => array_values($unKing),
                'fen' => $unKingTotal,
            ],
            'max' => $arr3[0],
            'min' => $arr3[4],
            // 旺衰得分
            'differ' => [
                'fen' => round($differ, 3), 'title' => $differTitle,
            ],
        ];
    }

    /**
     * 五行占比
     * @return array
     */
    protected function getWxPersent(): array
    {
        $wx = $this->lunar->getWuxingNum();
        $res = [];
        $total = array_sum($wx);
        foreach ($wx as $k => $v) {
            $res[] = [
                'wx' => $k,
                'per' => number_format($v * 100 / $total, 2),
            ];
        }
        return $res;
    }

    /**
     * 五行关系
     * @param $wx
     * @return array
     */
    protected function wxGuanxi($wx): array
    {
        $list = [
            'shen' => ['木' => '火', '火' => '土', '土' => '金', '金' => '水', '水' => '木'],
            'fu' => ['木' => '木', '火' => '火', '土' => '土', '金' => '金', '水' => '水'],
            'ke' => ['木' => '土', '火' => '金', '土' => '水', '金' => '木', '水' => '火'],
            'xie' => ['木' => '水', '火' => '木', '土' => '火', '金' => '土', '水' => '金'],
            'hao' => ['木' => '金', '火' => '水', '土' => '木', '金' => '火', '水' => '土'],
        ];
        $result = [];
        foreach ($list as $k => $v) {
            $result[$k] = $v[$wx];
        }
        return $result;
    }

    /**
     * 根据行业五行的生 扶 泄组合成五行数组
     * @return array
     */
    protected function getNameUsedWx(): array
    {
        $indInfo = $this->industryData;
        $catWx = $indInfo['like'];
        $yongShen = $this->xy['yong'];
        $list = [
            '金金' => [['土', '金']],
            '金木' => [['金', '土']],
            '金水' => [['金', '土'], ['金', '水']],
            '金火' => [['金', '土'], ['火', '土']],
            '金土' => [['金', '土']],
            '木金' => [['木', '水'], ['金', '水']],
            '木木' => [['木', '水']],
            '木水' => [['木', '水']],
            '木火' => [['木', '水'], ['木', '火']],
            '木土' => [['木', '水'], ['土', '土']],
            '水金' => [['水', '金']],
            '水木' => [['水', '金'], ['木', '水']],
            '水水' => [['水', '金']],
            '水火' => [['水', '金']],
            '水土' => [['水', '金'], ['土', '金']],
            '火金' => [['火', '木']],
            '火木' => [['火', '木']],
            '火水' => [['火', '木'], ['水', '木']],
            '火火' => [['火', '木']],
            '火土' => [['火', '木'], ['土', '火']],
            '土金' => [['土', '火'], ['金', '土']],
            '土木' => [['土', '火'], ['木', '火']],
            '土水' => [['土', '火']],
            '土火' => [['土', '火']],
            '土土' => [['土', '火']],
        ];
        $wxToNum = Shop::$wxToNum;
        $result2 = [];
        $result3 = [];
        foreach ($catWx as $hanwx) {
            $cy = $hanwx . $yongShen;
            $listU = $list[$cy];
            foreach ($listU as $item) {
                $tmp = [
                    $wxToNum[$item[0]], $wxToNum[$item[1]],
                ];
                $tmp2 = [$tmp, $tmp];
                $tmp3 = [$tmp, $tmp, $tmp];
                $arr2 = Utils::dikaer($tmp2, '');
                $arr3 = Utils::dikaer($tmp3, '');
                $result2 = array_merge($result2, $arr2);
                $result3 = array_merge($result3, $arr3);
            }
        }
        $result2 = array_values(array_unique($result2));
        $result3 = array_values(array_unique($result3));
        return [
            2 => $result2,
            3 => $result3,
            23 => array_merge($result2, $result3),
        ];
    }

    /**
     * 吉日
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        // 吉时
        $listJiShi = [
            '甲子' => ['酉'], '乙丑' => ['卯', '申'], '丙寅' => ['未'],
            '丁卯' => ['午', '未'], '戊辰' => ['巳', '申'], '己巳' => ['辰', '午'],
            '庚午' => ['卯', '申'], '辛未' => ['申'], '壬申' => ['辰', '巳'],
            '癸酉' => ['午'], '甲戌' => ['巳', '申'], '乙亥' => ['辰', '午'],
            '丙子' => ['酉'],
            '丁丑' => ['巳', '申'],
            '戊寅' => ['未'],
            '己卯' => ['卯', '午'],
            '庚辰' => ['巳', '申'],
            '辛巳' => ['辰', '午'],
            '壬午' => ['卯', '酉'],
            '癸未' => ['巳', '申'],
            '甲申' => ['辰', '巳'],
            '乙酉' => ['午', '未'],
            '丙戌' => ['巳', '申'],
            '丁亥' => ['辰', '午'],
            '戊子' => ['酉'],
            '己丑' => ['卯', '巳'],
            '庚寅' => ['未'],
            '辛卯' => ['卯', '午'],
            '壬辰' => ['巳', '酉'],
            '癸巳' => ['辰', '午'],
            '甲午' => ['卯', '申'],
            '乙未' => ['申'],
            '丙申' => ['巳'],
            '丁酉' => ['午', '未'],
            '戊戌' => ['巳', '申'],
            '己亥' => ['辰', '午'],
            '庚子' => ['酉'],
            '辛丑' => ['卯', '巳'],
            '壬寅' => ['未'],
            '癸卯' => ['卯', '午'],
            '甲辰' => ['巳', '酉'],
            '乙巳' => ['辰', '午'],
            '丙午' => ['卯', '申'],
            '丁未' => ['巳', '申'],
            '戊申' => ['辰', '巳'],
            '己酉' => ['午', '未'],
            '庚戌' => ['巳', '申'],
            '辛亥' => ['辰', '午'],
            '壬子' => ['辰', '酉'],
            '癸丑' => ['卯', '巳'],
            '甲寅' => ['未'],
            '乙卯' => ['卯', '午'],
            '丙辰' => ['巳', '酉'],
            '丁巳' => ['辰', '午'],
            '戊午' => ['卯', '酉'],
            '己未' => ['巳', '申'],
            '庚申' => ['辰', '巳'],
            '辛酉' => ['午', '未'],
            '壬戌' => ['巳'],
            '癸亥' => ['辰', '午'],
        ];
        // 岁破 年+日
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        // 杨公忌日（农历）
        $listYangji = [
            '1-13', '2-21', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19',
        ];
        // 其他凶日 月支+日子
        $listOther = [
            '卯乙亥', '卯乙未', '辰壬子', '辰庚申', '辰戊辰', '巳乙卯', '巳壬子', '午丙戌',
            '未乙卯', '未癸未亥', '未丁亥', '申戊午', '申乙卯', '申辛亥', '酉辛丑', '酉辛亥',
            '戌戊午', '戌辛亥', '亥辛酉', '亥丙午', '亥癸亥', '丑丁巳', '丑癸亥', '丑戊辰',
        ];
        // 月支+日支 月破（破日）	收日	闭日	小耗	小流财	受死
        $listPo = [
            // 月破（破日）
            '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未',
            // 收日
            '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申', '子酉', '丑戌',
            // 闭日
            '寅丑', '卯寅', '辰卯', '巳辰', '午巳', '未午', '申未', '酉申', '戌酉', '亥戌', '子亥', '丑子',
            // 小耗
            '寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午',
            // 小流财
            '寅亥', '卯申', '辰巳', '巳寅', '午卯', '未午', '申子', '酉酉', '戌丑', '亥未', '子辰', '丑戌',
            // 受死
            '寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉',
        ];
        // 煞向
        $listShaXian = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        // 黄道日黑道日描述
        $listDetail = [
            '青龙' => '青龙黄道日，该日行动的价值大于计划，有付出就有收获，适合各种行业。',
            '明堂' => '明堂黄道日，该日遭遇贵人几率较高，有机会得到贵人的帮助，适合各种行业。',
            '金匮' => '金匮黄道日，该日有利于开业祈福，尤其适合宗教文化行业、女性用品行业。',
            '天德' => '天德黄道日，该日大利出行，所做的事情容易成功，可视行业特点按需取用。',
            '玉堂' => '玉堂黄道日，该日对于求财有利，而且有受到贵人关注的机会，适合各种行业。',
            '司命' => '司命黄道日，该日对于白天营业的行业，开张大吉，夜间营业行业慎用。',
            '天刑' => '该日不忌开业，旅游、运输等行业可做权宜备选。',
            '朱雀' => '该日不忌开业，印刷、金属等行业可做权宜之用。',
            '白虎' => '该日不忌开业，宗教行业、餐饮业可做权宜之用。',
            '天牢' => '该日不忌开业，但最好为小店铺或小本生意开业。',
            '元武' => '该日不忌开业，餐饮业、服务业可选，偏门生意慎选。',
            '勾陈' => '该日不忌开业，印刷、餐饮等行业可做权宜之用。',
        ];
        // 吉神
        $listJishen = [
            '天德' => '有逢凶化吉的寓意，易获得贵人帮助。',
            '月德' => '有趋吉避凶的寓意，有机会遇到大福德。',
            '天德合' => '有远离盗贼和灾祸的寓意，逢凶化吉。',
            '月德合' => '有远离盗贼和灾祸的寓意，逢凶化吉。',
            '满日' => '有钱财满载而归，圆满的寓意。',
            '成日' => '有吉星高照，贵人指引的寓意。',
            '开日' => '有大吉大利，马到成功的寓意。',
            '天愿' => '有和、善的寓意，避诸多凶险。',
            '民日' => '有顺势而为，遵循天道的寓意。',
            '五富' => '有生活富足，生意昌盛的寓意。',
            '月财' => '有获得偏财，财源滚滚的寓意。',
            '天财' => '有受到财神眷顾，利市的寓意。',
            '六合' => '有喜庆寓意，有助于增加人缘。',
        ];
        // 宜 1.1基础数据调整为日历中宜开市+宜立券交易+宜纳财的日子
        $listYi = [
            '开市', '交易', '求财', '立契', '买卖', '纳财', '立券交易',
        ];
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $dzUser = [
            $jiNian['y'][1], $jiNian['d'][1],
        ];
        // 测算天数
        $limitNum = 365;
        $list = [];
        $otime = $this->parmData['otime'];
        $wuXingAttr = BaziExt::$wuXingAttr;
        for ($i = 1; $i <= $limitNum; $i++) {
            $time = strtotime("{$otime} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $huangli = Huangli::date($time);
            $base1 = $huangli->getLunarByBetween();
            $jiNianTmp = $base1['jinian'];
            $ziRi = $huangli->getZhiRi();
            if ($ziRi['huan_dao'] == '黑道') {
                continue;
            }
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            // 岁破过滤
            if ($listSuiPo[$jiNianTmp['y'][1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            // 事主地支(年日)+日支
            if ($listSuiPo[$dzUser[0]] == $jiNianTmp['d'][1] || $listSuiPo[$dzUser[1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            // 杨公忌日
            if (in_array($base1['_nongli']['m'] . '-' . $base1['_nongli']['d'], $listYangji)) {
                continue;
            }
            $shiLiAndJ = $this->getJieQiDay(date('Y', $time));
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            if (in_array($timeStr, $shiLiAndJ)) {
                continue;
            }
            // 其他凶日 月+日子
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][0] . $jiNianTmp['d'][1], $listOther)) {
                continue;
            }
            // 月支+日支 月破（破日）	收日	闭日	小耗	小流财	受死
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][1], $listPo)) {
                continue;
            }

            $jiXiong = $huangli->getJiXiong();
            if (!array_intersect($jiXiong['yi'], $listYi)) {
                continue;
            }
            $position = $huangli->getPosition();
            $hehai = $huangli->getTodayHeHai();
            $tmpOne = [
                't' => date('Y年m月d日', $time),
                // 星期
                'week' => Huangli::getWeekChs($time),
                'd' => $base1['nongli']['d'],
                // 相冲
                'chong' => $hehai['xian_chong'],
            ];
            $tmpListKey = $base1['nongli']['y'] . '年' . $base1['nongli']['m'];
            $list[$tmpListKey][] = $tmpOne;
        }
        $result = [];
        foreach ($list as $k => $v) {
            $result[] = [
                'm' => $k,
                'info' => $v,
            ];
        }
        return $result;
    }

    /**
     * 求四离 四绝 公历
     * 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
     * @param $year
     * @return array
     */
    protected function getJieQiDay($year): array
    {
        $list = [
            '春分', '夏至', '秋分', '冬至', '立春', '立夏', '立秋', '立冬',
        ];
        $jieqi = SolarTerm::getAllJieQi($year);
        $day = [];
        foreach ($jieqi as $k => $v) {
            if (in_array($k, $list)) {
                $day[] = date('Y-m-d', (strtotime($v) - 86400));
            }
        }
        return $day;
    }

    /**
     * 飞盘
     * @param string $time 时间字符串
     * @return array
     * @throws Exception
     */
    public function feipan($time): array
    {
        // 年
        $list1 = [
            '上元' => [
                [1, 9, 8, 7, 6, 5, 4, 3, 2],
                ['一白', '九紫', '八白', '七赤', '六白', '五黄', '四绿', '三碧', '二黑'],
            ],
            '中元' => [
                [4, 3, 2, 1, 9, 8, 7, 6, 5],
                ['四绿', '三碧', '二黑', '一白', '九紫', '八白', '七赤', '六白', '五黄'],
            ],
            '下元' => [
                [7, 6, 5, 4, 3, 2, 1, 9, 8],
                ['七赤', '六白', '五黄', '四绿', '三碧', '二黑', '一白', '九紫', '八白'],
            ],
        ];
        // 宫飞顺序
        $listGong = [
            '中', '乾', '兑', '艮', '离', '坎', '坤', '震', '巽',
        ];
        $listJieQiArr = [
            ['冬至', '小寒', '大寒', '立春'],
            ['雨水', '惊蛰', '春分', '清明'],
            ['谷雨', '立夏', '小满', '芒种'],
            // 逆
            ['夏至', '小暑', '大暑，立秋'],
            ['处暑', '白露', '秋分', '寒露'],
            ['霜降', '立冬', '小雪', '大雪'],
        ];
        $listJieQi = [
            [1, 2, 3, 4, 5, 6, 7, 8, 9],
            [7, 8, 9, 1, 2, 3, 4, 5, 6],
            [4, 5, 6, 7, 8, 9, 1, 2, 3],
            [9, 8, 7, 6, 5, 4, 3, 2, 1],
            [3, 2, 1, 9, 8, 7, 6, 5, 4],
            [6, 5, 4, 3, 2, 1, 9, 8, 7],
        ];
        $listMonth = [
            [8, 7, 6, 5, 4, 3, 2, 1, 9, 8, 7, 6],
            [5, 4, 3, 2, 1, 9, 8, 7, 6, 5, 4, 3],
            [2, 1, 9, 8, 7, 6, 5, 4, 3, 2, 1, 9],
        ];
        $lunar = Ex::date($time);
        $base = $lunar->getLunarByBetween();
        $nongliShu = $base['_nongli'];
        $year = $lunar->dateTime->format('Y');
        if ($year >= 1864 && $year <= 1923) {
            $yuan = '上元';
        } elseif ($year >= 1924 && $year <= 1983) {
            $yuan = '中元';
        } else {
            $yuan = '下元';
        }
        $jiNian = $base['jinian'];
        $jiNainYearStr = implode('', $jiNian['y']);
        $jiNianDayStr = implode('', $jiNian['d']);
        $YearKey = $this->getKeyByJiNian($jiNainYearStr);
        $dayKey = $this->getKeyByJiNian($jiNianDayStr);

        $startYearNum = $list1[$yuan][0][$YearKey];
        // 年
        $result = [];
        foreach ($listGong as $k => $v) {
            $tmp = ($startYearNum + $k) % 9;
            $result[$v]['name'] = $v;
            $result[$v]['y'] = $tmp ?: 9;
        }
        if (in_array($jiNian['y'][1], ['子', '午', '卯', '酉'])) {
            $monthKey = $listMonth[0][$nongliShu['m'] - 1];
        } elseif (in_array($jiNian['y'][1], ['辰', '戌', '丑', '未'])) {
            $monthKey = $listMonth[1][$nongliShu['m'] - 1];
        } else {
            $monthKey = $listMonth[2][$nongliShu['m'] - 1];
        }
        foreach ($listGong as $k => $v) {
            $tmp = ($monthKey + $k) % 9;
            $result[$v]['m'] = $tmp ?: 9;
        }
        $jieqiArr = $lunar->getJieQiCur();
        $jieqi = $jieqiArr['current'][0];
        $jieqiKey = 0;
        foreach ($listJieQiArr as $k => $v) {
            if (in_array($jieqi, $v)) {
                $jieqiKey = $k;
            }
        }
        // 顺 1顺 0逆
        $isShun = $jieqiKey < 3 ? 1 : 0;
        $startDayNum = $listJieQi[$jieqiKey][$dayKey];
        foreach ($listGong as $k => $v) {
            if ($isShun) {
                $tmp = ($dayKey + $k) % 9;
            } else {
                $tmp = ($dayKey - $k + 9) % 9;
            }
            // $tmp=$tmp ?: 9;
            $result[$v]['d'] = $listJieQi[$jieqiKey][$tmp];
        }
        $listPos = [
            '巽' => '东南', '离' => '正南', '坤' => '西南',
            '震' => '正东', '中' => '正中', '兑' => '正西',
            '艮' => '东北', '坎' => '正北', '乾' => '西北',
        ];
        $res = ['cai' => '', 'fei' => '', 'hua' => ''];
        foreach ($result as $v) {
            $tmpName = $v['name'];
            $pos = $listPos[$tmpName] ?? '正中';
            switch ($v['y']) {
                case 8:
                    $res['cai'] = $pos;
                    break;
                case 3:
                    $res['fei'] = $pos;
                    break;
                case 1:
                    $res['hua'] = $pos;
                    break;
            }
        }
        return $res;
    }

    /**
     * 根据干支纪年获得序号
     * @param $str
     * @return int
     */
    private function getKeyByJiNian($str): int
    {
        $list = [
            ['甲子', '癸酉', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            ['己巳', '戊寅', '丁亥', '丙申', '乙巳', '甲寅', '癸亥'],
            ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳'],
        ];
        $res = 0;
        foreach ($list as $k => $v) {
            if (in_array($str, $v)) {
                $res = $k;
                break;
            }
        }
        return $res;
    }

    /**
     * 获得64卦序号
     * @param array $biHua
     * @return int
     */
    protected function getGuaNumber(array $biHua): int
    {
        $bihuaTotal = array_sum($biHua);
        $jxIndex = $bihuaTotal % 81;
        $jxIndex = $jxIndex > 0 ? $jxIndex : 81;
        return $jxIndex;
    }

    /**
     * 获得名字所用的五行
     * @param $hanwx
     * @param $ywx
     * @return array
     */
    protected function getMingWx($hanwx, $ywx): array
    {
        $list = [
            '金金' => ['土金'], '金木' => ['金土'], '金水' => ['金土', '金水'], '金火' => ['金土', '火土'], '金土' => ['金土'],
            '木金' => ['木水', '金水'], '木木' => ['木水'], '木水' => ['木水', '土土'], '木火' => ['木水', '木火'], '木土' => ['木土'],
            '水金' => ['水金'], '水木' => ['水金', '木水'], '水水' => ['水金'], '水火' => ['水金'], '水土' => ['水金', '土金'],
            '火金' => ['火木'], '火木' => ['火木'], '火水' => ['火木', '水木'], '火火' => ['火木'], '火土' => ['火木', '土火'],
            '土金' => ['土火', '金土'], '土木' => ['土火', '木火'], '土水' => ['土火'], '土火' => ['土火'], '土土' => ['土火'],
        ];
        $list1 = $list[$hanwx . $ywx] ?? [];
        $result = [];
        foreach ($list1 as $v) {
            $arr = preg_split('/(?<!^)(?!$)/u', $v);
            $tmp = [$arr, $arr];
            $arr1 = Utils::dikaer($tmp, '');
            $result = array_merge($result, $arr1);
            $tmp = [$arr, $arr, $arr];
            $arr2 = Utils::dikaer($tmp, '');
            $result = array_merge($result, $arr2);
        }
        return array_values(array_unique($result));
    }
}
