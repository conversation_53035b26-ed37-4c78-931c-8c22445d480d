<?php
// +----------------------------------------------------------------------
// | Jiehunjiri. 结婚吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\jiri\JiRiCheckBadTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Jiehunjiri
{
    use JiRiCheckBadTraits;

    /**
     * @var string[]
     */
    protected $sxList = ['', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

    /**
     * @var Ex 男
     */
    protected Ex $mLunar;

    /**
     * @var Ex 女
     */
    protected Ex $fLunar;

    /**
     * 生肖相冲
     * @var string[]
     */
    protected array $sxChongList = ['', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳'];

    /**
     * 月份转农历称呼速查表.
     * @var array
     */
    protected array $monthAlias = ['正', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'];

    /**
     * 要排除的生肖
     * @var array
     */
    protected array $sxNumList = [];

    /**
     * 订单时间
     * @var Ex
     */
    protected Ex $oLunar;

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * 结婚吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男姓名
            // 'malename' => I('malename', '', 'trim'),
            // 男出生时间
            'maletime' => input('maletime', '', 'trim'),
            // 男父生肖
            'malefsx' => input('malefsx', 0, 'intval'),
            // 男母生肖
            'malemsx' => input('malemsx', 0, 'intval'),
            // 女姓名
            //'femalename' => I('femalename', '', 'trim'),
            // 女出生时间
            'femaletime' => input('femaletime', '', 'trim'),
            // 女父生肖
            'femalefsx' => input('femalefsx', 0, 'intval'),
            // 女母生肖
            'femalemsx' => input('femalemsx', 0, 'intval'),
            // 起始时间
            'otime' => input('otime', time(), 'trim'),
            // 月分
            'plan_time' => input('plan_time', 3, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'maletime|男方出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'malefsx|男父生肖' => ['require', 'between:0,12'],
                'malemsx|男母生肖' => ['require', 'between:0,12'],
                'femaletime|女方出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'femalefsx|女父生肖' => ['require', 'between:0,12'],
                'femalemsx|女母生肖' => ['require', 'between:0,12'],
                'otime|订单时间' => ['require', 'isDateOrTime:订单时间'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->mLunar = Ex::date($data['maletime'])->sex(0);
        $myear = $this->mLunar->dateTime->format('Y');
        $this->fLunar = Ex::date($data['femaletime'])->sex(1);
        $fyear = $this->fLunar->dateTime->format('Y');
        $this->oLunar = Ex::date($data['otime']);
        $maleBazi = $this->mLunar->getLunarByBetween();
        $maleBazi['god'] = $this->mLunar->getGod();
        $maleBazi['_god'] = $this->mLunar->_getGod();
        $femaleBazi = $this->fLunar->getLunarByBetween();
        $femaleBazi['god'] = $this->fLunar->getGod();
        $femaleBazi['_god'] = $this->fLunar->_getGod();
        $res = [
            'male' => $maleBazi,
            'femail' => $femaleBazi,
            'gua' => BaziExt::getMingGua($myear, $fyear),
            'dali' => $this->getDaLiYue(),
            'xiaoli' => $this->getXiaoLiYue(),
            // 生肖关系
            'guanxi' => $this->getShengXiaoGuanXi($maleBazi['jinian']['y'][1], $femaleBazi['jinian']['y'][1]),
            // 吉日
            'jiri' => $this->getJiri(),
        ];
        return $res;
    }

    /**
     * 大利月
     * @return array
     */
    protected function getDaLiYue(): array
    {
        $sx = $this->fLunar->getZodiac();
        $list = [
            '鼠' => ['十二月', '六月'],
            '牛' => ['十一月', '五月'],
            '虎' => ['八月', '二月'],
            '兔' => ['正月', '七月'],
            '龙' => ['四月', '十月'],
            '蛇' => ['三月', '九月'],
            '马' => ['十二月', '六月'],
            '羊' => ['十一月', '五月'],
            '猴' => ['八月', '二月'],
            '鸡' => ['正月', '七月'],
            '狗' => ['四月', '十月'],
            '猪' => ['三月', '九月'],
        ];
        return $list[$sx];
    }

    /**
     * 小利月
     * @return array
     */
    protected function getXiaoLiYue(): array
    {
        $year = $this->fLunar->getLunarGanzhiYear();
        $dz = $year[1];
        $list = [
            '寅' => ['三月', '九月'],
            '卯' => ['六月', '十二月'],
            '辰' => ['五月', '十一月'],
            '巳' => ['二月', '八月'],
            '午' => ['正月', '七月'],
            '未' => ['四月', '十月'],
            '申' => ['三月', '九月'],
            '酉' => ['六月', '十二月'],
            '戌' => ['五月', '十一月'],
            '亥' => ['二月', '八月'],
            '子' => ['正月', '七月'],
            '丑' => ['四月', '十月'],
        ];
        return $list[$dz];
    }

    /**
     * 生肖关系
     * @param string $dzY 年地支
     * @param string $dzY2 年地支2
     * @return array
     */
    protected function getShengXiaoGuanXi(string $dzY, string $dzY2): array
    {
        $list = [
            '从属相合婚的角度来看，男方与女方的生肖相冲，你们之间会有些小问题，选择吉日成婚可以化解这种不利关系。',
            '从属相合婚的角度来看，男方与女方的生肖相刑，遇事多谦让，选择吉日成婚可以化解这种不利关系，让婚姻状态更加稳定。',
            '从属相合婚的角度来看，男方与女方的生肖相合，选择吉日成婚可以让婚姻关系更和睦。',
            '从属相合婚的角度来看，男方与女方的生肖组合中规中矩，选择吉日成婚可以让感情关系更稳定。',
        ];
        // 妨夫月
        $list2 = [
            '寅' => '6、12', '卯' => '3、9', '辰' => '2、8', '巳' => '5、11', '午' => '4、10', '未' => '1、7',
            '申' => '6、12', '酉' => '3、9', '戌' => '2、8', '亥' => '5、11', '子' => '4、10', '丑' => '1、7',
        ];
        // 妨妇月
        $list3 = [
            '寅' => '1、7', '卯' => '2、8', '辰' => '3、9', '巳' => '4、10', '午' => '5、11', '未' => '6、12',
            '申' => '1、7', '酉' => '2、8', '戌' => '3、9', '亥' => '4、10', '子' => '5、11', '丑' => '6、12',
        ];
        if (BaziExt::getXianChongDz($dzY, $dzY2) || BaziExt::getXianChongDz($dzY2, $dzY)) {
            $str = $list[0];
        } elseif (BaziExt::getXianXinDz($dzY, $dzY2) || BaziExt::getXianXinDz($dzY2, $dzY)) {
            $str = $list[1];
        } elseif ($this->getSanHeByDz($dzY . $dzY2) || $this->getLiuHeByDz($dzY . $dzY2)) {
            $str = $list[2];
        } else {
            $str = $list[3];
        }
        $sxf = $this->fLunar->getZodiac();
        $sxm = $this->mLunar->getZodiac();
        $goodList = $this->getMonthUsed();
        $goodList1 = [];
        foreach ($goodList as $v) {
            $goodList1[] = $this->monthToCn($v) . '月';
        }
        $m1 = explode('、', $list2[$dzY2]);
        $f1 = explode('、', $list3[$dzY2]);
        $m1Arr = [
            $this->monthToCn($m1[0]) . '月', $this->monthToCn($m1[1]) . '月',
        ];
        $f1Arr = [
            $this->monthToCn($f1[0]) . '月', $this->monthToCn($f1[1]) . '月',
        ];
        $result = [
            'title' => $str,
            'chong' => [
                $this->getSxChong($sxm), $this->getSxChong($sxf),
            ],
            'good' => implode('、', $goodList1),
            'm' => implode('、', $m1Arr),
            'f' => implode('、', $f1Arr),
        ];
        return $result;
    }

    /**
     * 吉日
     * @return array[]
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = strtotime($this->oLunar->dateTime->format('Y-m-d'));
        $endTime = strtotime("+{$this->orginData['plan_time']} month", $startTime);
        $jiNianM = $this->mLunar->getLunarTganDzhi();
        $jiNianW = $this->fLunar->getLunarTganDzhi();
        $userDz = [
            $jiNianM['y'][1], $jiNianM['d'][1], $jiNianW['y'][1], $jiNianW['d'][1],
        ];
        $result = [];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];

        $listNo = [
            '7_15' => "七月半",
            '10_1' => "寒衣",
            '9_9' => "重阳",
        ];
        // 生肖相冲
        $fuMuChong = $this->getChongFumu();
        $liYue = $this->getMonthUsed();
        $listMonthNumber = [
            '正月' => 1, '二月' => 2, '三月' => 3, '四月' => 4, '五月' => 5, '六月' => 6,
            '七月' => 7, '八月' => 8, '九月' => 9, '十月' => 10, '十一月' => 11, '十二月' => 12,
        ];
        $daLi = [];//大利月
        $xiaoLi = [];//小利月
        foreach ($this->getDaLiYue() as $v) {
            if (!isset($listMonthNumber[$v])) {
                continue;
            }
            $daLi[] = $listMonthNumber[$v];
        }
        foreach ($this->getXiaoLiYue() as $v) {
            if (!isset($listMonthNumber[$v])) {
                continue;
            }
            $xiaoLi[] = $listMonthNumber[$v];
        }

        $list3 = [
            '天德' => '吉昌之日，有逢凶化吉、趋吉避凶的寓意。有助夫妻琴瑟调和，福泽绵延。',
            '月德' => '吉神相助，家宅顺遂。有助于避免婆媳之间的矛盾，婚后生活幸福圆满。',
            '天德合' => '五行契合之日，调和阴阳，避开与家人相冲的日子，有助夫妻和睦。',
            '月德合' => '五行契合之日，避开对双方造成不利影响的日子，有助家宅顺遂。',
        ];
        $explain = ['jc' => [], 'shen' => [], 'sha' => []];
        $userHourArr = $this->getShi();
        for ($i = 0; $i < 730; $i++) {
            $tmpTime = $startTime + $i * 86400;
            if ($tmpTime > $endTime) {
                break;
            }
            $huangli = Huangli::date($tmpTime);
            $jiXiong = $huangli->getJiXiong();
            $zhiRi = $huangli->getZhiRi();
            $base = $huangli->getLunarByBetween();
            $nongli = $huangli->getNongLi();
            $jiNian = $base['jinian'];
            $shaShen = $zhiRi['shen_sha'];
            $bool = true;
            $noList = [];
            if ($zhiRi['huan_dao'] == '黑道') {
                $bool = false;
            }

            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初一）七月半（七月十五） 七月十四
            if (isset($listNo[$nongliNumberStr])) {
                $noList[] = $listNo[$nongliNumberStr];
                $bool = false;
            }
            // 干支
            $tmpGZ = [
                implode('', $base['jinian']['y']), implode('', $base['jinian']['m']), implode('', $base['jinian']['d']),
            ];
            //            if (in_array('嫁娶', $jiXiong['ji'])) {
            //                $noList[] = "嫁娶";
            //                $bool = false;
            //            }
            $jieQi = Huangli::isJieQi($tmpTime);
            $tomorrowJieQi = Huangli::isJieQi($tmpTime + 86400);
            //
            if ($jieQi == '清明') {
                $noList[] = '清明';
                $bool = false;
            }
            // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天   2农历初1
            if (in_array($tomorrowJieQi, $list2)) {
                if (in_array($tomorrowJieQi, ["立春", "立夏", "立秋", "立冬"])) {
                    //四绝
                    $noList[] = '四绝日';
                } else {
                    $noList[] = '四离日';
                }
                $bool = false;
            }
            // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉 和十五
            if ($base['_nongli']['d'] == 1 || $base['_nongli']['d'] == 15) {
                if ($base['_nongli']['m'] != 10 && $base['_nongli']['m'] != 1) {
                    $noList[] = '朔日';
                }
                $bool = false;
            }
            // 彭祖忌			备注：亥不行嫁
            if ($base['jinian']['d'][1] == '亥') {
                $noList[] = '彭祖';
                $bool = false;
            }
            // 岁破
            if ($this->checkSuiPo($base['jinian']['y'][1] . $base['jinian']['d'][1])) {
                $noList[] = '岁破日';
                $bool = false;
            }

            $monthDzDayDz = $base['jinian']['m'][1] . $base['jinian']['d'][1];
            // 小红沙 流日月支	日支
            if ($this->checkXiaoHongSha($monthDzDayDz)) {
                $noList[] = '小红沙日';
                $bool = false;
            }
            // 披麻,月破,小红沙
            if ($this->checkYuePo($monthDzDayDz)) {
                $noList[] = '月破日';
                $bool = false;
            }
            // 流日月支+受死日
            if ($this->checkShouSi($monthDzDayDz)) {
                $noList[] = '受死日';
                $bool = false;
            }
            if ($this->checkPiMa($monthDzDayDz)) {
                $noList[] = '披麻日';
                $bool = false;
            }
            // 杨公忌+九毒日+天地交泰
            if ($this->checkYangGongOnly($nongliNumberStr)) {
                $noList[] = '杨公忌日';
                $bool = false;
            }
            // 三娘煞
            if ($this->checkSanNiangSha($tmpGZ[2], (int)$base['_nongli']['d'])) {
                $noList[] = '三娘煞';
                $bool = false;
            }
            // 正四废日
            if ($this->checkZhengSiFeiRi($base['jinian']['m'][1], $tmpGZ[2])) {
                $noList[] = '四废日';
                $bool = false;
            }
            // 根据男女双方生年分别去掉相冲、相刑日。 年支+流日支
            foreach ($userDz as $v1) {
                if (BaziCommon::getXianChong($v1 . $jiNian['d'][1])) {
                    $bool = false;
                    break;
                }
            }
            foreach ($userDz as $v1) {
                if (BaziCommon::getXianXin($jiNian['d'][1] . $v1)) {
                    $bool = false;
                    break;
                }
            }
            $tmpChongFm = [];
            foreach ($fuMuChong as $k => $v) {
                if (empty($v) || !isset($v['dz'])) {
                    continue;
                }
                if ($base['jinian']['d'][1] == $v['dz']) {
                    unset($v['dz']);
                    $tmpChongFm[] = $v;
                    $bool = false;
                }
            }
            // 男女命
            //            if ($this->checkMing($jiNianM, $base['jinian']['m'][1]) || $this->checkMing($jiNianW, $base['jinian']['m'][1], 1)) {
            //                $noList[] = $this->getBadRes('男女命');
            //                $bool = false;
            //            }
            $jianChu = $huangli->getJianChu();
            // 去掉建除中的平日、收日、闭日
            //            if (in_array($jianChu, ['平', '收', '闭'])) {
            //                $bool = false;
            //            }
            $jcType = in_array($jianChu, ['危', '破', '闭', '执']) ? 0 : 1;
            unset($jiNian['h']);
            $dgz = implode('', $jiNian['d']);
            $sanSha = Huangli::getSanSha($jiNian['d'][1]);
            $weekCn = Huangli::getWeekChs($tmpTime);
            $tmp = [
                'gongli' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'week' => $weekCn,
                'zhoumo' => in_array($weekCn, ['星期六', '星期日']) ? 1 : 0,
                'nongli' => $base['nongli']['m'] . $base['nongli']['d'],
                // 生肖
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'type' => 'buyi',
                'type_title' => '不宜',
                'zheng_chong' => BaziCommon::getChongBygz($dgz),
                // 煞向
                'sha_xian' => str_replace('煞', '', $sanSha[0]),
                'type_hl' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                'jishen' => $jiXiong['jishen'],
                'xiong' => $jiXiong['xiong'],
                'jianchu' => $jianChu,
                // 星神结果
                'shensha' => $shaShen,
                'hour' => [],
                'reason' => [],
                'fenxi' => [],
                'pos' => $this->getPos($base['jinian']['d'][0]),
            ];
            $leapStr = $nongli['leap'] ? '闰' : '';
            $keyStr = $nongli['y'] . '年' . $leapStr . $nongli['m'] . '月';
            $monthNum = $nongli['m'];
            $liYueStr = in_array($monthNum, $daLi) ? '大利月' : '';
            if (empty($liYueStr)) {
                $liYueStr = in_array($monthNum, $xiaoLi) ? '小利月' : '';
            }
            $num = $result[$keyStr]['num'] ?? 0;
            if ($bool) {
                $tmp['hour'] = $userHourArr[$jiNian['d'][1]];
                $num++;
                $reason = [];
                $jishenArr = array_intersect($jiXiong['jishen'], ['天德', '月德', '天德合', '月德合']);
                $list = ['不将', '季分', '三合', '岁德', '岁德合', '红鸾天喜', '显星', '曲星', '传星', '天德', '天德合', '月德', '月德合', '天赦', '天愿', '月恩', '四相', '时德'];
                foreach ($list as $v) {
                    if (in_array($v, $jiXiong['jishen'])) {
                        $reason[] = $v;
                    }
                }
                if (!$jcType) {
                    $tmp['jianchu'] = '';
                }
                // 流日日支+女命年支
                $strdzMyz = $base['jinian']['d'][1] . $jiNianW['y'][1];
                // 流日月支+流日日支
                $strMzDz = $base['jinian']['m'][1] . $base['jinian']['d'][1];
                $resultKey = 'ping';
                $tmp['type_title'] = '吉';
                $tmp['detail'] = '夫妇荣昌，有助婚后生活和谐幸福。';
                if (in_array($base['_nongli']['m'], $liYue) && !empty($jishenArr)) {
                    $jiShen = current($jishenArr);
                    $tmp['detail'] = $list3[$jiShen] ?? '';
                    $tmp['type_title'] = '大吉';
                    $resultKey = 'da';
                } elseif ($this->checkKeMingLiuHe($strdzMyz)) {
                    $tmp['type_title'] = '小吉';
                    $tmp['detail'] = '对女方有所助益，且与男方命局匹配，有助夫妻琴瑟和谐。';
                    $resultKey = 'xiao';
                } elseif ($this->checkKeMingSanHe($strdzMyz)) {
                    $tmp['type_title'] = '小吉';
                    $tmp['detail'] = '对女方有一定助益，且与男方命局匹配，有利于夫妻感情发展。';
                    $resultKey = 'xiao';
                } elseif (in_array($base['_nongli']['m'], $daLi)) {
                    $tmp['detail'] = '适逢女方出嫁最佳月份，剔除凶日，避免妨害翁姑长辈。';
                    $tmp['type_title'] = '吉';
                } elseif (in_array($base['_nongli']['m'], $xiaoLi)) {
                    $tmp['detail'] = '适逢女方出嫁次佳月份，剔除凶日，减少婚姻灾劫。';
                    $tmp['type_title'] = '吉';
                } elseif ($this->checkKeNeiLiuHe($strMzDz)) {
                    $tmp['detail'] = '五行凝聚力稍弱，但是为有情之日，琴瑟和谐。';
                    $tmp['type_title'] = '吉';
                } elseif ($this->checkKeNeiSanHe($strMzDz)) {
                    $tmp['detail'] = '五行凝聚力强，为吉配之日，有利于夫妻感情发展。';
                    $tmp['type_title'] = '吉';
                }
                $tmp['type'] = $resultKey;
                $tmp['reason'] = $reason;
            } else {
                if ($jcType) {
                    $tmp['jianchu'] = '';
                }
                if ($tmp['type_hl']) {
                    $tmp['shensha'] = '';
                }
                $tmp['reason'] = $noList;
                $tmp['xiong'] = array_unique(array_merge($jiXiong['xiong'], $noList));
                $tmp['fenxi'] = $this->getLiuGx($jiNian);
                $tmp['fumu'] = $tmpChongFm;
            }
            foreach ($tmp['reason'] as $v1) {
                if (isset($explain['shen'][$v1])) {
                    continue;
                }
                $explain['shen'][$v1] = $this->getExplain($v1);
            }
            if (!empty($tmp['jianchu']) && !isset($explain['jc'][$jianChu])) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if (!empty($tmp['shensha']) && !isset($explain['sha'][$shaShen])) {
                $explain['sha'][$shaShen] = $this->getShaRes($shaShen);
            }
            $result[$keyStr]['title'] = $keyStr;
            $result[$keyStr]['num'] = $num;
            $result[$keyStr]['liyue'] = $liYueStr;
            $result[$keyStr]['info'][] = $tmp;
        }
        return [
            'list' => array_values($result),
            'explain' => $explain,
        ];
    }

    /**
     * 判断是否三合
     * @param string $dz 两个地支拼成的字符串
     * @return bool
     */
    private function getSanHeByDz(string $dz): bool
    {
        $sanheList = [
            '子辰', '丑巳', '寅午', '卯亥', '辰子', '巳酉', '午寅', '未亥', '申辰', '酉巳', '戌午', '亥卯',
            '子申', '丑酉', '寅戌', '卯未', '辰申', '巳丑', '午戌', '未卯', '申子', '酉丑', '戌寅', '亥未',
        ];
        return in_array($dz, $sanheList);
    }

    /**
     * 判断地支六合
     * @param string $dz 两个地支拼成的字符串
     * @return bool
     */
    private function getLiuHeByDz(string $dz): bool
    {
        $list = [
            ['子丑', '丑子', '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅'],
        ];
        return in_array($dz, $list);
    }

    /**
     * 根据生肖获得相冲生肖
     * @param string $sx 生肖
     * @return string
     */
    private function getSxChong(string $sx): string
    {
        $chong = [
            '鼠' => '马', '牛' => '羊', '虎' => '猴', '兔' => '鸡', '龙' => '狗', '蛇' => '猪',
            '马' => '鼠', '羊' => '牛', '猴' => '虎', '鸡' => '兔', '狗' => '龙', '猪' => '蛇',
        ];
        return $chong[$sx];
    }

    /**
     * 获得符合条件的月份
     * @return array
     */
    private function getMonthUsed(): array
    {
        $list1 = [
            '正月' => 1, '二月' => 2, '三月' => 3, '四月' => 4, '五月' => 5, '六月' => 6,
            '七月' => 7, '八月' => 8, '九月' => 9, '十月' => 10, '十一月' => 11, '十二月' => 12,
        ];
        $goodList = array_merge($this->getDaLiYue(), $this->getXiaoLiYue());
        $goodList = array_unique($goodList);
        $goodMonth = [];
        foreach ($goodList as $v) {
            if (isset($list1[$v])) {
                $goodMonth[] = $list1[$v];
            }
        }
        sort($goodMonth);
        return $goodMonth;
    }

    /**
     * 获得用户全部吉时
     * @return array
     */
    private function getShi(): array
    {
        $list = [
            '子' => '23-1', '丑' => '1-3', '寅' => '3-5', '卯' => '5-7', '辰' => '7-9', '巳' => '9-11',
            '午' => '11-13', '未' => '13-15', '申' => '15-17', '酉' => '17-19', '戌' => '19-21', '亥' => '21-23',
        ];
        $list1 = [
            '子' => ['丑', '辰', '申'],
            '丑' => ['子', '巳', '酉', '亥'],
            '寅' => ['亥', '午', '戌', '卯', '辰'],
            '卯' => ['戌', '亥', '未', '辰', '寅'],
            '辰' => ['酉', '子', '申', '寅', '卯'],
            '巳' => ['申', '酉', '丑', '午', '未'],
            '午' => ['未', '寅', '戌', '巳'],
            '未' => ['午', '亥', '卯', '巳'],
            '申' => ['巳', '辰', '子', '酉', '戌'],
            '酉' => ['辰', '巳', '丑', '戌', '申'],
            '戌' => ['卯', '午', '寅', '申', '酉'],
            '亥' => ['寅', '卯', '未', '子', '丑'],
        ];
        $mYgz = $this->mLunar->getLunarGanzhiYear();
        $fYgz = $this->fLunar->getLunarGanzhiYear();
        $result = [];
        foreach ($list1 as $k => $item) {
            $num = 0;
            $tmpRes = [];
            foreach ($item as $v) {
                if (BaziCommon::getXianChong($v . $mYgz[1]) || BaziCommon::getXianChong($v . $fYgz[1])) {
                    continue;
                }
                if (BaziCommon::getXianXin($v . $mYgz[1]) || BaziCommon::getXianXin($v . $fYgz[1])) {
                    continue;
                }
                $tmpRes[] = $v . '时 ' . $list[$v] . '时';
                $num++;
                if ($num >= 3) {
                    break;
                }
            }
            $result[$k] = $tmpRes;
        }
        return $result;
    }

    /**
     * 获得方位
     * @param $tg
     * @return array
     */
    private function getPos($tg): array
    {
        $listXi = [
            '甲' => '东北', '乙' => '西北', '丙' => '西南', '丁' => '正南', '戊' => '东南', '己' => '东北', '庚' => '西北', '辛' => '西南', '壬' => '正南', '癸' => '东南',
        ];
        $listfu = [
            '甲' => '正北', '乙' => '西南', '丙' => '西北', '丁' => '东南', '戊' => '东北', '己' => '正北', '庚' => '西南', '辛' => '西北', '壬' => '东南', '癸' => '东北',
        ];
        $listCai = [
            '甲' => '东北', '乙' => '东北', '丙' => '西南', '丁' => '西南', '戊' => '正北', '己' => '正北', '庚' => '正东', '辛' => '正东', '壬' => '正南', '癸' => '正南',
        ];
        $result = [
            'up' => $listXi[$tg], 'down' => $listfu[$tg],
        ];
        if (in_array($tg, ['丙', '辛'])) {
            $result = [
                'up' => $listfu[$tg], 'down' => $listCai[$tg],
            ];
        }
        return $result;
    }

    /**
     * 流日和用户关系
     * @param array $jiNian
     * @return array
     */
    protected function getLiuGx($jiNian): array
    {
        $jiNianM = $this->mLunar->getLunarTganDzhi();
        $jiNianF = $this->fLunar->getLunarTganDzhi();
        $ydzM = $jiNianM['y'][1];
        $ddzM = $jiNianM['d'][1];
        $ydzF = $jiNianF['y'][1];
        $ddzF = $jiNianF['d'][1];
        $ddz = $jiNian['d'][1];
        $list = [
            '男' => [
                's' => [], 'y' => [], 't' => [],
            ],
            '女' => [
                's' => [], 'y' => [], 't' => [],
            ],
        ];
        $listChong = [
            ['男', '年', $ydzM . $ddz],
            ['男', '日', $ddzM . $ddz],
            ['女', '年', $ydzF . $ddz],
            ['女', '日', $ddzF . $ddz],
        ];
        foreach ($listChong as $k => $v) {
            $tKey = $v[0];
            if (BaziCommon::getXianChong($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '冲';
            }
            if (BaziCommon::getXianXin($v[2])) {
                $list[$tKey]['y'][] = $v[1];
                $list[$tKey]['t'][] = '刑';
            }
        }
        $result = [];
        foreach ($list as $k => $v) {
            if (empty($v['t'])) {
                continue;
            }
            $result[] = [
                't' => implode('', array_unique($v['t'])) . $k,
                'y' => array_unique($v['y']),
            ];
        }
        return $result;
    }

    /**
     * 获得解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '不将' => '有利嫁娶的传统吉日，当日结婚，寓意新人以后的生活平平顺顺。',
            '季分' => '传统嫁娶吉日，当日成婚，寓意福泽绵长。',
            '三合' => '寓意新人互生互利的吉日，可作为结婚的日子选用。',
            '岁德' => '德神护佑的吉日，积福之日，福气汇聚。',
            '岁德合' => '德神护佑，积福的日支，有福气汇聚的象征。',
            '红鸾天喜' => '有利嫁娶的吉日，红鸾报喜，夫妻恩爱。',
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺。',
            '曲星' => '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄，万事称心的寓意。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜进行入宅、婚嫁等事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，婚嫁大吉。',
            '四相' => '拥有四时王相贵气的日子，纯粹的小吉日，适合婚嫁诸喜事。',
            '时德' => '得到天地舒畅之气，得到四时之气的祝福，属天赐良日。',
            '岁破日' => '岁破有大事勿用的寓意，婚姻乃是终身大事，不可当日举行。',
            '小红沙日' => '红沙日有诸事不宜的说法，需要注意避开。',
            '受死日' => '该日有晦气风流，大事勿用的说法。',
            '披麻日' => '披麻主孝丧之事，寓意不吉。',
            '月破日' => '日值岁破，大事不宜。破日有破败之意，日月相冲，是为大耗。',
            '孤鸾日' => '孤鸾日有，犯之婚姻凶，难长久的说法。',
            '阴差阳错日' => '阴差阳错有着事与愿违的寓意，婚姻需要心想事成，故而应该避讳该日。',
            '朔日' => '此系恶鬼聚拢之辰，忌结婚进宅会客作乐。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '归忌日' => '该日寓意不好，该日有忌远行归家、迁徙移动、娶妇嫁女的说法。',
            '四废日' => '日值四废，作事不易成功，容易有始无终。',
            '四离日' => '日值四离，大事勿用。',
            '四绝日' => '古话有云，四绝日订婚嫁娶，犯之不顺，需避之。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来结婚有些不合时宜。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，诸喜事不宜。',
            '重阳' => '当日有大事勿用的说法，忌讳结婚。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意不吉。',
            '彭祖' => '彭祖百忌中有“亥不嫁娶，不利新郎”，解释为，亥日不能结婚。',
            '杨公忌日' => '这日有诸事不宜说法，结婚最好避开该日。',
            '三娘煞' => '在婚姻上该日属于大忌之日，古话有，迎亲嫁娶无男女。',
            '嫁娶' => '今日宜忌中有忌嫁娶，因而需要规避。',
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定。宜采纳、商贾、拜访、考试，婚嫁，移居等。',
            '满' => '满日在吉日中有“圆满”的含义，对于结婚来说，自然有着比较好的寓意。',
            '成' => '凡事有所成，因而该日诸事皆可办理，用于结婚乃是大吉。',
            '开' => '寓意着开通顺利，乃是一个好日子，对于结婚来说算是大吉。',
            '建' => '对于事情上来说，该日有着开始的说法，因而寓意较好，可选该日。',
            '平' => '平常、平分的日子，无吉无凶，结婚可选。',
            '收' => '有收成，收获的意思，该日结婚大吉。',
            '危' => '危日象征危机，是危险的一天，忌讳诸多喜事。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日有指破裂，冲破的含义，忌办一切喜凶事。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进的意思。这一日有小破耗的说法。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 获得十二星神解释
     * @param $str
     * @return string
     */
    protected function getShaRes($str): string
    {
        $list = [
            '青龙' => '传统习俗中的吉利日子，寓意成功，白头偕老。',
            '明堂' => '传统习俗中的吉利日子，寓意贵人相助，事情必定成功，能够恩爱有加。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克，该日万事皆忌。',
            '朱雀' => '天讼星，利于一些邢讼，常人凶，喜事忌用。',
            '金匮' => '传统习俗中的吉利日子，用于结婚乃是大吉。',
            '白虎' => '天杀星，乃凶相的征兆。',
            '玉堂' => '传统习俗中的吉利日子，用于结婚为上选。',
            '天牢' => '镇神星，阴人用事皆吉，其余都不利。',
            '玄武' => '该神属天狱星，在一些大事上略有机会，有多小人嘴舌之兆。',
            '司命' => '当天用事大吉，可用于结婚。但忌讳夜间夜间宴请宾客。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '天德' => '天德黄道吉日占得天时，有三合旺气，是上等的吉日。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 月份数字转中文
     * @param int $num
     * @return string
     */
    protected function monthToCn(int $num): string
    {
        $monthAlias = $this->monthAlias;
        return $monthAlias[$num - 1] ?? '';
    }

    /**
     * 获得父母生肖相冲数据
     * @return array
     */
    protected function getChongFumu(): array
    {
        $data = $this->orginData;
        $sxBase = new SxBase();
        $chongDz = $this->sxChongList;
        $sxList = $this->sxList;
        $sxNumList = [
            ['男', '父', $data['malefsx']],
            ['男', '母', $data['malemsx']],
            ['女', '父', $data['femalefsx']],
            ['女', '母', $data['femalemsx']],
        ];
        $res = [];
        foreach ($sxNumList as $k => $v) {
            if ($v[2] < 1) {
                continue;
            }
            $res[] = [
                'sex' => $v[0], 'fm' => $v[1], 'sx' => $sxList[$v[2]], 'dz' => $chongDz[$v[2]],
            ];
        }
        return $res;
    }
}
