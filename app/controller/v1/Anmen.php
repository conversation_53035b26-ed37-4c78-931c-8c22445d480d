<?php
// +----------------------------------------------------------------------
// | Anmen.安门吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\traits\DateConvertTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Anmen
{
    use DateConvertTraits;

    /**
     * 用户实例
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 与用户相冲相刑的地支
     * @var array
     */
    protected array $chongXinU = [];

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 节气数据
     * @var array
     */
    private array $jieQiArr = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 用户生日
            'time' => input('time', '', 'trim'),
            // 性别
            'sex' => input('sex', '', 'trim'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 要取的月份
            'month' => input('month', 1, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|用户生日' => ['require', 'isDateOrTime:用户生日'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'month|月分' => ['require', 'between:1,24'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $this->chongXinU = $this->getChongXinDz();
        $dayList = $this->getDayList();
        $result = [
            // 八字基础
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 与用户年支或日支冲刑的地支
            'chong_xin' => $this->chongXinU,
            // 测算日期列表
            'ji' => $dayList,
        ];
        return $result;
    }

    /**
     * 获得跟用户年支和日支相冲相刑的地支
     * @return array
     */
    protected function getChongXinDz(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $arr = [
            $jiNian['y'][1],
        ];
        $dzArr = Calendar::DI_ZHI;
        $res = [];
        foreach ($dzArr as $v) {
            foreach ($arr as $v1) {
                if (BaziCommon::getXianChong($v1 . $v)) {
                    $res[$v] = $v;
                    continue;
                }
                if (BaziCommon::getXianXin($v1 . $v)) {
                    $res[$v] = $v;
                }
            }
        }
        return array_values($res);
    }

    /**
     * 日期分析
     * @return array
     * @throws Exception
     */
    protected function getDayList()
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $uddz = $jiNian['d'][1];
        $uydz = $jiNian['y'][1];
        $result = [];
        $explainRes = [];
        // 测算天数
        $limitNum = $this->getAcquisitionDays($this->orginData['otime'], $this->orginData['month']);
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳节',
            '10_1' => '寒衣节',
        ];
        // 煞向
        $listShaXian = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $objShaShen = new ShaShen();
        for ($i = 1; $i <= $limitNum; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $keyYm = date('Y年m月', $time);
            $year1 = (int)date('Y', $time);
            $huangli = Huangli::date($time);
            $base1 = $huangli->getLunarByBetween();
            $jiNian1 = $base1['jinian'];
            $ziRi = $huangli->getZhiRi();
            $shenSha = $ziRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $ytg1 = $jiNian1['y'][0];
            $ydz1 = $jiNian1['y'][1];
            $mdz1 = $jiNian1['m'][1];
            $ddz1 = $jiNian1['d'][1];
            $dtg1 = $jiNian1['d'][0];
            $dgz1 = implode('', $jiNian1['d']);
            $mzDz = $mdz1 . $ddz1;
            $bool = true;
            $reason = [];
            $hdType = $ziRi['huan_dao'] == '黑道' ? 0 : 1;
            if (!$hdType) {
                $bool = false;
            }
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初一）七月半（七月十五）
            if (in_array($nongliNumberStr, ['7_15', '9_9', '10_1'])) {
                $noList[] = $listNo[$nongliNumberStr] ?? '';
                $bool = false;
            }
            $jianChu = $huangli->getJianChu();
            //            $jcType = 1;
            //            if (in_array($jianChu, ['平', '收', '闭'])) {
            //                $jcType = 0;
            //                $bool = false;
            //            }
            $jieQi = $this->getJieQiByYear($year1);
            if (date('Y-m-d', strtotime($jieQi['清明'])) == $timeStr) {
                $bool = false;
                $reason[] = '清明节';
            }
            if ($ziRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            $chongXinStr = BaziCommon::getXianChong($uydz . $ddz1) ? '冲' : '';
            if (BaziCommon::getXianXin($uydz . $ddz1)) {
                $chongXinStr .= '刑';
            }
            $chongXin = [];
            if ($chongXinStr) {
                $chongXin[] = ['k' => '年', 'd' => $chongXinStr];
                $bool = false;
            }
            if ($dgz1 == '庚寅') {
                $reason[] = '庚寅日';
            }
            if ($objShaShen->getZaiSha($mdz1 . $ddz1)) {
                $reason[] = '灾煞';
            }
            if ($objShaShen->getJieSha($mdz1 . $ddz1)) {
                $reason[] = '劫煞';
            }
            if (BaziCommon::getXianChong($ydz1 . $ddz1)) {
                $reason[] = '岁破';
            }
            if ($objShaShen->getSiFei($mdz1, $dgz1)) {
                $reason[] = '四废日';
            }
            // 去掉三煞、五黄煞、太岁遇五黄、岁破方、天地官符、阴府、暗建、四废、月刑、咸池、大时、天吏、致死、月厌、地火、五墓、庚寅日。十二建除：平、收、闭。晦日、五不遇时。
            if ($this->checkYueXin($mdz1, $ddz1)) {
                $reason[] = '月刑';
            }
            //            if ($objShaShen->getXianChi($ydz1 . $ddz1)) {
            //                $reason[] = '咸池';
            //            }
            if ($this->checkTianLi($mdz1, $ddz1)) {
                $reason[] = '天吏';
            }
            //            if ($this->checkYueYan($mdz1, $ddz1)) {
            //                $reason[] = '月厌';
            //            }
            if ($this->checkWuMu($mdz1, $dgz1)) {
                $reason[] = '五墓';
            }
            if ($this->checkShouSi($mdz1 . $ddz1)) {
                $reason[] = '受死';
            }
            $jiNianTmp = $jiNian1;
            unset($jiNianTmp['h']);
            $position = $huangli->getPosition();
            $hehai = $huangli->getTodayHeHai();
            $jiXiongYiJi = $huangli->getJiXiong();
            if (count($reason) > 0) {
                $bool = false;
            }
            $tmpRes = [
                'date' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                    // 星期
                    'week' => Huangli::getWeekChs($time),
                    'jinian' => $jiNianTmp,
                    'nongli' => $base1['nongli'],
                    '_nongli' => $base1['_nongli'],
                    // 生肖
                    'shengxiao' => $base1['shengxiao'],
                ],
                // 财神
                'cai_shen' => $position['cai_shen'],
                // 煞向
                'sha_xian' => str_replace('煞', '', $listShaXian[$ddz1]),
                // 相冲
                'chong' => $hehai['xian_chong'],
                // 吉神
                'jishen' => $jiXiongYiJi['jishen'],
                // 凶神
                'xiong' => $jiXiongYiJi['xiong'],
                // 算法合不合，不合为空
                'type' => 'buyi',
                // 黄道 1 黑道 0
                'hd_type' => $hdType,
                // 值日（星神）
                'xing_shen' => $ziRi['xing_shen'],
                'shensha' => $shenSha,
                'jianchu' => $jianChu,
                'reason' => $reason,
                'fenxi' => $chongXin,
            ];
            $num = $result[$keyYm]['num'] ?? 0;
            if ($bool) {
                $reason = [];
                $typeArr = [];
                $num++;
                // 天德、月德、天德合、月德合、四相、时德、三合、六合、月恩、天愿、五富、天赦、贵登天门时、四大吉时
                if ($objShaShen->getTianDe($mzDz) || $objShaShen->getTianDe($mdz1 . $dtg1)) {
                    $typeArr['da'] = 'da';
                    $reason[] = '天德';
                }
                if ($objShaShen->getTianDeHe($mzDz) || $objShaShen->getTianDeHe($mdz1 . $dtg1)) {
                    $typeArr['da'] = 'da';
                    $reason[] = '天德合';
                }
                if ($objShaShen->getYueDe($mdz1 . $ddz1)) {
                    $typeArr['xiao'] = 'xiao';
                    $reason[] = '月德';
                }
                if ($objShaShen->getYueDeHe($mdz1 . $ddz1) || $objShaShen->getYueDeHe($mdz1 . $dtg1)) {
                    $typeArr['xiao'] = 'xiao';
                    $reason[] = '月德合';
                }
                if ($this->checkShiDe($mdz1 . $ddz1)) {
                    $typeArr['xiao'] = 'xiao';
                    $reason[] = '时德';
                }
                if ($this->checkYueEn($mdz1 . $dtg1)) {
                    $typeArr['xiao'] = 'xiao';
                    $reason[] = '月恩';
                }
                if ($this->checkTianSe($mdz1, $dgz1)) {
                    $reason[] = '天赦';
                }
                if ($this->checkSiXian($mdz1, $dtg1)) {
                    $reason[] = '四相';
                }
                if (BaziCommon::liuHeDz($mdz1 . $ddz1)) {
                    $reason[] = '六合';
                }
                $sanHe = BaziCommon::getSanHeDz2([[$mdz1, $ddz1]]);
                if ($sanHe[2]) {
                    $reason[] = '三合';
                }
                if ($this->checkWuFu($mdz1 . $ddz1)) {
                    $reason[] = '五富';
                }
                $tmpRes['reason'] = $reason;
                if (empty($typeArr)) {
                    $type = 'ping';
                } else {
                    $type = current($typeArr);
                }
                $hourList = $this->getJishi($jiNian1['d']);
                $tmpHourRes = [];
                foreach ($hourList as $v5) {
                    $tmpHourRes[] = [
                        'dz' => $v5['dz'],
                        'h' => $v5['h'],
                        'chong' => $v5['chong'],
                    ];
                }
                $jishenRes = array_values(array_unique(array_merge($reason, $jiXiongYiJi['jishen'])));
                $tmpRes['jishen'] = $jishenRes;
                $tmpRes['type'] = $type;
                $tmpRes['hour'] = $tmpHourRes;
                $tmpres['jianchu'] = $jianChu;
                if (!$hdType) {
                    $tmpRes['shensha'] = '';
                }
            } else {
                if ($hdType) {
                    $tmpRes['shensha'] = '';
                }
            }
            foreach ($reason as $v1) {
                if (!isset($explainRes[$v1])) {
                    $explainRes[$v1] = $this->getExplain($v1);
                }
            }
            if (!empty($tmpRes['shensha']) && !isset($explainRes[$shenSha])) {
                $explainRes[$shenSha] = $this->getExplain($shenSha);
            }
            $result[$keyYm]['title'] = $keyYm;
            $result[$keyYm]['num'] = $num;
            $result[$keyYm]['list'][] = $tmpRes;
        }
        return [
            'list' => array_values($result),
            'explain' => $explainRes,
        ];
    }

    /**
     * 月刑
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueXin(string $mdz, string $ddz): bool
    {
        $list = ['寅巳', '卯子', '辰辰', '巳申', '午午', '未丑', '申寅', '酉酉', '戌未', '亥亥', '子卯', '丑戌'];
        return in_array($mdz . $ddz, $list);
    }


    /**
     * 天吏
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianLi(string $mdz, string $ddz): bool
    {
        $list = ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月厌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueYan(string $mdz, string $ddz): bool
    {
        $list = ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 五墓
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkWuMu(string $mdz, string $dgz): bool
    {
        $list = ['寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰'];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 五不遇时
     * @param string $dtg 流日天干
     * @param string $hdz 流时地支
     * @return bool
     */
    protected function checkWuBuYuShi(string $dtg, string $hdz): bool
    {
        $listWuBu = [
            '甲午', '乙巳', '丙辰', '丁卯', '戊寅', '己丑', '庚子', '辛酉', '壬申', '癸未',
        ];
        return in_array($dtg . $hdz, $listWuBu);
    }

    /**
     * 四相
     * @param string $mdz 月地支
     * @param string $rtg 日地支
     * @return bool
     */
    protected function checkSiXian(string $mdz, string $rtg): bool
    {
        $bool = false;
        switch ($mdz) {
            case '寅':
            case '卯':
            case '辰':
                if (in_array($rtg, ['丙', '丁'])) {
                    $bool = true;
                }
                break;
            case '巳':
            case '午':
            case '未':
                if (in_array($rtg, ['戊', '己'])) {
                    $bool = true;
                }
                break;
            case '申':
            case '酉':
            case '戌':
                if (in_array($rtg, ['壬', '癸'])) {
                    $bool = true;
                }
                break;
            default:
                if (in_array($rtg, ['甲', '乙'])) {
                    $bool = true;
                }
                break;
        }
        return $bool;
    }

    /**
     * 时德
     * @param string $str 月支+日支
     * @return bool
     */
    protected function checkShiDe(string $str): bool
    {
        $list = ['寅午', '卯午', '辰午', '巳辰', '午辰', '未辰', '申子', '酉子', '戌子', '亥寅', '子寅', '丑寅'];
        return in_array($str, $list);
    }

    /**
     * 天赦
     * @param string $mdz 月地支
     * @param string $rgz 日干支
     * @return bool
     */
    protected function checkTianSe(string $mdz, string $rgz): bool
    {
        $bool = false;
        switch ($mdz) {
            case '寅':
            case '卯':
            case '辰':
                if ($rgz === '戊寅') {
                    $bool = true;
                }
                break;
            case '巳':
            case '午':
            case '未':
                if ($rgz === '甲午') {
                    $bool = true;
                }
                break;
            case '申':
            case '酉':
            case '戌':
                if ($rgz === '戊申') {
                    $bool = true;
                }
                break;
            default:
                if ($rgz === '甲子') {
                    $bool = true;
                }
                break;
        }
        return $bool;
    }

    /**
     * 天愿
     * @param string $str 月支+日干支
     * @return bool
     */
    protected function checkTianYuan(string $str): bool
    {
        $list = ['寅甲午', '卯甲戌', '辰乙酉', '巳丙子', '午丁丑', '未戊午', '申甲寅', '酉丙辰', '戌辛卯', '亥戊辰', '子甲子', '丑癸未'];
        return in_array($str, $list);
    }

    /**
     * 月恩
     * @param string $str 月支+日干
     * @return bool
     */
    protected function checkYueEn(string $str): bool
    {
        $list = ['寅丙', '卯丁', '辰庚', '巳己', '午戊', '未辛', '申壬', '酉癸', '戌庚', '亥乙', '子甲', '丑辛'];
        return in_array($str, $list);
    }

    /**
     * 五富
     * @param string $str 月支+日支
     * @return bool
     */
    protected function checkWuFu(string $str): bool
    {
        $list = ['寅亥', '卯寅', '辰巳', '巳申', '午亥', '未寅', '申巳', '酉申', '戌亥', '亥寅', '子巳', '丑申'];
        return in_array($str, $list);
    }

    /**
     * 吉时
     * @param array $gzDay 日干支
     * @return array
     */
    protected function getJishi(array $gzDay): array
    {
        $arr = Calendar::DI_ZHI;
        $list1 = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $list2 = [
            [
                '子' => '甲', '丑' => '乙', '寅' => '丙', '卯' => '丁', '辰' => '戊', '巳' => '己',
                '午' => '庚', '未' => '辛', '申' => '壬', '酉' => '癸', '戌' => '甲', '亥' => '乙',
            ],
            [
                '子' => '丙', '丑' => '丁', '寅' => '戊', '卯' => '己', '辰' => '庚', '巳' => '辛',
                '午' => '壬', '未' => '癸', '申' => '甲', '酉' => '乙', '戌' => '丙', '亥' => '丁',
            ],
            [
                '子' => '戊', '丑' => '己', '寅' => '庚', '卯' => '辛', '辰' => '壬', '巳' => '癸',
                '午' => '甲', '未' => '乙', '申' => '丙', '酉' => '丁', '戌' => '戊', '亥' => '己',
            ],
            [
                '子' => '庚', '丑' => '辛', '寅' => '壬', '卯' => '癸', '辰' => '甲', '巳' => '乙',
                '午' => '丙', '未' => '丁', '申' => '戊', '酉' => '己', '戌' => '庚', '亥' => '辛',
            ],
            [
                '子' => '壬', '丑' => '癸', '寅' => '甲', '卯' => '乙', '辰' => '丙', '巳' => '丁',
                '午' => '戊', '未' => '己', '申' => '庚', '酉' => '辛', '戌' => '壬', '亥' => '癸',
            ],
        ];
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $list3 = [
            '甲' => 0, '乙' => 1, '丙' => 2, '丁' => 3, '戊' => 4, '己' => 0, '庚' => 1, '辛' => 2, '壬' => 3, '癸' => 4,
        ];
        $listShiTg = $list2[$list3[$gzDay[0]]];
        $jn = $this->lunar->getLunarGanzhiYear();
        $ydz1 = $jn[1];

        $result = [];
        $sxBase = new SxBase();
        foreach ($arr as $v) {
            if (in_array($v, ["子", "丑", "寅", "卯", "戌", "亥"])) {
                continue;
            }
            $tgS = $listShiTg[$v];
            $sx = $sxBase->getsxByDz($v);
            $chongSx = $sxBase->getChong($sx);
            $str = $v . $gzDay[1];
            $str2 = $v . $ydz1;
            if (BaziCommon::getXianChong($str) || BaziCommon::getXianXin($str) || BaziCommon::getXianHai($str)) {
                continue;
            }
            if (BaziCommon::getXianChong($str2) || BaziCommon::getXianPo($str2) || BaziCommon::getXianHai($str2)) {
                continue;
            }
            $result[] = [
                'tg' => $tgS, 'dz' => $v, 'h' => $hourList[$v],
                'chong' => $chongSx['name'], 'sha_xian' => $list1[$v] ?? $list1['子'],
            ];
        }
        return $result;
    }

    /**
     * 根据公历年获得节气数据
     * @param int $year
     * @return array
     */
    private function getJieQiByYear(int $year): array
    {
        if (isset($this->jieQiArr[$year])) {
            return $this->jieQiArr[$year];
        }
        $data = SolarTerm::getAllJieQi($year);
        $this->jieQiArr[$year] = $data;
        return $data;
    }

    /**
     * 受死
     * @param string $str 流日月支+日支
     * @return bool
     */
    private function checkShouSi(string $str): bool
    {
        $list = ['寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉'];
        return in_array($str, $list);
    }

    /**
     * 获得解释
     * @param string $str 键名
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '青龙' => '吉利之日，象征事事如意，顺风顺水。',
            '明堂' => '吉祥之日，有贵人相助之意，预示着事情的成功。',
            '金匮' => '吉日良辰，表示行事顺利，无往不利。',
            '宝光' => '此日吉利，适宜办理各类事务，事半功倍。',
            '玉堂' => '吉利之日，寓意着做任何事情都会顺利成功。',
            '司命' => '大吉之日，做事易成。但夜间不宜设宴款待宾客。',
            '天刑' => '天刑为凶星，主刑克孤独，此日诸事不宜。',
            '朱雀' => '天讼之星，利于诉讼之事，但普通人应避之，不宜办喜事。',
            '白虎' => '天杀凶星，预示着不祥之兆，需小心防范。',
            '天牢' => '镇神之星，适宜阴人行事，其他人则不利。',
            '玄武' => '此神属天狱星，大事上或有转机，但小心口舌之争。',
            '勾陈' => '地狱之星，此时开始的事务难得圆满，有始无终。',
            '清明节' => '此日为扫墓祭祖之日，安门则稍显不宜。',
            '七月半' => '七月半为传统文化中的鬼节，不宜办理喜事。',
            '重阳节' => '此日有不宜大事之说，安门尤为忌讳。',
            '寒衣节' => '寒衣节为四大鬼节之一，寓意不祥，需谨慎行事。',
            '岁煞' => '此日不宜挖掘、修建、搬迁，违者可能影响子孙。',
            '劫煞' => '此日忌兴造，否则易遭劫盗、伤害等不幸。',
            '灾煞' => '此日不宜动工兴建，违者日后或有健康之忧。',
            '五黄煞' => '此日不宜动土施工，否则会招致凶险，包括疾病、灾祸等。',
            '岁破' => '岁破日不宜兴建、搬迁、嫁娶、远行，否则财物损失、家主受害。',
            '阴府煞' => '阴府煞为风水大凶煞，坐山忌之，无论年月日时。主损丁破财。',
            '暗建煞' => '此日动土则伤人甚重，必须避免。',
            '四废日' => '此日百事皆忌，行事难以成功。',
            '月刑' => '此日不宜动土兴工，否则日后多争斗之事。',
            '受死' => '此日大忌，无法化解，重要事务必须避免。',
            '天吏' => '此日为上天凶吏所值，全无生机，极为不吉。',
            '天地官符' => '此日忌修方，否则易生是非、破财伤官。',
            '月厌' => '月厌大祸日，忌嫁娶、出行、搬家等兴工之事。',
            '五墓' => '此日忌营造、起土、嫁娶、出军等事务。',
            '庚寅日' => '此日通常为门神忌日，安门需特别注意避免。',
            '天德' => '天德为上天之福德，此日宜动土兴工、修造房屋。',
            '月德' => '月德为月之德神，此日宜设宴、上任或举行就职典礼。',
            '月德合' => '月德合值日，宜出师、升职、修造、祭祀等事务。',
            '四相' => '四相为四季王相之时，此日宜修造、起工、远行等。',
            '时德' => '时德为四季之德神，此日宜庆赐、宴乐、拜官等。',
            '三合' => '三合为同气不同位之日，宜结婚、交易、修造等。',
            '六合' => '六合为日月星宿相合之时，宜会客、结婚、立契等。',
            '月恩' => '月恩日宜营造、嫁娶、迁徙、祭祀、上官等事务。',
            '天愿' => '天愿为月中善神，此日宜嫁娶、纳财、访友。',
            '天德合' => '天德合为合德之神，此日宜动土、修造、祈福。',
            '五富' => '五富为富足昌盛之神，此日宜兴举运动、经求财富。',
            '天赦' => '天赦为赦罪免过之日，宜施恩、兴造土木等事务。',
        ];
        return $list[$str] ?? '';
    }
}
