<?php
// +----------------------------------------------------------------------
// | Zhuangxiujiri.动土吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\jiri\JiRiBaseTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Zhuangxiujiri
{
    use JiRiBaseTraits;
    use JiRiCheckTraits;

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户生日日历类
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 五行属性
     * @var array
     */
    protected array $wuXingAttr;

    /**
     * 动工吉日
     * @param string $time
     * @param string $sex
     * @param string $otime
     * @param int $limit
     * @return array
     * @throws Exception
     */
    public function index(string $time = '', string $sex = '', string $otime = '', int $limit = 365)
    {
        $data = [
            // 用户生日
            'time' => $time,
            // 性别 0男 1女
            'sex' => $sex,
            // 订单时间
            'otime' => $otime,
            // 限制天数
            'limit' => $limit,
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'sex|性别' => ['require', 'in:0,1'],
                'limit|限制天数' => ['require', 'number', 'between:20,731'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $dayList = $this->getDayList();
        $result = [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 用神五行
            'yongshen' => $xiYong['wx'][1],
            // 喜神五行
            'xishen' => $xiYong['wx'][0],
            // 吉日
            'ji' => $dayList,
            // 风水建议
            'fengshui' => $this->getFengShui($data['otime']),
        ];
        return $result;
    }

    /**
     * 日期列表
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $listHourRes = [
            '木' => [
                '子' => ['23时33分', '23时58分', '0时8分'],
                '丑' => ['1时33分', '2时8分'],
                '寅' => ['3时18分', '3时33分', '4时18分'],
                '卯' => ['5时8分', '5时58分', '6时18分', '6时58分'],
                '辰' => ['7时58分', '8时18分', '8时58分'],
                '巳' => ['9时8分', '9时18分', '9时58分', '10时18分'],
                '午' => ['11时33分', '11时58分', '12时8分'],
                '未' => ['13时33分', '13时58分', '14时18分'],
                '申' => ['16时8分', '16时18分'],
                '酉' => ['17时8分', '17时18分', '17时58分', '18时18分'],
                '戌' => ['19时8分', '19时18分'],
                '亥' => ['21时8分', '21时13分'],
            ],
            '火' => [
                '子' => ['23时37分', '0时12分'],
                '丑' => ['1时37分', '2时12分'],
                '寅' => ['3时37分'],
                '卯' => ['5时52分', '6时52分', '6时57分'],
                '辰' => ['7时12分', '7时32分', '8时12分', '8时52分'],
                '巳' => ['9时17分', '9时22分', '10时17分', '10时22分'],
                '午' => ['11时37分', '11时52分', '12时7分'],
                '未' => ['13时37分', '14时7分', '14时22分'],
                '申' => ['16时2分', '16时17分'],
                '酉' => ['17时17分', '17时22分', '18时17分', '18时22分'],
                '戌' => ['19时12分', '19时20分'],
                '亥' => ['21时2分', '21时12分'],
            ],
            '土' => [
                '子' => ['0时5分'],
                '丑' => ['1时35分'],
                '寅' => ['3时25分'],
                '卯' => ['5时10分', '5时50分', '6时20分', '6时50分'],
                '辰' => ['7时10分', '7时35分', '7时50分', '8时30分'],
                '巳' => ['9时15分', '9时30分', '10时25分'],
                '午' => ['11时35分', '11时50分', '12时10分'],
                '未' => ['13时35分', '13时50分'],
                '申' => ['16时15分', '16时20分'],
                '酉' => ['17时15分', '17时30分', '18时25分'],
                '戌' => ['19时10分'],
                '亥' => ['21时10分'],
            ],
            '金' => [
                '子' => ['23时39分', '0时9分'],
                '丑' => ['1时39分', '2时9分'],
                '寅' => ['3时19分'],
                '卯' => ['5时9分', '5时59分', '6时19分', '6时59分'],
                '辰' => ['7时9分', '7时59分', '8时19分', '8时59分'],
                '巳' => ['9时9分', '9时19分', '9时59分', '10时19分'],
                '午' => ['11时39分', '11时59分', '12时9分'],
                '未' => ['13时39分', '13时59分'],
                '申' => ['16时9分', '16时29分'],
                '酉' => ['17时9分', '17时19分', '17时59分', '18时19分'],
                '戌' => ['19时9分', '19时19分'],
                '亥' => ['21时9分'],
            ],
            '水' => [
                '子' => ['23时36分', '23时51分', '0时6分'],
                '丑' => ['1时36分', '2时6分'],
                '寅' => ['3时16分', '3时36分', '4时16分'],
                '卯' => ['5时6分', '5时56分', '6时16分', '6时56分'],
                '辰' => ['7时6分', '7时36分', '8时16分', '8时36分'],
                '巳' => ['9时6分', '9时36分', '9时56分', '10时16分'],
                '午' => ['11时31分', '11时56分', '12时6分'],
                '未' => ['13时31分', '13时56分', '14时16分'],
                '申' => ['16时1分', '16时16分'],
                '酉' => ['17时6分', '17时36分', '17时56分', '18时16分'],
                '戌' => ['19时6分', '19时16分'],
                '亥' => ['21时6分'],
            ],
        ];
        $ydz = $jiNian['y'][1];
        $ddz = $jiNian['d'][1];
        $result = [];
        $explain = [
            'jc' => [],
            'shen' => [],
            'sha' => [],
        ];
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        for ($i = 1; $i <= $this->orginData['limit']; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $year1 = (int)date('Y', $time);
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $jiXiong = $huangli->getJiXiong();
            $zhiRi = $huangli->getZhiRi();
            $base1 = $huangli->getLunarByBetween();
            $jianChu = $huangli->getJianChu();
            $nongli = $base1['_nongli'];
            $mdNongli = $nongli['m'] . '-' . $nongli['d'];
            $jiNian1 = $base1['jinian'];
            $ydz1 = $jiNian1['y'][1];
            $dtg1 = $jiNian1['m'][0];
            $mdz1 = $jiNian1['m'][1];
            $ddz1 = $jiNian1['d'][1];
            $dgz1 = implode('', $jiNian1['d']);
            $shenSha = $zhiRi['shen_sha'];
            $shenSha = ($shenSha === '天德' ? '宝光' : $shenSha);
            $bool = true;
            $fxStr1 = '';
            $fxStr2 = '';
            $noList = [];
            // 去除日支相冲、日支相刑
            if (BaziExt::getXianXinDz($ydz, $ddz1)) {
                $bool = false;
                $fxStr1 .= '刑';
            }
            if (BaziExt::getXianChongDz($ydz, $ddz1)) {
                $bool = false;
                $fxStr1 .= '冲';
            }
            if (BaziExt::getXianXinDz($ddz, $ddz1)) {
                $bool = false;
                $fxStr2 .= '刑';
            }
            if (BaziExt::getXianChongDz($ddz, $ddz1)) {
                $bool = false;
                $fxStr2 .= '冲';
            }
            $fenxiArr = [];
            if ($fxStr1) {
                $fenxiArr[] = ['y' => '年', 'd' => $fxStr1];
            }
            if ($fxStr2) {
                $fenxiArr[] = ['y' => '日', 'd' => $fxStr2];
            }
            // 岁破
            if (BaziExt::getXianChongDz($ddz1, $ydz1)) {
                $bool = false;
                $noList[] = '岁破日';
            }

            $jieQiYear = $this->getAllJieQi($year1);
            // 清明
            if (date('Y-m-d', strtotime($jieQiYear['清明'])) == $timeStr) {
                $noList[] = '清明';
                $bool = false;
            }
            // 七月半
            if ($mdNongli == '7-15') {
                $noList[] = '七月半';
                $bool = false;
            }
            // 杨公忌日
            if ($this->checkYangGongJi($nongli['m'], $nongli['d'])) {
                $noList[] = '杨公忌日';
                $bool = false;
            }
            // 小红沙日
            if ($this->checkXiaoHongSha($nongli['m'], $ddz1)) {
                $noList[] = '小红沙日';
                $bool = false;
            }
            // 绝烟火日
            if ($this->checkJueYanHuo($mdz1, $dgz1)) {
                $noList[] = '绝烟火日';
                $bool = false;
            }
            // 天官符
            if ($this->checkYueTianGuan($jiNian1['m'][0], $dgz1)) {
                $noList[] = '天官符';
                $bool = false;
            }
            //2、去除归忌、往亡、破日。
            //            if ($this->checkGuiJi($mdz1, $ddz1)) {
            //                $noList[] = '归忌日';
            //                $bool = false;
            //            }
            if ($this->checkWangWang($mdz1, $ddz1)) {
                $noList[] = '往亡日';
                $bool = false;
            }
            // 破日
            if (BaziExt::getXianChongDz($mdz1, $ddz1)) {
                $noList[] = '日破';
                $bool = false;
            }
            // 劫煞
            if ($this->checkJieSha($mdz1, $ddz1)) {
                $noList[] = '劫煞日';
                $bool = false;
            }
            // 3、筛选劫煞、灾煞、月煞、月刑、月厌、大时、天吏、四废、五墓、平收闭日
            if ($this->checkZaiSha($mdz1, $ddz1)) {
                $noList[] = '灾煞日';
                $bool = false;
            }
            if ($this->checkYueSha($mdz1, $ddz1)) {
                $noList[] = '月煞日';
                $bool = false;
            }
            if ($this->checkYueXin($mdz1, $ddz1)) {
                $noList[] = '月刑日';
                $bool = false;
            }
            if ($this->checkYueYan($mdz1, $ddz1)) {
                $noList[] = '月厌日';
                $bool = false;
            }
            if ($this->checkDaShi($mdz1, $ddz1)) {
                $noList[] = '大时';
                $bool = false;
            }
            if ($this->checkTianLi($mdz1, $ddz1)) {
                $noList[] = '天吏日';
                $bool = false;
            }
            // 四废
            if ($this->checkSiFei($mdz1, $dgz1)) {
                $noList[] = '四废日';
                $bool = false;
            }
            //            if ($this->checkWuMu($mdz1, $dgz1)) {
            //                $noList[] = '五墓日';
            //                $bool = false;
            //            }
            $jcType = 1;
            if (in_array($jianChu, ['破', '危', '平', '闭', '执'])) {
                $jcType = 0;
            }
            $hdType = $zhiRi['huan_dao'] == '黑道' ? 0 : 1;
            if (!$hdType) {
                $bool = false;
            }
            unset($jiNian1['h']);
            $jxYj = $huangli->getJiXiong();
            $keyStr = date('Y年m月', $time);
            $num = $result[$keyStr]['num'] ?? 0;
            $chongDz = BaziCommon::getChongDz($ddz1);
            $tmpRes = [
                'gongli' => [
                    'y' => $year1,
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']) ? 1 : 0,
                // 生肖
                'sx' => $base1['shengxiao'],
                // 农历
                'nongli' => $base1['nongli'],
                // 农历数字
                '_nongli' => $base1['_nongli'],
                // 纪年
                'jinian' => $jiNian1,
                // 正冲
                'zheng_chong' => [
                    'gz' => $chongDz,
                    'sx' => $this->lunar->getZodiac($chongDz),
                    'year' => '',
                ],
                // 煞向
                'sha_xian' => $this->getShaXian($ddz1),
                // 建除
                'jianchu' => $jianChu,
                // 宜忌
                'type' => 'buyi',
                // 黄道 1 黑道 0
                'hd_type' => $hdType,
                // 星神结果
                'shensha' => $shenSha,
                'fenxi' => $fenxiArr,
                'jishen' => $jxYj['jishen'],
                'xiong' => $jxYj['xiong'],
                'reason' => [],
            ];
            if ($bool) {
                $num++;
                $type = 'ping';
                // 4、筛选天德、月德、天德合、月德合。煞贡、人专、直星。
                $reason = [];
                if ($this->checkTianDe($mdz1 . $ddz1) || $this->checkTianDe($mdz1 . $dtg1)) {
                    $type = 'da';
                    $reason[] = '天德';
                }
                if ($this->checkTianDeHe($mdz1 . $ddz1) || $this->checkTianDeHe($mdz1 . $dtg1)) {
                    $type = 'da';
                    $reason[] = '天德合';
                }
                if ($this->checkYueDe($mdz1 . $ddz1)) {
                    $type = 'xiao';
                    $reason[] = '月德';
                }
                if ($this->checkYueDeHe($mdz1 . $ddz1) || $this->checkYueDeHe($mdz1 . $dtg1)) {
                    $type = 'xiao';
                    $reason[] = '月德合';
                }
                if ($this->checkShaGong($nongli['m'], $dgz1)) {
                    $type = 'xiao';
                    $reason[] = '煞贡';
                }
                if ($this->checkRenZhuan($nongli['m'], $dgz1)) {
                    $type = 'xiao';
                    $reason[] = '人专';
                }
                if ($this->checkZhiXin($nongli['m'], $dgz1)) {
                    $type = 'xiao';
                    $reason[] = '直星';
                }
                // 5、筛选天赦、月恩、四相、天愿、时德、民日、驿马、天马、成日、开日
                if ($this->checkTianSe($mdz1, $dgz1)) {
                    $reason[] = '天赦';
                }
                if ($this->checkYueEn($mdz1 . $dtg1)) {
                    $type = 'xiao';
                    $reason[] = '月恩';
                }
                if ($this->checkSiXian($mdz1, $dtg1)) {
                    $reason[] = '四相';
                }
                if ($this->checkTianYuan($mdz1 . $dgz1)) {
                    $type = 'xiao';
                    $reason[] = '天愿';
                }
                if ($this->checkShiDe($mdz1 . $ddz1)) {
                    $type = 'xiao';
                    $reason[] = '时德';
                }
                if ($this->checkYiMa($mdz1, $ddz1)) {
                    $reason[] = '驿马';
                }
                if ($this->checkTianMa($mdz1, $ddz1)) {
                    $reason[] = '天马';
                }
                $tmpRes['reason'] = $reason;
                $hourList = $this->getJishi($jiNian1['d']);
                $tmpHourRes = [];
                $jn10 = $jiNian1;
                foreach ($hourList as $v5) {
                    $jn10['h'] = [
                        $v5['tg'], $v5['dz'],
                    ];
                    $tmpHourRes[] = [
                        'dz' => $v5['dz'],
                        'h' => $v5['h'],
                        'chong' => $v5['chong'],
                    ];
                }
                $jishenRes = array_values(array_unique(array_merge($reason, $jxYj['jishen'])));
                $tmpRes['jishen'] = $jishenRes;
                $tmpRes['type'] = $type;
                $tmpRes['hour'] = $tmpHourRes;
                $tmpRes['pos'] = $this->getDongGongPos($jiNian1);
                if (!$hdType) {
                    $tmpRes['shensha'] = '';
                }
                if (!$jcType) {
                    $tmpRes['jianchu'] = '';
                }
            } else {
                if ($hdType) {
                    $tmpRes['shensha'] = '';
                }
                if ($jcType) {
                    $tmpRes['jianchu'] = '';
                }
                $tmpRes['reason'] = $noList;
            }
            foreach ($tmpRes['reason'] as $v1) {
                if (isset($explain['shen'][$v1])) {
                    continue;
                }
                $explain['shen'][$v1] = $this->getExplain($v1);
            }
            if (!empty($tmpRes['jianchu']) && !isset($explain['jc'][$jianChu])) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if (!empty($tmpRes['shensha']) && !isset($explain['sha'][$shenSha])) {
                $explain['sha'][$shenSha] = $this->getShaRes($shenSha);
            }
            $result[$keyStr]['title'] = $keyStr;
            $result[$keyStr]['num'] = $num;
            $result[$keyStr]['list'][] = $tmpRes;
        }
        return [
            'explain' => $explain,
            'list' => array_values($result),
        ];
    }

    /**
     * 杨公忌日
     * @param int $month 农历月
     * @param int $day 农历日
     * @return bool
     */
    protected function checkYangGongJi(int $month, int $day): bool
    {
        $str = "{$month}-{$day}";
        $listYangGong = ['1-13', '2-11', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19'];
        return in_array($str, $listYangGong);
    }

    /**
     * 小红沙
     * @param int $month
     * @param string $ddz
     * @return bool
     */
    protected function checkXiaoHongSha(int $month, string $ddz): bool
    {
        $listHongSha = ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'];
        $str = "{$month}-{$ddz}";
        return in_array($str, $listHongSha);
    }

    /**
     * 绝烟火日
     * @param string $mdz 月地支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkJueYanHuo(string $mdz, string $dgz): bool
    {
        $list = ['子癸酉', '丑庚午', '寅丁卯', '卯甲子', '辰癸酉', '巳庚午', '午丁卯', '未甲子', '申癸酉', '酉丁卯', '戌庚午', '亥甲子'];
        $str = $dgz . $dgz;
        return in_array($str, $list);
    }

    /**
     * 天官符
     * @param string $ydz 年支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueTianGuan(string $ydz, string $ddz): bool
    {
        $list = [
            '子亥', '丑申', '寅巳', '卯寅', '辰亥', '巳申', '午巳', '未寅', '申亥', '酉申', '戌巳', '亥寅',
        ];
        $str = $ydz . $ddz;
        return in_array($str, $list);
    }

    /**
     * 归忌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkGuiJi(string $mdz, string $ddz): bool
    {
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        return in_array($mdz . $ddz, $listGuiJi);
    }

    /**
     * 往亡
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkWangWang(string $mdz, string $ddz): bool
    {
        $list = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 劫煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkJieSha(string $mdz, string $ddz): bool
    {
        $list = ['寅亥', '卯申', '辰巳', '巳寅', '午亥', '未申', '申巳', '酉寅', '戌亥', '亥申', '子巳', '丑寅'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueSha(string $mdz, string $ddz): bool
    {
        $list = ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 灾煞
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkZaiSha(string $mdz, string $ddz): bool
    {
        $list = ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月刑
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueXin(string $mdz, string $ddz): bool
    {
        $list = ['寅巳', '卯子', '辰辰', '巳申', '午午', '未丑', '申寅', '酉酉', '戌未', '亥亥', '子卯', '丑戌'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 月厌
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkYueYan(string $mdz, string $ddz): bool
    {
        $list = ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天吏
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkTianLi(string $mdz, string $ddz): bool
    {
        $list = ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 四废
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkSiFei(string $mdz, string $dgz): bool
    {
        $list = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
        ];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 五墓
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkWuMu(string $mdz, string $dgz): bool
    {
        $list = ['寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰'];
        return in_array($mdz . $dgz, $list);
    }

    /**
     * 大时
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    protected function checkDaShi(string $mdz, string $ddz): bool
    {
        $list = ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 煞贡
     * @param int $month 农历月份数字
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkShaGong(int $month, string $dgz): bool
    {
        if (in_array($month, [1, 4, 7, 10])) {
            $list = ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'];
        } elseif (in_array($month, [2, 5, 8, 11])) {
            $list = ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'];
        } else {
            $list = ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'];
        }
        return in_array($dgz, $list);
    }

    /**
     * 人专
     * @param int $month 农历月份数字
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkRenZhuan(int $month, string $dgz): bool
    {
        if (in_array($month, [1, 4, 7, 10])) {
            $list = ['辛未', '庚辰', '己丑', '戊戍', '丁未', '丙辰'];
        } elseif (in_array($month, [2, 5, 8, 11])) {
            $list = ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'];
        } else {
            $list = ['己巳', '戊寅', '丁亥', '丙申', '乙巳', '甲寅', '癸亥'];
        }
        return in_array($dgz, $list);
    }

    /**
     * 直星
     * @param int $month 农历月份数字
     * @param string $dgz 日干支
     * @return bool
     */
    protected function checkZhiXin(int $month, string $dgz): bool
    {
        if (in_array($month, [1, 4, 7, 10])) {
            $list = ['戊辰', '丁丑', '丙戍', '乙未', '甲辰', '癸丑', '壬戍'];
        } elseif (in_array($month, [2, 5, 8, 11])) {
            $list = ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'];
        } else {
            $list = ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'];
        }
        return in_array($dgz, $list);
    }

    /**
     * 驿马
     * @param string $mdz 月地支
     * @param string $ddz 日地支
     * @return bool
     */
    protected function checkYiMa(string $mdz, string $ddz): bool
    {
        $list = ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 天马
     * @param string $mdz 月地支
     * @param string $ddz 日地支
     * @return bool
     */
    protected function checkTianMa(string $mdz, string $ddz): bool
    {
        $list = ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'];
        return in_array($mdz . $ddz, $list);
    }

    /**
     * 获得煞向
     * @param string $dz 日地支
     * @return string
     */
    protected function getShaXian(string $dz): string
    {
        $list = [
            '子' => '南', '丑' => '东', '寅' => '北', '卯' => '西', '辰' => '南', '巳' => '东',
            '午' => '北', '未' => '西', '申' => '南', '酉' => '东', '戌' => '北', '亥' => '西',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    protected function getTianGanSiSha(string $dz): array
    {
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $result = ['ju' => ['寅', '午', '戌'], 'no' => '丑', 'sx' => '牛'];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 解释
     * @param $str
     * @return string
     */
    protected function getExplain($str): string
    {
        $list = [
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '定为不动，有静对之意，一切顺利。',
            '满' => '为丰收圆满之义，意丰足充盈、丰收、美满的日子。',
            '建' => '建日本来是指万物孕育之时，但当中有吉亦有凶。',
            '成' => '此日指凡事成就，喜凶诸事均可办理。',
            '开' => '为开业、开放、开心之义，意开始、开展的日子，适合各种事情办之可成。',
            '危' => '危日象征危机，是危险的一天，提醒凡事都要小心之意。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日在解说有指破裂，冲破的含义，忌办一切喜事。',
            '平' => '此日平平，无出头之日。但每个人都喜欢上进、向往辉煌前程。',
            '收' => '收日有凶恶之神的说法，寓意不吉。',
            '闭' => '为坚固之义，关闭、紧闭之意。',
            '执' => '执日有小耗日的说法，寓意不吉，需规避。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，选择该日有些不吉。',
            '重阳' => '当日有喜事勿用的说法。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '彭祖百忌' => '彭祖百忌中有“亥不嫁娶，不利新郎”，该日嫁娶，不利新郎。',
            '清明' => '这里通常都是祭奠先祖，缅怀已故之人，最好规避。',
            '小红沙日' => '红沙日有诸事不宜，犯之则凶的说法。',
            '杨公忌日' => '这日诸事不宜。故而需要避开该日。',
            '正冲' => '该日天干地支正冲父母天干地支，需要规避。',
            '不将' => '有利移动，修造的传统吉日，当日装修，寓意顺顺利利，心想事成。',
            '季分' => '宜迁移、装修的传统吉日，寓意稳定顺利。',
            '岁德' => '德神护佑的吉日，积福之日，福气汇聚。',
            '岁德合' => '属上吉，天德合并月德合，有宜没有忌，宜修方，造葬，百事皆吉。',
            '显星' => '三皇吉星之一，该日有步步高升的寓意，因而利于修造等事。',
            '曲星' => '三皇吉星之一，该日对财运有益，喜事连连。',
            '传星' => '三皇吉星之一，有加官进禄，万事称心的寓意。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，百福并集，诸事皆宜，是个好日子。',
            '月德合' => '得到五行力量的聚合，为有福之日。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，象征新生活新希望，装修大吉。',
            '四相' => '拥有四时王相贵气的日子，纯粹的小吉日。',
            '时德' => '得到天地舒畅之气，得到四时之气的祝福，小吉日。',
            '天乙贵人' => '此日若装修，可以逢凶化吉，出门遇贵人。',
            '岁破日' => '岁破有大事勿用，诸事不宜的说法。',
            '披麻日' => '披麻主孝丧之事，寓意不吉。',
            '月破日' => '日值岁破，大事不宜。破日有破败之意，日月相冲，是为大耗。',
            '孤鸾日' => '孤鸾日有，犯之则凶，宜有阻碍的说法。',
            '阴差阳错' => '阴差阳错有着事与愿违的寓意，装修讲究平安，故而应该避讳该日。',
            '朔日' => '此系恶鬼聚拢之辰，忌装修进宅会客作乐。',
            '四离日' => '日值四离，大事勿用。',
            '四绝日' => '古话有云，日值四绝，诸事不顺。',
            '受死日' => '该日寓意大凶，因而有不宜诸吉事的说法。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '归忌日' => '该日寓意不好，有凶日的说法，最好规避。',
            '绝烟火日' => '该日寓意不好，有凶日的说法，最好规避。',
            '三煞日' => '三煞有凶恶，邪煞的意思，寓意不吉。',
            '天官符' => '该日有忌修方，犯之易有是非口角，伤官破财的说法。',
            '土符' => '符就是土神，当日动土触犯土地之神，寓意不吉。',
            '相冲' => '用户年支或日支与当日相冲，需规避。',
            '相刑' => '用户年支或日支与当日相刑，需规避。',
            '月厌日' => '月厌是厌魅之神，性情阴暗，不吉。',
            '日破' => '日破主不吉之事，有破损的说法。',
            '月煞日' => '当月杀神，寓意不吉。',
            '劫煞日' => '该日主打劫，破财，寓意不吉。',
            '灾煞日' => '灾煞主灾祸病痛等不吉之事，最好规避。',
            '四废日' => '日值四废，作事不易成功，容易有始无终。',
            '月刑日' => '该日主凶破，喜事上大有不吉。',
            '天吏日' => '天子的官吏，有奉天命而之人罪的寓意。',
            '五墓日' => '一种忌日',
            '煞贡' => '有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '人专' => '有入宅即安的寓意，百事吉庆，对财运有益，喜事连连。',
            '直星' => '有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富，步步高升。',
            '民日' => '有顺势而为，遵循天道的寓意。',
            '驿马' => '此日可外出走动，可以逢凶化吉，出门遇贵人。',
            '天马' => '天马是天的坐骑。',
            '大时' => '该日表示精光、消减，寓意不吉。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 获得十二星神解释
     * @param $str
     * @return string
     */
    protected function getShaRes($str): string
    {
        $list = [
            '青龙' => '传统习俗中的吉利日子，此日有祥瑞之召的日子。',
            '明堂' => '传统习俗中的吉利日子。该日是明辅星的日子，利于百事。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克。该日万事皆忌，不宜有所动作。',
            '朱雀' => '天讼星，利用公事，常人凶，喜事忌用。',
            '金匮' => '传统习俗中的吉利日子，用于装修乃是大吉。',
            '白虎' => '白虎是天杀星，为凶恶之神，最好规避该日。',
            '玉堂' => '传统习俗中的吉利日子，用于装修为上选，当日一切顺利。',
            '天牢' => '天牢有围困，官司的说法。',
            '玄武' => '该日容易出现一些口舌之事，也容易犯部分小人，在一些大事上略忌讳。',
            '司命' => '当天白天用事大吉，因此可用于装修，寓意万事顺意，装修大吉。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '宝光' => '传统习俗中的吉利日子，利于出行安全，动土装修等皆吉。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 吉时
     * @param array $gzDay 日干支
     * @return array
     */
    protected function getJishi(array $gzDay): array
    {
        $arr = Calendar::DI_ZHI;
        $list1 = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $list2 = [
            [
                '子' => '甲', '丑' => '乙', '寅' => '丙', '卯' => '丁', '辰' => '戊', '巳' => '己',
                '午' => '庚', '未' => '辛', '申' => '壬', '酉' => '癸', '戌' => '甲', '亥' => '乙',
            ],
            [
                '子' => '丙', '丑' => '丁', '寅' => '戊', '卯' => '己', '辰' => '庚', '巳' => '辛',
                '午' => '壬', '未' => '癸', '申' => '甲', '酉' => '乙', '戌' => '丙', '亥' => '丁',
            ],
            [
                '子' => '戊', '丑' => '己', '寅' => '庚', '卯' => '辛', '辰' => '壬', '巳' => '癸',
                '午' => '甲', '未' => '乙', '申' => '丙', '酉' => '丁', '戌' => '戊', '亥' => '己',
            ],
            [
                '子' => '庚', '丑' => '辛', '寅' => '壬', '卯' => '癸', '辰' => '甲', '巳' => '乙',
                '午' => '丙', '未' => '丁', '申' => '戊', '酉' => '己', '戌' => '庚', '亥' => '辛',
            ],
            [
                '子' => '壬', '丑' => '癸', '寅' => '甲', '卯' => '乙', '辰' => '丙', '巳' => '丁',
                '午' => '戊', '未' => '己', '申' => '庚', '酉' => '辛', '戌' => '壬', '亥' => '癸',
            ],
        ];
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $list3 = [
            '甲' => 0, '乙' => 1, '丙' => 2, '丁' => 3, '戊' => 4, '己' => 0, '庚' => 1, '辛' => 2, '壬' => 3, '癸' => 4,
        ];
        $listShiTg = $list2[$list3[$gzDay[0]]];
        $jn = $this->lunar->getLunarGanzhiYear();
        $ydz1 = $jn[1];

        $result = [];
        $sxBase = new SxBase();
        foreach ($arr as $v) {
            if (in_array($v, ['子', '丑', '寅', '卯', '戌', '亥'])) {
                continue;
            }
            $tgS = $listShiTg[$v];
            $sx = $sxBase->getsxByDz($v);
            $chongSx = $sxBase->getChong($sx);
            $str = $v . $gzDay[1];
            $str2 = $v . $ydz1;
            if (BaziCommon::getXianChong($str) || BaziCommon::getXianXin($str) || BaziCommon::getXianHai($str)) {
                continue;
            }
            if (BaziCommon::getXianChong($str2) || BaziCommon::getXianPo($str2) || BaziCommon::getXianHai($str2)) {
                continue;
            }
            $result[] = [
                'tg' => $tgS, 'dz' => $v, 'h' => $hourList[$v],
                'chong' => $chongSx['name'], 'sha_xian' => $list1[$v] ?? $list1['子'],
            ];
        }
        return $result;
    }
}
