<?php
// +----------------------------------------------------------------------
// | Qm
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziEx;
use app\lib\Utils;
use app\lib\WxAttr;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\Ming3;
use app\model\baobaoqm\Xing;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use Overtrue\Pinyin\Pinyin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Qm
{
    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 旺度
     * @var string
     */
    protected string $wangDu = '';

    /**
     * 字信息
     * @var array
     */
    protected array $arrZi = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'xing' => input('xing', '', 'trim'),
            // 性别 男为0，女为1 2未知
            'sex' => input('sex', '', 'intval'),
            // 出生日期
            'time' => input('time', '', 'trim'),
            // 名字的第二个字
            'name1' => input('name1', '', 'trim'),
            // 要获取的名字数
            'total' => input('total', 40, 'intval'),
            // 订单时间
            'local' => input('local', date('Y-m-d'), 'trim'),
            // 指定名字
            'nlist' => input('nlist', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓' => ['require', 'chs'],
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1,2'],
                'total|要取的名字数' => ['require', 'egt:10'],
                'local|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $sex = $data['sex'];
        if ($sex == 2) {
            $sex = 0;
        }
        $this->lunar = Ex::date($data['time'])->sex($sex);
        $base = $this->lunar->getLunarByBetween();
        $sxYongzi = $this->getSxYongZi($base['jinian']['y'][1]);

        $baziEx = new BaziEx($this->lunar);
        $this->wangDu = $baziEx->getWangDu();
        $xiY = $baziEx->getxiYongJi4();
        try {
            if ($data['nlist']) {
                $namesList = $this->getNamesByNlist();
            } else {
                $namesList = $this->getNamesList($data, $xiY['xy']);
            }
        } catch (\Exception $e) {
            return ['status' => 0, 'msg' => $e->getMessage()];
        }
        return [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            'day_attr' => $this->lunar->getDayAttr(),
            // 大运
            'fate' => $this->lunar->getFate(),
            // 五行占比
            'wx' => $this->getWxPersent(),
            'like_god' => $this->lunar->getLikeGod(),
            'like_zi' => "起名宜有：“" . implode('”，“', $sxYongzi) . "”等部首为吉",
            // 旺度
            'wangdu' => $this->wangDu,
            'xy' => $xiY['xy'],
            'names' => $namesList,
        ];
    }

    /**
     * 姓氏列表
     * @return array
     */
    public function xing()
    {
        return Xing::getlists();
    }

    /**
     * 获得名字列表
     * @param array $param
     * @param array $xy 喜用忌闲仇五行
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNamesList(array $param, array $xy): array
    {
        foreach ($xy as $k => $v) {
            $xy[$k] = WxAttr::getWxToNum($v);
        }
        $sex = $param['sex'];
        $genderArr = in_array($sex, [0, 1]) ? [$sex, 2] : [0, 1, 2];
        // 单名：姓氏+用神；姓氏+喜神：姓氏+闲神
        // 双名：姓氏+用神+用神；姓氏+用神+喜神；姓氏+喜神+用神；姓氏+喜神+喜神
        $wxArr = [$xy['yong'], $xy['xi'], $xy['xian'], $xy['yong'] . $xy['yong'], $xy['yong'] . $xy['xi'], $xy['xi'] . $xy['yong'], $xy['xi'] . $xy['xi']];
        $total = $param['total'];
        $localtime = (int)date('ydHis', strtotime($param['local'])) + (int)$this->lunar->dateTime->format('ydHis');
        $list = Ming3::lists($wxArr, $genderArr, $localtime);
        shuffle($list);
        $xingId = Xing::where('title', $param['xing'])->cache(3600)->value('id');
        $arrZi = [];
        $xingArr = preg_split('/(?<!^)(?!$)/u', $param['xing']);
        $endXing = end($xingArr);
        foreach ($xingArr as $v) {
            $arrZi[$v] = $v;
        }
        $i = 0;
        $listN = [];
        foreach ($list as $v) {
            if ($i >= $total) {
                break;
            }
            $stype = $v['stype'];
            $tmp = trim($v['xing'], '|');
            if (empty($tmp)) {
                $stype = 2;
            }
            $tmpArr = explode('|', $tmp);
            $zi1 = $v['zi'];
            $zi2 = $v['zi2'];
            // 第二字与姓氏最后一个相则过滤
            if ($endXing == $zi1) {
                continue;
            }
            if ($xingId && $stype == 1) {
                if (in_array($xingId, $tmpArr)) {
                    continue;
                }
            }
            if ($xingId && $stype == 0) {
                if (!in_array($xingId, $tmpArr)) {
                    continue;
                }
            }
            $rs = [$zi1];
            $arrZi[$zi1] = $zi1;
            if ($zi2) {
                $arrZi[$zi2] = $zi2;
                $rs[] = $zi2;
            }
            $i++;
            $listN[] = $rs;
        }
        $this->arrZi = array_merge($this->arrZi, Cnword::getInfo2($arrZi));
        unset($list);
        foreach ($listN as $k => $v) {
            $tmp = [];
            foreach ($v as $v1) {
                $tmp[] = $this->getZiInfo($v1);
            }
            $listN[$k] = $tmp;
            $i++;
        }

        $xingCn = [];
        foreach ($xingArr as $v) {
            $xingCn[] = $this->getZiInfo($v);
        }
        return [
            'xing' => $xingCn,
            'list' => $listN,
        ];
    }

    /**
     * 根据用户传入的名字获得结果
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNamesByNlist()
    {
        $list = explode(',', $this->orginData['nlist']);
        $arrZi = Utils::mbStrSplit(str_replace(',', '', $this->orginData['xing'] . $this->orginData['nlist']));
        $arrZi = array_values(array_unique($arrZi));
        $this->arrZi = Cnword::getInfo2($arrZi);
        $listName = [];
        foreach ($list as $k => $v) {
            $ziArr = Utils::mbStrSplit($v);
            $tmp = [];
            foreach ($ziArr as $v1) {
                $tmp[] = $this->getZiInfo($v1);
            }
            $listName[] = $tmp;
        }
        $xingArr = preg_split('/(?<!^)(?!$)/u', $this->orginData['xing']);
        $xingCn = [];
        foreach ($xingArr as $v) {
            $xingCn[] = $this->getZiInfo($v);
        }
        return [
            'xing' => $xingCn,
            'list' => $listName,
        ];
    }

    /**
     * 获得字详情
     * @param string $zi
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getZiInfo(string $zi = '')
    {
        if (empty($zi)) {
            return [];
        }
        $tmpData = $this->arrZi[$zi] ?? Cnword::info($zi);
        if (!empty($tmpData)) {
            return [
                'zi' => $zi,
                'py' => $tmpData['py2'],
                'wx' => $tmpData['wx'],
                'detail' => $tmpData['detail'],
            ];
        }
        return [
            'zi' => $zi,
            'py' => Pinyin::sentence($zi, 'none'),
            'wx' => '-',
            'detail' => '',
        ];
    }

    /**
     * 五行占比
     * @return array
     */
    protected function getWxPersent(): array
    {
        $wx = $this->lunar->getWuxingNum();
        $res = [];
        $total = array_sum($wx);
        foreach ($wx as $k => $v) {
            $res[] = [
                'wx' => $k,
                'per' => number_format($v * 100 / $total, 2),
            ];
        }
        return $res;
    }

    /**
     * 根据年地支获得起名用字的部首
     * @param string $ydz 年地支
     * @return array
     */
    protected function getSxYongZi(string $ydz): array
    {
        $list = [
            '子' => ['豆', '麦', '米', '梁', '禾', '草', '王', '君', '令', '口', '宀', '乙', '厶', '彡', '巾', '系', '衣', '采', '夕', '申', '辰', '亥', '丑', '牛'],
            '丑' => ['辶', '艹', '田', '禾', '叔', '麦', '米', '豆', '车'],
            '寅' => ['山', '林', '王', '君', '令', '大', '肉', '月', '心', '马', '午', '火', '戌', '犬', '衣', '系', '巾', '采', '氵', '水', '冫'],
            '卯' => ['艹', '禾', '米', '豆', '麦', '梁', '翟', '稻', '叔', '木', '卯', '东', '口', '宀'],
            '辰' => ['星', '云', '辰', '氵', '水', '王', '大', '君', '帝', '令', '主', '长', '日', '月', '厶', '冫', '子', '壬', '癸', '申', '袁', '马', '午'],
            '巳' => ['口', '宀', '辶', '廴', '弓', '走', '几', '己', '虫', '邑', '田', '小', '少', '臣', '士', '夕', '马', '羊', '酉', '丑', '乡', '系', '衣', '示', '采', '巾', '木', '口', '宀'],
            '午' => ['寅', '戌', '系', '巾', '彡', '农', '目', '宀', '木', '禾', '麦', '叔', '稷', '豆', '栗', '梁', '龙', '艹'],
            '未' => ['米', '麦', '禾', '豆', '稷', '叔”“木”“艹', '口', '山', '门', '冖', '足', '豕', '卯', '巳', '午'],
            '申' => ['木', '口', '宀', '冖', '王”“子', '冫', '氵', '水', '辰'],
            '酉' => ['禾', '豆', '米', '梁', '麦', '栗', '山', '彡', '纟', '采', '系', '小', '宀', '冖', '巳', '酉', '丑'],
            '戌' => ['马', '禾', '豆', '米', '梁', '麦', '栗', '山', '彡', '纟', '采', '系', '小', '宀', '冖', '巳', '酉', '丑'],
            '亥' => ['田', '子', '丑', '卯', '未', '氵', '冫', '口', '金”“钅', '豆', '禾', '米', '艹'],
        ];
        return $list[$ydz];
    }

    /**
     * 五行关系
     * @param string $wx 五行
     * @param string $gx 相互关系
     * @return string
     */
    protected function getWxGuanxi($wx, $gx = '生'): string
    {
        $list = [
            '扶' => ['木' => '木', '火' => '火', '土' => '土', '金' => '金', '水' => '水'],
            '生' => ['木' => '火', '火' => '土', '土' => '金', '金' => '水', '水' => '木'],
            '克' => ['木' => '土', '火' => '金', '土' => '水', '金' => '木', '水' => '火'],
            '耗' => ['木' => '金', '火' => '水', '土' => '木', '金' => '火', '水' => '土'],
            '泄' => ['木' => '水', '火' => '木', '土' => '火', '金' => '土', '水' => '金'],
        ];
        return $list[$gx][$wx];
    }

    /**
     * 喜用忌闲仇
     * @return array
     */
    protected function getxiYongJi(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wangDu = $this->wangDu;
        $wuXingAttr = $this->lunar->wuXingAttr;
        $dWx = $wuXingAttr[$jiNian['d'][0]];
        $list = [
            '身旺格' => '生', '从旺格' => '泄', '身弱格' => '耗', '从弱格' => '生',
        ];
        $yongShen = $this->getWxGuanxi($dWx, $list[$wangDu]);
        $xiShen = $this->getWxGuanxi($yongShen, '泄');
        // 喜神生用神，用神生闲神，闲神生仇神，仇神生忌神
        $xianShen = $this->getWxGuanxi($yongShen, '生');
        $qiuShen = $this->getWxGuanxi($xianShen, '生');
        $jiShen = $this->getWxGuanxi($qiuShen, '生');
        return [
            'yong' => $yongShen, 'xi' => $xiShen, 'xian' => $xianShen, 'qiu' => $qiuShen, 'ji' => $jiShen,
        ];
    }
}
