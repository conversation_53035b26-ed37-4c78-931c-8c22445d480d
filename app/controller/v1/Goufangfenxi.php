<?php
// +----------------------------------------------------------------------
// | Goufangfenxi.购房分析
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;

class Goufangfenxi
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 购房分析
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time' => input('time', '', 'trim'),
            // 性别 男 0 女 1
            'sex' => input('sex', 0, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $bazi = $this->lunar->getLunarByBetween();

        $result = [
            'base' => $bazi,
            'god' => $this->lunar->getGod(),
            '_god' => $this->lunar->_getGod(),
            // 引导语
            'main' => $this->getMain(),
            // 来源
            'from' => $this->getFrom(),
            // 住宅安稳
            'safe' => $this->getsafe(),
            // 环境的影响
            'affect' => $this->getAffect(),
            // 购置或迁住新房
            'goufan' => $this->getGouFan(),
            // 兴衰
            'xinshuai' => $this->getXinShuai(),
        ];
        return $result;
    }

    /**
     * 获得概述
     * @return string
     * @throws Exception
     */
    protected function getMain(): string
    {
        $list = [
            ['买房基本不需要自己操心，自有长辈给包办。', '长辈包办，说明买房的事情基本不用自己操心，自有长辈购入。'],
            ['贵人相帮，说明买房会与你的工作相关、单位相关。', '自主购房的几率较小，住宅多源于你的工作或者是单位。'],
            ['买房对你来说并不辛苦，有合适的机遇以及贵人来到时就能实现。', '有意外之财和意外的贵人相助，只要时机一到，可以自主买房。'],
            ['房子主要来源于自己的努力奋斗，存有一定积累之后自主购房。', '辛苦买房，需要靠自己较为艰辛的劳动付出而获得积累之后买房。'],
            ['会因为生活以及其他原因造成压力而自主购房，是一种负担。', '压力买房，明知买房会造成自己压力过重而由于好胜心的驱使而为之。'],
            ['工作上有权势，说明房屋来源大多与职业和公家有关。', '有贵人帮助，表示房子的来源大都和你的工作以及单位有关。'],
            ['家宅皆是易出高学历的人，房屋多是由于自己的名声和事业所获。', '房屋大都是因为自己的名声和事业而获得，因家宅能有成就之人。'],
            ['房子内有人会发财，因此住宅主要来源于自主购房或者是买卖房产所得。', '房子内有发财之人，主自己购房，也可能因为另一半而得到房子。'],
            ['难有祖上传下的房子，说明房子的主要来源还需靠自己的能力。', '难有祖居，说明需要靠自己的能力购房，在职场上要十分努力。'],
            ['自主购房的几率较小，能够得到兄弟姐妹的支助购房。', '家宅中兄弟姐妹有房，你的住宅来源可能还需要兄弟姐妹的帮助。'],
        ];
        $wuXingAttr = $this->lunar->wuXingAttr;
        $sex = $this->lunar->sex;
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wxDay = $jiNian['d'][0] . $wuXingAttr[$jiNian['d'][0]];
        $wxMonth = $jiNian['m'][1] . $wuXingAttr[$jiNian['m'][1]];
        $likeGod = $this->lunar->getLikeGod();
        // 阴阳 0为阴 1为阳
        $isYin = in_array($jiNian['y'][0], ["甲", "丙", "戊", "庚", "壬"]) ? '阴' : '阳';
        // 阳男阴女输出a，阴男阳女输出b。
        $keyIndex = in_array($isYin . $sex, ['阳0', '阴1']) ? 0 : 1;
        $key = $this->getKeyIndex();
        $mainString = "你的八字日主为（{$wxDay}），月令为（{$wxMonth}），经过五行季节力量的平衡评估，抑强扶弱、化敌为友、以达到五行平衡命局基础，取（{$likeGod}）为喜用五行。";
        $mainString .= $list[$key[0]][$keyIndex];
        return $mainString;
    }

    /**
     * 房屋来源
     * @return string
     * @throws Exception
     */
    protected function getFrom(): string
    {
        $list = [
            '你这一生基本上都不用为买房的事情而烦恼，因为只要时机一到自有长辈帮你包办得妥妥的，并且你最早可能会在二十岁至三十岁之间拥有自己的房屋，就算错过也不会距离太久。',
            '你这一生在买房或者获得房产方面并不会有太大的困难，因为真遇到麻烦的时候自会有贵人出手相助，而且你的房子多半与工作、单位相关，单位福利分房的机会挺大。',
            '买房对你来说并不会感到多么的辛苦，看似意外，又似水到渠成。只需等待那个时机一到，你就会因为有一大笔意外的钱财收入或意外的贵人相助而买房，所以买房自是不成问题。',
            '虽然你这一生还是有买房的机会，但其过程却是相当的辛苦，往往都是要靠自己多年的勤扒苦做，凭艰辛的劳动付出而获得一定的积累后才有可能买房。',
            '买房一事对于你来说可谓是压力山大，因为你很可能因为面子或好胜心而在明知买房会造成自己压力过重的情况下毅然出手，像是看着周围的人一个个都买了房子，或者给孩子准备婚房等。',
        ];
        $list2 = [
            5 => [
                '你这一生在买房上面不会有着太大的担忧，因为你的职业会为你带来一些成就，所以你的房子来源大多都是和自己的职业以及公家有关，遇到贵人时要及时抓住机会发展事业。',
                '你这一生在买房上面还是会有着一定的压力，或许因为你在工作上一些成就，会让你在获得房子时享受到一些职业或者公家有关的福利，但这与你前期的努力付出是不可分割的。',
            ],
            6 => [
                '你这一生不需要为买房的事情太过担心，你的工作会在努力下越来越有成就，并且也能得到贵人的帮助，所以你的房屋会是来自于公司的福利，也就是因为自己的名声和事业所获得。',
                '你这一生并不需要在买房的事情上操太多心，你房屋的来源很有可能会跟单位的福利有关，或许是因为做出了什么重大贡献而获得的奖励，也就是跟自己的声望和事业有关。',
            ],
            7 => [
                '你这一生想要买房并不是一件困难的事情，你的财运较好，能够因此而有购房的机会，当然购房也有可能是因为买卖房产所得，或是因为另一半而得到房子。',
                '你这一生想要买房是有点困难但也不少没有可能的事情，主要是卡在财运这块，当有购房的机会的时候，资金往往会因支援他事务而无力担负，当然也有机会因为另一半而得到房子。',
            ],
            8 => [
                '你这一生想要买房可能需要一些时间，祖上难有祖居，说明你的购房机会大都是来自于自己的能力，遇到贵人如果能够抓住机会，也能够尽快实现买房的想法。',
                '你这一生想要买房可能需要花费更多时间和精力，因为难有房产可以继承，所以你的购房机会大都是来自于自身的能力，机遇到来时如果能够牢牢抓住，也能够加快实现买房的想法。',
            ],
            9 => [
                '你这一生想要买房不是太困难的事情，因为会有贵人帮助，购房的机会主要是来源于自己的兄弟姐妹，兄弟姐妹有房，而你需要购房的时候就能够得到他们的支助。',
                '你这一生想要买房还是有一定困难的，所幸会有贵人相助，自己的兄弟姐妹有房，而且能为你来带购房的机会，在你需要购房的时候能够得到他们的支助。',
            ],

        ];
        $key = $this->getKeyIndex();
        $str = '';
        if ($key[0] < 5) {
            $str = $list[$key[0]];
        } elseif ($key[0] < 9 && $key[0] == $key[1]) {
            $str = $list2[$key[0]][0];
        } elseif ($key[0] < 9 && $key[0] == $key[2]) {
            $str = $list2[$key[0]][1];
        } elseif ($key[1] == 9) {
            $str = $list2[9][0];
        } else {
            $str = $list2[9][1];
        }
        return $str;
    }

    /**
     * 住宅安稳
     * @return string
     * @throws \Exception
     */
    protected function getSafe(): string
    {
        $list = [
            '正印' => ['你这一生住宅安稳，少有搬家的情况，而这样安稳的状态也可以表明家庭成员之间相处的和谐，各方面的运势也呈上升趋势，生活也非常的美好。', '你这一生的住宅还算安稳，出现搬家的情况较少，从另外一个方面来说，也表明家庭成员之间相处的也还算是比较和谐，各方面的运势也还不错。'],
            '偏印' => ['你这一生经常需要搬迁，属于在一个地方安定了一段时间之后又会出现搬家的情况，是不太安定的状态，对于家庭运势来说可能也存在一些不太好的影响。', '你这一生的住宅不太安稳，可能是经常都需要搬家的状况，会有些奔波的劳累，在心理上也会有些许压力产生，整个家庭成员之间的相处也显得没有那么和谐。'],
            '比肩' => ['你这一生无经常住宅，属于经常在更换居住地方的情况，太过频繁的搬家或许是在给你一个新的环境，让你充满活力，但可能也是会让你运气变差的情况。', '你这一生的住宅属于不太安定的状态，经常都在频繁的更换居住的地方，新的环境或许是新的希望，新的开始，但有时也会成为一种压力，带来很多不好的影响。'],
            '伤官' => ['你这一生是自立住宅的情况，相对来说，住宅肯定是比较安定的，不会出现四海为家的情况，从另外一方面来说，家庭运势也会随着安定的情况而较好，家庭和谐幸福。', '你这一生的住宅还算是比较安稳，不会出现经常性搬家的情况，既然住宅如此安定，也能说明家庭之间相处的十分和谐，住宅运势上也是比较好的。'],
            '食神' => ['你这一生的住宅还算是比较安稳，会自己修造的房屋或者是购置的房产，不会有轻易搬家的情况，对于整个家庭的运势来说也是让人觉得比较稳定的状态。', '你这一生的住宅较安定，不会有轻易搬家的情况发生，家庭成员之间的相处也还算是比较和谐，但是也要注意在住宅运势方面恐会有不太好的影响出现。'],
            '劫财' => ['你这一生的住宅容易易主，说明住宅并不是比较安稳的情况，会存在着一些变数，从住宅运势方面来说，更有可能会对家庭人员的运势造成一些影响。', '你这一生的住宅并不是十分的安稳，住宅容易易主，也就是会存在着一些变化，所以从家庭成员的相处来说可能也会存在一些问题，并不是十分和谐。'],
            '七杀' => ['你这一生住宅不太安稳，或许还会对家庭的和谐以及健康造成一些不太好的影响，建议可以通过住宅的某些环境布局来达到改善的作用，自然运势也会得到提升。', '你这一生住宅属于不太安定的状态，或许对于家庭成员的和谐以及健康都有些影响，建议可以通过一些住宅的局部改变来达到改善的作用。'],
            '正官' => ['你这一生住宅安稳且吉祥，属于比较安定的状态，少有搬家或者是损伤人口的状况出现，家人之间的相处和谐，家庭成员的运势也还算不错。', '你这一生的住宅属于比较安稳的状态，很少有搬家的情况出现，所以会在一个安定的地方生活很久，也说明家庭成员之间的相处也比较和谐，生活十分的美好。'],
            '正财' => ['你这一生住宅还算是比较安稳，不会出现经常搬家的情况，从住宅运势方面来说，家庭成员之间相处的比较和谐，较少出现因不合而造成对住宅有威胁的情况。', '你这一生的住宅可能并不是很安稳，虽然没有经常搬家的情况，但是从住宅运势上面来说，家庭成员之间相处的并不是很和谐，整个状况都不是特别安定的状态。'],
            '偏财' => ['你这一生的住宅属于比较安稳的状态，能有一个安定的居住场所，说明整个运势也会顺利许多，从住宅运势上来说，对你的家庭成员以及健康都有些许帮助。', '你这一生的住宅属于不太安稳的状态，整个住宅的运势也受到了影响，导致家庭成员之间相处的也不是很和谐，恐还会出现运势持续下降的情况。'],
        ];
        $list1 = [
            '比劫' => ['比肩', '劫财'], '印枭' => ['正印', '偏印'],
            '官杀' => ['正官', '七杀'], '才财' => ['正财', '偏财'], '食伤' => ['食神', '伤官'],
        ];
        // 0喜神、1用神、2仇神、3忌神、4闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $yongArr = $list1[$xiYong['shen'][1]];
        $jiArr = $list1[$xiYong['shen'][3]];
        $god = $this->lunar->getGod();
        $godName = $god['year'];
        $str = '';
        if (in_array($godName, $yongArr)) {
            $str = $list[$godName][0];
        } elseif (in_array($godName, $jiArr)) {
            $str = $list[$godName][1];
        } elseif (!in_array($godName, ['正印', '偏印'])) {
            $str = '你这一生的住宅并不是特别的安稳，住宅运势可能也会受到一些影响，家庭成员之间的相处更是存在问题，这也说明可能和住宅的安稳存在着一些联系。';
        } else {
            $str = '你这一生住宅不太安稳，会出现经常搬家的情况，难以在一个地方安定下来，可能也意味着家庭成员之间的关系也不是十分的和谐，会有些许的变动发生。';
        }
        return $str;
    }

    /**
     * 环境的影响
     * @return string
     * @throws Exception
     */
    protected function getAffect(): string
    {
        $list = [
            '甲' => [
                '周围的环境与住宅之间的关系很容易就会影响到宅内人员的运势。而你的住宅（正印方位）位置万万不可有大树，否则会对宅内人员的平安造成不好的影响，容易出现多灾或者多疾之类的情况发生。', '周围环境的好坏跟住宅存在着一定的联系，你的住宅周围适合有些植物存在，比如一些低矮的植物之类，但不适宜过高过大，对于住宅以及家宅人口的运势来说都有着一定的好处。',
            ],
            '乙' => [
                '住宅门前不适合有花草或者是高大的竹林，不仅仅是影响住宅的稳定性，从住宅运势来说家宅内小孩子容易多灾，发生一些意外事故，而女性很容易有身体健康上的担忧，和其他的心烦事。', '住宅门前适宜有植物的存在，不仅仅可以美化家居周围的环境，也可以使住宅的空气清新，从住宅运势来说也是有助于家宅好运，但门前的植物不宜过大过高，会对家宅人口健康造成不利。',
            ],
            '丙' => [
                '住宅旁边最好是不要有餐厅、歌厅、商场、烟囱等，虽然某些建筑会有利于生活的方便，但是人流密度较高，有时反而会给住宅安全，以及隐秘方面都带去不好的影响，实在是得不偿失。', '住宅周围最好是比较宽阔的地方，也没有高大的建筑阻挡，对于住宅来说，代表着一定的好运，家宅中有事业及工作者，都能够有着不错的成就，住宅背后有饱满秀丽之山更说明家宅人丁兴旺，财运丰隆。',
            ],
            '丁' => [
                '住宅周围忌讳人流密集较多的建筑存在，要尽量的避免餐厅、歌厅、商场、烟囱等建筑，虽然有些建筑是会给生活带来许多的便利，但对于住宅运势来说是非常不利的，会影响家宅人口的健康及事业发展。', '住宅周围的环境和住宅运势之间密切联系，所以住宅周围一定要忌讳不适合的高楼建筑，比如餐厅、商场等，住宅周围是宽阔的环境，会让家宅人口在事业中有着很多发展的可能，拥有许多财运。',
            ],
            '戊' => [
                '住宅周围的环境不好，说明住宅运势也会有所影响。你的住宅周围（正印方位）位置中，很忌讳有土堆及土包，或者是别人的屋脊相冲自己的住宅，或者有高楼挡住本宅的阳光，会有破财、疾病、官司的情况发生。', '在你的住宅周围当中，一般都会有土堆及土包，或者是别人的屋脊相冲着自己的住宅，或者有高楼挡住本宅的阳光，故容易和邻里之间出现口舌之争，要小心行事。',
            ],
            '己' => [
                '在你的住宅周围最好是不要有直冲房子的道路，道路有可能会冲克大门，土坎、土包、坟墓更加忌讳，会影响到你的住宅运势，容易出现口舌之争、或有小人陷害等一些怪异的事情。', '住宅周围的环境较好，那么家宅也能好运连连，在你的住宅周围中可能会有道路冲克大门的情况，或者是有土坎、土包、坟墓，这些对于住宅来说并不太好，会影响到家宅人员的健康。',
            ],
            '庚' => [
                '或许某些建筑对于生活来说非常的便利，但对住宅运势来说绝对是弊大于利的。你的住宅周围最好是不要有医院、学校、文化经营或者影院等，容易对家宅人员的身体健康造成不好的影响。', '住宅周边的地势好坏和家宅运有着密切的联系，在你的住宅周围中可能会有医院、学校、文化经营或者影院等建筑，建议在购房前做好调查，以免为家居生活带来不利的影响。',
            ],
            '辛' => [
                '你的住宅周围可以选择一些植物栽种，但不能选择过于高大的植物，周围空阔的环境跟密集的建筑物相比，家宅运会有很大的差距，所以最好是选择在空阔的地方，能够获利多多。', '在你的住宅附近可能会有医院、学校、文化经营或者影院等建筑，如此多的滞气积聚在一起，不利于人的身心健康，严重影响工作和学习的心情，建议购房前要多加注意。',
            ],
            '壬' => [
                '住宅附近如果出现坟场，不管远近都是非常的不利的，家宅人员的身体健康以及事业发展都会受到严重的影响，你的住宅附近最忌讳的就是出现坟场等晦气较重的建筑。', '你的住宅附近可能会有坟场的存在，尽管是在距离住宅较远的位置其实都是有所影响的，尤其是对于家宅人员的健康会表现的十分的明显，所以购房必须要慎重。',
            ],
            '癸' => [
                '你的住宅附近最忌讳的就是门口对着一颗大树或者是电杆，高压电杆更加的忌讳，会干扰家宅中本身正常的磁场，造成不良的影响，多数都会表现在身体健康上面。', '在你的住宅附近中最有可能出现的是门口对着一棵大树或者是电杆以及高压电杆，这样的环境很容易使家宅人口出现意外伤灾，对事业的发展极其不利，最好避免居住。',
            ],
            '子' => [
                '你的住宅附近最忌讳的就是前后左右均被立交桥或高架路紧紧环抱，经常有交通事故发生，更影响居住心态，凡遇五花大绑格局，主泄财，多数是财运差，事业发展缓慢。', '在你的住宅周围很容易出现立交桥或者是高架路，被这样的环境紧紧环抱，对于家宅运来说其实非常的不好，对居住其中者身心皆有害，故选择住宅时一定要认真观察更佳。',
            ],
            '丑' => [
                '你的住宅附近最忌讳的是有戏院、电影院的存在，开场时，人气众多，散场后，一哄而散。气场严重失衡，所以这附近居住的人运气也会反复无常，时好时坏，购房时可仔细观察。', '在你的住宅附近容易有戏院或者是电影院，表面看是非常热闹的景象，但其实住宅很忌讳这样的环境，开场时，人气众多，散场后，一哄而散，会使居住者的运气也反复无常。',
            ],
            '寅' => [
                '对于你来说，住宅最忌讳的是不见阳光，黑卧室、黑厨房、黑卫生间，享受阳光的基本权利被剥夺，对住宅运势来说也是弊大于利，在购买房子时需注意是否前方的高楼过高挡住了阳光。', '住宅周围有优质的外部环境，是营造家宅运的基础条件，你的住宅附近最有可能出现较高的建筑或者是大树，影响了住宅的光线，故居住者的健康以及财运可能都会受阻。',
            ],
            '卯' => [
                '你的住宅附近最忌讳的是庙宇、神祠等地方，人在遇到忧愁、郁闷时，便会到庙宇这类的场所寻求神灵的庇护，以求心情放松，所以和这些地方太靠近，对人的心理影响，尤其是孩子是极为不妥的。', '住宅附近如果有庙宇、神祠等地方，令人精神不安，情绪反复无常，所以这样的环境对于家宅运来说是有着很不好的影响，你在购房时需要注意远离这样的住宅。',
            ],
            '辰' => [
                '你的住宅附近最忌讳的是有政府机关、消防队、医院、垃圾池等煞气重的地方，会导致家宅人员有暗病缠身，以及和小人纠缠不清，脾气暴躁，官灾是非多多，因此买房必须慎重。', '住宅附近的环境其实对于家宅运来说是非常重要的，在你的住宅附近可能会有政府机关、消防队、医院、垃圾池等煞气重的地方，长此以往，容易影响家宅人员的身体健康。',
            ],
            '巳' => [
                '在你的住宅位置周围中最好是要忌讳国家单位、变压器、邮局、广场等文化性娱乐场所，会影响家宅人员的健康问题，职场上面也会受到影响，住宅及时的远离忌讳的建筑物，住宅运也能蒸蒸日上。', '你的住宅可能会紧挨国家单位、变压器、邮局、广场等文化性娱乐场所，不是犯罪就是生病、倒运之人，家宅运较差，房屋周围的环境和家宅财运，身体健康都有着紧密的联系。',
            ],
            '午' => [
                '住宅周围的环境和住宅运势有着很大的联系，你的住宅周围适合一些植物，不仅仅可以绿化环境，还能清新空气，也能带给住宅好运，当然不能选择太过高大的植物种类，以免带给住宅压迫感。', '你的住宅附近可能会有较大的植物存在，住宅或许会显得死气沉沉，没有阳光照射，对家宅人员中小孩的影响最大，住宅附近可以选择有植物存在，但是不适宜太过高大的植物。',
            ],
            '未' => [
                '你的住宅附近很忌讳有小桥冲射，不利于家宅人员的健康，运势可能也会受到阻碍，周围环境的好坏和家宅运之间有着较大的联系，购房时一定要选择好住宅周围环境。', '在你的住宅附近可能会有小桥冲射，要避免居住，这样的环境对于家宅运来说极其不利，尤其是家宅人员的身体健康，有想要升职加薪的人员事业发展也会受到影响。',
            ],
            '申' => [
                '你的住宅附近最忌讳的是菜市场以及垃圾站，菜市场和垃圾站都是环境不好，卫生很差的地方，有时甚至还有难闻的气味，住在此环境对家宅人员的健康大为不利。', '在你的住宅附近可能会有菜市场以及垃圾站，虽然对于生活非常的便利，但是从另外一方面来说，这两个地方对于家宅运的影响很大，周围环境和家宅运息息相关，一定要谨慎选择。',
            ],
            '酉' => [
                '在你的住宅附近最忌讳的就是有车站以及机场，虽然对于生活来说十分的便利，但是其实这样的环境对于家宅运来说非常不利，各种各样的人流涌动，影响家宅财运及事业发展。', '你的住宅附近可能会有车站以及机场，出行来说非常的方便，但是长期居住在嘈杂，人流涌动较大的环境中，家宅运及其不好，容易影响家宅人员的财运以及身体健康。',
            ],
            '戌' => [
                '你的住宅附近最忌讳的是有加油站，虽然说加油站对于生活来说十分的便利，但是如果长期居住在加油站的住宅，对于居住安全藏有隐患，从家宅运来说，频繁的汽车往来，噪音都对家宅人员健康有害。', '你的住宅附近可能会有加油站的存在，在购房时一定要尽量的避免，长期居住在加油站的附近，从家宅运来说，频繁的汽车往来，噪音都对家宅人员的健康有害。',
            ],
            '亥' => [
                '你的住宅附近不适合有学校、菜市场、影院，这些地方白天都是热热闹闹的，但是一到晚上就会变得非常的冷清，一片寂静，从家宅运来说，是非常不吉的，对家宅人员健康极为不利。', '你的住宅附近可能会有学校、菜市场、影院等人流聚集较多的地方，这些地方白天都是比较热闹的，但是到了晚上就会非常的寂静，容易导致家宅运势反复无常。',
            ],

        ];
        $listKey = [
            'y' => ['m'],
            'm' => ['y', 'd'],
            'd' => ['m', 'h'],
            'h' => ['d'],
        ];
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $god = [
            $this->lunar->getGod(),
            $this->getDzGodFirst(),
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wuXingAttr = $this->lunar->wuXingAttr;
        $str = $wuXingAttr[$jiNian['m'][0]];
        $gz = $jiNian['m'][0];
        $other = [];
        foreach ($god as $k => $v) {
            foreach ($v as $k1 => $v1) {
                if (!in_array($v1, ['正印', '偏印'])) {
                    continue;
                }
                $tmpKey = $listKeyJi[$k1];
                $gz = $jiNian[$tmpKey][$k];
                $str = $wuXingAttr[$gz];
                foreach ($listKey[$tmpKey] as $v) {
                    $other[] = $wuXingAttr[$jiNian[$v][$k]];
                }
                break 2;
            }
        }
        if (empty($other)) {
            $other = [
                $wuXingAttr[$jiNian['y'][0]], $wuXingAttr[$jiNian['d'][0]],
            ];
        }
        $index = 0;
        foreach ($other as $key => $val) {
            if (in_array($str . $val, $xianfuList)) {
                $index = 1;
                break;
            }
        }
        return $list[$gz][$index] ?? $list['甲'][0];
    }

    /**
     * 获得房子来源和引导语
     * @return array
     * @throws Exception
     */
    protected function getKeyIndex(): array
    {
        /**
         * 1    月干印星、日坐印星
         * 2    年官月印、官印相生
         * 3    印旺为病、财星为用
         * 4    食伤偏旺、日主偏弱
         * 5    身弱杀旺、命局透比
         * 6    正印临官星，官星为用
         * 7    正印临印，印星为用
         * 8    正印临财星，财星为用
         * 9    正印临食伤，食伤为用
         * 10    正印临比劫，比劫为用
         */
        $god = $this->lunar->getGod();
        $_god = $this->lunar->_getGod();
        // 0喜神、1用神、2仇神、3忌神、4闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);;
        $godList = [
            'y' => array_merge([$god['year']], $_god['year']['god']),
            'm' => array_merge([$god['month']], $_god['month']['god']),
            'd' => $_god['day']['god'],
            'h' => array_merge([$god['hour']], $_god['hour']['god']),
        ];
        // 比肩 劫财 食神 伤官 偏财 正财 七杀 正官 偏印 正印
        $list = [
            ['正印', '偏印'],
            ['正官', '七杀'],
        ];
        $yongIndex = $this->getYindex($xiYong['shen'][1]);
        $jiIndex = $this->getYindex($xiYong['shen'][3]);;
        if (in_array($god['month'], $list[0]) && in_array($_god['day']['god'][0], $list[0])) {
            $keyIndex = 0;
        } elseif (array_intersect($list[1], $godList['y']) && array_intersect($list[0], $godList['m'])) {
            // 2判断用户八字中年干或者年支中有没有官星存在，如果有再看对应的月干或者月支中有没有印星存在
            $keyIndex = 1;
        } elseif ($xiYong['shen'][3] == '印枭' && $xiYong['shen'][1] == '才财') {
            // 3八字中印星为忌神且旺，同时财星为用神
            $keyIndex = 2;
        } elseif ($this->getFour()) {
            $keyIndex = 3;
        } elseif ($this->getFive()) {
            $keyIndex = 4;
        } elseif ($this->getGodBeside('正印', ['正官', '七杀'])) {
            $keyIndex = 5;
        } elseif ($this->getGodBeside('正印', ['正印', '偏印'])) {
            $keyIndex = 6;
        } elseif ($this->getGodBeside('正印', ['正财', '偏财'])) {
            $keyIndex = 7;
        } elseif ($this->getGodBeside('正印', ['食神', '伤官'])) {
            $keyIndex = 8;
        } else {
            $keyIndex = 9;
        }
        return [$keyIndex, $yongIndex, $jiIndex];
    }

    /**
     * 获得用忌序号
     * @param string $str
     * @return int
     */
    protected function getYindex(string $str): int
    {
        $yongIndex = 9;
        switch ($str) {
            case '官杀':
                $yongIndex = 5;
                break;
            case '印枭':
                $yongIndex = 6;
                break;
            case '才财':
                $yongIndex = 7;
                break;
            case '食伤':
                $yongIndex = 8;
                break;
        }
        return $yongIndex;
    }

    /**
     * 日主身弱同时八字中有食伤且食伤偏旺
     * @return bool
     * @throws Exception
     */
    protected function getFour(): bool
    {
        // 4日主身弱同时八字中有食伤且食伤偏旺
        $god = $this->lunar->getGod();
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wangdu = BaziExt::getWangDu($jiNian);
        if (!in_array($wangdu, ['从弱格', '身弱格'])) {
            return false;
        }
        if ($this->getGodShen('食神') || $this->getGodShen('伤官')) {
            return true;
        };
        return false;
    }

    /**
     * 5日主身弱，且七杀旺，同时天干中有比劫
     * @return bool
     * @throws Exception
     */
    protected function getFive(): bool
    {
        $god = $this->lunar->getGod();
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wangdu = BaziExt::getWangDu($jiNian);
        if (!in_array($wangdu, ['从弱格', '身弱格'])) {
            return false;
        }
        if (empty(array_intersect(['比肩', '劫财'], $god))) {
            return false;
        }
        return $this->getGodShen('七杀');
    }

    /**
     * 判断有没有十神且相邻的有相应的十神
     * @param string $godName 十神名
     * @param array $beside 相邻的十神
     * @return bool
     * @throws Exception
     */
    protected function getGodBeside($godName, $beside): bool
    {
        $list = ['year' => ['month'], 'month' => ['year', 'day'], 'day' => ['month', 'hour'], 'hour' => ['day']];
        $god = [
            $this->lunar->getGod(),
            $this->getDzGodFirst(),
        ];
        $res = false;
        foreach ($god as $k => $v) {
            $index = array_search($godName, $v);
            if ($index) {
                $keyList = $list[$index];
                foreach ($keyList as $v2) {
                    $godsib = $v[$v2];
                    if (in_array($godsib, $beside)) {
                        $res = true;
                        break 2;
                    }
                }
            }
        }
        return $res;
    }

    /**
     * 获得地支的第一个
     * @return array
     */
    private function getDzGodFirst(): array
    {
        $_god = $this->lunar->_getGod();
        return [
            'year' => $_god['year']['god'][0],
            'month' => $_god['month']['god'][0],
            'day' => $_god['day']['god'][0],
            'hour' => $_god['hour']['god'][0],
        ];
    }

    /**
     * 十神在八字中是否扶助
     * @param $godName
     * @return bool
     */
    protected function getGodShen($godName): bool
    {
        $listKey = [
            'y' => ['m'],
            'm' => ['y', 'd'],
            'd' => ['m', 'h'],
            'h' => ['d'],
        ];
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $god = $this->lunar->getGod();
        $_god = $this->getDzGodFirst();
        $jiNianWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $jiNianWx[$k] = [
                $wuXingAttr[$v[0]], $wuXingAttr[$v[1]],
            ];
        }
        $indexTg = array_search($godName, $god);
        if ($indexTg) {
            $indexTg = $listKeyJi[$indexTg];
            foreach ($listKey[$indexTg] as $v) {
                if (!in_array($jiNianWx[$indexTg][0] . $jiNianWx[$v][0], $xianfuList)) {
                    continue;
                }
                return true;
            }
        }
        $indexdz = array_search($godName, $_god);
        if ($indexdz) {
            $indexdz = $listKeyJi[$indexdz];
            foreach ($listKey[$indexdz] as $v) {
                if (!in_array($jiNianWx[$indexdz][1] . $jiNianWx[$v][1], $xianfuList)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 购房
     * @return array
     */
    protected function getGouFan(): array
    {
        $listYong = $this->getYongJiList();
        $listji = $this->getYongJiList(0);
        $yinList = ['正印', '偏印'];
        $useYear = (int)$this->lunar->dateTime->format('Y');
        $wuXingAttr = $this->lunar->wuXingAttr;
        $jiNian = $this->lunar->getLunarTganDzhi();
        $god = $this->lunar->getGod();
        $godDz = $this->getDzGodFirst();
        $god11 = [$god, $godDz];
        $list2 = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $list3 = [
            ['正官', '七杀'], ['食神', '伤官'], ['正财', '偏财'], ['正印', '偏印'], ['比肩', '劫财'],
        ];
        $listDzShen = [
            '甲子', '丙寅', '丁卯', '戊辰', '已巳', '辛未', '壬申', '癸酉', '乙亥', '庚辰', '己丑', '戊戌',
            '辛丑', '丙午', '庚戌', '壬子', '甲寅', '乙卯', '丁巳', '戊午', '已未', '庚申', '辛酉', '癸亥',
        ];
        $list4 = ['year' => 'month', 'month' => 'day', 'day' => 'hour', 'hour' => ''];
        $fateTg = $this->getfateYearTg();
        foreach ($godDz as $v) {
            if (in_array($v, $god)) {
                continue;
            }
            $god[] = $v;
        }
        $res = [];
        if (array_intersect($yinList, $god)) {
            $tmpKey = [];
            foreach ($god11 as $k => $v) {
                foreach ($v as $k1 => $v1) {
                    if (in_array($v1, $yinList)) {
                        $tmpKey[$k] = $k1;
                        break;
                    }
                }
            }
            $count = 0;
            foreach ($god as $v) {
                if (in_array($v, $yinList)) {
                    $count++;
                }
            }
            $tmpTg = array_search('正印', $god11[0]);
            if (false == $tmpTg) {
                $tmpTg = array_search('偏印', $god11[0]);
            }
            if (false == $tmpTg) {
                $tmpTg = array_search('正印', $god11[1]);
            }
            if (false == $tmpTg) {
                $tmpTg = array_search('偏印', $god11[1]);
            }
            $tmpTg = $list2[$tmpTg];
            $ii = 0;
            if ($count >= 3) {
                // 流年逢印星
                for ($i = 30; $i < 71; $i++) {
                    $tmpYear = $useYear + $i;
                    $tmpJNYear = BaziExt::getGanZhi($tmpYear);
                    if (in_array($this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJNYear[0]), $yinList) && in_array($tmpJNYear[0] . $tmpJNYear[1], $listDzShen)) {
                        if ($ii < 1) {
                            $res[$tmpYear] = '会出现购房的情形。';
                        }
                        $ii++;
                        continue;
                    }
                    foreach ($tmpKey as $k => $v) {
                        if ($k && BaziExt::getXianChongDz($jiNian[$list2[$v]][$k], $tmpJNYear[$k])) {
                            $res[$tmpYear] = '会出现搬迁的情形。';
                            break;
                        }
                        if ($k == 0 && BaziExt::getXianChongTg($jiNian[$list2[$v]][$k], $tmpJNYear[$k])) {
                            $res[$tmpYear] = '会出现搬迁的情形。';
                            break;
                        }
                    }
                }
            }
            $guanPos = $this->getGodPos($list3[0], $god11);
            $yinPos = $this->getGodPos($list3[3], $god11);
            $flag = false;
            foreach ($guanPos as $val) {
                if (in_array($list4[$val], $yinPos)) {
                    $flag = true;
                    break;
                }
            }
            $dayDzKey = in_array($jiNian['d'][0], ['辰', '戌', '丑', '未']) ? 1 : 0;
            $ii = 0;
            $xx = 0;
            $yy = 0;
            $zz = 0;
            for ($i = 30; $i < 71; $i++) {
                $tmpYear = $useYear + $i;
                $liufatetg = $this->getLiuTg($tmpYear, $fateTg);
                $tmpJNYear = BaziExt::getGanZhi($tmpYear);
                $godName1 = $this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJNYear[0]);
                if (!BaziExt::getXianShenTg($liufatetg, $tmpJNYear[0])) {
                    continue;
                }
                // 八字中同时含有官星和印星，且官星在印星左边相临的柱上，遇流年十神为正官的年份
                if (array_intersect($list3[0], $god) && array_intersect($list3[3], $god)) {
                    if ($godName1 == '正官' && $flag) {
                        if ($ii % 3 == 0) {
                            $res[$tmpYear] = '会有置办房产的情形。';
                        }
                        $ii++;
                        continue;
                    }
                }
                // 八字中财星临印星，遇财星或者生助财星的流年
                if ($this->getGodBeside('正财', $list3[3]) || $this->getGodBeside('偏财', $list3[3])) {
                    if ($this->getGodShen($godName1) && BaziExt::getXianShenTg($tmpJNYear[0], $jiNian['d'][0])) {
                        if ($xx % 3 == 0) {
                            $res[$tmpYear] = '可能因意外收入而实现买房愿望。';
                        }
                        $xx++;
                        continue;
                    }
                }
                // 八字中比劫旺（比劫未被相临干支克制），遇官星（官星不被地支克制）流年的年份
                if ($this->getGodShen('比肩') && $this->getGodShen('劫财')) {
                    if (in_array($godName1, $list3[0]) && BaziExt::getXianShenTg($tmpJNYear[0], $jiNian['d'][0])) {
                        if ($yy % 3 == 0) {
                            $res[$tmpYear] = '有轻松置办房产的可能。';
                        }
                        $yy++;
                        continue;
                    }
                }
                // 八字地支含有(辰戌丑未)其中之一的时候，遇到与之相冲的年份
                if (BaziExt::getXianChongTg($jiNian['d'][0], $tmpJNYear[0]) && $dayDzKey) {
                    if ($zz % 3 == 0) {
                        $res[$tmpYear] = '有置办产业房子的机会。';
                    }
                    $zz++;
                    continue;
                }
            }
        } else {
            // 无印星
            $listYin = [
                '印星' => ['god' => ['正印', '偏印'], 'info' => '会有造宅、迁宅、购置住宅的行为。'],
                '官星' => ['god' => ['正官', '七杀'], 'info' => '会有造宅、购宅和迁宅的情况发生。'],
            ];
            foreach ($listYin as $k => $v) {
                $tmp = $this->getYearByFateGod($v['god']);
                foreach ($tmp as $v1) {
                    $res[$v1] = $v['info'];
                }
            }
            $startYear = $useYear + 30;
            for ($i = 0; $i < 40; $i++) {
                $tmpYear = $startYear + $i;
                $tmpJianNianYear = BaziExt::getGanZhi($tmpYear);
                if ($wuXingAttr[$tmpJianNianYear[1]] == '土' && in_array($this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJianNianYear[0]), $listYong)) {
                    $res[$tmpYear] = '会发生购置房屋的情形。';
                    break;
                }
            }
        }
        for ($i = 30; $i < 71; $i++) {
            $tmpYear = $useYear + $i;
            $liufatetg = $this->getLiuTg($tmpYear, $fateTg);
            $tmpJNYear = BaziExt::getGanZhi($tmpYear);

            $godName1 = $this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJNYear[0]);
            // 当八字中有财星且为用神，大运流年十神同时为比劫的年份（且大运流年十神相同）
            if (array_intersect($list3[2], $god) && array_intersect($list3[2], $listYong) && in_array($godName1, $list3[4])) {
                $res[$tmpYear] = '有可能因买房而耗尽家财。';
                break;
            }
            // 当八字中有食神且为用神，流年十神为偏印的年份（且流年地支生助流年天干）
            if (in_array('食神', $god) && array_intersect($god, $listYong) && $godName1 == '偏印') {
                $res[$tmpYear] = '会有买房子盖房子的情形。';
                break;
            }
            // 当八字中有比劫且为用神，流年十神为七杀的年份（且流年地支生助流年天干）
            if (array_intersect($list3[4], $god) && array_intersect($list3[4], $listYong)) {
                if ($godName1 == '七杀') {
                    $res[$tmpYear] = '会有建房买房的意动。';
                    break;
                }
                // 当八字中有比劫且为用神，流年十神为食伤的年份（且流年地支生助流年天干）
                if (in_array($godName1, $list3[4])) {
                    $res[$tmpYear] = '可能因子女的关系而考虑买房。';
                    break;
                }
            }
            // 当八字中有财星且为忌神，流年十神为财星的年份（且流年地支生助流年天干）
            if (array_intersect($list3[2], $god) && array_intersect($list3[2], $listji) && in_array($godName1, $list3[2])) {
                $res[$tmpYear] = '会因获得大笔收入而动买房的心思。';
                break;
            } elseif (array_intersect($list3[3], $god) && array_intersect($list3[3], $listji) && in_array($godName1, $list3[2])) {
                // 当八字中有印星且为忌神，流年十神为财星的年份（且流年地支生助流年天干）
                $res[$tmpYear] = '拥有足够资金后开始寻思买房。';
                break;
            }
            // 当八字中有伤官且为忌神，大运流年十神同时为官星的年份（且大运流年十神相同）
            if (in_array('伤官', $god) && in_array('伤官', $listji) && in_array($godName1, $list3[0])) {
                $res[$tmpYear] = '会因单位或者政府的福利而获得房产。';
                break;
            }
        }
        $res2 = [];
        if (count($res) < 2) {
            for ($i = 30; $i < 71; $i++) {
                $tmpYear = $useYear + $i;
                $tmpJNYear = BaziExt::getGanZhi($tmpYear);
                if ($wuXingAttr[$tmpJNYear[1]] == '土' && in_array($this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJNYear[0]), $listYong)) {
                    $res[$tmpYear] = '会有购置房屋的情况发生。';
                    break;
                }
            }
            for ($i = 30; $i < 71; $i++) {
                $tmpYear = $useYear + $i;
                $tmpJNYear = BaziExt::getGanZhi($tmpYear);
                if (in_array($this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJNYear[0]), $yinList) && in_array($tmpJNYear[0] . $tmpJNYear[1], $listDzShen)) {
                    $res[$tmpYear] = '很有可能出现购房的情形。';
                    break;
                }
            }
        }
        ksort($res);
        foreach ($res as $k => $v) {
            $res2[] = [
                'year' => $k,
                'info' => $v,
            ];
        }
        return $res2;
    }

    /**
     * 大运和流年同时为印星或官星
     * @param $godArr
     * @return array
     * @throws \Exception
     */
    protected function getYearByFateGod($godArr): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $fate = $this->lunar->getFate();
        $userYear = (int)$this->lunar->dateTime->format('Y');
        $year = [];
        foreach ($fate['eight']['_god'] as $k => $v) {
            if (!in_array($v, $godArr)) {
                continue;
            }
            $tmpStartYear = $fate['eight']['year'][$k];
            for ($i = 0; $i < 10; $i++) {
                $tmpYear = $tmpStartYear + $i;
                $age = $tmpYear - $userYear + 1;
                if ($age < 30 || $age > 70) {
                    continue;
                }
                $tmpJianNianYear = BaziExt::getGanZhi($tmpYear);
                $tmpGodName = $this->lunar->getGodNameByTg($jiNian['d'][0], $tmpJianNianYear[0]);
                if ($tmpGodName == $godArr[0]) {
                    $year[] = $tmpYear;
                }
            }
        }
        return $year;
    }

    /**
     * 获得Fate的起始天干
     * @return array
     * @throws \Exception
     */
    protected function getfateYearTg(): array
    {
        $res = [];
        $fate = $this->lunar->getFate();
        foreach ($fate['eight']['year'] as $v) {
            $gz = BaziExt::getGanZhi($v);
            $res[] = [$v, $v + 10, $gz[0]];
        }
        return $res;
    }

    /**
     * 获得当前流年大运天干
     * @param int $year
     * @param array $fateTg
     * @return string
     */
    protected function getLiuTg($year, $fateTg)
    {
        if ($year < $fateTg[0][0]) {
            return $fateTg[0][2];
        }
        $count = count($fateTg);
        if ($year > $fateTg[$count - 1][0]) {
            return $fateTg[$count - 1][2];
        }
        $str = $fateTg[0][2];
        for ($i = 0; $i < $count - 1; $i++) {
            if ($year > $fateTg[$i][0] && $year < $fateTg[$i + 1][0]) {
                $str = $fateTg[$i][2];
            }
        }
        return $str;
    }

    /**
     * 获得当前用户用神或忌神list
     * @param $yong 1输出用list 0代表忌
     * @return array
     */
    protected function getYongJiList($yong = 1): array
    {
        $yonglist = [
            ['正官', '七杀', '正财', '偏财', '食神', '伤官'],
            ['正印', '偏印', '比肩', '劫财'],
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wuDu = BaziExt::getWangDu($jiNian);
        $index = 0;
        if (in_array($wuDu, ['身旺格', '从弱格'])) {
            $index = $yong ? 0 : 1;
        } else {
            $index = $yong ? 1 : 0;
        }
        return $yonglist[$index];
    }

    /**
     * 获得十神在八柱中的位置
     * @param $godArr
     * @param $list
     * @return array
     */
    protected function getGodPos($godArr, $list): array
    {
        $res = [];
        foreach ($godArr as $v) {
            foreach ($list as $v1) {
                if (array_search($v, $v1)) {
                    $res[] = array_search($v, $v1);
                }
            }
        }
        if (empty($res)) {
            return [];
        }
        return array_unique($res);
    }

    /**
     * 住宅兴衰
     * @return string
     */
    protected function getXinShuai()
    {
        $god = [
            $this->lunar->getGod(), $this->getDzGodFirst(),
        ];
        $listRes = [
            '驿马' => '家宅兴盛，不过当住宅出现刚迁进或者即将出售，或者有人租居，或者即将变迁的情况时，会是四处奔波劳累的状态，但这些四处奔波的劳累是美好收获的前提，会让你获利多多。',
            '天德' => '家宅兴盛，住宅内人口或是为高知识的人，能够有职有权，不仅仅是事业上如此让人称心如意，在健康上面也少有疾病，会是长寿之人，家宅属于步步高升的状态。',
            '月德' => '家宅兴盛，运势大放异彩，尽管有时会遇到一些十分棘手的问题影响着各个方面的发展，但宅内人口或是为高知识的人，因此是能为有成就之人，能够化险为夷，让家宅变得更加繁荣昌盛。',
            '天乙' => '家宅兴盛，家宅内人口有升职加薪的可能，是有所成就之人，财运和事业运都是一天比一天好，感情方面也能得到进展，喜讯不断，生活甜蜜，不好的事情都会慢慢的得到一些改变。',
            '天医' => '家宅兴盛，宅内人口大都为有高知识的人，或会有所成就，是有职有权之人，既能够在职场上扩张社交人脉活跃发展，也可以处理好家中七大姑八大姨的亲戚关系，家宅兴盛，步步高升。',
            '将星' => '家宅兴盛，且财运福气较为绵长悠久，有延续几代人之势，若等到机遇来临时，努力和坚持都会有所收获，家人升职有望，加薪不断，一切都会变的非常顺利，获利多多。',
            '死' => '家宅气场不吉，尤其是住宅属于采光很差，或者是原来是墓地、坡地的时候，家宅会一直持续在衰败的状态当中，难以会有兴旺的转机，家宅内人口身体欠佳，运势不旺。',
            '绝' => '家宅气场不吉，尤其是当住宅采光差，或者原来是墓地、坡地之类，家宅运势一落千丈，主要表现为与人不睦，工作以及和亲戚之间恐都容易发生矛盾，工作上也会有压力增加。',
            '墓' => '家宅气场不吉，当住宅属于采光差，或者原来是墓地、破地之类时，可能会是难以兴旺的状况，想要家宅运势获得转变，可能还需要对住宅的周围环境进行一些改善。',
            '长生' => '家宅兴盛，且一生的居住也比较安稳，不会发生较大的变化，发生变化，也会往更好的方向变迁，家宅运势反而越发的顺利，工作中随着人脉逐渐宽广，会有升职加薪的可能，晚年子孙绕膝，可享清福。',
            '冠带' => '家宅兴盛，即使住宅发生变化，大多都会往好的方向变迁，说明家庭气场旺盛，家运兴旺昌盛。对于付出的努力，会在事业上面得到一些收获，能有升职加薪的可能，或小有成就。',
            '临官' => '家宅兴盛，一生的居住安稳，整个住宅大都会往好的方向前进，事业处于正在发展的家庭，家人中有学生或年轻人的话，会有着很好的进步，出现让人出乎意料的成绩。',
            '帝旺' => '家宅兴盛，居住安稳，如果住宅发生变化，也大多会往好的方向变迁，家宅运会越来越好，住宅内人口经过不断奋斗，事业财运都能够越过越旺，福气财气也会绵长悠久。',
            '咸池' => '家运气场不吉，尤其是家中的女性会受到极大的影响，身体健康欠佳，工作不顺，与人之间易发生矛盾，不仅如此，家宅内人口运势也不太顺利，整个家庭呈衰落的状态。',
            '桃花' => '家宅运不佳，住宅周围如果有隐藏的低矮隐形的气流，对于家宅内女性的健康以及各方面可能都会存在着较大的影响，不仅如此，宅内其他人员也同样会有危害。',
            '沐浴' => '家宅气场不吉，住宅周围如果有隐藏的低矮隐形的气流，住宅内人口身体欠佳，运势不旺，尤其是对于女性来说，不管是身体健康还是各方面可能都会有些危害存在。',
        ];
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $terrain = $this->lunar->getTerrain();
        $key = '';
        foreach ($god as $k => $v) {
            foreach ($v as $k1 => $v1) {
                if (in_array($v1, ['正印', '偏印'])) {
                    $key = $k1;
                    break 2;
                }
            }
        }
        $listYinYang = [];
        foreach ($jiNian as $k => $v) {
            $listYinYang[$k] = BaziExt::getYinYang($v[0]);
        }
        // 若八字中无印星则通过以下方法假借印星
        if (empty($key)) {
            // 若八字中无印星，则取月柱或时柱中与日元阴阳同性的一方为偏印，阴阳异性的一方为正印。若无同性或全是同性，则以时柱看偏印，月柱看正印。
            if ($this->lunar->sex) {
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'hour' : 'month';
            } else {
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'month' : 'hour';
            }
        }
        $key1 = $listKeyJi[$key];
        $terrainName = $terrain[$key];
        $listGod = BaziExt::getGodArr($jiNian, $this->lunar->sex);
        if (isset($listRes[$terrainName])) {
            return $listRes[$terrainName];
        }
        $listGodRes = $listGod[$key1];
        // 沐浴
        $listMuYu = ['甲子', '乙巳', '丙卯', '丁申', '戊卯', '己申', '庚午', '辛亥', '壬酉', '癸寅'];
        if (in_array('驿马', $listGodRes)) {
            return $listRes['驿马'];
        } elseif (in_array('天德贵人', $listGodRes)) {
            return $listRes['天德'];
        } elseif (in_array('月德贵人', $listGodRes)) {
            return $listRes['月德'];
        } elseif (in_array('天乙贵人', $listGodRes)) {
            return $listRes['天乙'];
        } elseif (in_array('天医', $listGodRes)) {
            return $listRes['天医'];
        } elseif (in_array('将星', $listGodRes)) {
            return $listRes['将星'];
        } elseif (in_array('咸池', $listGodRes)) {
            return $listRes['咸池'];
        } elseif (in_array('桃花', $listGodRes)) {
            return $listRes['桃花'];
        } elseif (in_array($jiNian['d'][0] . $jiNian[$key1][1], $listMuYu)) {
            return $listRes['沐浴'];
        } else {
            return '家宅兴盛，如若抓住合适的机遇，家宅中有人能升职加薪，各方面运势都会变得非常的顺利，前程无忧，而且这样的财运和福气也会一直绵长悠久，对家宅来说会喜讯不断。';
        }
    }
}
