<?php
// +----------------------------------------------------------------------
// | Lingzhengjiri.领证吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Lingzhengjiri
{
    use JiRiCheckTraits;

    /**
     * 男日历相关
     * @var Ex
     */
    protected Ex $mlunar;

    /**
     * 女日历相关
     * @var Ex
     */
    protected Ex $flunar;

    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'mtime' => input('mtime', '', 'trim'),
            'ftime' => input('ftime', '', 'trim'),
            'otime' => input('otime', '', 'trim'),
            'two' => input('two', 0, 'intval'),
            'limit' => input('limit', 0, 'intval'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'mtime|男方出生时间' => ['require', 'isDateOrTime:男方出生时间'],
                'ftime|女方出生时间' => ['require', 'isDateOrTime:女方出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'two|只看双日' => ['require', 'in:0,1'],
                'limit|查看天数' => ['require', 'between:1,720'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->mlunar = Ex::date($data['mtime'])->sex(0);
        $this->flunar = Ex::date($data['ftime'])->sex(1);
        $xiYong = BaziExt::getxiYongJi($this->mlunar);
        $xiYong2 = BaziExt::getxiYongJi($this->flunar);
        $result = [
            'm' => [
                'base' => $this->mlunar->getLunarByBetween(),
                'na_yin' => $this->mlunar->getNayin(),
                'like_god' => $data['source'] == 'wnl' ? $this->mlunar->getLikeGod2() : $this->mlunar->getLikeGod(),
                // 用神五行
                'yongshen' => $xiYong['wx'][1],
            ],
            'f' => [
                'base' => $this->flunar->getLunarByBetween(),
                'na_yin' => $this->flunar->getNayin(),
                'like_god' => $data['source'] == 'wnl' ? $this->flunar->getLikeGod2() : $this->flunar->getLikeGod(),
                // 用神五行
                'yongshen' => $xiYong2['wx'][1],
            ],
            'ji' => $this->getJiri(),
        ];
        return $result;
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = $this->orginData['otime'];
        $limit = $this->orginData['limit'];
        $isdouble = $this->orginData['two'];
        // 破日 月份+破日
        $listPo = ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'];
        $listWangWang = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        // 归忌
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        // 受死 农历月份+日支
        $listShouSi = ['1|戌', '2|辰', '3|亥', '4|巳', '5|子', '6|午', '7|丑', '8|未', '9|寅', '10|申', '11|卯', '12|酉'];
        // 小红沙日 农历月份+日支
        $listHongSha = ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'];
        // 杨公忌日 农历月份+日
        $listYangGong = ['1-13', '2-11', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        // 相冲日 相刑日 年支+流日支
        $listChongXin = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
            '子卯', '丑未', '寅巳', '卯子', '辰辰', '巳申', '午午', '未戌', '申寅', '酉酉', '戌丑', '亥亥',
        ];
        $listXiongShi = [
            '子' => '午时中午11点至13点', '丑' => '未时下午13点至15点', '寅' => '申时下午15点至17点', '卯' => '酉时下午17点至19点',
            '辰' => '戌时晚上19点至21点', '巳' => '亥时晚上21点至23点', '午' => '子时晚上23点至1点', '未' => '丑时凌晨1点至3点',
            '申' => '寅时凌晨3点至5点', '酉' => '卯时早上5点至7点', '戌' => '辰时早上7点至9点', '亥' => '巳时早上9点至11点',
        ];
        $yearGz = $this->mlunar->getLunarGanzhiYear();
        $yearGz1 = $this->flunar->getLunarGanzhiYear();
        $result = [];
        for ($i = 1; $i <= $limit; $i++) {
            $time = strtotime("{$startTime} +{$i} day");
            $timeStr = date('Y年m月d日', $time);
            $day = date('d', $time);
            // 判断单日
            if ($isdouble && ($day % 2 == 1)) {
                continue;
            }
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                continue;
            }
            $nongli = $huangli->getNongLi();
            $base = [
                'nongli' => [
                    'y' => $nongli['cn_y'],
                    'm' => $nongli['cn_m'],
                    'd' => $nongli['cn_d'],
                ],
                '_nongli' => [
                    'y' => (int)$nongli['y'],
                    'm' => (int)$nongli['m'],
                    'd' => (int)$nongli['d'],
                ],
                'jinian' => $huangli->getLunarTganDzhi(),
            ];
            $jiNian = $base['jinian'];
            $mzDz = $jiNian['m'][1] . $jiNian['d'][1];
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            // 彭祖忌 亥日例：所有地支为亥的日子都去掉
            if ($jiNian['d'][1] === '亥') {
                continue;
            }
            // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉
            if ($base['_nongli']['d'] == 1) {
                continue;
            }
            // 四立两分两至（反目日） 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至
            $jieQi = Huangli::isJieQi($time);
            if ($jieQi && in_array($jieQi, $list2)) {
                continue;
            }
            // 清明、七月半（七月十五）
            if ($jieQi === '清明' || ($base['_nongli']['m'] == 7 && $base['_nongli']['d'] == 15)) {
                continue;
            }
            // 去除破日+往亡+归忌
            if (in_array($mzDz, $listPo) || in_array($mzDz, $listWangWang) || in_array($mzDz, $listGuiJi)) {
                continue;
            }
            // 受死+小红沙日
            $strShouSi = $base['_nongli']['m'] . '|' . $jiNian['d'][1];
            if (in_array($strShouSi, $listShouSi) || in_array($strShouSi, $listHongSha)) {
                continue;
            }
            $strYangGong = $base['_nongli']['m'] . '-' . $base['_nongli']['d'];
            // 杨公忌日
            if (in_array($strYangGong, $listYangGong)) {
                continue;
            }
            // 建除为建、危、闭的日子
            $jianChu = $huangli->getJianChu();
            if (in_array($jianChu, ['建', '危', '闭'])) {
                continue;
            }
            // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天
            $tomorrowJieQi = Huangli::isJieQi($time + 86400);
            if ($tomorrowJieQi && in_array($tomorrowJieQi, $list2)) {
                continue;
            }
            // 正四废日
            // 干支
            $tmpGZ = [
                implode('', $jiNian['y']), implode('', $jiNian['m']), implode('', $jiNian['d']),
            ];
            if ($this->checkZhengSiFeiRi($jiNian['m'][1], $tmpGZ[2])) {
                continue;
            }
            // 当日正冲年份正好是新人出生年干支则去除该日
            if ($this->checkZhengChong($tmpGZ[0], implode('', $yearGz)) || $this->checkZhengChong($tmpGZ[0], implode('', $yearGz1))) {
                continue;
            }
            // 根据男女双方生年分别去掉相冲、相刑日。 年支+流日支
            if (in_array($yearGz[1] . $jiNian['d'][1], $listChongXin) || in_array($yearGz1[1] . $jiNian['d'][1], $listChongXin)) {
                continue;
            }
            $rdz = $jiNian['d'][1];
            $tmpRes = [
                'time' => $time,
                'gongli' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'week' => $week,
                'nongli' => $base['nongli'],
                'xiong' => $listXiongShi[$rdz] ?? '',
                'detail' => [],
            ];
            if ($this->checkBuJian($base['_nongli']['m'], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['不将', '有利嫁娶的传统吉日，寓意新人以后的生活平平顺顺。'];
            }
            if ($this->checkJiFen($base['_nongli']['m'], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['季分', '传统嫁娶吉日，寓意福泽绵长。'];
            }
            if ($this->checkSuiDe($jiNian['y'][0] . $jiNian['d'][0])) {
                $tmpRes['detail'][] = ['岁德', '德神护佑的吉日，积福之日，福气汇聚。'];
            }
            if ($this->checkSuiDeHe($jiNian['y'][0] . $jiNian['d'][0])) {
                $tmpRes['detail'][] = ['岁德合', '德神护佑的吉日，积福之日，福气汇聚。'];
            }
            $strmzrz = $jiNian['m'][1] . $jiNian['d'][1];
            if ($this->checkHongLuanTianXi($strmzrz)) {
                $tmpRes['detail'][] = ['红鸾天喜', '有利嫁娶的吉日，红鸾报喜。'];
            }
            if ($this->getSanHeByDz($strmzrz)) {
                $tmpRes['detail'][] = ['三合', '寓意新人互生互利的吉日，可作为领证的日子选用。'];
            }
            $strmzrg = $jiNian['m'][1] . $jiNian['d'][0];//月支+日干
            if ($this->checkTianDe($strmzrg) || $this->checkTianDe($strmzrz)) {
                $tmpRes['detail'][] = ['天德', '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。'];
            }
            if ($this->checkTianDeHe($strmzrg) || $this->checkTianDeHe($strmzrz)) {
                $tmpRes['detail'][] = ['天德合', '合德之神相助，各种凶煞伏藏不出，有小福的日子。'];
            }
            if ($this->checkYueDe($strmzrz)) {
                $tmpRes['detail'][] = ['月德', '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。'];
            }
            if ($this->checkYueDeHe($strmzrg) || $this->checkYueDeHe($strmzrz)) {
                $tmpRes['detail'][] = ['月德合', '得到五行力量的聚合，为有福之日，适宜进行入宅、祭祖等事。'];
            }
            if ($this->checkTianSe($jiNian['m'][1], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['天赦', '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。'];
            }
            if ($this->checkTianYuan($jiNian['m'][1] . $tmpGZ[2])) {
                $tmpRes['detail'][] = ['天愿', '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。'];
            }
            if ($this->checkYueEn($strmzrg)) {
                $tmpRes['detail'][] = ['月恩', '受恩之日，象征新生活新希望，领证可用。'];
            }
            if ($this->checkSiXian($jiNian['m'][1], $jiNian['d'][1])) {
                $tmpRes['detail'][] = ['四相', '拥有四时王相贵气的日子，纯粹的小吉日。'];
            }
            if ($this->checkShiDe($strmzrz)) {
                $tmpRes['detail'][] = ['时德', '得到天地舒畅之气，得到四时之气的祝福，小吉日。'];
            }
            if ($this->checkXianXing($base['_nongli']['m'], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['显星', '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。'];
            }
            if ($this->checkQuXing($base['_nongli']['m'], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['曲星', '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。'];
            }
            if ($this->checkChuanXing($base['_nongli']['m'], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['传星', '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。'];
            }
            if (empty($tmpRes['detail'])) {
                continue;
            }
            $result[] = $tmpRes;
        }
        return $result;
    }

    /**
     * 正四废日
     * @param string $monthDz 月地支
     * @param string $daygz 日干支
     * @return bool
     */
    private function checkZhengSiFeiRi(string $monthDz, string $daygz): bool
    {
        switch ($monthDz) {
            case '寅':
            case '卯':
            case '辰':
                $bool = in_array($daygz, ['庚申', '辛酉']);
                break;
            case '巳':
            case '午':
            case '未':
                $bool = in_array($daygz, ['壬子', '癸亥']);
                break;
            case '申':
            case '酉':
            case '戌':
                $bool = in_array($daygz, ['甲寅', '乙卯']);
                break;
            default:
                $bool = in_array($daygz, ['丙午', '丁巳']);
                break;
        }
        return $bool;
    }

    /**
     * 当日正冲年份正好是新人出生年干支则去除该日
     * @param string $liuRi 日干支
     * @param string $yeargz 年干支
     * @return bool
     */
    protected function checkZhengChong(string $liuRi, string $yeargz): bool
    {
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        $str = $list[$liuRi] ?? '';
        $bool = false;
        if ($str === $yeargz) {
            $bool = true;
        }
        return $bool;
    }
}
