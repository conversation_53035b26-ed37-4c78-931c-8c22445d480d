<?php
// +----------------------------------------------------------------------
// | 提车吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\traits\jiri\JiRiBaseTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Tichejiri
{
    use JiRiBaseTraits;

    /**
     * 输入数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 五行属性
     * @var array
     */
    protected array $wuXingAttr = [];

    /**
     * 动工吉日
     * @param string $time
     * @param string $sex
     * @param string $otime
     * @param int $limit
     * @return array
     * @throws Exception
     */
    public function index($time = '', $sex = '', $otime = '', $limit = 365)
    {
        $data = [
            // 用户生日
            'time' => $time,
            // 性别 0男 1女
            'sex' => $sex,
            // 订单时间
            'otime' => $otime,
            // 限制天数
            'limit' => $limit,
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'sex|性别' => ['require', 'in:0,1'],
                'limit|限制天数' => ['require', 'number', 'between:20,731'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        $dayList = $this->getDayList();
        $baziEx = new BaziEx($this->lunar);
        $xiY = $baziEx->getxiYongJi3();
        $result = [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 用神五行
            'yongshen' => $xiY['xy']['yong'],
            // 喜神五行
            'xishen' => $xiY['xy']['xi'],
            // 喜用神
            'like_god' => $data['source'] == 'wnl' ? $this->lunar->getLikeGod2() : $this->lunar->getLikeGod(),
            // 吉日
            'ji' => $dayList,
            // 风水建议
            //'fengshui' => $this->getFengShui()
        ];
        return $result;
    }

    /**
     * 获得日期列表
     * @return array[]
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    protected function getDayList(): array
    {
        // 岁破 年+日
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        // 当月月支+地支
        $listShsd = [
            // 三合死地日
            '寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子',
            // 红沙日
            '寅酉', '卯巳', '辰丑', '巳酉', '午巳', '未丑', '申酉', '酉巳', '戌丑', '亥酉', '子巳', '丑丑',
            // 归忌
            '子寅', '丑子', '寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑',
            // 五鬼
            '子未', '丑戌', '寅午', '卯寅', '辰辰', '巳酉', '午卯', '未申', '申丑', '酉巳', '戌子', '亥亥',
            // 往亡
            '子戌', '丑丑', '寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未',
            // 九空
            '子申', '丑巳', '寅辰', '卯丑', '辰戌', '巳未', '午卯', '未子', '申酉', '酉午', '戌寅', '亥亥',
            // 破日
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        // 月家天官符 月干+日干支
        $listYueTianGuan = [
            '甲' => ['丁卯', '癸卯', '壬子', '丙子', '乙酉', '辛酉'],
            '乙' => ['戊辰', '丁丑', '癸丑', '丙戌', '壬戌'],
            '丙' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '丁' => ['乙丑', '辛丑', '甲戌', '庚戌', '癸未', '丁未', '壬辰'],
            '戊' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '己' => ['丁卯', '癸卯', '壬子', '丙子', '乙酉', '辛酉'],
            '庚' => ['戊辰', '丁丑', '癸丑', '丙戌', '壬戌'],
            '辛' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '壬' => ['乙丑', '辛丑', '甲戌', '庚戌', '癸未', '丁未', '壬辰'],
            '癸' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
        ];
        // 五墓 月+日干+日支
        $listWumu = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        // 四废 月+日干+日支
        $listShiFei = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
            // 丧车煞日
            '子丙午', '丑丙午', '寅庚申', '卯庚申', '辰庚申', '巳壬子', '午壬子', '未壬子', '申甲寅', '酉甲寅',
            '戌甲寅', '亥丙午', '子丁巳', '丑丁巳', '寅辛酉', '卯辛酉', '辰辛酉', '巳癸亥', '午癸亥', '未癸亥',
            '申乙卯', '酉乙卯', '戌乙卯', '亥丁巳',
        ];
        // 天干正冲
        $listChongTg = [
            '甲庚', '乙辛', '丙壬', '丁癸', '戊甲', '己乙', '庚丙', '辛丁', '壬戊', '癸己',
        ];
        // 相冲 或时破
        $listChongDz = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        // 五不遇时 流日天干	流时地支
        $listWuBu = [
            '甲午', '乙巳', '丙辰', '丁卯', '戊寅', '己丑', '庚子', '辛酉', '壬申', '癸未',
        ];
        // 五不归日
        $listWuBuGui = [
            '己卯', '辛巳', '丙戌', '壬辰', '丙申', '己酉', '辛亥', '壬子', '丙辰', '辛酉', '庚申',
        ];
        $listDes = [
            'xiao' => [
                '三会' => '五行力量统一，融合有力，避免与自身运势相冲。',
                '三合' => '五行聚合，有情相生，趋吉避凶。',
                '半三合' => '事从权宜，提车吉，可按需采用。',
                '六合' => '贵人助力，暗中帮扶，五行力量不明显。',
                '旺' => '符合四季旺衰变化规律，四时之力处于旺盛状态。',
                '长生' => '万物始生，出行平安，提车吉。',
                '冠带' => '万物渐荣，寓意吉祥，提车可用。',
                '临官' => '身禄近贵，五行为用，小吉之日。',
                '帝旺' => '逢时强盛，五行调和，提车主吉。',
            ],
            'da' => [
                '三会' => '吉神照拂，干支有力，对福主有生助作用，提车主吉。',
                '三合' => '五行力量聚合的日子，相扶相生，有趋吉避凶的作用。',
                '半三合' => '五行力量稍弱，但是对福主有助益作用，事从权宜，提车可用。',
                '六合' => '五行力量稍弱，但有贵人暗中帮扶的意象，可用于提车之日。',
                '旺' => '当季五行力量较强，对福主运势有提升的作用。',
                '长生' => '处悠闲之地，五行始生，欣欣向荣，有开拓进取的意象。',
                '冠带' => '处喜庆之地，五行逐渐兴荣，有小成、小贵的意象。',
                '临官' => '处拼搏之地，利官近贵，有付出能够得到回报的意象。',
                '帝旺' => '处荣发之地，主吉，利禄进财，有达到最旺盛时期的意象。',
            ],
        ];
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        // 用户生日
        $userMd = $base['_nongli']['m'] . '-' . $base['_nongli']['d'];
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $result = [
            'da' => [], 'xiao' => [],
        ];
        for ($i = 1; $i <= $this->orginData['limit']; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y年m月d日', $time);
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $jiXiong = $huangli->getJiXiong();
            if (!in_array('出行', $jiXiong['yi'])) {
                continue;
            }
            $base1 = $huangli->getLunarByBetween();
            // 过滤掉用户农历出生日期
            $liuMd = $base1['_nongli']['m'] . '-' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($liuMd, ['7-14', '7-15', '9-9', '10-10'])) {
                continue;
            }
            if ($userMd == $liuMd) {
                continue;
            }
            $jiNianTmp = $base1['jinian'];
            $jiNianDay = implode('', $jiNianTmp['d']);
            // 过滤五不归
            if (in_array($jiNianDay, $listWuBuGui)) {
                continue;
            }
            // 岁破过滤
            if ($listSuiPo[$jiNianTmp['y'][1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            $shiLiAndJ = $this->getJieQiDay(date('Y', $time));
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            if (in_array($timeStr, $shiLiAndJ)) {
                continue;
            }
            $jianchu = $huangli->getJianChu();
            // 过滤 破 建
            if (in_array($jianchu, ['破', '建'])) {
                continue;
            }
            // 三合死地和红沙日 过滤
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][1], $listShsd)) {
                continue;
            }
            // 月家天官符
            if (in_array($jiNianDay, $listYueTianGuan[$jiNianTmp['m'][0]])) {
                continue;
            }
            $yueRi2 = $jiNianTmp['m'][1] . $jiNianTmp['d'][0] . $jiNianTmp['d'][1];
            // 五墓+四废
            if (in_array($yueRi2, $listWumu) || in_array($yueRi2, $listShiFei)) {
                continue;
            }
            // 流日日天干不能冲事主的年天干
            if (in_array($jiNian['y'][0] . $jiNianTmp['d'][0], $listChongTg)) {
                continue;
            }
            if (in_array($jiNian['y'][1] . $jiNianTmp['d'][1], $listChongDz)) {
                continue;
            }
            $hourList = $huangli->getHourDetail();
            $jieqiArr = $huangli->getJieQiCur();
            $jieqiCur = $jieqiArr['current'][0];
            foreach ($hourList as $k1 => $v1) {
                // 流日地支	流时地支
                $hourTmp1 = $jiNianTmp['d'][1] . $v1['h'][1];
                $hourTmp2 = $jiNianTmp['d'][0] . $v1['h'][1];

                if (in_array($hourTmp1, $listChongDz)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 流日地支	流时地支
                if (BaziExt::getXianXinDz($jiNianTmp['d'][1], $v1['h'][1])) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 五不遇时 流日天干+流时地支
                if (in_array($hourTmp2, $listWuBu)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 日干+时干支
                $hourTmp3 = $jiNianTmp['d'][0] . $v1['h'][0] . $v1['h'][1];
                if ($this->getTianMen($jieqiCur, $hourTmp3)) {
                    continue;
                }
                // 日支+时支
                if ($this->getHuanDaoJiShi($hourTmp1)) {
                    continue;
                }
                // 日干+时干支
                if ($this->getHuanDaoJiShi($hourTmp3)) {
                    continue;
                }
                unset($hourList[$k1]);
            }
            $tmpWxNum = 0;
            $tmpRes = [];
            foreach ($hourList as $v1) {
                $tmpJiNian = $jiNianTmp;
                $tmpJiNian['h'] = $v1['h'];
                $chenJuWanDe = $this->getChenJuWanDe($tmpJiNian);
                if (empty($chenJuWanDe)) {
                    continue;
                }
                // 五行数值
                $wxNumList = BaziExt::getWuxingNum($tmpJiNian);
                arsort($wxNumList);
                $wxLiu = key($wxNumList);
                $maxWxNum = current($wxNumList);
                // 喜神、用神、仇神、忌神、闲神
                $type = '';
                if (in_array($wxLiu, [$xiYong['wx'][0], $xiYong['wx'][1]])) {
                    $type = 'da';
                } elseif ($wxLiu == $xiYong['wx'][4]) {
                    $type = 'xiao';
                }
                if (empty($type)) {
                    continue;
                }
                if ($maxWxNum <= $tmpWxNum) {
                    continue;
                }
                $tmpWxNum = $maxWxNum;

                $tmpRes = [
                    'type' => $type,
                    'date' => $timeStr,
                    'nongli' => $base1['nongli'],
                    'h' => $v1['h'][1],
                    'jinian' => $tmpJiNian,
                    'week' => $week,
                    'des' => $listDes[$type][$chenJuWanDe] ?? $listDes[$type]['三会'],
                    // 'time' => $tmpResTimeList[$timeKey],
                    'sx_chong' => $this->getDzChongSx($tmpJiNian['d'][1]),
                    'pos' => $this->getDongGongPos($tmpJiNian),
                    // 煞向
                    'sha' => $this->getShaXian($tmpJiNian['d'][1]),
                ];
            }
            if (empty($tmpRes)) {
                continue;
            }
            $resType = $tmpRes['type'];
            unset($tmpRes['type']);
            $result[$resType][] = $tmpRes;
        }

        return $result;
    }

    /**
     * 求四离 四绝 公历
     * 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
     * @param $year
     * @return array
     */
    protected function getJieQiDay($year): array
    {
        $list = [
            '春分', '夏至', '秋分', '冬至', '立春', '立夏', '立秋', '立冬',
        ];
        $jieqi = SolarTerm::getAllJieQi($year);
        $day = [];
        foreach ($jieqi as $k => $v) {
            if (in_array($k, $list)) {
                $day[] = date('Y-m-d', (strtotime($v) - 86400));
            }
        }
        return $day;
    }

    /**
     * 日干不旺靠时禄补
     * @param string $str 日干+时干支
     * @return bool
     */
    protected function getLuGui(string $str): bool
    {
        $list = [
            '甲丙寅', '乙己卯', '丙癸巳', '丁丙午', '戊丁巳', '己庚午', '庚甲申', '辛丁酉', '壬辛亥', '癸壬子',
        ];
        return in_array($str, $list);
    }

    /**
     * 获得地支相冲生肖
     * @param $dz
     * @return string
     */
    protected function getDzChongSx($dz): string
    {
        $list = [
            '子' => '马', '丑' => '羊', '寅' => '猴', '卯' => '鸡', '辰' => '狗', '巳' => '猪',
            '午' => '鼠', '未' => '牛', '申' => '虎', '酉' => '兔', '戌' => '龙', '亥' => '蛇',
        ];
        return $list[$dz];
    }

    /**
     * 获得煞向
     * @param string $dz 日地支
     * @return string
     */
    protected function getShaXian($dz): string
    {
        $list = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        return $list[$dz] ?? '';
    }
}
