<?php
// +----------------------------------------------------------------------
// | 真命天子
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: zhaofeng <<EMAIL>> 2017/3/23 11:03
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\ziwei\ZiweiData;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\facade\Request;

class Zhenming
{
    /**
     * 你和他的距离
     * @var array
     */
    protected array $distance = [
        0 => [
            'age' => [
                'eq' => ['稍大', '你的真命天女比你的年龄稍大，你们的岁数相近，志趣相投，凡事都能互相沟通、交流想法，是一对亲朋密友般的好情侣。'],
                'gt' => [
                    ['稍大', '你的真命天女比你的年龄稍大，你们的岁数相近，志趣相投，凡事都能互相沟通、交流想法，是一对亲朋密友般的好情侣。'],
                    ['比较大', '你的真命天女的岁数会比你大一点，在彼此的相处上，她会对你迁就包容比较多，经常给予你关心和呵护，能够给你一份稳定，贴心的感情。'],
                    ['大很多', '你与真命天女的岁数差得会有点多，她比你年长许多，你对她有着崇拜和依赖，凡事都习惯听取她的想法，而她对你也是无限的宠溺和爱怜，你们彼此都很享受这种感情模式。'],
                ],
                'lt' => [
                    ['小', '你的真命天女比你的年龄稍小，你们的岁数相近，彼此能够理解对方的想法，三观也基本接近，感情和谐融洽，彼此既是情侣，更是挚友。'],
                    ['比较小', '你的真命天女的岁数会比你小一点，在彼此的相处上，虽然她岁数比你小，但是思想却比你成熟，她可以给你完全的安全感和依靠，让你感觉很是窝心。'],
                    ['小得多', '你与真命天女的岁数差得会有点多，她比你年轻许多，因此总是能带给你更多的青春活力，让你逆生长，进入她的世界。在日常相处中，她也很享受你的温柔照顾。'],
                ],
            ],
            'parent' => [
                'day' => '你的真命天女可能与你距离较近的同事，同学或者是同乡，建议不要错过同学、同乡聚会，或是同事下班放松的社交平台，也许真爱就在你眼前哦。',
                'month' => '你的真命天女与你在同一城市或同一地域，你们能够通过朋友介绍或者工作上的接触而逐渐建立起情谊，平日相处中容易慢慢看对眼。',
                'hour' => '你的真命天女与你在同一城市或同一地域，你们能够通过朋友介绍或者工作上的接触而逐渐建立起情谊，平日相处中容易慢慢看对眼。',
                'year' => '你的真命天女与你的距离有一些远，若想遇到她你要注意你的外派或者出差、游学的场合，在异地外市，也许你们能来一个浪漫的邂逅，并从此定情。',
                'null' => '你的真命天女跨省界，更甚者异国他乡，也许你会觉得与她的距离太过遥远，但所谓千里姻缘一线牵，真爱可以跨越任何距离，在留学、出国公干甚至是旅行散心的时候，你们都可能会相遇。',
            ],
        ],
        1 => [
            'age' => [
                'eq' => ['稍大', '你的真命天子比你的年龄稍大，你们的岁数相近，志趣相投，凡事都能互相沟通、交流想法，是一对亲朋密友般的好情侣。'],
                'gt' => [
                    ['稍大', '你的真命天子比你的年龄稍大，你们的岁数相近，志趣相投，凡事都能互相沟通、交流想法，是一对亲朋密友般的好情侣。'],
                    ['比较大', '你的真命天子的岁数会比你大一点，在彼此的相处上，会对你迁就包容比较多，经常给予你关心和呵护，能够给你一份稳定，贴心的感情。'],
                    ['大很多', '你与真命天子的岁数差得会有点多，他比你年长许多，你对他有着崇拜和依赖，凡事都习惯听取他的想法，而他对你也是无限的宠溺和爱怜，你们彼此都很享受这种感情模式。'],
                ],
                'lt' => [
                    ['小', '你的真命天子比你的年龄稍小，你们的岁数相近，彼此能够理解对方的想法，三观也基本接近，感情和谐融洽，彼此既是情侣，更是挚友。'],
                    ['比较小', '你的真命天子的岁数会比你小一点，在彼此的相处上，虽然他岁数比你小，但是思想却比你成熟，他可以给你完全的安全感和依靠，让你感觉很是窝心。'],
                    ['小得多', '你与真命天子的岁数差得会有点多，他比你年少许多，因此总是能带给你更多的青春活力，让你逆生长，进入他的世界。在日常相处中，他也很享受你的温柔照顾。'],
                ],
            ],
            'parent' => [
                'day' => '你的真命天子可能与你距离较近的同事，同学或者是同乡，建议不要错过同学、同乡聚会，或是同事下班放松的社交平台，也许真爱就在你眼前哦。',
                'month' => '你的真命天子与你在同一城市或同一地域，你们能够通过朋友介绍或者工作上的接触而逐渐建立起情谊，平日相处中容易慢慢看对眼。',
                'hour' => '你的真命天子与你在同一城市或同一地域，你们能够通过朋友介绍或者工作上的接触而逐渐建立起情谊，平日相处中容易慢慢看对眼。',
                'year' => '你的真命天子与你的距离有一些远，若想遇到他你要注意你的外派或者出差、游学的场合，在异地外市，也许你们能来一个浪漫的邂逅，并从此定情。',
                'null' => '你的真命天子跨省，或异国他乡，也许你会觉得与他的距离太过遥远，但所谓千里姻缘一线牵，真爱可以跨越任何距离，在留学、出国公干甚至是旅行散心的时候，你们都可能会相遇。',
            ],
        ],
    ];

    /**
     * 能力运势如何
     * @var array
     */
    protected array $yunshi = [
        'face' => [
            '子' => [
                '将来的另一半个性会是幽默风趣、反应很快的，但是身材会微胖。此外，也要小心另一半将来的桃花可能会比你更旺喔！',
                '你的真命天子（女）个性风趣幽默，反应敏捷，思维活跃，虽然身形有些微胖，第一眼可能不够亮眼，但TA的智慧和口才却让TA很受身边的异性欢迎，和TA在一起能让你感受到很多乐趣。',
            ],
            '丑' => [
                '你的另一半个性是比较闷的，也比较容易钻牛角尖，身材则是属於结实、健美型的。		',
                '你的真命天子（女）有些沉默寡言，脾气耿直又易钻牛角尖，有时候会让你好气又好笑。在外形上，TA是名副其实的穿衣显瘦，脱衣有肉，身材可是很健美哦。',
            ],
            '寅' => [
                '代表你的另一半是一个正直、有责任感，而且很上进的人，身材上属於高挑轻瘦型的。',
                '你的真命天子（女）个性正直勇敢，待人热诚，做事责任心强，且积极要求上进，跟TA在一起你会感觉很热血，很有干劲。在外形上，TA身材高挑，纤瘦。',
            ],
            '卯' => [
                '代表另一半是忠实诚信，有爱心的，身材背挺、高挑。',
                '你的真命天子（女）内心十分温柔善良，爱心满满，同时为人忠厚老实，诚信可靠，身形也高大挺拔，可以给你十足的安全感。',
            ],
            '辰' => [
                '代表另一半自我意识很浓厚，喜欢自由，所以绝不能对TA碎碎念，身材属於适中偏壮。',
                '你的真命天子（女）自我意识比较强，凡事比较主观，也喜好自由洒脱，所以千万不要试图去束缚TA，TA最不喜欢就是无止境的唠叨和管束。在外形上属于中等偏状型。',
            ],
            '巳' => [
                '代表另一半个性较极端，防卫心较重，外表属高大、强健型，很少会生病或感冒。',
                '你的真命天子（女）内心缺乏安全感，防卫心思较重，有时想法会比较极端，建议多给TA一点心灵抚慰。在外形上，TA高大强壮，身体素质也很好。',
            ],
            '午' => [
                '那么，你就要特别小心了，因为你的另一半桃花重，异性缘很好，个性上也就很乐观开朗，身材属於高挑健美型的。',
                '你的真命天子（女）个性活泼开朗，乐观风趣，异性缘很好，桃花运颇佳，TA时常受到异性的青睐可能会让你深感烦恼。在身材上，TA也是高挑挺拔，风姿健美。',
            ],
            '未' => [
                '代表你的另一半在行事上是比较谨慎小心的，防卫心重、城府深，但亲和力却很好，外表肌肉强壮，个子不算高。',
                '你的真命天子（女）其实内心城府很深，防卫心重，行事十分谨慎小心，但外在表现却很有亲和力，与人友善。在体型上，TA虽然个子不算高，但肌肉却较为发达。',
            ],
            '申' => [
                '代表另一半是聪颖、机智，爱表现的，身材属於轻瘦骨感、皮肤白皙，很斯文的。',
                '你的真命天子（女）聪慧灵敏，表现欲强，很受大家的喜爱和欢迎。在身型上，TA清瘦骨感，皮肤白皙，外表斯文清秀，见者都会觉得很清新舒爽。',
            ],
            '酉' => [
                '要特别小心另一半，因为TA会是个超级大桃花，比较容易红杏出墙或出轨，TA的个性热情、急躁，且自尊心强，你若用话语刺激TA，TA可能就会去跟别人在一起；也因为热情，你也必须常常在性方面多下苦工；身材属消瘦，皮肤是比较白皙的。',
                '你的真命天子（女）个性急躁，自尊心强，不喜欢被言语挑战。在情感上TA热情似火，激情十足，对性方面的需求较大。但是有时也显得不够忠诚，会容易红杏出墙和出轨。TA的外表清瘦，皮肤白皙，也比较讨异性喜欢。',
            ],
            '戌' => [
                '代表另一半是非常善良、温和朴实的，生活规律，但生活上也就没有什么情调或新鲜感，所以相处久了，你就会觉得你跟TA个性好像不合的错觉。身材中等，但比较健壮。',
                '你的真命天子（女）个性温柔敦厚，善良朴实，质朴可靠，但欠缺一点情趣。与TA相处日久之后会被平淡的生活消磨掉了彼此的激情，感情会逐渐变浅，产生彼此性格不合的错觉。在身型上，TA身材中等，但还算健壮。',
            ],
            '亥' => [
                '另一半是温和善良，比较顾家的；身材丰腴，属於矮胖型。',
                '你的真命天子（女）个性温和，脾气良好，心地善良。对于感情忠诚专一，比较顾家，能给你安定的生活。在身型上，TA个子不高，体态丰腴，给人敦厚踏实之感。',
            ],
        ],
        'nengli' => [
            0 => [
                '正官' => [
                    '正官星是贵气之星，也是女命的夫星，喜欢生旺，更喜欢一位进入夫妻宫，则夫星得位，最为吉利，如果年月时地支没有出现冲刑害来影响，则多能嫁到有能力有地位的男人，自然不用担心钱财。如果遇到相冲相刑相害，则吉性减半，甚至呈凶。',
                    '你的真命天女财雄势大，多为能力强且地位高之辈，因此作为她的另一半你能够得到她的多方辅助，从不会受到生计和钱财的困扰，社会地位和名誉也都比较好，生活安逸，物质丰满，能获得贵气之星的加持。',
                ],
                '正财' => [
                    '正财星有辛勤耐劳和积蓄成富的特点，钱财来得稳来得久，不会出现三更富贵四更穷的现象，正财星又会生助夫星正官，为夫星的源头，当一位落入夫妻宫时，年月时柱没有出现比肩劫财星来冲击，则容易嫁到有钱且顾家的老公。',
                    '你的真命天女未必大富大贵，但足够给你温馨平稳的日常生活。她有能力也有学识，事业有成，经济来源稳定，不会让你有过多的开支或不满，能够给你的生活带来足够的富足感。同时，她还十分顾家体贴，婚后生活十分舒心美满。',
                ],
                '正印' => [
                    '正印星是护身之物，是贵人，也是名誉地位的代表，当八字的食神伤官星或正官七杀星较多时，势必不利于自身生存和发展，这种情况如果有正印星落入夫妻宫，则一印护身，平安顺利，不仅可以化解挫折，而且所嫁之人有荣誉有学识，易因名得利。',
                    '你的真命天女可能未必一开始就拥有较多钱财，但她是一只十足的潜力股，学识很高，在她找到自己所属的领域后，将自己的知识和能力转化为物质和金钱，给家庭良好的生活更添一份富足，尤其她除了钱财，社会名誉地位也很高，这一点更是值得加分。',
                ],
                '比肩' => [
                    '比肩、劫财星是克财之物，一般不以吉论，但当八字的财星太多时，则会出现财旺身弱的情况，即自身薄弱挑不起旺财，这种情况就需要象征朋友兄弟的比肩劫财星来帮忙分担，那么，比肩劫财星反成为一种助力，当落入夫妻宫时，主配偶善于赚钱。',
                    '你的真命天女个性稳重踏实，虽然赚钱能力不强，但是是十足的居家好贤妻，同时她的旺夫运也很好，能够对你的事业运势和财运提供较大助益，也许在家庭中你的经济地位能比他高，但是你的成绩其实离不开她在背后的默默支持。',
                ],
                '劫财' => [
                    '比肩、劫财星是克财之物，一般不以吉论，但当八字的财星太多时，则会出现财旺身弱的情况，即自身薄弱挑不起旺财，这种情况就需要象征朋友兄弟的比肩劫财星来帮忙分担，那么，比肩劫财星反成为一种助力，当落入夫妻宫时，主配偶善于赚钱。',
                    '你的真命天女个性稳重踏实，虽然赚钱能力不强，但是是十足的居家好贤妻，同时她的旺夫运也很好，能够对你的事业运势和财运提供较大助益，也许在家庭中你的经济地位能比他高，但是你的成绩其实离不开她在背后的默默支持。',
                ],
                '伤官' => [
                    '能力不太看好，写委婉点	',
                    '你的真命天女才能和学识较为普通，并没有大富大贵的潜力，但是为人正直可靠，能给你踏实稳定的感情，虽然你们的生活物资条件一般，但是彼此十分真情实意，甘之如饴，随遇而安的生活也十分美满。',
                ],
                '食神' => [
                    '能力不太看好，写委婉点	',
                    '你的真命天女才能和学识较为普通，并没有大富大贵的潜力，但是为人正直可靠，能给你踏实稳定的感情，虽然你们的生活物资条件一般，但是彼此十分真情实意，甘之如饴，随遇而安的生活也十分美满。',
                ],
                'null' => [
                    '平淡无奇，加一些相关的感情描述',
                    '你的真命天女资质和能力都较为普通，收入稳定，但并没有更大的潜力和更好的发展空间。你们的生活安定和谐，彼此对于物资都没有更多的欲望，目标一致，三观契合，在感情方面还是比较和谐融洽的。',
                ],
            ],
            1 => [
                '正官' => [
                    '正官星是贵气之星，也是女命的夫星，喜欢生旺，更喜欢一位进入夫妻宫，则夫星得位，最为吉利，如果年月时地支没有出现冲刑害来影响，则多能嫁到有能力有地位的男人，自然不用担心钱财。如果遇到相冲相刑相害，则吉性减半，甚至呈凶。',
                    '你的真命天子财雄势大，多为能力强且地位高之辈，因此作为他的另一半你能够享受富足安稳的生活，从不会受到生计和钱财的困扰，社会地位和名誉也都比较好，生活安逸，物质丰满，能获得贵气之星的加持。',
                ],
                '正财' => [
                    '正财星有辛勤耐劳和积蓄成富的特点，钱财来得稳来得久，不会出现三更富贵四更穷的现象，正财星又会生助夫星正官，为夫星的源头，当一位落入夫妻宫时，年月时柱没有出现比肩劫财星来冲击，则容易嫁到有钱且顾家的老公。',
                    '你的真命天子未必大富大贵，但足够给你安稳富足的日常生活。你的丈夫有能力也有学识，事业有成，经济来源稳定，不会让你过上时而富贵时而贫穷的颠簸生活，能够给你的生活带来足够的安全感。同时，他还十分顾家体贴，婚后生活十分舒心美满。',
                ],
                '正印' => [
                    '正印星是护身之物，是贵人，也是名誉地位的代表，当八字的食神伤官星或正官七杀星较多时，势必不利于自身生存和发展，这种情况如果有正印星落入夫妻宫，则一印护身，平安顺利，不仅可以化解挫折，而且所嫁之人有荣誉有学识，易因名得利。',
                    '你的真命天子可能未必一开始就拥有较多钱财，但他是一只十足的潜力股，学识很高，在他的专业领域十分有名望和声誉，因此只要把握机会，就能够因名而得利，将自己的知识和能力转化为物质和金钱，让你过上良好的生活，尤其他出了钱财，社会名誉地位也很高，这一点更是值得加分。',
                ],
                '比肩' => [
                    '比肩、劫财星是克财之物，一般不以吉论，但当八字的财星太多时，则会出现财旺身弱的情况，即自身薄弱挑不起旺财，这种情况就需要象征朋友兄弟的比肩劫财星来帮忙分担，那么，比肩劫财星反成为一种助力，当落入夫妻宫时，主配偶善于赚钱。',
                    '你的真命天子个性稳重踏实，虽然赚钱能力不强，但是是十足的居家好男人，同时他的旺妻运也很好，能够对你的事业运势和财运提供较大助益，也许在家庭中你的经济地位能比他高，但是你的成绩其实离不开他在背后的默默支持。',
                ],
                '劫财' => [
                    '比肩、劫财星是克财之物，一般不以吉论，但当八字的财星太多时，则会出现财旺身弱的情况，即自身薄弱挑不起旺财，这种情况就需要象征朋友兄弟的比肩劫财星来帮忙分担，那么，比肩劫财星反成为一种助力，当落入夫妻宫时，主配偶善于赚钱。',
                    '你的真命天子个性稳重踏实，虽然赚钱能力不强，但是是十足的居家好男人，同时他的旺妻运也很好，能够对你的事业运势和财运提供较大助益，也许在家庭中你的经济地位能比他高，但是你的成绩其实离不开他在背后的默默支持。',
                ],
                '伤官' => [
                    '能力不太看好，写委婉点	',
                    '你的真命天子才能和学识较为普通，并没有大富大贵的潜力，但是为人正直可靠，能给你踏实稳定的感情，虽然你们的生活物资条件一般，但是彼此十分真情实意，甘之如饴，随遇而安的生活也十分美满。',
                ],
                '食神' => [
                    '能力不太看好，写委婉点	',
                    '你的真命天子才能和学识较为普通，并没有大富大贵的潜力，但是为人正直可靠，能给你踏实稳定的感情，虽然你们的生活物资条件一般，但是彼此十分真情实意，甘之如饴，随遇而安的生活也十分美满。',
                ],
                'null' => [
                    '平淡无奇，加一些相关的感情描述',
                    '你的真命天子资质和能力都较为普通，收入稳定，但并没有更大的潜力和更好的发展空间。你们的生活安定和谐，彼此对于物资都没有更多的欲望，目标一致，三观契合，在感情方面还是比较和谐融洽的。',
                ],
            ],
        ],
    ];

    /**
     * 夫妻宫主星
     * @var array
     */
    protected array $xing = [
        '七杀星' => [
            '你既主观又冲动，一看对眼，哪怕只是惊鸿一瞥，一样会马上认定：这人就是我的真命天子!可是，爱情是两个人的事，在你主动出击之时，要先了解对方是否也看上了你。你若能耐心等候，多去了解对方，不急于进攻，他(她)才会浮出水面喔!',
            '你对待感情冲劲十足，有时显得勇猛有余，而智慧不足。一旦遇到自己心仪的人，哪怕只是见上一面，你就会认定对方，感到自己的另一半非对方莫属，随即展开强烈的攻势，进行主动出击。但是这样的方法未免显得过于主观，你并不了解对方对你的想法，也没有真正的了解对方，有时候让彼此耐心的多做了解和尝试，让两颗心渐渐靠近，也许更容易遇到对的人哦。',
        ],
        '破军星' => [
            '你的热情自信加上十足的行动力和丰富的创意，是你寻找真命天子的最大优势。但是，你喜新厌旧的功夫也属一流，似乎总在变换恋爱对象，让人望而却步。如果能抱着“大范围撒网，认准后深入交往”的原则，你的他(她)才会悄悄靠近唷!',
            '你在感情上显得自信乐观，对待心仪的恋人能够通过自己的热情攻势和丰富创意来打动对方的心，然而你的激情总是来得快去的更快，你的喜新厌旧和朝三暮四常常让对方觉得无所适从，因此你的恋情维持时间总是很短。虽然，你善于运用的方法是普遍撒网，但是请别忘记，重点培养才能让真正收获稳定美好的恋情。',
        ],
        '廉贞星' => [
            '你看似内敛，实则强悍。外表看起来柔和，而且主动积极，是你吸引真命天子出现的优势。但是，要真正找到他(她)，还需要做到表里一致，内心与外表一样柔和。当对方靠近时，别表现出太强的占有欲，同时，让对方充分了解你，如此才是上策喔!',
            '你的外表看上去温柔乖巧，个性内敛低调，其实骨子是一个强悍刚硬的人。你对待爱情主动积极，占有欲强，虽然异性容易被你的外表所吸引，但是长久的相处中却会因为你的表里不一，或者控制欲太强而望而却步。建议你在感情中要向另一半展示自己柔软的内心，彼此充分的沟通和了解，而不要用强硬的脾气把真爱吓跑哦。',
        ],
        '贪狼星' => [
            '你亲切随和而且豪气大胆，很有桃花缘。可是，百花丛中却难辨真命天子(真命天女)。对待感情可别太随意，一副漫不经心的样子只会吓跑有缘人。别去招惹烂桃花，与异性 交往要讲究技巧，既不疏远也别太亲密，那朵属于你的桃花自然会翩然而至唷!',
            '你的感情观显得有些自然随性，对待异性随和大方，豪气干云，桃花运不错。虽然身边不缺异性伴侣，但是要说到真正的有缘人却又有些犯难。你的自然随意，漫不经心很容易吓跑对你真正有意的异性。有时候，烂桃花不如没桃花，对待异性，要明确自己的态度，不要模棱两可，只有你明确了自己到底想要什么，真爱才会早日降临。',
        ],
        '紫微星' => [
            '你气度恢宏，对权力的欲望最为强烈。既要爱情，也要面包。显得较为现实，重视物质，要求高而多，让人对你有不重视爱情的错觉。学习稍稍放下掌控一切的架式，多从对方的角度思考，将心比心，他(她)才会慢慢向你露出笑脸喔!',
            '你的外表气度不凡，一表人才，除了爱情，你对事业的追求和权力的渴望同样有着强烈的欲求。这样的你会让另一半觉得你总是以自我为中心，又偏重物质，太过现实，可能给不了他想要的爱情。在感情关系中，建议你的掌控欲不要太强，尝试多与对方交流，从他的角度思考和处理问题，也许你们的感情会慢慢回温的。',
        ],
        '天府星' => [
            '你外表粗枝大叶，实则心细如麻。你惬意潇洒、好享受，对爱情的要求也就很高，让人看不透，有时亲切随缘，过一会儿却又吹毛求疵。好好表现你的诚意与真心吧，无需东看西瞅，你真实的一面在不经意之中流露时，他(她)就会向你敞开胸怀喔!',
            '你虽然外表看起来有些粗枝大叶，肆意妄为，其实内心十分细腻敏感，心细如发。你其实缺乏安全感，因此总是用随性潇洒的态度来伪装自己，但有时却又显得有些谨慎小心，吹毛求疵，让人难以捉摸。在爱情面前，建议你能够勇敢表达自己的真心和诚意，不要再假意地装作无所谓，勇敢的向对方展现真实的自己，也许也会获得对方坦诚的回应哦。',
        ],
        '武曲星' => [
            '你严肃刚直，主观意识强烈，让人感觉是个不爱美人(英雄)爱江山的人，若要自由选择恋爱对象，会是个不小的考验唷。提高亲和力，多为他人着想，努力改变自我，当你觉得自己随和可亲的时候，就不难获得善缘。安排相亲或是亲友介绍较容易遇到真命天子(真命天女)。',
            '你为人主观意识较强，个性严肃耿直，刚正不阿，容易给人严谨冷酷，难以靠近的感觉，旁人会误解你是一个只顾拼事业而忽略感情的工作狂，尝试着多表现自己的亲和力和内心柔软的部分，多多去理解对方，关怀对方，也许缘分才会慢慢靠近你。如果感觉自己的圈子过于狭隘，自由选择的余地不大的话，不妨考虑尝试相亲或是亲友介绍，会比较容易遇到真爱。',
        ],
        '天相星' => [
            '你对待感情既谨慎又冷静，挑对象时非常小心。不用抱怨上天不公平，让你难以找到心中的好对象。你的真命天子(真命天女)正在观望着你的一举一动，等着你来爱呢。眼光别太高，过分挑剔恐怕会错过意中人唷。来一点感性与浪漫，他(她)会更快出现喔!',
            '你的感情观过于认真和严苛，用谨慎小心的态度和谨小慎微的行为来和另一半相处，你们彼此都会感觉心累。要求的太完美，太过吹毛求疵会让你在不知不觉间错过心仪的人。建议放松心态，多用直觉和真情实感来经营感情，也许那个对的人正在默默的观察着你的言行举止，期待着你们美丽的相遇呢。',
        ],
        '太阳星' => [
            '你人缘好，有博爱心，喜欢照顾人，常出现多角感情。又因怕伤人心，不知如何脱身，桃花越多苦恼越多。想找到真命天子(真命天女)，可别见一个爱一个，或是太容易投入一段感情，更不要把友情当爱情，稍微掩饰内心的想法，更能看清谁才是你的Mr. right(Ms. Right)喔!',
            '你对待周遭的异性朋友都十分的温柔体贴，显得有些博爱，你热衷于照顾人，又总是不懂得拒绝和伤他人心，因此往往不自觉就陷入多角恋情，桃花虽然多却都不够真爱。建议今后再感情关系中，不要轻易投入任何一段爱情，认真分清爱情和友情的界限，不要对待所有异性一视同仁，认真倾听自己的内心，才能真正找到对的那个人。',
        ],
        '巨门星' => [
            '你可以说是个“爱情专家”，但是自己的爱情实战分数却不及格。想找到真命天子(真命天女)，可要让嘴巴歇一歇，改改直言不讳的习惯。少一些疑神疑鬼，多一份自信与信任。别只口头上说说，拿出行动来大大方方地与异性 交往，真命天子才会出现喔!',
            '你在爱情上面临的问题是在言语上是一个巨人，但是在行动中却是个矮子。你没有多少实战经验，空有爱情的理论，建议遇见自己心仪的人，能够切实拿出行动来与对方大大方方的交往，不要只是光说不练。在正式的相处交往中，要给予对方多一些信任，自身也要多建立一些自信，无谓的猜忌和怀疑只会磨损彼此的感情。',
        ],
        '天机星' => [
            '你的在感情方面，很随缘被动，相信缘分天注定，不强求。因为思多行少，即使遇到心仪的对象，也容易出现“爱在心头，口难开”的状况。有时候缘分是可以争取的，别太多顾忌可让你更快找到真命天子(真命天女)。自我设限反而容易错过属于你的美好姻缘喔!',
            '你在感情上过于相信天意和缘分，总是相信缘分天注定，不愿努力争取和过分强求，有时显得有些被动，也欠缺把心意付诸行动的能力。你要懂得，在感情的问题上，不能思虑过多，也不能给自己自我设限过多，太多顾虑，迟迟不敢付诸实际很容易错失真爱，有时候，缘分也要靠自己的努力才能得到。',
        ],
        '太阴星' => [
            '你非常感性内敛，对待感情很被动。想找到真命爱人，就得敢于主动追求。遇到喜欢的他(她)，别太腼腆、害羞。“得之我幸，不得我命”这样不争的态度要改改，勇于靠近对方才能拉近彼此的距离，把握住属于你的Mr. Right(Ms. Right)!',
            '你对待感情的态度过于的被动和内敛，尽管内心感情丰富，但是外在表现总是过于羞涩、腼腆，不善于表达自己的感情，遇到喜欢的人也不会主动追求。有时候，在感情中不争不抢不见得是一件好事，你想要把握住自己的真爱，让双方的心逐渐的靠近，就要适时的主动表达，让对方了解你的真实心意。',
        ],
        '天梁星' => [
            '你乐善好施，很有博爱之心。但对自己的个人感情却有逃避的倾向，害怕受到伤害，小心翼翼。你的人缘很好，真命天子(真命天女)就在好朋友之中，你越是小心谨慎，就越离他(她)远去。别把爱情想得太完美，在练习中积累爱情学分，才能更好地寻找你心目中的王子(公主)。',
            '你的心地善良，乐善好施，人缘不错，也很有博爱之心。只是在爱情中，你却有些谨小慎微，因为害怕受到伤害而逃避感情，不敢勇敢追求真爱。其实，感情不是靠想象的，只有自己切实的经历过才能在爱里更好的成长，明白爱情的真实模样。你的真爱很可能就隐藏在身边的好朋友之中，建议用真心去感受对方对你的心意，彼此认真的付出。',
        ],
        '天同星' => [
            '你宽容平和，人缘好，桃花强，很容易被动地陷入多角关系中，在感情的世界里纠缠不清。要想找到心中的王子(公主)，稍稍收起你的感性吧，别把同情当爱情，委屈自己。应懂得说“不”，以“宁缺勿滥”的原则对待感情，才能把握属于你的他(她)喔!',
            '你的感情细腻丰富，与人为善，包容平和的好心态让你获得了不错的人缘和旺盛的桃花，只是有些感情并不是你心之所往，而是无意识地就陷入了多角关系中。建议今后在与异性相处中要把握好分寸，学会拒绝，分清同情、友情和爱情的明确界限。用宁缺毋滥的正确态度去等待和把握你的真爱。',
        ],
        '无主星' => [
            '顺其自然',
            '对待感情，你抱着顺其自然，随性而为的真实态度，不会刻意去强调自己的感情状态，对待异性的示好和主动总是淡淡的，也很少主动去追求异性。对你而言，感情并不是必需品，你相信真爱会在某一天来临，做真实的自己，过率性的日子就好，冥冥之中自有天注定，顺势而为的感情才是你所向往的。',
        ],
    ];

    /**
     * 真命天子
     * @return array
     * @throws \DateMalformedStringException
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 姓名
            'username' => Request::param('username', '', 'trim'),
            // 出生时间
            'dateTime' => Request::param('time', '', 'trim'),
            // 性别
            'sex' => Request::param('sex', 0, 'intval'),
            // 订单时间
            'otime' => Request::param('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'username|姓名' => ['require', 'chs', 'min:2', 'max:5'],
                'dateTime|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $otimeint = strtotime($data['otime']);
        $userBases = Ex::date($data['dateTime'])->sex($data['sex']);
        $result = [];
        // 基础数据
        $result['bases'] = $userBases->getLunarByBetween();
        $result['bases']['god'] = $userBases->getGod();
        $result['bases']['_god'] = $userBases->_getGod();
        $result['bases']['fate'] = $userBases->getFate();
        // 获取相遇时间
        $meet = function ($tiangan, $year, $sex = 0) use ($otimeint) {
            $tian = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
            $di = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
            $dayGan = [
                0 => ['己', '戊', '辛', '庚', '癸', '壬', '乙', '甲', '丁', '丙'],
                1 => ['辛', '庚', '癸', '壬', '乙', '甲', '丁', '丙', '己', '戊'],
            ];
            // 当前对应的流年天干
            $liuGan = $dayGan[$sex][array_search($tiangan, $tian)];
            // 当前时间   ---  距离$liuGan  最近的      3   0
            $meetYear = (date('Y', $otimeint) - 4) % 10;
            for ($i = 0; $i < 10; $i++) {
                if ($i <= 5) {
                    $gtTiangan[] = $tian[($meetYear + $i) % 10];
                } else {
                    $etTiangan[] = $tian[($meetYear + $i) % 10];
                }
            }
            if (in_array($liuGan, $gtTiangan)) {
                $offset = array_search($liuGan, $gtTiangan);
                $oldYear = (int)(date('Y', $otimeint)) + $offset;
            } else {
                $offset = array_search($liuGan, $etTiangan);
                $oldYear = (int)(date('Y', $otimeint)) + $offset - 4;
            }
            return [
                'year' => $oldYear,
                'age' => $oldYear - $year + 1,
                'nongli' => $tian[($oldYear - 4) % 10] . $di[($oldYear - 4) % 12],
            ];
        };
        // 何时遇见你的真命天人
        $result['meet'][$result['bases']['jinian']['d'][0]] = $meet($result['bases']['jinian']['d'][0], $userBases->dateTime->format('Y'), $data['sex']);
        foreach ($result['bases']['_god']['day']['hide'] as $val) {
            $result['meet'][$val] = $meet($val, $userBases->dateTime->format('Y'), $data['sex']);
        }
        // 排序
        foreach ($result['meet'] as $val) {
            $meetTemp[$val['year']] = $val;
        }
        ksort($meetTemp);
        $result['meet'] = $meetTemp;
        // 你和他的距离
        $result['distance'] = $this->distance($result['bases'], $data['sex']);
        // 他的能力运势如何
        $yunshiNengli = '';
        foreach ($result['bases']['_god']['day']['god'] as $val) {
            $yunshiNengli = $this->yunshi['nengli'][$data['sex']][$val] ?? '';
        }
        $result['yunshi'] = [
            'face' => $this->yunshi['face'][$result['bases']['jinian']['d'][1]],
            'nengli' => empty($yunshiNengli) ? $this->yunshi['nengli'][$data['sex']]['null'] : $yunshiNengli,
        ];
        // 他到来时我该怎么做
        $ziwei = new ZiweiData($data['username'], $data['dateTime'], $data['sex'], $data['otime']);
        // 获取夫妻宫命宫
        $fuqi = array_search('夫妻', $ziwei->getMingGong());
        // 紫微星的位置
        $ziweixing = $ziwei->getZhiWeiXing();
        $xing = [];
        if ($ziweixing == $fuqi) {
            $xing['紫微星'] = $this->xing['紫微星'];
        }
        foreach ($ziwei->getFourteenZhengXing() as $key => $val) {
            if ($val == $fuqi) {
                $xing[$key . '星'] = $this->xing[$key . '星'];
            }
        }
        $result['xing'] = empty($xing) ? ['无主星' => $this->xing['无主星']] : $xing;
        return $result;
    }

    /**
     * 男女距离
     * @param array $bases 八字基础
     * @param int $sex 性别
     * @return array
     */
    protected function distance(array $bases, int $sex = 0): array
    {
        $parentSearch = $sex == 0 ? ['正财', '偏财'] : ['正官', '七杀'];
        $ageAttr = $sex == 0 ? '正财' : '正官';
        $distanceParent = [];
        $arrt = [
            'year' => $bases['jinian']['y'][1],
            'month' => $bases['jinian']['m'][1],
            'day' => $bases['jinian']['d'][1],
            'hour' => $bases['jinian']['h'][1],
        ];
        // 是否相生
        $vaildateWuxing = function ($att1, $att2) {
            $temp = [
                '金' => ['土', '水'],
                '木' => ['水', '火'],
                '水' => ['金', '木'],
                '火' => ['土', '木'],
                '土' => ['金', '火'],
            ];
            if (false === array_search($att2, $temp[$att1])) {
            }
            return array_search($att2, $temp[$att1]);
        };
        $wuxingAttr = [
            '甲' => '木', '乙' => '木', '丙' => '火', '丁' => '火', '戊' => '土', '己' => '土', '庚' => '金', '辛' => '金',
            '壬' => '水', '癸' => '水', '子' => '水', '丑' => '土', '寅' => '木', '卯' => '木', '辰' => '土', '巳' => '火',
            '午' => '火', '未' => '土', '申' => '金', '酉' => '金', '戌' => '土', '亥' => '水',
        ];
        foreach ($bases['god'] as $key => $val) {
            // 祖籍的距离
            $temp = '';
            foreach ($parentSearch as $searchName) {
                $temp .= in_array($searchName, $bases['_god'][$key]['god']) ? 1 : 0;
                $temp .= $val == $searchName ? 1 : 0;
            }
            $distanceParent[$key] = $temp;
            // 年龄差
            $wuxing[$val] = $wuxingAttr[$arrt[$key]];
            if (!isset($distanceAge['t'][$val])) {
                $distanceAge['t'][$val] = 0;
            }
            foreach ($bases['_god'][$key]['god'] as $k => $v) {
                $wuxing[$v] = $wuxingAttr[$bases['_god'][$key]['hide'][$k]];
                if (!isset($distanceAge['d'][$v])) {
                    $distanceAge['d'][$v] = 0;
                }
                $distanceAge['d'][$v]++;
            }
            $distanceAge['t'][$val]++;
        }
        $parentKey = (int)max($distanceParent) == 0 ? 'null' : array_search(max($distanceParent), $distanceParent);
        // 年龄差
        foreach ([$ageAttr, '正印', '$userBases', '伤官', '食神'] as $val) {
            $distanceAge['t'][$val] ??= 0;
            $distanceAge['d'][$val] ??= 0;
        }
        $num1 = $distanceAge['t'][$ageAttr] + $distanceAge['d'][$ageAttr] + $distanceAge['t']['正印'] + $distanceAge['d']['正印'];
        $num2 = $distanceAge['t']['食神'] + $distanceAge['d']['食神'] + $distanceAge['t']['伤官'] + $distanceAge['d']['伤官'];
        if ($num1 == $num2) {
            $ageResult = $this->distance[$sex]['age']['eq'];
        } elseif ($num1 > $num2) {
            // 大于 ====
            $ageResult = $this->distance[$sex]['age']['gt'][0];
            if ((int)$distanceAge['t'][$ageAttr] > 0 && (int)$distanceAge['d'][$ageAttr] > 0) {
                $ageResult = $this->distance[$sex]['age']['gt'][1];
                if (isset($wuxing[$ageAttr]) && isset($wuxing['正印'])) {
                    // 是否相生
                    if ($vaildateWuxing($wuxing[$ageAttr], isset($wuxing['正印']))) {
                        $ageResult = $this->distance[$sex]['age']['gt'][2];
                    }
                }
            } elseif ((int)$distanceAge['t']['正印'] > 0 && (int)$distanceAge['d']['正印'] > 0) {
                $ageResult = $this->distance[$sex]['age']['gt'][1];
                if (isset($wuxing[$ageAttr]) && isset($wuxing['正印'])) {
                    // 是否相生
                    if ($vaildateWuxing($wuxing[$ageAttr], isset($wuxing['正印']))) {
                        $ageResult = $this->distance[$sex]['age']['gt'][2];
                    }
                }
            }
        } else {
            // 小于  ====
            $ageResult = $this->distance[$sex]['age']['lt'][0];
            if ((int)$distanceAge['t']['食神'] > 0 && (int)$distanceAge['d']['食神'] > 0) {
                $ageResult = $this->distance[$sex]['age']['lt'][1];
                if (isset($wuxing['食神']) && isset($wuxing['伤官'])) {
                    // 是否相生
                    if ($vaildateWuxing($wuxing['食神'], isset($wuxing['伤官']))) {
                        $ageResult = $this->distance[$sex]['age']['lt'][2];
                    }
                }
            } elseif ((int)$distanceAge['t']['伤官'] > 0 && (int)$distanceAge['d']['伤官'] > 0) {
                $ageResult = $this->distance[$sex]['age']['lt'][1];
                if (isset($wuxing['食神']) && isset($wuxing['伤官'])) {
                    // 是否相生
                    if ($vaildateWuxing($wuxing['食神'], isset($wuxing['伤官']))) {
                        $ageResult = $this->distance[$sex]['age']['lt'][2];
                    }
                }
            }
        }
        return [
            'age' => $ageResult,
            'parent' => $this->distance[$sex]['parent'][$parentKey],
        ];
    }
}
