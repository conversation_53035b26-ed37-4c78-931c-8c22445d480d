<?php
// +----------------------------------------------------------------------
// | Babynamed.星座360宝宝起名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\model\babynamed\ToolBbqm;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Babynamed
{
    /**
     * 获取名字列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getnamelist(): array
    {
        $data = [
            // 字段sort
            'id' => input("id", 0, 'intval'),
            // 性别 男 0 女 1
            'gender' => input("gender", 0, 'intval'),
        ];
        $validate = Validate::rule(
            [
                'id|字段sort' => ['require', 'number'],
                'gender|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $info = ToolBbqm::getNameList($data['id'], $data['gender']);
        if (!$info) {
            $data = ['status' => 0];
        } else {
            $data = ['data' => $info, 'status' => 1];
        }
        return $data;
    }

    /**
     * 返回姓名ID映射 $family, $gender
     * @return array
     */
    public function ifnameexists(): array
    {
        // 字段sort
        $family = input("family", '', 'trim');
        // 性别 男 0 女 1
        $gender = input("gender", 0, 'intval');
        if (empty($family)) {
            $data = ['status' => 0];
        } else {
            $sort = ToolBbqm::ifNamExists($family, $gender);
            if (!$sort) {
                $data = ['status' => 0];
            } else {
                $data = ['data' => (int)$sort, 'status' => 1];
            }
        }
        return $data;
    }
}
