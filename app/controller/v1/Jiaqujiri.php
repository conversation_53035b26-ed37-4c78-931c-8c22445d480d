<?php
// +----------------------------------------------------------------------
// | 嫁娶吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Jiaqujiri
{
    /**
     * @var array|string[]
     */
    protected array $sxList = ['', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

    /**
     * @var Ex 男
     */
    protected Ex $mLunar;

    /**
     * @var Ex 女
     */
    protected Ex $fLunar;

    /**
     * 生肖相冲
     * @var array|string[]
     */
    protected array $sxChongList = ['', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳'];

    /**
     * 要排除的生肖
     * @var array
     */
    protected array $sxNumList = [];

    /**
     * 订单时间
     * @var Ex
     */
    protected Ex $oLunar;

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * 结婚吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男姓名
            // 'malename' => I('malename', '', 'trim'),
            // 男出生时间
            'maletime' => input('maletime', '', 'trim'),
            // 男父生肖
            'malefsx' => input('malefsx', 0, 'intval'),
            // 男母生肖
            'malemsx' => input('malemsx', 0, 'intval'),
            // 女姓名
            //'femalename' => I('femalename', '', 'trim'),
            // 女出生时间
            'femaletime' => input('femaletime', '', 'trim'),
            // 女父生肖
            'femalefsx' => input('femalefsx', 0, 'intval'),
            // 女母生肖
            'femalemsx' => input('femalemsx', 0, 'intval'),
            // 起始时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
            // 月分
            'plan_time' => input('plan_time', 24, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'maletime|男方出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'malefsx|男父生肖' => ['require', 'between:0,12'],
                'malemsx|男母生肖' => ['require', 'between:0,12'],
                // ['femalename|女方姓名', ['require', 'chs',]],
                'femaletime|女方出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'femalefsx|女父生肖' => ['require', 'between:0,12'],
                'femalemsx|女母生肖' => ['require', 'between:0,12'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->mLunar = Ex::date($data['maletime'])->sex(0);
        $myear = $this->mLunar->dateTime->format('Y');
        $this->fLunar = Ex::date($data['femaletime'])->sex(1);
        $fyear = $this->fLunar->dateTime->format('Y');
        $this->oLunar = Ex::date($data['otime']);
        $sxNumList = [
            $data['malefsx'], $data['malemsx'], $data['femalefsx'], $data['femalemsx'],
        ];
        $sxNumList = array_unique($sxNumList);
        foreach ($sxNumList as $v) {
            if ($v > 0) {
                $this->sxNumList[] = $v;
            }
        }
        $maleBazi = $this->mLunar->getLunarByBetween();
        $maleBazi['god'] = $this->mLunar->getGod();
        $maleBazi['_god'] = $this->mLunar->_getGod();
        $maleBazi['nayin'] = $this->mLunar->getNayin();
        $femaleBazi = $this->fLunar->getLunarByBetween();
        $femaleBazi['god'] = $this->fLunar->getGod();
        $femaleBazi['_god'] = $this->fLunar->_getGod();
        $femaleBazi['nayin'] = $this->fLunar->getNayin();
        $res = [
            'male' => $maleBazi,
            'femail' => $femaleBazi,
            'gua' => BaziExt::getMingGua($myear, $fyear),
            'dali' => $this->getDaLiYue(),
            'xiaoli' => $this->getXiaoLiYue(),
            // 生肖关系
            'guanxi' => $this->getShengXiaoGuanXi($maleBazi['jinian']['y'][1], $femaleBazi['jinian']['y'][1]),
            // 吉日
            'jiri' => $this->getJiri(),
        ];
        return $res;
    }

    /**
     * 大利月
     * @return array
     */
    protected function getDaLiYue(): array
    {
        $sx = $this->fLunar->getZodiac();
        $list = [
            '鼠' => ['十二月', '六月'],
            '牛' => ['十一月', '五月'],
            '虎' => ['八月', '二月'],
            '兔' => ['正月', '七月'],
            '龙' => ['四月', '十月'],
            '蛇' => ['三月', '九月'],
            '马' => ['十二月', '六月'],
            '羊' => ['十一月', '五月'],
            '猴' => ['八月', '二月'],
            '鸡' => ['正月', '七月'],
            '狗' => ['四月', '十月'],
            '猪' => ['三月', '九月'],
        ];
        return $list[$sx];
    }

    /**
     * 小利月
     * @return array
     */
    protected function getXiaoLiYue(): array
    {
        $year = $this->fLunar->getLunarGanzhiYear();
        $dz = $year[1];
        $list = [
            '寅' => ['三月', '九月'],
            '卯' => ['六月', '十二月'],
            '辰' => ['五月', '十一月'],
            '巳' => ['二月', '八月'],
            '午' => ['正月', '七月'],
            '未' => ['四月', '十月'],
            '申' => ['三月', '九月'],
            '酉' => ['六月', '十二月'],
            '戌' => ['五月', '十一月'],
            '亥' => ['二月', '八月'],
            '子' => ['正月', '七月'],
            '丑' => ['四月', '十月'],
        ];
        return $list[$dz];
    }

    /**
     * 生肖关系
     * @param string $dzY 年地支
     * @param string $dzY2 年地支2
     * @return array
     */
    protected function getShengXiaoGuanXi(string $dzY, string $dzY2): array
    {
        $list = [
            '从属相合婚的角度来看，男方与女方的生肖相冲，你们之间会有些小问题，选择吉日嫁娶可以化解这种不利关系。',
            '从属相合婚的角度来看，男方与女方的生肖相刑，遇事多谦让，选择吉日嫁娶可以化解这种不利关系，让婚姻状态更加稳定。',
            '从属相合婚的角度来看，男方与女方的生肖相合，选择吉日嫁娶可以让婚姻关系更和睦。',
            '从属相合婚的角度来看，男方与女方的生肖组合中规中矩，选择吉日嫁娶可以让感情关系更稳定。',
        ];
        // 妨夫月
        $list2 = [
            '寅' => '6、12', '卯' => '3、9', '辰' => '2、8', '巳' => '5、11', '午' => '4、10', '未' => '1、7',
            '申' => '6、12', '酉' => '3、9', '戌' => '2、8', '亥' => '5、11', '子' => '4、10', '丑' => '1、7',
        ];
        // 妨妇月
        $list3 = [
            '寅' => '1、7', '卯' => '2、8', '辰' => '3、9', '巳' => '4、10', '午' => '5、11', '未' => '6、12',
            '申' => '1、7', '酉' => '2、8', '戌' => '3、9', '亥' => '4、10', '子' => '5、11', '丑' => '6、12',
        ];
        if (BaziExt::getXianChongDz($dzY, $dzY2) || BaziExt::getXianChongDz($dzY2, $dzY)) {
            $str = $list[0];
        } elseif (BaziExt::getXianXinDz($dzY, $dzY2) || BaziExt::getXianXinDz($dzY2, $dzY)) {
            $str = $list[1];
        } elseif ($this->getSanHeByDz($dzY . $dzY2) || $this->getLiuHeByDz($dzY . $dzY2)) {
            $str = $list[2];
        } else {
            $str = $list[3];
        }
        $sxf = $this->fLunar->getZodiac();
        $sxm = $this->mLunar->getZodiac();
        $result = [
            'title' => $str,
            'chong' => [
                $this->getSxChong($sxm), $this->getSxChong($sxf),
            ],
            'good' => implode('、', $this->getMonthUsed()),
            'm' => $list2[$dzY2],
            'f' => $list3[$dzY2],
        ];
        return $result;
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    protected function getJiri(): array
    {
        $startTime = strtotime($this->oLunar->dateTime->format('Y-m-d'));
        $endTime = strtotime("+{$this->orginData['plan_time']} month", $startTime);
        $jiNianM = $this->mLunar->getLunarTganDzhi();
        $jiNianW = $this->fLunar->getLunarTganDzhi();
        $result = [
        ];
        $list1 = ['清明'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        $list3 = [
            '天德' => '吉昌之日，有逢凶化吉、趋吉避凶的寓意。有助夫妻琴瑟调和，福泽绵延。',
            '月德' => '吉神相助，家宅顺遂。有助于避免婆媳之间的矛盾，婚后生活幸福圆满。',
            '天德合' => '五行契合之日，调和阴阳，避开与家人相冲的日子，有助夫妻和睦。',
            '月德合' => '五行契合之日，避开对双方造成不利影响的日子，有助家宅顺遂。',
        ];

        // 生肖相冲
        $sxChongList = $this->sxChongList;
        $sxChong = [];//相冲生肖
        foreach ($this->sxNumList as $v1) {
            $sxChong[] = $sxChongList[$v1];
        }
        $liYue = $this->getMonthUsed();
        $listChong = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        $listJianRen = [
            '甲' => ['卯' => '酉', '酉' => '卯'],
            '乙' => ['辰' => '戌', '戌' => '辰'],
            '丙' => ['午' => '子', '子' => '午'],
            '丁' => ['未' => '丑', '丑' => '未'],
            '戊' => ['午' => '子', '子' => '午'],
            '己' => ['未' => '丑', '丑' => '未'],
            '庚' => ['酉' => '卯', '卯' => '酉'],
            '辛' => ['戌' => '辰', '辰' => '戌'],
            '壬' => ['子' => '午', '午' => '子'],
            '癸' => ['丑' => '未', '未' => '丑'],
        ];
        $jiHourList = [];
        // 箭刃日元+日支
        if (isset($listJianRen[$jiNianW['d'][0]][$jiNianW['d'][1]])) {
            $jiHourList[] = $listJianRen[$jiNianW['d'][0]][$jiNianW['d'][1]];
        }
        if (isset($listJianRen[$jiNianM['d'][0]][$jiNianM['d'][1]])) {
            $jiHourList[] = $listJianRen[$jiNianM['d'][0]][$jiNianM['d'][1]];
        }
        // 男命需要忌讳的时辰 备注：真三杀 男年支+日支
        $listMan = [
            '子未' => '未', '丑辰' => '辰', '寅丑' => '丑', '卯戌' => '戌', '辰未' => '未', '巳辰' => '辰',
            '午丑' => '丑', '未戌' => '戌', '申未' => '未', '酉辰' => '辰', '戌丑' => '丑', '亥戌' => '戌',
        ];
        $strYDz = $jiNianM['y'][1] . $jiNianM['d'][1];
        if (isset($listMan[$strYDz])) {
            $jiHourList[] = $listMan[$strYDz];
        }
        $listMonthNumber = [
            '正月' => 1, '二月' => 2, '三月' => 3, '四月' => 4, '五月' => 5, '六月' => 6,
            '七月' => 7, '八月' => 8, '九月' => 9, '十月' => 10, '十一月' => 11, '十二月' => 12,
        ];
        // 大利月
        $daLi = [];
        // 小利月
        $xiaoLi = [];
        foreach ($this->getDaLiYue() as $v) {
            if (!isset($listMonthNumber[$v])) {
                continue;
            }
            $daLi[] = $listMonthNumber[$v];
        }
        foreach ($this->getXiaoLiYue() as $v) {
            if (!isset($listMonthNumber[$v])) {
                continue;
            }
            $xiaoLi[] = $listMonthNumber[$v];
        }
        // 小利月
        for ($i = 0; $i < 730; $i++) {
            $tmpTime = $startTime + $i * 86400;
            if ($tmpTime > $endTime) {
                break;
            }
            $huangli = Huangli::date($tmpTime);
            $jiXiong = $huangli->getJiXiong();
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                continue;
            }
            $base = $huangli->getLunarByBetween();
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '10_1', '9_9', '10_10'])) {
                continue;
            }
            // 干支
            $tmpGZ = [
                implode('', $base['jinian']['y']), implode('', $base['jinian']['m']), implode('', $base['jinian']['d']),
            ];
            if (!in_array('嫁娶', $jiXiong['yi'])) {
                continue;
            }
            $jieQi = Huangli::isJieQi($tmpTime);
            $tomorrowJieQi = Huangli::isJieQi($tmpTime + 86400);
            $tmpjiHourList = $jiHourList;
            $tmpjiHourList[] = $listChong[$base['jinian']['d'][1]];
            // 清明
            if (in_array($jieQi, $list1)) {
                continue;
            }
            // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天   2农历初1
            if (in_array($tomorrowJieQi, $list2) || $base['_nongli']['d'] == 1) {
                continue;
            }
            // 彭祖忌			备注：亥不行嫁
            if ($base['jinian']['d'][1] == '亥') {
                continue;
            }
            // 岁破
            if ($this->checkSuiPo($base['jinian']['y'][1] . $base['jinian']['d'][1])) {
                continue;
            }
            // 小红沙 流日月支	日支
            $monthDzDayDz = $base['jinian']['m'][1] . $base['jinian']['d'][1];
            // 披麻,月破,小红沙
            if ($this->checkXiaoHongSha($monthDzDayDz) || $this->checkYuePo($monthDzDayDz)) {
                continue;
            }
            // 流日月支+受死日
            if ($this->checkShouSi($monthDzDayDz)) {
                continue;
            }
            // 杨公忌+九毒日+天地交泰
            if ($this->checkYangGong($nongliNumberStr)) {
                continue;
            }
            // 三娘煞
            if ($this->checkSanNiangSha($tmpGZ[2], (int)$base['_nongli']['d'])) {
                continue;
            }
            // 判断生肖是否相冲
            if ($this->filterData3($tmpGZ[2], $sxChong)) {
                continue;
            }
            // 正四废日
            if ($this->checkZhengSiFeiRi($base['jinian']['m'][1], $tmpGZ[2])) {
                continue;
            }
            // 男女命
            if ($this->checkMing($jiNianM, $base['jinian']['m'][1]) || $this->checkMing($jiNianW, $base['jinian']['m'][1], 1)) {
                continue;
            }
            $jianChu = $huangli->getJianChu();
            // 去掉建除中的平日、收日、闭日
            if (in_array($jianChu, ['平', '收', '闭'])) {
                continue;
            }
            $tmp = [
                'gongli' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'week' => Huangli::getWeekChs($tmpTime),
                'nongli' => implode('', $base['nongli']),
                'hour' => $this->getShi($tmpjiHourList),
                'title' => '吉',
                'detail' => '夫妇荣昌，有助婚后生活和谐幸福。',
                'pos' => $this->getPos($base['jinian']['d'][0]),
            ];
            $jishenArr = array_intersect(["天德", "月德", "天德合", "月德合"], $jiXiong['jishen']);
            // 流日日支+女命年支
            $strdzMyz = $base['jinian']['d'][1] . $jiNianW['y'][1];
            // 流日月支+流日日支
            $strMzDz = $base['jinian']['m'][1] . $base['jinian']['d'][1];
            // 农历月
            $nongliYue = str_replace('闰', '', $base['nongli']['m']);//去除闰字
            $resultKey = 'xiao';
            if (in_array($base['_nongli']['m'], $liYue) && !empty($jishenArr)) {
                $jiShen = current($jishenArr);
                $tmp['detail'] = $list3[$jiShen] ?? '';
                $tmp['title'] = '大吉';
                $resultKey = 'da';
            } elseif ($this->checkKeMingLiuHe($strdzMyz)) {
                $tmp['detail'] = '对女方有所助益，且与男方命局匹配，有助夫妻琴瑟和谐。';
                $tmp['title'] = '中吉';
                $resultKey = 'zhong';
            } elseif ($this->checkKeMingSanHe($strdzMyz)) {
                $tmp['detail'] = '对女方有一定助益，且与男方命局匹配，有利于夫妻感情发展。';
                $tmp['title'] = '中吉';
                $resultKey = 'zhong';
            } elseif (in_array($base['_nongli']['m'], $daLi)) {
                $tmp['detail'] = '适逢女方出嫁最佳月份，剔除凶日，避免妨害翁姑长辈。';
                $tmp['title'] = '吉';
            } elseif (in_array($base['_nongli']['m'], $xiaoLi)) {
                $tmp['detail'] = '适逢女方出嫁次佳月份，剔除凶日，减少婚姻灾劫。';
                $tmp['title'] = '吉';
            } elseif ($this->checkKeNeiLiuHe($strMzDz)) {
                $tmp['detail'] = '五行凝聚力稍弱，但是为有情之日，琴瑟和谐。';
                $tmp['title'] = '吉';
            } elseif ($this->checkKeNeiSanHe($strMzDz)) {
                $tmp['detail'] = '五行凝聚力强，为吉配之日，有利于夫妻感情发展。';
                $tmp['title'] = '吉';
            }
            $titleTmp = date('Y年m月', $tmpTime);
            $result[$titleTmp]['title'] = $titleTmp;
            $result[$titleTmp]['list'][] = $tmp;
        }
        return array_values($result);
    }

    /**
     * 判断是否三合
     * @param string $dz 两个地支拼成的字符串
     * @return bool
     */
    private function getSanHeByDz($dz): bool
    {
        $sanheList = [
            '子辰', '丑巳', '寅午', '卯亥', '辰子', '巳酉', '午寅', '未亥', '申辰', '酉巳', '戌午', '亥卯',
            '子申', '丑酉', '寅戌', '卯未', '辰申', '巳丑', '午戌', '未卯', '申子', '酉丑', '戌寅', '亥未',
        ];
        return in_array($dz, $sanheList);
    }

    /**
     * 判断地支六合
     * @param string $dz 两个地支拼成的字符串
     * @return bool
     */
    private function getLiuHeByDz($dz): bool
    {
        $list = [
            ['子丑', '丑子', '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅'],
        ];
        return in_array($dz, $list);
    }

    /**
     * 根据生肖获得相冲生肖
     * @param string $sx 生肖
     * @return string
     */
    private function getSxChong(string $sx): string
    {
        $chong = [
            '鼠' => '马', '牛' => '羊', '虎' => '猴', '兔' => '鸡', '龙' => '狗', '蛇' => '猪',
            '马' => '鼠', '羊' => '牛', '猴' => '虎', '鸡' => '兔', '狗' => '龙', '猪' => '蛇',
        ];
        return $chong[$sx];
    }

    /**
     * 判断生肖是否相冲
     * @param $from
     * @param $filterList
     * @return bool
     */
    protected function filterData3($from, $filterList): bool
    {
        $res = false;
        foreach ($filterList as $v2) {
            if (str_contains($from, $v2)) {
                $res = true;
                break;
            }
        }
        return $res;
    }

    /**
     * 获得符合条件的月份
     * @return array
     */
    private function getMonthUsed(): array
    {
        $list1 = [
            '正月' => 1, '二月' => 2, '三月' => 3, '四月' => 4, '五月' => 5, '六月' => 6,
            '七月' => 7, '八月' => 8, '九月' => 9, '十月' => 10, '十一月' => 11, '十二月' => 12,
        ];
        $goodList = array_merge($this->getDaLiYue(), $this->getXiaoLiYue());
        $goodList = array_unique($goodList);
        $goodMonth = [];
        foreach ($goodList as $v) {
            if (isset($list1[$v])) {
                $goodMonth[] = $list1[$v];
            }
        }
        sort($goodMonth);
        return $goodMonth;
    }

    /**
     * 正四废日
     * @param string $monthDz 月地支
     * @param string $daygz 日干支
     * @return bool
     */
    private function checkZhengSiFeiRi(string $monthDz, string $daygz): bool
    {
        switch ($monthDz) {
            case '寅':
            case '卯':
            case '辰':
                $bool = in_array($daygz, ['庚申', '辛酉']);
                break;
            case '巳':
            case '午':
            case '未':
                $bool = in_array($daygz, ['壬子', '癸亥']);
                break;
            case '申':
            case '酉':
            case '戌':
                $bool = in_array($daygz, ['甲寅', '乙卯']);
                break;
            default:
                $bool = in_array($daygz, ['丙午', '丁巳']);
                break;
        }
        return $bool;
    }

    /**
     * 岁破
     * @param string $str 流年年支+日支
     * @return bool
     */
    private function checkSuiPo(string $str): bool
    {
        $list = ['子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳'];
        return in_array($str, $list);
    }

    /**
     * 小红沙
     * @param string $str 流日月支+红沙日支
     * @return bool
     */
    private function checkXiaoHongSha(string $str): bool
    {
        $list = ['寅酉', '卯巳', '辰丑', '巳酉', '午巳', '未丑', '申酉', '酉巳', '戌丑', '亥酉', '子巳', '丑丑'];
        return in_array($str, $list);
    }

    /**
     * 受死
     * @param string $str 流日月支+日支
     * @return bool
     */
    private function checkShouSi(string $str): bool
    {
        $list = ['寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉'];
        return in_array($str, $list);
    }

    /**
     * 杨公忌+九毒日+天地交泰
     * @param string $str 农历月数字+日数字
     * @return bool
     */
    private function checkYangGong(string $str): bool
    {
        $list = [
            '1_13', '2_11', '3_9', '4_7', '5_5', '6_3', '7_1', '7_29', '8_27', '9_25', '10_23', '11_21', '12_19',
            '5_6', '5_7', '5_14', '5_15', '5_16', '5_17', '5_25', '5_26', '5_27',
        ];
        return in_array($str, $list);
    }

    /**
     * 三娘煞
     * @param string $daygz 日干支
     * @param int $nongliDayNumber 日数字
     * @return bool
     */
    private function checkSanNiangSha(string $daygz, int $nongliDayNumber): bool
    {
        $str = $daygz . '_' . $nongliDayNumber;
        $list = ['庚午_3', '辛未_7', '戊申_13', '己酉_18', '丙午_22', '丁未_27'];
        return in_array($str, $list);
    }

    /**
     * 披麻,月破
     * @param string $str 月支+日支
     * @return bool
     */
    private function checkYuePo(string $str): bool
    {
        $list = [
            // 披麻
            '寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯',
            // 月破
            '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未',
        ];
        return in_array($str, $list);
    }

    /**
     * 男女命
     * @param array $jiNian 纪年
     * @param string $dz 日支
     * @param int $sex 性别 0男 1女
     * @return bool
     */
    private function checkMing($jiNian, $dz, $sex = 0): bool
    {
        $listGirl = ['子戌', '丑亥', '寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉'];
        // 女年支+日支
        $list = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
            '子未', '丑午', '寅巳', '卯辰', '辰卯', '巳寅', '午丑', '未子', '申亥', '酉戌', '戌酉', '亥申',
        ];
        if ($sex) {
            $list = array_merge($listGirl, $list);
        }
        // 女命月支+流日支
        $list1 = [
            '子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申',
        ];
        $bool = false;
        if (in_array($jiNian['y'][1] . $dz, $list) || in_array($jiNian['m'][1] . $dz, $list1)) {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 吉时
     * @param $jiList
     * @return string
     */
    private function getShi($jiList): string
    {
        $list = [
            '子' => '23-1', '丑' => '1-3', '寅' => '3-5', '卯' => '5-7', '辰' => '7-9', '巳' => '9-11',
            '午' => '11-13', '未' => '13-15', '申' => '15-17', '酉' => '17-19', '戌' => '19-21', '亥' => '21-23',
        ];
        $jihour = ['辰', '巳', '午', '未', '申'];
        $result = '辰时 7-9时';
        foreach ($jihour as $v) {
            if (in_array($v, $jiList)) {
                continue;
            }
            $result = $v . '时 ' . $list[$v] . '时';
            break;
        }
        return $result;
    }

    /**
     * 获得方位
     * @param $tg
     * @return array
     */
    private function getPos($tg): array
    {
        $listXi = [
            '甲' => '东北', '乙' => '西北', '丙' => '西南', '丁' => '正南', '戊' => '东南', '己' => '东北', '庚' => '西北', '辛' => '西南', '壬' => '正南', '癸' => '东南',
        ];
        $listfu = [
            '甲' => '正北', '乙' => '西南', '丙' => '西北', '丁' => '东南', '戊' => '东北', '己' => '正北', '庚' => '西南', '辛' => '西北', '壬' => '东南', '癸' => '东北',
        ];
        $listCai = [
            '甲' => '东北', '乙' => '东北', '丙' => '西南', '丁' => '西南', '戊' => '正北', '己' => '正北', '庚' => '正东', '辛' => '正东', '壬' => '正南', '癸' => '正南',
        ];
        $result = [
            'up' => $listXi[$tg], 'down' => $listfu[$tg],
        ];
        if (in_array($tg, ['丙', '辛'])) {
            $result = [
                'up' => $listfu[$tg], 'down' => $listCai[$tg],
            ];
        }
        return $result;
    }

    /**
     * 课命六合 流日日支+女命年支
     * @param $str
     * @return bool
     */
    private function checkKeMingLiuHe($str): bool
    {
        $list = [
            '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子',
        ];
        return in_array($str, $list);
    }

    /**
     * 课命三合 流日日支+女命年支
     * @param $str
     * @return bool
     */
    private function checkKeMingSanHe($str): bool
    {
        $list = [
            '寅午', '卯亥', '辰申', '巳酉', '午寅', '未亥', '申子', '酉丑', '戌午', '亥卯', '子申', '丑巳',
            '寅戌', '卯未', '辰子', '巳丑', '午戌', '未卯', '申辰', '酉巳', '戌寅', '亥未', '子辰', '丑酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 课内三合 流日月支    流日日支
     * @param $str
     * @return bool
     */
    private function checkKeNeiSanHe($str): bool
    {
        $list = [
            '卯亥', '辰申', '巳酉', '午寅', '未亥', '申子', '酉丑', '戌午', '亥卯', '子申', '丑巳', '卯未',
            '辰子', '巳丑', '午戌', '未卯', '申辰', '酉巳', '戌寅', '亥未', '子辰', '丑酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 课内六合 流日月支    流日日支
     * @param $str
     * @return bool
     */
    private function checkKeNeiLiuHe($str): bool
    {
        $list = [
            '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子',
        ];
        return in_array($str, $list);
    }
}
