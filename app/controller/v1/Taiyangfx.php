<?php
// +----------------------------------------------------------------------
// | Taiyangfx.星盘太阳返限盘接口
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\astro\AstrologyUtil;
use app\lib\astro\Chart;
use app\model\astro\Sweph;
use app\traits\CityLnglatTraits;
use app\traits\xp\XpResTraits;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Validate;

class Taiyangfx
{
    use CityLnglatTraits;
    use XpResTraits;

    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 经纬度
     * @var array
     */
    protected array $lnt = [];

    /**
     * 用户初始
     * @var AstrologyUtil
     */
    protected AstrologyUtil $astroUntil;

    /**
     * @var string
     */
    protected string $errorMsg = '';

    /**
     * @var array
     */
    protected array $hc;

    /**
     * 行星落点
     * @var array
     */
    protected array $planet;

    /**
     * @var Chart
     */
    protected Chart $chartLib;

    /**
     * 星盘太阳返限盘接口
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 时间
            'd' => input('date', '', 'trim'),
            'time' => input('time', '12:00', 'trim'),
            'timezone' => input('timezone', 8, 'intval'),
            'province' => input('province', '', 'trim'),
            'city' => input('city', '', 'trim'),
            'h_sys' => input('hsys', 'p', 'trim,strtolower'),
            'year' => input('year', date('Y'), 'intval'),
        ];
        $validate = Validate::rule(
            [
                'd|出生日期' => ['require', 'dateFormat:Y-m-d', 'before:2090-12-31', 'after:1900-01-01'],
                'time|时间' => ['require', function ($data) {
                    if (strtotime("2000-1-1 {$data}")) {
                        return true;
                    }
                    return '时间格式不正确';
                }],
                'timezone|时区' => ['require'],
                'province|省份' => ['require'],
                'city|城市' => ['require'],
                'h_sys|宫盘' => ['require'],
                'year|返限年份' => ['require', 'between:1990,2099'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        // 获得经纬度
        $lngLat = $this->getlngLat($data['province'], $data['city']);
        $this->lnt = $lngLat;
        $newTime = $this->getNewUtData();
        if (false === $newTime) {
            return ['status' => 0, 'msg' => $this->errorMsg];
        }
        $astroUntil = new AstrologyUtil($newTime['d'], $newTime['t'], $lngLat['lng'], $lngLat['lat']);
        $this->astroUntil = $astroUntil;
        $hc = $astroUntil->getHouseCup();
        $this->hc = $hc;
        $planet = $astroUntil->getPlanet();
        $this->planet = $astroUntil->getPlanetInfo();
        $chartLib = new Chart($planet, $hc);
        $this->chartLib = $chartLib;
        $resPlanet = [];
        $hourseNum = $astroUntil->getHourseNum();
        $plInXz = [];
        $plInHourse = [];
        foreach ($planet as $k => $v) {
            $planetList = AstrologyUtil::$planet[$v['planet']];
            $signKey = array_search($v['sign'], AstrologyUtil::$zodiaSignEnS);
            $signInfo = AstrologyUtil::$zodiacSign[$signKey];
            $tmpDegs = (int)($v['degs'] - $hc[0]['degs'] + 360) % 360;
            $hs = $this->getHsNum($tmpDegs, $hourseNum);
            // 行星落在星座中的结果
            $plInXzTmp = $this->getStarInXz($planetList[3], $signInfo[5]);
            $plInHourseTmp = $this->getStarInHouse($planetList[3], $hs);
            if (!empty($plInXzTmp)) {
                $plInXz[] = [
                    'pl' => $planetList[3],
                    'sign' => $signInfo[5],
                    'info' => $plInXzTmp,
                ];
            }
            if (!empty($plInHourseTmp)) {
                $plInHourse[] = [
                    'pl' => $planetList[3],
                    'hs' => $hs,
                    'info' => $plInHourseTmp,
                ];
            }
            $resPlanet[] = [
                'hs' => $hs,
                'deg' => $v['deg'],
                'min' => $v['min'],
                'sec' => $v['sec'],
                'planet' => [
                    'cn' => $planetList[3],
                    'glyph' => $planetList[1],
                    'en' => $planetList[2],
                ],
                'sign' => [
                    // ['Aries', 0, 'red', '♈', "AR", "白羊座"]
                    'cn' => $signInfo[5],
                    'en' => $signInfo[0],
                    'glyph' => $signInfo[3],
                    'en_s' => $signInfo[4],
                    'number' => $signInfo[1],
                ],
                'degs' => $v['degs'],
                'sui' => $v['sui'],
            ];
        }
        $heXian = $this->getHeXian();
        $four = $this->getFourHeXian();
        $hxDetail = $this->getHeXianInfo($heXian, $four);
        $res = [
            'planet' => $resPlanet,
            'hc' => $hc,
            'attr' => $astroUntil->getPlanetAttr(),
            'gong' => $chartLib->getcenterInCircleDeg(),
            'chart' => $chartLib->getChart(),
            'hexian' => $heXian,
            'four' => $four,
            'pl_in_xz' => $plInXz,
            'pl_in_hou' => $plInHourse,
            'hx_detail' => $hxDetail,
        ];
        return $res;
    }

    /**
     * 获得指定年份太阳在用户提供时间落点的相近日期
     * @return array|bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNewUtData()
    {
        $orginData = $this->orginData;
        $swephData = Sweph::info($orginData['d']);
        if (empty($swephData)) {
            $this->errorMsg = '当前日期不存在数据';
            return false;
        }
        $sunDeg = Sweph::getDegreeAndSign($swephData['sun']);
        $likeKey = $sunDeg['deg'] . $sunDeg['sign'];
        $startTime = $orginData['year'] . '-01-01';
        $endTime = $orginData['year'] . '-12-31';
        $swephNew = Sweph::where('d', 'between', [$startTime, $endTime])
            ->where('sun', 'like', "%{$likeKey}%")
            ->cache(3600)
            ->field('d,t,sun')
            ->find();
        if (empty($swephNew)) {
            $this->errorMsg = '当前年份未找到相应的';
            return false;
        }
        $astroUntil = new AstrologyUtil($orginData['d'], $orginData['time'], $this->lnt['lng'], $this->lnt['lat']);
        $planet0 = $astroUntil->getPlanet();
        $sun0 = $planet0[0];// 初始太阳位置
        $astroUntil1 = new AstrologyUtil($swephNew['d'], '12:00:00', $this->lnt['lng'], $this->lnt['lat']);
        $planet1 = $astroUntil1->getPlanet();
        $sun1 = $planet1[0];// 判断太阳位置
        $before = 1;// 在12点前
        if (($sun1['degs'] > $sun0['degs'] && $sun1['sui']) || ($sun1['degs'] < $sun0['degs'] && !$sun1['sui'])) {
            $startD = date('Y-m-d', strtotime($swephNew['d']) - 86400);
            $endD = $swephNew['d'];
        } elseif ($sun1['degs'] == $sun0['degs']) {
            return [
                'd' => $swephNew['d'],
                't' => '12:00:00',
            ];
        } else {
            $before = 0;
            $startD = $swephNew['d'];
            $endD = date('Y-m-d', strtotime($swephNew['d']) + 86400);
        }
        $swephDataStart = Sweph::info($startD);
        $swephDataEnd = Sweph::info($endD);
        // 用后一天的行星位置 - 当天的行星位置=（一天内行星移动位置）
        $next = Sweph::getDegreeAndSign($swephDataEnd['sun']);
        $curr = Sweph::getDegreeAndSign($swephDataStart['sun']);
        $diff = $next['deg'] * 60 + $next['min'] - ($curr['deg'] * 60 + $curr['min']);
        // 每分钟移动的距离
        $diffPerMin = round($diff / 1440, 4);
        $diffTwoTime = AstrologyUtil::parseDegToSec($sun1['degs'] - $sun0['degs']);
        $moveMin = round($diffTwoTime / ($diffPerMin * 60), 2);
        $moveSec = $moveMin * 60;
        $time = strtotime($swephNew['d'] . ' 12:00:00');
        $time = $before ? ($time - $moveSec) : ($time + $moveSec);
        return [
            'd' => date('Y-m-d', $time),
            't' => date('H:i:s', $time),
        ];
    }

    /**
     * 相位信息
     * @return array
     */
    protected function getHeXian(): array
    {
        $list = ['合', '六合', '刑', '拱', '冲'];
        $list1 = [0, 60, 90, 120, 180];
        $planetList = AstrologyUtil::$planet;

        $planet = $this->getPlanetDeg();
        unset($planet['Node']);
        //$planet = array_column($planet, 2);
        $result = [];
        foreach ($planet as $k => $v) {
            foreach ($planet as $k1 => $v1) {
                if ($k1 == $k) {
                    continue;
                }
                $tmpDeg = $this->getDegDiffHalf($v[2], $v1[2]);
                $key = $this->getXianWeiKey($tmpDeg);
                if ($key < 0) {
                    continue;
                }
                $result[$k][$k1] = [
                    'title' => $list[$key],
                    'deg' => $list1[$key],
                ];
                $result[$k1][$k] = [
                    'title' => $list[$key],
                    'deg' => $list1[$key],
                ];
            }
        }
        $result2 = [];
        foreach ($result as $k => $v) {
            $tmpPlanet = $planetList[strtolower($k)];
            $info = [];
            foreach ($v as $k1 => $v1) {
                $tmpPlanet1 = $planetList[strtolower($k1)];
                $info[] = [
                    'star' => [
                        $tmpPlanet1[1], $tmpPlanet1[2], $tmpPlanet1[3],
                    ],
                    'name' => $v1,
                ];
            }
            $result2[] = [
                'star' => [$tmpPlanet[1], $tmpPlanet[2], $tmpPlanet[3]],
                'info' => $info,
            ];
        }
        return $result2;
    }

    /**
     * 获得行星角度
     * @return array
     */
    protected function getPlanetDeg(): array
    {
        $planet = [];
        $hc = $this->hc[0];
        foreach ($this->planet as $v) {
            // $tmpDeg = (($v['hs'] - 1) * 30 + round(($v['deg'] + $v['min'] / 60 + $v['sec'] / 3600)) + 360) % 360;
            $tmpDegs = (int)($v['degs'] - $hc['degs'] + 360) % 360;
            $planet[$v['planet']['en']] = [
                $v['planet']['en'], $v['sign']['en'], $tmpDegs,
            ];
        }
        return $planet;
    }

    /**
     * 四个点的信息
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getFourHeXian(): array
    {
        $gong = $this->chartLib->getcenterInCircleDeg();
        $hc = $this->astroUntil->getHouseCup();
        $first = $hc[0]['degs'];
        $listFour = [
            'ASC' => [
                $gong[0], abs((int)($first - $hc[0]['degs'] + 360) % 360), ['en' => 'ASC', 'cn' => '上升'],
            ],
            'IC' => [
                $gong[3], (360 - abs((int)($first - $hc[3]['degs'] + 360) % 360)), ['en' => 'IC', 'cn' => '天底'],
            ],
            'DES' => [
                $gong[6], abs(($first - $hc[6]['degs'] + 360) % 360), ['en' => 'DES', 'cn' => '下降'],
            ],
            'MC' => [
                $gong[9], (360 - abs((int)($first - $hc[9]['degs'] + 360) % 360)), ['en' => 'MC', 'cn' => '天顶'],
            ],
        ];
        $list = ['合', '六合', '刑', '拱', '冲'];
        $list1 = [0, 60, 90, 120, 180];
        $planet = $this->getPlanetDeg();
        unset($planet['Node']);
        $planetList = AstrologyUtil::$planet;
        $result = [];
        foreach ($listFour as $k => $v) {
            $result[$k] = [
                'title' => $v[2],
                'sign' => $v[0],
                'info' => [],
            ];
            foreach ($planet as $k1 => $v1) {
                if ($k1 == $k) {
                    continue;
                }
                $tmpDeg = $this->getDegDiffHalf($v[1], $v1[2]);
                $key = $this->getXianWeiKey($tmpDeg);
                if ($key < 0) {
                    continue;
                }
                $tmpPlanet = $planetList[strtolower($k1)];
                $result[$k]['info'][] = [
                    $tmpPlanet[1], $tmpPlanet[2], $tmpPlanet[3],
                    $list[$key],
                    $list1[$key],
                ];
            }
        }
        return $result;
    }

    /**
     * 求角度相差半圆
     * @param int $deg1
     * @param int $deg2
     * @return int
     */
    protected function getDegDiffHalf(int $deg1, int $deg2): int
    {
        $deg = ($deg1 - $deg2 + 360) % 360;
        if ($deg > 180) {
            $deg = 360 - $deg;
        }
        return $deg;
    }

    /**
     * 判断是否符合条件
     * @param int $diff
     * @return int
     */
    private function getXianWeiKey(int $diff)
    {
        $result = -1;
        if ($diff <= 4 && $diff >= 0) {
            $result = 0;
        } elseif ($diff >= 57 && $diff <= 62) {
            $result = 1;
        } elseif ($diff >= 87 && $diff <= 93) {
            $result = 2;
        } elseif ($diff >= 117 && $diff <= 123) {
            $result = 3;
        } elseif ($diff >= 177 && $diff <= 183) {
            $result = 4;
        }
        return $result;
    }

    /**
     * 行星在哪个宫
     * @param int $degs 角度
     * @param array $arr
     * @return int
     */
    private function getHsNum(int $degs, array $arr): int
    {
        $ret = 1;
        foreach ($arr as $k => $v) {
            if ($degs >= $v[0] && $degs < $v[1]) {
                $ret = $k;
                break;
            }
        }
        return $ret;
    }
}
