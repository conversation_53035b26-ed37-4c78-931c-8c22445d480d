<?php
// +----------------------------------------------------------------------
// | Fengshuics.风水测试
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;

class Fengshuics
{
    /**
     * 用户生日基础类
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 风水测试
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            'sex' => input('sex', '', 'trim'),
            'pos' => input('pos', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间格式不正确'],
                'sex|性别' => ['require', 'in:0,1'],
                'pos|朝向' => ['require', function ($data) {
                    if (in_array($data, ["北", "东北", "东", "东南", "南", "西南", "西", "西北"])) {
                        return true;
                    }
                    return '房屋朝向不正确';
                }],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $fate = $this->getMingGua();
        $list = [
            '离' => ['生气', '天医', '伏位', '六煞', '五鬼', '绝命', '延年', '祸害'],
            '坎' => ['天医', '生气', '延年', '绝命', '祸害', '六煞', '伏位', '五鬼'],
            '巽' => ['延年', '伏位', '天医', '五鬼', '六煞', '祸害', '生气', '绝命'],
            '震' => ['伏位', '延年', '生气', '祸害', '绝命', '五鬼', '天医', '六煞'],
            '坤' => ['祸害', '五鬼', '六煞', '伏位', '天医', '延年', '绝命', '生气'],
            '乾' => ['五鬼', '祸害', '绝命', '延年', '生气', '伏位', '六煞', '天医'],
            '艮' => ['六煞', '绝命', '祸害', '生气', '延年', '天医', '五鬼', '伏位'],
            '兑' => ['绝命', '六煞', '五鬼', '天医', '伏位', '生气', '祸害', '延年'],
        ];
        // 正东方（震木）	东南方（巽木0	正南方（离火）	西南方（坤土）	正西方（兑金）	西北方（乾金）	正北方（坎水）	东北方（艮土）
        $listPos = [
            ['东', '震木', "木煞", "红色、紫色", "红色"], ['东南', '巽木', "木煞", "红色、紫色", "红色"],
            ['南', '离火', "火煞", "黄色、棕色、咖啡色", "咖啡色"], ['西南', '坤土', "土煞", "白色、金色、银色", "灰色"],
            ['西', '兑金', "金煞", "黑色、蓝色", "黑色"], ['西北', '乾金', "金煞", "黑色、蓝色", "黑色"],
            ['北', '坎水', "水煞", "青色、绿色", "绿色"], ['东北', '艮土', "土煞", "白色、金色、银色", "灰色"],
        ];
        // 东四命中的东
        $listDong = ['震', '巽', '坎', '离'];
        // 东四命 好 的0 不好1 西 好1不好0
        $listGoodOrBad = [
            ["北", "南", "东", "东南"],
            ["西", "西北", "西南", "东北"],
        ];
        // 坐东南朝西北(巽宅)、坐南朝北(离宅)、坐北朝南(坎宅)和坐东朝西(震宅)
        $dongZa = [
            '西北', '北', '南', '西',
        ];
        $listFate = $list[$fate];
        // 判断是东四命还是西四命
        $siMing = in_array($fate, $listDong) ? '东' : '西';
        $good = $siMing == '东' ? $listGoodOrBad[0] : $listGoodOrBad[1];
        $pan = [];
        $li = '不利';
        $xiongList = [];
        foreach ($listPos as $k => $v) {
            $tmpGood = in_array($v[0], $good) ? 1 : 0;
            $pan[$listFate[$k]] = [
                'pos' => $v[0],
                'wx' => $v[1],
                'fu' => $listFate[$k],
                'good' => $tmpGood,
            ];
            if ($data['pos'] == $v[0] && $tmpGood) {
                $li = '利';
            }
            if (!$tmpGood) {
                $xiongList[$listFate[$k]] = [
                    $listFate[$k], $v[0], $v[2], $v[3], $v[4],
                ];
            }
        }
        $listJiXiong = [
            ['生气', '天医', '伏位', '延年'],
            ['祸害', '六煞', '五鬼', '绝命'],
        ];
        $resJi = [];
        $resXiong = [];
        foreach ($listJiXiong[0] as $v) {
            $resJi[] = [$v, $pan[$v]['pos']];
        }
        foreach ($listJiXiong[1] as $v) {
            $resXiong[] = $xiongList[$v];
        }

        return [
            // 八字基础
            'base' => $this->lunar->getLunarByBetween(),
            // 纳音
            'nayin' => $this->lunar->getNayin(),
            // 命卦
            'gua' => $fate,
            // 四命
            'siming' => $siMing,
            // 排版
            'pan' => array_values($pan),
            // 宅方位
            'zai' => in_array($data['pos'], $dongZa) ? '东' : '西',
            // 有利或不利
            'li' => $li,
            // 好的方位
            'good' => $good,
            // 吉位
            'ji' => $resJi,
            // 凶位
            'xiong' => $resXiong,
        ];
    }

    /**
     * 获得命卦
     * @return string
     */
    private function getMingGua(): string
    {
        $fate = [
            '1' => '坎', '2' => '坤', '3' => '震', '4' => '巽', '6' => '乾', '7' => '兑', '8' => '艮', '9' => '离',
            // 男为坤  女为艮
            '5' => ['坤', '艮'],
        ];
        $year = (int)$this->lunar->dateTime->format('Y');
        $dateYear = $year % 100;
        if ($this->lunar->sex == 0) {
            $num = $year >= 2000 ? abs((99 - $dateYear) % 9) : abs((100 - $dateYear) % 9);
            $num = $num == 0 ? 9 : $num;
            $fate = $num == 5 ? $fate[5][0] : $fate[$num];
        } else {
            $num = abs(($dateYear - 4) % 9);
            $num = $num == 0 ? 9 : $num;
            $fate = $num == 5 ? $fate[5][1] : $fate[$num];
        }
        return $fate;
    }
}
