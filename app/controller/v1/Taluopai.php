<?php
// +----------------------------------------------------------------------
// | Taluopai. 塔罗牌
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/8/10
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\model\taluopai\Taluopai as modelTaluopai;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Taluopai
{
    /**
     * 塔罗牌
     * @return array
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        // n为1-78 牌信息 p为位置 0正  1逆
        $data = [
            'n1' => input('n1', 1, 'intval'),
            'n2' => input('n2', 1, 'intval'),
            'n3' => input('n3', 1, 'intval'),
            'p1' => input('p1', 1, 'intval'),
            'p2' => input('p2', 1, 'intval'),
            'p3' => input('p3', 1, 'intval'),
            // 性别 男为0，女为1
            'sex' => input('sex', 0, 'intval'),
        ];
        $validate = Validate::rule(
            [
                'n1|第一张牌' => ['require', 'number', 'between:1,78'],
                'n2|第二张牌' => ['require', 'number', 'between:1,78'],
                'n3|第三张牌' => ['require', 'number', 'between:1,78'],
                'p1|第一张牌方位' => ['require', 'number', 'in:0,1'],
                'p2|第二张牌方位' => ['require', 'number', 'in:0,1'],
                'p3|第三张牌方位' => ['require', 'number', 'in:0,1'],
                'sex|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $res = [
            $this->getData($data['n1'], $data['sex'], $data['p1']),
            $this->getData($data['n2'], $data['sex'], $data['p2']),
            $this->getData($data['n3'], $data['sex'], $data['p3']),
        ];
        return $res;
    }

    /**
     * 根据id获得所需要的数据
     * @param int $id 牌ID
     * @param int $sex 性别
     * @param int $p 牌正反
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    private function getData(int $id, int $sex, int $p): array
    {
        $info = modelTaluopai::info($id)->toArray();
        $key = $p ? 'f' : 'z';
        $key = $key . $sex;
        if (empty($info)) {
            return ['title' => '', 'p' => '', 'key' => '', 'd' => [], 'c' => []];
        }
        return [
            'title' => $info['title'], 'p' => $p, 'key' => $info['key'], 'd' => $info['d'], 'c' => $info[$key],
        ];
    }
}
