<?php
// +----------------------------------------------------------------------
// | Qimendunjia. 奇门遁甲
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;

class Qimendunjia
{
    /**
     * 纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 节气
     * @var array
     */
    protected array $jieQi = [];

    /**
     * 方式 0 转盘 1 飞盘
     * @var int
     */
    protected int $fs = 0;

    /**
     * 1 拆补法 2 置润法 3 茅山道人法
     * @var
     */
    protected int $type = 1;

    /**
     * @var int
     */
    protected int $time = 0;

    /**
     * 天干
     * @var string[]
     */
    protected array $tg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

    /**
     * 地支
     * @var string[]
     */
    protected array $dz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * @var array
     */
    protected array $table = [];

    /**
     * 九宫名
     * @var string[]
     */
    protected array $gongList = ['坎一', '坤二', '震三', '巽四', '中五', '乾六', '兑七', '艮八', '离九'];

    /**
     * 九星盘固定位置
     * @var string[]
     */
    protected array $jiuXingList = ['天蓬', '天任', '天冲', '天辅', '天英', '天芮', '天柱', '天心'];

    /**
     * 八神盘
     * @var array[]
     */
    protected array $baShenList = [
        '阴' => ['直符', '腾蛇', '太阴', '六合', '白虎', '玄武', '九地', '九天'],
        '阳' => ['直符', '腾蛇', '太阴', '六合', '勾陈', '朱雀', '九地', '九天'],
    ];

    /**
     * @var array|int[]
     */
    protected array $tableIndex = [4, 9, 2, 3, 5, 7, 8, 1, 6];

    /**
     * 六十甲子
     * @var string[]
     */
    protected array $sixtyJiNian = [
        '甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉',
        '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未',
        '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳',
        '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯',
        '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑',
        '甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥',
    ];

    /**
     * 奇门遁甲
     * @param string $time
     * @param int $fs //方法 0 转盘 1 飞盘
     * @param int $type //2 置润法 1 拆补法 3 茅山道人法
     * @return array
     */
    public function index(string $time = '', int $fs = 0, int $type = 1)
    {
        $data = [
            'time' => $time,
            // 方法 0 转盘 1 飞盘
            'fs' => $fs,
            // 2 置润法 1 拆补法 3 茅山道人法
            'type' => $type,
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|用户时间' => ['require', 'isDateOrTime:出生时间'],
                'fs|方式' => ['require', 'in:0,1'],
                'type|盘类型' => ['require', 'in:1,2,3'],

            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->fs = $data['fs'];
        $this->type = $data['type'];
        $lunar = Ex::date($data['time'])->sex(0);
        $this->time = $lunar->dateTime->getTimestamp();
        $base = $lunar->getLunarByBetween();
        $this->jiNian = $base['jinian'];
        $this->jieQi = $lunar->getJieQiCur();
        $ju = $this->getJu();
        $list = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
        $ju[1] = $list[$ju[1]] ?? $ju[1];
        $result = [
            'nongli' => $base['nongli'],
            'jinian' => $base['jinian'],
            'xunkong' => $this->getXunKong(),
            'jieqi' => $this->jieQi,
            'ju' => $ju,
            'fuzhou' => $this->getFuzhou(),
        ];
        $detail = [];
        if ($data['fs']) {
            $detail = $this->getFeiPan();
        } else {
            $detail = $this->getZhuanPan();
        }
        // 盘详情
        $result['zhifu'] = $this->table['zhifu'];
        $result['zhishi'] = $this->table['zhishi'];
        $result['detail'] = $detail;
        //$result['t'] = $this->table;
        return $result;
    }

    /**
     * 获得 阳遁/阴遁
     * @return array
     * @throws Exception
     */
    protected function getJu()
    {
        $list = ['冬至', '惊蛰', '小寒', '大寒', '春分', '雨水', '清明', '立夏', '立春', '谷雨', '小满', '芒种'];
        $list2 = [
            '冬至' => [1, 7, 4], '惊蛰' => [1, 7, 4], '小寒' => [2, 8, 5],
            '大寒' => [3, 9, 6], '春分' => [3, 9, 6], '雨水' => [9, 6, 3],
            '清明' => [4, 1, 7], '立夏' => [4, 1, 7], '立春' => [8, 5, 2],
            '谷雨' => [5, 2, 8], '小满' => [5, 2, 8], '芒种' => [6, 3, 9],
            '夏至' => [9, 3, 6], '白露' => [9, 3, 6], '小暑' => [8, 2, 5],
            '大暑' => [7, 1, 4], '秋分' => [7, 1, 4], '立秋' => [2, 5, 8],
            '寒露' => [6, 9, 3], '立冬' => [6, 9, 3], '处暑' => [1, 4, 7],
            '霜降' => [5, 8, 2], '小雪' => [5, 8, 2], '大雪' => [4, 7, 1],
        ];
        $listYuan = ['上元', '中元', '下元'];
        $jieQiCur = $this->jieQi['current'][0];
        $ju = '阴';
        if (in_array($jieQiCur, $list)) {
            $ju = '阳';
        }
        $yuanIndex = 0;
        $juNum = 1;
        $tian = '';
        $tian2 = '';
        switch ($this->type) {
            case 1:
                $jiNianDayZ = $this->jiNian['d'][1];
                $jiNianDayT = $this->jiNian['d'][0];
                $indexTg = array_search($jiNianDayT, $this->tg);
                if ($indexTg > 5) {
                    $indexTg = $indexTg - 5;
                }
                $indexdz = array_search($jiNianDayZ, $this->dz);
                $index = ($indexdz + 12 - $indexTg) % 12;
                $tmp = $this->dz[$index];
                if (in_array($tmp, ['子', '午', '卯', '酉'])) {
                    $yuanIndex = 0;
                } elseif (in_array($tmp, ['寅', '申', '巳', '亥'])) {
                    $yuanIndex = 1;
                } else {
                    $yuanIndex = 2;
                }
                break;
            case 2:
                $jiNianDay = implode('', $this->jiNian['d']);
                $tmpList = [
                    [
                        '甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己卯', '庚辰', '辛巳', '壬午', '癸未',
                        '甲午', '乙未', '丙申', '丁酉', '戊戌', '己酉', '庚戌', '辛亥', '壬子', '癸丑',
                    ],
                    [
                        '己巳', '庚午', '辛未', '壬申', '癸酉', '甲申', '乙酉', '丙戌', '丁亥', '戊子',
                        '己亥', '庚子', '辛丑', '壬寅', '癸卯', '甲寅', '乙卯', '丙辰', '丁巳', '戊午',
                    ],
                    [
                        '甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己丑', '庚寅', '辛卯', '壬辰', '癸巳',
                        '甲辰', '乙巳', '丙午', '丁未', '戊申', '己未', '庚申', '辛酉', '壬戌', '癸亥',
                    ],
                ];
                $tmpIndex = 0;
                if (in_array($jiNianDay, $tmpList[0])) {
                    $tmpIndex = array_search($jiNianDay, $tmpList[0]);
                    $yuanIndex = 0;
                } elseif (in_array($jiNianDay, $tmpList[1])) {
                    $tmpIndex = array_search($jiNianDay, $tmpList[1]);
                    $yuanIndex = 1;
                } else {
                    $tmpIndex = array_search($jiNianDay, $tmpList[2]);
                    $yuanIndex = 2;
                }
                $tian = '第' . ($tmpIndex % 5 + 1) . '天';
                if ($this->fs == 0) {
                    $jieQi = $this->jieQi;
                    $startTime = strtotime($jieQi['current'][1]);
                    $endTime = strtotime($jieQi['next'][1]);
                    $diff = (int)(($endTime - $startTime) / 86400);
                    for ($i = 1; $i <= $diff; $i++) {
                        $diffTime = $startTime + 86400 * $i;
                        $diffLunar = Ex::date($diffTime);
                        $dayJianNian = $diffLunar->getLunarGanzhiDay();
                        $dayJianNianStr = implode('', $dayJianNian);
                        if (in_array($dayJianNianStr, $tmpList[0])) {
                            $tian2 = date('Y年m月d日', $diffTime) . ' ' . $dayJianNianStr;
                            break;
                        }
                    }
                }
                break;
            case 3:
                $diffDay = (int)(($this->time - strtotime($this->jieQi['current'][1])) / 86400);
                if ($diffDay <= 5) {
                    $yuanIndex = 0;
                } elseif ($diffDay <= 10) {
                    $yuanIndex = 1;
                } else {
                    $yuanIndex = 2;
                }
                break;
        }
        $juNum = $list2[$jieQiCur][$yuanIndex];
        $result = [
            $ju, $juNum, $listYuan[$yuanIndex], $tian, $tian2,
        ];
        // 地盘 从0开始为第一局 8为第9局
        $listDiPan = [
            '阳' => [
                ['戊', '己', '庚', '辛', '壬', '癸', '丁', '丙', '乙'],
                ['乙', '戊', '己', '庚', '辛', '壬', '癸', '丁', '丙'],
                ['丙', '乙', '戊', '己', '庚', '辛', '壬', '癸', '丁'],
                ['丁', '丙', '乙', '戊', '己', '庚', '辛', '壬', '癸'],
                ['癸', '丁', '丙', '乙', '戊', '己', '庚', '辛', '壬'],
                ['壬', '癸', '丁', '丙', '乙', '戊', '己', '庚', '辛'],
                ['辛', '壬', '癸', '丁', '丙', '乙', '戊', '己', '庚'],
                ['庚', '辛', '壬', '癸', '丁', '丙', '乙', '戊', '己'],
                ['己', '庚', '辛', '壬', '癸', '丁', '丙', '乙', '戊'],
            ],
            '阴' => [
                ['戊', '乙', '丙', '丁', '癸', '壬', '辛', '庚', '己'],
                ['己', '戊', '乙', '丙', '丁', '癸', '壬', '辛', '庚'],
                ['庚', '己', '戊', '乙', '丙', '丁', '癸', '壬', '辛'],
                ['辛', '庚', '己', '戊', '乙', '丙', '丁', '癸', '壬'],
                ['壬', '辛', '庚', '己', '戊', '乙', '丙', '丁', '癸'],
                ['癸', '壬', '辛', '庚', '己', '戊', '乙', '丙', '丁'],
                ['丁', '癸', '壬', '辛', '庚', '己', '戊', '乙', '丙'],
                ['丙', '丁', '癸', '壬', '辛', '庚', '己', '戊', '乙'],
                ['乙', '丙', '丁', '癸', '壬', '辛', '庚', '己', '戊'],
            ],
        ];
        $this->table['dipan'] = $listDiPan[$ju][$juNum - 1];
        return $result;
    }

    /**
     * 根据时干支获得符首
     * @return array
     */
    protected function getFuzhou()
    {
        $jiNianHourTz = $this->jiNian['h'][0] . $this->jiNian['h'][1];
        $list = $this->sixtyJiNian;
        $list2 = [
            [['甲', '子'], '戊'], [['甲', '戌'], '己'], [['甲', '申'], '庚'],
            [['甲', '午'], '辛'], [['甲', '辰'], '壬'], [['甲', '寅'], '癸'],
        ];
        $index = array_search($jiNianHourTz, $list);
        $num = (int)($index / 10);
        return $list2[$num] ?? $list2[0];
    }

    /**
     * 获得旬空
     */
    protected function getXunKong()
    {
        $result = [];
        $list = $this->sixtyJiNian;
        $list2 = ['戌亥', '申酉', '午未', '辰巳', '寅卯', '子丑'];
        $jiNian = $this->jiNian;
        foreach ($jiNian as $k => $v) {
            $tmp = implode('', $v);
            $index = array_search($tmp, $list);
            $num = (int)($index / 10);

            $result[$k] = $list2[$num] ?? $list2[0];
        }
        return $result;
    }

    /**
     * 转盘算法
     * @return array
     * @throws Exception
     */
    protected function getZhuanPan()
    {
        //阴/阳遁
        $ju = $this->getJu();
        $tmpList1 = [0, 7, 2, 3, 8, 1, 6, 5];
        $diPan = $this->table['dipan'];
        //符首
        $fuZhouList = $this->getFuzhou();
        $fuZhou = $fuZhouList[1];
        $fuzhouIndex = array_search($fuZhou, $diPan);
        $tmpFuZhou = $fuZhou;
        if ($fuzhouIndex == 4) {
            $tmpFuZhou = $diPan[1];
        }

        $tmpList = [$diPan[0], $diPan[7], $diPan[2], $diPan[3], $diPan[8], $diPan[1], $diPan[6], $diPan[5]];
        $fuZhouIndexBydp = array_search($tmpFuZhou, $tmpList);
        $hourIndex = array_search($this->jiNian['h'][0], $tmpList);
        // 天盘 开始
        if ($this->jiNian['h'][0] == '甲') {
            $this->table['tianpan'] = $diPan;
        } else {
            if (false === $hourIndex) {
                $hourIndex = 5;
            }
            $tianPanList = [];
            foreach ($tmpList as $k => $v) {
                $tianPanList[($hourIndex + $k) % 8] = $tmpList[($fuZhouIndexBydp + $k) % 8];
            }
            $tianPan = ['', '', '', '', '', '', '', '', ''];
            foreach ($tmpList1 as $k => $v) {
                $tianPan[$v] = $tianPanList[$k];
            }
            $this->table['tianpan'] = $tianPan;
        }
        // 天盘 结束
        // 值使 八门
        $baMenList = ['休门', '生门', '伤门', '杜门', '景门', '死门', '惊门', '开门'];
        $this->table['zhishi'][0] = $baMenList[$fuZhouIndexBydp];
        // 时支在地支位置
        $hourDzIndex = array_search($this->jiNian['h'][1], $this->dz);
        $xunDzIndex = array_search($fuZhouList[0][1], $this->dz);
        $diff = ($hourDzIndex - $xunDzIndex + 12) % 12;
        $this->table['bamen'] = ['', '', '', '', '', '', '', '', ''];
        foreach ($baMenList as $k => $v) {
            if ($ju[0] == '阴') {
                $this->table['bamen'][$tmpList1[$k]] = $baMenList[($k - $diff % 8 + 8) % 8];
            } else {
                $this->table['bamen'][$tmpList1[$k]] = $baMenList[($k + $diff) % 8];
            }
        }
        $zhishiIndex = array_search($this->table['zhishi'][0], $this->table['bamen']);
        $this->table['zhishi'][1] = $this->gongList[$zhishiIndex];
        $this->table['zhishi'][2] = $zhishiIndex;
        // 九星盘
        $this->table['zhifu'][0] = $this->jiuXingList[$fuZhouIndexBydp];
        // ['天蓬', '天任', '天冲', '天辅', '天英', '天芮', '天柱', '天心'];
        $zhiFuIndex = array_search($this->jiNian['h'][0], $tmpList);
        if (false === $zhiFuIndex) {
            $zhiFuIndex = 5;
        }
        $this->table['zhifu'][1] = $this->gongList[$zhiFuIndex];
        $this->table['zhifu'][2] = $zhiFuIndex;
        $this->table['jiuxing'] = ['', '', '', '', '', '', '', '', ''];
        foreach ($this->jiuXingList as $k => $v) {
            $this->table['jiuxing'][$tmpList1[$k]] = $this->jiuXingList[($k - ($zhiFuIndex - $fuZhouIndexBydp + 8) % 8 + 8) % 8];
        }
        // 第八步：确定八神盘，直符随值符排，阳遁顺排，阴遁逆排
        $baShenList = $this->baShenList[$ju[0]];
        $this->table['bashen'] = ['', '', '', '', '', '', '', '', ''];
        $baShenIndex = array_search($zhiFuIndex, $tmpList1);
        foreach ($baShenList as $k => $v) {
            if ($ju[0] == '阴') {
                $this->table['bashen'][$tmpList1[$k]] = $baShenList[($zhiFuIndex - $k + 8) % 8];
            } else {
                $this->table['bashen'][$tmpList1[$k]] = $baShenList[($zhiFuIndex + $k + 8) % 8];
            }
        }
        $this->table['yima'] = $this->getYiMa();
        $result = [];
        foreach ($this->tableIndex as $v) {
            $num = $v - 1;
            $cLeft = '';
            $bLeft = '';
            if ($this->table['jiuxing'][$num] == '天芮') {
                $cLeft = $this->table['dipan'][4];
                $bLeft = '禽';
            }
            $result[] = [
                'top' => [
                    $this->table['yima'][$num],
                    $this->table['bamen'][$num],
                    '',
                ],
                'center' => [
                    $cLeft,
                    $this->table['jiuxing'][$num],
                    $this->table['tianpan'][$num],
                ],
                'bottom' => [
                    $bLeft,
                    $this->table['bashen'][$num],
                    $this->table['dipan'][$num],
                ],
            ];
        }
        return $result;
    }

    /**
     * 飞盘
     * @return array
     * @throws Exception
     */
    protected function getFeiPan()
    {
        // 阴/阳遁
        $ju = $this->getJu();
        // $tmpList1 = [0, 7, 2, 3, 8, 1, 6, 5];
        $diPan = $this->table['dipan'];
        // 符首
        $fuZhouList = $this->getFuzhou();
        $fuZhou = $fuZhouList[1];
        $fuzhouIndex = array_search($fuZhou, $diPan);
        // 天盘 开始
        // 时干在地盘位置
        $hourIndex = array_search($this->jiNian['h'][0], $diPan);
        if (false === $hourIndex) {
            $this->table['tianpan'] = $diPan;
        } else {
            $this->table['tianpan'] = ['', '', '', '', '', '', '', '', ''];
            // 第四步：确定天盘：将符首“庚”加在时干“丙”在地盘的位置，查询相关资料中，所在遁局的九宫顺序，根据阳遁顺行，阴遁逆行以九宫顺序展开。
            if ($ju[0] == '阴') {
                $diPan1 = array_reverse($diPan);
                $fzIndex1 = array_search($fuZhou, $diPan1);
                foreach ($diPan1 as $k => $v) {
                    $this->table['tianpan'][$k] = $diPan1[(($fzIndex1 - $hourIndex + 9) % 9 + $k) % 9];
                }
            } else {
                foreach ($diPan as $k => $v) {
                    $this->table['tianpan'][$k] = $diPan[(($fuzhouIndex - $hourIndex + 9) % 9 + $k) % 9];
                }
            }
        }
        // 天盘 开始
        // 值使 八门
        $baMenList = ['休门', '死门', '伤门', '杜门', '中门', '开门', '惊门', '生门', '景门'];
        $this->table['zhishi'][0] = $baMenList[$fuzhouIndex];

        // 时支在地支位置
        $hourDzIndex = array_search($this->jiNian['h'][1], $this->dz);
        $xunDzIndex = array_search($fuZhouList[0][1], $this->dz);
        $diff = ($hourDzIndex - $xunDzIndex + 12) % 12;
        $this->table['bamen'] = ['', '', '', '', '', '', '', '', ''];

        foreach ($baMenList as $k => $v) {
            if ($ju[0] == '阴') {
                $this->table['bamen'][$k] = $baMenList[($k + $diff) % 9];
            } else {
                $this->table['bamen'][$k] = $baMenList[($k - $diff % 9 + 9) % 9];
            }
        }
        $zhishiIndex = array_search($this->table['zhishi'][0], $this->table['bamen']);
        $this->table['zhishi'][1] = $this->gongList[$zhishiIndex];
        $this->table['zhishi'][2] = $zhishiIndex;
        // 九星盘
        $this->jiuXingList = ['天蓬', '天芮', '天冲', '天辅', '天禽', '天心', '天柱', '天任', '天英'];
        $this->table['zhifu'][0] = $this->jiuXingList[$fuzhouIndex];
        $zhiFuIndex = array_search($fuZhou, $this->table['tianpan']);
        $this->table['zhifu'][1] = $this->gongList[$zhiFuIndex];
        $this->table['zhifu'][2] = $zhiFuIndex;
        $this->table['jiuxing'] = ['', '', '', '', '', '', '', '', ''];
        foreach ($this->jiuXingList as $k => $v) {
            $this->table['jiuxing'][$k] = $this->jiuXingList[(($fuzhouIndex - $zhiFuIndex + 9) % 9 + $k) % 9];
        }
        // 第八步：确定八神盘，直符随值符排，阳遁顺排，阴遁逆排  //符蛇阴合陈常雀地天
        $this->baShenList = [
            '阴' => ['直符', '腾蛇', '太阴', '六合', '白虎', '太常', '玄武', '九地', '九天'],
            '阳' => ['直符', '腾蛇', '太阴', '六合', '勾陈', '太常', '朱雀', '九地', '九天'],
        ];
        $baShenList = $this->baShenList[$ju[0]];
        $this->table['bashen'] = ['', '', '', '', '', '', '', '', ''];
        foreach ($baShenList as $k => $v) {
            if ($ju[0] == '阴') {
                $this->table['bashen'][$k] = $baShenList[($zhiFuIndex - $k + 9) % 9];
            } else {
                $this->table['bashen'][$k] = $baShenList[($k - $zhiFuIndex + 9) % 9];
            }
        }
        $this->table['yima'] = $this->getYiMa();
        $result = [];
        foreach ($this->tableIndex as $v) {
            $num = $v - 1;
            $result[] = [
                'top' => [
                    $this->table['bashen'][$num],
                    $this->table['jiuxing'][$num],
                    $this->table['tianpan'][$num],
                ],
                'center' => [
                    '',
                    $this->table['bamen'][$num],
                    '',
                ],
                'bottom' => [
                    $this->table['bashen'][$num],
                    $this->gongList[$num],
                    $this->table['dipan'][$num],
                ],
            ];
        }
        return $result;
    }

    /**
     * 值使八门 生门
     * @return string[]
     */
    protected function getYiMa()
    {
        // 第九步：排驿马，驿马：申时、子时、辰时，马星在艮卦八宫，寅时、午时、戌时，马星在坤卦二宫，巳时、酉时、丑时，马星在乾卦六宫，亥时、卯时、未时，马星在巽卦四宫。根据时柱地支判断驿马所在九宫宫位。
        $result = ['', '', '', '', '', '', '', '', ''];
        $hourdz = $this->jiNian['h'][1];
        if (in_array($hourdz, ['申', '子', '辰'])) {
            $result[7] = '驿马';
        } elseif (in_array($hourdz, ['寅', '午', '戌'])) {
            $result[1] = '驿马';
        } elseif (in_array($hourdz, ['巳', '酉', '丑'])) {
            $result[5] = '驿马';
        } else {
            $result[3] = '驿马';
        }
        return $result;
    }
}
