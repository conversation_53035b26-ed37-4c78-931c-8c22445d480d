<?php
// +----------------------------------------------------------------------
// | Cepaijixiong.车牌号码吉凶测试
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\plugin\Constellation;

class Cepaijixiong
{
    /**
     * @var Ex
     */
    protected Ex $Lunar;

    /**
     * 26字母
     * @var string[]
     */
    protected array $ziMu = [
        '', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    ];

    /**
     * 纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 喜神、用神、仇神、忌神、闲神 五行
     * @var array
     */
    protected array $xiYongJi = [];

    /**
     * 车牌五行 all 所有 last 尾数
     * @var array
     */
    protected array $cePaiWx = [];

    /**
     * 五行
     * @var array[]
     */
    protected array $ziWx = [
        '金' => [4, 9, 'U', 'C', 'G', 'J'],
        '木' => [3, 8, 'I', 'K', 'P', 'Y'],
        '水' => [1, 6, 'O', 'B', 'D', 'Q', 'S'],
        '火' => [2, 7, 'A', 'M', 'N', 'R', 'V', 'W', 'X'],
        '土' => [0, 5, 'E', 'F', 'H', 'L', 'T', 'Z'],
    ];

    /**
     * 车牌号码吉凶测试
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            // 性别 男为0，女为1
            'sex' => input('sex', 0, 'intval'),
            // 头个字母
            'head' => input('head', '', 'string'),
            // 号码
            'haoma' => input('haoma', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'head|首字母' => ['require', 'alpha'],
                'haoma|号码' => ['require', 'alphaNum', 'min:5', 'max:7'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->Lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->Lunar->getLunarByBetween();
        $this->jiNian = $base['jinian'];
        // 喜神、用神、忌神、仇神、闲神
        $this->xiYongJi = $this->getxiYongJi();
        $this->cePaiWx = $this->getCePaiWx($data['head'] . $data['haoma']);
        return [
            // 基础
            'lunar' => $base,
            // 天干十神
            'god' => $this->Lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->Lunar->_getGod(),
            // 纳音
            'na_yin' => $this->Lunar->getNayin(),
            // 地势
            'terrain' => $this->Lunar->getTerrain(),
            // 旺相休囚死
            'wxxqs' => $this->Lunar->getWxxqs(),
            // 日空
            'day_empty' => $this->Lunar->getEmptyDay(),
            // 喜用神
            'like_god' => $this->Lunar->getLikeGod(),
            // 日主天干五行
            'tgwx' => $this->Lunar->wuXingAttr[$base['jinian']['d'][0]],
            // 车牌五行 all 所有 last 尾数
            'cwx' => $this->cePaiWx,
            // 性格
            'xingge' => $this->getXingGe(),
            // 匹配度
            'pipei' => $this->getPipei(),
            // 事业
            'shiye' => $this->getShiYe(),
            // 社交助运
            'shejiao' => $this->getSheJiao(),
            // 偏财助运
            'caiyun' => $this->getCaiYun(),
            // 健康助运
            'health' => $this->getHealth(),
            // 特点
            'tedian' => $this->getTeDian($data['head'] . $data['haoma']),
            // 最旺你的车牌尾号
            'wangchar' => $this->getWangNum(),
            // 开运颜色
            'kaiyun' => $this->getKaiYunColor(),
            // 吉祥物
            'lucky' => $this->getLucky(),
            // 小贴士
            'tieshi' => $this->getTieshi(),
            'xiYongJi' => $this->xiYongJi,
        ];
    }

    /**
     * 生日生命灵数
     * @return int
     */
    protected function getBirthLingShu()
    {
        $time = $this->Lunar->dateTime->format('Ymd');
        return $this->getLingShu($time);
    }

    /**
     * 字母或数字字符串转灵数
     * @param string $haoma 字母数字字符串
     * @return float|int
     */
    protected function getLingShuStr($haoma)
    {
        $str = '';
        $haomaLen = strlen($haoma);
        for ($i = 0; $i < $haomaLen; $i++) {
            $tmp = strtolower($haoma[$i]);
            if (in_array($tmp, $this->ziMu)) {
                $str .= array_search($tmp, $this->ziMu);
            } else {
                $str .= $tmp;
            }
        }
        return $this->getLingShu($str);
    }

    /**
     * 递归获得灵数
     * @param string $numStr 数字字符串
     * @return float|int
     */
    protected function getLingShu($numStr)
    {
        $List = str_split($numStr);
        $sum = array_sum($List);
        if ($sum < 10) {
            return $sum;
        }
        return $this->getLingShu($sum);
    }

    /**
     * 获得五行属性
     * @param mixed $char 大写字母或数字
     * @return int|string
     */
    protected function getWx($char)
    {
        $char = strtoupper($char);
        $str = '金';
        foreach ($this->ziWx as $k => $v) {
            if (in_array($char, $v)) {
                $str = $k;
                break;
            }
        }
        return $str;
    }

    /**
     * 车牌五行
     * @param $chepai
     * @return array
     */
    protected function getCePaiWx($chepai): array
    {
        $haomaLen = strlen($chepai);
        $lastStr = $chepai[$haomaLen - 1];
        $lingShu = $this->getLingShuStr($chepai);
        return [
            'all' => $this->getWx($lingShu),
            'last' => $this->getWx($lastStr),
        ];
    }

    /**
     * 根据日柱天干地支得出对应结果
     * @return string
     */
    protected function getXingGe(): string
    {
        $list = [
            '甲子' => '你性情温和，沉稳内敛，充满同情心。在日常生活中深受人们喜爱，驾驶时也总是把安全放在首位。但有时你可能想得太多，容易陷入过度分析的困境，从而导致一些失误。',
            '甲寅' => '你是个直性子，不喜欢拐弯抹角，总是直言不讳。你的行为方式往往坦率而直接，驾驶风格也倾向于冒险和冲动。你的态度可能显得有些粗犷，对于挫折的忍受能力有待提高，因此需要学习如何更好地自我调控。',
            '甲辰' => '你的性格沉稳，从容不迫，驾驶技术也相当出色。但在实际操作中，你可能过于容易受到他人的影响，导致在驾驶时反应不够敏捷。',
            '甲午' => '你是个非常有主见的人，个性鲜明，行动力出众。但在驾驶时，你可能会有超速或强行并线的倾向，因此需要格外注意安全。',
            '甲申' => '你的内心充满反叛精神，不喜欢被规则束缚，解决问题时更倾向于直接、简单的方法。然而，驾驶时必须遵守交通规则，不能随心所欲，安全应始终放在首位。',
            '甲戌' => '你主观意识强烈，性格独特，但有时可能给人留下一种僵硬和孤寂的印象。在驾驶方面，你可以多进行一些技术性的锻炼。',
            '乙丑' => '你表面上看似从容不迫，但内心却富有策略。面对问题时，你能冷静思考，驾驶时也总是注重安全。然而，有时你可能给人一种缺乏生气的感觉。',
            '乙卯' => '你的个性坚定，有一种不屈不挠的精神，但处理事情时有时显得过于冲动，主要依赖直觉，缺乏条理性。在驾驶方面，你可能容易忽视对车辆的细致保养。',
            '乙巳' => '你个性开朗，行动力出众，喜欢主导事情的发展。但在驾驶时，你需要避免与人争斗，不要让急躁的情绪影响你，从而避免成为“路怒族”。',
            '乙未' => '你热爱冒险，追求新奇和刺激，心里藏不住话，总是直言不讳，有时显得有些孩子气。在驾驶时，你需要避免分心，集中注意力。',
            '乙酉' => '你是个感情丰富的人，内心强大，自信而果断，行动力强大，更重视结果而非过程。在驾驶时，你可能容易忽视对车辆的细节保养和检查。',
            '乙亥' => '你的个性随和，有很强的亲和力，人际关系通常都很好。但你可能有些惰性，容易满足现状，缺乏积极性。虽然你驾驶时能做到注意安全，但可能会有过多的担忧。',
            '丙子' => '你对待工作非常认真，有耐心，并且具有强烈的荣誉感。然而，你有时候过于深思熟虑，行动相对较少，导致你错过了许多机会。在驾驶方面，你需要更加灵活变通，果断行动。',
            '丙寅' => '你是个感性而细致的人，但不擅长主动表达自己，与他人保持一定距离，给人一种高冷的印象。你驾驶风格稳健，平时可以多进行一些技术练习。',
            '丙辰' => '你非常注重自己的品味和形象，举止优雅得体，具有绅士风度。你对驾驶技术的分析很到位，但在实际操作中可能不够自如。',
            '丙午' => '你个性刚毅热情，但行为上有时过于收敛，容易变得急躁和冲动，有可能成为“路怒族”的一员。因此，你需要学会适度控制自己的情绪。',
            '丙申' => '你是个善于学习的人，有强烈的自我意识，属于那种遇强则强的类型。在追求汽车个性化的同时，不要忽视安全问题。',
            '丙戌' => '你为人真诚热情，不做作，对待工作非常认真有追求。你开车时非常懂得礼让，但必要时也需要展现出强硬的一面。',
            '丁丑' => '你的个性平和，深思熟虑，看问题非常透彻，做事有条不紊，善于为自己留后路。你注重安全驾驶，处理问题周全细致，但整体上可能显得缺乏活力。',
            '丁卯' => '你是一个真诚善良的人，对自己的事务有明确的规划，但容易受周围环境的影响而改变。开车时也是如此，容易被周围的人影响。',
            '丁巳' => '你待人热情，喜欢夸大其词和炫耀自己的财富，说话缺乏连贯性，想到什么就说什么，常常让人摸不着头脑。开车时你需要保持冷静，不要过于冲动。',
            '丁未' => '你性子急躁，脾气暴烈，行事无所顾忌，属于典型的冲动型思维。你开车时表现出强烈的霸气，容易开得过快或强行抢占车道。',
            '丁酉' => '你具有强烈的独立意识和全局观，对事业充满热忱，行事目的性强。在驾驶时，你可能会因为急于赶时间而高速驾驶，但请务必注意安全。',
            '丁亥' => '你行事严谨，珍视荣誉，具有强烈的责任感。在驾驶方面，你同样注重安全问题，即使遇到突发情况也能从容应对。',
            '戊子' => '你心思细腻，性格温和，善于照顾他人，具有很好的人缘和亲和力。你特别注重汽车的装饰细节，但请不要忘记日常的保养。',
            '戊寅' => '你个性纯朴，自立能力强，但有时过于固执。在驾驶方面，你需要多加锻炼，增加处理各种突发情况的经验。',
            '戊辰' => '你细致认真，做事严谨，荣誉感强，喜欢受到表扬。在驾驶时，你懂得礼貌谦让，即使遇到突发情况也能够很好地应对。',
            '戊午' => '你性格沉稳踏实，追求舒适安逸的生活，缺乏紧迫感。在汽车方面，你注重个性化装饰的同时，也不要忘记内部的保养。',
            '戊申' => '你勇敢有谋略，行动力强，是积极践行的人。你的行事目的明确，追求长远利益。在驾驶方面，你也表现得很稳健，但请不要忘记对车辆的保养。',
            '戊戌' => '你性格纯朴厚道，善于思考，聪明且多不表达，所以看上去不易亲近。在驾驶方面，你表现得周全细致，但整体上可能显得缺乏活力。',
            '己丑' => '你性格儒雅内秀，沉稳不慌忙，面对突发事件能够冷静判断思考对策。但在驾驶时，过多的思考可能会让你分心走神，请注意安全问题。',
            '己卯' => '你内敛拘谨，行事谨慎小心，有着自己独特的见解。在驾驶方面，你需要多加注意，不要因为个人喜好而忽略了安全问题。',
            '己巳' => '你性情温和，善解人意，处事从容不迫，常怀宽容之心。然而，驾驶时务必坚决果断，该强硬时绝不手软。',
            '己未' => '你性格坚定，有勇有谋，喜欢顺畅而行，厌恶阻碍。驾驶时务必谨慎，避免超速和强行并线，确保安全。',
            '己酉' => '你天资聪颖，敏锐乐观，充满活力，善于言辞，有强烈的表达欲望。驾驶时务必沉着冷静，勿因斗气而驾车。',
            '己亥' => '你处事认真细致，稳重踏实，平和面对世事，遇事从容处理。驾驶时务必集中注意力，避免分心，确保安全。',
            '庚子' => '你精巧聪慧，善于展现自我，热爱繁华，不甘束缚。驾驶时务必遵守交通规则，确保自身与他人的安全。',
            '庚寅' => '你性格刚毅，明快敏锐，行事果敢决绝，充满威严。驾驶时务必宽容大度，懂得宁慢三分不抢一秒的道理，安全第一。',
            '庚辰' => '你个性温和内敛，沉静从容，深受周围人喜爱，处事有条不紊，驾驶稳健。务必保持专注，避免分心，确保安全。',
            '庚午' => '你喜欢规划生活，按部就班前行，珍视名誉，有强烈的荣誉感。驾驶时需要时刻关注周围环境，确保安全无虞。',
            '庚申' => '你性格独立自主，善于决断，不依赖他人，有强大的决策力，但思考不够缜密。可以通过多加练习提升驾驶技术。',
            '庚戌' => '你个性独特，不喜主动与人交往，追求舒适、独立和宁静的生活。在驾驶方面需要勤奋练习，才能掌握更高超的技巧。',
            '辛丑' => '你乐观开朗，心平气和，常怀憧憬，却缺乏行动力，易受他人影响，在驾驶时需更坚定自己的立场。',
            '辛卯' => '你直爽坦率，勇于担当，积极进取，目标明确，但驾驶时可能因心急而强行并线，务必谨慎，确保安全。',
            '辛巳' => '你性格内向，敏锐细腻，依赖情感，渴望展现自己的优点，驾驶时勿忘关注车辆细节，精心保养。',
            '辛未' => '你坚毅顽强，循规蹈矩，行事迅速果断，理性思维，判断准确，驾驶时安全可靠，但需提高反应灵敏度。',
            '辛酉' => '你性格急躁，固执己见，厌恶束缚，易引发纷争，驾驶时务必多加小心，严格遵守交通规则。',
            '辛亥' => '你聪颖文雅，风趣豁达，人际关系融洽，驾驶时需专注，心无旁骛，才能避免潜在危险。',
            '壬子' => '你性情坚忍不拔，行动力强，喜欢随心所欲，驾驶时容易忽略车辆保养，务必留心。',
            '壬寅' => '你性格外向，热爱自由，不受拘束，积极主动，善于表现自己，驾驶时需保持专注，遵守交通法规。',
            '壬辰' => '你性格刚毅，吃软不吃硬，独立自主，决策果断，驾驶时切忌浮躁斗气，以免成为“路怒族”。',
            '壬午' => '你举止端庄得体，气质出众，个性精明剔透，驾驶时避免分心杂念，确保行驶安全。',
            '壬申' => '你性情稳重，谦逊诚恳，行事从容不迫，驾车时懂得礼让，但适时展现坚定态度更有助于问题解决。',
            '壬戌' => '你谦逊知礼，沉稳可靠，具备勇往直前的毅力，驾驶时注重安全，遇事从容应对，保持冷静。',
            '癸丑' => '你有独立见解，但性格略显懒散，不习惯主动表达，驾驶风格注重安全，但需注入更多活力。',
            '癸卯' => '你勤奋好学，充满魅力，乐于付出，动手能力强，人缘佳，驾驶时需全神贯注，确保安全行驶。',
            '癸巳' => '你处事周到细致，为人随和，有追求和目标，在追求个性的同时，务必牢记安全驾驶的重要性。',
            '癸未' => '你性格正直，做事审慎专一，但过于在乎他人看法，驾驶时易受环境影响，需增强自信和果断。',
            '癸酉' => '你个性沉稳冷静，向往随性自在的生活，不喜过多思考，驾驶时需密切关注周围状况，避免与其他车辆发生冲突。',
            '癸亥' => '你坦率自信，行动力强，但有时过于逞强好胜，驾驶时务必注意安全，避免开赌气车。',
        ];
        $jiNianDay = implode('', $this->Lunar->getLunarGanzhiDay());
        return $list[$jiNianDay] ?? $list['甲子'];
    }

    /**
     * 获得匹配度
     * @return array
     */
    protected function getPipei(): array
    {
        $list = [
            ['小吉', '平时稍有裨益于你，可招一时之幸，但因根基不稳，难以长期和谐。逢凶化吉之时，唯有感叹无能为力。'],
            ['大吉', '对你的进步有助，可旺运势，纵使短暂受挫，亦可迅速复原。'],
            ['差', '对你的助力有限，难有大贡献，行事多有波折，尤其在运势下滑时，更易显困顿。'],
            ['较差', '平时对你的扶持有限，难增运势，遇难时难施巨力。'],
            ['一般', '对你的助力平平，难有大贡献，行事缺乏明确方向，效率欠佳，难见显著成果。'],
        ];
        $cwx = $this->cePaiWx['all'];
        $index = (int)array_search($cwx, $this->xiYongJi['wx']);
        return $list[$index] ?? $list[0];
    }

    /**
     * 获得喜神、用神、仇神、忌神、闲神 五行
     * @return array
     */
    protected function getxiYongJi(): array
    {
        $wangDu = BaziExt::getWangDu($this->jiNian);
        $god = $this->Lunar->getGod();
        $_god = $this->Lunar->_getGod();
        // 正官 七杀
        $numGuan = 0;
        // 食神 伤官
        $numshi = 0;
        // 正财 偏财
        $numCai = 0;
        // 正印 偏印
        $numYin = 0;
        // 比肩 劫财
        $numBi = 0;
        $godList = array_merge_recursive($god, $_god['year']['god'], $_god['month']['god'], $_god['day']['god'], $_god['hour']['god']);
        foreach ($godList as $v) {
            switch ($v) {
                case '正官':
                case '七杀':
                    $numGuan++;
                    break;
                case '食神':
                case '伤官':
                    $numshi++;
                    break;
                case '正财':
                case '偏财':
                    $numCai++;
                    break;
                case '正印':
                case '偏印':
                    $numYin++;
                    break;
                case '比肩':
                case '劫财':
                    $numBi++;
                    break;
            }
        }
        // 0身旺格  1身弱格  2从旺格  3从弱格
        $listShen = [];
        switch ($wangDu) {
            case '从弱格':
            case '身弱格':
                $array = [$numGuan, $numshi, $numCai];
                arsort($array);
                $key = key($array);
                $list = [
                    ['比劫', '印枭', '官杀', '才财', '食伤'],
                    ['比劫', '印枭', '官杀', '才财', '食伤'],
                    ['印枭', '比劫', '才财', '食伤', '官杀'],
                ];
                $listShen = $list[$key];
                break;
            case '从旺格':
            case '身旺格':
                $list = [
                    ['官杀', '才财', '比劫', '印枭', '食伤'],
                    ['才财', '食伤', '比劫', '印枭', '官杀'],
                ];
                $array = [$numYin, $numBi];
                arsort($array);
                $key = key($array);
                $listShen = $list[$key];
                break;
        }
        $list2 = [
            '食伤' => [
                '金' => '水', '木' => '火', '水' => '木', '火' => '土', '土' => '金',
            ],
            '比劫' => [
                '金' => '金', '木' => '木', '水' => '水', '火' => '火', '土' => '土',
            ],
            '才财' => [
                '金' => '木', '木' => '土', '水' => '火', '火' => '金', '土' => '水',
            ],
            '官杀' => [
                '金' => '火', '木' => '金', '水' => '土', '火' => '水', '土' => '木',
            ],
            '印枭' => [
                '金' => '土', '木' => '水', '水' => '金', '火' => '木', '土' => '火',
            ],
        ];
        $result = [];
        // 天干五行
        $tgwx = $this->Lunar->wuXingAttr[$this->jiNian['d'][0]];
        foreach ($listShen as $v) {
            $result[] = $list2[$v][$tgwx];
        }
        return [
            'wx' => $result,
            'shen' => $listShen,
        ];
    }

    /**
     * 事业助运
     * @return array
     */
    protected function getShiYe(): array
    {
        $sex = (int)$this->Lunar->sex;
        $list = [
            ['人与树相似，若想追寻更高处、阳光，其根基必须深扎于下，稳固于深处，欲得硕果，必先固其根本。', '万里之途，始于脚下；伟业非一日之功，乃需积小步以成大业。'],
            ['勤奋努力的人常常没有闲暇在他人面前夸夸其谈，而那些越是骄傲的人，却往往表现出更为谦卑的姿态。', '通往成功的道路上充满了尖刺和困难，只有忍受着痛苦，坚定地面对并克服这些挑战，我们才能最终尝到成功的滋味。'],
            ['我们助力您捍卫在职业领域的成果，确保您的心血结晶不会付诸东流。', '我们让您的辛勤耕耘得到应有的回报，避免付出与回报之间的不平衡，减少任何损失'],
            ['我们助你逐步超越自我，稳扎稳打，逐步攀升，终至成功的高峰。', '我们降低你在职业道路上的障碍，使事情进展更加流畅，让你收获满满的成果。'],
            ['我们助你在职业领域更加游刃有余，让你的行动更加流畅，实现事半功倍的效果。', '我们帮助你在前进的道路上更加顺畅，让你获得更丰硕的收获，行事更加熟练自如。'],
        ];

        $index3 = $this->getZhuYu('官杀');
        return [
            'star' => $index3 + 1,
            'detail' => $list[$index3][$sex] ?? $list[0][$sex],
        ];
    }

    /**
     * 获得助运index
     * @param string $str
     * @return integer
     */
    protected function getZhuYu(string $str): int
    {
        $index = array_search($str, $this->xiYongJi['shen']);
        $wx = $this->xiYongJi['wx'][$index];
        // 车牌五行
        $cwx = $this->cePaiWx['all'];
        $temp = [
            '金金', '金生', '木木', '木火', '水木', '水水', '火火', '火土', '土金', '土土',
        ];
        $list3 = [
            [4, 3], [4, 2], [0, 1], [0, 2], [3, 1],
        ];
        $index2 = 1;
        if (in_array($cwx . $wx, $temp)) {
            $index2 = 0;
        }
        return $list3[$index][$index2] ?? 4;
    }

    /**
     * 社交助运
     * @return array
     */
    protected function getSheJiao(): array
    {
        // 喜神、用神、仇神、忌神、闲神 五行
        $list = [
            ['常常保持微笑，与他人分享欢乐，你也会从他们那里收获快乐。', '一个品性端正、出色的朋友，既是物质上的富足，也是精神上的寄托。'],
            ['情绪具有感染力，你的愉悦将成为他人快乐的源泉。', '真正的友谊就像沙漠中的绿洲，为你带来生存的希望和前进的动力。'],
            ['友谊是一个相互了解、相互包容的过程。', '受到尊重是人类的基本需求，而良好的社交始于相互尊重。'], //忌
            ['交友之道在于平等对待、真诚待人，才能赢得他人的真诚相待。', '想要将陌生人变为亲密的朋友，首先自己必须展现出友善的态度，才能迈出第一步。'], //仇
            ['为他人尽最大努力，就是为自己尽最大努力。', '要赢得他人的喜爱，首先要去喜爱他人，主动出击才能结交更多的朋友。'], //闲
        ];
        $list2 = [4, 5, 1, 2, 3];
        $sex = (int)$this->Lunar->sex;
        // 天干五行
        $tgwx = $this->Lunar->wuXingAttr[$this->jiNian['d'][0]];
        $index = (int)(array_search($tgwx, $this->xiYongJi['wx']));
        return [
            'star' => $list2[$index] ?? $list2[0],
            'detail' => $list[$index][$sex] ?? $list[0][$sex],
        ];
    }

    /**
     * 获得偏财助运
     * @return array
     */
    protected function getCaiYun(): array
    {
        $list = [
            ['过分计较可能导致你心态失衡，从而错失更佳的机遇。', '生命中的美好惊喜，都源自于我们日常的辛勤付出，只有耕耘，才有收获。'],
            ['太重视眼过于看重眼前的小利，可能会失去长远的大利。', '我们帮助你规避不必要的开支，有效地保护你的个人财产。'],
            ['我们助你在金钱方面有所斩获，避免无谓的努力。', '我们助你更好地把握付出与回报的平衡，虽有得失，但总体不会亏损。'],
            ['我们带给你大量的机会，只要能抓住，就能有所收获。', '我们帮助你有更佳的选择，使你更为充实，收获更多。'],
            ['我们善于抓住赚钱的机遇，并且常常有意外的收获。', '我们助你提升状态，使你的思维更为清晰，行事更为顺利。'],
        ];
        $sex = (int)$this->Lunar->sex;
        $index3 = $this->getZhuYu('才财');
        return [
            'star' => $index3 + 1,
            'detail' => $list[$index3][$sex],
        ];
    }

    /**
     * 健康助运：车牌五行对日柱天干五行的生克关系得出对应关系
     * @return array
     */
    protected function getHealth(): array
    {
        $list = [
            '克' => ['确保按时休息，保持精力充沛，以预防因疲劳导致的驾驶问题。', '过度熬夜会加速衰老哦，所以确保充足的睡眠很重要。'],
            '泄' => ['减轻压力，放松心情，多与他人交流，会让你焕然一新。', '无论什么情况下，多喝热水都是一个简单且有效的方法。'],
            '耗' => ['适量补充维生素，能提升你的精神状态。', '多吃水果不仅能补充维生素，还能让你充满活力。'],
            '助' => ['适度的锻炼能增强你的体质和免疫力。', '适度的运动不仅能增强免疫力，还有抗衰老的效果哦。'],
            '生' => ['多保持充足的水分能促进身体代谢循环，使你更有活力。', '保持愉悦的心情，会让你更有青春活力。爱笑的女孩运气通常都不会太差哦。'],
        ];
        $list2 = [
            '克' => 1, '泄' => 2, '耗' => 3, '助' => 4, '生' => 5,
        ];
        $temp = [
            '金' => ['金' => '助', '木' => '克', '水' => '生', '火' => '耗', '土' => '泄'],
            '木' => ['金' => '耗', '木' => '助', '水' => '泄', '火' => '生', '土' => '克'],
            '水' => ['金' => '泄', '木' => '生', '水' => '助', '火' => '克', '土' => '耗'],
            '火' => ['金' => '克', '木' => '泄', '水' => '耗', '火' => '助', '土' => '生'],
            '土' => ['金' => '生', '木' => '耗', '水' => '克', '火' => '泄', '土' => '助'],
        ];
        // 车牌五行
        $cwx = $this->cePaiWx['all'];
        $sex = (int)$this->Lunar->sex;
        // 天干五行
        $tgwx = $this->Lunar->wuXingAttr[$this->jiNian['d'][0]];
        $index = $temp[$cwx][$tgwx];
        return [
            'star' => $list2[$index] ?? 3,
            'detail' => $list[$index][$sex],
        ];
    }

    /**
     * 特点
     * @param string $str 车牌号
     * @return array
     */
    protected function getTeDian(string $str): array
    {
        $sex = $this->Lunar->sex;
        $list = [
            '土' => ['冶金五金行业', '通讯器材行业', '制造工程行业', '金融财会行业', '证券信託行业', '首饰打造行业', '电动科技行业'],
            '水' => ['文学创作行业', '印刷出版行业', '文具书籍行业', '蔬菜果品行业', '医疗医务行业', '花卉园艺行业', '行政办公行业'],
            '金' => ['策划创作行业', '广告传媒业', '商业百货行业', '贸易商务行业', '运动体育行业', '清洁环保行业', '买卖零售行业'],
            '木' => ['石油煤气行业', '电气电工行业', '光电产品行业', '灯饰照明类行业', '食品加工行业', '衣帽经营行业', '美容美发行业'],
            '火' => ['挖掘采矿业行业', '农蓄牧业', '各种中介业', '司仪培训行业', '房产建筑业', '仓储物流行业', '工程建设行业'],
        ];
        $list2 = [
            '火' => ['机械及加工行业', '电子零器件行业', '汽車行业', '保险投资行业', '决策管理行业', '钟表机械行业', '电脑产品行业'],
            '金' => ['教育培训行业', '报纸杂志行业', '木器木材行业', '生物科技行业', '社工护理行业', '文化教育行业', '林业工程行业'],
            '土' => ['科研开发行业', '电子网络经营及商务行业', '酒业饮料行业', '运输仓储行业', '导游旅游行业', '泳池浴池行业', '博彩投机行业'],
            '水' => ['化工原料制品行业', '公关外交行业', '手工艺品行业', '照相影印行业', '餐饮行业', '人身装饰品行业', '烟花爆竹行业'],
            '木' => ['珠宝行业', '粮食经营行业', '券商投顾行业', '服务业', '律师会计行业', '建筑建材行业', '土地管理行业'],
        ];
        $list3 = [
            ['避免与前车距离过近，确保保持安全距离', '保持与前车的安全距离，切忌过近'],
            ['避免在开车时吸烟', '开车时请勿戴手套'],
            ['开车时避免使用电话', '开车时请勿打电话'],
            ['严禁酒后驾车', '酒后驾车是严格禁止的'],
            ['避免车内挂饰过多混乱', '车内挂饰应保持整洁，避免杂乱'],
            ['开车时避免佩戴过多装饰', '开车时请勿佩戴过多装饰品'],
            ['调整座位与方向盘的合适距离', '座位与方向盘应保持适当距离'],
            ['避免情绪化驾驶，不开斗气车', '冷静驾驶，不开斗气车'],
            ['在身体不适的情况下避免开车', '身体不适时，请勿驾驶车辆'],
            ['开车时请专注，避免分心观察美女', '驾驶时请勿穿高跟鞋'],
        ];
        $list4 = [
            '正南、西南', '西南、西北', '正南、正北', '正南、西南', '正南、西南', '正南、西南', '正西、东北', '正东、东南', '正南、西南', '正北、西北',
        ];
        $len = strlen($str);
        $result = [];
        $y = 0;
        $lastNum = 0;
        for ($i = 1; $i < $len; $i++) {
            $tmp = $str[$i];
            if (is_numeric($tmp)) {
                $lastNum = (int)$tmp;
            }
            $wx = $this->getWx($tmp);
            $result['wang'][] = $list[$wx][$y];
            $result['ji'][] = $list2[$wx][$y];
            $y++;
        }
        $lingShu = $this->getLingShuStr($str);
        $result['chuxing'] = $list3[$lingShu][$sex];
        $result['fangxiang'] = $list4[$lastNum];
        return $result;
    }

    /**
     * 最旺你的车牌尾号是：根据用神的五行，得出对应的结果
     * @return array
     */
    protected function getWangNum(): array
    {
        $yongWx = $this->xiYongJi['wx'][1];
        $list = [
            '金' => ['4', '9', 'E', 'O', 'Y', 'J', 'T'],
            '木' => ['3', '8', 'D', 'N', 'X', 'I', 'S'],
            '水' => ['1', '6', 'B', 'L', 'V', 'G', 'Q'],
            '火' => ['2', '7', 'C', 'M', 'W', 'H', 'R'],
            '土' => ['0', '5', 'A', 'K', 'U', 'F', 'P', 'Z'],
        ];
        return $list[$yongWx];
    }

    /**
     * 开运颜色
     * @return string
     * @throws Exception
     */
    protected function getKaiYunColor(): string
    {
        $m = (int)$this->Lunar->dateTime->format('m');
        $d = (int)$this->Lunar->dateTime->format('d');
        $xingZuo = Constellation::getInfo($m, $d);
        $list = [
            '白羊' => '红色、金黄色',
            '金牛' => '红色、玫瑰红',
            '双子' => '天蓝色、橙色',
            '巨蟹' => '白色、银色',
            '狮子' => '亮黄色、红色',
            '处女' => '白色、草绿色',
            '天秤' => '黄色、金色',
            '天蝎' => '黑色、紫红色',
            '射手' => '红色、银灰色',
            '摩羯' => '黑色、墨绿色',
            '水瓶' => '黄色、粉红色',
            '双鱼' => '蓝色、黑色',
        ];
        return $list[$xingZuo] ?? $list['白羊'];
    }

    /**
     * 开运吉祥物
     * @return array
     */
    protected function getLucky(): array
    {
        $list = [
            '坎' => ['thing' => '木质香水', 'info' => '在车内放置一瓶木质香水，不仅能改善车内空气质量，还能增进你的人缘，使你事事顺心。'],
            '坤' => ['thing' => '贴膜（防爆膜、隔热膜、太阳膜）', 'info' => '安装车窗膜不仅可以保护个人隐私、防止刺眼光线，还能在意外情况下防止玻璃飞溅，保障你的行车安全。'],
            '震' => ['thing' => '汽车脚垫', 'info' => '优质的汽车脚垫既能保持车内外的清洁，增添美观舒适的氛围，还能提升你的工作效率，助你事事顺心。'],
            '巽' => ['thing' => '汽车腰靠', 'info' => '汽车腰靠能有效缓解长时间驾驶带来的腰部疲劳和酸痛，提升你的精神状态，使你充满活力。'],
            '乾' => ['thing' => '座套', 'info' => '座椅套能够美化车内环境，保护原车座椅的干燥和清洁，防止皮革老化，从而提升你的驾驶体验。'],
            '兑' => ['thing' => '临时停车牌', 'info' => '临时停车牌是解决停车难题的利器，节省寻找车位的时间，让你有更多精力投入到其他事务中。'],
            '艮' => ['thing' => '倒车影像/车载雷达', 'info' => '倒车影像/车载雷达助你全面掌握后方路况，使倒车如同前进般轻松自如，提升你的自信心。'],
            '离' => ['thing' => '汽车防滑垫', 'info' => '汽车防滑垫确保你摆放的物品安全稳固，避免因意外情况造成人身伤害，让你的行车更加安全无忧。'],
        ];
        $guaList = ['离', '坎', '坤', '震', '巽', '坤', '乾', '兑', '艮'];
        $year = $this->Lunar->dateTime->format('Y');
        $sex = $this->Lunar->sex;
        if ($sex) {
            $guaList[5] = '艮';
        }
        $yearNum = $year % 100;
        $guaNum = 0;
        if ($year < 2000) {
            $guaNum = $sex ? (($yearNum + 9 - 4) % 9) : ((100 - $yearNum) % 9);
        } else {
            $guaNum = $sex ? ((6 + $yearNum) % 9) : ((99 - $yearNum) % 9);
        }
        $gua = $guaList[$guaNum];
        return $list[$gua] ?? $list['坎'];
    }

    /**
     * 开运小贴士
     * @return String
     */
    protected function getTieshi(): string
    {
        $list = [
            ['甲', '乙'],
            ['丙', '丁'],
            ['戊', '己'],
            ['庚', '辛'],
            ['壬', '癸'],
        ];
        $list2 = [
            '从寺庙附近汲取一桶清澈泉水，用它来冲洗车头，有助于吸引贵人而疏远小人。经常这样做，你的运势会更加旺盛。',
            '在车内脚垫上撒上海盐晶体，可以吸收负能量。在定期清理时，这些负能量也会随之被清除，同时它还具有除湿和去味的功效。',
            '整理车内杂物，调整车内物品的摆放，以改善能量的流动，这将使你的运势更加顺畅。',
            '清理车内的杂物，并在天气晴好时把车开到室外，打开车窗，让新鲜空气流通，以消除不良的能量。',
            '保持前挡风玻璃的清洁和透明，保持其整洁无瑕，这不仅会提升你的形象，还会对你的运势产生积极的影响。',
        ];
        $tg = $this->jiNian['d'][0];
        $result = '';
        foreach ($list as $k => $v) {
            if (in_array($tg, $v)) {
                $result = $list2[$k];
                break;
            }
        }
        return $result;
    }
}
