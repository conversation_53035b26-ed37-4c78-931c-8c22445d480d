<?php
// +----------------------------------------------------------------------
// | Xmcaqy.姓名测爱情运
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/7/5
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\model\baobaoqm\Cnword;
use app\validate\ValidateBase;
use calendar\exceptions\Exception;
use calendar\plugin\Constellation;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Xmcaqy
{
    /**
     * 星座
     * @var string
     */
    protected string $xingZuo = '';

    /**
     * 生肖
     * @var string
     */
    protected string $shengXiao = '';

    /**
     * 姓名测爱情运
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 姓
            'xing' => input('xing', '', 'trim'),
            // 名字
            'ming' => input('ming', '', 'trim'),
            // 出生时间
            'time' => input('time', '', 'trim'),
            // 性别
            'sex' => input('sex', 0, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓' => ['require', 'chs', 'min:1', 'max:2'],
                'ming|名' => ['require', 'chs', 'min:1', 'max:5'],
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        // 把生日转成时间戳
        $time = preg_match('/^[\d|-]\d+$/i', $data['time']) ? $data['time'] : strtotime($data['time']);
        $month = (int)date('m', $time);
        $day = (int)date('d', $time);
        $this->xingZuo = Constellation::getInfo($month, $day) . '座';
        $res = $this->getWuGe($data['xing'], $data['ming']);
        $this->shengXiao = $this->getShengxiao(date('Y', $time));
        $res['shengxiao'] = $this->shengXiao;
        $res['xingge'] = $this->getXingGe($res['renge']);
        $res['xinshang'] = $this->getXinShang($data['sex']);
        $res['airen'] = $this->getAiRen();
        $res['aiqing'] = $this->getAiqing($res['rengeWx']);
        $res['pinjian'] = $this->getPinJian($res['waigeWx'], $res['tiangeWx'], $res['digeWx']);
        $res['zhenyang'] = $this->getZhenYang($res['zongge']);
        return $res;
    }

    /**
     * 获得五行属性
     * @param int $bihua
     * @return string
     */
    private function getWuXing(int $bihua): string
    {
        $num = $bihua % 10;
        $list = [
            0 => '水', 1 => '木', 2 => '木', 3 => '火', 4 => '火', 5 => '土',
            6 => '土', 7 => '金', 8 => '金', 9 => '水',
        ];
        return $list[$num];
    }

    /**
     * 根据笔画数来获取天干
     * @param int $bihua
     * @return string
     */
    private function getTgbyBihua(int $bihua): string
    {
        $num = $bihua % 10;
        $tg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        return $tg[$num];
    }

    /**
     * 获取五格五行
     * @param string $xing 姓
     * @param string $ming 名字
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getWuGe(string $xing, string $ming): array
    {
        $data = [];
        $xingArr = $this->getZiBihua($xing);
        $nameArr = $this->getZiBihua($ming);
        $xingNum = count($xingArr);
        $mingNum = count($nameArr);
        $data['tiange'] = array_sum(array_column($xingArr, 'bihua'));
        if ($xingNum == 1) {
            $data['tiange'] = (int)$data['tiange'] + 1;
        }
        $data['renge'] = (int)$xingArr[$xingNum - 1]['bihua'] + (int)$nameArr[0]['bihua'];
        $data['dige'] = array_sum(array_column($nameArr, 'bihua'));
        if ($mingNum == 1) {
            $data['dige'] = $data['dige'] + 1;
        }
        $data['zongge'] = (int)array_sum(array_column($xingArr, 'bihua')) + array_sum(array_column($nameArr, 'bihua'));
        $data['waige'] = $data['zongge'] - $data['renge'];
        if ($xingNum > 1) {
            $data['waige'] = $data['waige'] + 1;
        }
        $data['tiangeWx'] = $this->getWuXing($data['tiange']);
        $data['tiangetg'] = $this->getTgbyBihua($data['tiange']);
        $data['rengeWx'] = $this->getWuXing($data['renge']);
        $data['digeWx'] = $this->getWuXing($data['dige']);
        $data['waigeWx'] = $this->getWuXing($data['waige']);
        $data['zonggeWx'] = $this->getWuXing($data['zongge']);
        $data['xing'] = $xingArr;
        $data['ming'] = $nameArr;
        return $data;
    }

    /**
     * 根据中文字笔画
     * @param string $str
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getZiBihua(string $str): array
    {
        $res = [];
        preg_match_all('/./u', $str, $arr);
        foreach ($arr[0] as $v) {
            $v = $this->replacestr($v);
            $tmp = Cnword::info($v);
            $res[] = [
                'zi' => $v,
                'bihua' => empty($tmp['bihua2']) ? 10 : $tmp['bihua2'],
            ];
        }
        return $res;
    }

    /**
     * 根据人格获得性格
     * @param int $renge
     * @return array
     */
    private function getXingGe(int $renge): array
    {
        $num = $renge % 10;
        $list = [
            '其性迟，缓滞不动，欠缺活动力，但易受冲动。一旦时机来临，有如山风谷雨，心潮澎湃。易失财破产，有才智，不忘功名利禄。',
            '性情多好静，富于理智。温厚中带有华丽气质，具有不屈不挠的精神。表面看似非活动家，其实蕴涵着相当实力，必取得相当的成功。其人生虽属渐进型，但终能为人首领。还可获得家庭幸福，但其人含有嫉妒心，大都好利。',
            '性情隐忍不动，意志坚定，善耐困难，表面温和，内含怒气，也有固执倾向变怪者。较好异性，猜疑心重，嫉妒心特别强，故易损害健康，切切警戒。',
            '性情急进，血气旺盛，手腕灵活，富活动力。名利心甚重，智谋才略具备，感情猛锐，有如烈火之气魄，但也有气狭者。大都富有成功，盛名一时，但也可能中途生出枝节。',
            '内具爆发性品质，表面却极平静，如火燃湿木一样，虽起浓烟而不能成火之象，往往有抑制思想不敢有虚伪，多曲折者。有病若、家属子女缘薄者，总之家庭不幸者属多。如人格为24数，则多温顺有智谋，易发财，可得权利名誉等幸运。',
            '心多洗练，容易亲近，温和沉着，有雅量，对人有同情心，荣誉心强。属于能享受家庭幸福命运。其内心有刚毅心肠，却不显于外表。有易亲近的一面，又有易疏冷的缺点，其嫉妒心稍强。',
            '其表面温和，内心刚毅，外表厚道，能得相当成功，而享受家庭幸福，富有侠义同情心。无奈人生历程漂浮不定，身多病，磨难而不能平静。一方面可以有杰出之士，一方面可以有愚笨之辈。如果不配合其他格慎重剖析，则很难判断准确。',
            '意志坚定，大都攻击性强，果敢决断，但缺乏同化力。吃苦耐劳，好争辩，遇事不会融通，多为强雄气派，追逐权势，自我意识强烈。',
            '顽固如砺石，刚毅木讷，富于持久力。爱打抱不平。若善加修养，必能光明磊落。有如黄钟，击之则响。此数不适应女人，有强顽之嫌。',
            '其活动力强，社交广，有如浩荡之波，生性好动。有智慧，富理性，大都好权利名誉，但四处奔波耗精力。',
        ];
        $wuXingAttr = ($num % 2) ? '阳' : '阴';
        $wuXingAttr .= $this->getWuXing($renge);
        return [$wuXingAttr, $list[$num]];
    }

    /**
     * 字体替换
     * @param string $data
     * @return string
     */
    private function replacestr(string $data): string
    {
        $array = [
            '㯋' => '颖',
        ];
        $res = $data;
        if (isset($array[$data])) {
            $res = $array[$data];
        }
        return $res;
    }

    /**
     * 获取欣赏的数据
     * @param int $sex 性别
     * @return array
     */
    private function getXinShang(int $sex): array
    {
        $lists = [
            '鼠' => [
                [
                    '有思想、有共鸣的才女',
                    '你永远都在寻找灵魂伴侣，有共鸣是关键点，同时ni 喜欢有个性、有见解，能表现自己意见的智慧型女孩。只要满足这两点，什么外形、家庭背景、穿着品味等，你统统都不会在意。此外，自以为是又没什么主见的女人，是最招你反感的。',
                ],
                [
                    '幽默阳光男',
                    '对你而言，和蠢笨、无趣的男人在一起是件无法忍受的事情。虽然你平时一副开朗活泼的模样，但在对待感情问题上，就会变得很顽固。要是有个阳光般的男生出现，再对你说几句有创意的笑话，立即就能开启她你紧闭的心门。',
                ],
            ],
            '牛' => [
                [
                    '有一定工作能力的贤妻良母',
                    '你的选择偏好永远是以务实为导向。你也爱美女，也喜欢身材劲爆的辣妹，但是要排序的话，你还是更看重对方的能力。在你看来，女人也许不需要聪明过人，但是一定要有头脑、识大体，顾大局。',
                ],
                [
                    '优雅绅士',
                    '你可以说是保守与害羞族的典范，因此一般的男性想要闯入你的心里很难。不过，对方若是在用餐时会先帮你拖开凳子的优雅绅士男的话，结果就不一样了，你很容易被对方体贴入微、彬彬有礼的绅士举动所倾倒。',],
            ],
            '虎' => [
                [
                    '个性独立的大女人',
                    '生性大胆的你什么都不怕，就怕两件事：女人哭、女人缠。对那些一哭二闹的小女子实在是头疼不已，也招架不了。如果遇到这种情况，你唯一想做且能做的事情就是闪人、消失。而且神经大条的你永远不会知道自己是在什么时候得罪了自己的女人，所以你只有和个性独立的女生呆在一起才觉得最省心。',
                ],
                [
                    '英勇霸气男',
                    '自由奔放的你，向往爱情但又害怕被爱情所束缚。身后有无数追逐你的男人，对你而言是很有成就感的事情，但这样的男人往往不会让你心动。而真正让你感动的戏码，却是英雄救美的俗套，这样霸气的男人在你的眼中就如同上了光环般那样耀眼。',
                ],
            ],
            '兔' => [
                [
                    '安静乖乖女',
                    '你对付女人很有一套，所以基本上要在你面前耍什么小聪明的手段，只能是让你嗤之以鼻。你占有欲强，不喜欢女友到处乱跑，甚至不喜欢自己的女人被人看。所以，你也不喜欢女朋友不听话，因此安静的乖乖女会是你最宝贝的选择。',
                ],
                [
                    '健壮型男',
                    '性感的你对于健壮的男性会有种难解的情怀。能有一个健壮的男人作为依靠，是你长期所向往的。因为这样的男人对你而言最有安全感，也最有男人味。高大强壮的身躯能为你遮风避雨，在这样男人的怀中沉睡做的梦都会是美的。',
                ],
            ],
            '龙' => [
                [
                    '温文尔雅的大众情人',
                    '你在爱情中，不管刹那间对方曾经多么吸引你，一旦对方的形象被大众否决，你会立刻与对方划清界限。你是社交能力强的人，如果你的朋友及所处的社交圈子不认可对方，基本上很快也会被你否定。相反，如果她是人人簇拥、口碑好的淑女，你自己会主动贴上来。',
                ],
                [
                    '花样美男',
                    '你在感情中算得上是“花痴”，因为你对花一样的男子特别迷恋。你本身就对美有着不懈的追求，不仅喜欢将自己打扮得漂漂亮亮，还希望自己的另一半也能够赏心悦目。帅气的男子若是微微一笑，立即能令龙女面红心跳。',
                ],
            ],
            '蛇' => [
                [
                    '自信的知性女人',
                    '对于自信的知性女人，你完全是欲罢不能。对于挑剔而事事追求完美的你来说，潜意识里其实有那么一点点的“犯贱”情节。因为你对于太容易得到的东西，完全不能勾起你的占有欲。那些让你有棋逢对手的感觉的女人，是最让你难以放手的。',
                ],
                ['博学才子',
                    '追求完美的你，选择自己的恋人时也会格外的挑剔，不过对方若是见多识广、博学多才的男人就另当别论了。你会被对方的独到见解折服，会被对方讲述的新奇故事吸引。你认为对方的才学，能够弥补你所有的不足，因而对博学才子倾心不已。',],
            ],
            '马' => [
                [
                    '真心崇拜自己',
                    '你事业心强，能努力奋斗。你虽然看起来大男人主义，但是对于女友提出的要求你都尽量满足。然而，要想长期被你宠在怀里，首先要学会把你当偶像一样捧在心头。你欣赏个性独立的女生，不过仅仅是欣赏而已，真正能拴住你的还是那个“没了你就活不了”的温顺小女人。',
                ],
                ['魅力成熟男',
                    '别看高贵、好强的你平时一副很了不起的模样，但在感情上内心其实还是相当脆弱的。你渴望能够得到情人的关心与呵护，所以成熟又充满男人味的男性，深情的对你说几句体贴的话，就会让你感动万分，爱到无法自拔。',],
            ],
            '羊' => [
                [
                    '善解人意不盲目的温婉女人',
                    '你具有相当的耐心、爱心，也有相当的包容心。所以看起来似乎你对于女人的要求不会很苛刻，但事实正好相反。骨子里保守、重家庭并且情绪敏感的你，对于善解人意的贤妻良母非常依赖，对于花钱如流水的购物狂，你是避之不及的。',
                ],
                ['纯真小男人',
                    '感情丰富且不做作的你，永远都有着一颗母性般善良的心。你最常做的事情，就是帮助人，照顾人。接触过你的异性，也很容易被你的温柔和体贴打动。不过真正能让你无怨无悔的还是那纯真又略带脆弱的男生，对方不经意流露出的忧郁、脆弱的表情便能博得你无限的怜爱。',],
            ],
            '猴' => [
                [
                    '心思机敏的百变娇娃',
                    '你讨厌一成不变，喜欢追着女人跑，喜欢不断的接受新鲜挑战。对于琢磨不透的女人，你更愿意不断的去钻研。此外，虽然超理性的女人会让你因为无从下手而头大，但缠人的女人更加会让你逃之夭夭。',
                ],
                ['可爱善良男',
                    '你不仅有着一颗永远年轻的心，而且还有着说变就变的个性，其实你也和你孩子般的纯真性格有关。喜新厌旧的你，面对其他的异性可能很快就会乏味。但对方若是可爱善良的男生，便能够牢牢的抓住你的心，因为这类的男生最容易让你产生共鸣。',],
            ],
            '鸡' => [
                [
                    '心思单纯、有气质的贤内助',
                    '你最讨厌的就是互相猜心思，对于一些心思复杂的女人你很头痛。努力务实的你很清楚自己需要一个贤内助，但不是没有要求的，不漂亮可以忍，但是一定要有气质。如果这两样都没有，那么你是不会喜欢的。',
                ],
                ['大方贵公子',
                    '你比较务实，说得不好听点就是现实。在选择爱情时，常常会不由自主的去参考对方的经济能力，其实这主要还是因为你想要过安稳的生活。所以，若是有一个贵族般的男人肯为你买下所有的梦想，你必定以身相许。',],
            ],
            '狗' => [
                [
                    '独立的可爱女人',
                    '没有女人味是很难吸引到你的，你喜欢有点嗲的女生，但是你又是最怕八婆这样的女人。如果遇到性格拖沓又爱哭的女人，你逃奔的速度绝对是极速的。所以，能拿捏好独立和温婉可爱的适中度，独立却不要强，可爱又懂事的女孩是你心头最爱。',
                ],
                ['冷面酷男',
                    '热情的你在感情上追求独立，对于那些婆婆妈妈、死缠烂打的男生，通常只会避而远之，而对那些不动声色、若即若离、酷酷的男生就犹如着魔般的痴迷。并且天生富有冒险精神的你，在感情上可谓是百折不挠，越是有挑战性的情人越是能牵引住你的心。',],
            ],
            '猪' => [
                [
                    '温柔善良、单纯的女生',
                    '你个性随意又敏感，通常喜欢个性善良单纯的女生，这种女生会让你感到很温和，很有安全感。至少能让你相信你不会受伤，你希望睁眼看到的每个人都是美好善良的。所以，斤斤计较的现实女人和太有主见、太强势的女人，都会让你觉得不自在。',
                ],
                ['浪漫多情男',
                    '你十分相信纯真的爱情，在感情上也没有太多的要求，异性的一句甜言蜜语都会让你特别的感动。如果对方如同像偶像剧中的男主角那样浪漫多情，会在无人的广场上点燃美丽的烟花，那你当场就会感动到要生死相许。',],],
        ];
        $listZishu = [
            '白羊座' => ['reqing' => [3, 56], 'langman' => [2, 33], 'kekao' => [3, 44]],
            '金牛座' => ['reqing' => [4, 72], 'langman' => [2, 24], 'kekao' => [5, 94]],
            '双子座' => ['reqing' => [3, 44], 'langman' => [4, 80], 'kekao' => [3, 43]],
            '巨蟹座' => ['reqing' => [2, 32], 'langman' => [3, 43], 'kekao' => [2, 82]],
            '狮子座' => ['reqing' => [3, 52], 'langman' => [4, 76], 'kekao' => [4, 79]],
            '处女座' => ['reqing' => [2, 34], 'langman' => [3, 49], 'kekao' => [5, 90]],
            '天秤座' => ['reqing' => [4, 66], 'langman' => [2, 32], 'kekao' => [4, 70]],
            '天蝎座' => ['reqing' => [3, 51], 'langman' => [5, 86], 'kekao' => [5, 84]],
            '射手座' => ['reqing' => [5, 89], 'langman' => [4, 70], 'kekao' => [4, 62]],
            '摩羯座' => ['reqing' => [2, 38], 'langman' => [4, 79], 'kekao' => [4, 76]],
            '水瓶座' => ['reqing' => [4, 76], 'langman' => [5, 93], 'kekao' => [2, 35]],
            '双鱼座' => ['reqing' => [4, 74], 'langman' => [3, 55], 'kekao' => [4, 67]],
        ];
        return [
            'zishu' => $listZishu[$this->xingZuo],
            'detail' => $lists[$this->shengXiao][$sex],
        ];
    }

    /**
     * 根据星座和生肖获取爱人相关数据
     * @return array
     */
    private function getAiRen(): array
    {
        $list = [
            '鼠' => 'TA好奇心非常旺盛，对一切事情无孔不入，能够很好地保守自己的秘密，但却是喜欢探听别人秘密的专家。TA使用搜集到的致命消息置人于死地而后快，并且钻别人的空子而问心无愧。总之，TA绝不会放过任何一个打听消息的机会。TA很爱管闲事，但用意多是好的。由于TA尽量不使自己的感情外露，所以当别人一旦发现TA变得易怒、无礼或鲁莽时，便知道TA正在心烦意乱。当然，也有些TA是极爱唠叨的。',
            '牛' => 'TA做事喜欢坚持固定的模式，尊重传统观念，总是精确地按照人们所期望的去做，致使人们都可以预料到TA的行动。一丝不苟的TA懂得只有按步就班地做事情，才能永远立于不败之地。TA的头脑不是杂乱无章的，别人决不会发现TA靠运气或拖泥带水地混日子。TA讲信用，一言既出，驷马难追。世俗的偏见对TA来说是无所谓的，TA会全身心地完成TA所做的工作，并厌恶半途而废。',
            '虎' => 'TA生来不知疲倦并有些鲁莽，因此通常行动很快。TA生性多疑、摇摆不定，常作出草率决定。TA很难相信其他人或平息自己的感情。TA决不把事情憋在心里。同时，又是一个诚实、柔情和慷慨的人，而且有奇妙的幽默感。TA的内心世界是浪漫的。TA爱玩、热情，感情丰富。与TA恋爱或结婚将会有很多感受，TA或她在嫉妒时会表现出过分的占有欲或爱争吵。',
            '兔' => 'TA表面上也许会对其他人的意见无动于衷，但TA实际上会在批评中一蹶不振。TA那“翻脸不开战”的技巧具有很大的欺骗性，而当专心致志的时候，会变得更加狡猾。TA对所爱的人温柔、亲切，而对其他人敷衍塞责，甚至冷酷无情。由于温文尔雅而又放纵自己，TA尽情地享受并把自己的愿望放在第一位。TA对不便利的事十分厌烦，因为TA是个羞怯考虑问题周到、并思想深遂的人，TA希望别人也这样。TA执着地相信人与人之间相互友好是件很容易的事，并且TA总是努力做到文明、有礼貌，甚至对TA的敌人也是如此。',
            '龙' => 'TA是强大的、果断的，但TA并不狡猾和诡计多端。但TA常常过于自信，被幻想所迷惑，因而对周围发生的倾覆或是密谋都无所察觉，更不能及时寻找到对策。TA非常傲慢，从不请求别人帮忙，在力量对比十分悬殊的情况下拒绝撤退。TA非常直率，从不扯谎。不管怎样，TA是一个坦率的人，别人能像看书一样来了解你。TA从不伪装自己的感情，也很少费心去尝试这一点。TA不能守口如瓶，保守秘密，甚至当TA发誓一个字也不说时，别人也能肯定在TA发怒的时候便会把秘密脱口而出，并且一字不错。',
            '蛇' => 'TA经常依靠自己的判断行事，与其他人不会进行推心置腹地交流。TA疑心非常强，但会把疑心隐藏在心中，把自己的秘密也隐藏在心中。你或许有很高的宗教造诣，或者你是个彻底的享乐主义者，但不管怎样，TA宁愿相信自己的臆想，也不愿接受别人的劝告。实际上，TA最难对付的地方就是表里不一，是在那安静的外表背后隐藏着的一颗时刻警惕的心。你从来喜怒不形于色，在TA真正开始行动之前，早已精心策划好了。所以在大多数的情况下，跟蛇的人讲话还是非常小心的。',
            '马' => 'TA性格中前后矛盾的现象产生于多变情绪。TA情感内向细微的变化常不被人注意，也就是说TA靠着自己对事物的直觉去行事。若要TA解释自己的直感及对事物进行的推理分析是不可能的。但每当一项活动处于发展阶段，TA那令人赞叹的潜在能力便会推进这项活动深入开展。TA经常一人同时从事多种活动，而且善于较好地控制局面。TA的决定一经做出，便毫不犹豫地投身于TA的事业中。人们看到这种人要么是东奔西跑地忙于事务，要么疲惫不堪地躺倒。',
            '羊' => 'TA性格忧郁，多愁善感，看问题时目光也总是忧暗的，把事情想得很糟。TA有待于人用强烈高昂的情绪去驱散TA内心的阴暗，期望周围的人给TA热情和支持。TA希望周围能有更多的朋友。TA的缺点之一是一遭困境的打击就不会轻易摆脱苦恼，别人早已忘记了TA的不幸，而TA还深深陷在痛苦之中。这种人另一个缺点是做事犹豫不决。',
            '猴' => 'TA很精明，只是用的不当，TA总想以诱惑人的手段行事，总是寻找既不花钱费力，又能捞到便宜的事去做，所以TA很难赢得人们的信任。人们对TA过分聪明的建议反而感到怀疑，怀疑TA是否有什么不纯的目的。TA有时在表面上也承认这点，但内心深处TA永远热爱自己。这样说绝不意味着TA顽固不化，只是TA比别人更善随机应变。不要为TA的举动生气、愠怒，不要说TA不可挽救，要利用TA的聪明才智，让TA发挥作用。',
            '鸡' => 'TA有不少优良品格弥补了自身的不足。TA精明强干，组织能力强，严肃认真，待人直率，遇事果断。TA对残暴的行为敢于正面指出，严厉批判。所有鸡的人都是对事物过分挑剔、追求尽善尽美的人。TA对理论性较强的问题很敏感，在处理任何问题时都要按确立的章程去落实，对那些不按规章办事的人感到不理解。鸡的人的优点还是很多的。TA会在自己力所能及的情况下尽力去帮助别人。只是TA的活力鼓动着TA太想显示自己。',
            '狗' => 'TA有这个特点，在内心里将人们按TA的观点划分等次，而且是两级划分，别人对别人们来说，或者是朋友，或者是对手，TA不相信中庸。TA同别人接触，一定要弄清别人是哪类人。但TA不会无根据地随意判断别人，即使TA对别人有点怀疑，只是对别人留点心罢了。就是性格最暴躁，最易发怒的TA，也不会毫无根据地给人做最后结论。但是，TA一旦对某人产生了自己的看法，那是很难使TA改变的。',
            '猪' => 'TA虽表面上容易受骗，但实际上还是比人们想象得要聪明。TA懂得用容忍的态度保护自己的利益。当有人骑到TA头上，TA还会再自动递上一条鞭子，当别人自鸣得意时，却早已骑虎难下，不得脱身了。这实在是TA的一条好策略。TA诚实，为自己辛勤劳作的成果而自豪。TA喜欢愉快的娱乐活动，但在情绪消极时，又极易沉沦。',
        ];
        $listZishu = [
            '白羊座' => ['zq' => [3, 53], 'ct' => [4, 71], 'tt' => [2, 36]],
            '金牛座' => ['zq' => [4, 77], 'ct' => [4, 70], 'tt' => [2, 23]],
            '双子座' => ['zq' => [3, 47], 'ct' => [5, 84], 'tt' => [4, 78]],
            '巨蟹座' => ['zq' => [4, 80], 'ct' => [5, 93], 'tt' => [4, 66]],
            '狮子座' => ['zq' => [4, 61], 'ct' => [5, 86], 'tt' => [3, 54]],
            '处女座' => ['zq' => [4, 64], 'ct' => [4, 68], 'tt' => [3, 43]],
            '天秤座' => ['zq' => [3, 45], 'ct' => [5, 90], 'tt' => [5, 91]],
            '天蝎座' => ['zq' => [5, 92], 'ct' => [5, 84], 'tt' => [4, 70]],
            '射手座' => ['zq' => [2, 28], 'ct' => [4, 62], 'tt' => [4, 62]],
            '摩羯座' => ['zq' => [3, 50], 'ct' => [3, 45], 'tt' => [2, 27]],
            '水瓶座' => ['zq' => [2, 33], 'ct' => [3, 56], 'tt' => [3, 42]],
            '双鱼座' => ['zq' => [5, 94], 'ct' => [5, 87], 'tt' => [3, 55]],
        ];
        return [
            'zishu' => $listZishu[$this->xingZuo],
            'detail' => $list[$this->shengXiao],
        ];
    }

    /**
     * 根据人格五行获得爱情运
     * @param string $rengeWx
     * @return array
     */
    private function getAiqing(string $rengeWx): array
    {
        // 根据人格五行
        $listRen = [
            '金' => [
                '由於夫妻宫灵动数呈现吉祥指数，且夫妻宫泄弱人格，代表你在爱情里，对另一半疼爱呵护有佳，凡事会站在对方立场，替他设想。',
                '尚未有对象者，虽然在寻寻觅觅的过程中感到孤单辛苦，但耐心守候之後，仍能有不错的姻缘出现。',
                '已经有对象者，你的体贴付出，经常使另一半感动窝心，因此经常给予回报，两人关系颇为和谐稳定。',
            ],
            '木' => [
                '由於夫妻宫灵动数呈现吉祥指数，且夫妻宫泄弱人格，代表你在爱情里，对另一半疼爱呵护有佳，凡事会站在对方立场，替他设想。',
                '尚未有对象者，虽然在寻寻觅觅的过程中感到孤单辛苦，但耐心守候之後，仍能有不错的姻缘出现。',
                '已经有对象者，你的体贴付出，经常使另一半感动窝心，因此经常给予回报，两人关系颇为和谐稳定。',
            ],
            '水' => [
                '由於夫妻宫灵动数呈现干扰指数，且人格克夫妻宫，代表你在爱情里通常属於强势主导的一方。',
                '尚未有对象者，在爱情方面颇为积极寻觅，但有时太过心急，反而造成反效果。',
                '已经有对象者，你在两人关系中经常扮演作主的一方，但是日子一久，你会感觉负担沉重、压力大；此外，对方也可能会受不了经常被牵着鼻子走的关系型态，而感觉厌倦，甚至作出一些让人感到惊讶的决定。',
            ],
            '火' => [
                '由於夫妻宫灵动数呈现吉祥指数，且夫妻宫克人格，代表你在爱情里通常属於比较顺从的一方。',
                '尚未有对象者，虽然透过贵人牵红线、介绍对象的机会不多，但耐心等待仍能有不错的姻缘出现。',
                '已经有对象者，与另一半的关系，通常由对方当家作主。对方有时虽然会管你、限制你，但在另一方面也提供给你相当不错的照顾呵护。',
            ],
            '土' => [
                '由於夫妻宫灵动数呈现干扰指数，且夫妻宫克人格，代表你在爱情里属於较顺从的一方。',
                '在寻觅对象的过程中，可能遭遇不少波折，令你感到失落孤单。',
                '你对待另一半往往是倾尽心力地付出，有时为了顾虑对方立场想法，而会做出某些让步牺牲日子一久就会使她备感压力，这是比较值得注意的一点。',
            ],
        ];
        return $listRen[$rengeWx];
    }

    /**
     * 获得爱情宫评鉴
     * @param string $wgwx 外格五行
     * @param string $tgwx 天格五行
     * @param string $dgwx 地格五行
     * @return array
     */
    private function getPinJian(string $wgwx, string $tgwx, string $dgwx): array
    {
        $listBozhe = [
            '白羊座' => [3, 43], '金牛座' => [4, 64], '双子座' => [3, 50], '巨蟹座' => [4, 63], '狮子座' => [2, 39], '处女座' => [4, 73],
            '天秤座' => [4, 76], '天蝎座' => [4, 75], '射手座' => [3, 53], '摩羯座' => [3, 48], '水瓶座' => [3, 44], '双鱼座' => [5, 93],
        ];
        // 根据外格五行来算贵人
        $listGuiRen = [
            '金' => [5, 83], '木' => [2, 36], '水' => [2, 34], '火' => [5, 90], '土' => [3, 60],
        ];
        // 根据天格五行来算契合
        $listQiHe = [
            '金' => [2, 36], '木' => [4, 66], '水' => [4, 62], '火' => [4, 80], '土' => [4, 76],
        ];
        // 根据地格五行
        $listD = [
            '金' => '吉/中等', '木' => '大吉/上等', '水' => '吉/中等', '火' => '吉/中等', '土' => '大吉/上等',
        ];
        return [
            'bozhe' => $listBozhe[$this->xingZuo],
            'guiren' => $listGuiRen[$wgwx],
            'qihe' => $listQiHe[$tgwx],
            'wangdu' => $listD[$dgwx],
        ];
    }

    /**
     * 根据总格获取大师箴言
     * @param int $zongge 总格数
     * @return string
     */
    private function getZhenYang($zongge): string
    {
        // 1代表甲木，2代表乙木，3代表丙火，4代表丁火，5代表戊土，6代表己土，7代表庚金，8代表辛金，9代表壬水，0代表癸水。
        $list2 = ['癸水', '甲木', '乙木', '丙火', '丁火', '戊土', '己土', '庚金', '辛金', '壬水'];
        $lists = [
            '甲木' => '这个名字的磁场，由于【爱情宫】的五行磁场为吉祥，加上【爱情宫】五行对本命元神甲木呈现生助的作用，再次给予评等：上等。这是属于为正面磁场相助的姓名！对于你的爱情运，产生的负面影响成分居少，不用另取合适偏名。',
            '乙木' => '这个名字的磁场，由于【爱情宫】的五行磁场为不理想，加上【爱情宫】五行对本命元神乙木呈现财挫的作用，再次给予评等：中等。这是属于为负面磁场干扰的姓名！对于你的爱情运，产生的负面影响成分居多，可以考虑另取合适偏名。',
            '丙火' => '这个名字的磁场，由于【爱情宫】的五行磁场为吉祥，加上【爱情宫】五行对本命元神丙火呈现生助的作用，再次给予评等：上等。这是属于为正面磁场相助的姓名！对于你的爱情运，产生的负面影响成分居少，不用另取合适偏名。',
            '丁火' => '这个名字的磁场，由于【爱情宫】的五行磁场为不理想，加上【爱情宫】五行对本命元神丁火呈现财挫的作用，再次给予评等：中等。这是属于为负面磁场干扰的姓名！对于你的爱情运，产生的负面影响成分居多，可以考虑另取合适偏名。',
            '戊土' => '这个名字的磁场，由于【爱情宫】的五行磁场为吉祥，加上【爱情宫】五行对本命元神戊土呈现生助的作用，再次给予评等：上等。这是属于为正面磁场相助的姓名！对于你的爱情运，产生的负面影响成分居少，不用另取合适偏名。',
            '己土' => '这个名字的磁场，由于【爱情宫】的五行磁场为不理想，加上【爱情宫】五行对本命元神己土呈现财挫的作用，再次给予评等：中等。这是属于为负面磁场干扰的姓名！对于你的爱情运，产生的负面影响成分居多，可以考虑另取合适偏名。',
            '庚金' => '这个名字的磁场，由于【爱情宫】的五行磁场为不理想，加上【爱情宫】五行对本命元神庚金呈现财挫的作用，再次给予评等：中等。这是属于为负面磁场干扰的姓名！对于你的爱情运，产生的负面影响成分居多，可以考虑另取合适偏名。',
            '辛金' => '这个名字的磁场，由于【爱情宫】的五行磁场为吉祥，加上【爱情宫】五行对本命元神辛金呈现生助的作用，再次给予评等：上等。这是属于为正面磁场相助的姓名！对于你的爱情运，产生的负面影响成分居少，不用另取合适偏名。',
            '壬水' => '这个名字的磁场，由于【爱情宫】的五行磁场为不理想，加上【爱情宫】五行对本命元神壬水呈现财挫的作用，再次给予评等：中等。这是属于为负面磁场干扰的姓名！对于你的爱情运，产生的负面影响成分居多，可以考虑另取合适偏名。',
            '癸水' => '这个名字的磁场，由于【爱情宫】的五行磁场为吉祥，加上【爱情宫】五行对本命元神癸水呈现生助的作用，再次给予评等：上等。这是属于为正面磁场相助的姓名！对于你的爱情运，产生的负面影响成分居少，不用另取合适偏名。',
        ];
        $str = $list2[$zongge % 10];
        return $lists[$str];
    }

    /**
     * 获取生肖
     * @param int $year 年份
     * @return string
     */
    private function getShengxiao(int $year): string
    {
        $data = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
        $index = ($year - 1900) % 12;
        return $data[$index];
    }
}
