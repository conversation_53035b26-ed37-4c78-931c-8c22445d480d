<?php
// +----------------------------------------------------------------------
// | Banjiajiri. 搬家吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\traits\DateConvertTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Banjiajiri
{
    use DateConvertTraits;

    /**
     * 日历基础
     * @var Ex
     */
    protected Ex $Lunar1;

    /**
     * @var Ex
     */
    protected Ex $Lunar2;

    /**
     * 订单时间
     * @var Ex
     */
    protected Ex $Lunar0;

    /**
     * 要筛选几个月
     * @var int
     */
    protected int $withinMonth = 3;

    /**
     * 搬家吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time1' => input('time1', '', 'trim'),
            // 出生时间2
            'time2' => input('time2', '', 'trim'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 默认3个月
            'longs' => input('longs', 3, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time1|出生时间1' => ['require', 'isDateOrTime:出生时间1'],
                'time2|出生时间2' => ['isDateOrTime:出生时间2'],
                'otime|订单时间' => ['require', 'isDateOrTime:订单时间格式不对'],
                'longs|时间区间' => ['require', 'between:1,25'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->withinMonth = $data['longs'];
        $this->Lunar1 = Ex::date($data['time1']);
        if (!empty($data['time2'])) {
            $this->Lunar2 = Ex::date($data['time2']);
        }
        $this->Lunar0 = Ex::date($data['otime']);
        $bazi = [
            $this->getBazi($this->Lunar1),
        ];
        if (!empty($this->Lunar2)) {
            $bazi[] = $this->getBazi($this->Lunar2);
        }
        $result = [
            'bazi' => $bazi,
        ];
        $result = array_merge($result, $this->getRiqi1());
        return $result;
    }

    /**
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBazi($lunar): array
    {
        if (empty($lunar)) {
            return [];
        }
        $base = $lunar->getLunarByBetween();
        $res = [
            'lunar' => $base,
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
        ];
        $listChong = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        $res['chong'] = [
            $listChong[$base['jinian']['y'][1]], $listChong[$base['jinian']['d'][1]],
        ];
        return $res;
    }

    /**
     * 1、去除日支相冲、日支相刑、岁破、清明、七月半
     * @return array
     * @throws Exception
     */
    protected function getRiqi1(): array
    {
        // 岁破
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        $listJieSha = [
            '寅亥' => '亥', '卯申' => '甲申', '辰巳' => '丁巳', '巳寅' => '庚寅', '午亥' => '辛亥', '未申' => '甲申',
            '申巳' => '巳', '酉寅' => '庚寅', '戌亥' => '辛亥', '亥申' => '甲申', '子巳' => '丁巳', '丑寅' => '庚寅',
        ];
        $listZaiSha = [
            '寅子' => ['壬子', '丙子'], '卯酉' => [], '辰午' => ['壬午'], '巳卯' => ['辛卯', '乙卯'],
            '午子' => [], '未酉' => ['己酉'], '申午' => ['戊午', '壬午'], '酉卯' => [], '戌子' => ['丙子'],
            '亥酉' => ['乙酉', '己酉'], '子午' => [], '丑卯' => ['乙卯'],
        ];
        $listYueSha = [
            '寅丑' => [], '卯戌' => ['甲戌'], '辰未' => [], '巳辰' => [],
            '午丑' => [], '未戌' => [], '申未' => [], '酉辰' => ['庚辰', '丙辰'],
            '戌丑' => [], '亥戌' => [], '子未' => [], '丑辰' => [],
        ];
        $listYueXing = [
            '寅巳' => ['丁巳', '辛巳'], '卯子' => [], '辰辰' => ['壬辰'], '巳申' => ['庚申'],
            '午午' => [], '未丑' => [], '申寅' => [], '酉酉' => ['乙酉'],
            '戌未' => ['辛未'], '亥亥' => ['乙亥', '己亥'], '子卯' => [], '丑戌' => ['庚戌'],
        ];
        $listYueYan = [
            '寅戌' => [], '卯酉' => [], '辰申' => ['壬申'], '巳未' => ['辛未', '乙未'], '午午' => ['甲午'],
            '未巳' => [], '申辰' => [], '酉卯' => [], '戌寅' => ['丙寅'], '亥丑' => ['乙丑', '己丑'],
            '子子' => ['甲子'], '丑亥' => [],
        ];
        $listDaShi = [
            '寅卯' => '卯', '卯子' => '', '辰酉' => '酉', '巳午' => '午', '午卯' => '辛卯', '未子' => '甲子', '申酉' => '酉',
            '酉午' => '庚午', '戌卯' => '卯', '亥子' => '子', '子酉' => '丁酉', '丑午' => '庚午',
        ];
        $listTianLi = [
            '寅酉' => ['丁酉', '辛酉'], '卯午' => [], '辰卯' => ['丁卯'], '巳子' => ['丙子', '庚子'], '午酉' => [],
            '未午' => ['甲午', '戊午'], '申卯' => ['癸卯', '丁卯'], '酉子' => [], '戌酉' => ['辛酉'], '亥午' => ['庚午', '甲午'],
            '子卯' => [], '丑子' => ['甲子'],
        ];
        $listWumu = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        $listRes = [
            '避刑冲克害，福德当值，搬家大吉。',
            '福聚恶消，五行合德，搬家大吉。',
            '吉星高照，宜搬家。',
            '善神护佑，宜搬家。',
            '寻常之日，不忌搬家。',
        ];
        $listShaXian = [
            '甲子' => '煞南', '乙丑' => '煞东', '丙寅' => '煞北', '丁卯' => '煞西', '戊辰' => '煞南',
            '己巳' => '煞东', '庚午' => '煞北', '辛未' => '煞西', '壬申' => '煞南', '癸酉' => '煞东',
            '甲戌' => '煞北', '乙亥' => '煞西', '丙子' => '煞南', '丁丑' => '煞东', '戊寅' => '煞北',
            '己卯' => '煞西', '庚辰' => '煞南', '辛巳' => '煞东', '壬午' => '煞北', '癸未' => '煞西',
            '甲申' => '煞南', '乙酉' => '煞东', '丙戌' => '煞北', '丁亥' => '煞西', '戊子' => '煞南',
            '己丑' => '煞东', '庚寅' => '煞北', '辛卯' => '煞西', '壬辰' => '煞南', '癸巳' => '煞东',
            '甲午' => '煞北', '乙未' => '煞西', '丙申' => '煞南', '丁酉' => '煞东', '戊戌' => '煞北',
            '己亥' => '煞西', '庚子' => '煞南', '辛丑' => '煞东', '壬寅' => '煞北', '癸卯' => '煞西',
            '甲辰' => '煞南', '乙巳' => '煞东', '丙午' => '煞北', '丁未' => '煞西', '戊申' => '煞南',
            '己酉' => '煞东', '庚戌' => '煞北', '辛亥' => '煞西', '壬子' => '煞南', '癸丑' => '煞东',
            '甲寅' => '煞北', '乙卯' => '煞西', '丙辰' => '煞南', '丁巳' => '煞东', '戊午' => '煞北',
            '己未' => '煞西', '庚申' => '煞南', '辛酉' => '煞东', '壬戌' => '煞北', '癸亥' => '煞西',
        ];
        $time = strtotime($this->Lunar0->dateTime->format('Y-m-d'));
        $limitNum = $this->getAcquisitionDays($this->Lunar0->dateTime->format('Y-m-d'), $this->withinMonth);
        $list1 = ['ping' => [], 'da' => [], 'xiao' => []];
        $listGuiJiAndWangwan = [
            // 归忌
            '寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子',
            // 往亡
            '寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑',
        ];
        for ($i = 0; $i < $limitNum; $i++) {
            $tmpTime = $time + $i * 86400;
            $huangli = Huangli::date($tmpTime);
            $base = $huangli->getLunarByBetween();
            $jiNian = $base['jinian'];
            $nongliYue = str_replace('闰', '', $base['nongli']['m']);//去除闰字
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                continue;
            }
            $jieQi = Huangli::isJieQi($tmpTime);
            $tmp = [
                'gangzhi' => $base['jinian'],
                'gongli' => [
                    date('Y年m月d日', $tmpTime),
                    Huangli::getWeekChs($tmpTime),
                ],
                'base' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'nongli' => $base['nongli'],
                'jieqi' => $jieQi ?: '',
                'res' => $listRes[4],
            ];
            $tmp['jishi'] = $this->getJishi($tmp['gangzhi']['d']);
            $res = $this->fiterDaJi($tmp);
            $tmp['res'] = $listRes[$res['res']];
            $rgz = $tmp['gangzhi']['d'][0] . $tmp['gangzhi']['d'][1];
            $tmp['sha_xian'] = $listShaXian[$rgz];
            $yueRi = $tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][1];
            $yueRi2 = $tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][0] . $tmp['gangzhi']['d'][1];
            if ($this->fiterChong($tmp['gangzhi']['d'][1])) {
                continue;
            }
            // 岁破
            if ($listSuiPo[$tmp['gangzhi']['y'][1]] == $tmp['gangzhi']['d'][1]) {
                continue;
            }
            // 彭祖忌 亥日例：所有地支为亥的日子都去掉
            if ($jiNian['d'][1] === '亥') {
                continue;
            }
            if ($tmp['jieqi'] == '清明') {
                continue;
            }
            // 往亡和归忌
            if (in_array($tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][1], $listGuiJiAndWangwan)) {
                continue;
            }
            $jianChu = $huangli->getJianChu();
            if ($jianChu == '破') {
                continue;
            }
            // 劫煞（绝地）月支+日支
            if (isset($listJieSha[$yueRi])) {
                $tmprgz = $tmp['gangzhi']['d'][0] . $tmp['gangzhi']['d'][1];
                if (in_array($yueRi, ['寅亥', '申巳'])) {
                    $tmprgz = $tmp['gangzhi']['d'][1];
                }
                if ($tmprgz == $listJieSha[$yueRi] && in_array($yueRi, ['寅亥', '辰巳', '未申', '申巳', '戌亥', '丑寅'])) {
                    $tmp['res'] = $listRes[2];
                    $list1['xiao'][] = $tmp;
                    continue;
                } elseif ($tmprgz == $listJieSha[$yueRi]) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 灾煞（正冲，凶于劫煞）
            if (isset($listZaiSha[$yueRi])) {
                if (in_array($rgz, $listZaiSha[$yueRi])) {
                    if (in_array($yueRi, ['寅子', '巳卯', '申午', '亥酉'])) {
                        $tmp['res'] = $listRes[2];
                        $list1['xiao'][] = $tmp;
                        continue;
                    }
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 月煞（尽地）
            if (isset($listYueSha[$yueRi])) {
                if (in_array($rgz, $listYueSha[$yueRi])) {
                    $tmp['res'] = $listRes[2];
                    $list1['xiao'][] = $tmp;
                    continue;
                }
                if (in_array($yueRi, ['卯戌', '酉辰'])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 月刑（月建刑伤之地）
            if (isset($listYueXing[$yueRi])) {
                if ($yueRi == '巳申' && in_array($rgz, $listYueXing[$yueRi])) {
                    $tmp['res'] = $listRes[2];
                    $list1['xiao'][] = $tmp;
                    continue;
                }
                if (in_array($rgz, $listYueXing[$yueRi])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 月厌
            if (isset($listYueYan[$yueRi])) {
                if (in_array($rgz, $listYueYan[$yueRi])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 大时（大败咸池）
            if (isset($listDaShi[$yueRi])) {
                $tmprgz = $rgz;
                if (in_array($yueRi, ['寅卯', '辰酉', '巳午', '申酉', '戌卯', '亥子'])) {
                    $tmp['res'] = $listRes[2];
                    $list1['xiao'][] = $tmp;
                    continue;
                }
                if ($tmprgz == $listDaShi[$yueRi]) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 天吏（凶于大时）
            if (isset($listTianLi[$yueRi])) {
                if (in_array($rgz, $listTianLi[$yueRi])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 五墓
            if (in_array($yueRi2, $listWumu)) {
                continue;
            }
            // 四废
            $listShiFei = [
                '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
                '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
                '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
            ];
            if (in_array($yueRi2, $listShiFei)) {
                continue;
            }
            // 平日
            if ($jianChu == '平') {
                if (in_array($yueRi2, ['卯甲午', '午辛酉', '申癸亥', '申丁亥', '酉庚子'])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 收执位
            if ($jianChu == '收') {
                if (in_array($yueRi2, ['辰丁丑', '午辛卯', '申癸亥', '子丁酉', '酉庚午'])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            // 闭执位
            if ($jianChu == '闭') {
                if (in_array($yueRi2, ['卯甲寅', '卯戊寅', '辰丁卯', '午辛巳', '未甲午', '未戊午', '酉庚申', '酉戊申', '戌辛酉', '子丁亥', '丑庚子', '丑甲子'])) {
                    $res = $this->fiterDaJi($tmp);
                    $tmp['res'] = $listRes[$res['res']];
                    $list1[$res['ji']][] = $tmp;
                    continue;
                }
                continue;
            }
            $list1[$res['ji']][] = $tmp;
        }
        return $list1;
    }

    /**
     * 日支相冲
     * @param $dz
     * @return bool
     */
    protected function fiterChong($dz): bool
    {
        // 相冲
        $listChong = ['子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', "午子", "未丑", "申寅", "酉卯", "戌辰", "亥巳"];
        // 用户地支
        $dzList = [
            $this->Lunar1->getLunarGanzhiYear()[1],
            $this->Lunar1->getLunarGanzhiDay()[1],
            empty($this->Lunar2) ? '' : $this->Lunar2->getLunarGanzhiYear()[1],
            empty($this->Lunar2) ? '' : $this->Lunar2->getLunarGanzhiDay()[1],
        ];
        $res = false;
        foreach ($dzList as $v) {
            if (in_array($dz . $v, $listChong)) {
                $res = true;
                break;
            }
        }
        return $res;
    }

    /**
     * 日支相刑
     * @param $dz
     * @return bool
     */
    protected function fiterXing($dz): bool
    {
        $xingList = [
            '丑未', '未丑', '丑戌', '戌丑', '未戌', '戌未', '子卯', '卯子',
            '辰辰', '午午', '亥亥', '酉酉', '申寅', '寅申', '申巳', '巳申', '寅巳', '巳寅',
        ];
        $rgz1 = $this->Lunar1->getLunarGanzhiDay();
        $rgz2 = empty($this->Lunar2) ? [] : $this->Lunar2->getLunarGanzhiDay();
        if (in_array($dz . $rgz1[1], $xingList)) {
            return true;
        }
        if (isset($rgz2[1]) && in_array($dz . $rgz2[1], $xingList)) {
            return true;
        }
        return false;
    }


    /**
     * 筛选出大吉 小吉 平
     * @param array $tmp 日期相关
     * @return  array
     */
    protected function fiterDaJi(array $tmp): array
    {
        $yueRi = $tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][1];
        $yueRi2 = $tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][0] . $tmp['gangzhi']['d'][1];
        // 月支日干
        $yzrg = $tmp['gangzhi']['m'][1] . $tmp['gangzhi']['d'][0];
        // 天德、月德、天月德合
        $listTianYue = [
            '寅丁', '卯申', '辰壬', '巳辛', '午亥', '未甲', '申癸', '酉寅', '戌丙', '亥乙', '子巳', '丑庚',
            '寅丙', '卯甲', '辰壬', '巳庚', '午丙', '未甲', '申壬', '酉庚', '戌丙', '亥甲', '子壬', '丑庚',
        ];
        $listTianYue1 = [
            '寅壬', '卯巳', '辰丁', '巳丙', '午寅', '未己', '申戊', '酉亥', '戌辛', '亥庚', '子申', '丑乙',
            '寅辛', '卯己', '辰丁', '巳乙', '午辛', '未己', '申丁', '酉乙', '戌辛', '亥己', '子丁', '丑乙',
        ];
        $listTianYuan = [
            '寅甲午', '卯甲戌', '辰乙酉', '巳丙子', '午丁丑', '未戊午', '申甲寅', '酉丙辰', '戌辛卯', '亥戊辰', '子甲子', '丑癸未',
        ];
        // 月恩
        $listYueEn = [
            '寅丙', '卯丁', '辰庚', '巳己', '午戊', '未辛', '申壬', '酉癸', '戌庚', '亥乙', '子甲', '丑辛',
        ];
        $listXiaoJi = [
            // 时德
            '寅午', '卯午', '辰午', '巳辰', '午辰', '未辰', '申子', '酉子', '戌子', '亥寅', '子寅', '丑寅',
            // 民日
            '寅午', '巳午', '申午', '亥酉', '卯酉', '午酉', '酉子', '子子', '辰子', '未卯', '戌卯', '丑卯',
            // 驿马
            '寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥',
            // 天马
            '寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰',
            // 开日
            '寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉', '子戌', '丑亥',
            // 成日
            '寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉',
        ];
        // 天德、月德 大吉
        if (in_array($yueRi, $listTianYue) || in_array($yzrg, $listTianYue)) {
            return ['ji' => 'da', 'res' => 0];
        }
        // 天月德合
        if (in_array($yueRi, $listTianYue1) || in_array($yzrg, $listTianYue1)) {
            return ['ji' => 'da', 'res' => 1];
        }
        $listTianShe = ['寅戊寅', '巳戊寅', '申戊寅', '亥甲午', '卯甲午', '午甲午', '酉戊申', '子戊申', '辰戊申', '未甲子', '戌甲子', '丑甲子'];//天赦
        // 四相
        $listShiXian = [
            '寅丙', '巳丙', '申丙', '亥戊', '卯戊', '午戊', '酉壬', '子壬', '辰壬', '未甲', '戌甲', '丑甲',
            '寅丁', '巳丁', '申丁', '亥己', '卯己', '午己', '酉癸', '子癸', '辰癸', '未乙', '戌乙', '丑乙',
        ];
        // 筛选天赦、月恩、四相、天愿、时德、民日、驿马、天马、成日、开日 小吉
        if (in_array($yueRi2, $listTianShe) || in_array($yueRi2, $listTianYuan) || in_array($yzrg, $listYueEn)) {
            return ['ji' => 'xiao', 'res' => 3];
        }
        if (in_array($yzrg, $listShiXian) || in_array($yueRi, $listXiaoJi)) {
            return ['ji' => 'xiao', 'res' => 3];
        }
        return ['ji' => 'ping', 'res' => 4];
    }

    /**
     * 返加吉时
     * @param array $gzDay 日干支
     * @return array
     */
    protected function getJishi(array $gzDay): array
    {
        $str = implode('', $gzDay);
        $list = [
            '甲子' => [['卯', '5:00-6:59', '入宅']],
            '乙丑' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '丙寅' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '入宅']],
            '丁卯' => [['卯', '5:00-6:59', '入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '戊辰' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '己巳' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '庚午' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '辛未' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '壬申' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '癸酉' => [['午', '11:00-12:59', '出行、入宅']],
            '甲戌' => [['巳', '9:00-10:59', '出行、入宅']],
            '乙亥' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '丙子' => [['卯', '5:00-6:59', '入宅']],
            '丁丑' => [['卯', '5:00-6:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '戊寅' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '己卯' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '庚辰' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '辛巳' => [['辰', '7:00-8:59', '入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '壬午' => [['卯', '5:00-6:59', '入宅'], ['午', '11:00-12:59', '入宅']],
            '癸未' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '甲申' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '乙酉' => [['午', '11:00-12:59', '入宅']],
            '丙戌' => [['巳', '9:00-10:59', '入宅']],
            '丁亥' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '戊子' => [['卯', '5:00-6:59', '入宅']],
            '己丑' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '庚寅' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '辛卯' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '壬辰' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '入宅']],
            '癸巳' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '甲午' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '乙未' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '丙申' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '入宅']],
            '丁酉' => [['午', '11:00-12:59', '出行、入宅']],
            '戊戌' => [['巳', '9:00-10:59', '入宅']],
            '己亥' => [['辰', '7:00-8:59', '入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '庚子' => [['卯', '5:00-6:59', '入宅']],
            '辛丑' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '壬寅' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '入宅']],
            '癸卯' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '甲辰' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '乙巳' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '丙午' => [['卯', '5:00-6:59', '入宅'], ['午', '11:00-12:59', '入宅']],
            '丁未' => [['卯', '5:00-6:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '戊申' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '己酉' => [['午', '11:00-12:59', '出行、入宅']],
            '庚戌' => [['巳', '9:00-10:59', '出行、入宅']],
            '辛亥' => [['辰', '7:00-8:59', '入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '壬子' => [['卯', '5:00-6:59', '入宅']],
            '癸丑' => [['卯', '5:00-6:59', '入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '甲寅' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '入宅']],
            '乙卯' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '丙辰' => [['辰', '7:00-8:59', '入宅'], ['巳', '9:00-10:59', '入宅']],
            '丁巳' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '出行、入宅']],
            '戊午' => [['卯', '5:00-6:59', '出行、入宅'], ['午', '11:00-12:59', '入宅']],
            '己未' => [['卯', '5:00-6:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '庚申' => [['辰', '7:00-8:59', '出行、入宅'], ['巳', '9:00-10:59', '出行、入宅']],
            '辛酉' => [['午', '11:00-12:59', '出行、入宅']],
            '壬戌' => [['巳', '9:00-10:59', '出行、入宅']],
            '癸亥' => [['辰', '7:00-8:59', '出行、入宅'], ['午', '11:00-12:59', '出行、入宅']],
        ];
        $list1 = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $list2 = [
            [
                '子' => '甲', '丑' => '乙', '寅' => '丙', '卯' => '丁', '辰' => '戊', '巳' => '己',
                '午' => '庚', '未' => '辛', '申' => '壬', '酉' => '癸', '戌' => '甲', '亥' => '乙',
            ],
            [
                '子' => '丙', '丑' => '丁', '寅' => '戊', '卯' => '己', '辰' => '庚', '巳' => '辛',
                '午' => '壬', '未' => '癸', '申' => '甲', '酉' => '乙', '戌' => '丙', '亥' => '丁',
            ],
            [
                '子' => '戊', '丑' => '己', '寅' => '庚', '卯' => '辛', '辰' => '壬', '巳' => '癸',
                '午' => '甲', '未' => '乙', '申' => '丙', '酉' => '丁', '戌' => '戊', '亥' => '己',
            ],
            [
                '子' => '庚', '丑' => '辛', '寅' => '壬', '卯' => '癸', '辰' => '甲', '巳' => '乙',
                '午' => '丙', '未' => '丁', '申' => '戊', '酉' => '己', '戌' => '庚', '亥' => '辛',
            ],
            [
                '子' => '壬', '丑' => '癸', '寅' => '甲', '卯' => '乙', '辰' => '丙', '巳' => '丁',
                '午' => '戊', '未' => '己', '申' => '庚', '酉' => '辛', '戌' => '壬', '亥' => '癸',
            ],
        ];
        $list3 = [
            '甲' => 0, '乙' => 1, '丙' => 2, '丁' => 3, '戊' => 4, '己' => 0, '庚' => 1, '辛' => 2, '壬' => 3, '癸' => 4,
        ];
        $listZhenChong = [
            '甲子' => ['庚午', '1930、1990'], '乙丑' => ['辛未', '1931、1991'], '丙寅' => ['壬申', '1932、1992'],
            '丁卯' => ['癸酉', '1933、1993'], '戊辰' => ['甲戌', '1934、1994'], '己巳' => ['乙亥', '1935、1995'],
            '庚午' => ['丙子', '1936、1996'], '辛未' => ['丁丑', '1937、1997'], '壬申' => ['戊寅', '1938、1998'],
            '癸酉' => ['己卯', '1939、1999'], '甲戌' => ['庚辰', '1940、2000'], '乙亥' => ['辛巳', '1941、2001'],
            '丙子' => ['壬午', '1942、2002'], '丁丑' => ['癸未', '1943、2003'], '戊寅' => ['甲申', '1944、2004'],
            '己卯' => ['乙酉', '1945、2005'], '庚辰' => ['丙戌', '1946、2006'], '辛巳' => ['丁亥', '1947、2007'],
            '壬午' => ['戊子', '1948、2008'], '癸未' => ['己丑', '1949、2009'], '甲申' => ['庚寅', '1950、2010'],
            '乙酉' => ['辛卯', '1951、2011'], '丙戌' => ['壬辰', '1952、2012'], '丁亥' => ['癸巳', '1953、2013'],
            '戊子' => ['甲午', '1954、2014'], '己丑' => ['乙未', '1955、2015'], '庚寅' => ['丙申', '1956、2016'],
            '辛卯' => ['丁酉', '1957、2017'], '壬辰' => ['壬寅', '1962、2022'], '癸巳' => ['己亥', '1959、2019'],
            '甲午' => ['庚子', '1960、2020'], '乙未' => ['辛丑', '1961、2021'], '丙申' => ['壬寅', '1962、2022'],
            '丁酉' => ['癸卯', '1963、2023'], '戊戌' => ['甲辰', '1964、2024'], '己亥' => ['乙巳', '1965、2025'],
            '庚子' => ['丙午', '1966、2026'], '辛丑' => ['丁未', '1967、2027'], '壬寅' => ['戊申', '1968、2028'],
            '癸卯' => ['己酉', '1969、2029'], '甲辰' => ['庚戌', '1970、2030'], '乙巳' => ['辛亥', '1971、2031'],
            '丙午' => ['壬子', '1972、2032'], '丁未' => ['癸丑', '1973、2033'], '戊申' => ['甲寅', '1974、2034'],
            '己酉' => ['乙卯', '1975、2035'], '庚戌' => ['丙辰', '1976、2036'], '辛亥' => ['丁巳', '1977、2037'],
            '壬子' => ['戊午', '1978、2038'], '癸丑' => ['己未', '1979、2039'], '甲寅' => ['庚申', '1980、2040'],
            '乙卯' => ['辛酉', '1981、2041'], '丙辰' => ['壬戌', '1982、2042'], '丁巳' => ['癸亥', '1983、2043'],
            '戊午' => ['甲子', '1924、1984'], '己未' => ['乙丑', '1925、1985'], '庚申' => ['丙寅', '1926、1986'],
            '辛酉' => ['丁卯', '1927、1987'], '壬戌' => ['戊辰', '1928、1988'], '癸亥' => ['己巳', '1929、1989'],
        ];
        $tmp = $list[$str] ?? $list['甲子'];
        $listShiTg = $list2[$list3[$gzDay[0]]];
        foreach ($tmp as $k => $v) {
            // 甲己	乙庚	丙辛	丁壬	戊癸
            $tmpshiG = $listShiTg[$v[0]] . $v[0];
            $tmp[$k][] = $listZhenChong[$tmpshiG];
            $tmp[$k][] = $list1[$v[0]] ?? $list1['子'];
        }
        return $tmp;
    }
}
