<?php
// +----------------------------------------------------------------------
// | Kaigong. 开工吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\DateConvertTraits;
use app\traits\jiri\JiRiCheckBadTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Kaigong
{
    use JiRiCheckBadTraits;
    use DateConvertTraits;

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 传入数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 节气数组
     * @var array
     */
    protected array $jq = [];

    /**
     * 开业吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            // 性别 男 0 女 1
            'sex' => input('sex', 0, 'intval'),
            // 日期范围 1-12月
            'month' => input('month', 1, 'intval'),
            // 请求日期
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'month|展示月份' => ['require', 'between:1,25'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        try {
            $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        } catch (\Exception $e) {
            return ['status' => 0, 'msg' => $e->getMessage()];
        }
        $base = $this->lunar->getLunarByBetween();
        $dayList = $this->getDayList();
        return [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 提示数据
            'nodz' => $this->getNodz(),
            // 吉日
            'ji' => $dayList,
        ];
    }

    /**
     * 提示数据
     * @return array
     */
    protected function getNodz(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $ydz = $jiNian['y'][1];
        $ddz = $jiNian['d'][1];
        $cdz1 = $this->getChongDz($ydz);
        $cdz2 = $this->getChongDz($ddz);
        $str1 = "{$cdz1}日、{$cdz2}日；因{$cdz1}{$ydz}相冲，{$cdz2}{$ddz}相冲";
        $shaArr = $this->getTianGanSiSha($ydz);
        return [
            'one' => $str1,
            'two' => "{$shaArr['no']}日{$shaArr['no']}时",
        ];
    }

    /**
     * 根据地支获得对应的地支
     * @param string $dz
     * @return string
     */
    protected function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    protected function getTianGanSiSha(string $dz): array
    {
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $result = ['ju' => ['寅', '午', '戌'], 'no' => '丑', 'sx' => '牛'];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 吉日
     * @return array
     * @throws \calendar\exceptions\Exception
     */
    protected function getDayList(): array
    {
        // 吉神
        $listJishen = [
            '天德', '月德', '天德合', '月德合', '天愿', '时德', '不将', '驿马', '月恩', '四相', '天赦',
        ];
        // 凶神
        $listXiong = [
            '劫煞', '灾煞', '月刑', '月厌', '天吏', '四废', '五墓', '小耗', '月煞', '四耗', '四穷',
        ];
        // 煞向
        $listShaXian = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $ydz = $jiNian['y'][1];
        $ddz = $jiNian['d'][1];
        $mdz = $jiNian['m'][1];
        $dzUser = [
            $jiNian['y'][1], $jiNian['d'][1],
        ];
        // 测算天数
        $limitNum = $this->getAcquisitionDays($this->orginData['otime'], $this->orginData['month']);
        $list = [];
        $listNo = [
            '7_15' => '七月半',
            '9_9' => '重阳',
            '10_1' => '寒衣',
        ];
        $explain = [];
        for ($i = 1; $i <= $limitNum; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $year = date('Y', $time);
            $keyYm = date('Y年m月', $time);
            $huangli = Huangli::date($time);
            $base1 = $huangli->getLunarByBetween();
            $jiNianTmp = $base1['jinian'];
            $ydz1 = $jiNianTmp['y'][1];
            $mdz1 = $jiNianTmp['m'][1];
            $ddz1 = $jiNianTmp['d'][1];
            $dgz1 = implode('', $jiNianTmp['d']);
            $ziRi = $huangli->getZhiRi();
            $bool = true;
            $noList = [];
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_15', '9_9', '10_1'])) {
                $noList[] = $listNo[$nongliNumberStr] ?? '';
                $bool = false;
            }
            // 岁破过滤
            if (BaziCommon::getXianChong($ydz1 . $ddz1)) {
                $bool = false;
                $noList[] = '岁破日';
            }
            // 杨公忌日
            if ($this->checkYangGongOnly($nongliNumberStr)) {
                $bool = false;
                $noList[] = '杨公忌日';
            }
            $listJq = $this->getJieQiDay($year);
            $jieQi = $listJq[$timeStr] ?? '';
            if ($jieQi === '清明') {
                $bool = false;
                $noList[] = '清明';
            }
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            // 明天
            $nextTime = $time + 86400;
            $nextDay = date('Y-m-d', $nextTime);
            $nextYear = date('Y', $nextTime);
            $listJq = $this->getJieQiDay($nextYear);
            $nextJq = $listJq[$nextDay] ?? '';
            if (in_array($nextJq, ['春分', '夏至', '秋分', '冬至'])) {
                $bool = false;
                $noList[] = '四离日';
            } elseif (in_array($nextJq, ["立春", "立夏", "立秋", "立冬"])) {
                $bool = false;
                $noList[] = '四绝日';
            }
            // 十恶大败
            $jianChu = $huangli->getJianChu();
            $jcType = 1;
            if (in_array($jianChu, ['危', '破', '执', '闭'])) {
                $jcType = 0;
            }
            // 月支+日支
            $tmpMdzDdz = $mdz1 . $ddz1;
            if ($this->checkXiaoHongSha($tmpMdzDdz)) {
                $bool = false;
                $noList[] = '小红沙';
            }
            // 事主年柱地支和日柱地支  相冲 相刑 相破
            $chongXinStr = '';
            if (BaziExt::getXianChongDz($ddz, $ddz1)) {
                $bool = false;
                $chongXinStr .= '冲';
            }
            if (BaziExt::getXianXinDz($ddz, $ddz1)) {
                $bool = false;
                $chongXinStr .= '刑';
            }
            $chongXinStr2 = '';
            if (BaziExt::getXianChongDz($ydz, $ddz1)) {
                $bool = false;
                $chongXinStr2 .= '冲';
            }
            if (BaziExt::getXianXinDz($ydz, $ddz1)) {
                $bool = false;
                $chongXinStr2 .= '刑';
            }
            $fenxi = [];
            if ($chongXinStr2) {
                $fenxi[] = ['年', $chongXinStr2];
            }
            if ($chongXinStr) {
                $fenxi[] = ['日', $chongXinStr];
            }
            $jiXiong = $huangli->getJiXiong();
            foreach ($listXiong as $v) {
                if (in_array($v, $jiXiong['xiong'])) {
                    $bool = false;
                    $noList[] = $v . '日';
                }
            }
            if (in_array('大时', $jiXiong['xiong'])) {
                $bool = false;
                $noList[] = '大时';
            }
            $shaShen = $ziRi['shen_sha'];
            $shaShen = ($shaShen === '天德') ? '宝光' : $shaShen;
            if ($ziRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            $position = $huangli->getPosition();
            $hehai = $huangli->getTodayHeHai();

            $jianChuStr = $jianChu . '日';
            if (isset($listJishen[$jianChuStr])) {
                $detailStr .= $jianChuStr . '：' . $listJishen[$jianChuStr];
            }
            unset($jiNianTmp['h']);
            $result = [
                'date' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                    // 星期
                    'week' => Huangli::getWeekChs($time),
                    'jinian' => $jiNianTmp,
                    'nongli' => $base1['nongli'],
                    '_nongli' => $base1['_nongli'],
                ],
                'sx' => $huangli->getZodiac(),
                // 财神
                'cai_shen' => $position['cai_shen'],
                // 煞向
                'sha_xian' => str_replace('煞', '', $listShaXian[$ddz1]),
                // 相冲
                'chong' => $hehai['xian_chong'],
                // 吉神
                'jishen' => $jiXiong['jishen'],
                // 凶神
                'xiong' => $jiXiong['xiong'],
                // 算法合不合，不合为空
                'type' => 'buyi',
                'type_hd' => $ziRi['huan_dao'] == '黑道' ? 0 : 1,
                // 值日
                'jianchu' => $jianChu,
                // 神煞
                'shensha' => $shaShen,
                'reason' => [],
                'fenxi' => $fenxi,
            ];
            $tmpTotal = (int)($list[$keyYm]['total'] ?? 0);
            if ($bool) {
                $reason = [];
                foreach ($listJishen as $v) {
                    if (in_array($v, $jiXiong['jishen'])) {
                        $reason[] = $v;
                    }
                }
                $hourResult = $this->getJishi($jiNianTmp['d'], $shaShen);
                $result['reason'] = $reason;
                $result['type'] = $this->getJiType($jiNianTmp);
                $result['gongsha'] = $this->huiTouGongShaLiu($jiNianTmp);
                $result['hour'] = $hourResult;
                if (!$jcType) {
                    $result['jianchu'] = '';
                }
                $tmpTotal++;
            } else {
                if ($ziRi['huan_dao'] !== '黑道') {
                    $result['shensha'] = '';
                }
                if ($jcType) {
                    $result['jianchu'] = '';
                }
                $result['reason'] = $noList;
            }
            $list[$keyYm]['title'] = $keyYm;
            $list[$keyYm]['total'] = $tmpTotal;
            $list[$keyYm]['list'][] = $result;
            foreach ($result['reason'] as $v) {
                if (isset($explain[$v])) {
                    continue;
                }
                $explain[$v] = $this->getExplain($v);
            }
            if ($result['shensha']) {
                $explain[$shaShen] = $this->getExplain($shaShen);
            }
            if ($result['jianchu']) {
                $explain[$jianChu] = $this->getExplain($jianChu);
            }
        }
        return [
            'explain' => $explain,
            'info' => array_values($list),
        ];
    }

    /**
     * 指定年份的节气 返回结果为 'Y-m-d'=>'节气名'
     * @param $year
     * @return array
     */
    protected function getJieQiDay($year): array
    {
        $jq = $this->jq;
        if (isset($jq[$year])) {
            return $jq[$year];
        }
        $list = SolarTerm::getAllJieQi($year);
        $result = [];
        foreach ($list as $k => $v) {
            $str = date('Y-m-d', strtotime($v));
            $result[$str] = $k;
        }
        $this->jq[$year] = $result;
        return $result;
    }

    /**
     * 求平日里面的吉日状态
     * @param $jiNian
     * @return string
     */
    protected function getJiType($jiNian): string
    {
        $keyStr = 'ping';
        $listPing = [
            '寅' => ['乙', '亥', '午', '辰', '戌', '子', '亥', '午', '辰', '亥'],
            '卯' => ['甲', '戌', '午', '巳', '亥', '丑', '寅', '乙', '午', '戌'],
            '辰' => ['乙', '酉', '午', '午', '子', '寅', '巳', '巳', '申', '酉'],
            '巳' => ['丙', '申', '酉', '未', '丑', '卯', '申', '未', '戌', '申'],
            '午' => ['丁', '未', '酉', '申', '寅', '辰', '亥', '酉', '子', '未'],
            '未' => ['戊', '午', '酉', '酉', '卯', '巳', '寅', '亥', '寅', '午'],
            '申' => ['己', '巳', '子', '戌', '辰', '午', '巳', '午', '辰', '巳'],
            '酉' => ['庚', '辰', '子', '亥', '巳', '未', '申', '乙', '午', '辰'],
            '戌' => ['辛', '卯', '子', '子', '午', '申', '亥', '巳', '申', '卯'],
            '亥' => ['壬', '寅', '卯', '丑', '未', '酉', '寅', '未', '戌', '寅'],
            '子' => ['癸', '丑', '卯', '寅', '申', '戌', '巳', '酉', '子', '丑'],
            '丑' => ['甲', '子', '卯', '卯', '酉', '亥', '申', '亥', '寅', '子'],
        ];
        $listDa = [
            '寅' => ['丁', '壬', '丙', '辛'],
            '卯' => ['申', '巳', '甲', '己'],
            '辰' => ['壬', '丁', '壬', '丁'],
            '巳' => ['辛', '丙', '庚', '乙'],
            '午' => ['亥', '寅', '丙', '辛'],
            '未' => ['甲', '己', '甲', '己'],
            '申' => ['癸', '戊', '壬', '丁'],
            '酉' => ['寅', '亥', '庚', '乙'],
            '戌' => ['丙', '辛', '丙', '辛'],
            '亥' => ['乙', '庚', '甲', '己'],
            '子' => ['巳', '申', '壬', '丁'],
            '丑' => ['庚', '乙', '庚', '乙'],
        ];
        if (array_intersect($jiNian['d'], $listPing[$jiNian['m'][1]])) {
            if (array_intersect($jiNian['d'], $listDa[$jiNian['m'][1]])) {
                $keyStr = 'da';
            } else {
                $keyStr = 'xiao';
            }
        }
        return $keyStr;
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return string
     */
    protected function huiTouGongShaLiu(array $jiNian): string
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $str = '无';
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $sxBase = new SxBase();
                $sx = $sxBase->getsxByDz($k);
                $str = $k . $sx;
                break;
            }
        }
        return $str;
    }

    /**
     * 吉时
     * @param array $gzDay 日干支
     * @param string $shenSha
     * @return array
     */
    protected function getJishi(array $gzDay, string $shenSha): array
    {
        $dzList = Calendar::DI_ZHI;
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];

        $jn = $this->lunar->getLunarGanzhiYear();
        $ydz1 = $jn[1];

        $result = [];
        $sxBase = new SxBase();
        foreach ($dzList as $v) {
            if (in_array($v, ['子', '丑', '寅'])) {
                continue;
            }
            if (in_array($v, ['子', '丑', '寅', '卯', '戌', '亥']) && $shenSha == '司命') {
                continue;
            }
            $hgz = Huangli::getGanzhiHour($gzDay[0], $v);
            $sx = $sxBase->getsxByDz($v);
            $chongSx = $sxBase->getChong($sx);
            $str = $v . $gzDay[1];
            $str2 = $v . $ydz1;
            if (BaziCommon::getXianChong($str) || BaziCommon::getXianXin($str) || BaziCommon::getXianHai($str)) {
                continue;
            }
            if (BaziCommon::getXianChong($str2) || BaziCommon::getXianPo($str2) || BaziCommon::getXianHai($str2)) {
                continue;
            }
            $sanSha = Huangli::getSanSha($v);
            $position = Huangli::getPositionbyTg($hgz[0]);
            $result[] = [
                'dz' => $v,
                'h' => $hourList[$v],
                'sha' => $sanSha[0],
                'cai_shen' => $position['cai_shen'],
                'chong' => $chongSx['name'],
            ];
        }
        return array_slice($result, 0, 3);
    }

    /**
     * 解释部份
     * @param $str
     * @return string
     */
    protected function getExplain($str)
    {
        $list = [
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '传星' => '三皇吉星之一，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '曲星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜开、开业诸事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，这天适合开始一段新的生活。',
            '四相' => '拥有四时王相的贵气，有吉神庇佑，利于开工。',
            '时德' => '得到天地舒畅之气，为四时所生，适宜祈福的日子。',
            '不将' => '有利移动，修造的传统吉日，当日动工，寓意顺顺利利，心想事成。',
            '驿马' => '驿马主奔波、走动、外出、旅行、出差、入宅、开工等吉祥之事。',
            '青龙' => '该日行动的价值大于计划，有付出就有收获，适合各种行业。',
            '明堂' => '该日遇到贵人几率较高，有机会得到贵人的帮助，适合各种行业。',
            '金匮' => '金匮属于福德星，乃是黄道日，该日有利于入宅、开张等诸喜事。',
            '宝光' => '该日大利开工，移动，所做的事情容易成功。',
            '玉堂' => '该日对于求财有利，而且有受到贵人关注的机会，适合各种行业。',
            '司命' => '该日对于白天营业的行业，开张大吉，夜间营业行业慎用。',
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定，寓意吉祥，该日可选。',
            '满' => '满日在吉日中有“圆满”的含义，对于开工来说，自然有着比较好的寓意。',
            '建' => '对于事情上来说，该日有着起始的说法，寓意较好。',
            '成' => '凡事有所成，因而该日诸事皆可办理。',
            '开' => '寓意着顺利开始，乃是一个好日子，开工可选。',
            '平' => '平常、平分的日子，无吉无凶，开工可选。',
            '收' => '有收成，收获的意思，该日开工大吉。',
            '危' => '危日象征危机，是危险的一天，忌讳诸多喜事。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日有指破裂，冲破的含义，忌办一切喜凶事。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进的意思。这一日有小破耗的说法，需规避。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克，该日万事皆忌。',
            '朱雀' => '日天讼星，利用公事，常人凶，诸事忌用，谨防远行动作。',
            '白虎' => '白虎是天杀星，为凶恶之神，最好规避该日。',
            '天牢' => '镇神星，阴人用事皆吉，其余都不利。',
            '玄武' => '天狱星，多有小人嘴舌之兆，寓意不吉。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满。',
            '劫煞日' => '劫煞为打劫之星，主路于不安，土匪劫道，有红伤血光之灾。',
            '灾煞日' => '灾煞为灾星，主牢狱凶灾，健康受损，六畜不安，寓意不祥。',
            '岁煞日' => '岁煞是当头太岁，太岁头上动土，招惹是非口舌，官司牢狱破财之事。',
            '月刑日' => '该日有刑伤之意，而且对于一些喜事上并不太适合，因规避该日。',
            '月厌日' => '该日又称大祸日，忌讳诸事。',
            '大时' => '该日表示精光、消减，在运势上容易影响到一个人的心境。',
            '天吏日' => '天吏的解释为天子的官吏，有奉天命而治人罪的寓意。',
            '四废日' => '四废有五行无气，福德不临之日的意思。',
            '五墓日' => '五墓日是一种忌日，诸事不宜。',
            '归忌日' => '该日诸事不顺，寓意不好，需规避。',
            '往亡日' => '古话有云，往亡煞临世，动必有险厄，需十分谨慎。',
            '岁破日' => '岁破有大事勿用的寓意，该日需规避。',
            '月煞日' => '该星的本质有灾厄，疾病的说法，寓意颇为不吉。',
            '小耗日' => '小耗有损失钱财，遗失，购贵物的说法，需注意避开。',
            '天贼日' => '天贼是专门行盗窃的凶神，遇之恐破财。',
            '四耗日' => '四耗有漏财破财运，财运不稳定，做事不吉，难有所成的说法。',
            '四穷日' => '四穷有财运波动，多起伏不稳的说法，最好避开。',
            '五离日' => '日值四离，多有不吉，大事勿用。',
            '清明' => '这里通常都是祭奠先祖，缅怀已故之人，最好规避。',
            '七月半' => '七月半在传统文化中属于鬼节(又称中元节)，选择该日有些不吉。',
            '重阳' => '当日有大事勿用的说法，开工最好避开。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '小红沙' => '红沙日有诸事不宜的说法，需要注意避开。',
            '杨公忌日' => '这日诸事不宜，大事勿用的说法，最好避开。',
            '四绝日' => '古话有云，四绝日诸事不宜，喜事勿用。',
            '四离日' => '日值四离，大事勿用。',
        ];
        return $list[$str] ?? '';
    }
}
