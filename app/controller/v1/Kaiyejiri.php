<?php
// +----------------------------------------------------------------------
// | Kaiyejiri. 开业吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\traits\DateConvertTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Kaiyejiri
{
    use DateConvertTraits;

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * @var string[]
     */
    protected array $chongList = [
        '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
        '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
    ];

    /**
     * @var array
     */
    protected array $wuXingAttr = [];

    /**
     * @var array
     */
    protected array $data = [];

    /**
     * 开业吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            'time' => input('time', '', 'trim'),
            // 性别 男 0 女 1
            'sex' => input('sex', 0, 'intval'),
            // 日期范围 1-12月
            'month' => input('month', 1, 'intval'),
            // 请求日期
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'month|展示月份' => ['require', 'between:1,25'],
                'otime|测试日期' => ['require', 'dateFormat:Y-m-d'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->data = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        $nodz = [
            $this->chongList[$base['jinian']['y'][1]], $this->chongList[$base['jinian']['d'][1]],
        ];
        $dayList = $this->getDayList();
        return [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 避开的地支
            'nodz' => $nodz,
            // 吉日
            'ji' => $dayList,
        ];
    }

    /**
     * 吉日
     * @return array
     * @throws Exception
     */
    protected function getDayList(): array
    {
        // 吉时
        $listJiShi = [
            '甲子' => ['酉'], '乙丑' => ['卯', '申'], '丙寅' => ['未'],
            '丁卯' => ['午', '未'], '戊辰' => ['巳', '申'], '己巳' => ['辰', '午'],
            '庚午' => ['卯', '申'], '辛未' => ['申'], '壬申' => ['辰', '巳'],
            '癸酉' => ['午'], '甲戌' => ['巳', '申'], '乙亥' => ['辰', '午'],
            '丙子' => ['酉'],
            '丁丑' => ['巳', '申'],
            '戊寅' => ['未'],
            '己卯' => ['卯', '午'],
            '庚辰' => ['巳', '申'],
            '辛巳' => ['辰', '午'],
            '壬午' => ['卯', '酉'],
            '癸未' => ['巳', '申'],
            '甲申' => ['辰', '巳'],
            '乙酉' => ['午', '未'],
            '丙戌' => ['巳', '申'],
            '丁亥' => ['辰', '午'],
            '戊子' => ['酉'],
            '己丑' => ['卯', '巳'],
            '庚寅' => ['未'],
            '辛卯' => ['卯', '午'],
            '壬辰' => ['巳', '酉'],
            '癸巳' => ['辰', '午'],
            '甲午' => ['卯', '申'],
            '乙未' => ['申'],
            '丙申' => ['巳'],
            '丁酉' => ['午', '未'],
            '戊戌' => ['巳', '申'],
            '己亥' => ['辰', '午'],
            '庚子' => ['酉'],
            '辛丑' => ['卯', '巳'],
            '壬寅' => ['未'],
            '癸卯' => ['卯', '午'],
            '甲辰' => ['巳', '酉'],
            '乙巳' => ['辰', '午'],
            '丙午' => ['卯', '申'],
            '丁未' => ['巳', '申'],
            '戊申' => ['辰', '巳'],
            '己酉' => ['午', '未'],
            '庚戌' => ['巳', '申'],
            '辛亥' => ['辰', '午'],
            '壬子' => ['辰', '酉'],
            '癸丑' => ['卯', '巳'],
            '甲寅' => ['未'],
            '乙卯' => ['卯', '午'],
            '丙辰' => ['巳', '酉'],
            '丁巳' => ['辰', '午'],
            '戊午' => ['卯', '酉'],
            '己未' => ['巳', '申'],
            '庚申' => ['辰', '巳'],
            '辛酉' => ['午', '未'],
            '壬戌' => ['巳'],
            '癸亥' => ['辰', '午'],
        ];
        // 岁破 年+日
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        // 杨公忌日（农历）
        $listYangji = [
            '1-13', '2-21', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19',
        ];
        // 其他凶日 月支+日子
        $listOther = [
            '卯乙亥', '卯乙未', '辰壬子', '辰庚申', '辰戊辰', '巳乙卯', '巳壬子', '午丙戌',
            '未乙卯', '未癸未亥', '未丁亥', '申戊午', '申乙卯', '申辛亥', '酉辛丑', '酉辛亥',
            '戌戊午', '戌辛亥', '亥辛酉', '亥丙午', '亥癸亥', '丑丁巳', '丑癸亥', '丑戊辰',
        ];
        // 月支+日支 月破（破日）	收日	闭日	小耗	小流财	受死
        $listPo = [
            // 月破（破日）
            '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未',
            // 收日
            '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申', '子酉', '丑戌',
            // 闭日
            '寅丑', '卯寅', '辰卯', '巳辰', '午巳', '未午', '申未', '酉申', '戌酉', '亥戌', '子亥', '丑子',
            // 小耗
            '寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午',
            // 小流财
            '寅亥', '卯申', '辰巳', '巳寅', '午卯', '未午', '申子', '酉酉', '戌丑', '亥未', '子辰', '丑戌',
            // 受死
            '寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉',
        ];
        // 煞向
        $listShaXian = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        // 黄道日黑道日描述
        $listDetail = [
            '青龙' => '青龙黄道日，该日行动的价值大于计划，有付出就有收获，适合各种行业。',
            '明堂' => '明堂黄道日，该日遭遇贵人几率较高，有机会得到贵人的帮助，适合各种行业。',
            '金匮' => '金匮黄道日，该日有利于开业祈福，尤其适合宗教文化行业、女性用品行业。',
            '天德' => '天德黄道日，该日大利出行，所做的事情容易成功，可视行业特点按需取用。',
            '玉堂' => '玉堂黄道日，该日对于求财有利，而且有受到贵人关注的机会，适合各种行业。',
            '司命' => '司命黄道日，该日对于白天营业的行业，开张大吉，夜间营业行业慎用。',
            '天刑' => '该日不忌开业，旅游、运输等行业可做权宜备选。',
            '朱雀' => '该日不忌开业，印刷、金属等行业可做权宜之用。',
            '白虎' => '该日不忌开业，宗教行业、餐饮业可做权宜之用。',
            '天牢' => '该日不忌开业，但最好为小店铺或小本生意开业。',
            '元武' => '该日不忌开业，餐饮业、服务业可选，偏门生意慎选。',
            '勾陈' => '该日不忌开业，印刷、餐饮等行业可做权宜之用。',
        ];
        // 吉神
        $listJishen = [
            '天德' => '有逢凶化吉的寓意，易获得贵人帮助。',
            '月德' => '有趋吉避凶的寓意，有机会遇到大福德。',
            '天德合' => '有远离盗贼和灾祸的寓意，逢凶化吉。',
            '月德合' => '有远离盗贼和灾祸的寓意，逢凶化吉。',
            '满日' => '有钱财满载而归，圆满的寓意。',
            '成日' => '有吉星高照，贵人指引的寓意。',
            '开日' => '有大吉大利，马到成功的寓意。',
            '天愿' => '有和、善的寓意，避诸多凶险。',
            '民日' => '有顺势而为，遵循天道的寓意。',
            '五富' => '有生活富足，生意昌盛的寓意。',
            '月财' => '有获得偏财，财源滚滚的寓意。',
            '天财' => '有受到财神眷顾，利市的寓意。',
            '六合' => '有喜庆寓意，有助于增加人缘。',
        ];
        // 宜 1.1基础数据调整为日历中宜开市+宜立券交易+宜纳财的日子
        $listYi = [
            '开市', '交易', '求财', '立契', '买卖', '纳财', '立券交易',
        ];
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $dzUser = [
            $jiNian['y'][1], $jiNian['d'][1],
        ];
        // 测算天数
        $limitNum = $this->getAcquisitionDays($this->data['otime'], $this->data['month']);
        $list = [
            'ping' => [],
            'xiao' => [],
            'da' => [],
        ];
        for ($i = 1; $i <= $limitNum; $i++) {
            $time = strtotime("{$this->data['otime']} +{$i} day");
            $timeStr = date('Y-m-d', $time);
            $huangli = Huangli::date($time);
            $base1 = $huangli->getLunarByBetween();
            $jiNianTmp = $base1['jinian'];
            $ziRi = $huangli->getZhiRi();
            if ($ziRi['huan_dao'] == '黑道') {
                continue;
            }
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            // 岁破过滤
            if ($listSuiPo[$jiNianTmp['y'][1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            // 事主地支(年日)+日支
            if ($listSuiPo[$dzUser[0]] == $jiNianTmp['d'][1] || $listSuiPo[$dzUser[1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            // 杨公忌日
            if (in_array($base1['_nongli']['m'] . '-' . $base1['_nongli']['d'], $listYangji)) {
                continue;
            }
            $shiLiAndJ = $this->getJieQiDay(date('Y', $time));
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            if (in_array($timeStr, $shiLiAndJ)) {
                continue;
            }
            // 其他凶日 月+日子
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][0] . $jiNianTmp['d'][1], $listOther)) {
                continue;
            }
            // 月支+日支 月破（破日）	收日	闭日	小耗	小流财	受死
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][1], $listPo)) {
                continue;
            }

            $jiXiong = $huangli->getJiXiong();
            if (!array_intersect($jiXiong['yi'], $listYi)) {
                continue;
            }

            // 黑道凶日
            //            $tmpType = 1;
            //            if (in_array($ziRi['shen_sha'], ['天刑', '朱雀', '白虎', '天牢', '玄武', '勾陈'])) {
            //                $tmpType = 0;
            //            }
            $position = $huangli->getPosition();
            $gzDay = implode('', $jiNianTmp['d']);
            $hehai = $huangli->getTodayHeHai();
            $detailStr = $jiNianTmp['d'][0] . $this->wuXingAttr[$jiNianTmp['d'][0]] . '在' . $jiNianTmp['m'][1] . '月。';
            $detailStr .= $listDetail[$ziRi['shen_sha']] ?? $listDetail['青龙'];
            $detailStr .= $this->isLuShen($jiNianTmp) ? '坐禄，' : '';
            $detailStr .= $this->isWeican($jiNianTmp) ? '坐文昌。' : '';
            $jiXiongYiJi = $huangli->getJiXiong();
            foreach ($listJishen as $key => $value) {
                if (in_array($key, $jiXiongYiJi['jishen'])) {
                    $detailStr .= '所值吉神，' . $value;
                    break;
                }
            }
            $jiHour = $listJiShi[$gzDay];

            $tmpOne = [
                'date' => [
                    'y' => date('Y', $time),
                    'm' => date('m', $time),
                    'd' => date('d', $time),
                    // 星期
                    'week' => Huangli::getWeekChs($time),
                    'jinian' => $jiNianTmp,
                    'nongli' => $base1['nongli'],
                    '_nongli' => $base1['_nongli'],
                ],
                // 吉时
                'hour' => $this->getHourInfo($jiNianTmp['d'], $jiHour),
                // 财神
                'cai_shen' => $position['cai_shen'],
                // 煞向
                'sha_xian' => $listShaXian[$jiNianTmp['d'][1]],
                // 相冲
                'chong' => $hehai['xian_chong'],
                // 描述
                'detail' => $detailStr,
            ];
            $list[$this->getJiType($jiNianTmp)][] = $tmpOne;
        }
        return $list;
    }

    /**
     * 求四离 四绝 公历
     * 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
     * @param $year
     * @return array
     */
    protected function getJieQiDay($year): array
    {
        $list = [
            '春分', '夏至', '秋分', '冬至', '立春', '立夏', '立秋', '立冬',
        ];
        $jieqi = SolarTerm::getAllJieQi($year);
        $day = [];
        foreach ($jieqi as $k => $v) {
            if (in_array($k, $list)) {
                $day[] = date('Y-m-d', (strtotime($v) - 86400));
            }
        }
        return $day;
    }

    /**
     * 求平日里面的吉日状态
     * @param $jiNian
     * @return string
     */
    protected function getJiType($jiNian): string
    {
        $keyStr = 'ping';
        $listPing = [
            '寅' => ['乙', '亥', '午', '辰', '戌', '子', '亥', '午', '辰', '亥'],
            '卯' => ['甲', '戌', '午', '巳', '亥', '丑', '寅', '乙', '午', '戌'],
            '辰' => ['乙', '酉', '午', '午', '子', '寅', '巳', '巳', '申', '酉'],
            '巳' => ['丙', '申', '酉', '未', '丑', '卯', '申', '未', '戌', '申'],
            '午' => ['丁', '未', '酉', '申', '寅', '辰', '亥', '酉', '子', '未'],
            '未' => ['戊', '午', '酉', '酉', '卯', '巳', '寅', '亥', '寅', '午'],
            '申' => ['己', '巳', '子', '戌', '辰', '午', '巳', '午', '辰', '巳'],
            '酉' => ['庚', '辰', '子', '亥', '巳', '未', '申', '乙', '午', '辰'],
            '戌' => ['辛', '卯', '子', '子', '午', '申', '亥', '巳', '申', '卯'],
            '亥' => ['壬', '寅', '卯', '丑', '未', '酉', '寅', '未', '戌', '寅'],
            '子' => ['癸', '丑', '卯', '寅', '申', '戌', '巳', '酉', '子', '丑'],
            '丑' => ['甲', '子', '卯', '卯', '酉', '亥', '申', '亥', '寅', '子'],
        ];
        $listDa = [
            '寅' => ['丁', '壬', '丙', '辛'],
            '卯' => ['申', '巳', '甲', '己'],
            '辰' => ['壬', '丁', '壬', '丁'],
            '巳' => ['辛', '丙', '庚', '乙'],
            '午' => ['亥', '寅', '丙', '辛'],
            '未' => ['甲', '己', '甲', '己'],
            '申' => ['癸', '戊', '壬', '丁'],
            '酉' => ['寅', '亥', '庚', '乙'],
            '戌' => ['丙', '辛', '丙', '辛'],
            '亥' => ['乙', '庚', '甲', '己'],
            '子' => ['巳', '申', '壬', '丁'],
            '丑' => ['庚', '乙', '庚', '乙'],
        ];
        if (array_intersect($jiNian['d'], $listPing[$jiNian['m'][1]])) {
            if (array_intersect($jiNian['d'], $listDa[$jiNian['m'][1]])) {
                $keyStr = 'da';
            } else {
                $keyStr = 'xiao';
            }
        }
        return $keyStr;
    }

    /**
     * 获得正冲
     * @param $dayGz
     * @return array
     */
    protected function getZhengChong($dayGz): array
    {
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        $list1 = [
            '甲子' => '1924、1984', '乙丑' => '1925、1985', '丙寅' => '1926、1986', '丁卯' => '1927、1987',
            '戊辰' => '1928、1988', '己巳' => '1929、1989', '庚午' => '1930、1990', '辛未' => '1931、1991',
            '壬申' => '1932、1992', '癸酉' => '1933、1993', '甲戌' => '1934、1994', '乙亥' => '1935、1995',
            '丙子' => '1936、1996', '丁丑' => '1937、1997', '戊寅' => '1938、1998', '己卯' => '1939、1999',
            '庚辰' => '1940、2000', '辛巳' => '1941、2001', '壬午' => '1942、2002', '癸未' => '1943、2003',
            '甲申' => '1944、2004', '乙酉' => '1945、2005', '丙戌' => '1946、2006', '丁亥' => '1947、2007',
            '戊子' => '1948、2008', '己丑' => '1949、2009', '庚寅' => '1950、2010', '辛卯' => '1951、2011',
            '壬辰' => '1952、2012', '癸巳' => '1953、2013', '甲午' => '1954、2014', '乙未' => '1955、2015',
            '丙申' => '1956、2016', '丁酉' => '1957、2017', '戊戌' => '1958、2018', '己亥' => '1959、2019',
            '庚子' => '1960、2020', '辛丑' => '1961、2021', '壬寅' => '1962、2022', '癸卯' => '1963、2023',
            '甲辰' => '1964、2024', '乙巳' => '1965、2025', '丙午' => '1966、2026', '丁未' => '1967、2027',
            '戊申' => '1968、2028', '己酉' => '1969、2029', '庚戌' => '1970、2030', '辛亥' => '1971、2031',
            '壬子' => '1972、2032', '癸丑' => '1973、2033', '甲寅' => '1974、2034', '乙卯' => '1975、2035',
            '丙辰' => '1976、2036', '丁巳' => '1977、2037', '戊午' => '1978、2038', '己未' => '1979、2039',
            '庚申' => '1980、2040', '辛酉' => '1981、2041', '壬戌' => '1982、2042', '癸亥' => '1983、2043',
        ];
        $key = $list[$dayGz] ?? '丙午';
        return [
            'gz' => $key,
            'year' => $list1[$key],
        ];
    }

    /**
     * 禄神 日干+月支、日支
     * @param $jinian
     * @return bool
     */
    protected function isLuShen($jinian): bool
    {
        // 禄神 日干+月支、日支
        $listLu = [
            '甲' => '寅', '乙' => '卯', '丙' => '巳', '丁' => '午', '戊' => '巳', '己' => '午',
            '庚' => '申', '辛' => '酉', '壬' => '亥', '癸' => '子',
        ];
        $dg = $listLu[$jinian['d'][0]];
        if ($jinian['d'][1] == $dg || $jinian['m'][1] == $dg) {
            return true;
        }
        return false;
    }

    /**
     * 文昌 日干+月支、日支
     * @param $jinian
     * @return bool
     */
    protected function isWeican($jinian): bool
    {
        $list = [
            '甲' => '巳', '乙' => '午', '丙' => '申', '丁' => '酉', '戊' => '申', '己' => '酉',
            '庚' => '亥', '辛' => '子', '壬' => '寅', '癸' => '卯',
        ];
        $dg = $list[$jinian['d'][0]];
        if ($jinian['d'][1] == $dg || $jinian['m'][1] == $dg) {
            return true;
        }
        return false;
    }

    /**
     * 获得时详情
     * @param array $jiNianDay 日干支
     * @param array $dz 吉时地支
     * @return array
     */
    protected function getHourInfo($jiNianDay, $dz = []): array
    {
        $res = [];
        // 天干支
        $dgz = implode('', $jiNianDay);
        $jiNianDayT = $jiNianDay[0];
        // 时辰吉 按日天干地支获得
        $list = [
            '甲子' => '子丑卯午申酉', '甲寅' => '子丑辰巳未戌', '甲辰' => '寅辰巳申酉亥', '甲午' => '子丑卯午申酉',
            '甲申' => '子丑辰巳未戌', '甲戌' => '寅辰巳未申酉亥', '己丑' => '寅卯巳申戌', '己卯' => '子寅卯午未酉',
            '己巳' => '丑辰午未戌亥', '己未' => '寅卯巳申戌亥', '己酉' => '子寅卯午未酉', '己亥' => '丑辰午未戌亥',
            '乙丑' => '寅卯巳申戌亥', '乙卯' => '子寅卯午未酉', '乙巳' => '丑辰午戌亥', '乙未' => '寅卯巳申戌亥',
            '乙酉' => '子寅卯午未酉', '乙亥' => '丑辰午戌亥', '庚子' => '子丑卯午申酉', '庚寅' => '子丑辰巳未戌',
            '庚辰' => '寅辰巳酉亥', '庚午' => '子丑卯午申酉', '庚申' => '子丑辰巳午未戌', '庚戌' => '寅辰巳申酉亥',
            '丙子' => '子丑卯午申酉', '丙寅' => '子丑辰巳未戌', '丙辰' => '寅辰巳申酉亥', '丙午' => '子丑卯午申',
            '丙申' => '子丑辰巳未戌', '丙戌' => '寅辰巳申酉亥', '辛丑' => '寅卯巳申戌亥', '辛卯' => '子寅卯午未酉',
            '辛巳' => '丑辰午未戌', '辛未' => '寅卯巳申戌亥', '辛酉' => '子寅卯午未酉', '辛亥' => '丑辰午戌亥',
            '丁丑' => '寅卯巳申戌亥', '丁卯' => '子寅卯午未酉', '丁巳' => '丑辰午未戌亥', '丁未' => '寅卯巳申戌亥',
            '丁酉' => '子寅卯午未酉', '丁亥' => '丑辰午未戌亥', '壬子' => '子丑卯午申酉', '壬寅' => '子丑辰巳未戌',
            '壬辰' => '寅辰巳申酉亥', '壬午' => '子丑卯午申酉', '壬申' => '子丑辰巳未戌', '壬戌' => '寅辰巳申酉亥',
            '戊子' => '子丑卯午申酉', '戊寅' => '子丑辰巳未戌', '戊辰' => '寅辰巳申酉亥', '戊午' => '子丑卯午申酉',
            '戊申' => '子丑辰巳未戌', '戊戌' => '寅辰巳申酉亥', '癸丑' => '寅卯巳申戌亥', '癸卯' => '子寅卯午酉',
            '癸巳' => '丑辰午未戌亥', '癸未' => '寅卯巳申戌亥', '癸酉' => '子寅卯午未酉', '癸亥' => '丑辰午未戌亥',
        ];
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $strJi = $list[$dgz] ?? $list['甲子'];
        foreach ($hourList as $k => $v) {
            if (!in_array($k, $dz) && !empty($dz)) {
                continue;
            }
            $hgz = Huangli::getGanzhiHour($jiNianDayT, $k);
            $tmp = [
                'h' => $hgz,
                'hour' => $v,
            ];
            $hourgz = implode('', $tmp['h']);
            // 时宜忌
            $tmp['zheng_chong'] = $this->getZhengChong(implode('', $hgz));
            $tmp['sha'] = Huangli::getSanSha($k)[0];
            $position = Huangli::getPositionbyTg($tmp['h'][0]);
            $tmp['cai_shen'] = $position['cai_shen'];
            $res[] = $tmp;
        }
        return array_values($res);
    }
}
