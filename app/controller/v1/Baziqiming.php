<?php
// +----------------------------------------------------------------------
// | Baziqiming.第一星座-八字取名接口开发
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\qumingdafen\Bihua;
use app\model\baobaoqm\Cnword;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use Overtrue\Pinyin\Pinyin;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Baziqiming
{
    /**
     * 初始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户生日实例
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 八字取名接口
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 姓
            'xing' => input('xing', '', 'trim'),
            // 性别 0 男 1 女
            'sex' => input('sex', '', 'trim'),
            // 生日
            'time' => input('time', '', 'trim'),
            // 名字字数
            'num' => input('num', 1, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'xing|姓' => ['require', 'chs'],
                'sex|性别' => ['require', 'in:0,1'],
                'time|生日' => ['require', 'isDateOrTime:生日'],
                'num|名字字数' => ['require', 'in:1,2'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->orginData = $data;
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYongList = $this->getxiYongJi();

        $xingInfo = $this->getXingInfo($data['xing']);
        $yiwx = $xingInfo['wx'] . $xiYongList['wx'][0];
        $wxPeiZhi = [
            $xiYongList['wx'][0],
        ];
        $bihuazuhe = $data['num'] > 1 ? $this->getBiHuaZuHeByXing($xingInfo['bihua']) : '';
        if ($data['num'] > 1) {
            $wxPeiZhi = $this->getWuXingPeiZhi($xiYongList['wx'][0]);
        }
        $result = [
            // 姓信息
            'xing' => $xingInfo,
            // 喜神
            'likegod' => $xiYongList['wx'][0],
            // 笔画组合 双字用
            'bihuazuhe' => $bihuazuhe,
            // 单字用 适宜
            'yiwx' => $data['num'] > 1 ? '' : $yiwx,
            // 五行配置
            'wxpeizhi' => $wxPeiZhi,
        ];
        return $result;
    }


    /**
     * 根据姓名笔画获得笔画组合
     * @param int $num
     * @return array
     */
    protected function getBiHuaZuHeByXing(int $num): array
    {
        $list = [
            2 => ['9+4', '1+10', '19+4', '11+10', '13+10', '3+10'],
            3 => ['18+14', '13+5', '12+6', '18+14', '10+22', '8+5', '8+24', '20+12', '3+12'],
            4 => ['20+15', '9+12', '9+2', '13+12', '13+4', '9+4', '9+22', '19+12', '3+14'],
            5 => ['20+4', '10+14', '12+4', '2+6', '18+14', '10+6', '8+5', '8+24', '12+6'],
            6 => ['11+14', '9+14', '9+6', '12+23', '19+4', '11+14', '19+16', '9+16', '10+7'],
            7 => ['9+16', '8+10', '10+6', '11+14', '8+17', '9+15', '30+15', '22+10', '8+16', '8+8'],
            8 => ['8+16', '9+7', '9+6', '13+12', '10+6', '3+12', '7+16', '9+6'],
            9 => ['7+16', '2+14', '2+4', '12+20', '12+4', '9+6', '20+12', '6+17', '8+7'],
            10 => ['14+17', '11+10', '11+2', '19+12', '11+12', '3+10', '11+20', '13+12', '3+12'],
            11 => ['10+20', '4+20', '2+4', '14+23', '12+12', '14+4', '21+20', '20+4', '10+14'],
            12 => ['9+16', '13+4', '1+10', '20+15', '9+12', '3+10', '23+12', '9+14', '3+14'],
            13 => ['18+14', '8+16', '12+4', '12+23', '12+12', '3+15', '18+17', '18+6', '12+6'],
            14 => ['10+15', '3+15', '3+12', '3+22', '9+12', '9+6', '11+12', '11+7'],
            15 => ['9+23', '10+7', '9+7', '22+15', '2+14', '20+4', '8+24', '3+14'],
            16 => ['19+6', '9+7', '9+4', '21+4', '13+4', '9+6', '19+5', '19+4', '2+14'],
            17 => ['20+15', '8+16', '8+7', '18+6', '8+10', '18+17', '12+6'],
            18 => ['13+4', '10+5', '19+4', '9+6', '13+12', '9+14'],
            19 => ['12+20', '12+4', '2+4', '11+7', '6+7', '12+17', '2+14'],
            20 => ['20+4', '10+14', '12+4', '2+6', '18+14', '10+6', '8+5', '8+24', '12+6'],
            21 => ['11+14', '9+14', '9+6', '12+23', '19+4', '11+14', '19+16', '9+16', '10+7'],
            22 => ['7+16', '2+14', '2+4', '12+20', '12+4', '9+6', '20+12', '6+17', '8+7'],
            23 => ['8+16', '9+7', '9+6', '13+12', '10+6', '3+12', '7+16', '9+6'],
            24 => ['9+16', '13+4', '1+10', '20+15', '9+12', '3+10', '23+12', '9+14', '3+14'],
            25 => ['10+20', '4+20', '2+4', '14+23', '12+12', '14+4', '21+20', '20+4', '10+14'],
            26 => ['18+14', '8+16', '12+4', '12+23', '12+12', '3+15', '18+17', '18+6', '12+6'],
            27 => ['10+15', '3+15', '3+12', '3+22', '9+12', '9+6', '11+12', '11+7'],
            28 => ['9+23', '10+7', '9+7', '22+15', '2+14', '20+4', '8+24', '3+14'],
            29 => ['20+15', '8+16', '8+7', '18+6', '8+10', '18+17', '12+6'],
            30 => ['7+16', '7+10', '7+6', '19+10', '11+10', '3+12', '14+15', '14+7', '11+6'],
            31 => ['12+20', '12+4', '2+4', '11+7', '6+7', '12+17', '2+14'],
            32 => ['11+14', '1+12', '9+23', '3+12', '12+20', '11+4'],
            33 => ['8+10', '11+5', '10+14', '10+6', '11+20', '2+14'],
        ];
        return $list[$num] ?? $list[3];
    }

    /**
     * 获得五行配置
     * @param string $wx 五行
     * @return array
     */
    protected function getWuXingPeiZhi(string $wx): array
    {
        $list = [
            '火' => ['火火', '火金', '火木', '火水', '火土', '金火', '木火', '水火', '土火'],
            '金' => ['金火', '金金', '金木', '金水', '金土', '火金', '木金', '水金', '土金'],
            '木' => ['木火', '木金', '木木', '木水', '木土', '火木', '金木', '水木', '土木'],
            '水' => ['水火', '水金', '水木', '水水', '水土', '火水', '金水', '木水', '土水'],
            '土' => ['土火', '土金', '土木', '土水', '土土', '火土', '金土', '木土', '水土'],
        ];
        return $list[$wx] ?? $list['火'];
    }

    /**
     * 获得喜神、用神、仇神、忌神、闲神 五行
     * @return array
     * @throws \Exception
     */
    protected function getxiYongJi(): array
    {
        $bases = $this->lunar->getLunarByBetween();
        $wangDu = BaziExt::getWangDu($bases['jinian']);
        $god = $this->lunar->getGod();
        $_god = $this->lunar->_getGod();
        // 正官 七杀
        $numGuan = 0;
        // 食神 伤官
        $numshi = 0;
        // 正财 偏财
        $numCai = 0;
        // 正印 偏印
        $numYin = 0;
        // 比肩 劫财
        $numBi = 0;
        $godList = array_merge_recursive($god, $_god['year']['god'], $_god['month']['god'], $_god['day']['god'], $_god['hour']['god']);
        foreach ($godList as $v) {
            switch ($v) {
                case '正官':
                case '七杀':
                    $numGuan++;
                    break;
                case '食神':
                case '伤官':
                    $numshi++;
                    break;
                case '正财':
                case '偏财':
                    $numCai++;
                    break;
                case '正印':
                case '偏印':
                    $numYin++;
                    break;
                case '比肩':
                case '劫财':
                    $numBi++;
                    break;
            }
        }
        // 0身旺格  1身弱格  2从旺格  3从弱格
        $listShen = [];
        switch ($wangDu) {
            case '从弱格':
            case '身弱格':
                $array = [$numGuan, $numshi, $numCai];
                arsort($array);
                $key = key($array);
                $list = [
                    ['比劫', '印枭', '官杀', '才财', '食伤'],
                    ['比劫', '印枭', '官杀', '才财', '食伤'],
                    ['印枭', '比劫', '才财', '食伤', '官杀'],
                ];
                $listShen = $list[$key];
                break;
            case '从旺格':
            case '身旺格':
                $list = [
                    ['官杀', '才财', '比劫', '印枭', '食伤'],
                    ['才财', '食伤', '比劫', '印枭', '官杀'],
                ];
                $array = [$numYin, $numBi];
                arsort($array);
                $key = key($array);
                $listShen = $list[$key];
                break;
        }
        $list2 = [
            '食伤' => [
                '金' => '水', '木' => '火', '水' => '木', '火' => '土', '土' => '金',
            ],
            '比劫' => [
                '金' => '金', '木' => '木', '水' => '水', '火' => '火', '土' => '土',
            ],
            '才财' => [
                '金' => '木', '木' => '土', '水' => '火', '火' => '金', '土' => '水',
            ],
            '官杀' => [
                '金' => '火', '木' => '金', '水' => '土', '火' => '水', '土' => '木',
            ],
            '印枭' => [
                '金' => '土', '木' => '水', '水' => '金', '火' => '木', '土' => '火',
            ],
        ];
        $result = [];
        // 天干五行
        $tgwx = $this->lunar->wuXingAttr[$bases['jinian']['d'][0]];
        foreach ($listShen as $v) {
            $result[] = $list2[$v][$tgwx];
        }
        return [
            'wx' => $result,
            'shen' => $listShen,
        ];
    }

    /**
     * 获得姓五行和笔画
     * @param string $xing 姓氏
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getXingInfo(string $xing): array
    {
        $wx = '火';
        $bihua = 10;
        $xingArr = $this->getZiInfo($xing);
        if (count($xingArr) == 1) {
            $bihua = $xingArr[0]['bihua'];
            $wx = $xingArr[0]['wx'];
        } else {
            $bihua = (int)array_sum(array_column($xingArr, 'bihua'));
            $wx = $this->getWuXing($bihua);
        }
        return [
            'xing' => $xing,
            'bihua' => $bihua,
            'wx' => $wx,
        ];
    }

    /**
     * 获得字相关拼音笔画
     * @param string $str
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getZiInfo(string $str): array
    {
        $modelBihua = new Bihua();
        $res = [];
        preg_match_all("/./u", $str, $arr);
        foreach ($arr[0] as $v) {
            $v = $this->replacestr($v);
            $data = Cnword::info($v);
            if (empty($data)) {
                $tmpBiHua = (int)$modelBihua->find($v);
                $tmpBiHua = $tmpBiHua > 0 ? $tmpBiHua : 10;
                $res[] = [
                    'name' => $v,
                    'big5' => $v,
                    'bihua' => $tmpBiHua,
                    'pinyin' => Pinyin::sentence($v, 'none')->join(''),
                    'wx' => '火',
                ];
            } else {
                $res[] = [
                    'name' => $v,
                    'big5' => $data['big5'],
                    'bihua' => $data['bihua2'],
                    'pinyin' => $data['py2'],
                    'wx' => $data['wx'] ?: '火',
                ];
            }
        }
        return $res;
    }

    /**
     * 字体替换
     * @param $data
     * @return string
     */
    private function replacestr($data): string
    {
        $array = [
            '㯋' => '颖', '麹' => '曲',
        ];
        $res = $data;
        if (isset($array[$data])) {
            $res = $array[$data];
        }
        return $res;
    }

    /**
     * 获得五行属性
     * @param int $bihua
     * @return string
     */
    private function getWuXing(int $bihua): string
    {
        $num = $bihua % 10;
        $list = [
            0 => '水', 1 => '木', 2 => '木', 3 => '火', 4 => '火', 5 => '土',
            6 => '土', 7 => '金', 8 => '金', 9 => '水',
        ];
        return $list[$num];
    }
}
