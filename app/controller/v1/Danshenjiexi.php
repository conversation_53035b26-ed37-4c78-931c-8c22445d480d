<?php
// +----------------------------------------------------------------------
// | Danshenjiexi.单身姻缘大解析接口算法开发
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;

class Danshenjiexi
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * 单身姻缘大解析
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time' => input('time', '', 'trim'),
            // 性别
            'sex' => input('sex', 0, 'intval'),
            // 测算日期
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->orginData = $data;
        $base = $this->lunar->getLunarByBetween();
        $result = [
            'base' => $base,
            // 先天情况
            'xiantian' => $this->getXianTian(),
            // 单身原因
            'reason' => $this->getDanshenReason(),
            // 脱单
            'suggest' => $this->getTuodanSuggest(),
            // 另一半
            'liyiban' => $this->getLiYiBan(),
            // 脱单时间
            'tuodan' => $this->getTuoDanTime(),
            // 不利时间
            'buli' => $this->getBuliTime(),
            // 不稳时间
            'buwen' => $this->getBuWenTime(),
        ];
        return $result;
    }

    /**
     * 先天情况
     * @return array
     * @throws Exception
     */
    protected function getXianTian(): array
    {
        $list = [
            [
                [
                    '可以看出你的异性缘并不是那么的理想，身边可能很难出现一些合适的异性，因此恋爱或者结婚的时机相对来说就会比较晚出现，或许你可以尝试着做出一些改变，借此寻找脱单的契机。',
                    '有些人单身的时间长了，就会开始担心，怀疑自己是不是将要孤独终老。至于你大可不必烦恼这个，你要相信自己只是一个普通人，那种万中无一的特殊人士肯定不会是你。',
                ],
                [
                    '你的异性缘并不是很好，命中所带的桃花显示，你很容易控制不住自己的情欲，跟很多人的相处都略显暧昧，所以会让人觉得有点花心，有些人就会对你的印象大大减分。',
                    '你的桃花会来的比较晚些，而你也在感情方面表现的比较迷茫，所以才没有抓紧一些脱单的机会，但这不至于让你一直处于单身的状况，等到桃花来临时，一切便会水到渠成。',
                ],
                [
                    '你的异性缘并不是很好，主要是你在感情上面属于不开窍的类型，和异性之间的相处存在着一些问题，因此异性缘就受到了影响，可能还需要自己做出一些改变才有所好转。',
                    '你目前虽然还是单身的状态，但是不要有孤独终老的担忧，因为你现在的单身处境主要是和自己的性格有些关系，做出改变之后爱情自然也就有会有新的进展。',
                ],
                [
                    '你的异性缘很好，往往都是一些做人做事上的体现，当然也有个人散发的特质和性格特点，是正中异性下怀的，所以身边可以发展恋爱的选择较多，只是要看你自己的想法是如何的了。',
                    '你可能会有孤独终老的情况，身边能够发展恋爱的异性较少，而且你也因为害怕别人不喜欢你而迟迟停在原地，没有任何行动，其实开朗豁达一点，人们就会喜欢你，完全不用担心。',
                ],
            ],
            [
                [
                    '你的异性缘还不错，一般来说身边肯定少不了一些恋爱的机会，只要能够把握住，双方又能相互包容，相处愉快，自然也就和谐幸福，能够走到最后。',
                    '虽然你目前是单身的状态，但是你也不至于孤独终老，只是缘分可能会来的比较晚，而自己在爱情上的领悟也较差，才导致了目前的单身状况，等缘分到来之时，一切甚至会比你想象的发展更快。',
                ],
                [
                    '你在感情这方面作风比较大胆，在其他人看来你是一个很有魅力的人，所以异性缘好也是情理之中的事情，只是异性缘太好也要注意分辨其中的真桃花和假桃花。',
                    '单身太久，会担心自己是不是一直如此单身，你并不需要为此担心，只要你想谈恋爱，那么是有机会的，除非是你自己没有这样的想法，那么才会保持一直独身的状态。',
                ],
                [
                    '你的异性缘一般，虽然和周围的异性能够和谐交往，但是你是一个非常难去满足的人，想拥有一个完美恋人，会很直接的挑三拣四，也就影响了周围异性对你的印象。',
                    '你目前的单身状态主要是因为时机还没有到来，所以千万不要操之过急，更不要有孤独终老的担忧，等缘分到了，再积极的抓住机会，那么恋情就会发展的很顺利了。',
                ],
                [
                    '你的人缘较好，所以异性缘也还算不错。身边追求者较多，如果自己不能够控制情欲，恐让人反感，对婚姻恋爱也会有一定的影响，但如果两个人能够好好在一起，生活会非常幸福。',
                    '目前的你虽然一直都是单身的状态，但是并不代表你会一直单身，在你的身边会有一些合适的异性，等到机遇成熟的时候，自然也就会互相吸引，展开新的恋爱了。',
                ],
            ],
            [
                [
                    '你的异性缘较好，很容易让人产生好感，因此身边不乏一些偷偷喜欢着自己的异性，甚至是直接向你表明自己的心意，但是桃花运太好容易导致你早恋。',
                    '你不用担心会有孤独终老的情况，因为以你的性格是会找到与其同甘共苦的人，只是这个人会来的晚一些，你不妨再稍微耐心等等，或者是先调整好自己的状态，等待着对方的到来。',
                ],
                [
                    '你的异性缘比较理想，身边能有一些追求者，而你的情路也会比较顺利，正缘会更早的来到你的身边，也就避免了众多桃花中烂桃花带来的影响，使自己受到没有必要的伤害。',
                    '你单身的情况虽然已经维持了一段时间，但是这并不说明你之后会一直都是单身的状态，之后的你或许缘分到来，找到了适合自己的另外一半，那么发展反而会更快。',
                ],
                [
                    '你的人缘很好，吃了蜂蜜一样说话使人听了非常甜，而因此你的异性缘也很不错，会有愿意想要去了解你的人，只是还需要你也有一些主动，才能使感情修成正果。',
                    '你会有脱单的时候，所以不要有找不到喜欢的人，没有人喜欢自己的担忧，或许只是你在感情方面上比较迟钝，造成了目前单身的处境，做出改变后，自然也就能有恋爱的机会。',
                ],
                [
                    '你身边的朋友很多，所以异性缘也很强，能有一些追求者，甚至婚后你的桃花运都会非常的旺盛，如果把握不好自己，很容易影响到婚姻，造成感情上面的不顺。',
                    '你千万不要认为现在的单身会维持很久，更不要有孤独终老的担忧。目前单身的原因可能是缘分还没到，也有可能和自己本身有些关系，所以得到改变后，自然也就会有脱单机会了。',
                ],
            ],
            [
                [
                    '你的人缘很好，不管是同性还是异性都能与你相处得非常愉快，因此在你的身边总是不会缺少心仪的对象或者追求者，而且都是十分的优秀，只是选择的机会多了，反而让你无从下手。',
                    '你会有脱单的时候，所以是不会孤独终老的，只是你对于爱情方面比较迟钝，在你之后的努力之下，以及身上闪闪发光的优点，那么就会有人被你吸引，而你也因此会找到自己命中注定的另一半。',
                ],
                [
                    '你的异性缘比较好，平时待人真诚，自然身边也有着很多的异性朋友，而其中也有那么一两个对你存有好感，所以只要你有想谈恋爱的想法，那么能够脱单的几率是很大的。',
                    '单身的情况只是暂时的，你不要有孤独终老的想法，只是现在有缘人还没有来到你的身边，或许对方还在遥远的地方做着自己的事情，等合适的时间到了，你们自然也会相遇。',
                ],
                [
                    '虽然你的异性缘只是一般，而你的社交能力也不是很好，但是你本身非常努力的改变自己，会用很多的精力去提高自己的能力，那么优秀的你也会吸引周围不少的异性。',
                    '你目前的单身状态并不会维持太久，所以你也不要担心自己会孤独终老。只是说明现在还不是你恋爱的最好时机，这时候不妨好好的调整自己，缘分到来时才能更好的抓住机会。',
                ],
                [
                    '你的桃花运较好，身边的追求者总是源源不断，因为你有这个足够的魅力，所以能够吸引到周围的异性，那么也说明你的身边是不缺少可以恋爱的机会的。',
                    '目前的单身状态不会维持太久，身边存在着潜藏的追求者，所以你不要觉得你会有孤独终老的情况发生，等到时机合适之后，你们也能够互相吸引，自然也就水到渠成。',
                ],
            ],
            [
                [
                    '你的异性缘可以说是非常的好，对待感情比较容易分心，所以身边的追求者也源源不断，很能讨人喜欢，真爱会较早的来到身边，桃花运太好恐会受到些许影响。',
                    '单身太久，就会有是不是孤独终老的疑问，你完全不用担心这个问题，你只是目前单身，之后的你，缘分到来，就会遇到心仪的另外一半，而且你们之间是情投意合，感情非常的幸福。',
                ],
                [
                    '你的异性缘还算理想，身边能有可以发展的异性朋友，甚至因为你的人缘太好，还会存在同时有好几个人喜欢你，让你不知道应该怎么选择，要注意小心会有多角恋的情况发生。',
                    '单身的情况不会纠缠你太久，所以不要有孤独终老的担忧，你会找到喜欢的人，并且你们也会幸福的生活在一起，只是这个时间，还不是你们相遇的最好时机。',
                ],
                [
                    '在相处过程中，你让人感到亲切，所以你的桃花运也很好，很容易让人产生好感，身边不乏一些偷偷喜欢着自己的异性，甚至是直接表明喜欢你的异性。',
                    '你不会有孤独终老的情况发生，目前的单身状态可能主要是每次恋爱找上门的时候，你没有及时的抓住，而恋爱机会走了之后，你又开始后悔，所以机会来临时一定要紧紧的抓住。',
                ],
                [
                    '不管是同性还是异性和你相处的都非常的和谐，因此在你的身边是有着一些追求者的，而且都还十分的优秀，所以只要你愿意，自然就能够有着脱单的机会，反倒是你，还不知道如何选择。',
                    '你不会出现孤独终老的情况，因为在你的身边是有着一些追求者的，只是你自己对于感情比较迟钝，不知道如何去面对这些感情，如果你能做出一些改变，那么自然感情也就能够发展的顺利了。',
                ],
            ],
            [
                [
                    '你的异性缘十分的旺，因为你本身就是一个热情的人，所以这一生你的人际关系十分的和谐，当然异性缘也非常的好，身边从来都不缺对自己有好感的异性。',
                    '目前虽然是单身的状况，但是你还不至于孤独终老，只是属于你的缘分还需要你自己去努力，主要是你在感情上面醒悟的比较迟钝，就会影响到爱情的进展，等你改变之后，想必爱情就会到来了。',
                ],
                [
                    '你本身很会察言观色，在和人的交往当中很让人觉得舒服，因此你人缘很好，身边的桃花运也很理想，但这也说明你可能会过早的恋爱，也可能会因为恋爱而带来不好的影响。',
                    '单身的状态不会维持太久，所以不要有孤独终身的忧虑，目前的你可能更适合将时间花在工作或者是自己身上，等缘分来临的时候，一切就会水到渠成，你也会有甜甜的恋爱了。',
                ],
                [
                    '你的桃花运旺盛，自身魅力很容易赢得异性的注意，所以周围围绕着对你有好感的异性，因此你也容易陷入一段又一段的恋爱当中，桃花太多也要小心烂桃花带来的危害。',
                    '你不会一直维持单身的状态，只是目前脱单的机会还没有到来而已，所以更不要有孤独终老的担忧。等到缘分来临时，你们能够相处的十分和谐，生活也很美好甜蜜。',
                ],
                [
                    '你的异性缘很好，心甘情愿喜欢你的人不少，身边追求者也源源不断，毕竟你对待感情十分的浪漫，是大家喜欢的类型，但是桃花运太好也要小心容易出现多角关系，影响恋情的发展。',
                    '虽然目前你一直处于单身的状态，但是你并不会都是一个人。目前你对待感情的方法需要改变，而且缘分也没到来，若你能够做出改变，恋爱自然也会顺利。',
                ],
            ],
        ];
        $list2 = [
            '子' => [
                [
                    '有了一个目标之后，你会努力的为之奋斗，而没有目标，就会变得懒散堕落，喜欢一个人也是如此，如果可以先寻找一个目标，那么你才会制定合理的计划，投其所好，自然脱单的几率也就变大了。',
                    '虽然女人对于赞美的话是永远都听不腻的，但你的赞美也不能过于敷衍，不然反而会适得其反，受到对方的反感。真诚的称赞会让对方对你留下好印象，瞬间拉近你们之间的距离，也容易有其他的发展。',
                    '要想告白成功，肯定需要主动的去表达自己的心意，所以一定要提高自己的胆子，遇到喜欢的人主动的去做一些打动对方的行为，或者是勇敢的去表达爱意，这样才能让你们有着更深入的交往。',
                    '没有人是十全十美的，就算是你，也存在着一些缺点，所以在寻找另外一半时，不要总是以较高的标准去评判别人，这样没有人会在你的标准之内，降低一些要求，多看看周围人的优点，会发现某些人也是非常优秀的。',
                ],
                [
                    '千万不要有自卑心理，喜欢一个人就要主动的去表达，让对方关注到自己，而不是默默的站在角落偷偷的喜欢着别人，拥有一定的自信才能让自己充满魅力，增加很多和异性交往的机会。',
                    '你总是以非常挑剔的理由去嫌弃别人，周围很难有人能够符合你的标准。要知道每个人都有自己的缺点，没有人能够做到十全十美，包括你也是如此，所以还是要实事求是，不可过于挑剔。',
                    '人和人之间总会因为不同的想法而产生误会，所以肯定需要一些包容和理解。因此在和异性相处的时候不可过于苛刻，学会包容和理解才能加深两人的关系，从而才会发生一些不可描述的事情。',
                    '虽然说男生和女生的交往大都是男生买单居多，但是在没有确定关系，以朋友身份出去吃饭时，女生还是应该偶尔有一次买单，这样反而能够让异性对你印象深刻，看到你的独立及为他人着想的金钱态度。',
                ],
            ],
            '丑' => [
                [
                    '好看的皮囊千篇一律，有趣的灵魂万里挑一。交谈时幽默风趣的笑点，偶尔的甜言蜜语，能够让女人一想起来就很开心的男人，一定很容易吸引女人的注意，有时候喜欢就产生在触电的那一瞬间。',
                    '过去的某些不太美好的事情就让他随风而去吧，遇见了错误的才能和正确的相遇。调整好的心态迎接新的人和事物，建立新的社交圈，充实自己，让自己变得更加的优秀，拥抱更好的人。',
                    '有的时候会因为喜欢对方而做出一些关心，但是如果这些关心的表达方式不对，就会让对方误解，所以我们在关心对方的时候，不能为了满足自己的控制欲，而是要发自内心的去关心，为对方做些温暖的事情。',
                    '在花钱上面，建议可以表现的大方一些，男人如果太过抠门会让女人觉得很小气。不提倡铺张浪费，但是在吃饭上面，以及生活中的一些小东西上面，该花的还是要花，这样周围的异性才能对你有好的印象。',
                ],
                [
                    '虽然说在男女相处当中女生不应该表现的过于主动，但是如果你遇见了有好感的人，尽管你是女生，你也有主动的权力。没有喜欢的人，你也可以主动的在异性面前展示自己的优点，这样才能吸引异性，找到脱单的机会。',
                    '正是因为人和人都是不完美的，所以才需要另外一个人来跟自己相互扶持，所以在选择伴侣时，不要用很高的标准去看待别人，总认为人都是十全十美的，多看看周围人的优点会更好。',
                    '你总是表现出一种非常独立的状态，就会给人一种不需要嘘寒问暖，不需要体贴关怀的错觉。该独立的时候需要独立，但有时候也应该寻求一些帮助，才能让男生有机会在你面前拥有表现自己的机会。',
                    '身边没有认识的异性可以恋爱，那么就要主动的去寻找一些认识异性的机会，比如说通过朋友的介绍，或者是网络，要知道现在的网恋成功率并不低，主动一点能够获得不少的脱单机会。',
                ],
            ],
            '寅' => [
                [
                    '如果所有的时间都用在了工作上面，自然是没有和异性交往的机会，所以休息日的时候就多给自己一些私人的时间，多出去聚餐，或者是参加一些社交活动。而且不管恋爱中，还是恋爱后，也是需要一定的时间去陪伴对方。',
                    '你要去多学习一些撩妹的技巧，总是整天都非常的严肃，会让异性远离你，偶尔也说说甜蜜的话，或者是有一些有趣的行为，会让人觉得你很有趣，非常的活泼，会有愿意跟你有更深入相处的想法。',
                    '你的外貌就是别人对你的第一印象，所以一定要好好的学习怎么打扮自己，找到适合自己的穿着风格，或者是发型，至少也要让人看起来干净舒服的感觉，这样才能吸引到异性，为自己带来一些脱单的机会。',
                    '有心仪或者是有好感的异性，可以主动的提出约饭，不仅仅可以了解对方对你的一些印象，同时也能让对方对你有一些了解，没有心仪对象时，可以多约几个同性异性的好友一起，大家相互了解和吸引。',
                ],
                [
                    '对于爱情千万不能有胆怯的想法，这样才能够主动的去和异性交流，发展很多未知的可能。尤其是在遇见喜欢的人时，千万不要认为女生就应该等着被追，有时也可以适当的去表明自己的心意。',
                    '有时一个不经意的动作就能够让男人深陷其中，所以要学习怎么才能够吸引到异性的注意力。比如说撩动头发，很灿烂的一个笑容，嘟嘟嘴卖萌，虽然并没有和男生有着太多的接触，但却很容易让异性心动。',
                    '对人的第一印象来自于人的外貌，所以外貌对于人来说非常的重要，但是除了外貌，人的内在却是更加的重要，内在会散发个人的内涵修养等等，所以在学着改变外在形象的同时，也别忘记了内在美的培养。',
                    '既然想要迎接新的恋情，那么就不要沉浸在过去的回忆当中了，否则会对你现在的恋爱造成很大的影响。过去的已经过去，而现在要做的就是把握当下，多多发现身边的优秀异性，才能迎接甜甜的恋爱。',
                ],
            ],
            '卯' => [
                [
                    '和异性在一起的时候就要更加的知趣一些，比如说周围女生有需要帮助的时候，主动的帮助比她来主动寻求你的帮助会更让人觉得加分，难过的时候你的一些安慰也非常的温暖，如此一来，必能让你提高些异性缘。',
                    '其实女生有的时候并不是真的生气，可能就需要你说说好话，所以和女生相处，多说一些甜言蜜语肯定是没错的，不过除了要会说甜言蜜语之外，你也必须有点智商，博览全书，阅历丰富，说出来的话才值得相信。',
                    '总是把事情放在心里，不会轻易的信任别人，很难和别人建立起感情，所以生活中要多学习和别人的沟通，才能和大家拉近距离，跟异性相处时，这样的你也能更得人心，默默无闻难以吸引到异性的注意。',
                    '尽管是霸道总裁，对于自己喜欢的女生都会特别的温柔，而且你也不是霸道总裁，所以对于身边的人，或者是有好感的异性，不要过于苛刻，稍微的温柔一些，反而是能够让人对你产生好感。',
                ],
                [
                    '没有喜欢的人，要主动的去和异性交往，有喜欢的人，更要主动的出现在对方的身边，所以胆子大一点对于想要脱单的人来说绝对是利大于弊，即使是女生，也可以主动的去寻找自己的爱情。',
                    '男生都喜欢温柔的女生，所以在和异性交往的时候，千万不要遇事就风风火火，总是叽叽喳喳说个不停，很容易给异性留下不好的印象，表现的温柔一些，更能吸引到异性。',
                    '在生活中当中多组织一些好友聚会，有时候很久没有见面的朋友，说不定两个人就看对眼了，加上你们又相互熟悉，会更容易走在一起，而且也可以通过这些朋友认识其他的异性，为自己介绍几个合适的异性朋友。',
                    '如果非要做一个高雅的女人，只想享受飘逸在各种晚会之中的生活，而不愿意出现在大排档里，会让异性觉得有一种不敢靠近的感觉，要知道高雅但不矫情的女人才是男人最喜欢的。',
                ],
            ],
            '辰' => [
                [
                    '为了让周围的人可以注意你，所以你捏造了假象的自己，掩盖了自己的缺点，但是其实你到底是个怎样的人，别人在和你的相处下都能够真实的感受到，做一个真实的自己，说不定还能让你更加的吸引异性。',
                    '有时候不妨从自身找找问题，一直如此单身必定有着一些理由，到底是性格上面的缺陷，还是对于爱情的不自信，找到关键所在，并努力的去做出一些改变，相信越来越优秀的你肯定能够吸引到异性。',
                    '跟女生相处的时候就要表现的大方一些，吃个饭都要aa，生活中更是一点小东西都不能问你要，自然会让异性觉得你很小气，不会想要和你这样的人交往，所以大方一点，才会给异性留下好印象，才有对方想了解你的机会。',
                    '正因为你不是一个富二代，所以更要努力上进的工作，不仅仅是因为想要创建更加美好的未来，从另外一方面来说，没有女生会喜欢整天懒散，不知上进的男生，至少也愿意接触一个上进的潜力股。',
                ],
                [
                    '在满心追求学识能力之余，不如先从情商修炼着手，将对方的所思所想、喜怒哀乐运筹帷幄，知道怎么说怎么做才能更加的吸引人，更加的让人喜欢，这样的高情商对于脱单会有着很大的帮助。',
                    '肤浅的男人看外表，成熟的男人听说话。所以说女人的谈吐优雅非常的重要，通过说话，可以了解女人的气质、修养、家庭、学识等诸多方面，从而就有想要和你有更进一步的相处机会。',
                    '和异性的交往当中，一开始还不是特别熟悉，就不要问一些别人不愿意回答，或者是比较隐私的问题，这样会让对方觉得你十分的不礼貌，所以交谈当中要注意如何正确的交谈更让人喜欢。',
                    '外冷内热，就很容易给别人一种你不好相处的错觉，不敢轻易的接触你，所以这肯定就会错过和异性接触的机会，生活中还是要表现的活泼阳光一些，会让人觉得很有亲切感，有想要接触你的想法。',
                ],
            ],
            '巳' => [
                [
                    '虽然说人都是存在着一些缺点，但是如果你知道一些缺点，却不去做出改变，那么只会让自己变得越来越差，这样自然是没有异性愿意和你交往，所以也要学会做出改变，让自己变得更加优秀。',
                    '因为是单身，所以会和身边的很多异性都暧昧不清，这不是在给自己寻找脱单的机会，反而是给人很花心的印象，因此要注意维系自己的印象，尽管有想要和对方接触的想法，也不要做出一些不太礼貌的行为。',
                    '平时的时候，可以多做一些讨喜的行为，能够收获到异性的青睐，如果有心仪的对象，就更要去了解对方的一些喜好，用心的制造一些浪漫，那么就会给对方很好的印象，也容易让彼此产生感情。',
                    '会聊天的人，自然能够深受别人的喜欢，而不会聊天的人，就会导致别人的不满，所以学会加强自己的沟通能力，不仅仅能够帮助你搭讪她人，也能够让你和异性相处的时候，更让异性青睐。',
                ],
                [
                    '不要总是消极的状态，不管是在和人交谈，还是在朋友圈，满满的负能量，会让人觉得不知道应该怎么和你相处，记住一定要充分展现自己的优点给异性看，这样才能更让人想靠近。',
                    '在和异性交流的时候，千万不要让整个气氛都变得十分的尴尬，主动的寻找一些有趣的话题，让对方对你有着一个好的印象，才有想要继续接触你的想法，所以一定要学会交流的一些小技巧。',
                    '总是十分高冷的样子，容易给人一种不好相处的感觉，所以遇见对你有好感的异性可能也不敢开口表白。要学会做一个随和，温暖的人，学着主动打招呼，主动微笑，主动和大家聊天，大家才会愿意和你相处。',
                    '千万不要在谈话的过程中传播自己的坏情绪，尤其是有时候心情非常不好，就会说话带刺，声声逼人，这样给别人的印象很差，会觉得你的情绪捉摸不透，没有想要和你相处的想法，降低对你的好感。',
                ],
            ],
            '午' => [
                [
                    '脾气温柔，喜欢微笑的人，肯定更能深得人心，总是摆出一副高冷的样子，自然就会让大家远离，也更不要期待异性缘的增加，因此对待周围的人还是要和善一些，这样不仅仅是和异性，跟同性也能相处的更好。',
                    '找到自己的特长，并在合适的时间当中展现出来，会让异性觉得眼前一亮的感觉，所以一定要拥有一到几个自己的特长，没有的话，就要去挖掘，去学习，这样才能够让自己多一些吸引别人的亮点。',
                    '虽然幽默有趣的对话更能让人喜欢，但是也要分清楚真幽默和假幽默的区别，将低俗的笑话当做幽默，反而是得不偿失，给异性不好的印象，寻找一些正确的笑点，才能更让人觉得交谈舒服，也更能吸引到异性。',
                    '如果一个男生整天对周围的人都很暴躁，那么肯定没有女生愿意接近，要知道女生都喜欢温柔的男生，相处的时候会觉得特别的舒服，莫名的就产生亲切感，试着将自己打造成一个温柔的男生吧。',
                ],
                [
                    '世界很大，我们的生活却非常的小，每天都在一个特定的圈子里面生活，限制了社交范围，所以走出去看一看吧，多出去结交朋友，才能认识到更多的异性，也容易碰到一见如故的伴侣。',
                    '在和人交谈的时候，千万不要音量很高，给人一种叽叽喳喳说个不停的感觉，很容易给别人留下不好的印象，尽量的压低说话的声音，保持让人舒适的分贝，这样才会吸引男生，对你产生好感。',
                    '有的时候感情就是单方的，你对别人有好感，别人却只当你是普通的朋友，所以这个时候，你也可以主动的去追求自己的爱情，适时的表达自己的好感，多在对方身边围绕，说不定也就变成两情相愿了。',
                    '学会提高自己的人际关系，多观察身边人际关系好的朋友，是如何跟周围的人相处的，像是在说话语气、态度上的拿捏，最重要的还有对人的一种尊重，提高人际关系后，自然也就能够认识很多异性了。',
                ],
            ],
            '未' => [
                [
                    '男生就算不会打扮，至少也要收拾的干净舒服，总是不修边幅会给人一种邋遢不爱干净的感觉，别人不仅仅不会被你吸引，反而还会有抵触的心理出现。多学习一些如何通过改变自身形象来提升魅力的装扮吧。',
                    '想要认识异性，就要学会厚脸皮，不管是认识还是不认识的人，遇见了能够让自己心动的，就要在对方不反感的情况下，毫不犹豫的去主动交流，这样才能有发展的机会，什么都不敢做，自然难有脱单机会。',
                    '如果实在不知道应该怎么去脱单，那么不妨花点时间好好的投资一下自己，找出时间多去健身运动，女生对于健硕的身体向来都是无法抗拒的，忍不住多看两眼，说不定就能够为你带去一些脱单的机会。',
                    '很多人说话经常会无意之间冒出一些脏话，在和女生的交流当中，这样的表现是非常的不礼貌的，因此一定要养成良好的习惯，不说脏话，做一个温柔儒雅的翩翩公子，更容易给异性留下深刻的印象。',
                ],
                [
                    '要知道爱笑的女孩子运气都不会太差，所以一定要经常微笑，这样给人的感觉很好相处，就会有很多的异性想要和你交往，反之，则会让人觉得你很难相处，会对你非常的疏远。',
                    '朋友圈一旦固定，就感觉很难再融入其他的圈子，你一定要勇于打破自己固定的朋友圈，可以通过好友或者是工作上的交流认识新的朋友，说不定也能遇见和自己有火花产生的异性。',
                    '自己优秀，所以也想要找个优秀的人是没错，但是你自己也有着一些缺点，却想要找个十全十美的人，是不是有点过于高标准，在选择另外一半的时候，还是不要以太高的标准去评判别人，会有些让人不敢靠近。',
                    '女生有时候应该聪明一些，但是有时却不能太过聪明，一直表现的过于聪明会让男生不敢轻易靠近，所以有的时候还是需要装装傻，对于男生来说，女生偶尔的单纯反而能够让他们轻松起来。',
                ],
            ],
            '申' => [
                [
                    '如果你能够在女生委屈的时候，送上一些安慰，在女生难过的时候，及时的送上温暖，不仅仅会让人觉得非常的绅士，反而还能带给别人很好的印象，所以做一个绅士的男人吧，能获得很多异性缘。',
                    '提高自身形象有利于提升魅力，提高异性缘，但是也不是一定要穿名牌衣服才能彰显你的魅力，干净的装扮，舒服的状态，也会给人非常的舒适，也能增加很多自己和异性交往的机会。',
                    '投其所好非常的重要，如果你总是说一些异性不爱听的话，那么就很容易受到异性的讨厌，如果你能够了解到异性喜欢什么，投其所好，取其之兴，奉其能欢，定能更快的引起对方的注意。',
                    '尽管是想要获得别人的好感，但是也不能过分恭维以及讨好对方。没有掌握度，很容易适得其反，所以还是要展现真实的一些评价。偶尔的称赞，循序渐进的相处方式反而更能赢得人心。',
                ],
                [
                    '不管在什么场合，都应该好好的打扮自己，展现出自己的魅力，这样你才能够在众多的女人中脱颖而出，吸引到男生的注意力，所以女生还是要有一定的自信，不能对自己太过自卑，反而难让男生注意到你。',
                    '男人有时候就会表现的强势一些，想要在女人的面前展现自己的能力，所以你有的时候就不要表现的过于坚强和独立，这样很难让人接近，要知道偶尔的示弱，才能够激起男人的保护欲。',
                    '自卑的人在和异性交谈的时候，就总是在看着其他地方，这样很容易让对方觉得你不尊重他，其次也会有你对他不感兴趣的想法，从而停止对你的好感，所以跟异性交谈时还是要认真的倾听，更有利你们的相处。',
                    '如果身边没有可以发展的异性，怎么能够脱单呢。所以要学会扩大自己的圈子，比如说多参加社团活动，或者是多和朋友聚会，没事就到处走走，才能认识更多的异性，也能有让你脱单的机会。',
                ],
            ],
            '酉' => [
                [
                    '你可以选择多花点时间来投资自己，不仅仅是和别人交谈的时候会显得很有水平，为人处世上面也会圆滑很多，只有自己变得优秀了，才更容易吸引别人的注意力。所以多参加实践，多学习，多增加自己的阅历吧。',
                    '如果别人跟你的相处是很轻松愉快的，肯定也会愿意和你相处，如果别人跟你的相处是很尴尬的，自然就会不愿意和你相处，所以有时也不能表现的过于高冷，随和健谈反而能够更受异性喜欢哦。',
                    '经常泡吧，爱玩游戏，这些不好的生活习惯会让你变得越来越糟。而良好的生活习惯会将你打造成一个阳光积极，非常努力的男生，这样的你会更受到异性的喜欢，吸引到异性的注意。',
                    '有的时候不要太过排斥相亲，本来生活忙于三点一线，就缺少认识异性的机会，而父母朋友因为了解你，所以也会给你选择一个合适的相亲对象，所以相亲不乏也是一个好的选择，万一对方正是你所喜欢的类型。',
                ],
                [
                    '主动的去和别人交往，才能够有结识异性的机会，而有好感的异性时，也应该主动的去跟对方表达自己的一些好感，千万不要害怕拒绝，被拒绝只是伤心几个月而已，没有在一起，可能会一直在心里耿耿于怀。',
                    '长时间没有脱单也不要变得消极，或者是表现出来很着急的样子，这样很容易给别人留下不好的印象，没有可以发展的异性，就学会投资自己，在自己变得更优秀的过程中，一定会有异性为你驻足。',
                    '整天待在家里肯定缺少和异性认识的机会，所以一定要多出去走走，多参加朋友之间的聚会，说不定还会有朋友为你介绍她身边的一些异性，而且也能在这些场所有认识异性的机会，脱单就指日可待了。',
                    '要学会投资自己，只有自己变得越来越优秀，才能够吸引到更多的异性，所以养成一个良好的生活习惯，多在生活当中培养一些兴趣爱好，从学识，气质，谈吐，担当中去变成更好的自己，也能早点遇到另外一半。',
                ],
            ],
            '戌' => [
                [
                    '明明自己条件也还不错，但为什么就是缺少脱单的机会呢？那是因为你不懂女人的心，不知道怎么去投其所好，所以不妨跟周围的恋爱高手学习一些和异性交往的技巧，说不定还能帮助你增加异性缘。',
                    '技多不压身，在平时的生活当中找一些兴趣爱好去培养，多发掘自己的优点，在朋友圈或者是合适的机会当中展现出来，那么就会吸引到很多的异性，让人看到你身上的魅力，自然也给你带去了脱单的机会。',
                    '生活当中接触异性的机会较少，那么就要利用一些闲余时间，比如说线下社交活动，聚会，或者是一些相亲活动，都是不错的选择，认识的异性多了，你才能够有交往的机会，自然也能告别单身了。',
                    '没有机会接触异性，那么怎么能脱单呢？所以要懂得制造机会跟女生相处，并且投其所好，找到一些感兴趣的话题来吸引对方，这样在不断的相处当中，就能够有着更加深入的接触。',
                ],
                [
                    '如果你在异性的面前展示的是你什么都会做，什么也都懂的独立状态，那么异性是难以靠近你的，不是说女孩子不需要独立，不需要什么都懂，而是说应该适当的示弱，异性才有机会接触你，也能有相处的机会。',
                    '现在的女生经常都是口不折言，在公共场所就会出口成脏，可以说这样的行为会让你的形象大打折扣，出口成脏不代表不善良，但是给人的第一印象非常的不好。男生还是比较喜欢温柔，单纯的女生。',
                    '在和异性的交往当中，学会多展现自己的优点，这样才能更吸引对方，当然如果你有喜欢的人，那么也要学会在和他的交流时有一些暗示，去了解他对你的一些想法。即使是女生，也有主动去追求幸福的权力。',
                    '和异性之间的交往还是要有一定的距离，尤其是话语和行为当中，太过亲近会给人一种暧昧不清的关系，男生不喜欢到处撒网的女生，一般会选择敬而远之，所以不要认为单身就可以到处撩。',
                ],
            ],
            '亥' => [
                [
                    '或许你需要想想为什么一直以来的你都没有找到脱单的机会呢？是和异性沟通太少，还是自己标准太高，不妨好好的寻找其中的问题，并且做出相应的一些改变，说不定之后的你就会迎来一些可以脱单的机会。',
                    '生活始终都是要重新开启的，所以就从过去当中的回忆走出来吧，或许你会发现接下来遇到的人会更加的适合你，更让你喜欢。成长总是要有些不让人舒心的经历，你才会有所改变。',
                    '人都是视觉动物，对于外貌非常的执着，所以想要吸引异性的注意，你就需要对自身形象做出一些改变，找到适合自己的穿衣风格，这样才能让自己有更多的机会去认识异性，摆脱单身的现况。',
                    '俗话说得好，好看的皮囊千篇一律，有趣的灵魂万里挑一，生活本就是平淡乏味的，如果你可以给生活增加一些乐趣，自然是很能让人产生好感，跟异性交谈时多增加一些有趣的话题吧。',
                ],
                [
                    '总是一副高冷的样子，很难让异性有想要接触你的欲望，所以对待周围的人一定要和善以及随和，积极的去和别人谈论一些有趣的话题，会让沟通变得十分的轻松，也会让你和异性之间拉近距离。',
                    '多学习一些跟异性沟通的方法，不仅仅是可以帮助搭讪异性，也会让你跟异性相处的时候更深得其喜欢，不会沟通的人，多半也不会说话，基本一说话就很让人反感，容易给人留下不好的印象。',
                    '虽然是单身状态，但是也不要到处撒网，很容易给人一种花心的感觉，异性可能会选择远离你，所以说也要带给周围的人一个好的形象，保持自己做人的原则，这样才能更获得异性的青睐。',
                    '女生随便打扮一下都非常的好看，所以千万不要觉得自己没有喜欢的人就不用打扮，总是不修边幅难以吸引异性，平时不妨多学习如何打扮，找到适合自己的穿衣风格，会让自己添加不少的魅力。',
                ],
            ],
        ];
        $sex = $this->lunar->sex;
        $listKey = $sex ? ['正官', '七杀'] : ['正财', '偏财'];
        $godTg = $this->lunar->getGod();
        $god = [
            $godTg, $this->getDzGodFirst(),
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $taohuaNumList = Ex::getFlower($jiNian, $sex);
        unset($taohuaNumList['流年桃花']);
        $taohuaNum = 0;
        foreach ($taohuaNumList as $v) {
            $taohuaNum += $v ? 1 : 0;
        }
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $key = '';
        $godCur = '';
        $keytg = 0;
        foreach ($god as $k => $v) {
            foreach ($v as $k1 => $v1) {
                if (in_array($v1, $listKey)) {
                    $key = $k1;
                    $godCur = $v1;
                    $keytg = $k;
                    break 2;
                }
            }
        }
        $listYinYang = [];
        foreach ($jiNian as $k => $v) {
            $listYinYang[$k] = BaziExt::getYinYang($v[0]);
        }
        // 判断用户八字中夫妻星（男看财星，女看官星，若没有则采用假借十神）
        if (empty($key)) {
            if ($sex) {
                // 若八字中无官星，则取月柱或时柱中与日元阴阳同性的一方为七杀，阴阳异性的一方为正官。若无同性或全是同性，则以月柱看七杀，时柱看正官。
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'hour' : 'month';
            } else {
                // 若八字中无财星，则取月柱或时柱中与日元阴阳同性的一方为偏财，阴阳异性的一方为正财。若无同性或全是同性，则以时柱看偏财，月柱看正财
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'month' : 'hour';
            }
        }
        $godCur = empty($godCur) ? $godTg[$key] : $godCur;
        $yongList = $this->getYongJiList();
        $isYong = in_array($godCur, $yongList) ? 1 : 0;
        $fu = $this->getJianNianShen($listKeyJi[$key], $keytg);
        $you = 0;
        if (($fu > 1 && $keytg = 0) || ($keytg && $fu)) {
            $you = 1;
        }
        $index = 0;
        if ($you) {
            $index = $isYong ? 0 : 3;
        } else {
            $index = $isYong ? 1 : 2;
        }
        $result = [];
        if ($taohuaNum > 4) {
            $result = $list[5][$index];
        } else {
            $result = $list[$taohuaNum][$index];
        }
        return [
            'info' => $result,
            'suggest' => $list2[$jiNian['d'][1]][$sex][$index],
        ];
    }

    /**
     * 单身原因
     * @return string
     */
    protected function getDanshenReason(): string
    {
        $list = [
            '甲' => [
                [
                    '你在感情当中显得有些自卑，缺乏了一些自信，尤其是在遇见心动的异性时，不会主动的去表达自己的好感，有时候会在心里就直接的否定了这一段感情，长期下去，你总会因此而难有展开新恋情的机会。',
                    '总是不太自信，表现的很自卑，难以在异性和喜欢的人面前展现自己的优点，遇见有喜欢的人也更不敢主动表明心意，而有喜欢自己的人，自己也不敢轻易的去接受，这样的你，想要迎接新恋情有点困难。',
                ],
                [
                    '在金钱上面有自己的一些支配，可能在其他人的眼里就会显得很节俭，会有被人说小气的情况，可是你认为在金钱支配上面还是不能过于随便，所以你也期待寻找一个跟你在这方面志同道合的人，或许有点困难。',
                    '别人无意之间的话或者是行为都容易遭到你的质疑，因为你有着其他的想法，所以会误会别人的意思，这样跟人交往很难亲近，别人难以靠近你，也难有异性对你产生好感，那自然是没有新的恋爱可以展开。',
                ],
                [
                    '你不喜欢被约束的感觉，害怕有一个人闯进你的生活，打乱你的计划，试图让你做出改变，对于爱情除了期待，更多的是局促不安。所以当找不到志同道合的人时，你不会做出任何行动，会认为一个人也挺好。',
                    '虽然你也期待爱情，但是你并不认为爱情就是生活的全部，当爱情没有到来的时候，你也能生活的很好，会将时间花费在工作上面，不会轻易和人暧昧，也不会有其他的想法，这样一来，自然难有脱单机会。',
                ],
                [
                    '你是一个不会掩饰自己感情的人，所以跟周围的人相处也会毫不犹豫的就展现自己的优点，遇见喜欢的人，更是会主动的去表达自己的感情，有时会因为过于直接而让对方受到惊吓遭到拒绝。',
                    '你是一个内心拥有傲气的人，所以你不会故意的说好话去讨好别人，在遇到喜欢的人时，也有可能出现咄咄相逼的情况，对方就会认为你很讨厌他，所以这也成为了你无法脱单的一个理由。',
                ],
            ],
            '乙' => [
                [
                    '可能和单身太久的关系也有一些原因，在你的身边难有让你信任的人，你有时会将这种感情寄托在现实之外的人或者是事物当中，这会让你不敢轻易接受别人，也对另外一半有着极高的标准。',
                    '什么事情你都习惯了自己去做，不是不信任别人，而是习惯了独立，但这会给别人一种你不信任对方的感觉，很难跟你之间建立起感情，有想要认识你的异性也会因此而疏远你，难有其他深入的交往。',
                ],
                [
                    '你深知自己身上的缺点，所以希望可以找一个各方面都优秀，能够跟自己完美契合的人，但是优秀的人看到你身上的缺点，肯定也是对你有意见的，想要寻找优秀的人，首先自己也得是一个优秀的人。',
                    '过于敏感的你，很容易就感受到对方对你的一些好感，在慌乱纠结的心情下会选择暂时躲避，故意和对方拉开一些距离，这样反而会引起一些误会，影响别人对你的关心，错过一些恋爱的机会。',
                ],
                [
                    '你很敏感，所以在和人的交往当中不敢轻易的信任别人，多少会让周围的人认为你难以相处，异性也会因此而疏远你，遇到喜欢的人，更是害怕被拒绝，被辜负，这会成为脱单中较大的阻碍。',
                    '在爱情中你会表现的比较被动，不会在异性面前主动的去展现自己的魅力，遇到自己喜欢的人更会选择避而不见，有一个能够发展的异性但是都可能会因为你的胆怯而错过这次恋爱的机会。',
                ],
                [
                    '在选择另外一半的时候，你可能会选择一个经济条件比较好的异性，如果成为上门女婿或到女方家乡发展也还好，但是其实你自己也并没有那么优秀，所以想要找到很优秀的一个伴侣，要两情相愿是难上加难。',
                    '在和周围人相处的时候，你总会盯着别人的缺点不放，所以当你发现你喜欢的人，或者是喜欢你的人有着一定的缺点之后，你就会非常排斥和对方的接触，容易错失很多的恋爱机会，是至今单身的一个原因。',
                ],
            ],
            '丙' => [
                [
                    '如果你清楚自己是一个不太主动的人，那么在恋爱上肯定就需要朋友以及同事的介绍来认识异性，但自己偶尔也需要主动一些，毕竟女生会因为你的不主动而缺乏安全感，对以后的恋爱发展来说也有一定的弊处。',
                    '你渴望爱情同时也害怕爱情，因为你本身希望的就是自由自在的生活，而多了一个人必定会有更多的唠叨，让你缺少了一定的空间。真正喜欢一个人就会因为关心而变得唠叨，这也是对方爱你的一种方式啊。',
                ],
                [
                    '你属于来者不拒，可能和单身的原因有关，会觉得是个异性都有可能发展成为恋人，但是你这样的举动很容易给别人造成你很花心的误会，所以即使是单身，在和异性交流的时候还是要有一个度较好。',
                    '你对自己有着苛刻的要求，所以对于另外一半也会有着很高的标准，和异性相处时总会先观察对方的缺点，你这样肯定会对身边的人都不满意，难以找到心仪的另外一半，要知道人无完人，没有人能够做到十全十美。',
                ],
                [
                    '你很看重情谊，尽管身边有着一些可以发展成为恋人的异性朋友，但是你很害怕因为你的告白，而使朋友之间的关系发生变化，所以你不敢轻易的去表白自己的心意。对于爱情还是要勇敢一些啊。',
                    '在感情当中你即使遇到可以发展的异性也不会表现的很主动，因此对方会有你对他不感兴趣的想法出现，继而错过了恋爱的机会。你很期待自己的爱情，但最大的阻碍就是缺乏一份开启恋情的勇气。',
                ],
                [
                    '你可能更希望寻找一个女强人来帮助自己，你害怕如果是普通的女生，可能过程的不怨和不满会让你们感到特别的疲惫，但是女强人恐不会那么温顺的在你身边协助你，要想找到跟你两情相愿的一方有点困难。',
                    '很多时候，你都只考虑自己的感受，忽略了周围人的想法，多少会让人觉得不讨喜，对你的印象也不是很好，自然难有开展新恋爱的机会，而有遇见自己喜欢的人，你也不太懂得如何去表现自己，机会也难以掌握。',
                ],
            ],
            '丁' => [
                [
                    '寻找另外一半时，你会显得比较被动，即使遇见了能让自己心动的人，也会无动于衷，有时，还会因为父母的一声令下而断绝所有的关系，可父母选择的也并不是你喜欢的，这样一来，自然也就难以脱单。',
                    '你希望得到的是简单而自由的爱情，即使恋爱，也能有自己的一个空间，但是爱情啊，总归会有一些束缚，喜欢你才会想要约束你，一直排斥这样的爱情，也会让你缺少一份展开新恋情的勇气。',
                ],
                [
                    '你是一个比较随心所欲的人，在选择另外一半的时候，会跟着感觉走的情况比较多，而这个感情有时还会随着心情的变化而变化，这样的你很容易因为这种感觉而错过合适的一些恋爱机会。',
                    '为了不给别人增加麻烦，你跟别人的交往中偶尔会有善意的谎言出现，不喜欢将所有事情的真相都告诉别人，但是当别人知道真相之后，可能就会对你造成一些误会，自然就难以建立信任，有更深的交往。',
                ],
                [
                    '在期待恋爱的同时，你却将自己的时间都给了工作，或许是你认为这样会让自己变得更加优秀，也或许是你本来就将爱情看得没有那么重要，但既然你想要脱单，至少也要做出一些行动才能有所收获。',
                    '你将工作看得十分的重要，所以很多时候你会逼着自己一直不断的前进，而在这个过程中，你已经没有更多的精力投入到爱情当中，所以爱情才会迟迟没有进展。想要爱情也有变化，工作上是要有一些取舍。',
                ],
                [
                    '你对另外一半的要求很高，这样也就无形当中限制了择偶的圈子，很多周围的异性也都变成了不可发展的状态，而想要找到一个让你真正满意的人也不会那么的容易，这也成为你至今没有脱单的一个阻碍。',
                    '你总是希望别人都能够按照你的意思去处理问题，未免过于自私，经常也会忽略别人的好意，不在乎别人的感受，这样人际关系会受到影响，异性缘也难以提高，很多对自己有好感的异性也有却而止步的情况。',
                ],
            ],
            '戊' => [
                [
                    '在和别人交往的时候，你总会莫名其妙的做一些不顾及别人想法的行为，会让人觉得你有些不理智。男生如果过于幼稚，女生会没有想要和你接触的想法，因为都希望有一个成熟的人来包容自己。',
                    '虽然你也渴望爱情，但是现阶段，对于工作也非常的重视，你把所有的时间都安排在了工作上，没有留给爱情，这样虽然会显得你很独立，很能干，但肯定是没有其他时间认识异性，有机会发展恋爱的。',
                ],
                [
                    '以自我为中心的人，难以听取别人的建议或者是劝告，有时因为不太考虑别人的感受，而有得罪人的行为出现，所以异性缘也会遭受影响。除非是有愿意包容和理解你的异性，否则很难开启新的恋爱。',
                    '对于另外一半，你有着自己的一个标准，并且你告诉自己一定要找一个这样的伴侣，少一项都不行，这样的固守观念会成为你感情上的阻碍，想要找到完全符合你心目中的类型会是一件比较困难的事情。',
                ],
                [
                    '你属于生活中比较热心肠的类型，有时会让周围的人觉得温暖，有时会认为这是一种负担，所以异性会觉得跟你在一起会很有压力，没有自己的太多空间，这也是造成你感情迟迟没有进展的原因。',
                    '你的心动来的很快去的也很快，所以你很容易喜欢一个人，但是随着时间的变化，你心里的感情也容易发生变化，你不断的变化就是希望新鲜感能够让生活变得比较刺激，但生活本来就是平淡乏味的。',
                ],
                [
                    '在感情当中，你很容易喜新厌旧，如果在你的身边出现了更加优秀的人，你可能就会更换目标，如此一来，给人留下的印象是非常的花心的，这样的状态如果持续维持，可能会让你很难脱单。',
                    '你很敏感，所以容易玻璃心，别人无意之间的一些话或者是行为会让你浮想联翩，让你没办法对人轻易的产生信任，周围的人跟你相处就会产生距离感，没办法了解你的优点，自然也没有被你吸引的地方。',
                ],
            ],
            '己' => [
                [
                    '当经历了一场被欺骗的爱情之后，你虽然渴望爱情的到来，同时也会对爱情产生一些恐惧，有时看到幸福的爱情故事也没有以前那么向往，你没有沉浸在过去难忘的回忆中，只是这让你缺乏了一些勇气去开启新的恋爱。',
                    '不争不抢的性格让你在爱情中少了一些自信，这让你不会主动的去认识异性，遇见喜欢的人，也不会主动的去接近别人，默默无闻的你反倒是成就了别人，而自己当然也是一直孤身的状态。',
                ],
                [
                    '你习惯了以感性的思维去判断事情，所以在爱情中也大都是会选择跟着自己的感觉走，因此你容易对异性心动，但爱情属于来的快走的也快，无法对一个人保持持续的新鲜感，会让异性无法在你身上找到安全感。',
                    '身边有能够发展为恋人的异性朋友，但是你还没有从上一段感情中走出来，对于爱情存在着一些害怕。你以为你已经忘记，但是其实你还是有那么一丝想念对方，没有勇气结束一段旧恋情，也会很难开启一段新的恋情。',
                ],
                [
                    '你不太照顾别人的感受，容易得罪人，所以身边的很多人都不太愿意跟你相处，也难有可以发展的异性朋友，而且你对于另外一半的要求也很高，有着各种各样的标准，难以开启新的恋情也是有理由的。',
                    '你希望自己的感情是比较刺激的，所以有时候你的感情来的快去得也很快，一份感情到底能够维持多久你自己也不知道，这样的你自然就难以找到真心的恋爱，还是尽量要自己保持初心去寻找爱情吧。',
                ],
                [
                    '你的异性缘不错，很能够吸引异性，但是桃花运太多也有一定的弊处，没办法给人足够的安全感，尽管你有喜欢的人，她也认为和你在一起十分的没有安全感，对待没法发展的桃花还是果断拒绝为好。',
                    '你在恋爱上面少了一些直觉，也不太懂得如何表达自己，所以在和周围的人相处时，和异性难有火花产生，遇到喜欢自己的异性，也会不知所措的就拒绝对方，这样一来，你就很容易错过一些可以恋爱的机会。',
                ],
            ],
            '庚' => [
                [
                    '喜欢挑战以及刺激，就总是会做一些别人难以理解的事情，所以也会让人觉得你捉摸不透，女生认为跟你在一起也难有安全感，自然也都没有想要跟你有恋爱的想法，既然想恋爱，就要给周围的人留下好印象才对。',
                    '你对于另外一半的要求很高，所以即使有人跟你表白，没有达到你的标准，你会果断的拒绝并且跟对方划开一定的界限，所以这也导致你看不上别人，别人也有疏远你的想法，自然也就一直处在单身的状态了。',
                ],
                [
                    '你期待爱情，但是同时你也不知道自己到底想要的是什么，感情中感到十分的迷茫。如果连自己想要什么都不知道，那么自然是不知道应该怎么去发展自己的恋情，不妨好好的想想其中的问题所在。',
                    '你有时会比较内敛，所以有些事情也不会轻易的和人诉说，周围的人会觉得难以跟你建立起信任，总存在着一些距离。这样也难有异性靠近你，自然也就无法开启新的恋情，自己能够做出一些变化会更好。',
                ],
                [
                    '很多时候你都不懂得顾及别人的感受，会导致很多的人不愿意和你相处，身边难有发展的异性，而你也对于另外一半的要求很高，认为对方一定要达到自己的各项标准，所以才难展开新的恋情。',
                    '你知道并不是所有的爱情都可以美满幸福，而你却希望自己能够找到一份这样的爱情，所以你不会轻易的开启一场恋爱，在感情当中表现的比较理智，你所认为的没有发展的恋情，一开始就会拒绝接触。',
                ],
                [
                    '性格过于强势，所以很多时候都不会太在乎别人的感受，比较我行我素，如果遇到自己喜欢的人，更是将自己想要表达的想法强加给别人，这反而是容易让别人误会自己的好意，会成为脱单路上的一个阻碍。',
                    '你比较喜欢宅在家里，不太喜欢到处游玩，所以缺少很多认识异性的机会，也更没有机会发展新的恋爱。经常宅在家里肯定是没有办法认识异性，还是要出去参加一些聚会，才有能够认识异性的机会。',
                ],
            ],
            '辛' => [
                [
                    '不太懂得怎么去表达自己，也不会轻易的对人敞开心怀，别人也很难能够读懂你内心真实的想法，所以在跟你的交往当中也不会有太深的接触，这会让你错过一些恋爱机会，难以开启新的恋爱。',
                    '即使是身边亲密的人，心里面有什么想法也不会轻易的告诉对方，周围的人会认为你难相处，捉摸不透，就会自动的疏远你，难有想要恋爱的想法。这也就导致了你迟迟没有新的恋情产生。',
                ],
                [
                    '对于爱情你还是显得有些保守和不自信，所以在和人的交往当中也不会主动的去表达好感，有时反而还会疏远和大家的交往，有些让周围的人觉得难以亲近，是开启新恋爱中的最大阻碍。',
                    '比较敏感的你，可能会因为周围人无意的话以及行为而浮想联翩，自然也就难以跟周围的人有过多的交往，更没有勇气去开展一段新的恋爱，你渴望爱情的到来，同时却缺乏主动跨出第一步的勇气。',
                ],
                [
                    '你说话比较直接，有时候会是让人比较伤心的话，而自己却不知道，所以你也会得罪不少的异性，异性缘不太好。对你有好感的人，有时更会因为你无心的话给伤害，想要脱单还是要注意些较好。',
                    '你比较爱玩，但其实并不花心，也没有做特别过分的事情，只是周围的异性会认为你很不安定，很花心，所以你身边的异性不敢轻易的对你说出有好感之意，除非是对你有了深入的了解之后。',
                ],
                [
                    '虽然你也期待爱情的到来，但是更多的时候还是觉得随缘就好，属于佛系脱单的状态，认为一个人生活也可以过得很好，正因为你这样不积极的态度，就很容易错过和异性发展的机会，无法开展新的恋爱。',
                    '其实你身边不乏可以发展的异性，只是你对于另外一半的要求很高，所以遇见不太让你满意的异性你并不想有任何的接触，甚至是有些排斥，有喜欢你的异性更不敢轻易的对你表达好感。',
                ],
            ],
            '壬' => [
                [
                    '有时候你会觉得别人很好，但有时你又认为别人不够理想，在反复纠结的状态下，最后还是会认为别人跟自己的理想类型不一样。看谁都不满意的状态归根究底还是要求太高，这让你难以轻易的展开新的恋情。',
                    '总觉得身边的人接近你都有着一定的目标，难以轻易的去相信别人，这会让你和异性的相处出现问题，从另外一方面来说，即使遇见了让你喜欢的异性，你也没有足够的勇气去接近对方，去开展新的恋爱。',
                ],
                [
                    '你的情绪变换的很快，很容易因为生活中的一件小事而翻脸，这让很多人都觉得捉摸不透，不知道你什么时候就会爆发出不好的情绪，这自然也会吓走不少的异性，错失一些可以发展新恋爱的机会。',
                    '别人的情绪在很多时候都能够影响到你的情绪，所以一旦周围的人告诉你谈恋爱很累的时候，你心里多少也会有些排斥爱情，但是当一个人需要面对所有的困难时，你又开始觉得多一个人的陪伴会更好。',
                ],
                [
                    '在感情上你比较大男子主义，基本上都是说一不二，很难听取别人的建议和想法，但是现在的女性多半都是比较独立以及有主见的，有时也会比较强势，而遇到同样强势的男生肯定是没有太多的好感。',
                    '你的性格比较我行我素，会经常鲁莽的采取一些别人所不能理解的行动，而且也经常有得罪别人的情况发生，容易给人留下幼稚，任性，性格不好的印象，难有让异性心动，有想要了解你的想法。',
                ],
                [
                    '遇到事情你容易欠缺考虑，所以你也很容易将自己的恋爱机会推到千里之外，不管是遇到喜欢自己的人，还是自己喜欢的人，都应该理智的考虑之后再给出答案，不要总是做一些让人无法理解的事情。',
                    '你在感情上面表现的不是很自信，不懂得主动的去表明自己的心意，遇见喜欢的人也是在背后默默的喜欢，有时还会越喜欢反而远离别人，而别人就还会有你排斥他的想法，这会让你错过很多恋爱机会。',
                ],
            ],
            '癸' => [
                [
                    '影响你脱单的原因是因为太爱面子，因此害怕被拒绝之后的难堪，不愿意主动的去表达自己的心意，也更不会厚脸皮的去关心别人，这样别人难以感受到你的心意，自然你也无法开展新的恋情。',
                    '一旦发现别人的缺点就会接受不了，而且还会有排斥的心理出现，你这样会让人觉得十分的不礼貌，人都是不完美的，就算是你也存在着一些缺点，你如此追求完美自然无法找到能够心仪的另外一半。',
                ],
                [
                    '生活中的很多事情你都希望按照自己的想法进行，不太能够接受别人的建议，有点太不顾及周围人的感受，有时得罪人却不自知，人际关系会受到一些影响，自然异性缘也不是很好，对脱单有一些影响。',
                    '难有亲密的人，不容易和别人产生信任，那么在和异性交往的时候也会让对方感到有距离，所以也并没有想要跟你继续交往的想法，要想开启新的恋爱，自己可能也是需要做出一些改变才好。',
                ],
                [
                    '你是比较敏感的人，别人的一些不经意的话容易对你造成一些影响，所以你和周围人的相处总是存在着一些距离感，异性也不敢轻易的靠近你，你心中有所想法也不会主动的去靠近别人，想要脱单自然会有些困难。',
                    '你在感情上面比较理智，爱情和面包，会果断的选择面包，更看重的是对方的物质条件，这样一来你的择偶标准也会非常的高，周围难有人能够达到你的标准，这也成为了你一直单身的原因。',
                ],
                [
                    '你的人缘还算不错，身边可发展异性很多，只是桃花旺的时候，总是会有烂桃花接踵而至，而你如果跟这些烂桃花太过纠缠，就很容易错过好桃花，反而是得不偿失，该拒绝就要果断拒绝。',
                    '其实你身边能有可以发展的异性，只是你碍于面子，不会主动的表达自己的感情，也不会主动的去表白自己的心意，如此一来，你可能会因此而错过一些合适的恋爱机会，也是你至今还没脱单的一个较大阻碍。',
                ],
            ],
        ];
        $sex = (int)$this->lunar->sex;
        $listKey = $sex ? ['正官', '七杀'] : ['正财', '偏财'];
        $god = $this->lunar->getGod();
        $godDz = $this->lunar->_getGod();
        $key = '';
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $dayTg = $jiNian['d'][0];
        $listYinYang = [];
        foreach ($jiNian as $k => $v) {
            $listYinYang[$k] = BaziExt::getYinYang($v[0]);
        }
        foreach ($god as $k => $v) {
            if (in_array($v, $listKey)) {
                $key = $k;
                break;
            }
        }
        // 1无 0有
        $fuIndex = empty($key) ? 1 : 0;
        // 判断用户八字中夫妻星（男看财星，女看官星，若没有则采用假借十神）
        if (empty($key)) {
            if ($sex) {
                // 若八字中无官星，则取月柱或时柱中与日元阴阳同性的一方为七杀，阴阳异性的一方为正官。若无同性或全是同性，则以月柱看七杀，时柱看正官。
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'hour' : 'month';
            } else {
                // 若八字中无财星，则取月柱或时柱中与日元阴阳同性的一方为偏财，阴阳异性的一方为正财。若无同性或全是同性，则以时柱看偏财，月柱看正财
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'month' : 'hour';
            }
        }
        $you = 1;
        $key1 = $listKeyJi[$key];
        if (in_array($jiNian[$key1][0], $godDz[$key]['hide'])) {
            $you = 0;
        }
        if ($fuIndex) {
            $index = $you ? 0 : 3;
        } else {
            $index = $you ? 1 : 2;
        }
        return $list[$dayTg][$index][$sex];
    }

    /**
     * 脱单建议
     * @return array
     * @throws Exception
     */
    protected function getTuodanSuggest(): array
    {
        $list = [
            '甲' => [
                '遇到喜欢的人，没有办法主动的去表达自己的感情，很容易就会错过，所以如果真的遇见了心动的异性时，就主动的大胆的去表白吧，就算结果不会都是尽人意的，但至少你将这份爱意说出了口。',
                '你要试着敞开心扉去相信别人，不要总是对周围的一切疑神疑鬼，这样将会没有人走进你的心里，也没有人愿意走进你的生活，所以有什么想法不妨和大家一起商量，多给彼此一些了解对方的机会。',
            ],
            '乙' => [
                '有什么事情你不妨和周围的人多商量，一起解决，多信任别人，才能增加彼此的信任以及相处，这样才能够让周围的异性有多了解你的一些机会，也能给自己增加一些脱单的机会。',
                '你应该积极的去和周围的人相处，有喜欢的人时，主动大胆的表达爱意，而有人愿意接近你，并对你表达一定的好感时，你也可以先试着去了解一下，不要逃避或者是直接拒绝，反而引起异性的反感。',
            ],
            '丙' => [
                '如果遇见了喜欢的人，有好感的人，一定要主动的去增加两人的相处机会，积极的去表达自己的感情，过于被动，那么别人会感受不到你的感情，有时候往往就会造成彼此错过，作为男生，你需要让自己更加的主动。',
                '对于另外一半的要求你或许要稍微降低一些标准，毕竟你要清楚人无完人，就算是你，也有着一定的缺点，所以少关注对方的缺点，多关注对方的优点，说不定会让你有些意外的惊喜。',
            ],
            '丁' => [
                '在感情中，你需要增强自己的主见以及自信，既然是自己喜欢的人，就不要通过别人的嘴巴去了解对方，也不要因为周围人的一些看法而影响了你心中的感情，相信自己与对方的相处中所产生的判断。',
                '你在和别人交谈的时候总是呈现了太多的谎言，很难建立起一定的信任，所以你需要变得更加的诚实以及真诚，在和异性交往的时候，不要总是巧言令色，不妨直接的诚实的表达想法，反而更让人喜欢。',
            ],
            '戊' => [
                '喜欢对方就不要给对方一种你讨厌她的信号，做一些让人觉得莫名其妙的事情，你想掩盖自己对她的喜欢，反而弄巧成拙让她误会，既然喜欢她，当然就要做一些让她觉得温暖的事情，这样才能吸引到对方的注意呢。',
                '有时候在一起的人往往和自己的理想标准有着很大的出入，所以你不能够认定一个标准就无法接受其他的类型，能够符合并且让你遇见的是少之又少，张开怀抱，多期待一些其他类型的交往吧。',
            ],
            '己' => [
                '你首先要明白不可能碰见一个喜欢的人就会一辈子在一起，结婚生孩子，永远都幸福，有的时候总会发生一些意想不到的变故，而我们依然应该对爱情充满期待，毕竟遇见了错的人才能和对的人相遇。',
                '你不能再继续沉浸在过去的回忆当中了，不管你因为之前的感情受到了伤害，还是日常生活听多了一些悲惨的爱情故事，你都要对爱情充满期待和希望，相信甜甜的恋爱一定会来到你的身边。',
            ],
            '庚' => [
                '要给予异性足够的安全感，不要做一些让她觉得莫名其妙的事情，小心适得其反，反而深受到异性的讨厌。因此有的时候你需要安分守己一些，不要过于冒险去挑战一些东西，反而更让人喜欢。',
                '心里有什么想法一定要大胆的表达出来，不然的话别人不知道你在想什么，无法和你产生信任，就会拒绝和你的相处，尤其是有喜欢的人时，更不要藏在心里什么都不说，至少也要有一些表现去吸引对方的注意。',
            ],
            '辛' => [
                '你要学会去积极的表达自己，遇到自己喜欢的人主动的去表达心意，或者是做一些能够让对方可以对自己产生好感的行为，而遇到喜欢自己的人，也不要禁闭心门，有时候不妨试着相处一下，说不定还会有不错的结局。',
                '少想多做是你需要做出的改变，别人的一些不经意的话或者是不经意的行为，就当做没有发生好了，不要胡思乱想，不要冲动的做出一些后悔的行为，这样你才不会有另外的想法而拒绝和异性的交往。',
            ],
            '壬' => [
                '你需要对自己自信一些，或许你也拥有着一些缺点，但是人无完人，每个人都有自己的缺点，所以你要更加自信的展现自己的优点，当你变得自信后，异性才能看到你的优点，对你有着很好的印象。',
                '在感情当中，就没有不吵架，不分手，不发生矛盾的爱情，所以即使你的身边存在很多不美好的爱情，你也依然要对爱情拥有期待，这样才能让你对爱情更加主动一些，也能拥有不少脱单机会。',
            ],
            '癸' => [
                '放下面子，去做一些能够让对方感到好感的行为，去积极的表达自己的感情，才能够让对方感受到你的心意，而和你的相处也会变得很有安全感，深深的被你吸引，从而展开一段美好的恋爱。',
                '既然对爱情充满着期待，那么就不要把这种感情寄托在不现实的人身上，不妨敞开心扉，建立和周围人的信任，这样才容易了解别人，找到自己喜欢的人，也或者是有一些喜欢自己的人。',
            ],

        ];
        $list1 = [
            '子' => [
                [
                    '你的体质有点属于偏招渣体质，会有被渣女纠缠的情况，这个时候你需要果断的去拒绝对方，如果她还依然死皮赖脸的纠缠着你，你不妨做出一些让对方伤心的事情，对方自然就会寻找下一个目标了。',
                    '你遇到渣男的概率较大，因此在交友过程当中，有异性经常都会在你的周围吐槽他的前任，说着前任的一些坏话，那么和这种男生一定不要有感情上的纠纷，或许是个好朋友，但一定不是个好男友。',
                ],
                [
                    '你遇见渣女的可能性较小，但是对于渣女还是不得不防，毕竟她们非常的厚脸皮，对你的躲闪都会采取视而不见继续骚扰的状态，那么你一定要在第一时间就表示拒绝，认真严肃的表明自己的态度。',
                    '喜欢你的男生都是愿意为你花钱的，所以如果你遇见了总是要你买单，而且还每次都会计较和你aa的男生，你千万不要自作主张的认为对方是拥有不乱花钱，很节俭的品质，他是因为觉得你不值得让他付出这么多。',
                ],
            ],
            '丑' => [
                [
                    '你有遇见渣女的可能，尤其是那种喜欢认年长的帅哥为哥哥，年小的为弟弟，有着非常帅的男闺蜜的女生，是你最没有可能驾驭住的，她基本上都是因为想暧昧没有合理的借口，而彼此亲近达到感情上的纠葛。',
                    '你在感情当中很有可能会遇见渣男，尤其是对于甜言蜜语你毫无招架之力，所以当身边出现了情商很高，很会说甜言蜜语的男生，你一定要在花言巧语当中保持冷静，仔细的去辨别他们口中的真假。',
                ],
                [
                    '虽然你遇见渣女的可能性较小，但是还是要注意防范渣女，毕竟渣女都非常的厚脸皮，一开始对你的拒绝可能都不会有所反应，还会继续保持不依不饶的态度，你就可能要做一些过分的事情来让对方意识到你的介意。',
                    '你遇见渣男的可能较小，但也不能因此掉以轻心。面对男人，你一定要有一个意识，男人的话，听听就好，重点是要用眼睛去看，他的行为是否和他说的一样。只会说却少有行动的男人是没办法走心的。',
                ],
            ],
            '寅' => [
                [
                    '你有遇见渣女的可能，尤其是那种对外宣称单身，却总有约不完的会，跟一堆异性有着线上线下的频繁互动，但是从来没有一个名正言顺的男友，你可千万不要单纯的认为对方就是单身，其实有着很多暧昧的对象。',
                    '你可能会遇见渣男，或许次数还比较多。一个男人，如果身边的朋友都认为他很好，只有你觉得他很傻，那他十有八九是个好男人，而如果他身边的朋友都认为他不好，只有你觉得他对你好，那他十有八九就是一个渣男。',
                ],
                [
                    '你遇见渣女的可能较小，但也不能忽视对渣女的防范，遇见渣女的纠缠，最好是一开始就果断的拒绝，但某些渣女不会选择退缩，而是越挫越勇，那么你就要表明你真的很讨厌她，并删掉所有联系方式，离她远远的。',
                    '你遇见渣男的几率较少，但也要具备一些辨别渣男的能力，最简单的就是当周围的人都对这个男人评价极差，认为这个男人是渣男的时候，你千万还不要认为别人都是在挑拨离间，这个男人十有八九就是个渣男。',
                ],
            ],
            '卯' => [
                [
                    '你会有遇见渣女的可能，或许对方在你面前表现的非常温柔以及斯文，但是这都是她所拥有的面具，在别人的眼中她就是泼妇，也一点都不温柔，所以要相处之前，不妨多去了解一些对方。',
                    '你遇见渣男的概率很大，要知道渣男口才都很好，生气的时候立马就能哄你开心，总能在他的嘴里听见你想要的话，而你往往听到这些话就无法自拔，所以千万要擦亮眼睛，不要被迷惑，珍爱生命，远离渣男。',
                ],
                [
                    '你遇见渣女的概率较小，但也不能因此忽视，如果遇见了那种不懂得拒绝的女生，不管是对你的讨好还是对其他男生的讨好都来者不拒，那么这种女生多半都是把你们当做了备胎，等需要你们时，就要你们倾尽全力的为她付出所有。',
                    '你遇见渣男的机会不多，但是人际圈中如果有出现异性朋友很多，并且还经常对异性都异常关心的男人，那么也要少和这样的男人有感情的纠纷，多半这个男人是属于中央空调类型的渣男，他还拥有着很多的备胎。',
                ],
            ],
            '辰' => [
                [
                    '你遇见渣女的概率较大，无视你对她的好，无视你对她的温柔，还心安理得的收下你的馈赠，享受呼之即来，挥之即去的便利，玩腻了，或者有新欢再一脚踢开你，你一定要远离这种不懂得珍惜的女生。',
                    '你有遇见渣男的可能，尤其是对每个人都很好的中央空调，虽然有时他的关心确实让你感觉很温暖，但是你要清楚，他并不是只对你一个人这么多，他的关心是很泛滥的，你要是因此而对他产生了好感，那就完蛋了。',
                ],
                [
                    '你遇见渣女的概率较小，从一开始就会避免接触她们，不给渣女机会，但她们的阴谋伎俩还是不得不防，将自己伪装的十分单纯，善于掩饰自己的情绪，你很有可能就会被迷惑，所以一定要有敏锐的洞察力，一旦发现，坚决远离。',
                    '虽然你遇见渣男的可能性不大，但是在生活当中还是要避免和渣男的接触，如果你的生活当中有打女人的男人，那么这种男人千万不要有过多的交往，更不要说恋爱，小心之后会有家暴的倾向。',
                ],
            ],
            '巳' => [
                [
                    '你有遇见渣女的可能，所以当你看见周围的某个女生身边有着很多的异性围绕时，那么你就要远离这样的女生，她可能在私底下不止跟一个男生暧昧，她的目的就是很享受这种被异性簇拥的虚荣感。',
                    '以你热情交友的性格，有很大概率是能够遇见渣男的，所以当有渣男开始纠缠你的时候，一定要明确的表达他没有希望，自己不可能喜欢他，因为一旦你果断的拒绝了，他就不好意思再继续厚脸皮的纠缠着你。',
                ],
                [
                    '你遇见渣女的可能较小，但是也不能掉以轻心，渣女经常都会伪装成另外一个你喜欢的样子，很容易陷入其中，所以要知道，那种你捉摸不透，总拥有自己独立和私人空间的女生，很能可能在私底下还拥有各种各样的约会。',
                    '你遇见渣男的可能性不大，但是生活中还是要少受花言巧语的诱惑，很多男生都擅长将事情说的天花乱坠，会给你很多美好的幻想，这种男生基本就是见一个爱一个，遇到了就要赶紧离开。',
                ],
            ],
            '午' => [
                [
                    '你遇见渣女的概率很大，所以一定要谨慎严防，如果有那种异性朋友很多，还经常跟异性有着过分亲密举动的女生，你千万不要因为她对你的某些温柔就陷入其中，她可能对另外的男生更加的温柔。',
                    '你很有可能会遇上被渣男纠缠的情况，这个时候一定不要优柔寡断，因为你一不留神就容易陷入他们的陷阱。他们一般都是知难而退，你直接拒绝，可能就会主动放弃了，当然有些厚脸皮的可能还需要你主动删除好友。',
                ],
                [
                    '你遇见渣女的可能性不大，但也需要有一定的堤防。身边里的女生经常和男生称兄道弟，勾肩搭背，某些男生就会以为是直率，但其实这样的女生是来者不拒，多半都是同性缘较差，异性缘很好的渣女。',
                    '尽管遇见渣男的几率不大，但是你也不能轻视，对于那种认识不久，但是却说很喜欢你，对你特别温柔体贴的男生要有戒备之心，大家都才认识不久，就如此深情，说明他的爱来的快，走的也会很快。',
                ],
            ],
            '未' => [
                [
                    '你会一不小心陷入渣女的圈套，所以千万不要精虫上脑，那种认识不久就说喜欢你，要跟你上床的女人，她们多半都是上床之后就跟你提条件，或者对你有什么要求，她们在感情中多半难以付出自己的真心。',
                    '你是有可能被渣男纠缠的，这时，你就要做一些让对方讨厌的事情，比如天天不修边幅，故作邋遢，拜金，花掉他所有的钱，在他朋友面前羞辱他，不给他面子。这一系列行为出来他估计也是不用你说也会离开你了。',
                ],
                [
                    '你有遇见渣女的可能，但概率不大，不过也要多加重视，身边如果有频繁换男友，和异性之间爱开玩笑，相处太过于愉快的女生，那么一定不是人际关系好，而是她很爱撩异性，从另外一方面来说，十有八九就是个渣女。',
                    '你遇见渣男的可能较小，但是辨别渣男也不可不重视，如果你的身边有一个经常都很忙，行踪飘忽的男人，那么一定要小心谨慎的相处，或许他是真的忙，或许他是忙着泡其他妹子。',
                ],
            ],
            '申' => [
                [
                    '你要仔细辨别身边的渣女，会有很大几率能和渣女相遇。一般，渣女身边的同性朋友都非常的少，因为大多数的渣女异性缘非常的好，同性缘却差到极点，毕竟女孩子才更了解自己的同类。',
                    '你可能会遇见渣男。日常生活中，一定要远离那种拥有很多社交网站账号的异性，一般这种异性都很会撩妹，在各大社交网站都会有那么几个红颜知己，这就说明他嘴上尽管说着喜欢你，心里却装着不少的异性。',
                ],
                [
                    '你还是有遇见渣女的可能，日常生活当中如果有女生的朋友圈底下会有很多的异性跪舔，或者是无脑称赞，那么说明这个女生多半都是渣女，在私底下经常都会撩异性，足够了解后再相处比较好。',
                    '你遇见渣男的可能性较小，但是情感江湖，也有不慎入陷阱的时候，所以你要睁大了眼睛，远离身边花言巧语，最爱说甜蜜话的男生，他永远都知道你愿意听的是什么，但他也知道其他女生愿意听什么。',
                ],
            ],
            '酉' => [
                [
                    '你会有遇见渣女的可能，因此要多听听周边大多数女生的意见，因为一般女人更了解自己的同类，所以说她们知道哪些女人是装的，千万不要认为这是嫉妒的表现，多听听其他女人的意见准没错。',
                    '你会有被渣男缠住的可能，这个时候你可以寻找一个非常熟悉的异性朋友保护，不管去哪里，都和他一起，说两个人正在进一步交往当中，并且警告渣男不要再找你，要知道渣男一般都是知难而退，适当放弃。',
                ],
                [
                    '你有遇见渣女的可能，因此还是要有一定的防范，渣女都擅长于伪装自己，小心陷入套路，遇见跟异性玩的很好，而且有着较多感情经历，却每段感情都不长的女生，一定要远离，以及及时的拒绝，这样的女生多半对于感情都不太认真。',
                    '你遇见渣男的可能性不大，但是如果不小心陷入了渣男的陷阱，也要懂得及时的说不，如果一直优柔寡断，渣男就会得寸进尺，认为你这样的软柿子非常好捏，及时的拒绝会让对方知难而退。',
                ],
            ],
            '戌' => [
                [
                    '你会有和渣女遇见的可能，绝大部分的渣女都习惯来伪装自己，将自己诉说的非常的可怜以达到让你有保护的欲望，要知道这都是对方在自导自演，遇见无法看透，难以看透的女生，最好是多了解后再相处。',
                    '你有着很大的概率遇见渣男，所以在生活当中一定要小心进入渣男的陷阱，尤其是一些渣男很喜欢吹牛，总是把自己的身世，自己的阅历吹得多么厉害，吸引别人的注意，这种男生十有八九都非常的花心。',
                ],
                [
                    '你遇见渣女的概率较小，但是也不能掉以轻心，当你跟一个女生暧昧的时候，你发现她还在跟其他的异性暧昧，那么这个女生十有八九就是个渣女了，多半都是把你们当做了备胎，在有需要的时候能拥有你们的帮助。',
                    '你遇见渣男的可能性不大，但也要小心谨慎的辨别周围的渣男。渣男一般都是出场自带着光芒，在他的吹嘘下你会认为他十分的优秀，但其实这都是他演出来的，没有过多的了解不要轻易接受对方，小心他是一匹披着羊皮的狼。',
                ],
            ],
            '亥' => [
                [
                    '你会有遇见渣女的可能，要想获得真爱，那么就要远离渣女，不要和渣女有过多的纠葛，如果渣女仍然继续纠缠着你，你就要在她纠缠你的时候不与之进行任何的交往，只要你不要再做回应，她肯定会知难而退的。',
                    '你会有和渣男产生孽缘的可能，本来两个人的状态已经是非常的暧昧，但是对方却迟迟没有表白的意思，这个时候你就要果断的远离对方了，因为他压根就没有和你长远的发展，你只是他众多知己中的一个罢了。',
                ],
                [
                    '你有遇见渣女的可能，因此要小心生活当中的渣女，一般渣女都是物以类聚，尽管她隐藏的很好，但通过她身边的朋友也可以看出她的一些人品，如果她身边都是爱玩，穿着暴露，且异性朋友较多的朋友，多半她也是渣女了。',
                    '你能够和渣男遇见的概率不大，但是也要小心提防着伪装的渣男，一般的渣男都非常的死皮赖脸，因此生活当中，遇见这种极端，不达目的就死皮赖脸缠着不放的男生，要有一个正确的认知。',
                ],
            ],
        ];
        $lanFlowerList = $this->getLanFlower();
        $lanFlowerList = array_sum($lanFlowerList);
        $sex = $this->lunar->sex;
        $jiNian = $this->lunar->getLunarTganDzhi();
        $result = [
            $list[$jiNian['d'][0]][$sex],
        ];
        if ($lanFlowerList > 2) {
            $result[1] = $list1[$jiNian['d'][1]][0][$sex];
        } else {
            $result[1] = $list1[$jiNian['d'][1]][1][$sex];
        }
        return $result;
    }

    /**
     * 另一半
     * @return array
     */
    protected function getLiYiBan(): array
    {
        // 正官 +日支（0喜神 1忌神）+0男 1女
        $list = [
            '正官' => [
                [
                    '你命中的伴侣漂亮端庄，气质不俗，在为人出事上往往能够做到面面俱到，而且在面对诱惑时，能够紧守本心。',
                    '你命中的伴侣形象阳光开朗，热情大方，遇事冷静理智，明辨是非，恪守本分，能够脚踏实地，踏踏实实地做事。',
                ],
                [
                    '你命中的伴侣行事有点保守，而且过于敏感的心思，造成了时常疑神疑鬼，情绪波动大，遇事总往坏的方面想。',
                    '你命中的伴侣刻板严肃，凡事优柔寡断，畏首畏尾，总想着有充足的把握在出手，不够果决，容易错失良机，导致一事无成。',
                ],
            ],
            '七杀' => [
                [
                    '你命中的伴侣性情颇为豪迈，而且善良正直，在遇到弱者有困难的时后能够伸出援助之手，做事职责有当担。',
                    '你命中的伴侣志向远大，积极进取，能够随机应变，面对困难挫折，能够不屈不挠，勇往直前，不达目的决不罢休。',
                ],
                [
                    '你命中的伴侣个性较为偏激，容易走极端，行事风风火火，经常会有钻牛角尖的时候，只能看你自己多担待了。',
                    '你命中的伴侣个性刚强，倔强叛逆，性子有有些急躁，行事过于霸道，不讲情面，常常遭遇挫折，需要付出更大的努力。',
                ],
            ],
            '正印' => [
                [
                    '你命中的伴侣贤惠善良，乐观开朗，在待人接物上进退得体，追求精神上的满足，能够积极充实自己。',
                    '你命中的伴侣性格老成，稳重老实，面对困难挫折，也能不卑不亢，心态平和，能够逆来顺受，有着宽广的心胸。',
                ],
                [
                    '你命中的伴侣个性聪慧天真，但做事缺乏耐心，又喜欢出风头，常常虎头蛇尾，会不断掩饰自己的过错来维护形象。',
                    '你命中的伴侣个性老实敦厚，不善言辞，不善察言观色，缺乏随机应变的能力，对于别人所表达的意思通常要时候才能反应过来，进取心较弱。',
                ],
            ],
            '偏印' => [
                [
                    '你命中的伴侣精明干练，机智善辩，擅长推理，能够闻一知十，举一反三，在企划或是创作上很有天赋。',
                    '你命中的伴侣性格沉稳内向，思维敏捷，领悟能力强，凡事能够举一反三，遇事能够冷静理智地处理，注重行事的效率。',
                ],
                [
                    '你命中的伴侣性情孤独冷漠，不解人意，过于阴沉偏激的性情有时会过于自私自利，而不顾及他人的感受，缺乏能够真正交流沟通的朋友。',
                    '你命中的伴侣个性轻浮，会过高地看重自己的能力，做事三心二意，过于草率，缺乏耐心，多谋少成，有时过于刻薄。',
                ],
            ],
            '比肩' => [
                [
                    '你命中的伴侣个性积极，理性主动，做事有责任心，能够有始有终，但有时会与他人合不来，较为孤僻。',
                    '你命中的伴侣性情刚毅稳健，果断坚忍，能够严格要求自己，任劳任怨，不贪非分之功，固守本分，是一个意志坚定的人。',
                ],
                [
                    '你命中的伴侣个性自私，自顾自己，不顾他人，凡事缺乏主动，不会主动与他人打交道，所以与他人合不来，容易形成孤僻。',
                    '你命中的伴侣个性孤僻，缺乏毅力，遇事犹豫不决，消极被动，又缺乏交际手段，目光短浅，凡事不易成功。',
                ],
            ],
            '劫财' => [
                [
                    '你命中的伴侣个性活泼，善于交际。凡事古道热肠，乐于助人，能够见义勇为，不畏惧艰险困难。',
                    '你命中的伴侣志向高远，性格坚毅，不畏艰险，有坚韧奋斗的精神，凡事积极进取，为人古道热肠，能主动帮助别人解决问题。',
                ],
                [
                    '你命中的伴侣个性天真，有时会过于自以为是，做事欠缺思考，总是盲目行动，往往是成事不足败事有余。',
                    '你命中的伴侣喜欢冒险，凡事急功近利，喜欢走捷径，缺乏思考，做事乱冲乱撞，喜欢用武力来解决问题。',
                ],
            ],
            '食神' => [
                [
                    '你命中的伴侣性格温柔，善解人意，多才多艺，待人亲切，不喜欢受到传统的约束，敢于追求自我价值。',
                    '你命中的伴侣个性温文儒雅，待人宽容厚道，思想超脱，思虑周详，能够充分考虑到他人的感受，会有着许多的朋友，深得大家喜爱。',
                ],
                [
                    '你命中的伴侣个性固执，表里不一， 浮华不实，追求表面风光，缺少进取之心，会不断地来掩饰自己。',
                    '你命中的伴侣个性冷漠，不解人意，有时过于固执，不听他人意见，假道斯文，容易忽略现实生活，而陶醉在自我世界中。',
                ],
            ],
            '伤官' => [
                [
                    '你命中的伴侣聪明伶俐，活泼善辩，机智善变，有着强大的荣誉感，做事认真积极，有使命感，期望得到别人的肯定。',
                    '你命中的伴侣英明睿智，机智善变，深富谋略，才华横溢，雄心万丈，积极进取，追求卓越的荣誉感，渴望得到他人的认同和赞善。',
                ],
                [
                    '你命中的伴侣性格乖戾，叛逆偏激，爱慕虚荣，凡事逞强好胜，为了结果不择手段，言辞刻薄，容易惹事上身。',
                    '你命中的伴侣个性高傲，持才傲物，刚愎自用，做事我行我素，性格乖张，容易得罪他人，招惹是非。',
                ],
            ],
            '正财' => [
                [
                    '你命中的伴侣勤劳节俭，不喜欢投机事项，守信本分，性格温柔亲切，重视家庭，有责任心，能够知足常乐，生活安稳。',
                    '你命中的伴侣忠厚老实，勤劳朴素，凡事任劳任怨，做事踏实稳重，按部就班，爱护家庭，衣食丰足，懂得量力而为，温和保守，追求稳定发展，生活安泰。',
                ],
                [
                    '你命中的伴侣贪图享受，不求上进，安于现状，吝啬寡情，与人斤斤计较，缺乏通融，做事容易消极怠惰，不够积极。',
                    '你命中的伴侣好逸恶劳，贪图享受，容易不求上进，安于现状，行事魄力不足，没有耐心，常常虎头蛇尾，容易因小失大，得不偿失。',
                ],
            ],
            '偏财' => [
                [
                    '你命中的伴侣慷慨多情，能歌善舞，多才多艺，重友执宜，乐于助人，圆滑机智权通变达，乐观进取，不畏困难，富于交际手腕，能够把握机会。',
                    '你命中的伴侣机智灵敏，精明能干，敏捷效率，处事圆滑变通，善于交际，与人交往真诚坦率，热心豪爽，善于把握机会，生平多机缘巧遇。',
                ],
                [
                    '你命中的伴侣贪慕虚荣，奢侈浪费，投机取巧，行为浮华，欠缺责任感，喜欢玩乐，会因此而巧言欺诈。',
                    '你命中的伴侣言行不一，缺乏责任，做事马虎，喜钻捷径，风流成性，玩世不恭，举止轻浮，花言巧语，常有意外耗财，缺乏家庭责任，喜留恋声色犬马，有巧言诈欺之毛病。',
                ],
            ],
        ];
        // 日支+用户性别
        $list1 = [
            '正官' => [
                '她的身高大约在158～162公分左右，身材还算标准。',
                '他的身高大约在168～172公分，标准身材。',
            ],
            '七杀' => [
                '她的身高约162公分以上，骨架较大、体型挺拔。',
                '他的身高约175公分以上，体格高大壮硕。',
            ],
            '正印' => [
                '她的身高约156～160公分，体型不会太瘦。',
                '他的身高约168～172公分，体型不会太瘦。',
            ],
            '偏印' => [
                '她的身材就有点极端了，若不是高高瘦瘦型，就一定是矮胖型，而且是胖妞的机率非常高。',
                '他的身材就有点极端了，若不是高高瘦瘦型，就一定是矮胖型。',
            ],
            '比肩' => [
                '她的身高约158～162公分，也有可能更高些，而且身材大都是肉肉的。',
                '他的身高约170～175公分左右，也有可能更高些，而且身材大都是肉肉的。',
            ],
            '劫财' => [
                '她的身高约164公分以上，举止动作少了一些斯文气质。',
                '他的身高约175公分以上，偶尔会有脾气火爆的一面。',
            ],
            '食神' => [
                '她的身高约162公分以上，体格很有可能是壮硕型的胖妹。',
                '他的身高约175公分以上，体格很有可能是壮硕型的胖哥。',
            ],
            '伤官' => [
                '她的身高约156～160公分，当财运不错时，很难控制其食欲及体重。',
                '他的身高约170～175公分左右，当财运不错时，很难控制其食欲及体重。',
            ],
            '正财' => [
                '她的身高比较难以捉摸，不过很少是既矮又瘦的，也就是说大都会属于比较有肉型的体格。',
                '他的身高比较难以捉摸，不过很少是既矮又瘦的，也就是说大都会属于比较有肉型的体格。',
            ],
            '偏财' => [
                '她的身材基本属于标准衣架子型，不会太胖也不会太瘦。',
                '他的身材基本属于标准衣架子型，不会太胖也不会太瘦。',
            ],
        ];
        // 日柱+性别
        $list2 = [
            '甲' => ['土', '金'],
            '乙' => ['土', '金'],
            '丙' => ['金', '水'],
            '丁' => ['金', '水'],
            '戊' => ['水', '木'],
            '己' => ['水', '木'],
            '庚' => ['木', '火'],
            '辛' => ['木', '火'],
            '壬' => ['火', '土'],
            '癸' => ['火', '土'],
        ];
        // 相扶
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $wuXingAttr = $this->lunar->wuXingAttr;
        $jiNian = $this->lunar->getLunarTganDzhi();
        $_god = $this->lunar->_getGod();
        $godName = $_god['day']['god'][0];
        // 喜神、用神、仇神、忌神、闲神 五行
        $yongList = $this->getYongJiList();
        $index = in_array($godName, $yongList) ? 0 : 1;
        $sex = $this->lunar->sex;
        $result = [
            $list[$godName][$index][$sex],
        ];
        $result[] = $list1[$godName][$sex];
        $dWx = $list2[$jiNian['d'][0]][$sex];
        $monthWx = $wuXingAttr[$jiNian['m'][1]];
        if (in_array($monthWx . $dWx, $xianfuList)) {
            $result[] = '身体状况还算不错，少生病，';
        } else {
            $result[] = '身体状况只能算是一般，总会有这样那样的毛病，';
        }
        $yonglist = $this->getYongJiList();
        $_god = $this->lunar->_getGod();
        $godDayDz = $_god['day']['god'][0];
        $result[] = in_array($godDayDz, $yonglist) ? '将来多能富贵，为你带来不少益处。' : '将来对你的帮助有限，能否富贵还得看付出的努力。';
        return $result;
    }

    /**
     * 烂桃花
     * @return array
     * @throws Exception
     */
    protected function getLanFlower(): array
    {
        $lunar = $this->lunar->getLunarByBetween();
        // 年日时支
        $dzYdh = $lunar['jinian']['y'][1] . $lunar['jinian']['d'][1] . $lunar['jinian']['h'][1];
        // 年支	日支	月支	时支
        $dzYdmh = $lunar['jinian']['y'][1] . $lunar['jinian']['d'][1] . $lunar['jinian']['m'][1] . $lunar['jinian']['h'][1];
        $res = '';
        $listLan = [
            0, 0, 0, 0, 0,
        ];
        // 桃花被冲
        if (in_array($dzYdh, ['子酉卯', '丑午子', '寅卯酉', '卯子午', '辰酉卯', '巳午子', '午卯酉', '未子午', '申酉卯', '酉午子', '戌卯酉', '亥子午'])) {
            $listLan[0] = 1;
        }
        // 桃花被刑
        if (in_array($dzYdh, ['子酉酉', '丑午午', '寅卯子', '卯子卯', '辰酉酉', '巳午午', '午卯子', '未子卯', '申酉酉', '酉午午', '戌卯子', '亥子卯'])) {
            $listLan[1] = 1;
        }
        //桃花争合
        if (in_array($dzYdmh, ['子酉辰辰', '丑午未未', '寅卯戌戌', '卯子丑丑', '辰酉辰辰', '巳午未未', '午卯戌戌', '未子丑丑', '申酉辰辰', '酉午未未', '戌卯戌戌', '亥子丑丑'])) {
            $listLan[2] = 1;
        }
        // 4、桃花带劫
        $tmp = ['甲乙', '乙甲', '丙丁', '丁丙', '戊己', '己戊', '庚辛', '辛庚', '壬癸', '癸壬'];
        foreach ($lunar['jinian'] as $k => $v) {
            if ($k == 'd' || !in_array($v[1], ['子', '午', '卯', '酉'])) {
                continue;
            }
            $tmpTg = $v[0] . $lunar['jinian']['d'][0];
            if (in_array($tmpTg, $tmp)) {
                $listLan[3] = 1;
                break;
            }
        }
        // 5、桃花带杀
        $tmp = ['甲庚', '乙辛', '丙壬', '丁癸', '戊甲', '己乙', '庚丙', '辛丁', '壬戊', '癸己'];
        foreach ($lunar['jinian'] as $k => $v) {
            if ($k == 'd' || !in_array($v[1], ['子', '午', '卯', '酉'])) {
                continue;
            }
            $tmpTg = $v[0] . $lunar['jinian']['d'][0];
            if (in_array($tmpTg, $tmp)) {
                $listLan[4] = 1;
                break;
            }
        }
        return $listLan;
    }

    /**
     * 纪年扶助
     * @param string $kk
     * @param int $pos 位置 0天干 1地支
     * @return int
     */
    protected function getJianNianShen(string $kk, $pos = 0): int
    {
        $listKey = [
            'y' => ['m'],
            'm' => ['y', 'd'],
            'd' => ['m', 'h'],
            'h' => ['d'],
        ];
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wuXingAttr = $this->lunar->wuXingAttr;
        $jiNianWx = [];
        foreach ($jiNian as $k => $v) {
            $jiNianWx[$k] = [
                $wuXingAttr[$v[0]], $wuXingAttr[$v[1]],
            ];
        }
        $index = 0;
        $wx = $jiNianWx[$kk][$pos];
        foreach ($listKey[$kk] as $v) {
            $tmpwx = $wx . $jiNianWx[$v][$pos];
            if (in_array($tmpwx, $xianfuList)) {
                $index++;
            }
        }
        if ($pos == 0 && in_array($wx . $jiNianWx[$kk][1], $xianfuList)) {
            $index++;
        }
        return $index;
    }

    /**
     * 十神在八字中是否扶助
     * @param $godName
     * @return bool
     * @throws Exception
     */
    protected function getGodShen($godName): bool
    {
        $listKey = [
            'y' => ['m'],
            'm' => ['y', 'd'],
            'd' => ['m', 'h'],
            'h' => ['d'],
        ];
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $god = $this->lunar->getGod();
        $_god = $this->getDzGodFirst();
        $jiNianWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $jiNianWx[$k] = [
                $wuXingAttr[$v[0]], $wuXingAttr[$v[1]],
            ];
        }
        $indexTg = array_search($godName, $god);
        if ($indexTg) {
            $indexTg = $listKeyJi[$indexTg];
            foreach ($listKey[$indexTg] as $v) {
                if (!in_array($jiNianWx[$indexTg][0] . $jiNianWx[$v][0], $xianfuList)) {
                    continue;
                }
                return true;
            }
        }
        $indexdz = array_search($godName, $_god);
        if ($indexdz) {
            $indexdz = $listKeyJi[$indexdz];
            foreach ($listKey[$indexdz] as $v) {
                if (!in_array($jiNianWx[$indexdz][1] . $jiNianWx[$v][1], $xianfuList)) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 获得地支的第一个
     * @return array
     */
    private function getDzGodFirst()
    {
        $_god = $this->lunar->_getGod();
        return [
            'year' => $_god['year']['god'][0],
            'month' => $_god['month']['god'][0],
            'day' => $_god['day']['god'][0],
            'hour' => $_god['hour']['god'][0],
        ];
    }

    /**
     * 获得当前用户用神或忌神list
     * @param $yong 1输出用list 0代表忌
     * @return array
     */
    protected function getYongJiList($yong = 1): array
    {
        $yonglist = [
            ["正官", "七杀", "正财", "偏财", "食神", "伤官"],
            ["正印", "偏印", "比肩", "劫财"],
        ];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wuDu = BaziExt::getWangDu($jiNian);
        $index = 0;
        if (in_array($wuDu, ['身旺格', '从弱格'])) {
            $index = $yong ? 0 : 1;
        } else {
            $index = $yong ? 1 : 0;
        }
        return $yonglist[$index];
    }

    /**
     * 获得邂逅的年份
     * @return array
     * @throws Exception
     */
    private function getXieHou(): array
    {
        $Lunar = $this->lunar;
        $lunar = $Lunar->getLunarByBetween();
        $sex = $Lunar->sex;
        $year = (int)date('Y', strtotime($this->orginData['otime']));
        $res = $this->getYearLiuNian($lunar['jinian']['d'][0], $sex, $year);
        $res = array_merge($res, $this->getYearByHong($lunar['jinian']['y'][1], $year));
        $res = array_merge($res, $this->getYearByRz($lunar['jinian']['y'][1], $year));
        $res = array_merge($res, $this->getYearByHe($year));
        $res = array_unique($res);
        sort($res);
        return $res;
    }

    /**
     * 根据年份获得年份
     * @param string $rg 日干
     * @param int $sex 性别
     * @param int $year 起始年份
     * @return array
     */
    private function getYearLiuNian($rg, $sex, $year): array
    {
        $list = [
            [
                '甲' => ['戊', '己'],
                '乙' => ['戊', '己'],
                '丙' => ['庚', '辛'],
                '丁' => ['庚', '辛'],
                '戊' => ['壬', '癸'],
                '己' => ['壬', '癸'],
                '庚' => ['甲', '乙'],
                '辛' => ['甲', '乙'],
                '壬' => ['丙', '丁'],
                '癸' => ['丙', '丁'],
            ],
            [
                '甲' => ['庚', '辛'],
                '乙' => ['庚', '辛'],
                '丙' => ['壬', '癸'],
                '丁' => ['壬', '癸'],
                '戊' => ['甲', '乙'],
                '己' => ['甲', '乙'],
                '庚' => ['丙', '丁'],
                '辛' => ['丙', '丁'],
                '壬' => ['戊', '己'],
                '癸' => ['戊', '己'],
            ],
        ];
        $selectTg = $list[$sex][$rg];
        $res = [];
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $year + $i;
            $tmpYearGz = BaziExt::getGanZhi($tmpYear);
            if (in_array($tmpYearGz[0], $selectTg)) {
                $res[] = $tmpYear;
            }
        }
        return $res;
    }

    /**
     * 八字中遇到引动夫妻宫，逢六合、三合配偶宫、五合日主时
     * @param $year
     * @param int $isDaYun 是否判断大运
     * @return array
     * @throws Exception
     */
    private function getYearByHe($year, $isDaYun = 0): array
    {
        $Lunar = $this->lunar;
        $lunar = $Lunar->getLunarByBetween();
        // 日地支
        $rdz = $lunar['jinian']['d'][1];
        // 日天干
        $rtg = $lunar['jinian']['d'][0];
        $res = [];
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $year + $i;
            $tmpYearGz = BaziExt::getGanZhi($tmpYear);
            if ($isDaYun) {
                if ($this->daYunYear($tmpYear) == false) {
                    continue;
                }
            }
            // 地支六合
            if (in_array($rdz . $tmpYearGz[1], ['子丑', '寅亥', '卯戌', '辰酉', '巳申', '午未'])) {
                $res[] = $tmpYear;
            } elseif (in_array($rdz . $tmpYearGz[1], ['子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥'])) {
                $res[] = $tmpYear;
            } elseif (in_array($rtg . $tmpYearGz[0], ['甲己', '乙庚', '丙辛', '丁壬', '戊癸'])) {
                $res[] = $tmpYear;
            } else {
                if (in_array($rdz, ['申', '子', '辰'])) {
                    if (in_array($tmpYearGz[1], ['申', '子', '辰'])) {
                        $res[] = $tmpYear;
                    }
                } elseif (in_array($rdz, ['巳', '酉', '丑'])) {
                    if (in_array($tmpYearGz[1], ['巳', '酉', '丑'])) {
                        $res[] = $tmpYear;
                    }
                } elseif (in_array($rdz, ['寅', '午', '戌'])) {
                    if (in_array($tmpYearGz[1], ['寅', '午', '戌'])) {
                        $res[] = $tmpYear;
                    }
                } else {
                    if (in_array($tmpYearGz[1], ['亥', '卯', '未'])) {
                        $res[] = $tmpYear;
                    }
                }
            }
        }
        return $res;
    }

    /**
     * @param string $rz 日支
     * @param int $year
     * @return array
     */
    private function getYearByRz(string $rz, $year): array
    {
        $res = [];
        $yearZhi = '';
        if (in_array($rz, ['申', '子', '辰'])) {
            $yearZhi = '酉';
        } elseif (in_array($rz, ['寅', '午', '戌'])) {
            $yearZhi = '卯';
        } elseif (in_array($rz, ['亥', '卯', '未'])) {
            $yearZhi = '子';
        } else {
            $yearZhi = '午';
        }
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $year + $i;
            $tmpYearGz = BaziExt::getGanZhi($tmpYear);
            if ($tmpYearGz[1] == $yearZhi) {
                $res[] = $tmpYear;
            }
        }
        return $res;
    }

    /**
     * 命逢红鸾星和天喜星时
     * @param string $yz 年支
     * @param int $year 起始年份
     * @return array
     */
    private function getYearByHong(string $yz, int $year): array
    {
        $list = [
            '子' => ['卯', '酉'],
            '丑' => ['寅', '申'],
            '寅' => ['丑', '未'],
            '卯' => ['子', '午'],
            '辰' => ['亥', '巳'],
            '巳' => ['戊', '辰'],
            '午' => ['酉', '卯'],
            '未' => ['申', '寅'],
            '申' => ['未', '丑'],
            '酉' => ['午', '子'],
            '戌' => ['巳', '亥'],
            '亥' => ['辰', '戌'],
        ];
        $selectList = $list[$yz];
        $res = [];
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $year + $i;
            $tmpYearGz = BaziExt::getGanZhi($tmpYear);
            if (in_array($tmpYearGz[1], $selectList)) {
                $res[] = $tmpYear;
            }
        }
        return $res;
    }

    /**
     * 根据年份所在的大运时间判断
     * @param int $year
     * @return bool
     * @throws \Exception
     */
    private function daYunYear(int $year): bool
    {
        $res = false;
        $Lunar = $this->lunar;
        $sex = $Lunar->sex;
        $sexList = [
            ['正财', '偏财'],
            ['正官', '七杀'],
        ];
        $sexChoose = $sexList[$sex];
        $daYunList = $Lunar->getFate()['eight'];
        foreach ($daYunList['year'] as $k => $v) {
            if (($v > $year) && ($k > 0)) {
                if (in_array($daYunList['_god'][($k - 1)], $sexChoose)) {
                    $res = true;
                }
                break;
            }
        }
        return $res;
    }

    /**
     * 脱单时间
     * @return array
     * @throws Exception
     */
    protected function getTuoDanTime(): array
    {
        $xieHouList = $this->getXieHou();
        $list = [
            '子' => [[7, 3], 12],
            '丑' => [[4, 8], 11],
            '寅' => [[5, 9], 10],
            '卯' => [[6, 10], 9],
            '辰' => [[7, 11], 8],
            '巳' => [[8, 12], 7],
            '午' => [[1, 9], 6],
            '未' => [[2, 10], 5],
            '申' => [[3, 11], 4],
            '酉' => [[4, 12], 3],
            '戌' => [[1, 5], 2],
            '亥' => [[2, 6], 1],
        ];
        $tuoDan = [];
        foreach ($xieHouList as $k => $v) {
            if ($k > 1) {
                break;
            }
            $tmpJiNian = BaziExt::getGanZhi($v);
            $tmpMonth = $list[$tmpJiNian[1]];
            $count = count($tuoDan);
            $dif = $count % 2;
            if ($dif) {
                $tuoDan[] = "{$v}年{$tmpMonth[0][0]}月";
                $tuoDan[] = "{$v}年{$tmpMonth[0][1]}月";
            } else {
                $tuoDan[] = "{$v}年{$tmpMonth[1]}月";
            }
        }
        return $tuoDan;
    }

    /**
     * 不利时间
     * @return array
     * @throws Exception
     */
    protected function getBuliTime(): array
    {
        $kk = $this->getJiNianKey();
        $jiNian = $this->lunar->getLunarTganDzhi();
        $time = strtotime("{$this->orginData['otime']} -5 year");
        $startYear = date('Y', $time);
        $result = [];
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $startYear + $i;
            $tmpJiNian = BaziExt::getGanZhi($tmpYear);
            // 相冲、相破、相害、相刑
            if (BaziExt::getXianChongDz($jiNian[$kk][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianPoDz($jiNian[$kk][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianXinDz($jiNian[$kk][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianHaiDz($jiNian[$kk][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
        }
        return $result;
    }

    /**
     * 不稳时间
     * @return array
     */
    protected function getBuWenTime(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $time = strtotime("{$this->orginData['otime']} +6 year");
        $startYear = (int)date('Y', $time);
        $result = [];
        for ($i = 0; $i < 10; $i++) {
            $tmpYear = $startYear + $i;
            $tmpJiNian = BaziExt::getGanZhi($tmpYear);
            // 相冲、相破、相害、相刑
            if (BaziExt::getXianChongDz($jiNian['d'][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianPoDz($jiNian['d'][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianXinDz($jiNian['d'][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
            if (BaziExt::getXianHaiDz($jiNian['d'][1], $tmpJiNian[1])) {
                $result[] = $tmpYear;
                continue;
            }
        }
        return $result;
    }

    /**
     * 获得夫妻星所在柱名
     * @return string
     */
    private function getJiNianKey(): string
    {
        $sex = (int)$this->lunar->sex;
        $listKey = $sex ? ['正官', '七杀'] : ['正财', '偏财'];
        $god = $this->lunar->getGod();
        $key = '';
        $listKeyJi = ['year' => 'y', 'month' => 'm', 'day' => 'd', 'hour' => 'h'];
        $jiNian = $this->lunar->getLunarTganDzhi();
        $listYinYang = [];
        foreach ($jiNian as $k => $v) {
            $listYinYang[$k] = BaziExt::getYinYang($v[0]);
        }
        foreach ($god as $k => $v) {
            if (in_array($v, $listKey)) {
                $key = $k;
                break;
            }
        }
        // 判断用户八字中夫妻星（男看财星，女看官星，若没有则采用假借十神）
        if (empty($key)) {
            if ($sex) {
                // 若八字中无官星，则取月柱或时柱中与日元阴阳同性的一方为七杀，阴阳异性的一方为正官。若无同性或全是同性，则以月柱看七杀，时柱看正官。
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'hour' : 'month';
            } else {
                // 若八字中无财星，则取月柱或时柱中与日元阴阳同性的一方为偏财，阴阳异性的一方为正财。若无同性或全是同性，则以时柱看偏财，月柱看正财
                $key = $listYinYang['m'] == $listYinYang['h'] ? 'month' : 'hour';
            }
        }
        $kk = $listKeyJi[$key] ?? 'y';
        return $kk;
    }
}
