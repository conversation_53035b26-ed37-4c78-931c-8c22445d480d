<?php
/**
 * Xingpan.星盘算法 1.0
 * User: Administrator Author:<EMAIL>
 * Date: 2018/10/28 Time: 15:37
 */

namespace app\controller\v1;

use app\lib\astro\AstrologyUtil;
use app\lib\astro\Chart;
use app\traits\CityLnglatTraits;
use app\traits\xp\XpResTraits;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Validate;

class Xingpan
{
    use CityLnglatTraits;
    use XpResTraits;

    /**
     * 行星落点
     * @var array
     */
    protected array $planet = [];

    /**
     * @var AstrologyUtil
     */
    protected AstrologyUtil $astroUntil;

    /**
     * @var Chart
     */
    protected Chart $chartLib;

    /**
     * @var array
     */
    protected array $hc = [];

    /**
     * 星盘内容
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 时间
            'd' => input('date', '', 'trim'),
            'time' => input('time', '12:00', 'trim'),
            'timezone' => input('timezone', 8, 'intval'),
            'province' => input('province', '', 'trim'),
            'city' => input('city', '', 'trim'),
            'h_sys' => input('hsys', 'p', 'trim,strtolower'),

        ];
        $validate = Validate::rule(
            [
                'd|出生日期' => ['require', 'dateFormat:Y-m-d'],
                'time|时间' => ['require', function ($data) {
                    if (strtotime("2000-1-1 {$data}")) {
                        return true;
                    }
                    return '时间格式不正确';
                }],
                'timezone|时区' => ['require'],
                'province|省份' => ['require'],
                'city|城市' => ['require'],
                'h_sys|宫盘' => ['require'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        // 获得经纬度
        $lngLat = $this->getlngLat($data['province'], $data['city']);
        // $lngLat=['lng'=>118.08948,'lat'=>24.47951];
        // $lngLat=['lng'=>121.46667, 'lat'=>31.23333];
        $astroUntil = new AstrologyUtil($data['d'], $data['time'], $lngLat['lng'], $lngLat['lat']);
        $this->astroUntil = $astroUntil;
        $hc = $astroUntil->getHouseCup();
        $this->hc = $hc;
        $planet = $astroUntil->getPlanet();
        $this->planet = $astroUntil->getPlanetInfo();
        $chartLib = new Chart($planet, $hc);
        $this->chartLib = $chartLib;
        $resPlanet = [];
        $hourseNum = $astroUntil->getHourseNum();
        $plInXz = [];
        $plInHourse = [];
        foreach ($planet as $k => $v) {
            $planetList = AstrologyUtil::$planet[$v['planet']];
            $signKey = array_search($v['sign'], AstrologyUtil::$zodiaSignEnS);
            $signInfo = AstrologyUtil::$zodiacSign[$signKey];
            $tmpDegs = (int)($v['degs'] - $hc[0]['degs'] + 360) % 360;
            $hs = $this->getHsNum($tmpDegs, $hourseNum);
            // 行星落在星座中的结果
            $plInXzTmp = $this->getStarInXz($planetList[3], $signInfo[5]);
            $plInHourseTmp = $this->getStarInHouse($planetList[3], $hs);
            if (!empty($plInXzTmp)) {
                $plInXz[] = [
                    'pl' => $planetList[3],
                    'sign' => $signInfo[5],
                    'info' => $plInXzTmp,
                ];
            }
            if (!empty($plInHourseTmp)) {
                $plInHourse[] = [
                    'pl' => $planetList[3],
                    'hs' => $hs,
                    'info' => $plInHourseTmp,
                ];
            }
            $resPlanet[] = [
                'hs' => $hs,
                'deg' => $v['deg'],
                'min' => $v['min'],
                'sec' => $v['sec'],
                'planet' => [
                    'cn' => $planetList[3],
                    'glyph' => $planetList[1],
                    'en' => $planetList[2],
                ],
                'sign' => [
                    // ['Aries', 0, 'red', '♈', "AR", "白羊座"]
                    'cn' => $signInfo[5],
                    'en' => $signInfo[0],
                    'glyph' => $signInfo[3],
                    'en_s' => $signInfo[4],
                    'number' => $signInfo[1],
                ],
                'degs' => $v['degs'],
                'sui' => $v['sui'],
            ];
        }
        $heXian = $this->getHeXian();
        $four = $this->getFourHeXian();
        $hxDetail = $this->getHeXianInfo($heXian, $four);
        $res = [
            'planet' => $resPlanet,
            'hc' => $hc,
            'attr' => $astroUntil->getPlanetAttr(),
            'gong' => $chartLib->getcenterInCircleDeg(),
            'chart' => $chartLib->getChart(),
            'hexian' => $heXian,
            'four' => $four,
            'pl_in_xz' => $plInXz,
            'pl_in_hou' => $plInHourse,
            'hx_detail' => $hxDetail,
        ];
        return $res;
    }

    /**
     * 相位信息
     * @return array
     */
    protected function getHeXian(): array
    {
        $list = ['合', '六合', '刑', '拱', '冲'];
        $list1 = [0, 60, 90, 120, 180];
        $planetList = AstrologyUtil::$planet;

        $planet = $this->getPlanetDeg();
        unset($planet['Node']);
        //$planet = array_column($planet, 2);
        $result = [];
        foreach ($planet as $k => $v) {
            foreach ($planet as $k1 => $v1) {
                if ($k1 == $k) {
                    continue;
                }
                $tmpDeg = $this->getDegDiffHalf($v[2], $v1[2]);
                $key = $this->getXianWeiKey($tmpDeg);
                if ($key < 0) {
                    continue;
                }
                $result[$k][$k1] = [
                    'title' => $list[$key],
                    'deg' => $list1[$key],
                ];
                $result[$k1][$k] = [
                    'title' => $list[$key],
                    'deg' => $list1[$key],
                ];
            }
        }
        $result2 = [];
        foreach ($result as $k => $v) {
            $tmpPlanet = $planetList[strtolower($k)];
            $info = [];
            foreach ($v as $k1 => $v1) {
                $tmpPlanet1 = $planetList[strtolower($k1)];
                $info[] = [
                    'star' => [
                        $tmpPlanet1[1], $tmpPlanet1[2], $tmpPlanet1[3],
                    ],
                    'name' => $v1,
                ];
            }
            $result2[] = [
                'star' => [$tmpPlanet[1], $tmpPlanet[2], $tmpPlanet[3]],
                'info' => $info,
            ];
        }
        return $result2;
    }

    /**
     * 获得行星角度
     * @return array
     */
    protected function getPlanetDeg(): array
    {
        $planet = [];
        $hc = $this->hc[0];
        foreach ($this->planet as $v) {
            // $tmpDeg = (($v['hs'] - 1) * 30 + round(($v['deg'] + $v['min'] / 60 + $v['sec'] / 3600)) + 360) % 360;
            $tmpDegs = (int)($v['degs'] - $hc['degs'] + 360) % 360;
            $planet[$v['planet']['en']] = [
                $v['planet']['en'], $v['sign']['en'], $tmpDegs,
            ];
        }
        return $planet;
    }

    /**
     * 四个点的信息
     * @return array
     */
    protected function getFourHeXian(): array
    {
        $gong = $this->chartLib->getcenterInCircleDeg();
        $hc = $this->hc;
        $first = $hc[0]['degs'];
        $listFour = [
            'ASC' => [
                $gong[0], abs((int)($first - $hc[0]['degs'] + 360) % 360), ['en' => 'ASC', 'cn' => '上升'],
            ],
            'IC' => [
                $gong[3], (360 - abs((int)($first - $hc[3]['degs'] + 360) % 360)), ['en' => 'IC', 'cn' => '天底'],
            ],
            'DES' => [
                $gong[6], abs((int)($first - $hc[6]['degs'] + 360) % 360), ['en' => 'DES', 'cn' => '下降'],
            ],
            'MC' => [
                $gong[9], (360 - abs((int)($first - $hc[9]['degs'] + 360) % 360)), ['en' => 'MC', 'cn' => '天顶'],
            ],
        ];
        $list = ['合', '六合', '刑', '拱', '冲'];
        $list1 = [0, 60, 90, 120, 180];
        $planet = $this->getPlanetDeg();
        unset($planet['Node']);
        $planetList = AstrologyUtil::$planet;
        $result = [];
        foreach ($listFour as $k => $v) {
            $result[$k] = [
                'title' => $v[2],
                'sign' => $v[0],
                'info' => [],
            ];
            foreach ($planet as $k1 => $v1) {
                if ($k1 == $k) {
                    continue;
                }
                $tmpDeg = $this->getDegDiffHalf($v[1], $v1[2]);
                $key = $this->getXianWeiKey($tmpDeg);
                if ($key < 0) {
                    continue;
                }
                $tmpPlanet = $planetList[strtolower($k1)];
                $result[$k]['info'][] = [
                    $tmpPlanet[1], $tmpPlanet[2], $tmpPlanet[3],
                    $list[$key],
                    $list1[$key],
                ];
            }
        }
        return $result;
    }

    /**
     * 求角度相差半圆
     * @param $deg1
     * @param $deg2
     * @return int
     */
    protected function getDegDiffHalf($deg1, $deg2): int
    {
        $deg = (int)($deg1 - $deg2 + 360) % 360;
        if ($deg > 180) {
            $deg = 360 - $deg;
        }
        return $deg;
    }

    /**
     * 判断是否符合条件
     * @param int $diff
     * @return int
     */
    private function getXianWeiKey(int $diff)
    {
        $result = -1;
        if ($diff <= 4 && $diff >= 0) {
            $result = 0;
        } elseif ($diff >= 57 && $diff <= 62) {
            $result = 1;
        } elseif ($diff >= 87 && $diff <= 93) {
            $result = 2;
        } elseif ($diff >= 117 && $diff <= 123) {
            $result = 3;
        } elseif ($diff >= 177 && $diff <= 183) {
            $result = 4;
        }
        return $result;
    }

    /**
     * 行星在哪个宫
     * @param mixed $degs 角度
     * @param array $arr
     * @return int
     */
    private function getHsNum($degs, $arr)
    {
        $ret = 1;
        foreach ($arr as $k => $v) {
            if ($degs >= $v[0] && $degs < $v[1]) {
                $ret = $k;
                break;
            }
        }
        return $ret;
    }
}
