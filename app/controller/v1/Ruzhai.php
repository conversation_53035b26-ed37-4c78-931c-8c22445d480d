<?php
// +----------------------------------------------------------------------
// | 入宅吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\SxBase;
use app\lib\new2021\facade\BaziCommon;
use app\traits\DateConvertTraits;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Ruzhai
{
    use JiRiCheckTraits;
    use DateConvertTraits;

    /**
     * 日历基础
     * @var Ex
     */
    protected Ex $lunar1;

    /**
     * @var Ex|null
     */
    protected ?Ex $lunar2 = null;

    /**
     * 订单时间
     * @var Ex
     */
    protected Ex $lunar0;

    /**
     * 要筛选几个月
     * @var int
     */
    protected int $withinMonth = 3;

    /**
     * 节气数据
     * @var array
     */
    protected array $jieQiYear = [];

    /**
     * 搬家吉日
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time1' => input('time1', '', 'trim'),
            // 出生时间2
            'time2' => input('time2', '', 'trim'),
            // 订单时间
            'otime' => input('otime', '', 'trim'),
            // 默认3个月
            'longs' => input('longs', 3, 'intval'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time1|出生时间1' => ['require', 'isDateOrTime:出生时间1'],
                'time2|出生时间2' => ['isDateOrTime:出生时间2'],
                'otime|订单时间' => ['require', 'isDateOrTime:订单时间'],
                'longs|时间区间' => ['require', 'between:1,25'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->withinMonth = $data['longs'];
        $this->lunar1 = Ex::date($data['time1']);
        if (!empty($data['time2'])) {
            $this->lunar2 = Ex::date($data['time2']);
        }
        $this->lunar0 = Ex::date($data['otime']);
        $bazi = [
            $this->getBazi($this->lunar1),
        ];
        if (!empty($this->lunar2)) {
            $bazi[] = $this->getBazi($this->lunar2);
        }
        $result = [
            'bazi' => $bazi,
            'nodz' => $this->getNodz(),
            'jiri' => $this->getRiqi1(),
        ];
        return $result;
    }

    /**
     * @param Ex $lunar
     * @return array
     * @throws Exception
     */
    protected function getBazi($lunar): array
    {
        if (empty($lunar)) {
            return [];
        }
        $base = $lunar->getLunarByBetween();
        $res = [
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 地势
            'terrain' => $lunar->getTerrain(),
        ];
        $res = array_merge($base, $res);
        $res['chong'] = [
            $this->getChongDz($base['jinian']['y'][1]), $this->getChongDz($base['jinian']['d'][1]),
        ];
        $res['xin'] = [
            $this->getXianXinDz($base['jinian']['y'][1]), $this->getXianXinDz($base['jinian']['d'][1]),
        ];
        return $res;
    }

    /**
     * 1、去除日支相冲、日支相刑、岁破、清明、七月半
     * @return array
     * @throws Exception
     */
    protected function getRiqi1(): array
    {
        // 岁破
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        $listJieSha = [
            '寅亥' => '亥', '卯申' => '甲申', '辰巳' => '丁巳', '巳寅' => '庚寅', '午亥' => '辛亥', '未申' => '甲申',
            '申巳' => '巳', '酉寅' => '庚寅', '戌亥' => '辛亥', '亥申' => '甲申', '子巳' => '丁巳', '丑寅' => '庚寅',
        ];
        $listZaiSha = [
            '寅子' => ['壬子', '丙子'], '卯酉' => [], '辰午' => ['壬午'], '巳卯' => ['辛卯', '乙卯'],
            '午子' => [], '未酉' => ['己酉'], '申午' => ['戊午', '壬午'], '酉卯' => [], '戌子' => ['丙子'],
            '亥酉' => ['乙酉', '己酉'], '子午' => [], '丑卯' => ['乙卯'],
        ];
        $listYueSha = [
            '寅丑' => [], '卯戌' => ['甲戌'], '辰未' => [], '巳辰' => [],
            '午丑' => [], '未戌' => [], '申未' => [], '酉辰' => ['庚辰', '丙辰'],
            '戌丑' => [], '亥戌' => [], '子未' => [], '丑辰' => [],
        ];
        $listYueXing = [
            '寅巳' => ['丁巳', '辛巳'], '卯子' => [], '辰辰' => ['壬辰'], '巳申' => ['庚申'],
            '午午' => [], '未丑' => [], '申寅' => [], '酉酉' => ['乙酉'],
            '戌未' => ['辛未'], '亥亥' => ['乙亥', '己亥'], '子卯' => [], '丑戌' => ['庚戌'],
        ];
        $listYueYan = [
            '寅戌' => [], '卯酉' => [], '辰申' => ['壬申'], '巳未' => ['辛未', '乙未'], '午午' => ['甲午'],
            '未巳' => [], '申辰' => [], '酉卯' => [], '戌寅' => ['丙寅'], '亥丑' => ['乙丑', '己丑'],
            '子子' => ['甲子'], '丑亥' => [],
        ];
        $listDaShi = [
            '寅卯' => '卯', '卯子' => '', '辰酉' => '酉', '巳午' => '午', '午卯' => '辛卯', '未子' => '甲子', '申酉' => '酉',
            '酉午' => '庚午', '戌卯' => '卯', '亥子' => '子', '子酉' => '丁酉', '丑午' => '庚午',
        ];
        $listTianLi = [
            '寅酉' => ['丁酉', '辛酉'], '卯午' => [], '辰卯' => ['丁卯'], '巳子' => ['丙子', '庚子'], '午酉' => [],
            '未午' => ['甲午', '戊午'], '申卯' => ['癸卯', '丁卯'], '酉子' => [], '戌酉' => ['辛酉'], '亥午' => ['庚午', '甲午'],
            '子卯' => [], '丑子' => ['甲子'],
        ];
        $listWumu = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        $listRes = [
            '避刑冲克害，福德当值，搬家大吉。',
            '福聚恶消，五行合德，搬家大吉。',
            '吉星高照，宜搬家。',
            '善神护佑，宜搬家。',
            '寻常之日，不忌搬家。',
        ];
        $listShaXian = [
            '甲子' => '煞南', '乙丑' => '煞东', '丙寅' => '煞北', '丁卯' => '煞西', '戊辰' => '煞南',
            '己巳' => '煞东', '庚午' => '煞北', '辛未' => '煞西', '壬申' => '煞南', '癸酉' => '煞东',
            '甲戌' => '煞北', '乙亥' => '煞西', '丙子' => '煞南', '丁丑' => '煞东', '戊寅' => '煞北',
            '己卯' => '煞西', '庚辰' => '煞南', '辛巳' => '煞东', '壬午' => '煞北', '癸未' => '煞西',
            '甲申' => '煞南', '乙酉' => '煞东', '丙戌' => '煞北', '丁亥' => '煞西', '戊子' => '煞南',
            '己丑' => '煞东', '庚寅' => '煞北', '辛卯' => '煞西', '壬辰' => '煞南', '癸巳' => '煞东',
            '甲午' => '煞北', '乙未' => '煞西', '丙申' => '煞南', '丁酉' => '煞东', '戊戌' => '煞北',
            '己亥' => '煞西', '庚子' => '煞南', '辛丑' => '煞东', '壬寅' => '煞北', '癸卯' => '煞西',
            '甲辰' => '煞南', '乙巳' => '煞东', '丙午' => '煞北', '丁未' => '煞西', '戊申' => '煞南',
            '己酉' => '煞东', '庚戌' => '煞北', '辛亥' => '煞西', '壬子' => '煞南', '癸丑' => '煞东',
            '甲寅' => '煞北', '乙卯' => '煞西', '丙辰' => '煞南', '丁巳' => '煞东', '戊午' => '煞北',
            '己未' => '煞西', '庚申' => '煞南', '辛酉' => '煞东', '壬戌' => '煞北', '癸亥' => '煞西',
        ];
        // 建除不合
        $listJc = [
            '平' => ['卯甲午', '午辛酉', '申癸亥', '申丁亥', '酉庚子'],
            // '收' => ['辰丁丑', '午辛卯', '申癸亥', '子丁酉', '酉庚午'],
            '闭' => ['卯甲寅', '卯戊寅', '辰丁卯', '午辛巳', '未甲午', '未戊午', '酉庚申', '酉戊申', '戌辛酉', '子丁亥', '丑庚子', '丑甲子'],
            '破' => [],
        ];
        $time = strtotime($this->lunar0->dateTime->format('Y-m-d'));
        $limitNum = $this->getAcquisitionDays($this->lunar0->dateTime->format('Y-m-d'), $this->withinMonth);
        $listGuiJiAndWangwan = [
            // 归忌
            '归忌日' => ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'],
            // 往亡
            '往亡日' => ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'],
        ];
        $listNo = [
            '7_15' => '七月半', '9_9' => '重阳', '10_1' => '寒衣',
        ];
        $result = [];
        $explain = ['jc' => [], 'shen' => [], 'sha' => []];
        for ($i = 1; $i <= $limitNum; $i++) {
            $tmpTime = $time + $i * 86400;
            $tYear = date('Y', $tmpTime);
            $tGongli = date('Y-m-d', $tmpTime);
            $jieqiArr = $this->getJieqiByYear($tYear);
            $jieQi = $jieqiArr[$tGongli] ?? '';
            $huangli = Huangli::date($tmpTime);
            $base = $huangli->getLunarByBetween();
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];

            $zhiRi = $huangli->getZhiRi();
            $jiNian = $base['jinian'];
            unset($jiNian['h']);
            $jiXionYiji = $huangli->getJiXiong();
            $rgz = $jiNian['d'][0] . $jiNian['d'][1];
            $shenSha = $zhiRi['shen_sha'];
            if ($shenSha === '天德') {
                $shenSha = '宝光';
            }
            $jianChu = $huangli->getJianChu();
            $week = Huangli::getWeekChs($tmpTime);
            $tmp = [
                'gongli' => [
                    'y' => (int)date('Y', $tmpTime),
                    'm' => (int)date('m', $tmpTime),
                    'd' => (int)date('d', $tmpTime),
                ],
                'week' => $week,
                'zhoumo' => in_array($week, ['星期六', '星期日']) ? 1 : 0,
                'nongli' => $base['nongli'],
                'sx' => $base['shengxiao'],
                'jinian' => $jiNian,
                'jishen' => $jiXionYiji['jishen'],
                'xiong' => $jiXionYiji['xiong'],
                'type' => 'buyi',
                'type_hl' => $zhiRi['huan_dao'] == '黑道' ? 0 : 1,
                'sha_xian' => $listShaXian[$rgz],
                'hour' => [],
                'jianchu' => $jianChu,
                'shensha' => $shenSha,
                'gongsha' => $this->huiTouGongShaLiu($jiNian),
                // 正冲
                'zheng_chong' => $this->getChongByRgz($jiNian['d'][1]),
                'res' => '',
                'reason' => [],
                'fenxi' => [],
            ];
            $keyStr = date('Y年m月', $tmpTime);
            $mdzToddz = $jiNian['m'][1] . $jiNian['d'][1];
            $yueRi = $jiNian['m'][1] . $jiNian['d'][1];
            $yueRi2 = $jiNian['m'][1] . $jiNian['d'][0] . $jiNian['d'][1];
            $res = $this->fiterDaJi($jiNian);
            $tmp['res'] = $listRes[$res['res']];
            $tmp['type'] = $res['ji'];
            $bool = true;
            $noList = [];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_15', '9_9', '10_1'])) {
                $noList[] = $listNo[$nongliNumberStr];
                $bool = false;
            }
            // 去掉刑冲
            $fenxi = $this->getLiuGx($jiNian);
            if ($fenxi) {
                $bool = false;
            }
            // 劫煞（绝地）月支+日支
            if (isset($listJieSha[$yueRi])) {
                $tmprgz = $jiNian['d'][0] . $jiNian['d'][1];
                if (in_array($yueRi, ['寅亥', '申巳'])) {
                    $tmprgz = $jiNian['d'][1];
                }
                if ($tmprgz == $listJieSha[$yueRi] && in_array($yueRi, ['寅亥', '辰巳', '未申', '申巳', '戌亥', '丑寅'])) {
                    $tmp['res'] = $listRes[2];
                    $tmp['type'] = 'xiao';
                } elseif ($tmprgz == $listJieSha[$yueRi]) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $noList[] = '劫煞日';
                    $bool = false;
                }
            }
            // 灾煞（正冲，凶于劫煞）
            if (isset($listZaiSha[$yueRi])) {
                if (in_array($rgz, $listZaiSha[$yueRi])) {
                    if (in_array($yueRi, ['寅子', '巳卯', '申午', '亥酉'])) {
                        $tmp['res'] = $listRes[2];
                        $tmp['type'] = 'xiao';
                    } else {
                        $tmp['res'] = $listRes[$res['res']];
                        $tmp['type'] = $res['ji'];
                    }
                } else {
                    $noList[] = '灾煞日';
                    $bool = false;
                }
            }
            // 月煞（尽地）
            //            if (isset($listYueSha[$yueRi])) {
            //                if (in_array($rgz, $listYueSha[$yueRi])) {
            //                    $tmp['res'] = $listRes[2];
            //                    $tmp['type'] = 'xiao';
            //
            //                } elseif (in_array($yueRi, ['卯戌', '酉辰'])) {
            //                    $tmp['res'] = $listRes[$res['res']];
            //                    $tmp['type'] = $res['ji'];
            //                } else {
            //                    //$noList[] = '月煞';
            //                    $bool = false;
            //                }
            //            }
            // 月刑（月建刑伤之地）
            if (isset($listYueXing[$yueRi])) {
                if ($yueRi == '巳申' && in_array($rgz, $listYueXing[$yueRi])) {
                    $tmp['res'] = $listRes[2];
                    $tmp['type'] = 'xiao';
                } elseif (in_array($rgz, $listYueXing[$yueRi])) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $noList[] = '月刑';
                    $bool = false;
                }
            }
            // 月厌
            if (isset($listYueYan[$yueRi])) {
                if (in_array($rgz, $listYueYan[$yueRi])) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $noList[] = '月厌';
                    $bool = false;
                }
            }
            if ($zhiRi['huan_dao'] == '黑道') {
                $bool = false;
            }
            // 大时（大败咸池）
            if (isset($listDaShi[$yueRi])) {
                $tmprgz = $rgz;
                if (in_array($yueRi, ['寅卯', '辰酉', '巳午', '申酉', '戌卯', '亥子'])) {
                    $tmp['res'] = $listRes[2];
                    $tmp['type'] = 'xiao';
                } elseif ($tmprgz == $listDaShi[$yueRi]) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $noList[] = '大时';
                    $bool = false;
                }
            }
            // 天吏（凶于大时）
            if (isset($listTianLi[$yueRi])) {
                if (in_array($rgz, $listTianLi[$yueRi])) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $noList[] = '天吏';
                    $bool = false;
                }
            }
            if (isset($listJc[$jianChu])) {
                $arr =
                    [$jianChu];
                if (in_array($yueRi2, $arr)) {
                    $tmp['res'] = $listRes[$res['res']];
                    $tmp['type'] = $res['ji'];
                } else {
                    $bool = false;
                }
            }
            $jcType = in_array($jianChu, ['破', '危', '平', '闭', '执']) ? 0 : 1;
            // 岁破
            if ($listSuiPo[$jiNian['y'][1]] == $jiNian['d'][1]) {
                $noList[] = '岁破日';
                $bool = false;
            }
            if ($jieQi == '清明') {
                $noList[] = '清明';
                $bool = false;
            }
            // 往亡和归忌
            foreach ($listGuiJiAndWangwan as $k1 => $v1) {
                if (in_array($mdzToddz, $v1)) {
                    $noList[] = $k1;
                    $bool = false;
                }
            }
            // 五墓
            if (in_array($yueRi2, $listWumu)) {
                $noList[] = '五墓日';
                $bool = false;
            }
            // 四废
            if ($this->getSiFei($yueRi2)) {
                $noList[] = '四废日';
                $bool = false;
            }
            $num = $result[$keyStr]['num'] ?? 0;
            if ($bool) {
                $num++;
                $reason = $res['reason'];
                foreach ($jiXionYiji['jishen'] as $v) {
                    $tmpReason = $this->getExplain($v);
                    if ($tmpReason) {
                        $reason[] = $v;
                    }
                }
                $tmp['reason'] = array_values(array_unique($reason));
                if (!$jcType) {
                    $tmp['jianchu'] = '';
                }
                $tmp['jishen'] = array_values(array_unique(array_merge($jiXionYiji['jishen'], $reason)));
                $tmp['hour'] = $this->getJishi($jiNian['d'], $shenSha);
            } else {
                $tmp['type'] = 'buyi';
                $tmp['res'] = '';
                $tmp['hour'] = [];
                $tmp['reason'] = $noList;
                if ($tmp['type_hl'] == 1) {
                    $tmp['shensha'] = '';
                }
                if ($jcType) {
                    $tmp['jianchu'] = '';
                }
                $tmp['fenxi'] = $fenxi;
            }
            foreach ($tmp['reason'] as $k1 => $v1) {
                $tmpDetail = $this->getExplain($v1);
                if (empty($tmpDetail)) {
                    unset($tmp['reason'][$k1]);
                    continue;
                }
                $explain['shen'][$v1] = $tmpDetail;
            }
            $tmp['reason'] = array_values($tmp['reason']);
            if (!empty($tmp['jianchu']) && !isset($explain['jc'][$jianChu])) {
                $explain['jc'][$jianChu] = $this->getExplain($jianChu);
            }
            if (!empty($tmp['shensha']) && !isset($explain['sha'][$shenSha])) {
                $explain['sha'][$shenSha] = $this->getShaShenRes($shenSha);
            }
            $result[$keyStr]['title'] = $keyStr;
            $result[$keyStr]['num'] = $num;
            $result[$keyStr]['info'][] = $tmp;
        }
        return [
            'explain' => $explain,
            'list' => array_values($result),
        ];
    }

    /**筛选出大吉 小吉 平
     * @param array $jiNian 八字相关
     * @return  array
     */
    protected function fiterDaJi(array $jiNian): array
    {
        $yueRi = $jiNian['m'][1] . $jiNian['d'][1];
        $yueRi2 = $jiNian['m'][1] . $jiNian['d'][0] . $jiNian['d'][1];
        $dgz = $jiNian['d'][0] . $jiNian['d'][1];
        $mdz = $jiNian['m'][1];
        $ddz = $jiNian['d'][1];
        // 月支日干
        $yzrg = $jiNian['m'][1] . $jiNian['d'][0];
        $type = 'ping';
        $res = 4;
        $reason = [];
        // 天德、月德 大吉
        if ($this->checkTianDe($yueRi) || $this->checkTianDe($yzrg)) {
            $type = 'da';
        }
        if ($this->checkYueDe($yueRi) || $this->checkYueDe($yzrg)) {
            $type = 'da';
            $reason[] = '月德';
        }
        if ($this->checkTianDeHe($yueRi) || $this->checkTianDeHe($yzrg)) {
            $type = 'da';
            $res = 1;
            $reason[] = '天德合';
        }
        if ($this->checkYueDeHe($yueRi) || $this->checkYueDeHe($yzrg)) {
            $type = 'da';
            $res = 1;
            $reason[] = '月德合';
        }
        if ($this->checkTianYuan($yueRi2)) {
            $type = 'xiao';
            $res = 3;
            $reason[] = '天愿';
        }
        if ($this->checkTianSe($jiNian['m'][1], $dgz)) {
            $type = 'xiao';
            $res = 3;
            $reason[] = '天赦';
        }
        if ($this->checkYueEn($yzrg)) {
            $type = 'xiao';
            $res = 3;
            $reason[] = '月恩';
        }
        // 四相
        if ($this->checkSiXian($mdz, $ddz)) {
            $type = 'xiao';
            $res = 3;
            $reason[] = '四相';
        }
        $listXiaoJi = [
            // 时德
            '时德' => ['寅午', '卯午', '辰午', '巳辰', '午辰', '未辰', '申子', '酉子', '戌子', '亥寅', '子寅', '丑寅'],
            // 民日
            '民日' => ['寅午', '巳午', '申午', '亥酉', '卯酉', '午酉', '酉子', '子子', '辰子', '未卯', '戌卯', '丑卯'],
            // 驿马
            '驿马' => ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'],
            // 天马
            '天马' => ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'],
        ];
        foreach ($listXiaoJi as $k => $v) {
            if (!in_array($mdz . $ddz, $v)) {
                continue;
            }
            $type = 'xiao';
            $res = 3;
            $reason[] = $k;
        }
        return ['ji' => $type, 'res' => $res, 'reason' => $reason];
    }

    /**
     * 吉时
     * @param array $gzDay
     * @param string $shenSha
     * @return array
     */
    protected function getJishi(array $gzDay, string $shenSha): array
    {
        $arr = Calendar::DI_ZHI;
        $list1 = [
            '子' => '煞南', '丑' => '煞东', '寅' => '煞北', '卯' => '煞西', '辰' => '煞南', '巳' => '煞东',
            '午' => '煞北', '未' => '煞西', '申' => '煞南', '酉' => '煞东', '戌' => '煞北', '亥' => '煞西',
        ];
        $list2 = [
            [
                '子' => '甲', '丑' => '乙', '寅' => '丙', '卯' => '丁', '辰' => '戊', '巳' => '己',
                '午' => '庚', '未' => '辛', '申' => '壬', '酉' => '癸', '戌' => '甲', '亥' => '乙',
            ],
            [
                '子' => '丙', '丑' => '丁', '寅' => '戊', '卯' => '己', '辰' => '庚', '巳' => '辛',
                '午' => '壬', '未' => '癸', '申' => '甲', '酉' => '乙', '戌' => '丙', '亥' => '丁',
            ],
            [
                '子' => '戊', '丑' => '己', '寅' => '庚', '卯' => '辛', '辰' => '壬', '巳' => '癸',
                '午' => '甲', '未' => '乙', '申' => '丙', '酉' => '丁', '戌' => '戊', '亥' => '己',
            ],
            [
                '子' => '庚', '丑' => '辛', '寅' => '壬', '卯' => '癸', '辰' => '甲', '巳' => '乙',
                '午' => '丙', '未' => '丁', '申' => '戊', '酉' => '己', '戌' => '庚', '亥' => '辛',
            ],
            [
                '子' => '壬', '丑' => '癸', '寅' => '甲', '卯' => '乙', '辰' => '丙', '巳' => '丁',
                '午' => '戊', '未' => '己', '申' => '庚', '酉' => '辛', '戌' => '壬', '亥' => '癸',
            ],
        ];
        $hourList = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $list3 = [
            '甲' => 0, '乙' => 1, '丙' => 2, '丁' => 3, '戊' => 4, '己' => 0, '庚' => 1, '辛' => 2, '壬' => 3, '癸' => 4,
        ];
        $listZhenChong = [
            '甲子' => ['庚午', '1930、1990'], '乙丑' => ['辛未', '1931、1991'], '丙寅' => ['壬申', '1932、1992'],
            '丁卯' => ['癸酉', '1933、1993'], '戊辰' => ['甲戌', '1934、1994'], '己巳' => ['乙亥', '1935、1995'],
            '庚午' => ['丙子', '1936、1996'], '辛未' => ['丁丑', '1937、1997'], '壬申' => ['戊寅', '1938、1998'],
            '癸酉' => ['己卯', '1939、1999'], '甲戌' => ['庚辰', '1940、2000'], '乙亥' => ['辛巳', '1941、2001'],
            '丙子' => ['壬午', '1942、2002'], '丁丑' => ['癸未', '1943、2003'], '戊寅' => ['甲申', '1944、2004'],
            '己卯' => ['乙酉', '1945、2005'], '庚辰' => ['丙戌', '1946、2006'], '辛巳' => ['丁亥', '1947、2007'],
            '壬午' => ['戊子', '1948、2008'], '癸未' => ['己丑', '1949、2009'], '甲申' => ['庚寅', '1950、2010'],
            '乙酉' => ['辛卯', '1951、2011'], '丙戌' => ['壬辰', '1952、2012'], '丁亥' => ['癸巳', '1953、2013'],
            '戊子' => ['甲午', '1954、2014'], '己丑' => ['乙未', '1955、2015'], '庚寅' => ['丙申', '1956、2016'],
            '辛卯' => ['丁酉', '1957、2017'], '壬辰' => ['壬寅', '1962、2022'], '癸巳' => ['己亥', '1959、2019'],
            '甲午' => ['庚子', '1960、2020'], '乙未' => ['辛丑', '1961、2021'], '丙申' => ['壬寅', '1962、2022'],
            '丁酉' => ['癸卯', '1963、2023'], '戊戌' => ['甲辰', '1964、2024'], '己亥' => ['乙巳', '1965、2025'],
            '庚子' => ['丙午', '1966、2026'], '辛丑' => ['丁未', '1967、2027'], '壬寅' => ['戊申', '1968、2028'],
            '癸卯' => ['己酉', '1969、2029'], '甲辰' => ['庚戌', '1970、2030'], '乙巳' => ['辛亥', '1971、2031'],
            '丙午' => ['壬子', '1972、2032'], '丁未' => ['癸丑', '1973、2033'], '戊申' => ['甲寅', '1974、2034'],
            '己酉' => ['乙卯', '1975、2035'], '庚戌' => ['丙辰', '1976、2036'], '辛亥' => ['丁巳', '1977、2037'],
            '壬子' => ['戊午', '1978、2038'], '癸丑' => ['己未', '1979、2039'], '甲寅' => ['庚申', '1980、2040'],
            '乙卯' => ['辛酉', '1981、2041'], '丙辰' => ['壬戌', '1982、2042'], '丁巳' => ['癸亥', '1983、2043'],
            '戊午' => ['甲子', '1924、1984'], '己未' => ['乙丑', '1925、1985'], '庚申' => ['丙寅', '1926、1986'],
            '辛酉' => ['丁卯', '1927、1987'], '壬戌' => ['戊辰', '1928、1988'], '癸亥' => ['己巳', '1929、1989'],
        ];
        $listShiTg = $list2[$list3[$gzDay[0]]];
        $jn = $this->lunar1->getLunarGanzhiYear();
        $ydz1 = $jn[1];
        $ydz2 = '';
        if ($this->lunar2) {
            $jn2 = $this->lunar2->getLunarGanzhiYear();
            $ydz2 = $jn2[1];
        }
        $result = [];
        $sxBase = new SxBase();
        foreach ($arr as $v) {
            if ($shenSha === '司命') {
                if (!in_array($v, ['寅', '卯', '辰', '巳', '午', '未', '申'])) {
                    continue;
                }
            }
            $tgS = $listShiTg[$v];
            $gz = $tgS . $v;
            $chongDz = $this->getChongDz($v);
            $sx = $sxBase->getsxByDz($chongDz);
            $str = $v . $gzDay[1];
            $str2 = $v . $ydz1;
            $str3 = $v . $ydz2;
            if (BaziCommon::getXianChong($str) || BaziCommon::getXianXin($str) || BaziCommon::getXianHai($str)) {
                continue;
            }
            if (BaziCommon::getXianChong($str2) || BaziCommon::getXianPo($str2) || BaziCommon::getXianHai($str2)) {
                continue;
            }
            if (BaziCommon::getXianChong($str3) || BaziCommon::getXianPo($str3) || BaziCommon::getXianHai($str3)) {
                continue;
            }
            $result[] = [
                'dz' => $v, 'h' => $hourList[$v], 'yi' => '', 'zh' => $listZhenChong[$gz],
                'chong' => $sx, 'sha_xian' => $list1[$v] ?? $list1['子'],
            ];
        }
        return $result;
    }

    /**
     * 获得煞神
     * @param string $shaShen
     * @return string
     */
    protected function getShaShenRes($shaShen): string
    {
        $list = [
            '青龙' => '该日行动的价值大于计划，有付出就有收获，适合各种搬动行走。',
            '明堂' => '该日遇到贵人几率较高，有机会得到贵人的帮助。',
            '金匮' => '福德星，乃是黄道日，该日有利于入宅，诸事皆宜。',
            '玉堂' => '该日对于求财有利，而且有受到贵人关注的机会。',
            '司命' => '白天用事吉，夜晚则不宜。',
            '天刑' => '天刑属火，乃一凶星，主刑夭孤克。该日万事皆忌，不宜有大动作。',
            '朱雀' => '天讼星，利用公事，常人凶，诸事忌用，谨防远行动作。',
            '白虎' => '天杀星，宜出师，祭祀，皆吉，远行，迁移，搬动皆忌。',
            '天牢' => '镇神星，阴人用事皆吉，其余都不利。',
            '玄武' => '该神属天狱星，寓意不吉，多犯小人口舌。',
            '勾陈' => '地狱星，此时所作一切事，有始无终，难有圆满，故不宜入宅出行。',
            '宝光' => '该日大利入宅，移动，所做的事情容易成功。',
        ];
        return $list[$shaShen] ?? '';
    }

    /**
     * 获得名词解释
     * @param string $str
     * @return string
     */
    protected function getExplain(string $str): string
    {
        $list = [
            '往亡日' => '古话有云，往亡煞临世，动必有险厄。',
            '受死日' => '该日寓意不吉祥，因而有不宜诸吉事的说法。入宅为喜事，因而不宜。',
            '归忌日' => '归忌主凶，所值之日忌出门远行、 返程归家、搬迁移徙、入宅。',
            '清明' => '该日乃是用来扫坟祭祖的，若是用来搬迁入宅有些不合时宜。',
            '七月半' => '七月半在传统文化中属于鬼节，选择入宅有些不太适合。',
            '重阳' => '当日有大事勿用的说法，因而忌出行搬动。',
            '寒衣' => '寒衣节乃是传统的四大鬼节之一，寓意上并不吉祥。',
            '小红沙日' => '红沙日有诸事不宜的说法，因而需要注意避开。',
            '杨公忌日' => '这日诸事不宜，动之则凶的说法。',
            '劫煞日' => '劫煞为打劫之星，主路遇不安，土匪劫道，有红伤血光之灾。',
            '灾煞日' => '灾煞为灾星，主牢狱凶灾，健康受损，六畜不安。且不宜出行、搬动、入宅。',
            '岁煞日' => '岁煞是当头太岁，太岁头上动土，招惹是非口舌，官司牢狱破财之事。',
            '月刑' => '该日有刑伤之意，而且对于一些喜事上并不太适合，而入宅大事谨慎平安为主。',
            '月厌' => '该日又称大祸日，是忌嫁娶、出行、赴任、入宅等移动迁徙活动的凶日。',
            '大时' => '该日表示精光、消减。在运势上容易影响到一个人的心境。',
            '天吏' => '天子的官吏，有奉天命而治人罪的寓意。',
            '四废日' => '四废有五行无气，福德不临的意思。',
            '五墓日' => '一种忌日，不宜婚丧嫁娶，不宜动土兴造，不宜出行入宅。',
            '岁破日' => '岁破有大事勿用，诸事不宜的说法。',
            '月煞' => '',
            '显星' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '传星' => '三皇吉星之一，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '曲星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '煞贡' => '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。',
            '人专' => '三皇吉星之一，有百事吉庆，万事如意的象征。对财运亦是有益，喜事连连。',
            '直星' => '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。',
            '天德' => '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。',
            '天德合' => '合德之神相助，各种凶煞伏藏不出，有小福的日子。',
            '月德' => '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。',
            '月德合' => '得到五行力量的聚合，为有福之日，适宜进行入宅、祭祖等事。',
            '天赦' => '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。',
            '天愿' => '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。',
            '月恩' => '受恩之日，这天适合开始一段新的生活。',
            '四相' => '拥有四时王相的贵气，这一天适合入宅、出行、祭祀等。',
            '时德' => '得到天地舒畅之气，为四时所生，适宜祈福的日子。',
            '不将' => '不将是阴阳中和之日，有万事大吉的说法。',
            '驿马' => '主奔波、走动、外出、旅行、出差、入宅、转职等与移动有关的事象。',
            '民日' => '有顺势而为，遵循天道的寓意。',
            '天马' => '天马是天的坐骑，当值之日适宜入宅。',
            '除' => '此日为“除旧布新”之日，大吉，故很少有不宜之事。',
            '定' => '该日凡事皆有定。宜采纳、商贾、拜访、考试，入宅，移居等，此外诸凶事不宜。',
            '成' => '此日指凡事成就，喜凶诸事均可办理。而入宅选择，寓意福气满满，开门大吉。',
            '开' => '寓意着顺利开始，乃是一个好日子，入宅可选。',
            '收' => '有收成，收获的意思，该日入宅大吉。',
            '满' => '满日在吉日中有“圆满”的含义，对于入宅来说，自然有着比较好的寓意。',
            '建' => '对于事情上来说，该日有着起始的说法，寓意较好。',
            '破' => '此日万事不利，只能做破垣坏屋之事。破日在解说有指破裂，冲破的含义。',
            '危' => '危日象征危机，是危险的一天，提醒凡事都要小心之意。',
            '平' => '此日平平，无出头之日。',
            '闭' => '此日除修筑堤防之类的事外，万事皆凶。',
            '执' => '取义守成，即是守住成果，不宜冒进的意思。这一日有小破耗的说法，需规避。',
        ];
        return $list[$str] ?? '';
    }

    /**
     * 四废
     * @param string $str
     * @return bool
     */
    protected function getSiFei(string $str): bool
    {
        // 四废
        $listShiFei = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
        ];
        return in_array($str, $listShiFei);
    }

    /**
     * 天罡四煞
     * @param string $dz
     * @return array
     */
    protected function getTianGanSiSha(string $dz): array
    {
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $result = ['ju' => ['寅', '午', '戌'], 'no' => '丑', 'sx' => '牛'];
        $listSx = Calendar::C_ZODIAC;
        $listDz = Calendar::DI_ZHI;
        foreach ($list as $k => $v) {
            if (in_array($dz, $v)) {
                $dzIndex = (int)array_search($k, $listDz);
                $result = [
                    'ju' => $v,
                    'no' => $k,
                    'sx' => $listSx[$dzIndex],
                ];
                break;
            }
        }
        return $result;
    }

    /**
     * 天罡四煞
     * @return array
     */
    protected function getNodz(): array
    {
        $result = [];
        $jiNian = $this->lunar1->getLunarTganDzhi();
        $ydz = $jiNian['y'][1];
        $shaArr = $this->getTianGanSiSha($ydz);
        $result[] = [$ydz, "{$shaArr['no']}日{$shaArr['no']}时"];
        if (!empty($this->lunar2)) {
            $jiNian1 = $this->lunar2->getLunarTganDzhi();
            $ydz1 = $jiNian1['y'][1];
            $shaArr1 = $this->getTianGanSiSha($ydz1);
            $result[] = [$ydz1, "{$shaArr1['no']}日{$shaArr1['no']}时"];
        }
        return $result;
    }

    /**
     * 根据地支获得对应的地支
     * @param string $dz
     * @return string
     */
    protected function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 获得年份内所有节气
     * @param $year
     * @return array
     */
    private function getJieqiByYear($year): array
    {
        $arr = $this->jieQiYear;
        if (isset($arr[$year])) {
            return $arr[$year];
        }
        $jArr = SolarTerm::getAllJieQi($year);
        $jieqiArr = [];
        foreach ($jArr as $k => $v) {
            $jTmp = date('Y-m-d', strtotime($v));
            $jieqiArr[$jTmp] = $k;
        }
        $this->jieQiYear[$year] = $jieqiArr;
        return $jieqiArr;
    }

    /**
     * 流日和用户关系
     * @param array $jiNian
     * @return array
     */
    protected function getLiuGx($jiNian): array
    {
        $jiNianM = $this->lunar1->getLunarTganDzhi();
        $jiNianF = $this->lunar2 ? $this->lunar2->getLunarTganDzhi() : [];
        $ydzM = $jiNianM['y'][1];
        $ddzM = $jiNianM['d'][1];
        $ydzF = $jiNianF['y'][1] ?? '';
        $ddzF = $jiNianF['d'][1] ?? '';
        $ddz = $jiNian['d'][1];
        $list = [
            '年' => [$ydzM . $ddz, $ydzF . $ddz],
            '日' => [$ddzM . $ddz, $ddzF . $ddz],
        ];
        $result = [];
        foreach ($list as $k => $v) {
            $str = '';
            if (BaziCommon::getXianXin($v[0]) || BaziCommon::getXianXin($v[1])) {
                $str .= '刑';
            }
            if (BaziCommon::getXianChong($v[0]) || BaziCommon::getXianChong($v[1])) {
                $str .= '冲';
            }
            if (empty($str)) {
                continue;
            }
            $result[] = [$k, $str];
        }

        return $result;
    }

    /**
     * 获得相刑的地支
     * @param string $dz 地支
     * @return string
     */
    public function getXianXinDz(string $dz): string
    {
        $list = [
            '子' => '卯', '丑' => '戌', '寅' => '巳', '卯' => '子', '戌' => '未', '未' => '丑',
            '巳' => '申', '申' => '寅', '辰' => '辰', '午' => '午', '酉' => '酉', '亥' => '亥',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 根据日柱
     * @param $rgz
     * @return array
     */
    protected function getChongByRgz(string $dz): array
    {
        $chongDz = BaziCommon::getChongDz($dz);
        $sxBase = new SxBase();
        return [
            'gz' => $chongDz,
            'sx' => $sxBase->getsxByDz($chongDz),
            'year' => '',
        ];
    }

    /**
     * 回头贡杀
     * @param array $jiNian
     * @return array
     */
    protected function huiTouGongShaLiu(array $jiNian): array
    {
        $dzArr = array_column($jiNian, 1);
        $list = [
            '丑' => ['寅', '午', '戌'], '辰' => ['巳', '酉', '丑'], '戌' => ['亥', '卯', '未'], '未' => ['申', '子', '辰'],
        ];
        $sxBase = new SxBase();
        $result = [];
        foreach ($list as $k => $v) {
            $arr = array_intersect($v, $dzArr);
            if (count($arr) > 2) {
                $result = [
                    'dzs' => implode('', $v),
                    'dz' => $k,
                    'sx' => $sxBase->getsxByDz($k),
                ];
                break;
            }
        }
        return $result;
    }
}
