<?php
// +----------------------------------------------------------------------
// | 灵签接口
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\model\lingqian\Daisen;
use app\model\lingqian\Guanyin;
use app\model\lingqian\Wealth;
use app\model\lingqian\Yuelao;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Lingqian
{
    /**
     * 灵签接口
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        // 类型    ====    月老，黄大仙，观音，财神
        // 黄大仙   ===    婚姻，前程，健康，财运，流年运程
        // 观音  ====  婚姻，学业，事业，财运
        // 财神  ====   婚姻，学业，健康，财运
        $data = [
            'type' => input('type', 'trim'),
            'number' => input('number', 'intval'),
        ];
        $validate = Validate::rule(
            [
                'type|类型' => ['require', function ($data) {
                    $arr = ['yuelao', 'huangdaxian', 'guanyin', 'caishen'];
                    if (!in_array($data, $arr)) {
                        return '类型不存在';
                    } else {
                        return true;
                    }
                }],
                'number|签数' => ['require', 'number'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        switch ($data['type']) {
            case 'yuelao':
                $result = Yuelao::where('id', $data['number'])->cache(300)->find()->toArray();
                break;
            case 'huangdaxian':
                $result = Daisen::where('id', $data['number'])->cache(300)->find()->toArray();
                break;
            case 'guanyin':
                $result = Guanyin::where('id', $data['number'])->cache(300)->find()->toArray();
                $result['notes'] = trim($result['notes']);
                break;
            case 'caishen':
                $result = Wealth::where('id', $data['number'])->cache(300)->find()->toArray();
                break;
            default:
                $result = ['status' => 0];
                break;
        }
        return $result;
    }
}
