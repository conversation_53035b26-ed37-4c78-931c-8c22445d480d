<?php
// +----------------------------------------------------------------------
// | Donggongjiri.动工吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\traits\jiri\JiRiBaseTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Donggongjiri
{
    use JiRiBaseTraits;

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 五行属性
     * @var array
     */
    protected array $wuXingAttr;

    /**
     * 动工吉日
     * @param string $time
     * @param string $sex
     * @param string $otime
     * @param int $limit
     * @return array
     * @throws Exception
     * @throws \think\Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index($time = '', $sex = '', $otime = '', $limit = 365)
    {
        $data = [
            // 用户生日
            'time' => $time,
            // 性别 0男 1女
            'sex' => $sex,
            // 订单时间
            'otime' => $otime,
            // 限制天数
            'limit' => $limit,
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'sex|性别' => ['require', 'in:0,1'],
                'limit|限制天数' => ['require', 'number', 'between:20,731'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $dayList = $this->getDayList();
        $result = [
            'lunar' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 用神五行
            'yongshen' => $xiYong['wx'][1],
            // 喜神五行
            'xishen' => $xiYong['wx'][0],
            // 吉日
            'ji' => $dayList,
            // 风水建议
            'fengshui' => $this->getFengShui($data['otime']),
        ];
        return $result;
    }

    /**
     * 获得日期列表
     * @return array
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    protected function getDayList(): array
    {
        // 岁破 年+日
        $listSuiPo = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        // 当月月支+地支
        $listShsd = [
            // 三合死地日
            '寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子',
            // 红沙日
            '寅酉', '卯巳', '辰丑', '巳酉', '午巳', '未丑', '申酉', '酉巳', '戌丑', '亥酉', '子巳', '丑丑',
        ];
        // 月家天官符 月干+日干支
        $listYueTianGuan = [
            '甲' => ['丁卯', '癸卯', '壬子', '丙子', '乙酉', '辛酉'],
            '乙' => ['戊辰', '丁丑', '癸丑', '丙戌', '壬戌'],
            '丙' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '丁' => ['乙丑', '辛丑', '甲戌', '庚戌', '癸未', '丁未', '壬辰'],
            '戊' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '己' => ['丁卯', '癸卯', '壬子', '丙子', '乙酉', '辛酉'],
            '庚' => ['戊辰', '丁丑', '癸丑', '丙戌', '壬戌'],
            '辛' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
            '壬' => ['乙丑', '辛丑', '甲戌', '庚戌', '癸未', '丁未', '壬辰'],
            '癸' => ['辛未', '丁未', '丙辰', '庚辰', '乙丑', '戊戌'],
        ];
        // 五墓 月+日干+日支
        $listWumu = [
            '寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '丑戊辰',
        ];
        // 四废 月+日干+日支
        $listShiFei = [
            '寅庚申', '巳庚申', '申庚申', '亥壬子', '卯壬子', '午壬子', '酉甲寅', '子甲寅', '辰甲寅', '未丙午',
            '戌丙午', '丑丙午', '寅辛酉', '巳辛酉', '申辛酉', '亥癸亥', '卯癸亥', '午癸亥', '酉乙卯', '子乙卯',
            '辰乙卯', '未丁巳', '戌丁巳', '丑丁巳',
        ];
        // 天干正冲
        $listChongTg = [
            '甲庚', '乙辛', '丙壬', '丁癸', '戊甲', '己乙', '庚丙', '辛丁', '壬戊', '癸己',
        ];
        // 相冲 或时破
        $listChongDz = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        // 五不遇时 流日天干	流时地支
        $listWuBu = [
            '甲午', '乙巳', '丙辰', '丁卯', '戊寅', '己丑', '庚子', '辛酉', '壬申', '癸未',
        ];
        $listDes = [
            'xiao' => [
                '三会' => '五行力量统一，融合有力，避免与自身运势相冲。',
                '三合' => '五行聚合，有情相生，趋吉避凶。',
                '半三合' => '事从权宜，动工小吉，可作为备用日期按需采用。',
                '六合' => '贵人助力，暗中帮扶，五行力量不明显。',
                '旺' => '符合四季旺衰变化规律，四时之力处于旺盛状态。',
                '长生' => '万物始生，屋宅平安，修造主吉。',
                '冠带' => '万物渐荣，寓意吉祥，动工可用。',
                '临官' => '身禄近贵，五行为用，小吉之日。',
                '帝旺' => '逢时强盛，五行调和，动工主吉。',
            ],
            'da' => [
                '三会' => '吉神照拂，干支有力，对福主有生助作用，动土、修造主吉。',
                '三合' => '五行力量聚合的日子，相扶相生，有趋吉避凶的作用。',
                '半三合' => '五行力量稍弱，但是对福主有助益作用，事从权宜，动工可用。',
                '六合' => '五行力量稍弱，但有贵人暗中帮扶的意象，可用于动工之日。',
                '旺' => '当季五行力量较强，对福主运势有提升的作用。',
                '长生' => '处悠闲之地，五行始生，欣欣向荣，有开拓进取的意象。',
                '冠带' => '处喜庆之地，五行逐渐兴荣，有小成、小贵的意象。',
                '临官' => '处拼搏之地，利官近贵，有付出能够得到回报的意象。',
                '帝旺' => '处荣发之地，主吉，利禄进财，有达到最旺盛时期的意象。',
                '',
            ],
        ];
        $listHourRes = [
            '木' => [
                '子' => ['23时33分', '23时58分', '0时8分'],
                '丑' => ['1时33分', '2时8分'],
                '寅' => ['3时18分', '3时33分', '4时18分'],
                '卯' => ['5时8分', '5时58分', '6时18分', '6时58分'],
                '辰' => ['7时58分', '8时18分', '8时58分'],
                '巳' => ['9时8分', '9时18分', '9时58分', '10时18分'],
                '午' => ['11时33分', '11时58分', '12时8分'],
                '未' => ['13时33分', '13时58分', '14时18分'],
                '申' => ['16时8分', '16时18分'],
                '酉' => ['17时8分', '17时18分', '17时58分', '18时18分'],
                '戌' => ['19时8分', '19时18分'],
                '亥' => ['21时8分', '21时13分'],
            ],
            '火' => [
                '子' => ['23时37分', '0时12分'],
                '丑' => ['1时37分', '2时12分'],
                '寅' => ['3时37分'],
                '卯' => ['5时52分', '6时52分', '6时57分'],
                '辰' => ['7时12分', '7时32分', '8时12分', '8时52分'],
                '巳' => ['9时17分', '9时22分', '10时17分', '10时22分'],
                '午' => ['11时37分', '11时52分', '12时7分'],
                '未' => ['13时37分', '14时7分', '14时22分'],
                '申' => ['16时2分', '16时17分'],
                '酉' => ['17时17分', '17时22分', '18时17分', '18时22分'],
                '戌' => ['19时12分', '19时20分'],
                '亥' => ['21时2分', '21时12分'],
            ],
            '土' => [
                '子' => ['0时5分'],
                '丑' => ['1时35分'],
                '寅' => ['3时25分'],
                '卯' => ['5时10分', '5时50分', '6时20分', '6时50分'],
                '辰' => ['7时10分', '7时35分', '7时50分', '8时30分'],
                '巳' => ['9时15分', '9时30分', '10时25分'],
                '午' => ['11时35分', '11时50分', '12时10分'],
                '未' => ['13时35分', '13时50分'],
                '申' => ['16时15分', '16时20分'],
                '酉' => ['17时15分', '17时30分', '18时25分'],
                '戌' => ['19时10分'],
                '亥' => ['21时10分'],
            ],
            '金' => [
                '子' => ['23时39分', '0时9分'],
                '丑' => ['1时39分', '2时9分'],
                '寅' => ['3时19分'],
                '卯' => ['5时9分', '5时59分', '6时19分', '6时59分'],
                '辰' => ['7时9分', '7时59分', '8时19分', '8时59分'],
                '巳' => ['9时9分', '9时19分', '9时59分', '10时19分'],
                '午' => ['11时39分', '11时59分', '12时9分'],
                '未' => ['13时39分', '13时59分'],
                '申' => ['16时9分', '16时29分'],
                '酉' => ['17时9分', '17时19分', '17时59分', '18时19分'],
                '戌' => ['19时9分', '19时19分'],
                '亥' => ['21时9分'],
            ],
            '水' => [
                '子' => ['23时36分', '23时51分', '0时6分'],
                '丑' => ['1时36分', '2时6分'],
                '寅' => ['3时16分', '3时36分', '4时16分'],
                '卯' => ['5时6分', '5时56分', '6时16分', '6时56分'],
                '辰' => ['7时6分', '7时36分', '8时16分', '8时36分'],
                '巳' => ['9时6分', '9时36分', '9时56分', '10时16分'],
                '午' => ['11时31分', '11时56分', '12时6分'],
                '未' => ['13时31分', '13时56分', '14时16分'],
                '申' => ['16时1分', '16时16分'],
                '酉' => ['17时6分', '17时36分', '17时56分', '18时16分'],
                '戌' => ['19时6分', '19时16分'],
                '亥' => ['21时6分'],
            ],
        ];

        $jiNian = $this->lunar->getLunarTganDzhi();
        // 获得喜神、用神、仇神、忌神、闲神 五行
        $xiYong = BaziExt::getxiYongJi($this->lunar);
        $result = [
            'da' => [], 'xiao' => [],
        ];
        for ($i = 1; $i <= $this->orginData['limit']; $i++) {
            $time = strtotime("{$this->orginData['otime']} +{$i} day");
            $timeStr = date('Y年m月d日', $time);
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $jiXiong = $huangli->getJiXiong();
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                continue;
            }
            if (empty(array_intersect(['修造', '动土'], $jiXiong['yi']))) {
                continue;
            }
            $nongli = $huangli->getNongLi();
            $base1 = [
                'nongli' => [
                    'y' => $nongli['cn_y'],
                    'm' => $nongli['cn_m'],
                    'd' => $nongli['cn_d'],
                ],
                '_nongli' => [
                    'y' => (int)$nongli['y'],
                    'm' => (int)$nongli['m'],
                    'd' => (int)$nongli['d'],
                ],
                'jinian' => $huangli->getLunarTganDzhi(),
            ];
            $nongliNumberStr = $base1['_nongli']['m'] . '_' . $base1['_nongli']['d'];
            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            $jiNianTmp = $base1['jinian'];
            $jiNianDay = implode('', $jiNianTmp['d']);
            // 岁破过滤
            if ($listSuiPo[$jiNianTmp['y'][1]] == $jiNianTmp['d'][1]) {
                continue;
            }
            $shiLiAndJ = $this->getJieQiDay(date('Y', $time));
            // 每年春分、夏至、秋分、冬至前一日即是四离 立春、立夏、立秋、立冬前一日即是四绝
            if (in_array($timeStr, $shiLiAndJ)) {
                continue;
            }
            $jianchu = $huangli->getJianChu();
            // 过滤 破 建
            if (in_array($jianchu, ['破', '建'])) {
                continue;
            }
            // 三合死地和红沙日 过滤
            if (in_array($jiNianTmp['m'][1] . $jiNianTmp['d'][1], $listShsd)) {
                continue;
            }
            // 月家天官符
            if (in_array($jiNianDay, $listYueTianGuan[$jiNianTmp['m'][0]])) {
                continue;
            }
            $yueRi2 = $jiNianTmp['m'][1] . $jiNianTmp['d'][0] . $jiNianTmp['d'][1];
            // 五墓+四废
            if (in_array($yueRi2, $listWumu) || in_array($yueRi2, $listShiFei)) {
                continue;
            }
            // 流日日天干不能冲事主的年天干
            if (in_array($jiNian['y'][0] . $jiNianTmp['d'][0], $listChongTg)) {
                continue;
            }
            if (in_array($jiNian['y'][1] . $jiNianTmp['d'][1], $listChongDz)) {
                continue;
            }
            $hourList = $huangli->getHourDetail();
            $jieqiArr = $huangli->getJieQiCur();
            $jieqiCur = $jieqiArr['current'][0];
            foreach ($hourList as $k1 => $v1) {
                // 流日地支	流时地支
                $hourTmp1 = $jiNianTmp['d'][1] . $v1['h'][1];
                $hourTmp2 = $jiNianTmp['d'][0] . $v1['h'][1];

                if (in_array($hourTmp1, $listChongDz)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 流日地支	流时地支
                if (BaziExt::getXianXinDz($jiNianTmp['d'][1], $v1['h'][1])) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 五不遇时 流日天干+流时地支
                if (in_array($hourTmp2, $listWuBu)) {
                    unset($hourList[$k1]);
                    continue;
                }
                // 日干+时干支
                $hourTmp3 = $jiNianTmp['d'][0] . $v1['h'][0] . $v1['h'][1];
                if ($this->getTianMen($jieqiCur, $hourTmp3)) {
                    continue;
                }
                // 日支+时支
                if ($this->getHuanDaoJiShi($hourTmp1)) {
                    continue;
                }
                // 日干+时干支
                if ($this->getHuanDaoJiShi($hourTmp3)) {
                    continue;
                }
                unset($hourList[$k1]);
            }
            $tmpWxNum = 0;
            $tmpRes = [];
            foreach ($hourList as $v1) {
                $tmpJiNian = $jiNianTmp;
                $tmpJiNian['h'] = $v1['h'];
                $chenJuWanDe = $this->getChenJuWanDe($tmpJiNian);
                if (empty($chenJuWanDe)) {
                    continue;
                }
                // 五行数值
                $wxNumList = BaziExt::getWuxingNum($tmpJiNian);
                arsort($wxNumList);
                $wxLiu = key($wxNumList);
                $maxWxNum = current($wxNumList);
                // 喜神、用神、仇神、忌神、闲神
                $type = '';
                if (in_array($wxLiu, [$xiYong['wx'][0], $xiYong['wx'][1]])) {
                    $type = 'da';
                } elseif ($wxLiu == $xiYong['wx'][4]) {
                    $type = 'xiao';
                }
                if (empty($type)) {
                    continue;
                }
                if ($maxWxNum <= $tmpWxNum) {
                    continue;
                }
                $tmpWxNum = $maxWxNum;
                $tmpResTimeList = $listHourRes[$xiYong['wx'][1]][$v1['h'][1]];
                $count = count($tmpResTimeList);
                $timeKey = $time % $count;
                $tmpRes = [
                    'type' => $type,
                    'date' => $timeStr,
                    'nongli' => $base1['nongli'],
                    'h' => $v1['h'][1],
                    'jinian' => $tmpJiNian,
                    'week' => $week,
                    'des' => $listDes[$type][$chenJuWanDe] ?? $listDes[$type]['三会'],
                    'time' => $tmpResTimeList[$timeKey],
                    'sx_chong' => $this->getDzChongSx($tmpJiNian['d'][1]),
                    'pos' => $this->getDongGongPos($tmpJiNian),
                ];
            }
            if (empty($tmpRes)) {
                continue;
            }
            $resType = $tmpRes['type'];
            unset($tmpRes['type']);
            $result[$resType][] = $tmpRes;
        }
        return $result;
    }

    /**
     * 日干不旺靠时禄补
     * @param string $str 日干+时干支
     * @return bool
     */
    protected function getLuGui(string $str): bool
    {
        $list = [
            '甲丙寅', '乙己卯', '丙癸巳', '丁丙午', '戊丁巳', '己庚午', '庚甲申', '辛丁酉', '壬辛亥', '癸壬子',
        ];
        return in_array($str, $list);
    }

    /**
     * 获得地支相冲生肖
     * @param $dz
     * @return string
     */
    protected function getDzChongSx($dz): string
    {
        $list = [
            '子' => '马', '丑' => '羊', '寅' => '猴', '卯' => '鸡', '辰' => '狗', '巳' => '猪',
            '午' => '鼠', '未' => '牛', '申' => '虎', '酉' => '兔', '戌' => '龙', '亥' => '蛇',
        ];
        return $list[$dz];
    }
}
