<?php
// +----------------------------------------------------------------------
// |  Wxqm 五行起名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\Utils;
use app\lib\WxAttr;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\Qmwx;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\plugin\WuXing;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Wxqm
{
    /**
     * 基础
     * @var Ex $lunar
     */
    protected Ex $lunar;

    /**
     * @var array
     */
    protected array $orginData = [];

    /**
     * 已使用的名字
     * @var array[]
     */
    protected array $mingsIn = [
        'zi' => [],
        'zi_py' => [],
        'zi_py_no' => [],
        'py' => [],
        'py_no' => [],
    ];

    /**
     * 用户输入的禁用拼音
     * @var array
     */
    protected array $jinRes = [];

    /**
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            'xing' => input('xing', '', 'trim'),
            // 性别 0 男 1 女
            'sex' => input('sex', 0, 'intval'),
            // 是否出生  0 未出生 1 已出生
            'born' => input('born', '', 'intval'),
            // 用户生日
            'time' => input('time', '', 'trim'),
            // 测算时间
            'otime' => input('otime', '', 'trim'),
            // 0 按喜用神起名， 1按缺五行起名
            'type' => input('type', 0, 'intval'),
            // 要取的数量
            'limit' => input('limit', 50, 'intval'),
            // 禁用字
            'jin' => str_replace(' ', '', input('jin', '', 'trim')),
            // 指定名字
            'mings' => input('mings', '', 'trim'),
        ];
        $validate = Validate::rule(
            [
                'xing|姓氏' => ['require', 'chs'],
                'sex|性别' => ['require', 'in:0,1'],
                'born|是否出生' => ['require', 'in:0,1'],
                'time|用户生日' => ['require', 'date', 'before:2090-12-31', 'after:1920-01-01'],
                'otime|测算时间' => ['require', 'date', 'before:2090-12-31', 'after:2020-01-01'],
                'jin|禁用字' => ['chs'],
            ]
        );
        if (!$validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $wxFenXi = $this->wxFenxi();
        if ($data['type'] && $wxFenXi['que_type'] != 3) {
            // 从五行缺失中获得名字五行
            $wxN = $this->getWxByQs($wxFenXi['que']);
        } else {
            $str = $wxFenXi['xy']['yong'] . $wxFenXi['xy']['xi'];
            $wxN = $this->getWxByYongXi($str);
        }
        $names = $this->getNames($wxN);
        return [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 五行分析
            'wx_fx' => $wxFenXi,
            // 纪年五行
            'jnwx' => $this->getJiNianWxNum(),
            // 喜用神
            'like_god' => $this->lunar->getLikeGod(),
            // 名字列表
            'names' => $names,
        ];
    }

    /**
     * 名字记录
     * @return array
     */
    public function record()
    {
        // 操作类型 1 记录名字 2 喜欢 3 不喜欢
        $act = input('act', 0, 'intval');
        if (!in_array($act, [1, 2, 3])) {
            return ['status' => 0, 'msg' => '操作类型不正确'];
        }
        return match ($act) {
            1 => $this->recordNames(),
            2, 3 => $this->recordLove($act)
        };
    }

    /**
     * 根据名字id更新输出数
     * @return array
     */
    protected function recordNames(): array
    {
        $mingIds = input('mings', '', 'trim');
        $mingArr = explode(',', $mingIds);
        Qmwx::whereIn('id', $mingArr)->inc('num', 1)->update();
        return ['status' => 1, 'msg' => '成功'];
    }

    /**
     * 记录名字喜欢和不喜欢
     * @param int $act
     * @return array
     */
    protected function recordLove(int $act): array
    {
        $upKey = $act == 2 ? 'love' : 'dislove';
        $id = input('mings', 0, 'intval');
        Qmwx::where('id', $id)->inc($upKey, 1)->update();
        return ['status' => 1, 'msg' => '成功'];
    }

    /**
     * 五行分析
     * @return array
     */
    protected function wxFenxi()
    {
        $data = $this->lunar->getWxFenxi();
        $arrWx = array_merge($data['king'], $data['unking']);
        $arrWx = array_column($arrWx, 'fen', 'wx');
        asort($arrWx);
        $minNum = current($arrWx);
        $total = array_sum($arrWx);
        $que = [];
        $resWx = ['金' => [], '木' => [], '水' => [], '火' => [], '土' => []];
        $queName = '根据五行的占比来看您的五行相对平衡，即金木水火土各自的数目或者说力量差不多，没有悬殊的差别处于中间的比较和谐的状态。请继续查看下面五行喜用析！';
        $queType = 3;
        foreach ($arrWx as $k => $v) {
            $per = round($v * 100 / $total, 2);
            if ($minNum == $v) {
                $que[] = $k;
                if ($per == 0) {
                    $queName = "根据五行的占比来看您的五行缺【x】，五行缺失属于正常现象，可以通过方法补足，所以您不用担心。起名不仅仅是按照五行的个数来判定，而是根据上述五行的数值精确分析后，判定你缺什么五行进行起名，这样有利于宝宝今后各个方面的发展。请继续查看下面五行喜用析！";
                    $queType = 0;
                } elseif ($per <= 10) {
                    $queName = '根据五行的占比来看您的五行轻微缺失【x】，五行轻微缺失属于正常现象，可以通过方法补足，所以您不用担心。起名不仅仅是按照五行的个数来判定，而是根据上述五行的数值精确分析后，判定你缺什么五行进行起名，这样有利于宝宝今后各个方面的发展。请继续查看下面五行喜用析！';
                    $queType = 1;
                } elseif ($per < 20) {
                    $queName = '根据五行的占比来看您的五行轻微缺失【x】，五行轻微缺失属于正常现象，可以通过方法补足，所以您不用担心。起名不仅仅是按照五行的个数来判定，而是根据上述五行的数值精确分析后，判定你缺什么五行进行起名，这样有利于宝宝今后各个方面的发展。请继续查看下面五行喜用析！';
                    $queType = 2;
                }
            }
            $resWx[$k] = [
                'num' => $v,
                'per' => $per,
            ];
        }
        $queName = str_replace('【x】', '【' . implode('，', $que) . '】', $queName);
        $data['fraction'] = $resWx;
        $data['que'] = $que;
        $data['que_type'] = $queType;
        $data['que_name'] = $queName;
        $kingTotal = array_sum(array_column($data['king'], 'fen'));
        $kingPer = round($kingTotal * 100 / $total, 2);
        $data['per'] = [
            'king' => $kingPer,
            'unking' => round(100 - $kingPer, 2),
        ];
        $differ = $data['differ'];
        if ($differ > 0) {
            $differTitle = '偏旺';
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
        }
        $data['differ_title'] = $differTitle;
        return $data;
    }

    /**
     * 获得纪年五行
     * @return array
     */
    protected function getJiNianWxNum(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $godHide = $this->lunar->_getGod();
        $wxlist = WuXing::GZ_TO_WX;
        $wxNums = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        $wxNumsH = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        foreach ($jiNian as $k => $v) {
            foreach ($v as $v1) {
                $wx = $wxlist[$v1];
                $wxNums[$wx]++;
            }
        }
        foreach ($godHide as $v) {
            foreach ($v['hide'] as $v1) {
                $wx = $wxlist[$v1];
                $wxNumsH[$wx]++;
            }
        }
        $wxNumAll = [];
        foreach ($wxNums as $k => $v) {
            $wxNumAll[$k] = $v + $wxNumsH[$k];
        }
        return [
            // 八字五行数量
            'gz' => $wxNums,
            // 八字五行包含藏干
            'all' => $wxNumAll,
        ];
    }

    /**
     * 根据喜用神获得所需要的五行数字
     * @param string $wx 用神+喜神
     * @return array
     */
    protected function getWxByYongXi(string $wx): array
    {
        $list = [
            '木火' => ['木木', '木火', '火火', '火木'],
            '木土' => ['木木', '土土'],
            '木金' => ['木木', '金金'],
            '木水' => ['木木', '木水', '水木', '水水'],
            '火木' => ['火火', '火木', '木火', '木木'],
            '火土' => ['火火', '火土', '土火', '土土'],
            '火金' => ['火火', '金金'],
            '火水' => ['火火', '水水'],
            '土木' => ['土土', '木木'],
            '土火' => ['土土', '火土', '土火', '火火'],
            '土金' => ['土土', '土金', '金土', '金金'],
            '土水' => ['土土', '水水'],
            '金木' => ['金金', '木木'],
            '金火' => ['金金', '火火'],
            '金土' => ['金金', '金土', '土金', '土土'],
            '金水' => ['金金', '金水', '水金', '水水'],
            '水木' => ['水水', '水木', '木水', '木木'],
            '水火' => ['水水', '火火'],
            '水土' => ['水水', '土土'],
            '水金' => ['水水', '水金', '金水', '金金'],
        ];
        $data = $list[$wx];
        $result = [];
        foreach ($data as $v) {
            $result[] = WxAttr::getWxToNum($v);
        }
        return $result;
    }

    /**
     * 根据五行缺失获得使用的五行
     * @param array $que
     * @return array
     */
    protected function getWxByQs(array $que): array
    {
        if (count($que) == 1) {
            $list = [
                '木' => ['木水', '水木'],
                '火' => ['火木', '木火'],
                '土' => ['土火', '火土'],
                '金' => ['土金', '金土'],
                '水' => ['水金', '金水'],
            ];
            $str = implode('', $que);
            $data = $list[$str];
        } else {
            $list = [
                [['木', '火'], ['木木', '火火', '木火', '火木']],
                [['木', '土'], ['木木', '土土']],
                [['木', '金'], ['木木', '金金']],
                [['木', '水'], ['木木', '水水', '木水', '水木']],
                [['火', '土'], ['火火', '土土', '火土', '土火']],
                [['火', '金'], ['火火', '金金']],
                [['火', '水'], ['火火', '水水']],
                [['土', '金'], ['土土', '金金', '土金', '金土']],
                [['土', '水'], ['土土', '水水']],
                [['金', '水'], ['金金', '水水', '金水', '水金']],
            ];
            $data = [];
            foreach ($list as $v) {
                if (in_array($v[0][0], $que) && in_array($v[0][1], $que)) {
                    $data = $v[1];
                }
            }
        }
        $result = [];
        foreach ($data as $v) {
            $result[] = WxAttr::getWxToNum($v);
        }
        return $result;
    }

    /**
     * 根据字库获得名字
     * @param array $wxNum
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNames(array $wxNum)
    {
        if (empty($wxNum)) {
            return [];
        }
        $mingIds = $this->orginData['mings'];
        if ($mingIds) {
            $list = $this->getNamesById($mingIds);
            $mingStr = implode('', array_column($list, 'title'));
            $mingArr = array_values(array_unique(Utils::mbStrSplit($mingStr)));
            $listZi = Cnword::getInfo2($mingArr);
        } else {
            //  0 未出生 1 已出生
            $born = $this->orginData['born'];
            $sex = $this->orginData['sex'];
            $limit = $this->orginData['limit'];
            if ($born) {
                $list = $this->getNamesWithNum($wxNum, $sex, $limit);
            } else {
                $num = (int)($limit / 2);
                $list = $this->getNamesWithNum($wxNum, 0, $num);
                $list1 = $this->getNamesWithNum($wxNum, 1, $num);
                $list = array_merge($list, $list1);
            }
            $list = $this->getNameSort($list, $wxNum);
            $listZi = Cnword::getInfo2(array_keys($this->mingsIn['zi']));
        }

        foreach ($list as $k => $rs) {
            $ziArr = Utils::mbStrSplit(trim($rs['title']));
            $pyArr = array_values(array_filter(explode(' ', $rs['py'])));
            $pyNo = array_values(array_filter(explode(' ', $rs['py_no'])));
            $wxStr = WxAttr::getNumToWx($rs['wx']);
            $wxArr = Utils::mbStrSplit($wxStr);
            foreach ($ziArr as $k1 => $v1) {
                $infoZi = $listZi[$v1];
                $rs['zi'][] = [
                    'zi' => $v1,
                    'big5' => $infoZi['big5'] ?? $v1,
                    'py' => $pyArr[$k1] ?? '',
                    'py_no' => $pyNo[$k1] ?? '',
                    'wx' => $wxArr[$k1] ?? '',
                    'bushou' => $infoZi['bushou'] ?? '',
                    'bihua2' => $infoZi['bihua2'] ?? '',
                    'mean' => $infoZi['detail3']['mean'] ?? '',
                ];
            }
            $list[$k] = $rs;
        }
        return $list;
    }

    /**
     * 从数据库中根据条件获得数据
     * @param array $wxNum 名字五行
     * @param int $sex 性别
     * @param int $num 要取的数量
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getNamesWithNum(array $wxNum, int $sex, int $num): array
    {
        $list = Qmwx::lists($wxNum, $sex, 1000);
        $result = [];
        $jinPy = $this->getJinPy();
        $i = 1;
        // $show = [];
        foreach ($list as $k => $rs) {
            if ($i > $num) {
                break;
            }
            unset($list[$k]);
            $tmpPy = $rs['py'];
            $tmpPyNo = $rs['py_no'];
            // 1、名字不同字但同拼音（带音标）只能出现1次，如：逸风，益丰，只能出现1次
            $numPy = $this->mingsIn['py'][$tmpPy] ?? 0;
            if ($numPy >= 1) {
                //$show[] = "{$rs['title']},{$tmpPy} 拼音（带音标）只能出现1次";
                continue;
            }
            // 2、名字同拼音（不带音标）最多能出现2次，如：益丰，怡枫，一峰，只能出现益丰，怡枫。
            $numPyNo = $this->mingsIn['py_no'][$tmpPyNo] ?? 0;
            if ($numPyNo >= 2) {
                //$show[] = "{$rs['title']},{$tmpPyNo} 拼音（不带音标）最多能出现2次";
                continue;
            }
            // 3、用户下单后所输出的名字，同一个字只能出现2次
            $ziArr = Utils::mbStrSplit($rs['title']);
            $bool = true;
            foreach ($ziArr as $v2) {
                $ziNum = $this->mingsIn['zi'][$v2] ?? 0;
                if ($ziNum >= 2) {
                    $bool = false;
                    break;
                }
            }
            if (false === $bool) {
                //$show[] = "{$rs['title']},用户下单后所输出的名字，同一个字只能出现2次";
                continue;
            }
            // 4、相同拼音（带音标）但不同汉字，最多能出现3次【如：毅，奕】
            $pyArr = explode(' ', $rs['py']);
            foreach ($pyArr as $v2) {
                $ziNum = $this->mingsIn['zi_py'][$v2] ?? 0;
                if ($ziNum >= 3) {
                    $bool = false;
                    break;
                }
            }
            if (false === $bool) {
                // $show[] = "{$rs['title']},相同拼音（带音标）但不同汉字，最多能出现3次";
                continue;
            }
            // 5、相同拼音（不带音标），最多能出现6次【如：一，怡】（6次是加上3和4两条的总次数）
            $pyNoArr = explode(' ', $rs['py_no']);
            foreach ($pyNoArr as $v2) {
                $ziNum = $this->mingsIn['zi_py_no'][$v2] ?? 0;
                if ($ziNum >= 6) {
                    $bool = false;
                    break;
                }
            }
            if (false === $bool) {
                // $show[] = "{$rs['title']},相同拼音（不带音标），最多能出现6次";
                continue;
            }
            // 7、去除用户填写的禁用文字（文字本身+拼音（带音标））
            foreach ($pyArr as $v2) {
                if (in_array($v2, $jinPy)) {
                    $bool = false;
                    break;
                }
            }
            if (false === $bool) {
                // $show[] = "{$rs['title']},去除用户填写的禁用文字（文字本身+拼音（带音标））";
                continue;
            }
            $this->mingsIn['py'][$tmpPy] = $numPy + 1;
            $this->mingsIn['py_no'][$tmpPyNo] = $numPyNo + 1;
            foreach ($ziArr as $v) {
                $ziNum = $this->mingsIn['zi'][$v] ?? 0;
                $this->mingsIn['zi'][$v] = $ziNum + 1;
            }
            foreach ($pyArr as $v) {
                $ziNum = $this->mingsIn['zi_py'][$v] ?? 0;
                $this->mingsIn['zi_py'][$v] = $ziNum + 1;
            }
            foreach ($pyNoArr as $v) {
                $ziNum = $this->mingsIn['zi_py_no'][$v] ?? 0;
                $this->mingsIn['zi_py_no'][$v] = $ziNum + 1;
            }
            $result[] = $rs;
            $i++;
        }
        return $result;
    }

    /**
     * 禁用拼音
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getJinPy()
    {
        if ($this->jinRes) {
            return $this->jinRes;
        }
        $jin = $this->orginData['jin'];
        if (empty($jin)) {
            return [];
        }
        $jinArr = Utils::mbStrSplit($jin);
        $list = Cnword::getInfo2($jinArr);
        $result = [];
        foreach ($list as $rs) {
            $py = str_replace(['，', ' ', '、'], ',', $rs['py2']);
            $pyArr = explode(',', $py);
            foreach ($pyArr as $v1) {
                if (empty($v1)) {
                    continue;
                }
                $result[$v1] = $v1;
            }
        }
        $this->jinRes = array_values($result);
        return $this->jinRes;
    }

    /**
     * 根据ID获得名字
     * @return array
     */
    protected function getNamesById(string $mingIds)
    {
        if (empty($mingIds)) {
            return [];
        }
        $ids = explode(',', $mingIds);
        $list = Qmwx::whereIn('id', $ids)
            ->column('id,title,sex,wx,py,py_no,py1,explain', 'id');
        $result = [];
        foreach ($ids as $id) {
            if (!isset($list[$id])) {
                continue;
            }
            $result[] = $list[$id];
        }
        return $result;
    }

    /**
     * @param array $list 取出的名字列表
     * @param array $wxN
     * @return array
     */
    protected function getNameSort(array $list, array $wxN)
    {
        $list1 = [];
        foreach ($wxN as $v) {
            $list1[$v] = [];
        }
        foreach ($list as $v) {
            $list1[$v['wx']][] = $v;
        }
        $list = [];
        foreach ($list1 as $rs) {
            $list = array_merge($list, $rs);
        }
        return $list;
    }
}
