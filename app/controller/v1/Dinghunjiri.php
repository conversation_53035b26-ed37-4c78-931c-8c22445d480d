<?php
// +----------------------------------------------------------------------
// | Dinghunjiri.订婚吉日
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\traits\JiRiCheckTraits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class Dinghunjiri
{
    use JiRiCheckTraits;

    /**
     * @var array 输入的数据
     */
    protected array $orginData;

    /**
     * @var Ex 男方生日类
     */
    protected Ex $lunarM;

    /**
     * @var Ex 女方生日类
     */
    protected Ex $lunarF;

    /**
     * 气往亡
     * @var array
     */
    protected array $qiWangwan = [];

    /**
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 男方生日
            'mtime' => input('mtime', '', 'trim'),
            // 女方生日
            'ftime' => input('ftime', '', 'trim'),
            // 订单时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
            // 男方父亲出生年份
            'mfather' => input('mfather', '', 'trim'),
            // 男方母亲的出生年份
            'mmon' => input('mmon', '', 'trim'),
            // 女方父亲出生年份
            'ffather' => input('ffather', '', 'trim'),
            // 女方母亲的出生年份
            'fmon' => input('fmon', '', 'trim'),
            // 限定的天数
            'limit' => input('limit', 0, 'intval'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'mtime|男出生时间' => ['require', 'isDateOrTime:男出生时间'],
                'ftime|女出生时间' => ['require', 'isDateOrTime:女出生时间'],
                'mfather|男方父亲出年年份' => ['between:1930,2020'],
                'mmon|男方母亲出年年份' => ['between:1930,2020'],
                'ffather|女方父亲出年年份' => ['number', 'between:1930,2020'],
                'fmon|女方母亲出年年份' => ['between:1930,2020'],
                'otime|订单时间' => ['require', 'dateFormat:Y-m-d'],
                'limit|查看天数' => ['require', 'between:1,720'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunarM = Ex::date($data['mtime'])->sex(0);
        $this->lunarF = Ex::date($data['ftime'])->sex(1);
        $this->orginData = $data;
        $result = [
            'm' => [
                'base' => $this->lunarM->getLunarByBetween(),
                // 纳音
                'nayin' => $this->lunarM->getNayin(),
                // 地势
                'terrain' => $this->lunarM->getTerrain(),
                'like_god' => $data['source'] == 'wnl' ? $this->lunarM->getLikeGod2() : $this->lunarM->getLikeGod(),
                // 阳气
                'yangqi' => $this->getYangQi(),
            ],
            'f' => [
                'base' => $this->lunarF->getLunarByBetween(),
                'nayin' => $this->lunarF->getNayin(),
                // 地势
                'terrain' => $this->lunarF->getTerrain(),
                'like_god' => $data['source'] == 'wnl' ? $this->lunarF->getLikeGod2() : $this->lunarF->getLikeGod(),
                // 阴胎天干地支
                'yintai' => $this->getYinTai(),
            ],
            'ji' => $this->getJiri(),
        ];
        return $result;
    }

    /**
     * 获得吉日
     * @return array
     * @throws Exception
     */
    protected function getJiri()
    {
        $startTime = $this->orginData['otime'];
        $limit = $this->orginData['limit'];
        // 破日 月份+破日
        $listPo = ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'];
        // 往亡
        $listWangWang = ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'];
        // 归忌
        $listGuiJi = ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'];
        // 受死 农历月份+日支
        $listShouSi = ['1|戌', '2|辰', '3|亥', '4|巳', '5|子', '6|午', '7|丑', '8|未', '9|寅', '10|申', '11|卯', '12|酉'];
        // 小红沙日 农历月份+日支
        $listHongSha = ['1|巳', '2|酉', '3|丑', '4|巳', '5|酉', '6|丑', '7|巳', '8|酉', '9|丑', '10|巳', '11|酉', '12|丑'];
        // 杨公忌日 农历月份+日
        $listYangGong = ['1-13', '2-11', '3-9', '4-7', '5-5', '6-3', '7-1', '7-29', '8-27', '9-25', '10-23', '11-21', '12-19'];
        $list2 = ['春分', '秋分', '夏至', '冬至', '立春', '立夏', '立秋', '立冬'];
        $listChong = [
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        $listXiongShi = [
            '子' => '午时中午11点至13点', '丑' => '未时下午13点至15点', '寅' => '申时下午15点至17点', '卯' => '酉时下午17点至19点',
            '辰' => '戌时晚上19点至21点', '巳' => '亥时晚上21点至23点', '午' => '子时晚上23点至1点', '未' => '丑时凌晨1点至3点',
            '申' => '寅时凌晨3点至5点', '酉' => '卯时早上5点至7点', '戌' => '辰时早上7点至9点', '亥' => '巳时早上9点至11点',
        ];
        // 不利月  翁姑月 父母月 妨夫月 妨妇月
        $listBuliYue = [
            '子' => [2, 3, 4, 11], '丑' => [9, 2, 1, 12], '寅' => [4, 5, 6, 1], '卯' => [5, 4, 3, 2],
            '辰' => [6, 1, 2, 3], '巳' => [1, 6, 5, 4], '午' => [8, 9, 10, 5], '未' => [3, 8, 7, 6],
            '申' => [10, 11, 12, 7], '酉' => [11, 10, 9, 8], '戌' => [12, 7, 8, 9], '亥' => [7, 12, 11, 10],
        ];
        // 父母生冲干支
        $zhengChong = $this->getZhenChong();
        $yearGz = $this->lunarM->getLunarGanzhiYear();
        $yearGz1 = $this->lunarF->getLunarGanzhiYear();
        $jiNianF = $this->lunarF->getLunarTganDzhi();
        $jiNianM = $this->lunarM->getLunarTganDzhi();
        $nvMing = $this->getNvMing($jiNianF['d'][0]);
        $listBuliYue1 = $listBuliYue[$yearGz1[1]];
        $sx1 = $this->lunarF->getZodiac();
        // 大利月
        $daliYue = $this->getDaLiYue($sx1);
        // 小利月
        $xiaoLiYue = $this->getXiaoLiYue($sx1);
        $result = [];
        for ($i = 1; $i <= $limit; $i++) {
            $time = strtotime("{$startTime} +{$i} day");
            $timeStr = date('Y年m月d日', $time);
            $yearLiu = date('Y', $time);
            $day = date('d', $time);
            $week = Huangli::getWeekChs($time);
            $huangli = Huangli::date($time);
            $base = $huangli->getLunarByBetween();
            $jiNian = $base['jinian'];
            $nongliYueNum = $base['_nongli']['m'];
            $mzDz = $jiNian['m'][1] . $jiNian['d'][1];
            $rgz = implode('', $jiNian['d']);
            $nongliNumberStr = $base['_nongli']['m'] . '_' . $base['_nongli']['d'];

            // 重阳节（九月初九）、寒衣节（十月初十）七月半（七月十五） 七月十四
            if (in_array($nongliNumberStr, ['7_14', '7_15', '9_9', '10_10'])) {
                continue;
            }
            // 彭祖忌 亥日例：所有地支为亥的日子都去掉
            if ($jiNian['d'][1] === '亥') {
                continue;
            }
            $zhiRi = $huangli->getZhiRi();
            if ($zhiRi['huan_dao'] == '黑道') {
                continue;
            }
            // 四立两分两至（反目日） 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至
            $jieQi = Huangli::isJieQi($time);
            if ($jieQi && in_array($jieQi, $list2)) {
                continue;
            }
            // 朔日（与横天朱雀同一天） 初一	例：所有农历初一的日子都去掉 和十五
            if ($base['_nongli']['d'] == 1 || $base['_nongli']['d'] == 15) {
                continue;
            }
            // 清明、七月半（七月十五）
            if ($jieQi === '清明') {
                continue;
            }
            // 去除亢金龙
            $xingSu = $huangli->getXingSu();
            if ($xingSu[0] === '亢金龙') {
                continue;
            }
            // 去除破日+往亡+归忌
            if (in_array($mzDz, $listPo) || in_array($mzDz, $listWangWang) || in_array($mzDz, $listGuiJi)) {
                continue;
            }
            // 受死+小红沙日
            $strShouSi = $base['_nongli']['m'] . '|' . $jiNian['d'][1];
            if (in_array($strShouSi, $listShouSi) || in_array($strShouSi, $listHongSha)) {
                continue;
            }
            $strYangGong = $base['_nongli']['m'] . '-' . $base['_nongli']['d'];
            // 杨公忌日
            if (in_array($strYangGong, $listYangGong)) {
                continue;
            }
            // 四离四绝 立春、立夏、立秋、立冬、春分、秋分、夏至、冬至的前一天
            $tomorrowJieQi = Huangli::isJieQi($time + 86400);
            if ($tomorrowJieQi && in_array($tomorrowJieQi, $list2)) {
                continue;
            }
            // 干支
            $tmpGZ = [
                implode('', $jiNian['y']), implode('', $jiNian['m']), implode('', $jiNian['d']),
            ];
            // 气往亡
            $qiWangwanTmp = $this->getQiWangwang($yearLiu);
            if (in_array($timeStr, $qiWangwanTmp)) {
                continue;
            }
            // 1、去掉与新人双方生肖相冲的日子
            if (in_array($yearGz[1] . $jiNian['d'][1], $listChong) || in_array($yearGz1[1] . $jiNian['d'][1], $listChong)) {
                continue;
            }
            // 2、去掉女命日元与流日成忌神的日子
            // 3、根据女命日干去除不能用的日子
            if (in_array($rgz, $nvMing)) {
                continue;
            }
            // 4、根据男女命月支去掉对冲日。
            if ($this->checkDuiChong($jiNianF['m'][1], $jiNian['d'][1]) || $this->checkDuiChong($jiNianM['m'][1], $jiNian['d'][1])) {
                continue;
            }
            // 正冲
            if (in_array($rgz, $zhengChong)) {
                continue;
            }
            $rdz = $jiNian['d'][1];
            $tmpRes = [
                'time' => $time,
                'gongli' => [
                    'y' => (int)date('Y', $time),
                    'm' => (int)date('m', $time),
                    'd' => (int)date('d', $time),
                ],
                'week' => $week,
                'nongli' => $base['nongli'],
                //凶时
                'xiong' => $listXiongShi[$rdz] ?? '',
                'chong' => $this->getChongByRgz($rgz),
                'detail' => [],
            ];
            $nongliMonthNum = (int)$base['_nongli']['m'];
            $keyRes = $base['_nongli']['y'] . '年' . $base['_nongli']['m'] . '月(农历)';
            if ($this->checkBuJian($nongliMonthNum, $tmpGZ[2])) {
                $tmpRes['detail'][] = ['不将', '有利嫁娶的传统吉日，寓意新人以后的生活平平顺顺。'];
            }
            if ($this->checkJiFen($nongliMonthNum, $tmpGZ[2])) {
                $tmpRes['detail'][] = ['季分', '嫁娶、订婚传统吉日，寓意幸福。'];
            }
            if ($this->checkSuiDe($jiNian['y'][0] . $jiNian['d'][0])) {
                $tmpRes['detail'][] = ['岁德', '德神护佑的吉日，积福之日，福气汇聚。'];
            }
            if ($this->checkSuiDeHe($jiNian['y'][0] . $jiNian['d'][0])) {
                $tmpRes['detail'][] = ['岁德合', '德神护佑的吉日，积福之日，福气汇聚。'];
            }
            $strmzrz = $jiNian['m'][1] . $jiNian['d'][1];
            if ($this->checkHongLuanTianXi($strmzrz)) {
                $tmpRes['detail'][] = ['红鸾天喜', '有利嫁娶的吉日，红鸾报喜。'];
            }
            if ($this->getSanHeByDz($strmzrz)) {
                $tmpRes['detail'][] = ['三合', '寓意新人互生互利的吉日，可作为领证的日子选用。'];
            }
            // 月支+日干
            $strmzrg = $jiNian['m'][1] . $jiNian['d'][0];
            if ($this->checkTianDe($strmzrg) || $this->checkTianDe($strmzrz)) {
                $tmpRes['detail'][] = ['天德', '占得天时，有三合旺气，是上等的吉日。与月德一起出现，寓意万福大吉。'];
            }
            if ($this->checkTianDeHe($strmzrg) || $this->checkTianDeHe($strmzrz)) {
                $tmpRes['detail'][] = ['天德合', '合德之神相助，各种凶煞伏藏不出，有小福的日子。'];
            }
            if ($this->checkYueDe($strmzrz)) {
                $tmpRes['detail'][] = ['月德', '得太阴福德的日子，吉神作用稍逊于天德。与天德一起出现，寓意万福大吉。'];
            }
            if ($this->checkYueDeHe($strmzrg) || $this->checkYueDeHe($strmzrz)) {
                $tmpRes['detail'][] = ['月德合', '得到五行力量的聚合，为有福之日，适宜进行入宅、祭祖等事。'];
            }
            if ($this->checkTianSe($jiNian['m'][1], $tmpGZ[2])) {
                $tmpRes['detail'][] = ['天赦', '天帝赦罪日，四季大吉，有利于消灾化煞，适合做任何事。'];
            }
            if ($this->checkTianYuan($jiNian['m'][1] . $tmpGZ[2])) {
                $tmpRes['detail'][] = ['天愿', '五行在这天有极旺的气势，喜神眷顾，能趋吉避凶。'];
            }
            if ($this->checkYueEn($strmzrg)) {
                $tmpRes['detail'][] = ['月恩', '受恩之日，这天适合开始一段新的生活。'];
            }
            if ($this->checkSiXian($jiNian['m'][1], $jiNian['d'][1])) {
                $tmpRes['detail'][] = ['四相', '拥有四时王相贵气的日子。'];
            }
            if ($this->checkShiDe($strmzrz)) {
                $tmpRes['detail'][] = ['时德', '得到天地舒畅之气，为四时所生，适宜祈福的日子。'];
            }
            if ($this->checkXianXing($nongliMonthNum, $tmpGZ[2])) {
                $tmpRes['detail'][] = ['显星', '三皇吉星之一，有步步高升的寓意，有益不动产，象征富贵兴旺，大发财源。'];
            }
            if ($this->checkQuXing($nongliMonthNum, $tmpGZ[2])) {
                $tmpRes['detail'][] = ['曲星', '三皇吉星之一，有早生贵子的寓意，百事吉庆，对财运有益，喜事连连。'];
            }
            if ($this->checkChuanXing($nongliMonthNum, $tmpGZ[2])) {
                $tmpRes['detail'][] = ['传星', '三皇吉星之一，有加官进禄的寓意，事事称心，象征牛马兴旺，生财致富。'];
            }
            if ($jiNian['m'][1] === $jiNian['d'][1]) {
                $tmpRes['detail'][] = ['天帝', '先天卦数中，蕴含日月旺气，均衡融合的吉日。'];
            }
            $monthTitle = [];
            if (in_array($nongliYueNum, $daliYue)) {
                $monthTitle = ['大利月', '女方大利月，本月吉日为上上之选。'];
            } elseif (in_array($nongliYueNum, $xiaoLiYue)) {
                $monthTitle = ['小利月', '女方小利月，比大利月吉日较次，可做备选日。'];
            } elseif ($nongliYueNum == $listBuliYue1[0]) {
                $monthTitle = ['翁姑月', '本月订婚吉日当天若不是天月岁德合日等，新人进家门，翁姑暂避则吉。'];
            } elseif ($nongliYueNum == $listBuliYue1[1]) {
                $monthTitle = ['父母月', '本月订婚吉日当天若不是天月岁德合日等，女方离开自家时，父母勿出送则吉。'];
            } elseif ($nongliYueNum == $listBuliYue1[2]) {
                $monthTitle = ['妨夫月', '不利男方运势的月份，若吉神多，可权用。'];
            } elseif ($nongliYueNum == $listBuliYue1[3]) {
                $monthTitle = ['妨妇月', '不利女方运势的月份，若吉神多，可权用。'];
            }
            if (empty($tmpRes['detail'])) {
                continue;
            }
            $result[$keyRes]['title'] = $monthTitle;
            $result[$keyRes]['ym'] = $keyRes;
            $numByMonth = $result[$keyRes]['num'] ?? 0;
            $result[$keyRes]['num'] = ($numByMonth + 1);
            $result[$keyRes]['info'][] = $tmpRes;
        }
        return array_values($result);
    }

    /**
     * 根据日干获得不能用的天干地支
     * @param string $tg
     * @return array
     */
    protected function getNvMing(string $tg): array
    {
        $list = [
            '甲' => ['丁丑', '壬申'], '乙' => ['丙戌', '癸巳'], '丙' => ['己亥', '甲辰'], '丁' => ['戊辰', '乙卯'], '戊' => ['辛酉', '丙寅'],
            '己' => ['庚辰', '丁丑'], '庚' => ['癸巳', '戊子'], '辛' => ['壬寅', '己亥'], '壬' => ['乙卯', '庚戌'], '癸' => ['甲子', '辛酉'],
        ];
        return $list[$tg] ?? $list['甲'];
    }

    /**
     * 根据日天干获得妻星和天官
     * @param string $tg
     * @return array
     */
    protected function getManQiAndGuan(string $tg): array
    {
        /**
         * 妻星    天官
         */
        $list = [
            '甲' => ['己巳', '辛未'], '乙' => ['戊寅', '辛未'], '丙' => ['辛卯', '癸巳'], '丁' => ['庚戌', '壬寅'], '戊' => ['癸亥', '乙卯'],
            '己' => ['壬申', '甲戌'], '庚' => ['乙酉', '丁亥'], '辛' => ['甲午', '丙申'], '壬' => ['丁未', '己酉'], '癸' => ['丙辰', '戊午'],
        ];
        $data = $list[$tg];
        return [
            'qi' => $data[0],
            'tian' => $data[1],
        ];
    }

    /**
     * 根据日天干获得夫星和天嗣
     * @param string $tg
     * @return array
     */
    protected function getFuAndTian(string $tg): array
    {
        $list = [
            '甲' => ['辛未', '丙寅'], '乙' => ['庚辰', '丁亥'], '丙' => ['癸巳', '戊戌'], '丁' => ['壬寅', '己酉'], '戊' => ['乙卯', '庚申'],
            '己' => ['甲戌', '辛未'], '庚' => ['丁亥', '壬午'], '辛' => ['丙申', '癸巳'], '壬' => ['己酉', '甲辰'], '癸' => ['戊午', '乙卯'],
        ];
        $data = $list[$tg];
        return [
            'qi' => $data[0],
            'tian' => $data[1],
        ];
    }

    /**
     * 获得阳气干支
     * @return array
     * @throws \Exception
     */
    protected function getYangQi(): array
    {
        $list = [
            '甲' => '乙', '乙' => '丙', '丙' => '丁', '丁' => '戊', '戊' => '己',
            '己' => '庚', '庚' => '辛', '辛' => '壬', '壬' => '癸', '癸' => '甲',
        ];
        $list1 = [
            '子' => '卯', '丑' => '辰', '寅' => '巳', '卯' => '午', '辰' => '未', '巳' => '申',
            '午' => '酉', '未' => '戌', '申' => '亥', '酉' => '子', '戌' => '丑', '亥' => '寅',
        ];
        $gz = $this->lunarM->getLunarGanzhiMonth();
        return [
            $list[$gz[0]], $list1[$gz[1]],
        ];
    }

    /**
     * 根据女方月干支获得阴胎干支
     * @return array
     * @throws \Exception
     */
    protected function getYinTai(): array
    {
        $list = [
            '甲' => '乙', '乙' => '丙', '丙' => '丁', '丁' => '戊', '戊' => '己',
            '己' => '庚', '庚' => '辛', '辛' => '壬', '壬' => '癸', '癸' => '甲',
        ];
        $list1 = [
            '子' => '卯', '丑' => '辰', '寅' => '巳', '卯' => '午', '辰' => '未', '巳' => '申',
            '午' => '酉', '未' => '戌', '申' => '亥', '酉' => '子', '戌' => '丑', '亥' => '寅',
        ];
        $gz = $this->lunarF->getLunarGanzhiMonth();
        return [
            $list[$gz[0]], $list1[$gz[1]],
        ];
    }

    /**
     * 根据年份获得气往亡日期
     * @param $year
     * @return array
     */
    protected function getQiWangwang($year): array
    {
        if (isset($this->qiWangwan[$year])) {
            return $this->qiWangwan[$year];
        }
        $jieQiAll = SolarTerm::getAllJieQi($year);
        // 立春后第七日；惊蛰后十四日；清明后二十一日；立夏后第八日；芒种后十六日；小暑后二十四日；立秋后第九日；白露后十八日；
        // 寒露后二十七日；立冬后十日；大雪后二十日；小寒后三十日。比如说，2018年4月5日是清明，21天后的4月26日为天门日犯往亡煞。
        $result = [
            date('Y-m-d', strtotime("{$jieQiAll['立春']} +7 day")),
            date('Y-m-d', strtotime("{$jieQiAll['惊蛰']} +14 day")),
            date('Y-m-d', strtotime("{$jieQiAll['清明']} +21 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立夏']} +8 day")),
            date('Y-m-d', strtotime("{$jieQiAll['芒种']} +16 day")),
            date('Y-m-d', strtotime("{$jieQiAll['小暑']} +24 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立秋']} +9 day")),
            date('Y-m-d', strtotime("{$jieQiAll['白露']} +18 day")),
            date('Y-m-d', strtotime("{$jieQiAll['寒露']} +27 day")),
            date('Y-m-d', strtotime("{$jieQiAll['立冬']} +10 day")),
            date('Y-m-d', strtotime("{$jieQiAll['大雪']} +20 day")),
            date('Y-m-d', strtotime("{$jieQiAll['小寒']} +30 day")),
        ];
        $this->qiWangwan[$year] = $result;
        return $result;
    }

    /**
     *月支和日支检查对冲日。
     * @param string $mdz 月支
     * @param string $rdz 日支
     * @return bool
     */
    protected function checkDuiChong(string $mdz, string $rdz): bool
    {
        $list = [
            '子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申',
        ];
        if (in_array($mdz . $rdz, $list)) {
            return true;
        }
        return false;
    }

    /**
     * 根据父母出生年份求出正冲干支
     * @return  array
     */
    protected function getZhenChong(): array
    {
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        $yearList = [
            $this->orginData['mfather'], $this->orginData['mmon'], $this->orginData['ffather'], $this->orginData['fmon'],
        ];
        $yearList = array_unique($yearList);
        $result = [];
        foreach ($yearList as $v) {
            if ($v < 1920 || $v > 2050) {
                continue;
            }
            $gz = BaziExt::getGanZhi($v);
            $gzStr = implode('', $gz);
            $result[] = $list[$gzStr];
        }
        return array_unique($result);
    }

    /**
     * 根据日柱
     * @param $rgz
     * @return array
     */
    protected function getChongByRgz($rgz)
    {
        // 正冲
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        // 正冲对应年份
        $list1 = [
            '甲子' => [1984, '鼠'], '乙丑' => [1985, '牛'], '丙寅' => [1986, '虎'], '丁卯' => [1987, '兔'], '戊辰' => [1988, '龙'],
            '己巳' => [1989, '蛇'], '庚午' => [1990, '马'], '辛未' => [1991, '羊'], '壬申' => [1992, '猴'], '癸酉' => [1993, '鸡'],
            '甲戌' => [1994, '狗'], '乙亥' => [1995, '猪'], '丙子' => [1996, '鼠'], '丁丑' => [1997, '牛'], '戊寅' => [1998, '虎'],
            '己卯' => [1999, '兔'], '庚辰' => [1940, '龙'], '辛巳' => [1941, '蛇'], '壬午' => [1942, '马'], '癸未' => [1943, '羊'],
            '甲申' => [1944, '猴'], '乙酉' => [1945, '鸡'], '丙戌' => [1946, '狗'], '丁亥' => [1947, '猪'], '戊子' => [1948, '鼠'],
            '己丑' => [1949, '牛'], '庚寅' => [1950, '虎'], '辛卯' => [1951, '兔'], '壬辰' => [1952, '龙'], '癸巳' => [1953, '蛇'],
            '甲午' => [1954, '马'], '乙未' => [1955, '羊'], '丙申' => [1956, '猴'], '丁酉' => [1957, '鸡'], '戊戌' => [1958, '狗'],
            '己亥' => [1959, '猪'], '庚子' => [1960, '鼠'], '辛丑' => [1961, '牛'], '壬寅' => [1962, '虎'], '癸卯' => [1963, '兔'],
            '甲辰' => [1964, '龙'], '乙巳' => [1965, '蛇'], '丙午' => [1966, '马'], '丁未' => [1967, '羊'], '戊申' => [1968, '猴'],
            '己酉' => [1969, '鸡'], '庚戌' => [1970, '狗'], '辛亥' => [1971, '猪'], '壬子' => [1972, '鼠'], '癸丑' => [1973, '牛'],
            '甲寅' => [1974, '虎'], '乙卯' => [1975, '兔'], '丙辰' => [1976, '龙'], '丁巳' => [1977, '蛇'], '戊午' => [1978, '马'],
            '己未' => [1979, '羊'], '庚申' => [1980, '猴'], '辛酉' => [1981, '鸡'], '壬戌' => [1982, '狗'], '癸亥' => [1983, '猪'],
        ];
        $gz = $list[$rgz];
        return $list1[$gz];
    }
}
