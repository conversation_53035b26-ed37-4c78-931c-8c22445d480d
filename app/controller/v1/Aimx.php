<?php
// +----------------------------------------------------------------------
// | Aimx. AI面相
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\bazi\ShenSha;
use app\lib\ziwei\ZiweiData;
use app\model\more\Dayun;
use app\traits\liunian\Base2019Traits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Aimx
{
    use Base2019Traits;

    /**
     * 日期相关
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 紫薇相关
     * @var ZiweiData
     */
    protected ZiweiData $ziwei;

    /**
     * 2019年，纪年
     * @var array
     */
    protected array $liuNian = [
        2019, ['己', '亥'],
    ];

    /**
     * 当前大运纪年
     * @var array
     */
    protected array $curDaYunYear = [];

    /**
     * 天干地支五行
     * @var array
     */
    protected array $wuXingAttr = [];

    /**
     * 用户纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 十二地支
     * @var string[]
     */
    protected array $dz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * 天干
     * @var string[]
     */
    protected array $tg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

    /**
     * 阴阳
     * @var string
     */
    protected string $yinyang = '';

    /**
     * AI面相
     * @return array
     * @throws \DateMalformedStringException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 年龄
            'age' => input('age', 0, 'intval'),
            // 性别 0男 1女
            'sex' => input('sex', -1, 'intval'),
            // 测算时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'age|年龄' => ['require', 'between:1,100'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|测算时间' => ['require', 'isDateOrTime:测算时间'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $age = $data['age'];
        $otime = preg_match('/^[\d|-]\d+$/i', $data['otime']) ? $data['otime'] : strtotime($data['otime']);
        $oYear = (int)date('Y', $otime);
        $this->liuNian = [
            $oYear, BaziExt::getGanZhi($oYear),
        ];
        $userYear = $oYear - $age;
        $time = date("{$userYear}-m-d H:i", $otime);
        // 0 男 1 女
        $sex = $data['sex'];
        $this->lunar = Ex::date($time)->sex($sex);
        $this->ziwei = new ZiweiData('无名', $time, $sex, $data['otime']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        $listWr = BaziExt::$wr;
        $wr = $listWr[$base['jinian']['d'][0]][$base['jinian']['m'][1]];
        $this->jiNian = $base['jinian'];
        $dgz = $base['jinian']['d'];
        $hour = $this->lunar->getLunarGanzhiHour()[1] . '时';
        $fate = $this->lunar->getFate();
        $i = BaziExt::getKeyWithArray($oYear, $fate['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $shiShengKey = $fate['eight']['_god'][$i];
        $yearNameCur = $fate['eight']['chronlogy_year'][$i];
        $yearNameCurList = preg_split('/(?<!^)(?!$)/u', $yearNameCur);
        $this->curDaYunYear = $yearNameCurList;
        // 岁运
        $suiYun = $this->lunar->getGodNameByTg($dgz[0], '戊');

        $curryear = $fate['eight']['year'][$i];
        $yearNameCurStr = $yearNameCur . '年（' . $curryear . '年-' . ($curryear + 10) . '年）';
        $daYunShi = $this->getDaYunShi($dgz, $yearNameCurList);
        // 设阴阳
        $tmpindex = (int)array_search($base['jinian']['y'][0], $this->tg);
        if ($tmpindex % 2 == 0) {
            $this->yinyang = '阳';
        } else {
            $this->yinyang = '阴';
        }
        $shShaInfo = new ShenSha($this->lunar, $this->liuNian);
        $god = $this->lunar->getGod();
        $result = [
            'hour' => $hour,
            // 纪年
            'base' => $base,
            // 面相概述
            'facemain' => $this->getFaceMain($god['year']),
            // 眼睛
            'face_eye' => $this->getFaceEye($god['year'], $wr),
            // 眉毛结果
            'face_eye_brow' => $this->getFaceEyeBrow($god['year'], $wr),
            // 命宫
            'minggong' => implode('', $shShaInfo->getMingGong()),
            // 身宫
            'shengong' => $this->getShenGong($base),
            // 天干十神
            'god' => $god,
            // 地支十神
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'nayin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 旺相休求死
            'wxxqs' => $this->lunar->getWxxqs(),
            // 喜用神
            'like' => $this->lunar->getLikeGod(),
            // 获取胎元
            'fetus' => $this->lunar->getFetus(),
            // 获取日空
            'empty' => $this->lunar->getEmptyDay(),
            // 大运
            'fate' => $fate,
            'dianping' => [
                'year' => $yearNameCurStr,
                'shi' => $daYunShi,
                'info' => $this->getDianPin($shiShengKey, $suiYun),
            ],
            // 神煞
            'shensha' => $this->getShenSha(),
            // 财富
            'caifu' => $this->getCaiFu($base['jinian']['y'][1], $this->liuNian[1][1]),
            // 事业
            'shiye' => $this->getShiYe(),
            // 感情
            'ganqing' => $this->getGanQing(),
            // 友情(人际)
            'youqing' => $this->getYouQing(),
            // 健康
            'jiankan' => $this->getJianKan(),
            // 学业
            'xueye' => $this->getXueYe(),
            // 家庭
            'jiating' => $this->getJiaTing(),
            // 主星影响和预示
            'zhuxing' => $this->getZhuXing(),
            // 开运
            'kaiyun' => $this->getKaiYun(),
            // 流月
            'liuyue' => $this->getLiuyue(),
        ];
        return $result;
    }

    /**
     * 返回大运诗
     * @param array $dgz 日干支
     * @param array $yearNameCurList 当前大运干支
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getDaYunShi($dgz, $yearNameCurList): array
    {
        $res = [];
        $d1 = $dgz[0] . $yearNameCurList[0];
        $d2 = $dgz[0] . $yearNameCurList[1];
        $data = Dayun::where('gz', 'in', [$d1, $d2])->field('info')->cache(300)->select();
        foreach ($data as $v) {
            $res = array_merge_recursive($res, $v['info']);
        }
        return $res;
    }

    /**
     * 开运
     * @return array
     */
    private function getKaiYun(): array
    {
        $year = $this->liuNian[0];
        $list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        $listPos = ['中宫', '西北', '正西', '东北', '正南', '正北', '西南', '正东', '东南'];
        $listPos2 = ['中央', '西北', '正西', '东北', '正南', '正北', '西南', '正东', '东南'];
        $diff = (int)abs($year - 2017) % 9;
        $index = $year >= 2017 ? (9 - $diff) : $diff;
        $currList = [];
        foreach ($list as $k => $v) {
            $tmpIndex = ($index + $k) % 9;
            $currList[$list[$tmpIndex]] = $k;
        }
        return [
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]], $listPos2[$currList[1]]],
            [$listPos[$currList[3]], $listPos[$currList[3]], $listPos[$currList[3]], $listPos[$currList[3]]],
            [$listPos[$currList[9]], $listPos[$currList[9]]],
            [],
            [$listPos[$currList[4]], $listPos[$currList[8]]],
            [$listPos[$currList[8]], $listPos[$currList[8]]],
            [$listPos[$currList[3]], $listPos[$currList[3]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]]], //重复
            [$listPos[$currList[5]], $listPos2[$currList[2]], $listPos[$currList[5]], $listPos[$currList[5]], $listPos2[$currList[2]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]]],
        ];
    }

    /**
     * 身宫
     * @param $Lunar
     * @return string
     */
    private function getShenGong($Lunar): string
    {
        $dz = ['', '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        $lists = [
            '寅' => ['丙寅', '戊寅', '庚寅', '壬寅', '甲寅'],
            '卯' => ['丁卯', '己卯', '辛卯', '癸卯', '乙卯'],
            '辰' => ['戊辰', '庚辰', '壬辰', '甲辰', '丙辰'],
            '巳' => ['己巳', '辛巳', '癸巳', '乙巳', '丁巳'],
            '午' => ['庚午', '壬午', '甲午', '丙午', '戊午'],
            '未' => ['辛未', '癸未', '乙未', '丁未', '己未'],
            '申' => ['壬申', '甲申', '丙申', '戊申', '庚申'],
            '酉' => ['癸酉', '乙酉', '丁酉', '己酉', '辛酉'],
            '戌' => ['甲戌', '丙戌', '戊戌', '庚戌', '壬戌'],
            '亥' => ['乙亥', '丁亥', '己亥', '辛亥', '癸亥'],
            '子' => ['丙子', '戊子', '庚子', '壬子', '甲子'],
            '丑' => ['丁丑', '己丑', '辛丑', '癸丑', '乙丑'],
        ];
        $start = (int)$Lunar['_nongli']['m'];
        $hIndex = (int)array_search($Lunar['jinian']['h'][1], $dz);
        $dIndex = 0;
        if ($hIndex > 10) {
            $dIndex = $hIndex;
        } else {
            $dIndex = 10 - $hIndex;
        }
        $sIndex = ($start - $dIndex + 12) % 12;
        $sdz = $dz[$sIndex];
        if ($sIndex == 0) {
            $sdz = '亥';
        }
        $tg = $Lunar['jinian']['y'][0];
        $tgIndex = 0;
        if (in_array($tg, ["甲", "己"])) {
            $tgIndex = 0;
        } elseif (in_array($tg, ["乙", "庚"])) {
            $tgIndex = 1;
        } elseif (in_array($tg, ["丙", "辛"])) {
            $tgIndex = 2;
        } elseif (in_array($tg, ["丁", "壬"])) {
            $tgIndex = 3;
        } else {
            $tgIndex = 4;
        }
        return $lists[$sdz][$tgIndex];
    }

    /**
     * 根据年柱十神获得面相概述
     * @param string $yearGod
     * @return array
     */
    protected function getFaceMain(string $yearGod): array
    {
        $list = [
            '正印' => [
                'title' => '上庭适中',
                'info' => '上庭适中，早年平顺，聪明但耐力稍欠，学业稍平，算不上非常理想，但早年无大病大灾，能顺利度过。中年时基本能按照理想稳步发展，事业发迹如果能得到他人相助则能转逆为顺。下庭无伤痕以及污点，有美满的爱情和宽大的度量，同时身体也很健康，诸事皆有个圆满的结果。',
            ],
            '正官' => [
                'title' => '上庭适中',
                'info' => '上庭适中，人中深且宽，是一种贵相。男性有此面貌生殖能力强，性能力强，同时做人真诚热忱，耿直没有坏心眼。女性有此面貌对夫家好，待人亲和有礼，并且生育能力好，身体好。事业上也是良师益友多多，能闯出自己的一片天地。健康方面没有太大问题，健康又长寿。',
            ],
            '正财' => [
                'title' => '前额骨平正方阔',
                'info' => '前额骨平正方阔，有宏大之理想与志向。主其人聪慧，富有创造力和鉴识能力。处事谨慎专注，执行能力强。中庭宽阔势高，30岁以后会越过越好，经常能得朋友帮助，充分发挥自己的能力施展抱负。下庭圆满、端正而厚重，一生有福气。',
            ],
            '食神' => [
                'title' => '上庭饱满',
                'info' => '上庭饱满丰隆，人缘好，前途广阔，而且智慧高，有学识。中庭较长，中年是人生的黄金岁月。自身的创造能力和拼搏能力很强，在三十到五十这二十年，自身成就达到人生的巅峰。下庭平满、端正而厚实，说明晚年较佳，子孙贤孝。',
            ],
            '伤官' => [
                'title' => '上庭略高',
                'info' => '上庭略高，且有美人尖的发际线，有艺术气质，多数有可能从事艺术相关的行业，也可能从事演艺事业，行事容易成功，异性缘也较好，但是性格比较叛逆，不服管，但是整体来讲还是容易成功的。',
            ],
            '七杀' => [
                'title' => '上庭适中',
                'info' => '上庭适中，颧骨高，且有肉包住颧骨，福气之相。为人自信有冲劲，好胜心强，对权势有所追求，爱做团队中的领头羊，好支配别人。事业所求皆可得。情感上得到容易相守难，在感情中太强势，让伴侣感到压力。精力旺盛，健康方面注意心脏和肝脏的问题。钱财不缺，喜欢孜孜不倦的追求。',
            ],
            '偏印' => [
                'title' => '上庭适中',
                'info' => '上庭适中，早年平顺，聪明但耐力稍欠，学业稍平，算不上非常理想，但早年无大病大灾，能顺利度过。中庭如果鼻子生的挺拔有肉，那么一生都不会缺少钱财，做事有能力有主意，能够忍辱负重完成大业。下庭无伤疤以及污点，并且下巴饱满的人，可以存住钱财，而且可以富贵',
            ],
            '偏财' => [
                'title' => '上庭高而宽阔',
                'info' => '上庭高而宽阔，通达事理，聪颖而富于知性，容易得到友人长辈的帮助。中年时基本能按照理想稳步发展，事业发迹如果能得到他人相助则会较为顺利。下庭无伤痕以及污点，有美满的爱情和宽大的度量，同时身体也很健康，凡事一定有个圆满的结果。',
            ],
            '比肩' => [
                'title' => '上庭适中',
                'info' => '上庭适中，不善于社交，适合技术工作，进入社会较晚。适宜学习专门技术，宜得一技之长，一生较为安稳，否则四十岁前必然困苦不堪，生活每况愈下。下庭无伤疤以及污点，并且下巴饱满的人，可以存住钱财，而且可以富贵',
            ],
            '劫财' => [
                'title' => '上庭高而宽阔',
                'info' => '上庭高而宽阔，通达事理，聪颖而富于知性，容易得到友人长辈的帮助。但意志力稍稍会比较薄弱，因此到了中年期间，容易招来不好的事情，应心平气和等待晚年的转变。下庭无伤疤以及污点，并且下巴饱满的人，可以存住钱财，而且可以富贵',
            ],
        ];
        return $list[$yearGod] ?? $list['正印'];
    }

    /**
     * 获得眼睛
     * @param string $yearGod 年十神
     * @param string $wr 旺弱
     * @return array
     */
    protected function getFaceEye(string $yearGod, string $wr): array
    {
        $wrStr = $wr ? '旺' : '弱';
        $list = [
            '正印' => [
                '旺' => [
                    'title' => '率直开朗',
                    'info' => '个性坦率，乐观而天真，善于表现自我，社交能力佳，喜欢浪漫的事物，但占有欲强，且带些投机取巧的心态。性格容易暴躁，做事急功近利，总想一步登天，好的方面是做事积极，有努力有志气，成功的机率高，但一生在事业方面会多遇挫折。率直开朗、表里一致，做事积极，而且感觉敏锐，比较早熟。个性单纯，容易受到外界的诱惑，也比较无法把持住自己的感情和情绪。',
                ],
                '弱' => [
                    'title' => '大胆直爽',
                    'info' => '眼睛偏大的人，通常个性比较大胆直爽，对于许多事都感到好奇。这样的人容易相信别人，自信心强。领悟力宽博，对于一般事物有兴趣。大眼睛的人表示好动、敏捷、富有灵感。如果眼大且是双眼皮，人际关系通常不错，但有些多愁善感，较为天真没心机；爱表现爱出风头，但也带些迷茫的性格，感受力极强，喜欢华丽的事务，因此也有些虚荣心态。',
                ],
            ],
            '正官' => [
                '旺' => [
                    'title' => '精神饱满',
                    'info' => '眼长深邃之人眼睛细长、黑白分明，再加上瞳仁位置端正，大小适中，上下左右没有露出眼白，给人神色清秀，精神饱满的感觉，有这种眼睛的人都极富思考的能力，智慧过人，不但洞察力敏锐，而且进退有节，做事能脚踏实地，但会过分细腻地考虑问题而变成钻牛角尖。具有这种眼睛的人，男人能出人头地；女性能相夫教子，属于理想的眼相。',
                ],
                '弱' => [
                    'title' => '眼圆有神',
                    'info' => '双眼神情自如，个性爽朗，兴趣广泛及富有感情，但易欠缺思考力而失败，容易受到诱惑，在性的方面很积极，且容易因感情问题招惹麻烦。这种眼睛的人非常明朗、感觉亦十分敏锐，但较易与他人附和雷同,容易被他人的鼓吹和忽悠所欺骗，轻易的相信别人，做出不明智的事，所以要多一些防备心和思考别人传达给你的信息。此外，在情感上，总是默默付出。在选择伴侣上选择一个理智沉稳的人会比较好，考虑问题比较周全，同时不过分感情用事的伴侣，是你的最佳选择。',
                ],
            ],
            '正财' => [
                '旺' => [
                    'title' => '眼距过近',
                    'info' => '个性好勇斗狠，一生少成多败（但军武职者不在此列）考虑问题比较尖锐，注意力会主要放到某一个事上，而执着不放松，往往忽略了更好的选择。',
                ],
                '弱' => [
                    'title' => '心胸宽大',
                    'info' => '具有这种眼睛的人，个性积极，主动，果断，想象力丰富，心胸宽大，缺点是对人对事太有信心而遭到失败。',
                ],
            ],
            '食神' => [
                '旺' => [
                    'title' => '心胸宽广',
                    'info' => '两眼距离过宽的人，做事有头无尾，容易三天热血，心性比较宽，为人不计较，对钱财规划不够，婚姻生活一般不稳定。',
                ],
                '弱' => [
                    'title' => '热情开朗',
                    'info' => '重感情，容易感情用事。在感情方面，容易受到诱惑，女孩子则爱打扮，男女皆容易轻信别人。知觉性强，感情丰富，热情开朗，顺应性和协调性优异，行动积极敏捷。个性较为开朗热诚，别人一些贴心的举动或嘘寒问暖，特别是来自异性的，就非常容易受感动，因而往往抵抗不了异性的诱惑。有胆识有个性，爱冲动，但感情脆弱，一般在职场当中能够当正职。感情丰富，但较缺乏耐力，偏好感性事务，稍欠些许理性思维，易受到情感方面的纷扰，屡屡招嫉或易受到情感方面的压力及烦恼。',
                ],
            ],
            '伤官' => [
                '旺' => [
                    'title' => '决断力强',
                    'info' => '眼睛细又长的人，较为傲气，凡事喜欢速战速决，富有决断力，处事镇定也多能经过深思熟虑后再付诸于行动。聪明有智慧，受人尊重有贵气。眼睛外形细而长的人，思考力足，善于精打细算，精于人情世故，做事老练。',
                ],
                '弱' => [
                    'title' => '有穿透强',
                    'info' => '比较固执，思维好，具有穿透力，能投其所好。眼力好，识人本领高，看事物本质强，顾会对领导或者有利的人物，投其所好。个性敏感、操劳，心境较不敞开，外形看起来忧伤，但善于察言观色，常感心声不宁，凡事皆逆来顺受。很懒散，对很多事情都很被动，一点也不积极，往往听之任之，就是明知道有补救的办法也不会果断地采取措施去争取。其好吃懒做的脾性会经常使其无所事事。所以就会经常在外惹麻烦，惹了麻烦还没有能力承担。',
                ],
            ],
            '七杀' => [
                '旺' => [
                    'title' => '意志力强',
                    'info' => '眼睛单眼皮意志力很强，行为谨慎，有冷静头脑，善猜忌，怀疑心重。对感情的表达方式含蓄内敛，即使眼前站的就是平日欣赏或喜欢的人，也会尽可能保持镇定，不露痕迹。不善表达内心的感受，然而毅力坚强，不易退缩，并努力追求自我设定的目标。虽然为人积极，但表现却让人感到冷漠而热情不足。耐力较强，比较能够承受压力，可以成为组织的主管人才。处世冷静，有逻辑性，观察力和集中力均优，思虑深，意志力坚强。性格消极，沉默寡言。做事细心、谨慎，虽有持续力，但个性顽固。',
                ],
                '弱' => [
                    'title' => '谨慎小心',
                    'info' => '眼角下垂的人，大都具有良好的观察能力，个性谨慎小心，凡事设想周到，行事温和，态度稳健，也是个很好的辅佐人才。但是个性较为悲观，不喜欢出风头，行为多数比较谨慎，思想也极为保守。这样的女性外表看着比较温柔可爱，但是总给人一种缺乏自信的感觉，实际上内心却很坚强。眼角下垂的人性格比较稳重踏实，家运不错。性格较内向，行为被动或消极，为人处事抱有与世无争的态度，行为被动或消极，为人处事抱有与世无争的态度，但并非完全没有上进心。若男人过分下垂就不好了，遇事会优柔寡断，说话办事慢半拍。对于女性来说，鱼尾稍下垂或平直较好，性情柔顺，夫唱妇随，与丈夫配合很默契，手脚也勤快，整理家务井井有条，也会看丈夫眼色行事。职场中，有这种眼相的女子会讨上司喜欢，会巴结上司。',
                ],
            ],
            '偏印' => [
                '旺' => [
                    'title' => '眼神灵活',
                    'info' => '眼神灵活的人脑子十分活跃，总有满脑子的鬼点子，古灵精怪的性格，一般是口才也很好，随机应变能力强，是个人见人爱的捧场王，你性格自由不拘小节，做很多事都是随心而走，也比较容易改变想法，思想自由，不喜欢被别人束缚，你喜欢从事创意类的工作，比如设计师，自己创业等等。男性一般很会讨女生的欢心，总是能给人带来浪漫的惊喜，女性一般古灵精怪，思维跳跃，总是出其不意，让男生觉得她十分有趣而被吸引。',
                ],
                '弱' => [
                    'title' => '慎密敏感',
                    'info' => '个性喜怒无常，经常会自我矛盾，内心自卑又倔强，感情生活或事业容易起伏不定。不过你的金钱来源不错，赚钱可是很有办法。女性的话，好胜而有才华，表面功夫相当漂亮，可以随心所欲地控制他人，隐瞒自己的缺点。看事物有两套标准、心思慎密敏感，一般都比较聪明 但情绪起伏也很大。',
                ],
            ],
            '偏财' => [
                '旺' => [
                    'title' => '洞察力强',
                    'info' => '你眼神比较深邃，瞳孔占据的比例大的人，是一个洞察力强，心里很有自己想法的人。情商很高也很会察言观色，在跟他人交往的过程中很能捕捉别人的微表情跟言语里的意思，同时你总是看起来很神秘，不太表露自己的情感，也很少提及自己的事，你相对会比较有防备心，比较难走进你的内心。在感情中，你要试着敞开心扉，让对方少很多非得解释的时候，这样才会给对方更多的爱跟温暖。',
                ],
                '弱' => [
                    'title' => '谨慎理智',
                    'info' => '做事谨慎、迟缓、精明、理智。眼睛偏小的人，通常胆子就小，具有敏锐的观察力，思维缜密，行事小心，常带有自我保护之心态，因为你的个性比较为保守谨慎，除非有把握的事，否则不轻易行动。对人对事都富警戒心，不容易相信别人，所以让人感觉个性多猜疑、精明且冷淡。一般很聪明，在爱情上很有智慧，细心，能看透另一半的心事，在姻缘上不易陷入困境中，他们在姻缘上比较有眼光，比较现实，选择的对象往往在婚后生活中很适合自己，且得另一半的深爱。感情上较为专一，不容易变心，但却容易钻牛角尖。',
                ],
            ],
            '比肩' => [
                '旺' => [
                    'title' => '谨慎稳重',
                    'info' => '你是理性慎重的人。做任何决定都是十分慎重的，凡事都会做个计划来做。你在事业上谨慎保守，不太愿意做风险大和冒险的事，投机取巧的事也不会做。你偏向沉默，在大家都哗啦啦发表言论的时候，你是人群中始终没有发表言论的人，因为谨慎让自己管住了自己的嘴。在情感上，你也是不太容易被诱惑冲昏头脑的人，会理智的判断并做出自己的决定，不是意气用事的人。建议在日常中，偶尔多一些冲动，可能会收获意想不到的惊喜哦。',
                ],
                '弱' => [
                    'title' => '眼神灵活',
                    'info' => '眼神灵活，并且纯净的人，是不可多得的善良相貌。始终心怀感恩，对待他人真诚，备受大家的喜欢。这种类型的异性缘是很旺的哦，一双迷人的眼睛就可以迷住不少的异性。在事业方面，合伙创业或者在公司工作，都是容易有好的发展的，反应快，又比较聪明，是大家心中的小机灵，自然就乐于把好的机会给你啦。但是要注意在情感上不要陷入纠纷矛盾中，要始终专一真诚。也要小心被欺骗，纯真是好事，但遇到不好的人就会容易被利用哦。',
                ],
            ],
            '劫财' => [
                '旺' => [
                    'title' => '眼圆有神',
                    'info' => '眼睛有神者性格开朗、活泼可爱，这种人一般心胸比较开阔，不计较小节，善与人相处。一般性格比较开朗，心中瞒不住事情。你如果有什么心事，总会在有意无意之间流露出来。男人一般都比较灵活大胆，具有多方面的才能，语言水平高，办事速度快，你的眼睛会说话，态度和蔼，善解人意，很会迎合女性的心理。和女人在一起，你总会是女人感到轻松自在，不知不觉中让女孩子倾心相诉，因此这种男人谈恋爱成功的概率相当高。眼睛明亮的女孩一般都具有较好的艺术感受力，歌声优美，音乐节奏感很强。能歌善舞、性格开朗，具有天生的表演艺术才能。缺乏生活主意。',
                ],
                '弱' => [
                    'title' => '头脑精敏',
                    'info' => '多富行动力，头脑精敏灵活，处事爽快明朗，但带些高傲自负的心态，善于掌握时机，能够即使发挥自我优点。个性较刚烈，爱钻牛角尖，爱走极端，乐天派，有着不服输的个性，好胜心较强。头脑比较灵活聪明，对自己的人生有规划和主见，掌控欲强，领导能力也很强，眼角上扬也会给人一种很厉害很凌厉的感觉。女人若鱼尾上扬，可说是女中强人，对工作积极热心，争强好胜，自尊心很强，性情容易嫉妒，对男人看管很严，极力要控制丈夫，而她自己不受丈夫的管束。这样的女子不善与人相处，容易树敌。如果鱼尾上扬得比鼻梁还高，这是晚年孤独的特征，要改变这种境况最好不要太过突出自我，多一份谦虚才是。',
                ],
            ],
        ];
        return $list[$yearGod][$wrStr] ?? $list['正印']['旺'];
    }

    /**
     * 眉毛结果
     * @param string $yearGod 年十神
     * @param string $wr 旺弱
     * @return array
     */
    protected function getFaceEyeBrow(string $yearGod, string $wr): array
    {
        $list = [
            '正印' => [
                '弱' => [
                    'title' => '交连眉',
                    'info' => '连眉大家都知道不好了，这是一个很普通的常识，有很多人的眉上也可以看到轻微的有相连之势，这种是一般的眉心较窄而气量狭小罢了，如果并不止轻微欲连结，而是两条眉毛的眉头有力地连结起来，就成为「六害眉」中的交连眉。如果是一双既粗且浓又「杂乱」的交连眉最是可怕，它会令一个人失去理智，胡涂地作出傻事',
                ],
                '旺' => [
                    'title' => '交加眉',
                    'info' => '所谓交加眉，是在眉的中间或尾部另叉开了一条眉毛，有点像个「叉」子，又像两条眉毛交加在一起，有这样眉的人，会在人生的路途里饱受亲人至爱的生离死别之苦，性格飘忽，东摇西摆，如果作为女性，在感情上的离离合合，往往会特别的多。如若眼再生得不好，便连累到婚姻也会不稳定了。',
                ],
            ],
            '正官' => [
                '弱' => [
                    'title' => '黄薄眉',
                    'info' => '若然眉毛生得太过稀疏浅薄，明显表示了这人很情绪化，如是女性的话，更是个神经过敏的人，喜怒无常，但若再加上眉毛不是乌黑色，而是棕色或啡黄之败色，这便是犯上了「六害眉」中的黄薄眉了。黄薄眉的人一般不会念亲情、友情和爱情，且攻于心计，利害得失之心非常重，但可惜上天并不会体恤这种人，将会赐给他绝望与孤独的人生。',
                ],
                '旺' => [
                    'title' => '八字眉',
                    'info' => '眉如“八”字，个性悲观，人生起伏不定，男克妻，女克夫。眉毛长得好的人，能成为医生、律师、学者、政治家。八字眉配八字眼最佳，倒八字眉个性刚烈。',
                ],
            ],
            '正财' => [
                '弱' => [
                    'title' => '狮子眉',
                    'info' => '眉毛粗厚、略高于眼者,喜欢奉承他人,早年发展平平,但是晚年来福、甚至荣华富贵,但是通常知心亲朋寡.',
                ],
                '旺' => [
                    'title' => '眉毛竖直',
                    'info' => '眉毛是抬高长的，也就是说眉毛不是贴着肉长的，而是一个接一个地抬高。从五官的角度来说，这意味着他这个人思考能力差，冲动，喜欢想用蛮力来解决事情。因此，他的生活中时常有坏事发生。此外，有这样眉毛的人比较粗鲁，而且不是一个理想的好老公；有的女人也会有同样的眉毛。',
                ],
            ],
            '食神' => [
                '弱' => [
                    'title' => '似虎眉',
                    'info' => '眉毛粗而威严,此人有胆有识,智勇双全;抱负远大,能成大事,健康长寿.不过容易克兄弟姐妹.',
                ],
                '旺' => [
                    'title' => '眉毛断开',
                    'info' => '眉毛断开是指不连在一起的眉。它可能是出生就有的，也可能是长大后搞出来，这些都是所谓的眉毛断裂。从面貌的角度来看，眉毛断缺和眉毛少了一截都是一样的，有可能这个人兄弟俩很早就生死相隔，这也意味着手足很容易受伤。如因后天意外，男性左边的眉多为19岁、10岁、1岁，右眉多为25岁、16岁、7岁。',
                ],
            ],
            '伤官' => [
                '弱' => [
                    'title' => '眉毛粗厚',
                    'info' => '雄性眉毛较厚，雌性眉毛较薄，这是正常的。然而眉毛粗代表人生道路广，眉毛细则注重生活中的细小事物。从面貌的角度来看，如果男性眉毛细，这意味着这种男人和女人一样敏感，喜欢关注生活琐事，甚至喜欢照顾家庭琐事和安排家中的事；然而，如果男性的眉毛太厚或眉型太粗，则代表着对女人有欲望或者对老婆有恐惧，他一生可能会有一次坐牢的经历，也可能会有比自己年纪长的妻子。如果女人眉毛太厚，就有阳刚之气，凡事主动，有领导的本能，容易和年轻的人在一块，眉太厚了也可能代表粗心。',
                ],
                '旺' => [
                    'title' => '扫把眉',
                    'info' => '「扫把眉」形状就有如扫把一般，头尖尾散，即眉头尖细而眉尾散碎是也。眉头太尖细的人，有一个很差的缺点，便是疑心太大，又时常跟别人勾心斗角，没有容人之量，胆小而心虚，因此在日常生活中都很易招惹他人嫉恨，在互相猜忌下，其人便难有半日安宁畅快了。这种尖头眉再加上尾散而碎的话，便在性格方面彻底地失败了，因为他将会再加上精神不集中、心绪不宁、胡思乱想和贪心等不良性格，因此很容易便应了古人所说「克妻害子老不闲」此恶句了。',
                ],
            ],
            '七杀' => [
                '弱' => [
                    'title' => '眉散',
                    'info' => '所谓散是指眉比较稀少，从面貌的角度看，这样的人比较稳定和有见识，但往往缺乏主动性和指导能力。',
                ],
                '旺' => [
                    'title' => '一字眉',
                    'info' => '一字眉的人性格倔强，行动男性化。较宽的一字眉的人有胆识，专注力强，感情专一，但做事不深思熟虑，性急，冲动。较窄的一字眉的人比较固执和缺乏耐力，而且比较阴险，通常为智慧型的犯罪者。女人一字眉，自尊心较强，有男子气概，若眉毛过浓，对婚姻不利。',
                ],
            ],
            '偏印' => [
                '弱' => [
                    'title' => '眉毛乱',
                    'info' => '眉毛乱是指眉毛不整洁，不靠近眉骨生长。从面貌上看，有这样眉毛的人不会理解他人，不会有完整的思考，这类人的智力普遍一般，很难取得社会成就。如果眼睛也没有神，他们就会生活在贫穷之中。',
                ],
                '旺' => [
                    'title' => '柳叶眉',
                    'info' => '柳叶眉的人性格温柔而且有智慧，能够孝敬父母，与兄弟姐妹和睦相处。',
                ],
            ],
            '偏财' => [
                '弱' => [
                    'title' => '短眉毛',
                    'info' => '眉毛比眼睛短。从面貌的角度看，这种人性格可能有点偏激，所以大部分都喜欢快速的解决问题，不喜欢拖拖拉拉。要是眉毛贴着肉而不散，这类人的大部分的决定都是正确的，如果眉毛粗糙不整，这种人一般是没什么好友的。',
                ],
                '旺' => [
                    'title' => '新月眉',
                    'info' => '眉毛似一轮新月且长得秀，一生平稳，人际关系好，亲戚朋友多助，夫妻生活美满。',
                ],
            ],
            '劫财' => [
                '旺' => [
                    'title' => '眉毛过粗过浓',
                    'info' => '欣赏比自己强的男人，但却一心想要压倒对方，从比自己弱的男人身上得到顺从的满足后又瞧不起对方，这种心性必定要经历更多的人生磨砺后，才有顺利的婚姻出现。',
                ],
                '弱' => [
                    'title' => '短眉',
                    'info' => '缺乏兄弟帮助照顾。做事欠缺协调性，依赖性强又很倔，有激情，容易与人发生感情，成为别人心爱的对象，但异性缘多并非好事，应小心选择。',
                ],
            ],
            '比肩' => [
                '弱' => [
                    'title' => '大浓眉',
                    'info' => '大浓眉毛的人，重视感情，重视缘分，一定会被感情所伤害。做事积极努力，不撞南墙不回头，一条路跑到黑，而不知回头。执着于自己喜欢的事情，有一技之长，傲气而自大，容易受意外伤害，一生多坎坷。　',
                ],
                '旺' => [
                    'title' => '眉距宽者',
                    'info' => '这种代表你为人较聪明，任何事情都能把握，游刃有余，说话直爽，胸怀坦荡，具有包容心，对于一些事情，能够采取宽宏的态度，富有同情心。经常会路见不平，拔刀相助。侠义心肠浓厚。是一个强者。',
                ],
            ],
        ];
        $wrStr = $wr ? '旺' : '弱';
        return $list[$yearGod][$wrStr] ?? $list['正印']['旺'];
    }
}
