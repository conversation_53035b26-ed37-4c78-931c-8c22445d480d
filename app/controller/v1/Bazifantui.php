<?php
// +----------------------------------------------------------------------
// | Bazifantui.八字反推
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\model\bazi\Fantui;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Bazifantui
{
    /**
     * 八字反推
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $jiaZi = BaziExt::JIA_ZI;
        $dz = Calendar::DI_ZHI;
        // 时辰对应的时区间
        $listHour = [
            '子' => '23:00-00:59', '丑' => '01:00-02:59', '寅' => '03:00-04:59', '卯' => '05:00-06:59',
            '辰' => '07:00-08:59', '巳' => '09:00-10:59', '午' => '11:00-12:59', '未' => '13:00-14:59',
            '申' => '15:00-16:59', '酉' => '17:00-18:59', '戌' => '19:00-20:59', '亥' => '21:00-22:59',
        ];
        $listHour1 = [
            '子' => 0, '丑' => 2, '寅' => 4, '卯' => 6, '辰' => 8, '巳' => 10, '午' => 12, '未' => 14, '申' => 16, '酉' => 18, '戌' => 20, '亥' => 22,
        ];
        $keyName = ['y' => '年', 'm' => '月', 'd' => '日', 'h' => '时'];
        $data = [
            'y' => input('y', '', 'trim'),
            'm' => input('m', '', 'trim'),
            'd' => input('d', '', 'trim'),
            'h' => input('h', '', 'trim'),
        ];
        $sex = input('sex', 0, 'intval');
        foreach ($data as $k => $v) {
            if (!in_array($v, $dz) && $k == 'h') {
                return ['status' => 0, 'msg' => '时-数据不合要求'];
            }
            if (!in_array($v, $jiaZi) && $k != 'h') {
                return ['status' => 0, 'msg' => $keyName[$k] . '-数据不合要求'];
            }
        }
        $info = Fantui::where('y', '=', $data['y'])
            ->where('m', '=', $data['m'])
            ->cache(600)
            ->find();
        if (empty($info)) {
            return ['status' => 0, 'msg' => '没有符合的数据'];
        }

        $detail = $info->gongli[$data['d']] ?? [];
        $result = [];
        // 时地支
        $jiNianHDz = $data['h'];

        foreach ($detail as $v) {
            // 2078-08-27|二零七八-七月-二十
            $dayList = explode('|', $v);
            $tmpTime = "{$dayList[0]} {$listHour1[$jiNianHDz]}:00";
            if (date('Y', strtotime($tmpTime)) > 2090) {
                continue;
            }
            $lunar = Ex::date($tmpTime)->sex($sex);
            $base = $lunar->getLunarByBetween();
            $tmpResult = [
                // 公历
                'gongli' => $dayList[0],
                // 时区间
                'hour' => $listHour[$jiNianHDz],
                // 八字基础
                'base' => $base,
                // 天干十神
                'god' => $lunar->getGod(),
                // 地支十神  和  藏干
                '_god' => $lunar->_getGod(),
                // 纳音
                'na_yin' => $lunar->getNayin(),
                // 地势
                'terrain' => $lunar->getTerrain(),
                // 旺相休囚死
                'wxxqs' => $lunar->getWxxqs(),
                // 喜用神
                'like_god' => $lunar->getLikeGod(),
                // 胎元
                'fetus' => $lunar->getFetus(),
                // 日空
                'day_empty' => $lunar->getEmptyDay(),
                // 煞神
                'shashen' => BaziExt::getGodArr($base['jinian'], $sex),
                // 节气
                'jieqi' => $lunar->getJieQiCur(),
                // 大运
                'fate' => $lunar->getFates(),
                // 命宫
                'minggong' => BaziExt::getMingGong($base['jinian']),
                // small 小运 xusui 虚岁
            ];
            // 交运
            $fateAge = $tmpResult['fate']['age'];
            $tmpStr = "+{$fateAge['year']} year {$fateAge['month']} month {$fateAge['day']} day +{$fateAge['hour']} hour";
            $tmpJaoTime = strtotime($tmpStr, $lunar->dateTime->getTimestamp());
            $tmpResult['jiaoyun'] = date('Y年m月d日 H时', $tmpJaoTime);
            // 小运
            $_tianGan = ['庚', -9 => '辛', -8 => '壬', -7 => '癸', -6 => '甲', -5 => '乙', -4 => '丙', -3 => '丁', -2 => '戊', -1 => '己'];
            $_diZhi = ['申', -11 => '酉', -10 => '戌', -9 => '亥', -8 => '子', -7 => '丑', -6 => '寅', -5 => '卯', -4 => '辰', -3 => '巳', -2 => '午', -1 => '未'];

            // 男阳 女阴图    年柱天干
            if (in_array($base['jinian']['y'][0], ['甲', '丙', '戊', '庚', '壬'])) {
                // 如果等于0    说明是顺时针，  如果等于 1  说明是逆时针
                $isYY = $sex == 0 ? 0 : 1;
            } else {
                $isYY = $sex == 1 ? 0 : 1;
            }
            $diZhi = $lunar->getArrayShuiMove(Calendar::DI_ZHI, 8);
            $tianGan = $lunar->getArrayShuiMove(Calendar::TIAN_GAN, 6);
            $terrain = Ex::getTerrainData();
            for ($i = 0; $i < 3; $i++) {
                // 大运几年
                $num = $isYY == 0 ? ($i + 1) : -($i + 1);
                $offestT = (int)array_search($base['jinian']['h'][0], $tianGan) + $num;
                $offestD = (int)array_search($base['jinian']['h'][1], $diZhi) + $num;

                $smallT = $offestT < 0 ? $_tianGan[$offestT % 10] : $tianGan[$offestT % 10];
                $smallD = $offestD < 0 ? $_diZhi[$offestD % 12] : $diZhi[$offestD % 12];
                $tmpResult['small']['jinian'][] = $smallT . $smallD;
                $tmpResult['small']['terrain'][] = $terrain[$base['jinian']['d'][0] . $smallD];

                $offestT = (int)array_search($base['jinian']['y'][0], $tianGan) + $i;
                $offestD = (int)array_search($base['jinian']['y'][1], $diZhi) + $i;
                $tempT = $tianGan[$offestT % 10];
                $tempD = $diZhi[$offestD % 12];
                $tmpResult['xusui']['jinian'][] = $tempT . $tempD;
                $tmpResult['xusui']['terrain'][] = $terrain[$base['jinian']['d'][0] . $tempD];
                $tmpResult['xusui']['age'][] = $i + 1;
            }
            $result[] = $tmpResult;
        }
        return $result;
    }
}
