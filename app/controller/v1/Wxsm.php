<?php
// +----------------------------------------------------------------------
// | Wxsm.五行算命
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziEx;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\facade\Validate;

class Wxsm
{
    /**
     * 日历类相关
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 五行算命
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 用户生日 时间字符串
            'time' => input('time', '', 'trim'),
            // 性别 0男 1 女
            'sex' => input('sex', 0, 'intval'),
        ];
        $validate = Validate::rule(
            [
                'time|用户生日' => ['require', 'date'],
                'sex|性别' => ['require', 'in:0,1'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $lunar = Ex::date($data['time'])->sex($data['sex']);
        $this->lunar = $lunar;
        $base = $lunar->getLunarByBetween();
        $result = [
            // 农历中文
            'nongli' => $base['nongli'],
            // 农历数字
            '_nongli' => $base['_nongli'],
            // 纪年
            'jinian' => $base['jinian'],
            // 获得纪年五行
            'jnwx' => $this->getJiNianWx(),
            // 生肖
            'shengxiao' => $base['shengxiao'],
            // 天干十神
            'god' => $lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $lunar->_getGod(),
            // 纳音
            'na_yin' => $lunar->getNayin(),
            // 喜用神
            'like_god' => $lunar->getLikeGod(),
            // 胎元
            'fetus' => $lunar->getFetus(),
            // 起名用字分析
            'yzfenxi' => $this->getBaziFenXin(),
        ];
        return $result;
    }

    /**
     * 用字分析
     * @return array
     * @throws \Exception
     */
    protected function getBaziFenXin(): array
    {
        $temp = [
            '金' => [['土'], ['水', '火', '木']],
            '木' => [['水'], ['金', '火', '土']],
            '水' => [['金'], ['火', '木', '土']],
            '火' => [['木'], ['土', '水', '金']],
            '土' => [['火'], ['水', '木', '金']],
        ];
        // 五行数值表
        $likeGodNum = $this->lunar->getWuxingNum();
        arsort($likeGodNum);
        $jiNian = $this->lunar->getLunarTganDzhi();
        // 异类
        $unKing = [];
        $likeGodDay = $this->lunar->wuXingAttr[$jiNian['d'][0]];

        $unKingTotal = 0;
        $unKing2 = [];
        foreach ($temp[$likeGodDay][1] as $val) {
            $unKing2[$val] = $likeGodNum[$val];
            $unKing[] = [
                'wx' => $val,
                'fen' => $likeGodNum[$val],
            ];
            $unKingTotal += $likeGodNum[$val];
        }
        $kingTotal = $likeGodNum[$likeGodDay] + $likeGodNum[$temp[$likeGodDay][0][0]];
        $differ = $kingTotal - $unKingTotal;

        $diffDetail = '';
        if ($differ > 0) {
            $differTitle = '偏旺';
            $diffDetail = "此命日元旺盛，喜（" . implode(',', array_column($unKing, 'wx')) . "）来克泄，忌（{$likeGodDay},{$temp[$likeGodDay][0][0]}）再来帮扶日元。";
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
            $diffDetail = "此命日元较弱，喜（{$likeGodDay},{$temp[$likeGodDay][0][0]}）帮扶日元，忌（" . implode(',', array_column($unKing, 'wx')) . "）来克泄。";
        }
        asort($unKing2);
        $unKeys = array_keys($unKing2);
        $wanKeyArr = array_keys($likeGodNum);
        // 木克土，土克水，水克火、火克金、金克木
        // 木生火，火生土，土生金，金生水，水生木
        $baziEx = new BaziEx($this->lunar);
        $xiY = $baziEx->getxiYongJi();
        $likeGod = $xiY['yong'];
        $wxqr = $this->getWuxingQianRuo();

        $wxWangwx = [];
        foreach ($wxqr as $v) {
            if ($v['fen'] >= 40) {
                $wxWangwx[] = $v['wx'];
            }
        }
        $wxqrDetail = '您八字中没有五行偏旺的情况。';
        if (!empty($wxWangwx)) {
            $wxqrDetail = "您八字中的五行（" . implode(',', $wxWangwx) . "）偏旺，根据八字中和的原则，五行偏旺者，亦制之泄之";
        }
        $result = [
            // 五行强弱
            'wxqr' => $wxqr,
            'wxqrinfo' => $wxqrDetail,
            // 纪年五行
            'jnwx' => $this->getJianNianWxNum(),
            // 缺的五行
            'que' => $this->getQueJNwx(),
            // 旺的五行
            'wang' => $wanKeyArr[0],
            // 喜用神
            'likegod' => $likeGod,
            // 次喜神
            'sec' => $unKeys[1],
            // 同类
            'king' => [
                [
                    'wx' => $likeGodDay,
                    'fen' => $likeGodNum[$likeGodDay],
                ],
                [
                    'wx' => $temp[$likeGodDay][0][0],
                    'fen' => $likeGodNum[$temp[$likeGodDay][0][0]],
                ],
            ],
            'unking' => $unKing,
            // 旺衰得分
            'differ' => [
                'fen' => round($differ, 2), 'title' => $differTitle,
                'info' => $diffDetail,
            ],
        ];
        return $result;
    }

    /**
     * 获得纪年五行数量
     * @return array
     */
    protected function getJianNianWxNum()
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wxlist = $this->lunar->wuXingAttr;
        $list = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        foreach ($jiNian as $v) {
            $wx0 = $wxlist[$v[0]];
            $wx1 = $wxlist[$v[1]];
            $list[$wx0] = $list[$wx0] + 1;
            $list[$wx1] = $list[$wx1] + 1;
        }
        $res = [];
        foreach ($list as $k => $v) {
            $res[] = [
                'wx' => $k, 'fen' => $v,
            ];
        }
        return $res;
    }

    /**
     * 五行强弱
     * @return array
     */
    protected function getWuxingQianRuo()
    {
        $wxNum = $this->lunar->getWuxingNum();
        $sum = array_sum($wxNum);
        $res = [];
        foreach ($wxNum as $k => $v) {
            $persent = round($v / $sum, 3);
            $res[] = [
                'wx' => $k,
                'fen' => round($persent * 100, 2),
            ];
        }
        return $res;
    }

    /**
     * 获得纪行五行中缺失
     * @return string
     */
    protected function getQueJNwx()
    {
        $jnWx = $this->getJianNianWxNum();
        $list = [];
        $res = [];
        foreach ($jnWx as $v) {
            if ($v['fen'] == 0) {
                $res[] = $v['wx'];
            }
        }
        if (empty($res)) {
            $wxqrArr = $this->getWuxingQianRuo();
            foreach ($wxqrArr as $v) {
                $list[$v['wx']] = $v['fen'];
            }
            asort($list);
            $res[] = key($list);
        }
        return implode(',', $res);
    }

    /**
     * 获得纪年五行
     * @return array
     */
    protected function getJiNianWx()
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $wxlist = $this->lunar->wuXingAttr;
        $result = [];
        foreach ($jiNian as $k => $v) {
            $result[$k] = [
                $wxlist[$v[0]], $wxlist[$v[1]],
            ];
        }
        return $result;
    }
}
