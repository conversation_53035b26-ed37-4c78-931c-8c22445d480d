<?php
// +----------------------------------------------------------------------
// | Qimendunjia. 起名建议
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\qumingdafen\Bihua;
use app\lib\Utils;
use app\model\baobaoqm\Cnword;
use app\model\baobaoqm\Jinyongku;
use app\model\baobaoqm\QmjyZi;
use app\model\baobaoqm\Xing;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\plugin\WuXing;
use calendar\SolarTerm;
use Overtrue\Pinyin\Pinyin;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Validate;

class Qimingjy
{
    /**
     * 原始数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 忌用字数组
     * @var array
     */
    protected array $jiArr = [];

    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 用户姓氏详细
     * @var
     */
    protected array $xing = [];

    /**
     * @var array
     */
    protected array $buShouNo = [];

    /**
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 姓氏
            'xing' => input('xing', '', 'trim'),
            // 性别 0 男 1 女
            'sex' => input('sex', -1, 'intval'),
            // 出生时间 Y-m-d H:i:s
            'time' => input('time', '', 'trim'),
            // 是否已出生 0 未出生 1 已出生
            'born' => input('born', 1, 'intval'),
            // 忌用字 空格隔开
            'ji' => input('ji', '', 'trim'),
            // 名1
            'ming1' => input('ming1', '', 'trim'),
            // 名2
            'ming2' => input('ming2', '', 'trim'),
            // 订单时间
            'otime' => input('otime', date('Y-m-d'), 'trim'),
        ];
        $validate = Validate::rule(
            [
                'xing|姓氏' => ['require', function ($value) {
                    $listX = Xing::cache(600)->column('title');
                    if (in_array($value, $listX)) {
                        return true;
                    }
                    return '暂不支持该姓';
                }],
                'sex|性别' => ['require', 'in:0,1'],
                'time' => ['require', 'date', 'before:2090-01-01', 'after:1920-01-01'],
                'born' => ['require', 'in:0,1'],
                'otime' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        if (!empty($data['ji'])) {
            $this->jiArr = array_unique(array_filter(explode(' ', $data['ji'])));
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $fx = $this->lunar->getWxFenxi();
        $jnWx = $this->getJnwx($base['jinian']);
        $this->xing = $this->getZiInfo($data['xing']);
        $sxjy = $this->getShengXiaoJy();
        $this->buShouNo = explode('、', $sxjy['bad']);
        return [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支藏干十神
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 纪年五行
            'jnwx' => $jnWx,
            // 喜用忌闲仇
            'xy' => $fx['xy'],
            // 五行旺衰分析
            'wxfx' => $this->getwuXingFengXi($fx),
            // 生肖建议
            'shengxiaojy' => $sxjy,
            // 立春
            'lichun' => $this->getLiChun(),
            'xing' => $this->xing,
            'ming' => $this->getMing(),
        ];
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnwx(array $jiNian): array
    {
        $result = [];
        $wuXingAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [$tgWx, $dzWx];
        }
        return $result;
    }

    /**
     * 五行分析
     * @param array $fx 五行分析初始数据
     * @return array
     */
    protected function getwuXingFengXi(array $fx)
    {
        $differ = $fx['differ'];
        if ($differ > 0) {
            $differTitle = '偏旺';
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
        }
        $all = array_merge($fx['king'], $fx['unking']);
        $all = array_column($all, 'fen', 'wx');
        $totalK = round(array_sum(array_column($fx['king'], 'fen')), 3);
        $totalUk = round(array_sum(array_column($fx['unking'], 'fen')), 3);
        $totalAll = round($totalK + $totalUk, 3);
        $list = [
            '金' => [], '木' => [], '水' => [], '火' => [], '土' => [],
        ];
        foreach ($list as $k => $v) {
            $list[$k] = [
                'wx' => $k, 'fen' => $all[$k], 'per' => round($all[$k] * 100 / $totalAll, 1),
            ];
        }
        return [
            'all' => $list,
            'king' => $fx['king'],
            'unking' => $fx['unking'],
            'differ' => $fx['differ'],
            'differ_name' => $differTitle,
            'total' => $totalAll,
            'total_k' => $totalK,
            'total_uk' => $totalUk,
        ];
    }

    /**
     * 生肖建议
     * @return string[]
     */
    protected function getShengXiaoJy()
    {
        $list = [
            '子' => [
                'sx' => '鼠',
                'info' => '老鼠喜欢披彩衣(漂亮)或戴冠，得王及掌权，喜吃五谷杂粮，能得洞穴或得水或得木则属佳，如又得龙、得猴成三合局，如得猪、得牛成三会局—名字中如有符合下述条件者为好名。',
                'good' => '宀、米、豆、鱼、艹、金、玉、人、木、月、田、钅、亻',
                'bad' => '午、马、火、灬、日',
            ],
            '丑' => [
                'sx' => '牛',
                'info' => '牛喜欢有洞穴住，喜欢吃五谷杂粮，如能得水得木得草最佳，得蛇得鸡成三合局，得鼠得猪成三会局，得田符合属性，得栅栏得休息—名字中如有符合下述条件者为好名。',
                'good' => '水、艹、豆、米、金、玉、宀、人、木、氵、钅、亻',
                'bad' => '心、忄、马、羊、彡、巾、衣、示、系',
            ],
            '寅' => [
                'sx' => '虎',
                'info' => '老虎喜欢有洞穴住，如能得山部，披彩衣为佳，能得王掌权最佳，又戴冠，吃肉最棒，能得水得木得森林更好，得马得狗三合局，有奔跑字形更有前途—名字中如有符合下述条件者为好名。',
                'good' => '山、玉、金、木、示、水、月、犭、马、氵、钅',
                'bad' => '辶、一、丨、邑、虫、廴、人、彳、门、小',
            ],
            '卯' => [
                'sx' => '兔',
                'info' => '兔子喜欢有洞穴住，披彩衣为佳，有五谷得温饱，能得水、得木、得森林、得草为适得其所，如有羊或猪之形为三合局，得栅栏可休息—名字中如有符合下述条件者为好名。',
                'good' => '月、艹、山、田、人、禾、木、宀、白、玉、豆、钅、亻',
                'bad' => '酉、西、鸡、几、羽、金、日、阳、宇',
            ],
            '辰' => [
                'sx' => '龙',
                'info' => '龙喜欢得日月精华，披彩衣为佳，能得王掌权最佳，又戴冠，得水最棒，能天上飞，能得鼠、得猴，成三合局—名字中如有符合下述条件者为好名。',
                'good' => '水、金、玉、白、赤、月、鱼、酉、人、氵、钅、亻',
                'bad' => '戌、犭、犬、虫',
            ],
            '巳' => [
                'sx' => '蛇',
                'info' => '蛇喜欢有洞穴住，能披彩衣为佳，能得王掌权最佳，又戴冠，有肉可吃饱，有火边，又得森林，也得木，得牛、得鸡为三合局，如得马、得羊成三会局—名字中如有符合下述条件者为好名。',
                'good' => '艹、虫、豆、鱼、酉、木、田、山、金、玉、月、土、钅、禾、宀、马、羊、牛、羽、忄、心、辶、廴、几',
                'bad' => '虎、亥',
            ],
            '午' => [
                'sx' => '马',
                'info' => '马喜欢有洞穴住，能披彩衣为佳，能得王又能传令更显高贵，得人骑，表有被照顾，有五谷杂粮，有火边，有太阳，武器，得森林、得草原平地、得奔跑字形，得虎、得狗为三合局，得蛇、得羊为三会局。',
                'good' => '艹、金、玉、木、禾、虫、米、人、月、土、才、钅、亻',
                'bad' => '山、奇、其',
            ],
            '未' => [
                'sx' => '羊',
                'info' => '羊喜欢有洞穴住，得人疼，有五谷吃，有太阳晒，又能得森林及草原或平地，当然能得木字形也不错，如得兔或得猪即成三合局，如得蛇或得马即成三会局，如得栅栏得宜家—名字中如有符合下述条件者为好名。',
                'good' => '金、白、玉、月、田、豆、米、马、禾、木、人、艹、鱼、亻',
                'bad' => '大、王、君、长、辰、戌、犬、丑、未、刀、皿、酉、车',
            ],
            '申' => [
                'sx' => '猴',
                'info' => '猴喜欢有洞穴住，见人喜开口，如有森林得乐趣，如得草原平地更爽，有木字形或披彩衣显高贵，见王能掌权，跟鼠或龙成三合局，得鸡得狗成三会局—名字中有符合下述条件者为好名。',
                'good' => '木、禾、玉、豆、米、田、山、月、水、人、氵、亻、彡、巾、系、采、衣、示、',
                'bad' => '金、酉、西、兑、皿、鸟、月、豕、口',
            ],
            '酉' => [
                'sx' => '鸡',
                'info' => '鸡喜欢有洞穴住，或在森林得草原平地跑跳，如有五谷字形，可得温饱，能披彩衣更佳，能见王戴冠显高贵，与蛇跟牛成三合局，得栅栏可受保护—名字中如有符合下述条件者为好名。',
                'good' => '米、豆、虫、木、禾、玉、宀、山、艹、钅、彡、巾、系',
                'bad' => '东、月、兔、金、西、兑、酉、刀、示、力、石、人、手、血、水、子',
            ],
            '戌' => [
                'sx' => '狗',
                'info' => '狗喜欢有洞穴住，能披彩衣为佳，能得王掌权最佳，又戴冠，有肉可吃饱，得虎得马成三合局，有栅栏可住得舒服—名字中如有符合下述条件者为好名。',
                'good' => '鱼、豆、米、宀、马、金、玉、艹、田、月、禾、水、人、氵、钅、亻',
                'bad' => '田、犭、木、未、羊、丑、牛、贝',
            ],
            '亥' => [
                'sx' => '猪',
                'info' => '猪喜欢得洞穴等于有家可住，得森林成野猪可奔放，得草原平地也可，有五谷吃可得温饱，有兔或羊成三合局，遇鼠或牛成三会局。',
                'good' => '豆、米、鱼、水、金、玉、月、木、人、山、土、艹、氵、亻',
                'bad' => '糸，石，刀，力，血，弓，儿，皮，父，巳，火，土',
            ],

        ];
        $ygz = $this->lunar->getLunarGanzhiYear();
        return $list[$ygz[1]];
    }

    /**
     * 立春
     * @return array
     */
    protected function getLiChun(): array
    {
        $oy = (int)$this->lunar->dateTime->format('Y');
        $res = [];
        for ($i = 0; $i < 2; $i++) {
            $y = $oy + $i;
            $jq = SolarTerm::getAllJieQi($y);
            $t = strtotime($jq['立春']);
            $res[] = [
                'y' => (int)date('Y', $t),
                'm' => (int)date('n', $t),
                'd' => (int)date('j', $t),
                'h' => (int)date('H', $t),
                'i' => (int)date('i', $t),
                's' => (int)date('s', $t),
            ];
        }
        return $res;
    }

    /**
     * 根据三才配置获得名字
     * @return array
     */
    protected function getMingBihua(): array
    {
        $listU = $this->getSanCai();
        // 最佳三才配置
        $peak = [];
        $result = [];
        $arr = end($this->xing);
        // 计算地格所需要的姓笔画
        $bihua1 = $arr['bihua'];
        $bihuaAll = [];
        foreach ($listU as $k => $v) {
            $result[$k] = [
                'one' => [],
                'two' => [],
            ];
            foreach ($v as $v1) {
                $key = $v1['one'] . $v1['two'] . $v1['three'];
                if ($k == 'daji') {
                    $peak[$key] = $key;
                }
                for ($i = 1; $i <= 30; $i++) {
                    $renGe = $bihua1 + $i;
                    $renGeWx = $this->getWuXing($renGe);
                    if ($renGeWx != $v1['two']) {
                        continue;
                    }
                    // 单名地格
                    $diGe = $i + 1;
                    $diGeWx = $this->getWuXing($diGe);
                    if ($diGeWx == $v1['three']) {
                        $result[$k]['one'][] = $i;
                    }
                    for ($j = 1; $j < 30; $j++) {
                        $diGe = $i + $j;
                        $diGeWx = $this->getWuXing($diGe);
                        if ($diGeWx != $v1['three']) {
                            continue;
                        }
                        $result[$k]['two'][$i][] = $j;
                        $bihuaAll[$i] = $i;
                        $bihuaAll[$j] = $j;
                    }
                }
            }
        }
        $bihuaAll = array_values($bihuaAll);
        sort($bihuaAll);
        $result['bihua'] = $bihuaAll;
        $result['good'] = array_values($peak);
        return $result;
    }

    /**
     * 获得取名所用的字
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getMing(): array
    {
        $result = ['ming1' => [], 'ming2' => []];
        if ($this->orginData['ming1']) {
            $listSanCai = $this->getSanCai();
            $arr1 = mb_str_split($this->orginData['ming1']);
            $arr2 = mb_str_split($this->orginData['ming2']);
            $arr = array_merge($arr1, $arr2);
            $list1 = QmjyZi::whereIn('zi', $arr)
                ->whereIn('stype', [1, 12, 13, 123])
                ->append(['wxName'])
                ->hidden(['id'])
                ->cache(300)
                ->select();
            $list1 = $list1->column(null, 'zi');
            $ming1 = [];
            $ming2 = [];
            foreach ($arr1 as $zi) {
                if (!isset($list1[$zi])) {
                    continue;
                }
                $ming1[] = $list1[$zi]->toArray();
            }
            foreach ($arr2 as $zi) {
                if (!isset($list1[$zi])) {
                    continue;
                }
                $ming2[] = $list1[$zi]->toArray();
            }
            $result = [
                'ming1' => $ming1,
                'ming2' => $ming2,
            ];
            foreach ($listSanCai['daji'] as $v) {
                $result['good'][] = $v['one'] . $v['two'] . $v['three'];
            }
        } else {
            $mingBiHua = $this->getMingBihua();
            if ($this->orginData['born'] == 0) {
                $list = $this->getMingByBiHua($mingBiHua, 0);
                $result = $this->getMingRes($list, $result, 5);
                $list = $this->getMingByBiHua($mingBiHua, 1);
                $result = $this->getMingRes($list, $result, 5);
            } else {
                $list = $this->getMingByBiHua($mingBiHua, $this->orginData['sex']);
                $result = $this->getMingRes($list, $result, 10);
                $result['good'] = $mingBiHua['good'];
            }
        }
        return $result;
    }

    /**
     * 根据天格获得三才配置
     * @return array
     */
    private function getSanCai(): array
    {
        $list = [
            '金' => [
                'daji' => [
                    ['one' => '金', 'two' => '金', 'three' => '土', 'fen' => '92'],
                    ['one' => '金', 'two' => '水', 'three' => '金', 'fen' => '94'],
                    ['one' => '金', 'two' => '水', 'three' => '木', 'fen' => '96'],
                    ['one' => '金', 'two' => '土', 'three' => '火', 'fen' => '93'],
                    ['one' => '金', 'two' => '土', 'three' => '金', 'fen' => '97'],
                    ['one' => '金', 'two' => '土', 'three' => '土', 'fen' => '92'],
                ],
                'zhongji' => [
                    ['one' => '金', 'two' => '金', 'three' => '金', 'fen' => '96'],
                    ['one' => '金', 'two' => '金', 'three' => '水', 'fen' => '86'],
                    ['one' => '金', 'two' => '水', 'three' => '水', 'fen' => '85'],
                    ['one' => '金', 'two' => '水', 'three' => '土', 'fen' => '86'],
                    ['one' => '金', 'two' => '土', 'three' => '木', 'fen' => '89'],
                    ['one' => '金', 'two' => '土', 'three' => '水', 'fen' => '77'],
                ],
                'ciji' => [
                    ['one' => '金', 'two' => '火', 'three' => '火', 'fen' => '70'],
                    ['one' => '金', 'two' => '火', 'three' => '土', 'fen' => '67'],
                ],
            ],
            '木' => [
                'daji' => [
                    ['one' => '木', 'two' => '火', 'three' => '木', 'fen' => '94'],
                    ['one' => '木', 'two' => '火', 'three' => '土', 'fen' => '93'],
                    ['one' => '木', 'two' => '木', 'three' => '火', 'fen' => '91'],
                    ['one' => '木', 'two' => '木', 'three' => '木', 'fen' => '93'],
                    ['one' => '木', 'two' => '木', 'three' => '土', 'fen' => '92'],
                    ['one' => '木', 'two' => '水', 'three' => '金', 'fen' => '97'],
                    ['one' => '木', 'two' => '水', 'three' => '木', 'fen' => '95'],
                    ['one' => '木', 'two' => '水', 'three' => '水', 'fen' => '95'],
                ],
                'zhongji' => [
                    ['one' => '木', 'two' => '火', 'three' => '火', 'fen' => '85'],
                    ['one' => '木', 'two' => '木', 'three' => '水', 'fen' => '82'],
                    ['one' => '木', 'two' => '土', 'three' => '火', 'fen' => '87'],
                    ['one' => '木', 'two' => '土', 'three' => '金', 'fen' => '80'],
                    ['one' => '木', 'two' => '土', 'three' => '土', 'fen' => '86'],
                ],
                'ciji' => [],
            ],
            '水' => [
                'daji' => [
                    ['one' => '水', 'two' => '金', 'three' => '水', 'fen' => '93'],
                    ['one' => '水', 'two' => '金', 'three' => '土', 'fen' => '97'],
                    ['one' => '水', 'two' => '木', 'three' => '火', 'fen' => '95'],
                    ['one' => '水', 'two' => '木', 'three' => '木', 'fen' => '96'],
                    ['one' => '水', 'two' => '木', 'three' => '水', 'fen' => '92'],
                    ['one' => '水', 'two' => '木', 'three' => '土', 'fen' => '93'],
                    ['one' => '水', 'two' => '水', 'three' => '金', 'fen' => '90'],
                    ['one' => '水', 'two' => '水', 'three' => '木', 'fen' => '100'],
                ],
                'zhongji' => [
                    ['one' => '水', 'two' => '火', 'three' => '木', 'fen' => '87'],
                    ['one' => '水', 'two' => '金', 'three' => '金', 'fen' => '85'],
                    ['one' => '水', 'two' => '水', 'three' => '水', 'fen' => '86'],
                    ['one' => '水', 'two' => '土', 'three' => '火', 'fen' => '84'],
                    ['one' => '水', 'two' => '土', 'three' => '金', 'fen' => '86'],
                    ['one' => '水', 'two' => '土', 'three' => '土', 'fen' => '85'],
                ],
                'ciji' => [],
            ],
            '火' => [
                'daji' => [
                    ['one' => '火', 'two' => '火', 'three' => '木', 'fen' => '91'],
                    ['one' => '火', 'two' => '火', 'three' => '土', 'fen' => '99'],
                    ['one' => '火', 'two' => '木', 'three' => '火', 'fen' => '96'],
                    ['one' => '火', 'two' => '木', 'three' => '木', 'fen' => '95'],
                    ['one' => '火', 'two' => '木', 'three' => '土', 'fen' => '96'],
                    ['one' => '火', 'two' => '土', 'three' => '火', 'fen' => '98'],
                    ['one' => '火', 'two' => '土', 'three' => '金', 'fen' => '100'],
                    ['one' => '火', 'two' => '土', 'three' => '土', 'fen' => '98'],
                ],
                'zhongji' => [
                    ['one' => '火', 'two' => '火', 'three' => '火', 'fen' => '86'],
                    ['one' => '火', 'two' => '木', 'three' => '水', 'fen' => '86'],
                    ['one' => '火', 'two' => '土', 'three' => '木', 'fen' => '75'],
                    ['one' => '火', 'two' => '土', 'three' => '水', 'fen' => '73'],
                ],
                'ciji' => [
                    ['one' => '火', 'two' => '金', 'three' => '土', 'fen' => '71'],
                ],
            ],
            '土' => [
                'daji' => [
                    ['one' => '土', 'two' => '火', 'three' => '火', 'fen' => '94'],
                    ['one' => '土', 'two' => '火', 'three' => '木', 'fen' => '97'],
                    ['one' => '土', 'two' => '火', 'three' => '土', 'fen' => '96'],
                    ['one' => '土', 'two' => '金', 'three' => '金', 'fen' => '99'],
                    ['one' => '土', 'two' => '金', 'three' => '水', 'fen' => '99'],
                    ['one' => '土', 'two' => '金', 'three' => '土', 'fen' => '98'],
                    ['one' => '土', 'two' => '土', 'three' => '火', 'fen' => '93'],
                    ['one' => '土', 'two' => '土', 'three' => '金', 'fen' => '96'],
                    ['one' => '土', 'two' => '土', 'three' => '土', 'fen' => '97'],
                ],
                'zhongji' => [
                    ['one' => '土', 'two' => '火', 'three' => '金', 'fen' => '85'],
                    ['one' => '土', 'two' => '木', 'three' => '火', 'fen' => '85'],
                    ['one' => '土', 'two' => '木', 'three' => '木', 'fen' => '85'],
                    ['one' => '土', 'two' => '土', 'three' => '木', 'fen' => '82'],
                ],
                'ciji' => [
                    ['one' => '土', 'two' => '水', 'three' => '金', 'fen' => '68'],
                ],
            ],
        ];
        $tianGe = $this->getTianGe();
        $tianGeWx = $this->getWuXing($tianGe);
        return $list[$tianGeWx];
    }

    /**
     * 根据名笔画数取出名字
     * @param array $mingBiHua
     * @param int $sex
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getMingByBiHua(array $mingBiHua, int $sex)
    {
        $listZi = $this->getZiList($mingBiHua['bihua'], $sex);
        $listZi = Collection::make($listZi);
        unset($mingBiHua['bihua'], $mingBiHua['good']);
        $useZi = [];
        $result = [];
        foreach ($mingBiHua as $k => $v) {
            $biHuaArr = $v['two'];
            if (empty($biHuaArr)) {
                continue;
            }
            $biHua1 = array_keys($biHuaArr);

            $listMing1 = $listZi->whereIn('bihuakx', $biHua1)
                ->whereIn('pos', [0, 1, 12])
                ->whereNotIn('zi', $useZi)
                ->toArray();
            shuffle($listMing1);
            $listMing1 = array_splice($listMing1, 0, 12);
            $tmpZi1 = array_column($listMing1, 'zi');
            $useZi = array_merge($useZi, $tmpZi1);
            foreach ($listMing1 as $v1) {
                $biHuaKx1 = $v1['bihuakx'];
                $biHua2 = $biHuaArr[$biHuaKx1];
                $zi1 = $v1['zi'];
                $ziWx1 = $v1['wxName'];
                $listMing2 = $listZi->whereIn('bihuakx', $biHua2)
                    ->whereNotIn('zi', $useZi)
                    ->whereIn('pos', [0, 2, 12])
                    ->toArray();
                shuffle($listMing2);
                $j = 0;
                foreach ($listMing2 as $v2) {
                    if ($j > 10) {
                        continue;
                    }
                    $zi2 = $v2['zi'];
                    $ziWx2 = $v2['wxName'];
                    $wxGx = WuXing::getWuxingGuanXi($ziWx1, $ziWx2);
                    if (in_array($wxGx, ['克', '耗'])) {
                        continue;
                    }
                    $j++;
                    $key = $zi1 . $zi2;
                    $result[$key] = [$v1, $v2];
                    $useZi[] = $zi2;
                }
            }
        }
        $listJin = Jinyongku::whereIn('title', array_keys($result))
            ->whereIn('typem', [1, 12, 13, 123])
            ->cache(300)
            ->column('title');
        foreach ($listJin as $v) {
            unset($result[$v]);
        }
        return $result;
    }

    /**
     * 从结果中获取指定条数的名字
     * @param array $list
     * @param array $result
     * @param int $num
     * @return array
     */
    private function getMingRes(array $list, array $result, int $num)
    {
        $i = 0;
        $j = 0;
        foreach ($list as $v) {
            if ($i >= $num && $j >= $num) {
                break;
            }
            if (!in_array($v[0], $result['ming1']) && $i < $num) {
                $result['ming1'][] = $v[0];
                $i++;
            }
            if (!in_array($v[1], $result['ming2']) && $j < $num) {
                $result['ming2'][] = $v[1];
                $j++;
            }
        }
        return $result;
    }

    /**
     * 获得宜用表中所有符合条件的字
     * @param array $bihua 笔画数组
     * @param int $sex 性别
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getZiList(array $bihua, int $sex)
    {
        $list1 = QmjyZi::where('wx', 'between', [1, 5])
            ->whereIn('stype', [1, 12, 13, 123])
            ->append(['wxName'])
            ->hidden(['id'])
            ->limit(5000)
            ->cache(600)
            ->select();
        if ($list1->isEmpty()) {
            return [];
        }
        return $list1->whereNotIn('bushou', $this->buShouNo)
            ->whereIn('bihuakx', $bihua)
            ->whereIn('sex', [$sex, 2])
            ->whereNotIn('zi', $this->jiArr)
            ->toArray();
    }

    /**
     * 获得天格
     * @return int
     */
    private function getTianGe()
    {
        $xing = $this->xing;
        $num = count($xing);
        $tianGe = array_sum(array_column($xing, 'bihua'));
        if ($num == 1) {
            $tianGe++;
        }
        return $tianGe;
    }

    /**
     * 获得字相关拼音笔画
     * @param string $str
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getZiInfo(string $str): array
    {
        $modelBihua = new Bihua();
        $res = [];
        $arr = Utils::mbStrSplit($str);
        $listWx = [
            '木' => '五行为木 ，可以弥补性格缺陷，增强上进心，变的善良博爱、温文尔雅、气宇轩昂。',
            '土' => '五行为土 ，可以弥补性格缺陷，增强忍耐力，变的踏实稳重、言行一致、忠孝志诚。',
            '金' => '五行为金 ，可以弥补性格缺陷，增强决断力，变的行动稳健、办事认真、刚毅果决。',
            '水' => '五行为水 ，可以弥补性格缺陷，增强智慧，变的聪明好学、足智多谋、刚柔相济。',
            '火' => '五行为火 ，可以弥补性格缺陷，增强主动性，变的热情好礼、善交朋友、口才佳、人缘好。',
        ];
        foreach ($arr as $v) {
            $v = $this->replacestr($v);
            $data = Cnword::info($v);
            if (empty($data)) {
                $tmpBiHua = (int)$modelBihua->find($v);
                $tmpBiHua = $tmpBiHua > 0 ? $tmpBiHua : 10;
                $res[] = [
                    'name' => $v,
                    'big5' => $v,
                    'bihua' => $tmpBiHua,
                    'pinyin' => Pinyin::sentence($v)->join(''),
                    'py' => Pinyin::sentence($v, 'none')->join(''),
                    'detail' => '',
                    'explain' => '',
                    'wuxing' => '',
                    'bushou' => '',
                ];
            } else {
                $res[] = [
                    'name' => $v,
                    'big5' => $data['big5'],
                    'bihua' => $data['bihua2'],
                    'pinyin' => $data['py2'],
                    'py' => $data['py'],
                    'detail' => $data['detail3']['mean'] ?? '',
                    'explain' => $listWx[$data['wx']] ?? '',
                    'wuxing' => $data['wx'],
                    'bushou' => $data['bushou'] ?? '',
                ];
            }
        }
        return $res;
    }

    /**
     * 字体替换
     * @param $data
     * @return string
     */
    private function replacestr($data): string
    {
        $array = [
            '㯋' => '颖', '麹' => '曲',
        ];
        $res = $data;
        if (isset($array[$data])) {
            $res = $array[$data];
        }
        return $res;
    }

    /**
     * 获得五行属性
     * @param int $bihua
     * @return string
     */
    private function getWuXing(int $bihua): string
    {
        $num = $bihua % 10;
        $list = [
            0 => '水', 1 => '木', 2 => '木', 3 => '火', 4 => '火', 5 => '土',
            6 => '土', 7 => '金', 8 => '金', 9 => '水',
        ];
        return $list[$num];
    }
}
