<?php
// +----------------------------------------------------------------------
// | 四柱基础算法
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\bazi\ShenSha;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Sizhusuanfa
{
    /**
     * 初始用户数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 用户日期类
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 四柱基础算法
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $data = [
            // 用户生日
            'time' => input('time', '', 'trim'),
            // 性别 0男 1 女
            'sex' => input('sex', 0, 'intval'),
            // 订单时间
            'otime' => input('otime', date('Y-m-d', time()), 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'otime|订单时间' => ['require', 'date'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->orginData = $data;
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $base = $this->lunar->getLunarByBetween();
        $baziEx = new BaziEx($this->lunar);
        $xiY = $baziEx->getxiYongJi3();
        // 流年数据
        $otimeAmp = strtotime($data['otime']);
        $liuNianData = [
            date('Y', $otimeAmp),
            BaziExt::getGanZhi((int)date('Y', $otimeAmp)),
        ];
        $shShaInfo = new ShenSha($this->lunar, $liuNianData);
        $flowerArr = Ex::getFlower($base['jinian'], $data['sex']);
        $flower2 = [];
        foreach ($flowerArr as $k => $v) {
            $flower2[] = [
                'hua' => $k, 'num' => $v,
            ];
        }
        return [
            'base' => $base,
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神  和  藏干
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'na_yin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 旺相休囚死
            'wxxqs' => $this->lunar->getWxxqs(),
            // 胎元
            'fetus' => $this->lunar->getFetus(),
            // 日空
            'day_empty' => $this->lunar->getEmptyDay(),
            // 大运
            'fate' => $this->lunar->getFates(),
            // 喜用忌闲仇
            'xiy' => $xiY['xy'],
            // 空亡
            'kongwang' => $this->getKongWang($base['jinian']),
            // 煞神
            'shashen' => BaziExt::getGodArr($base['jinian'], $data['sex']),
            // 命宫
            'minggong' => implode('', $shShaInfo->getMingGong()),
            // 同类和异类
            'wxfenxi' => $this->getBaziFenXin(),
            // 身宫
            'shengong' => $this->getShenGong($base),
            // 十二神煞
            'shiershensha' => [
                'da' => $shShaInfo->getDaXian(),
                'xiao' => $shShaInfo->getXiaoXianShenSha(),
            ],
            // 另一半
            'ling_yi_ban' => $this->getlingYiBan(),
            // 桃花
            'flower' => $flower2,
        ];
    }

    /**
     * 获得空亡
     * @param array $jiNian
     * @return array
     */
    protected function getKongWang(array $jiNian)
    {
        $result = [];
        foreach ($jiNian as $k => $v) {
            $str = implode('', $v);
            $result[$k] = Huangli::getKongWangbyGz($str);
        }
        return $result;
    }

    /**
     * 八字同类和异类
     * @return array
     */
    protected function getBaziFenXin(): array
    {
        $temp = [
            '金' => [['土'], ['水', '火', '木']],
            '木' => [['水'], ['金', '火', '土']],
            '水' => [['金'], ['火', '木', '土']],
            '火' => [['木'], ['土', '水', '金']],
            '土' => [['火'], ['水', '木', '金']],
        ];
        // 五行数值表
        $likeGodNum = $this->lunar->getWuxingNum();
        $jiNian = $this->lunar->getLunarTganDzhi();
        // 异类
        $unKing = [];
        $likeGodDay = $this->lunar->wuXingAttr[$jiNian['d'][0]];

        $unKingTotal = 0;
        foreach ($temp[$likeGodDay][1] as $val) {
            $unKing[$val] = [
                'wx' => $val,
                'fen' => round($likeGodNum[$val], 3),
            ];
            $unKingTotal += round($likeGodNum[$val], 3);
        }
        $unKingTotal = round($unKingTotal, 3);
        $kingTotal = round($likeGodNum[$likeGodDay] + $likeGodNum[$temp[$likeGodDay][0][0]], 3);
        $differ = $kingTotal - $unKingTotal;
        if ($differ > 0) {
            $differTitle = '偏旺';
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
        }
        $tmp2 = [];
        foreach ($likeGodNum as $k => $v) {
            $tmp2[] = [$k, $v];
        }
        $result = [
            'fraction' => $tmp2,
            // 同类
            'king' => [
                'wx' => $likeGodDay . $temp[$likeGodDay][0][0],
                'fen' => $kingTotal,
            ],
            'unking' => [
                'wx' => implode('', array_column($unKing, 'wx')),
                'fen' => $unKingTotal,
            ],
            // 旺衰得分
            'differ' => [
                'fen' => round($differ, 3), 'title' => $differTitle,
            ],
        ];
        return $result;
    }

    /**
     * 身宫
     * @param array $Lunar 八字基础数据
     * @return string
     */
    private function getShenGong($Lunar): string
    {
        $dz = Calendar::DI_ZHI;
        array_unshift($dz, "");
        $lists = [
            '寅' => ['丙寅', '戊寅', '庚寅', '壬寅', '甲寅'],
            '卯' => ['丁卯', '己卯', '辛卯', '癸卯', '乙卯'],
            '辰' => ['戊辰', '庚辰', '壬辰', '甲辰', '丙辰'],
            '巳' => ['己巳', '辛巳', '癸巳', '乙巳', '丁巳'],
            '午' => ['庚午', '壬午', '甲午', '丙午', '戊午'],
            '未' => ['辛未', '癸未', '乙未', '丁未', '己未'],
            '申' => ['壬申', '甲申', '丙申', '戊申', '庚申'],
            '酉' => ['癸酉', '乙酉', '丁酉', '己酉', '辛酉'],
            '戌' => ['甲戌', '丙戌', '戊戌', '庚戌', '壬戌'],
            '亥' => ['乙亥', '丁亥', '己亥', '辛亥', '癸亥'],
            '子' => ['丙子', '戊子', '庚子', '壬子', '甲子'],
            '丑' => ['丁丑', '己丑', '辛丑', '癸丑', '乙丑'],
        ];
        $start = $Lunar['_nongli']['m'];
        $hIndex = array_search($Lunar['jinian']['h'][1], $dz);
        $dIndex = 0;
        if ($hIndex > 10) {
            $dIndex = $hIndex;
        } else {
            $dIndex = 10 - $hIndex;
        }
        $sIndex = ($start - $dIndex + 12) % 12;
        $sdz = $dz[$sIndex];
        if ($sIndex == 0) {
            $sdz = '亥';
        }
        $tg = $Lunar['jinian']['y'][0];
        $tgIndex = 0;
        if (in_array($tg, ['甲', '己'])) {
            $tgIndex = 0;
        } elseif (in_array($tg, ['乙', '庚'])) {
            $tgIndex = 1;
        } elseif (in_array($tg, ['丙', '辛'])) {
            $tgIndex = 2;
        } elseif (in_array($tg, ['丁', '壬'])) {
            $tgIndex = 3;
        } else {
            $tgIndex = 4;
        }
        return $lists[$sdz][$tgIndex];
    }

    /**
     * 另一半算法
     * @return array
     */
    private function getlingYiBan(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $_god = $this->lunar->_getGod();
        $dayGodName = $_god['day']['god'][0];
        $sex = $this->lunar->sex;
        // 日支看另一半长相
        $list = [
            '子' => '配偶多长相不错，懂得打扮，有气质，穿衣打扮方面较为精通。因为它们被称为四桃花，代表感情丰富、风流、漂亮；端庄或有能力。在生活中讲情调，有浪漫主义色彩，往往会让你在生活中情趣多多，给你带来惊喜，且身材偏矮小。',
            '丑' => '配偶可能相貌较丑，为人厚道，朴素、敦厚，不懂浪漫为何物，固执脾气大，不通情理，容易得罪人，身材高大。踏实、大方，勤俭持家，能过日子。不属于花花蝴蝶。',
            '寅' => '配偶可能长相一般，为人精明，人好说、热情、聪明。对方工作方面多出差，在生活比较务实，更多的是从生活本质考虑，没那么多浪漫的东西，身材中等。是个有能力的人。',
            '卯' => '配偶多长相不错，懂得打扮，有气质，穿衣打扮方面较为精通。因为它们被称为四桃花，代表感情丰富、风流、漂亮；端庄或有能力。在生活中讲情调，有浪漫主义色彩，往往会让你在生活中情趣多多，给你带来惊喜，且身材偏矮小。',
            '辰' => '配偶可能相貌较丑，为人厚道，朴素、敦厚，不懂浪漫为何物，固执脾气大，不通情理，容易得罪人，身材高大。踏实、大方，勤俭持家，能过日子。不属于花花蝴蝶。',
            '巳' => '配偶可能长相一般，为人精明，人好说、热情、聪明。对方工作方面多出差，在生活比较务实，更多的是从生活本质考虑，没那么多浪漫的东西，身材中等。是个有能力的人。',
            '午' => '配偶多长相不错，懂得打扮，有气质，穿衣打扮方面较为精通。因为它们被称为四桃花，代表感情丰富、风流、漂亮；端庄或有能力。在生活中讲情调，有浪漫主义色彩，往往会让你在生活中情趣多多，给你带来惊喜，且身材偏矮小。',
            '未' => '配偶可能相貌较丑，为人厚道，朴素、敦厚，不懂浪漫为何物，固执脾气大，不通情理，容易得罪人，身材高大。踏实、大方，勤俭持家，能过日子。不属于花花蝴蝶。',
            '申' => '配偶可能长相一般，为人精明，人好说、热情、聪明。对方工作方面多出差，在生活比较务实，更多的是从生活本质考虑，没那么多浪漫的东西，身材中等。是个有能力的人。',
            '酉' => '配偶多长相不错，懂得打扮，有气质，穿衣打扮方面较为精通。因为它们被称为四桃花，代表感情丰富、风流、漂亮；端庄或有能力。在生活中讲情调，有浪漫主义色彩，往往会让你在生活中情趣多多，给你带来惊喜，且身材偏矮小。',
            '戌' => '配偶可能相貌较丑，为人厚道，朴素、敦厚，不懂浪漫为何物，固执脾气大，不通情理，容易得罪人，身材高大。踏实、大方，勤俭持家，能过日子。不属于花花蝴蝶。',
            '亥' => '配偶可能长相一般，为人精明，人好说、热情、聪明。对方工作方面多出差，在生活比较务实，更多的是从生活本质考虑，没那么多浪漫的东西，身材中等。是个有能力的人。',
        ];
        // 配偶星即男看财星，女看官星，对应财星或官星的属性 0 男 1 女
        $list2 = [
            [
                '甲' => ['xing' => ['戊', '己'], 'wx' => '土'],
                '乙' => ['xing' => ['戊', '己'], 'wx' => '土'],
                '丙' => ['xing' => ['庚', '辛'], 'wx' => '金'],
                '丁' => ['xing' => ['庚', '辛'], 'wx' => '金'],
                '戊' => ['xing' => ['壬', '癸'], 'wx' => '水'],
                '己' => ['xing' => ['壬', '癸'], 'wx' => '水'],
                '庚' => ['xing' => ['甲', '乙'], 'wx' => '木'],
                '辛' => ['xing' => ['甲', '乙'], 'wx' => '木'],
                '壬' => ['xing' => ['丙', '丁'], 'wx' => '火'],
                '癸' => ['xing' => ['丙', '丁'], 'wx' => '火'],
            ],
            [
                '甲' => ['xing' => ['庚', '辛'], 'wx' => '金'],
                '乙' => ['xing' => ['庚', '辛'], 'wx' => '金'],
                '丙' => ['xing' => ['壬', '癸'], 'wx' => '水'],
                '丁' => ['xing' => ['壬', '癸'], 'wx' => '水'],
                '戊' => ['xing' => ['甲', '乙'], 'wx' => '木'],
                '己' => ['xing' => ['甲', '乙'], 'wx' => '木'],
                '庚' => ['xing' => ['丙', '丁'], 'wx' => '火'],
                '辛' => ['xing' => ['丙', '丁'], 'wx' => '火'],
                '壬' => ['xing' => ['戊', '己'], 'wx' => '土'],
                '癸' => ['xing' => ['戊', '己'], 'wx' => '土'],
            ],
        ];
        // 配偶星属性
        $list3 = [
            '火' => '夫妻星(财、官)为火，主亮丽，面红润；脸型偏鹅蛋脸，或者说偏圆，他们身体比较丰满。且体毛浓密，面色和善，而且直言快语。人光鲜亮丽，棱角分明。（过旺或逢生，则脾气火暴，过弱或受制，则小肚鸡肠。）火主礼，他们重视礼节，有文采，很乐于装扮的，有很乐观的精神',
            '木' => '夫妻星(财、官)为木，主长得高，发秀，身形优美，有着乌黑发亮的秀发，也是眉清目秀的类型，他们有着清脆的嗓音。人高发秀，鼻直口方，面色偏黄，（过旺高大，毛发粗硬，不及则身材矮小，男的容易秃头。）木是仁厚和慈爱的象征，他们通常理智有主见，潇洒不做作。乙卯木的人待人和谐，容易同情别人，但是有时候也虚伪，容易嫉妒。',
            '水' => '夫妻星(财、官)为水，主人较胖，面黑，人机灵，相貌一般。身材较圆，眉眼浓粗，比较好动，声音亲和。人丰体胖，脸圆发密，皮肤水嫩，（过旺身材高大而肥胖，不及则身材瘦小。）水是主聪慧的，他们心胸宽阔，思想远见，有很多财富。',
            '土' => '夫妻星(财、官)为土，主长的敦厚结实，个头矮，较丑，且皮肤偏黄。人壮敦实，貌似一般，皮肤粗暗，（过旺身材高大而敦厚，不及则身材矮小，）',
            '金' => '夫妻星(财、官)为金，人长得白皙端庄，对方脸型较方，或者偏圆，他们的肤色嫩白，眉清目秀的容貌让人看着赏心悦目，声音是清亮的，有着健硕的体质，他们的形体美观大方。（过旺身材高胖，皮肤细腻，不及则身材瘦矮皮肤昏暗。）因为性格比较仗义，也获得很多人的喜爱，过旺性格火暴，容易发脾气，不及则小气，对生活缺乏情趣。',
        ];
        // 另一半身高 断对象的高矮，胖瘦以日支为主，旺者高大，强者胖。再结合婚年流年，小运对日支的生克来定。
        $list4 = [
            [
                '正印' => '其老婆的身高约156～160公分，体型皆不会太瘦。',
                '偏印' => '其另一半的身材就有点极端了，若不是高高瘦瘦型，就一定是矮胖型，而且老婆是胖妞的机率非常高。',
                '正官' => '其老婆的身高约158～162公分，皆属于标准身材。',
                '七杀' => '其老婆的骨架较大、体型挺拔，身高约162公分以上。',
                '比肩' => '其老婆的身高约158～162公分。但若日支的比肩是寅或卯，则另一半身高都会比一般更高些。',
                '劫财' => '其老婆的身高约164公分以上。日支是劫财的人，其另一半脾气通常会比较火爆，举止动作少了一些斯文气质，也有点粗鲁。',
                '正财' => '另一半的身高比较难以捉摸，不过很少是既矮又瘦的，有就是说大都会属于比较有胖型。',
                '偏财' => '其另一半的身材大都属于标准衣架子型，不会太胖也不会太瘦。',
                '食神' => '其老婆的身高约162公分以上，很有可能是壮硕型的胖哥胖妹，即使婚前是属于健美型，但婚后也很容易变型呢，但是如果八字的天干有正印或偏印，那么您的对象身高则可能要各减10公分左右，而且很难长出肉来。',
                '伤官' => '其老婆的身高约156～160公分。虽然平常不太容易发胖，但当财运不错时，就很难控制其食欲及体重。',
            ],
            [
                '正印' => '其老公身高约168～172公分',
                '偏印' => '其另一半的身材就有点极端了，若不是高高瘦瘦型，就一定是矮胖型，',
                '正官' => '老公身高约168～172公分',
                '七杀' => '其老公的体格高大壮硕，身高约175公分以上',
                '比肩' => '老公身高约170～175公分左右，但若日支的比肩是寅或卯，则另一半身高都会比一般更高些，而且身材大都是肉肉的。',
                '劫财' => '其老公身高约175公分以上，极个别脾气会比较火爆，也有点粗鲁。',
                '正财' => '另一半的身高比较难以捉摸，不过很少是既矮又瘦的，有就是说大都会属于比较有胖型。',
                '偏财' => '其另一半的身材大都属于标准衣架子型，不会太胖也不会太瘦。',
                '食神' => '其老公身高约175公分以上;很有可能是壮硕型的胖哥，即使婚前是属于健美型，但婚后也很容易变型呢，但是如果八字的天干有正印或偏印，那么您的对象身高则可能要各减10公分左右，而且很难长出肉来。',
                '伤官' => '其老公身高约170～175公分左右，但当财运不错时，就很难控制其食欲及体重。',
            ],
        ];
        $list5 = [
            [
                '子' => '妻偏矮小.身材内满.有气质',
                '丑' => '妻身材极端.即夫妻身材不匹配型.男高大.则女矮小..男廋则女胖..(如不应则妻有病灾)',
                '寅' => '身材中等.在1.6米左右上下.妻略丰满.',
                '卯' => '妻偏矮小.身材内满.有气质',
                '辰' => '妻身材极端.即夫妻身材不匹配型.男高大.则女矮小..男廋则女胖..(如不应则妻有病灾)',
                '巳' => '身材中等.在1.6米左右上下.妻略丰满.',
                '午' => '妻偏矮小.身材内满.有气质',
                '未' => '妻身材极端.即夫妻身材不匹配型.男高大.则女矮小..男廋则女胖..(如不应则妻有病灾)',
                '申' => '身材中等.在1.6米左右上下.妻略丰满.',
                '酉' => '妻偏矮小.身材内满.有气质',
                '戌' => '妻身材极端.即夫妻身材不匹配型.男高大.则女矮小..男廋则女胖..(如不应则妻有病灾)',
                '亥' => '身材中等.在1.6米左右上下.妻略丰满.',
            ],
            [
                '子' => '丈夫偏矮小，身材结实，内秀有气质。',
                '丑' => '夫身材极端，即夫妻身材不匹配型。男高大，则女矮小；男瘦则女胖。(如不应则夫有病灾)。',
                '寅' => '丈夫身材中等，在1.7米左右上下。',
                '卯' => '丈夫偏矮小，身材结实，内秀有气质。',
                '辰' => '夫身材极端，即夫妻身材不匹配型。男高大，则女矮小；男瘦则女胖。(如不应则夫有病灾)。',
                '巳' => '丈夫身材中等，在1.7米左右上下。',
                '午' => '丈夫偏矮小，身材结实，内秀有气质。',
                '未' => '夫身材极端，即夫妻身材不匹配型。男高大，则女矮小；男瘦则女胖。(如不应则夫有病灾)。',
                '申' => '丈夫身材中等，在1.7米左右上下。',
                '酉' => '丈夫偏矮小，身材结实，内秀有气质。',
                '戌' => '夫身材极端，即夫妻身材不匹配型。男高大，则女矮小；男瘦则女胖。(如不应则夫有病灾)。',
                '亥' => '丈夫身材中等，在1.7米左右上下。',
            ],
        ];
        $dayDz = $jiNian['d'][1];
        $peiOuXing = $list2[$sex][$jiNian['d'][0]];
        $result = [
            'xiang' => $list[$dayDz],
            'attr' => $list3[$peiOuXing['wx']],
            'height' => $list4[$sex][$dayGodName],
            'height2' => $list5[$sex][$dayDz],
        ];
        return $result;
    }
}
