<?php
// +----------------------------------------------------------------------
// | Gongsiqm.公司起名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use think\Exception;

class Gongsiqm
{
    /**
     * 双字
     * @var array
     */
    protected $shuangzi = [
        '元义', '宝伟', '伟永', '厚鑫', '贵富', '安晶', '协干', '康鑫', '利盈', '新巨', '如复',
        '高大', '公盈', '公厚', '康义', '益进', '厚聚', '干昌', '圣伟', '欣干', '圣茂', '元久',
        '干顺', '祥光', '祥如', '美凯', '兴弘', '干源', '皇利', '恒同', '巨高', '新宝', '亨优',
        '通茂', '富多', '盈和', '晶谦', '协盈', '厚协', '东飞', '百德', '协永', '捷大', '耀满',
        '益万', '旺满', '干汇', '千裕', '兴美', '佳新', '新伟', '昌协', '复飞', '昌生', '厚贵',
        '皇富', '利优', '宏通', '安宏', '复久', '富发', '厚宝', '圣益', '新鑫', '公富', '正发',
        '源广', '欣鑫', '欣旺', '新盛', '全利', '兴庆', '公元', '祥百', '和协', '宝隆', '恒辉',
        '利全', '公利', '利华', '欣东', '聚进', '耀康', '通利', '百厚', '信春', '康洪', '盛禄',
        '发公', '永吉', '盛贵', '盈昌', '凯新', '协谦', '生恒', '久巨', '新久', '合谦', '昌隆',
        '伟凯', '皇春', '优耀', '盛多', '通春', '晶伟', '亨公', '升浩', '飞协', '谦和', '新浩',
        '干新', '福祥', '华元', '合春', '安隆', '皇瑞', '久义', '巨洪', '万千', '亚久', '满金',
        '永耀', '光春', '皇和', '多万', '圣干', '信德', '巨凯', '多茂', '宝生', '捷巨', '盛同',
        '协高', '益成', '亚成', '欣满', '成聚', '公荣', '新兴', '久通', '多巨', '兴满', '浩瑞',
        '复元', '通辉', '百美', '长干', '宏洪', '中泰', '优欣', '高多', '金利', '浩成', '复捷',
        '富国', '亨长', '安益', '宏鑫', '富捷', '旺成', '泰顺', '进益', '厚富', '久万', '裕祥',
        '祥恒', '亚高', '广合', '本发', '永千', '安复', '信百', '皇公', '汇宝', '多进', '皇润',
        '凯捷', '安顺', '欣千', '干千', '汇复', '同永', '新洪', '庆如', '巨新', '辉台', '金协',
        '伟发', '凯弘', '安信', '通大', '富晶', '谦福', '欣高', '旺皇', '聚广', '亚美', '亚生',
        '百茂', '康吉', '盈兴', '协益', '巨旺', '新生', '多顺', '协久', '旺广', '元美', '干旺',
        '义盛', '利同', '皇吉', '兴万', '高广', '隆成', '安茂', '金佳', '圣洪', '利伟', '盛皇',
        '正佳', '厚多', '高光', '皇通', '兴义', '正如', '同祥', '广安', '高新', '源祥', '捷升',
        '欣弘', '本百', '长和', '久复', '聚茂', '仁春', '通干', '百正', '久广', '厚发', '全信',
        '宏高', '中荣', '协欣', '泰耀', '吉祥', '浩弘', '生干', '亚金', '升信', '公宝', '耀发',
        '恒庆', '干进', '成荣', '富优', '祥宝', '如康', '本干', '合弘', '金鼎', '安永', '隆如',
        '本佳', '巨盛', '义亚', '厚信', '飞瑞', '成通', '洪本', '元仁', '兴德', '本吉', '永浩',
        '欣义', '义凯', '厚荣', '新康', '永捷', '盛永', '高佳', '欣久', '厚进', '信和', '合隆',
        '浩宏', '康本', '全庆', '泰同', '公寿', '久益', '祥晶', '百复', '欣昌', '兴旺', '久汇',
        '佳优', '耀进', '金辉', '百进', '泰通', '飞全', '仁发', '鼎洪', '厚百', '耀庆', '康圣',
        '厚广', '兴欣', '寿进', '干复', '裕丰', '益茂', '干捷', '隆聚', '贵本', '义恒', '禄亚',
        '成恒', '多丰', '金谦', '圣永', '富伟', '协厚', '益光', '高进', '盛汇', '华飞', '长大',
        '成茂', '丰浩', '祥成', '耀新', '亚义', '金新', '新欣', '公聚', '晶优', '旺亚', '飞源',
        '谦金', '泰欣', '元圣', '满耀', '泰中', '康全', '复圣', '洪亚', '祥荣', '伟弘', '凯茂',
        '晶禄', '广益', '盛大', '飞通', '皇晶', '汇和', '进丰', '万皇', '源春', '本国', '祥复',
        '晶贵', '巨鼎', '福康', '圣旺', '广多', '干亚', '佳宏', '亨瑞', '欣金', '谦进', '晶弘',
        '东春', '宏兴', '富飞', '生大', '高本', '聚春', '优如', '巨千', '鼎复', '新顺', '隆宏',
        '凯发', '广荣', '新凯', '义晶', '久瑞', '捷鑫', '升元', '仁华', '信升', '升盛', '隆昌',
        '祥巨', '升新', '泰亚', '厚宏', '复茂', '公宏', '新永', '凯顺', '旺捷', '公千', '兴捷',
        '成润', '万飞', '庆盈', '祥裕', '益弘', '千宝', '复富', '利亚', '利源', '进宝', '春利',
        '皇飞', '宝仁', '长圣', '富新', '康鼎', '谦寿', '昌信', '干瑞', '合国', '晶本', '亚信',
        '旺泰', '公信', '晶富', '茂宝', '国源', '益如', '协旺', '弘协', '飞利', '祥利', '春合',
        '成伟', '聚祥', '全发', '多祥', '飞贵', '捷兴', '新本', '伟荣', '荣万', '元台', '正祥',
        '鑫长', '康丰', '永多', '厚全', '伟同', '凯本', '升中', '仁国', '鼎祥', '安庆', '康安',
        '谦昌', '仁捷', '亚隆', '复国', '浩鼎', '昌汇', '千协', '鼎仁', '利欣', '合贵', '金盈',
        '晶祥', '隆安', '万荣', '信公', '聚公', '协协', '利康', '皇辉', '恒聚', '万百', '成华',
        '源洪', '协成', '富盛', '干义', '旺久', '宏昌', '亚万', '佳飞', '中皇', '安万', '富百',
        '晶隆', '正益', '干正', '益安', '寿国', '广金', '利光', '华中', '万春', '飞辉', '利茂',
        '亨恒', '浩同', '多隆', '厚庆', '和优', '凯升', '多皇', '生裕', '本顺', '欣进', '春长',
        '圣昌', '福益', '成盈', '合利', '欣亚', '优鑫', '新和', '荣茂', '如广', '亨祥', '宝祥',
        '干福', '庆旺', '裕弘', '耀国', '皇全', '升飞', '谦丰', '成春', '协弘', '和新', '义协',
        '公祥', '万合', '耀优', '安国', '本禄', '新耀', '正百', '隆仁', '鼎耀', '信昌', '协进',
        '优全', '鼎康', '干巨', '全凯', '协巨', '康合', '谦宝', '万同', '祥新', '协长', '盛公',
        '和盈', '优盛', '福庆', '华益', '如弘', '裕复', '昌裕', '祥通', '欣飞', '成光', '信福',
        '金润', '凯优', '祥瑞', '厚盛', '鼎顺', '优金', '昌鑫', '旺新', '美泰', '全国', '晶生',
        '晶吉', '昌盈', '浩凯', '祥顺', '安凯', '升源', '元鼎', '信长', '升圣', '满多', '厚台',
        '信广', '公茂', '利台', '祥华', '裕长', '复升', '晶如', '合祥', '优寿', '欣成', '皇厚',
        '成元', '祥台', '伟裕', '全德', '鼎鑫', '亚裕', '多永', '安正', '旺优', '信合', '富浩',
        '伟成', '安康', '宝元', '新恒', '皇昌', '干禄', '宝裕', '祥升', '华庆', '进长', '百广',
        '厚瑞', '东皇', '弘东', '复耀', '茂复', '万盈', '万高', '干和', '安亨', '高弘', '万金',
        '满安', '利如', '万多', '皇德', '隆合', '协昌', '长佳', '协庆', '永安', '合发', '寿德',
        '多谦', '新复', '公凯', '干富', '伟台', '弘兴', '公盛', '协宏', '公复', '高成', '祥宏',
        '光飞', '洪多', '义正', '春福', '安进', '干长', '美兴', '耀辉', '光恒', '佳昌', '吉谦',
        '鼎和', '满通', '大本', '聚成', '兴佳', '凯安', '欣洪', '捷佳', '仁禄', '昌久', '久发',
        '公泰', '恒欣', '如益', '中发', '欣捷', '聚浩', '多利', '广同', '宏如', '复美', '伟巨',
        '厚旺', '皇宏', '鼎升', '浩公', '隆福', '厚金', '恒昌', '祥福', '恒亨', '信多', '新升',
        '弘汇', '康公', '中利', '长鼎', '弘裕', '浩高', '巨耀', '凯如', '利润', '庆益', '耀富',
        '复千', '昌谦', '飞寿', '吉巨', '安恒', '成协', '伟顺', '富茂', '升晶', '凯元', '祥永',
        '合益', '春通', '巨百', '满合', '本兴', '春亚', '万安', '亨厚', '安生', '优通', '伟生',
        '新千', '发百', '亚厚', '宝弘', '盛鼎', '厚辉', '荣中', '恒台', '元新', '全厚', '和永',
        '升祥', '义润', '春瑞', '复百', '长新', '聚千', '信佳', '裕元', '元源', '永旺', '正国',
        '万久', '本大', '耀万', '复本', '干永', '万益', '厚春', '鼎巨', '合厚', '兴进', '成兴',
        '恒福', '辉兴', '皇满', '多洪', '干洪', '生润', '元隆', '干广', '皇亚', '优庆', '合德',
        '美如', '旺春', '鼎元', '佳润', '欣泰', '合巨', '如生', '凯伟', '长恒', '恒荣', '伟高',
        '新富', '佳美', '通广', '弘顺', '全贵', '亚长', '兴盈', '茂晶', '复浩', '泰润', '安皇',
        '安丰', '富如', '百恒', '宝盈', '优协', '公成', '金恒', '元贵', '旺高', '润优', '盈泰',
        '本长', '凯国', '干圣', '凯义', '百万', '仁伟', '万恒', '寿大', '公中', '昌昌', '多亨',
        '合广', '长优', '隆富', '益义', '亨百', '寿浩', '如恒', '旺庆', '恒亚',
    ];

    /**
     * 三字
     * @var array
     */
    protected array $sanzi = [
        '源东捷', '宏升升', '万满千', '干兴润', '汇恒润', '吉安久', '多茂寿', '国百全', '聚升福',
        '裕合丰', '汇顺益', '正德优', '富福全', '永厚中', '兴谦安', '春瑞福', '谦满泰', '庆长源',
        '润富大', '千亚安', '福万润', '新茂新', '荣裕昌', '盛晶广', '发多满', '华隆昌', '新信义',
        '德美宏', '优吉信', '瑞鼎盛', '协高大', '瑞宏耀', '春高吉', '台禄正', '义亨新', '源仁庆',
        '新国圣', '汇高如', '成利祥', '昌隆润', '祥多满', '辉义信', '裕百高', '本高和', '发隆益',
        '满干亨', '汇捷干', '长大仁', '元鼎东', '台满百', '协寿厚', '茂高源', '洪东正', '永久久',
        '贵辉福', '宝元祥', '长益永', '升巨进', '圣宏富', '永高大', '洪亚源', '通生元', '久福公',
        '瑞元德', '中凯万', '合合正', '同晶新', '伟国广', '复谦亚', '泰禄祥', '鼎千欣', '如益佳',
        '宝盛利', '同宝浩', '亚广丰', '旺祥盛', '厚亚东', '同荣圣', '大新德', '巨富进', '吉广伟',
        '飞协荣', '信皇干', '康凯福', '公润发', '昌优华', '浩光福', '汇万优', '兴高国', '中昌厚',
        '源寿盈', '恒茂公', '德多晶', '巨万飞', '公润春', '盈义义', '多茂亨', '佳义富', '泰安源',
        '飞复亚', '国万万', '昌润东', '浩全德', '同如吉', '宏荣大', '国协耀', '满贵益', '正巨荣',
        '光仁金', '富千华', '金义国', '凯辉宝', '顺公广', '圣昌顺', '吉捷吉', '成鑫优', '国义满',
        '谦宏飞', '祥耀全', '巨鼎合', '谦裕优', '欣耀优', '祥生盈', '台通裕', '满隆辉', '满荣润',
        '久成金', '洪通康', '聚汇巨', '春富凯', '欣宝盈', '康高元', '进鑫协', '宝皇恒', '亚华春',
        '亨元洪', '升进升', '辉浩光', '久广裕', '弘生长', '华盈高', '华中寿', '成鼎泰', '利东久',
        '裕聚荣', '盛美源', '禄义康', '飞隆厚', '干满光', '泰隆本', '辉亚长', '合大满', '久利弘',
        '盈长亚', '亚信辉', '厚美华', '祥润盛', '巨盛广', '飞洪宝', '贵吉隆', '盈辉协', '进圣国',
        '多富永', '吉聚泰', '盈利圣', '厚禄利', '通伟富', '盛满台', '荣生福', '佳洪庆', '丰贵亨',
        '恒隆凯', '东耀中', '祥寿荣', '华全庆', '贵宏巨', '庆亨庆', '厚久仁', '凯复金', '升鼎仁',
        '利多益', '多伟荣', '万益宏', '同春皇', '寿利宝', '福兴信', '升信丰', '久茂元', '隆盈禄',
        '顺长发', '和亨同', '贵荣德', '生生辉', '巨合安', '全本如', '久源富', '德中丰', '复贵飞',
        '千宏本', '亚祥富', '春泰大', '和康丰', '长东本', '光皇久', '升宝佳', '全吉盈', '兴公永',
        '进宏德', '吉瑞皇', '成成庆', '弘浩亚', '新禄汇', '益公仁', '恒巨干', '凯裕聚', '通弘润',
        '亨信春', '顺耀禄', '亨巨公', '千升恒', '如泰大', '伟祥圣', '弘千弘', '万干百', '合顺鼎',
        '富正利', '凯通多', '广宏满', '捷源正', '盈祥永', '长万合', '荣鼎吉', '多升亚', '高凯泰',
        '汇谦伟', '正晶德', '盛亚顺', '发浩华', '义禄义', '多捷富', '全欣广', '茂仁皇', '佳安辉',
        '和润庆', '台复裕', '盛瑞新', '富美昌', '裕通聚', '旺中千', '永如裕', '吉亨弘', '中多义',
        '禄合顺', '源亚久', '兴全捷', '永瑞生', '盈盈本', '长巨弘', '干协源', '聚贵浩', '佳禄祥',
        '荣中辉', '皇庆欣', '裕国庆', '皇欣同', '亚和圣', '康亚寿', '利全润', '宏进优', '中佳盈',
        '润万荣', '光信合', '元荣弘', '安汇盛', '瑞鼎耀', '和厚成', '发复新', '台利仁', '贵干久',
        '昌飞仁', '元兴伟', '协德晶', '亚凯弘', '仁永源', '生通元', '义源合', '多全公', '泰和盛',
        '兴润本', '东干聚', '百庆富', '本兴协', '聚多皇', '隆皇生', '中辉裕', '伟皇飞', '台厚台',
        '鼎泰泰', '丰巨百', '泰谦利', '如升如', '公金盈', '洪鼎源', '久盈晶', '捷泰干', '荣荣弘',
        '德昌高', '本鼎浩', '如中千', '义成泰', '皇台亚', '瑞弘台', '禄盛凯', '捷同贵', '吉新晶',
        '高华巨', '复昌本', '同金飞', '成禄生', '亚昌裕', '永聚同', '如发巨', '美鑫佳', '禄千裕',
        '汇宏复', '优益公', '洪成盛', '德浩捷', '庆盈万', '飞贵台', '国发贵', '复满聚', '元瑞谦',
        '和永亚', '浩隆亨', '进安中', '巨全捷', '丰寿辉', '公飞发', '金弘元', '发贵禄', '润利复',
        '复生中', '华茂富', '飞禄德', '千旺顺', '千本佳', '源丰盈', '隆国禄', '盈亚长', '信谦伟',
        '吉正同', '润成复', '皇汇伟', '祥全仁', '合寿久', '多吉捷', '弘凯成', '益顺康', '顺优润',
        '茂仁千', '浩源洪', '高优盛', '协茂台', '利福干', '台台优', '庆发丰', '宏丰益', '宝春同',
        '晶优光', '华本新', '富谦元', '公德全', '富德旺', '禄光本', '升东盈', '捷盈谦', '多元安',
        '贵佳泰', '浩正亚', '鑫丰德', '千中信', '泰同润', '谦进盈', '佳发贵', '成巨久', '寿佳益',
        '吉润圣', '欣金昌', '春生信', '和满发', '中元久', '长丰巨', '寿凯捷', '巨弘百', '宝合广',
        '金德安', '庆本千', '裕裕全', '顺恒汇', '大同台', '广福元', '泰义茂', '祥如升', '美安荣',
        '复合源', '凯巨进', '安信盈', '寿广久', '全春旺', '国复欣', '如寿国', '宝恒康', '荣春荣',
        '浩正长', '旺发圣', '祥厚贵', '伟鑫同', '满巨顺', '皇全春', '中贵宏', '顺正寿', '洪富茂',
        '顺盛旺', '仁德圣', '顺鼎寿', '丰久耀', '大华欣', '久巨金', '巨汇伟', '荣寿宝', '高巨信',
        '茂久千', '伟春久', '吉合裕', '光和泰', '欣生本', '弘美中', '飞茂进', '高荣协', '晶益皇',
        '中千华', '贵亨通', '福中鼎', '万富百', '安光圣', '中隆茂', '春春巨', '欣巨干', '庆宏成',
        '本成盛', '洪新顺', '康长吉', '欣长飞', '汇高安', '寿东辉', '浩同中', '庆晶巨', '本进泰',
        '复亨合', '正源公', '复进昌', '聚仁荣', '久圣润', '弘国佳', '瑞春鑫', '裕贵同', '聚协春',
        '华升亨', '隆台安', '顺升本', '禄元安', '盈广协', '久欣成', '干兴顺', '聚鑫华', '富信寿',
        '祥凯发', '泰大升', '泰洪兴', '满通发', '兴源正', '兴优满', '国生本', '新信圣', '宏台凯',
        '晶台大', '千巨长', '正本源', '佳鑫成', '捷满禄', '全优益', '兴伟全', '福百进', '圣信恒',
        '千大亨', '恒祥瑞', '旺贵大', '盈兴安', '美洪晶', '合安信', '顺进亚', '满佳如', '盛生广',
        '佳进合', '生进生', '协皇合', '隆义正', '利凯耀', '皇捷利', '辉皇东', '大长瑞', '庆伟辉',
        '安吉美', '泰益丰', '圣新信', '仁本通', '盈干优', '聚亨华', '亨亨升', '益同利', '百东元',
        '升汇昌', '元浩益', '广正厚', '祥盈盛', '荣洪进', '顺鼎中', '全润厚', '义如正', '国安兴',
        '富恒新', '宝成寿', '圣福富', '伟永亚', '顺生中', '飞泰义', '泰弘飞', '本复兴', '祥荣如',
        '浩盛丰', '光泰如', '隆洪贵', '耀禄荣', '元亚优', '高高辉', '新干协', '利台浩', '公义广',
        '升生信', '祥盈升', '源盈亚', '辉宝贵', '吉巨瑞', '华升华', '恒安裕', '利寿恒', '正富和',
        '优圣茂', '皇东凯', '新大谦', '美洪耀', '凯干盈', '全泰同', '信顺聚', '谦合寿', '华高生',
        '台顺义', '信盈新', '台浩凯', '晶大福', '生凯如', '昌祥禄', '正华耀', '正晶丰', '瑞鼎旺',
        '飞瑞厚', '本国义', '进荣升', '大满光', '耀瑞华', '全飞谦', '吉浩辉', '全千寿', '宏洪复',
        '寿凯庆', '和进福', '亨千义', '浩凯凯', '合亚亨', '多茂兴', '皇全大', '升发禄', '洪禄瑞',
        '国亨荣', '辉元公', '百荣伟', '凯昌晶', '升汇利', '禄荣弘', '昌茂全', '发正福', '凯高进',
        '仁裕东', '百百同', '寿欣飞', '中本长', '谦兴聚', '宏光本', '鼎凯洪', '飞百东', '多万益',
        '满丰鑫', '洪升仁', '飞聚富', '宝耀长', '贵浩贵', '兴圣安', '丰高优', '安茂晶', '光源国',
        '茂亚洪', '如全飞', '复润盈', '生晶和', '元洪美', '荣仁圣', '伟元多', '巨巨安', '耀百满',
        '庆东凯', '昌茂安', '和润汇', '百泰光', '如发寿', '生源台', '合圣耀', '台福生', '顺光国',
        '旺宝汇', '汇中华', '同鼎百', '高皇弘', '贵公新', '多巨生', '盈圣久', '汇元晶', '寿安祥',
        '广协荣', '德安寿', '如谦聚', '皇如皇', '佳捷干', '百弘利', '吉亨大', '复厚长', '盈优同',
        '复如伟', '如禄光', '千升飞', '复安久', '宏干茂', '千富全', '义通百', '广和东', '干益复',
        '贵华宏', '弘耀宏', '同通万', '顺捷仁', '高富成', '仁永多', '圣茂本', '如皇皇', '皇洪巨',
        '利元义', '新宝庆', '利益发', '汇福光', '厚光大', '顺如源', '禄佳润', '恒宏辉', '生谦皇',
        '东恒隆', '仁光本', '亚金利', '汇中多', '庆百通', '仁裕长', '聚久源', '益益弘', '春全飞',
        '佳和弘', '鼎信利', '伟弘信', '伟仁源', '泰禄厚', '多大万', '公新鑫', '百聚巨', '通茂合',
        '隆昌多', '本茂皇', '新国昌', '福万元', '富义宏', '长长弘', '复利升', '干义兴', '鼎德圣',
        '复亚富', '生满国', '华元成', '茂美康', '美亚全', '佳高捷', '东广光', '仁欣伟', '春汇信',
        '昌康瑞', '康金本', '欣顺浩', '国康圣', '晶德丰', '谦成飞', '洪满耀', '洪春同', '汇生华',
        '公如康', '汇升公', '大洪顺', '复干洪', '汇万盈', '吉泰东', '进瑞长', '汇晶凯', '茂万禄',
        '鼎合瑞', '鼎禄禄', '义金东', '发金久', '广捷元', '欣浩仁', '和康本', '厚瑞长', '寿辉干',
        '成康本', '大祥盈', '义多禄', '国晶通', '如昌皇', '昌贵干', '盈顺台', '贵成裕', '生庆凯',
        '伟百富', '光光伟', '晶聚亨', '多满复', '兴荣进', '圣中贵', '利盛升', '利义公', '成通和',
        '盈佳佳', '汇安捷', '禄中亚', '合顺本', '辉公贵', '亚辉洪', '亨东协', '利优飞', '国鼎厚',
        '昌高弘', '进发顺', '荣公安', '高同欣', '凯聚成', '久通元', '永进裕', '润隆福', '和台大',
        '佳顺鼎', '润大百', '禄金凯', '永高鑫', '利聚宝', '圣正千', '利贵恒', '和万晶', '百浩协',
        '生万禄', '广富永', '洪盈耀', '如同升', '美如和', '生久裕', '协盛满', '宏干长', '聚禄安',
        '洪和祥', '台盛祥', '成大圣', '兴飞发', '隆同昌', '润长浩', '大利捷', '大长裕', '巨顺耀',
        '润弘瑞', '丰飞中', '复大久', '亚恒干', '春久正', '本祥东', '百如元', '凯复进', '宏元顺',
        '华隆优', '春义万', '台泰全', '中广圣', '复荣全', '浩春谦', '顺如合', '华多干', '复复隆',
        '千复亚', '耀德顺', '公永宏', '润协捷', '合耀宏', '厚义洪', '耀皇千', '华久安', '美佳贵',
        '亨高富', '发庆凯', '美和满', '高巨优', '光发优', '顺贵隆', '恒台东', '瑞中辉', '荣巨贵',
        '裕生千', '兴弘贵', '厚耀丰', '润盈洪', '盛泰台', '庆升捷', '捷祥信', '中庆晶', '进如富',
        '利长同', '优源吉', '台本福', '吉生顺', '安和万', '伟安长', '浩汇广', '发飞千', '美安旺',
        '安金谦', '国德信', '辉厚捷', '美千祥', '恒进发', '多裕捷', '进长飞', '弘寿鼎', '盈义高',
        '宝国协', '飞久美', '巨弘禄', '凯协元', '生仁美', '华安隆', '德生捷', '寿安千', '优信圣',
        '益优福', '禄满元', '高台和', '欣耀伟', '干益顺', '优恒丰', '福亨干', '协寿恒', '仁巨厚',
        '广圣春', '发华多', '辉亨恒', '发圣多', '洪协台', '广久德', '台干顺', '正光汇', '耀禄通',
    ];

    /**
     * 公司起名
     * @return array
     */
    public function index()
    {
        try {
            $type = input('type', 1, 'trim');
            $page = input('page', 1, 'trim,intval');
            $limit = input('limit', 20, 'trim,intval');
            if (!in_array($type, [1, 2, 3])) {
                return ['status' => 0, 'msg' => '参数错误'];
            }
            $limit = min($limit, 100);
            $offset = ($page - 1) * $limit;
            switch ($type) {
                case 1:
                    $result = array_slice($this->shuangzi, $offset, $limit);
                    break;
                case 2:
                    $result = array_slice($this->sanzi, $offset, $limit);
                    break;
                case 3:
                    $one = ceil($limit / 2);
                    $two = $limit - $one;
                    $result = array_merge(array_slice($this->shuangzi, $offset, $one), array_slice($this->sanzi, $offset, $two));
                    break;
            }
            if (empty($result)) {
                return ['status' => 0, 'msg' => '没有数据了'];
            }
            return $result;
        } catch (Exception $e) {
            return ['status' => 0, 'msg' => $e->getMessage()];
        }
    }
}
