<?php
// +----------------------------------------------------------------------
// | Bazihepan 八字合盘
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use api\ApiResult;
use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\validate\ValidateBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\plugin\WuXing;
use think\facade\Request;

class Bazihepan
{

    /**
     * 原始用户数据
     * @var array
     */
    protected array $orginData = [];

    /**
     * 男基础
     * @var Ex
     */
    protected Ex $male;

    /**
     * 女基础
     * @var Ex
     */
    protected Ex $female;

    /**
     * 神煞
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * 用户基础数据
     * @var array
     */
    protected array $userInfo = [];

    /**
     * 八字合盘
     */
    public function index()
    {
        $data = [
            // 男出生时间
            'maletime' => Request::param('maletime', '', 'trim'),
            // 女出生时间
            'femaletime' => Request::param('femaletime', 0, 'trim'),
            // 测算订单时间
            'otime' => Request::param('otime', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate = $validate->rule(
            [
                'maletime|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'femaletime|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'otime|测试日期' => ['require', 'isDateOrTime:测试日期'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ApiResult::error($validate->getError());
        }

        $this->orginData = $data;
        $this->shaShen = new ShaShen();
        // 男
        $this->male = Ex::date($data['maletime'])->sex(0);
        // 女
        $this->female = Ex::date($data['femaletime'])->sex(1);

        // 用户基本盘
        $baseMale = $this->getBasePan(0);
        $baseFemale = $this->getBasePan(1);
        $this->userInfo = [
            $baseMale, $baseFemale,
        ];
        unset($baseMale['gx'], $baseMale['god_tzs'], $baseMale['god_ths'], $baseFemale['gx'], $baseFemale['god_tzs'], $baseFemale['god_ths']);

        // 纪年
        $jiNianMale = $baseMale['base']['jinian'];
        $jiNianFemale = $baseFemale['base']['jinian'];

        // 分数
        $fenArr = [
            // 年柱合分
            'year' => $this->getFenByYear(),
            // 日柱合分
            'day' => $this->getFenByDay(),
            // 属相分数
            'shuxian' => $this->getShuXiangFen(),
            // 纳音分数
            'nayin' => $this->getNaYinFen(),
            // 神煞合婚
            'shensha' => $this->getShenShaFen(),
            // 六亲合婚
            'liuqing' => $this->liuQingFen(),
            // 喜用神分
            'xyfen' => $this->getXiYongFen(),
        ];

        // 结果
        $result = [
            // 基本盘
            'pan' => [
                'male' => $baseMale,
                'female' => $baseFemale,
            ],
            // 旺夫旺妻
            'wangyun' => [
                'male' => $this->getWangFuQi(0),
                'female' => $this->getWangFuQi(1),
            ],
            // 婚前婚后财富
            'caifu' => [
                'male' => $this->getCaiFuBySex(0),
                'female' => $this->getCaiFuBySex(1),
            ],
            // 分数
            'fen' => $fenArr,
            // 合婚分数
            'fen_he' => $this->getHeHunFen($fenArr),
            // 神煞
            'shensha' => [
                'male' => $this->shaShen->detail($jiNianMale, 0),
                'female' => $this->shaShen->detail($jiNianFemale, 1),
            ],
            // 相互关系
            'xhgx' => $this->getXhgx(),
            // 五行参数
            'wxcs' => $this->getWxcs(),
        ];
        // 天干关系
        $result['tggx'] = $this->getTggx($result['shensha']);
        return $result;
    }

    /**
     * 五行参数
     * @return array
     */
    protected function getWxcs()
    {
        // 组合十神数据表，五行十神
        $zhssb = [
            '金' => ['金' => '比劫', '木' => '才财', '水' => '食伤', '火' => '官杀', '土' => '印枭',],
            '木' => ['金' => '官杀', '木' => '比劫', '水' => '印枭', '火' => '食伤', '土' => '才财',],
            '水' => ['金' => '印枭', '木' => '食伤', '水' => '比劫', '火' => '才财', '土' => '官杀',],
            '火' => ['金' => '才财', '木' => '印枭', '水' => '官杀', '火' => '比劫', '土' => '食伤',],
            '土' => ['金' => '食伤', '木' => '官杀', '水' => '才财', '火' => '印枭', '土' => '比劫',],
        ];
        // 纪年五行
        $jnWxdMale = $this->userInfo[0]['jnwx']['wx']['d'][0];
        $jnWxdFemale = $this->userInfo[1]['jnwx']['wx']['d'][0];

        // 取得组合十神
        $zhssListMale = $zhssb[$jnWxdMale];
        $zhssListFemale = $zhssb[$jnWxdFemale];

        // 取得五行数值
        $wuxingNumMale = $this->male->getWuxingNum();
        $wuxingNumFemale = $this->female->getWuxingNum();

        // 取得最大和最小五行
        $maxMale = array_keys($wuxingNumMale, max($wuxingNumMale))[0];
        $minMale = array_keys($wuxingNumMale, min($wuxingNumMale))[0];
        $maxFemale = array_keys($wuxingNumFemale, max($wuxingNumFemale))[0];
        $minFemale = array_keys($wuxingNumFemale, min($wuxingNumFemale))[0];

        return [
            'male' => [
                'min' => $minMale,
                'min_ss' => $zhssListMale[$minMale],
                'max' => $maxMale,
                'max_ss' => $zhssListMale[$maxMale],
            ],
            'female' => [
                'min' => $maxFemale,
                'min_ss' => $zhssListFemale[$minFemale],
                'max' => $maxFemale,
                'max_ss' => $zhssListFemale[$maxFemale],
            ],
        ];
    }

    /**
     * 天干关系
     * @param array $shensha 神煞
     * @return array
     */
    protected function getTggx(array $shensha)
    {
        // 纪年
        $jiNianMale = $this->userInfo[0]['base']['jinian'];
        $jiNianFemale = $this->userInfo[1]['base']['jinian'];

        // 天干关系表
        $tggxb = [
            '甲' => [
                '相同' => ['甲'],
                '相生' => ['丙', '丁'],
                '相克' => ['戊', '己'],
                '相冲' => ['庚'],
                '五合' => ['己'],
            ],
            '乙' => [
                '相同' => ['乙'],
                '相生' => ['丙', '丁'],
                '相克' => ['戊', '己'],
                '相冲' => ['辛'],
                '五合' => ['庚'],
            ],
            '丙' => [
                '相同' => ['丙'],
                '相生' => ['戊', '己'],
                '相克' => ['庚', '辛'],
                '相冲' => ['壬'],
                '五合' => ['辛'],
            ],
            '丁' => [
                '相同' => ['丁'],
                '相生' => ['戊', '己'],
                '相克' => ['庚', '辛'],
                '相冲' => ['癸'],
                '五合' => ['壬'],
            ],
            '戊' => [
                '相同' => ['戊'],
                '相生' => ['庚', '辛'],
                '相克' => ['壬', '癸'],
                '相冲' => ['无'],
                '五合' => ['癸'],
            ],
            '己' => [
                '相同' => ['己'],
                '相生' => ['庚', '辛'],
                '相克' => ['壬', '癸'],
                '相冲' => ['无'],
                '五合' => ['甲'],
            ],
            '庚' => [
                '相同' => ['庚'],
                '相生' => ['壬', '癸'],
                '相克' => ['甲', '乙'],
                '相冲' => ['甲'],
                '五合' => ['乙'],
            ],
            '辛' => [
                '相同' => ['辛'],
                '相生' => ['壬', '癸'],
                '相克' => ['甲', '乙'],
                '相冲' => ['乙'],
                '五合' => ['丙'],
            ],
            '壬' => [
                '相同' => ['壬'],
                '相生' => ['甲', '乙'],
                '相克' => ['丙', '丁'],
                '相冲' => ['丙'],
                '五合' => ['丁'],
            ],
            '癸' => [
                '相同' => ['癸'],
                '相生' => ['甲', '乙'],
                '相克' => ['丙', '丁'],
                '相冲' => ['丁'],
                '五合' => ['戊'],
            ],
        ];
        // 地支关系
        $dzgxb = [
            '子' => [
                '相同' => ['子'],
                '相破' => ['酉'],
                '相冲' => ['午'],
                '相刑' => ['卯'],
                '相害' => ['未'],
                '三合' => ['辰', '申'],
                '三会' => ['丑', '亥'],
                '六合' => ['丑'],
                '暗合' => ['巳'],
            ],
            '丑' => [
                '相同' => ['丑'],
                '相破' => ['辰'],
                '相冲' => ['未'],
                '相刑' => ['戌'],
                '相害' => ['午'],
                '三合' => ['巳', '酉'],
                '三会' => ['子', '亥'],
                '六合' => ['子'],
                '暗合' => ['寅'],
            ],
            '寅' => [
                '相同' => ['寅'],
                '相破' => ['亥'],
                '相冲' => ['申'],
                '相刑' => ['巳'],
                '相害' => ['巳'],
                '三合' => ['午', '戌'],
                '三会' => ['卯', '辰'],
                '六合' => ['亥'],
                '暗合' => ['丑', '午'],
            ],
            '卯' => [
                '相同' => ['卯'],
                '相破' => ['午'],
                '相冲' => ['酉'],
                '相刑' => ['子'],
                '相害' => ['辰'],
                '三合' => ['亥', '未'],
                '三会' => ['辰', '寅'],
                '六合' => ['戌'],
                '暗合' => ['申'],
            ],
            '辰' => [
                '相同' => ['辰'],
                '相破' => ['丑'],
                '相冲' => ['戌'],
                '相刑' => ['辰'],
                '相害' => ['卯'],
                '三合' => ['子', '申'],
                '三会' => ['寅', '卯'],
                '六合' => ['酉'],
            ],
            '巳' => [
                '相同' => ['巳'],
                '相破' => ['申'],
                '相冲' => ['亥'],
                '相刑' => ['申'],
                '相害' => ['寅'],
                '三合' => ['酉', '丑'],
                '三会' => ['午', '未'],
                '六合' => ['申'],
                '暗合' => ['酉', '子'],
            ],
            '午' => [
                '相同' => ['午'],
                '相破' => ['卯'],
                '相冲' => ['子'],
                '相刑' => ['午'],
                '相害' => ['丑'],
                '三合' => ['寅', '戌'],
                '三会' => ['巳', '未'],
                '六合' => ['未'],
                '暗合' => ['寅', '亥'],
            ],
            '未' => [
                '相同' => ['未'],
                '相破' => ['戌'],
                '相冲' => ['丑'],
                '相刑' => ['丑'],
                '相害' => ['子'],
                '三合' => ['亥', '卯'],
                '三会' => ['巳', '午'],
                '六合' => ['午'],
            ],
            '申' => [
                '相同' => ['申'],
                '相破' => ['巳'],
                '相冲' => ['寅'],
                '相刑' => ['寅'],
                '相害' => ['亥'],
                '三合' => ['辰', '子'],
                '三会' => ['酉', '戌'],
                '六合' => ['巳'],
                '暗合' => ['卯'],
            ],
            '酉' => [
                '相同' => ['酉'],
                '相破' => ['子'],
                '相冲' => ['卯'],
                '相刑' => ['酉'],
                '相害' => ['戌'],
                '三合' => ['巳', '丑'],
                '三会' => ['戌', '申'],
                '六合' => ['辰'],
                '暗合' => ['巳'],
            ],
            '戌' => [
                '相同' => ['戌'],
                '相破' => ['未'],
                '相冲' => ['辰'],
                '相刑' => ['未'],
                '相害' => ['酉'],
                '三合' => ['午', '寅'],
                '三会' => ['申', '酉'],
                '六合' => ['卯'],
            ],
            '亥' => [
                '相同' => ['亥'],
                '相破' => ['寅'],
                '相冲' => ['巳'],
                '相刑' => ['亥'],
                '相害' => ['申'],
                '三合' => ['卯', '未'],
                '三会' => ['子', '丑'],
                '六合' => ['寅'],
                '暗合' => ['午'],
            ],
        ];
        // 五行相克表
        $wxxkb = [
            '金木', '木土', '水火', '火金', '土水',
            '木金', '水土', '金火', '火水', '土木',
        ];
        // 五行相生表
        $wxxsb = [
            '金水', '水金', '木火', '火木', '水木',
            '木水', '火土', '土火', '土金', '金土',
        ];
        // 四柱关系概要解释表
        $szgxgyjsb = [
            '相刑' => '可能导致双方性格上的冲突和不合，影响彼此间的相处和沟通。',
            '相生' => '有助于增进双方的理解和包容，对关系有积极的影响。',
            '相冲' => '可能带来性格上的对立和冲突，需要双方注意调节和沟通。',
            '相害' => '可能带来一定的负面影响，如健康、财运等方面的不顺，也可能影响双方的感情和信任。',
            '相合' => '表示双方性格、观念等方面有相似之处，有助于增进感情和默契。',
            '相克' => '可能带来一些挑战和困难，需要双方共同努力去克服。',
            '三合、三会' => '通常被视为吉利的象征，表示双方在某些方面有共同点和互补性，有利于关系的和谐与发展。',
            '五合' => '在天干中较为常见，但年干五合的具体影响可能不如其他地支关系显著，一般也象征和谐与互助。',
            '暗合' => '年干暗合可能意味着双方在某种层面上有默契或共同的目标，但不易被外界察觉，对双方关系有潜在的正面影响。',
        ];
        // 四柱天干地支详情表
        $sztgdzxqb = [
            'ytg' => [
                '含义' => '年干代表着个体的根本性格特征、早年环境以及家族遗传等因素。它在八字命局中占据核心地位，对整体格局有着不可忽视的影响。',
                '影响' => '年干若为吉神，可能预示着命主早年生活顺遂，性格平和；若为凶神，则可能意味着早年经历波折，性格复杂多变，但也可能因此激发出非凡的斗志和成就。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突。',
                        '运势影响' => '在运势上可能相互冲突，影响双方的生活和事业。',
                        '婚姻关系' => '需要双方高度理解和包容，否则婚姻可能面临挑战。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互学习和促进成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的思维方式和行为习惯，易于产生共鸣。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退。',
                        '婚姻关系' => '彼此间有较强的默契和认同感，有助于婚姻的稳定，但也可能因过于相似而缺乏新鲜感。',
                    ],
                ],
            ],
            'ydz' => [
                '含义' => '年支则与个体的根基、家族背景以及早年成长环境紧密相关。它反映了命主在社会中的定位感和归属感。',
                '影响' => '年支的稳定与否对命主的内心安全感和外在表现有着直接的影响。有利的年支组合可能带来事业和家庭的顺遂，而不利的组合则可能带来挑战和变故。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方性格可能截然相反，容易产生冲突。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定。',
                    ],
                    '相破' => [
                        '性格影响' => '双方性格可能存在不和谐因素，容易产生矛盾。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能带有相似的家族烙印和兴趣爱好。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇。',
                        '婚姻关系' => '彼此间有较强的认同感和归属感，但也可能因过于相似而缺乏变化。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方性格可能存在不利影响，容易产生伤害和矛盾。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业。',
                        '婚姻关系' => '需要双方付出更多努力来克服障碍，维护婚姻关系。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方性格可能和谐互补，易于相处和协作。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
            'mtg' => [
                '含义' => '月干代表着个体的情感表达、内在性格以及对外界环境的适应能力。它反映了命主在特定年龄段（通常是青少年到成年初期）的生活状态和心理特征。',
                '影响' => '月干若为吉神，可能预示着命主在此阶段情感丰富、性格开朗，易于与他人建立良好关系；若为凶神，则可能意味着命主在此阶段经历情感波折，性格较为内敛或复杂，但也可能因此培养出独特的魅力和深度。同时，月干还影响着命主的社交能力和人际关系。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成默契的情感关系。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互理解和包容，促进彼此的成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和默契，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感表达方式和内在性格，易于产生共鸣和情感上的默契。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退，相互支持。',
                        '婚姻关系' => '彼此间有较强的情感纽带和认同感，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'mdz' => [
                '含义' => '月支则与个体的家庭背景、成长环境以及情感需求紧密相关。它反映了命主在情感层面的归属感和安全感。',
                '影响' => '月支的稳定与否对命主的情感表达和内心安全感有着直接的影响。有利的月支组合可能带来温馨的家庭氛围和和谐的情感关系，而不利的组合则可能带来情感上的挑战和内心的不安。同时，月支还与命主的运势和事业发展有着密切的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方情感表达方式可能截然相反，容易产生冲突和误解。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感需求可能存在不和谐因素，容易产生矛盾和冲突。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展和生活质量。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感需求和家庭背景，易于产生共鸣和情感上的联系。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇，能够共同把握机会，共同发展。',
                        '婚姻关系' => '彼此间有较强的情感纽带和归属感，有助于婚姻的稳定和和谐。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业发展。',
                        '婚姻关系' => '需要双方付出更多努力来克服障碍，维护婚姻关系的和谐与稳定。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方情感关系可能和谐互补，易于相处和协作，形成默契的情感纽带。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
            'dtg' => [
                '含义' => '日干代表着个体的核心性格、自我表达能力以及对外界环境的直接反应。它如同一面镜子，映照出命主最本质的一面。',
                '影响' => '日干的属性若为吉，可能预示着命主性格开朗、积极向上，善于表达自我；若为凶，则可能意味着命主性格内敛或复杂，但也可能因此孕育出独特的魅力和深度。同时，日干还与命主的运势和事业发展息息相关，影响着其一生的走向。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成深厚的情感纽带。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐，否则可能面临分离的风险。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解来调和。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免因性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互欣赏和学习，促进彼此的成长。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同提升生活质量。',
                        '婚姻关系' => '关系中充满温情和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的核心性格和自我表达方式，易于产生共鸣和理解。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够携手共进。',
                        '婚姻关系' => '彼此间有较强的性格契合度和默契感，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'ddz' => [
                '含义' => '日支则与个体的内心世界、情感需求以及家庭背景紧密相连。它如同一扇窗，透露出命主情感层面的真实面貌。',
                '影响' => '日支的稳定与否直接影响着命主的情感表达和内心安全感。有利的日支组合可能带来和谐的家庭氛围和稳定的情感关系，而不利的组合则可能引发情感上的波折和内心的不安。同时，日支还与命主的社交能力和人际关系有着千丝万缕的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方情感表达方式可能截然相反，容易产生冲突和误解，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整策略。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定和和谐，加强沟通和交流。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感需求可能存在不和谐因素，容易产生矛盾和冲突，需要更多的沟通和理解来调和。',
                        '运势影响' => '在运势上可能相互破坏，影响双方的发展和生活质量，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐，否则可能面临分离的风险。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感需求和家庭背景，易于产生共鸣和情感上的联系，增进彼此的理解。',
                        '运势影响' => '在运势上可能面临相似的社会环境和机遇，能够共同把握机会，实现共同发展。',
                        '婚姻关系' => '彼此间有较强的情感纽带和归属感，有助于婚姻的稳定和和谐，增强家庭的凝聚力。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解来化解冲突。',
                        '运势影响' => '在运势上可能相互阻碍，影响双方的生活和事业发展，需要共同努力来克服障碍。',
                        '婚姻关系' => '需要双方付出更多努力来维护婚姻关系的和谐与稳定，增强信任和包容度。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方情感关系可能和谐互补，易于相处和协作，形成默契的情感纽带，增进彼此的理解和信任。',
                        '运势影响' => '在运势上可能形成强强联合，共同创造美好未来，提升彼此的生活质量和发展空间。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满，增强家庭的幸福感和归属感。',
                    ],
                ],
            ],
            'htg' => [
                '含义' => '时干代表着个体的行动力、决策能力以及对外界环境的应变能力。它如同一面旗帜，引领着命主在生活中的前进方向。',
                '影响' => '时干的属性若为吉，可能预示着命主行动果断、决策力强，善于把握时机；若为凶，则可能意味着命主行事犹豫或冲动，但也可能因此锻炼出独特的判断力和行动力。同时，时干还与命主的晚年运势和子女状况有着紧密的联系。',
                '关系' => [
                    '五合' => [
                        '性格影响' => '双方性格和谐，易于沟通和交流，能够形成默契的行动力和决策方式。',
                        '运势影响' => '在运势上可能相互助力，共同创造美好未来，提升彼此的生活质量。',
                        '婚姻关系' => '关系中充满和谐与甜蜜，有利于婚姻的幸福美满和长久稳定。',
                    ],
                    '相冲' => [
                        '性格影响' => '双方性格可能截然不同，容易产生激烈冲突，需要更多的包容和妥协来维系关系中的行动力和决策共识。',
                        '运势影响' => '在运势上可能相互冲突，给双方带来挑战和变化，需要共同应对和调整策略。',
                        '婚姻关系' => '需要双方高度理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相克' => [
                        '性格影响' => '双方性格可能存在差异，可能产生摩擦和冲突，需要更多的沟通和理解来调和行动力和决策方式。',
                        '运势影响' => '在运势上可能相互制约，影响彼此的发展，需要共同努力来克服挑战。',
                        '婚姻关系' => '需要双方付出更多努力来维护关系，避免因性格不合导致婚姻破裂。',
                    ],
                    '相生' => [
                        '性格影响' => '双方性格互补，能够相互激励和促进，共同提升行动力和决策能力。',
                        '运势影响' => '可能形成良性互动，一方的顺利能带动另一方，共同创造美好的未来。',
                        '婚姻关系' => '关系中充满活力和支持，有利于婚姻的长久发展。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的行动力和决策方式，易于在行动上产生共鸣和协作。',
                        '运势影响' => '在事业和财运上可能面临相似的机遇和挑战，能够共同进退，相互支持。',
                        '婚姻关系' => '彼此间有较强的行动默契和决策共识，有助于婚姻的稳定和和谐。',
                    ],
                ],
            ],
            'hdz' => [
                '含义' => '时支则与个体的内心世界、情感归宿以及晚年生活紧密相连。它如同一盏明灯，照亮了命主晚年生活的道路。',
                '影响' => '时支的稳定与否直接影响着命主的情感寄托和晚年生活的幸福感。有利的时支组合可能带来温馨的晚年生活和和谐的情感关系，而不利的组合则可能引发情感上的孤独和晚年生活的不安。同时，时支还与命主的社交圈层和人际关系有着密切的联系。',
                '关系' => [
                    '相冲' => [
                        '性格影响' => '双方晚年生活的期望可能截然相反，容易产生冲突和误解，需要更多的包容和妥协来维系关系。',
                        '运势影响' => '在晚年运势上可能相互冲突，给双方带来挑战和变化。',
                        '婚姻关系' => '需要双方更多的理解和包容，以维持婚姻的稳定和和谐。',
                    ],
                    '相破' => [
                        '性格影响' => '双方情感归宿可能存在不和谐因素，容易产生矛盾和冲突，需要更多的沟通和理解来调和晚年生活的期望。',
                        '运势影响' => '在晚年运势上可能相互破坏，影响双方的生活质量和幸福感。',
                        '婚姻关系' => '需要双方付出努力来克服障碍，维护婚姻关系的稳定和和谐。',
                    ],
                    '相同' => [
                        '性格影响' => '双方可能具有相似的情感归宿和晚年生活期望，易于产生共鸣和情感上的联系。',
                        '运势影响' => '在晚年运势上可能面临相似的社会环境和机遇，能够共同把握机会，享受美好的晚年生活。',
                        '婚姻关系' => '彼此间有较强的情感纽带和晚年生活的共同期望，有助于婚姻的稳定和和谐。',
                    ],
                    '相刑、相害' => [
                        '性格影响' => '双方情感关系可能存在不利影响，容易产生伤害和矛盾，需要更多的沟通和理解来化解晚年生活的冲突。',
                        '运势影响' => '在晚年运势上可能相互阻碍，影响双方的生活质量和幸福感。',
                        '婚姻关系' => '需要双方付出更多努力来维护婚姻关系的和谐与稳定。',
                    ],
                    '三合、三会、六合、暗合' => [
                        '性格影响' => '双方晚年生活的期望可能和谐互补，易于相处和协作，形成默契的情感纽带。',
                        '运势影响' => '在晚年运势上可能形成强强联合，共同创造美好的晚年生活。',
                        '婚姻关系' => '关系中充满默契和协作精神，有利于婚姻的长久稳定和幸福美满。',
                    ],
                ],
            ],
        ];

        // 男女天干地支关系表
        $nvtgdzgxb = [];
        $nvtgdzgxbMale = [];
        $nvtgdzgxbFemale = [];
        // 需要加 b 标签
        $tintBArr = ['三合', '三会', '五合', '六合', '暗合'];
        // 四柱关系
        $szgxArr = [];
        // 以男的走一遍
        foreach ($jiNianMale as $k => $v) {
            // 天干关系
            $tggxMale = $tggxb[$jiNianMale[$k][0]];
            // 地支关系
            $dzgxMale = $dzgxb[$jiNianMale[$k][1]];

            foreach ($tggxMale as $name => $var) {
                if (in_array($jiNianFemale[$k][0], (array)$var)) {
                    $nvtgdzgxbMale[$k]['tg'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbMale[$k]['tg'])) {
                $nvtgdzgxbMale[$k]['tg'][] = '无';
            }
            foreach ($dzgxMale as $name => $var) {
                if (in_array($jiNianFemale[$k][1], (array)$var)) {
                    $nvtgdzgxbMale[$k]['dz'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbMale[$k]['dz'])) {
                $nvtgdzgxbMale[$k]['dz'][] = '无';
            }
        }
        // 以女的走一遍
        foreach ($jiNianFemale as $k => $v) {
            // 天干关系
            $tggxMale = $tggxb[$jiNianFemale[$k][0]];
            // 地支关系
            $dzgxMale = $dzgxb[$jiNianFemale[$k][1]];

            foreach ($tggxMale as $name => $var) {
                if (in_array($jiNianMale[$k][0], (array)$var)) {
                    $nvtgdzgxbFemale[$k]['tg'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbFemale[$k]['tg'])) {
                $nvtgdzgxbFemale[$k]['tg'][] = '无';
            }
            foreach ($dzgxMale as $name => $var) {
                if (in_array($jiNianMale[$k][1], (array)$var)) {
                    $nvtgdzgxbFemale[$k]['dz'][] = in_array($name, $tintBArr) ? "<b>{$name}</b>" : $name;
                    $szgxArr[$name] = $name;
                }
            }
            if (empty($nvtgdzgxbFemale[$k]['dz'])) {
                $nvtgdzgxbFemale[$k]['dz'][] = '无';
            }
        }
        // 结果合并，如果男的其中一个为“无”，以女的填充
        $nvtgdzgxb = $nvtgdzgxbMale;
        foreach ($nvtgdzgxbMale as $k => $list) {
            foreach ($list as $na => $nameList) {
                foreach ($nameList as $k2 => $var) {
                    if ($var == '无') {
                        $nvtgdzgxb[$k][$na][$k2] = $nvtgdzgxbFemale[$k][$na][$k2];
                    }
                }
            }
        }

        // 年柱、日柱纳音五行
        $nayinYmale = preg_split('/(?<!^)(?!$)/u', $this->userInfo[0]['na_yin']['year']);
        $nayinDmale = preg_split('/(?<!^)(?!$)/u', $this->userInfo[0]['na_yin']['day']);
        $nayinYfemale = preg_split('/(?<!^)(?!$)/u', $this->userInfo[1]['na_yin']['year']);
        $nayinDfemale = preg_split('/(?<!^)(?!$)/u', $this->userInfo[1]['na_yin']['day']);
        $ynNyWx = [
            'male' => [
                'y' => end($nayinYmale),
                'd' => end($nayinDmale),
            ],
            'female' => [
                'y' => end($nayinYfemale),
                'd' => end($nayinDfemale),
            ],
        ];

        // 五行相生相克
        $wxxsxk = [
            'y' => '无',
            'd' => '无',
        ];
        $nayinYearMale = $this->userInfo[0]['na_yin']['year'];
        $nayinYearFemale = $this->userInfo[1]['na_yin']['year'];
        $nayinDayMale = $this->userInfo[0]['na_yin']['day'];
        $nayinDayFemale = $this->userInfo[1]['na_yin']['day'];
        // 男女纳音年相同
        if ($nayinYearMale === $nayinYearFemale) {
            $wxxsxk['y'] = '相同';
        } else {
            // 检查是否存在相克或相生关系
            $pair = $ynNyWx['male']['y'] . $ynNyWx['female']['y'];
            if (in_array($pair, $wxxkb)) {
                $wxxsxk['y'] = '相克';
            } elseif (in_array($pair, $wxxsb)) {
                $wxxsxk['y'] = '相生';
            }
        }
        // 男女纳音月相同
        if ($nayinDayMale === $nayinDayFemale) {
            $wxxsxk['d'] = '相同';
        } else {
            // 检查是否存在相克或相生关系
            $pair = $ynNyWx['male']['d'] . $ynNyWx['female']['d'];
            if (in_array($pair, $wxxkb)) {
                $wxxsxk['d'] = '相克';
            } elseif (in_array($pair, $wxxsb)) {
                $wxxsxk['d'] = '相生';
            }
        }

        // 夫妻宫加持吉神
        $fqgjcjs = [];
        $shenshaAll = array_merge($shensha['male']['d'], $shensha['female']['d']);
        foreach ($shenshaAll as $name) {
            if (in_array($name, ['天乙贵人', '天德贵人', '月德贵人', '天德合', '月德合'])) {
                $fqgjcjs[$name] = $name;
            }
        }
        $fqgjcjs = array_values($fqgjcjs);

        // 四柱概况解释
        $szgkjs = [];
        foreach ($szgxArr as $name) {
            foreach ($szgxgyjsb as $na => $js) {
                $naArr = explode('、', $na);
                if (in_array($name, $naArr)) {
                    $szgkjs[$name] = $js;
                }
            }
        }
        // 四柱天干地支详情
        $sztgdzxq = [];
        foreach ($nvtgdzgxb as $t => $val) {
            // 循环天干地支
            foreach ($val as $tgdz => $item) {
                $key = "{$t}{$tgdz}";
                $keyName = match ($key) {
                    'ytg' => '年干',
                    'ydz' => '年支',
                    'mtg' => '月干',
                    'mdz' => '月支',
                    'dtg' => '日干',
                    'ddz' => '日支',
                    'htg' => '时干',
                    'hdz' => '时支',
                };
                // 获取天干地支详情
                $tgdzxq = $sztgdzxqb[$key]['关系'] ?? [];
                if (empty($tgdzxq)) {
                    continue;
                }
                $gx = [];
                foreach ($item as $name) {
                    $na = strip_tags($name);
                    $jsArr = [];
                    $jsKey = '';
                    if (!isset($tgdzxq[$na])) {
                        foreach ($tgdzxq as $title => $js) {
                            $titleArr = explode('、', $title);
                            if (in_array($na, $titleArr)) {
                                $jsArr = $js;
                                $jsKey = $title;
                            }
                        }
                    } else {
                        $jsArr = $tgdzxq[$na];
                        $jsKey = $na;
                    }
                    if (empty($jsArr)) {
                        continue;
                    }
                    // 判断是否类似 ‘三合、三会、六合、暗合’ 这种一组的，需要合并
                    if (isset($gx[$jsKey])) {
                        $gx[$jsKey] = [
                            'name' => $gx[$jsKey]['name'] . '、' . $na,
                            'js' => $jsArr,
                        ];
                    } else {
                        $gx[$jsKey] = [
                            'name' => $na,
                            'js' => $jsArr,
                        ];
                    }
                }
                if (!empty($gx)) {
                    $sztgdzxq[] = [
                        'name' => $keyName,
                        'dy' => [
                            '含义' => $sztgdzxqb[$key]['含义'],
                            '影响' => $sztgdzxqb[$key]['影响'],
                        ],
                        'gx' => array_values($gx),
                    ];
                }
            }
        }

        $result = [
            // 男女天干地支关系
            'nvtgdzgxb' => $nvtgdzgxb,
            // 年柱、日柱纳音五行
            'ynnywx' => $ynNyWx,
            // 五行相生相克
            'wxxsxk' => $wxxsxk,
            // 夫妻宫加持吉神
            'fqgjcjs' => $fqgjcjs,
            // 四柱概况解释
            'szgkjs' => $szgkjs,
            // 四柱天干地支详情
            'sztgdzxq' => $sztgdzxq,
        ];
        return $result;
    }

    /**
     * 相互关系
     * @return array
     */
    protected function getXhgx()
    {
        // 纪年
        $jiNianMale = $this->userInfo[0]['base']['jinian'];
        $jiNianFemale = $this->userInfo[1]['base']['jinian'];

        // 男
        $ytgMale = $jiNianMale['y'][0];
        $ydzMale = $jiNianMale['y'][1];
        $mtgMale = $jiNianMale['m'][0];
        $mdzMale = $jiNianMale['m'][1];
        $dtgMale = $jiNianMale['d'][0];
        $ddzMale = $jiNianMale['d'][1];
        $htgMale = $jiNianMale['h'][0];
        $hdzMale = $jiNianMale['h'][1];

        // 女
        $ytgFemale = $jiNianFemale['y'][0];
        $ydzFemale = $jiNianFemale['y'][1];
        $mtgFemale = $jiNianFemale['m'][0];
        $mdzFemale = $jiNianFemale['m'][1];
        $dtgFemale = $jiNianFemale['d'][0];
        $ddzFemale = $jiNianFemale['d'][1];
        $htgFemale = $jiNianFemale['h'][0];
        $hdzFemale = $jiNianFemale['h'][1];

        $result = [];

        // 女
        foreach ($jiNianFemale as $k => $v) {
            if (
                // 根据男的年干找到结果，看看女的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($ytgMale, $v[1]) ||
                // 根据男的日干找到结果，看看女的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($dtgMale, $v[1])
            ) {
                $result['female'][$v[1] . '天乙贵人'] = [$v[1], '天乙贵人', '寓意：催旺贵人运，遇事有人帮。'];
            }
            if ($this->shaShen->getTianChu($ytgMale . $v[1]) || $this->shaShen->getTianChu($htgMale . $v[1])) {
                $result['female'][$v[1] . '天厨贵人'] = [$v[1], '天厨贵人', '寓意：催旺口福，主提升家庭生活品质。'];
            }
            if ($k !== 'y' && $this->shaShen->getZaiSha($ydzMale . $v[1])) {
                $result['female'][$v[1] . '灾煞'] = [$v[1], '灾煞', '寓意：出现障碍，不让强出头。'];
            }
            if ($this->shaShen->getTianDe($mdzMale . $v[0])) {
                $result['female'][$v[0] . '天德贵人'] = [$v[0], '天德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDe($mdzMale . $v[0])) {
                $result['female'][$v[0] . '月德贵人'] = [$v[0], '月德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getTianDeHe($mdzMale . $v[0])) {
                $result['female'][$v[0] . '天德合'] = [$v[0], '天德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDeHe($mdzMale . $v[0])) {
                $result['female'][$v[0] . '月德合'] = [$v[0], '月德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYangRen($dtgMale . $v[1])) {
                $result['female'][$v[1] . '羊刃'] = [$v[1], '羊刃', '寓意：情绪冲动，脾气大倔强。'];
            }
            if ($this->shaShen->getXianChi($ydzMale . $v[1])) {
                $result['female'][$v[1] . '桃花'] = [$v[1], '桃花', '寓意：催旺桃花运，主异性缘旺。'];
            }
        }
        $result['female'] = array_values($result['female'] ?? []);

        // 男
        foreach ($jiNianMale as $k => $v) {
            if (
                // 根据女的年干找到结果，看看男的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($ytgFemale, $v[1]) ||
                // 根据女的日干找到结果，看看男的地支有没有在结果里面
                $this->shaShen->getTianYiGuiRen($dtgFemale, $v[1])
            ) {
                $result['male'][$v[1] . '天乙贵人'] = [$v[1], '天乙贵人', '寓意：催旺贵人运，遇事有人帮。'];
            }
            if ($this->shaShen->getTianChu($ytgFemale . $v[1]) || $this->shaShen->getTianChu($htgFemale . $v[1])) {
                $result['male'][$v[1] . '天厨贵人'] = [$v[1], '天厨贵人', '寓意：催旺口福，主提升家庭生活品质。'];
            }
            if ($k !== 'y' && $this->shaShen->getZaiSha($ydzFemale . $v[1])) {
                $result['male'][$v[1] . '灾煞'] = [$v[1], '灾煞', '寓意：出现障碍，不让强出头。'];
            }
            if ($this->shaShen->getTianDe($mdzFemale . $v[0])) {
                $result['male'][$v[0] . '天德贵人'] = [$v[0], '天德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDe($mdzFemale . $v[0])) {
                $result['male'][$v[0] . '月德贵人'] = [$v[0], '月德贵人', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getTianDeHe($mdzFemale . $v[0])) {
                $result['male'][$v[0] . '天德合'] = [$v[0], '天德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYueDeHe($mdzFemale . $v[0])) {
                $result['male'][$v[0] . '月德合'] = [$v[0], '月德合', '寓意：催旺贵人运，主逢凶化吉。'];
            }
            if ($this->shaShen->getYangRen($dtgFemale . $v[1])) {
                $result['male'][$v[1] . '羊刃'] = [$v[1], '羊刃', '寓意：情绪冲动，脾气大倔强。'];
            }
            if ($this->shaShen->getXianChi($ydzFemale . $v[1])) {
                $result['male'][$v[1] . '桃花'] = [$v[1], '桃花', '寓意：催旺桃花运，主异性缘旺。'];
            }
        }
        $result['male'] = array_values($result['male'] ?? []);
        return $result;
    }

    /**
     * 合婚分数
     * @param array $fenArr 分数数组
     * @return array
     */
    protected function getHeHunFen(array $fenArr): array
    {
        $total = array_sum($fenArr) + $fenArr['xyfen'] * 3;
        asort($fenArr);
        $keys = array_keys($fenArr);
        $min = $keys[0];
        $name = '';
        if ($total <= 25) {
            $list = [
                'shuxian' => '婚姻配对欠佳，占有欲强，希望掌握对方动向。但婚姻需独立空间，保持神秘和惊喜。',
                'nayin' => '婚姻配对不佳，相处或缺包容。初期新鲜，后期问题显。需磨合适应，以免影响婚姻。',
                'year' => '婚姻配对欠佳，处理问题有差异。需良好沟通，顾及对方感受，免生怀疑。',
                'day' => '婚姻配对欠佳，有感情基础但吸引力减。话题多变，忽略美好。需关注生活琐事中的幸福。',
                'shensha' => '婚姻配对欠佳，与理想有差距。婚后美感褪，缺点显。需适应彼此习惯和性格。',
                'liuqing' => '婚姻配对欠佳，面对问题难达共识。心理诉求无回应，失落积累。需提高情感灵敏度。',
                'xyfen' => '婚姻配对欠佳，面对家庭问题易分歧。双方角度和顾虑不同，需商量、理解以达成一致。',
            ];
        } elseif ($total <= 30) {
            $list = [
                'shuxian' => '婚姻配对不佳，聚少离多，沟通不足，因家庭分歧而矛盾加剧、感情疏远。多为对方考虑、平心沟通可改善。',
                'nayin' => '婚姻配对欠佳，常因家庭争执。关注点和解决方式不同导致障碍和隔阂，未及时发现问题则关系疏远。',
                'year' => '婚姻组合不太好，同甘共苦不易。遇困无法感同身受时，意见难一致。随时间推移，易出现婚姻不利现象。',
                'day' => '婚姻组合欠佳，初期美好，共同生活后分歧显。敏感问题引发争执，影响尊重和快乐。需互相包容和认可，保持平稳。',
                'shensha' => '婚姻组合不良，婚前婚后差距大导致不满。小事放大，争吵不断，感情消磨。需共同讨论未来经营，否则不了了之。',
                'liuqing' => '婚姻组合不佳，异性缘旺导致猜忌和隔阂。桃花多影响安全感。需坚守自我、与异性保持距离、给予信任和信心，提高幸福度。考验心性和爱意。',
                'xyfen' => '婚姻组合欠佳，婚后问题多、易分歧和争吵。若无妥善解决，终将导致分离。',
            ];
        } elseif ($total <= 40) {
            $list = [
                'shuxian' => '婚姻组合虽不突出，但不代表不幸福。聚少离多、异地生活可能冲淡感情、引发争吵。需减少异地时间、增强互动、保持热情。',
                'nayin' => '婚姻组合一般，但不意味结局不佳。婚后家庭问题需磨合与包容，长时间相处可化解问题、减少争吵、维系感情。',
                'year' => '婚姻组合欠佳，虽多为对方考虑，但分歧时坚持自我。此现象常见，但及时解决可避免隐患爆发。',
                'day' => '婚姻组合一般，好坏取决于对家庭生活的态度。需正确表达不满、交流解决问题，以保幸福。',
                'shensha' => '婚姻组合不太好，婚后对感情和家庭生活各有畅想，易因大事争吵。需有效沟通、确定计划以避免翻旧账。',
                'liuqing' => '婚姻组合稍差，身边异性可能影响感情。需主动与异性保持距离、给予安全感，以减少隔阂、考验忠诚。',
                'xyfen' => '婚姻组合一般，不意味完全差劲。生活常见问题易引发争吵，及时处理可避免问题累积爆发。',
            ];
        } elseif ($total <= 60) {
            $list = [
                'shuxian' => '婚姻组合一般，相处问题不大，但不保证平稳。处理问题需考虑对方感受，以避免埋下隐患。',
                'nayin' => '婚姻组合一般，婚后责任增加，可能发现更多缺点。需及时沟通、包容和指出问题，避免一味忍让导致无法忍受。',
                'year' => '婚姻一般，有深厚感情基础，会为对方考虑。但需增加沟通、避免交流差异导致误解和不幸福。',
                'day' => '婚姻组合一般，常因小事争吵，甚至放大问题。需及时应对和改善，以维护婚姻生活。',
                'shensha' => '婚姻相对一般，虽能互帮互助，但感情基础差距在遇问题时易造成看法差异，可能成为彼此心中的刺。',
                'liuqing' => '婚姻组合一般，接人待物、处理事情差异逐渐显露，可能出现大分歧。需换位思考、寻找适合双方的相处模式。',
                'xyfen' => '婚姻组合一般，稳定但新鲜感可能减弱。忙碌工作可能影响感情交流，需加强交流、共创小美好。',
            ];
        } elseif ($total <= 70) {
            $list = [
                'shuxian' => '你们的婚姻稳定且平凡，感情平稳且默契。生活中的习惯差异和困扰可通过包容和真诚沟通来解决。',
                'nayin' => '你们的婚姻平凡中见真情，已准备好共同面对生活的挑战。面对困难时需携手共进，这样才能使感情更加坚定。',
                'year' => '你们的婚姻稳定，感情基础深厚，但看待问题的角度或有不同。应开放心扉，增加交流，以避免情感上的障碍。',
                'day' => '你们的婚姻稳定，日常生活中能够互相配合和包容。应更关注伴侣和婚姻的经营，而不是在意外界的看法。',
                'shensha' => '你们的婚姻稳定，但生活习惯和消费观念或有不同。需要时间去磨合和适应，同时多关注对方的感受，保持情感交流。',
                'liuqing' => '你们的婚姻稳定，有良好的感情基础，虽然会因琐事争吵。但注重沟通、尊重和理解是维系婚姻和感情的关键。',
                'xyfen' => '你们的婚姻稳定，但随着时间的推移，双方的小缺点可能会逐渐暴露。需要耐心、包容和理解来找到舒适的相处方式。',
            ];
        } elseif ($total <= 80) {
            $list = [
                'shuxian' => '你们的婚姻组合良好，默契十足，矛盾易于解决。但为了稳固婚姻，还需共同努力协调家庭问题。',
                'nayin' => '你们的结合被外界看好，对家庭和事业都有助益。但婚后需注意处理琐事引发的矛盾，以提升生活的融洽度。',
                'year' => '你们的婚姻组合不错，感情融洽。为了长久相伴，需互相包容和沟通以克服久处后暴露的缺点。',
                'day' => '你们的婚姻组合良好，婚后虽有分歧但能随时间磨合适应。为了感情更加融洽，需多沟通交流和相互包容。',
                'shensha' => '你们的婚姻组合较佳，生活方式的差异可能带来摩擦。但相互理解和包容后能找到和谐相处的方式。',
                'liuqing' => '面对困难时你们或许有不同的处理方式，但能共同面对并互相支持。不必过于在意外界评论。',
                'xyfen' => '你们的婚姻组合较好，有感情上的默契。婚后生活可能围绕琐事展开，与预期有所不同。为了增加情调需加强沟通和制造惊喜。',
            ];
        } elseif ($total <= 90) {
            $list = [
                'shuxian' => '你们的婚姻组合优秀，从恋爱到婚姻感情深厚。面对家庭责任如子女教育、父母赡养等问题时需共同努力。',
                'nayin' => '你们婚后相处融洽即使兴趣不同也不影响感情。能接纳对方的优缺点并共同学习进步。有明确的未来规划相伴到老的可能性很大。',
                'year' => '你们的婚姻组合良好几乎没有难以解决的矛盾高度契合。小打小闹不会成为问题修成正果的希望很大。',
                'day' => '你们的婚姻组合不错即使琐事也不会影响感情。婚后感情可能趋于平淡但有落差时不放弃努力制造小惊喜可以稳定婚姻。',
                'shensha' => '你们的婚姻组合很好默契感强给予对方安全感。婚后难免有磕碰但需包容和理解以提升婚姻质量。',
                'liuqing' => '你们婚后生活融洽相处愉快。遇到看法、态度不一致时可能产生矛盾需及时沟通并有共同的目标和认同感。',
                'xyfen' => '你们婚后愿意分享未来规划和幸福生活的理解。生活方式的差异可能导致分歧但经过时间磨合和适应后感情会更加融洽幸福指数会提升。',
            ];
        } else {
            $list = [
                'shuxian' => '你们婚后处理问题时想法一致配合默契在分歧时能及时沟通。琐事成为爱情的调味品婚姻的幸福指数很高。',
                'nayin' => '你们婚后感情甜蜜是朋友眼中的完美一对。珍惜、包容、理解对方给予足够的安全感。即使偶有摩擦相伴到老的可能性也很大。',
                'year' => '你们的婚姻非常幸福认定彼此为一生伴侣经常给对方小惊喜增加仪式感。虽然生活方式有所不同但愿意为对方适应。',
                'day' => '你们在婚姻中彼此提升共同进步默契度很高。因琐事产生矛盾时能妥协、有效沟通并包容对方。',
                'shensha' => '你们从恋爱到婚姻感情一直深厚默契度满分。彼此一个眼神就能了解对方的想法很少因为沟通产生分歧非常认可对方。',
                'liuqing' => '你们婚后责任感很强能设身处地为对方着想。遇到问题时不逃避及时交流并通过行动表达关心。在意见分歧时能够理性解决。',
                'xyfen' => '你们婚后感情很好愿意倾听对方分享的事业和生活小事。虽然消费方式不同可能产生摩擦但经过磨合后能适应对方感情不会受到影响生活也会越来越好。',
            ];
        }
        if ($total < 30) {
            $total = round($total / 0.8);
        } elseif ($total < 50) {
            $total = round($total / 0.81);
        } elseif ($total < 60) {
            $total = round($total / 0.82);
        } elseif ($total < 66) {
            $total = round($total / 0.84);
        } elseif ($total < 79) {
            $total = 78;
        }
        if ($total < 29) {
            $name = '大凶';
        } elseif ($total < 38) {
            $name = '凶';
        } elseif ($total < 50) {
            $name = '小凶';
        } elseif ($total < 60) {
            $name = '平';
        } elseif ($total < 80) {
            $name = '小吉';
        } elseif ($total < 88) {
            $name = '吉';
        } else {
            $name = '大吉';
        }
        return [
            'name' => $name,
            'fen' => $total,
            'info' => $list[$min] ?? '',
        ];
    }

    /**
     * 喜用神分数
     * @return float
     */
    protected function getXiYongFen(): float
    {
        $xyM = $this->userInfo[0]['xy'];
        $xyF = $this->userInfo[1]['xy'];
        $jnWxM = array_column($this->userInfo[0]['jnwx']['num'], 'num', 'wx');
        $jnWxF = array_column($this->userInfo[1]['jnwx']['num'], 'num', 'wx');
        arsort($jnWxM);
        arsort($jnWxF);
        $jnM1 = $this->userInfo[0]['jnwx']['wx'];
        $jnF1 = $this->userInfo[1]['jnwx']['wx'];
        $wxM = array_keys($jnWxM);
        $wxF = array_keys($jnWxF);
        $wxNumM = array_values($jnWxM);
        $wxNumF = array_values($jnWxF);
        if ($wxNumM[0] >= 3 && in_array(WuXing::getWuxingGuanXi($jnM1['m'][1], $wxM[0]), ['生', '扶'])) {
            $list = ['木木' => 10, '木水' => 8, '水水' => 10, '水金' => 8, '金金' => 10, '金土' => 8, '火木' => 8, '火火' => 10, '土土' => 10, '土火' => 8];
            $str = $xyM['xy']['yong'] . $wxM[0];
            if (isset($list[$str])) {
                return $list[$str];
            }
        }
        if ($wxNumF[0] >= 3 && in_array(WuXing::getWuxingGuanXi($jnF1['m'][1], $wxF[0]), ['生', '扶'])) {
            $list = ['木木' => 10, '木水' => 8, '水水' => 10, '水金' => 8, '金金' => 10, '金土' => 8, '火木' => 8, '火火' => 10, '土土' => 10, '土火' => 8];
            $str = $xyM['xy']['yong'] . $wxM[0];
            if (isset($list[$str])) {
                return $list[$str];
            }
        }
        if (WuXing::getWuxingGuanXi($xyM['xy']['yong'], $xyF['xy']['yong']) == '生') {
            $fen = 5;
        } elseif ($xyM['shen']['yong'] == $xyF['shen']['xi']) {
            $fen = 4.5;
        } elseif ($xyM['shen']['yong'] == $xyF['shen']['yong']) {
            $fen = 4;
        } elseif ($xyM['shen']['ji'] == $xyF['shen']['yong']) {
            $fen = 3.5;
        } elseif (WuXing::getWuxingGuanXi($xyM['xy']['ji'], $xyF['xy']['yong']) == '克') {
            $fen = 3;
        } elseif (in_array(WuXing::getWuxingGuanXi($xyM['xy']['ji'], $xyF['xy']['yong']), ['生', '扶'])) {
            $fen = 2;
        } else {
            $fen = 3.5;
        }
        $list1 = [
            '泄泄' => 5, '扶泄' => 4.5, '生泄' => 4, '克泄' => 2.5, '泄扶' => 3.5, '扶扶' => 4, '生扶' => 3.5, '克扶' => 2.5,
            '泄生' => 3, '扶生' => 3.5, '生生' => 3, '克生' => 2.5, '泄克' => 3.5, '扶克' => 2.5, '生克' => 2, '克克' => 2,
        ];
        $str = WuXing::getWuxingGuanXi($xyM['xy']['yong'], $xyF['xy']['yong']) . WuXing::getWuxingGuanXi($xyM['xy']['xi'], $xyF['xy']['xi']);
        $fen1 = $list1[$str] ?? 0;
        return $fen + $fen1;
    }

    /**
     * 六亲合婚
     * @return int
     */
    protected function liuQingFen(): int
    {
        $godThsM = $this->userInfo[0]['god_ths'];
        $godThsF = $this->userInfo[1]['god_ths'];
        $godTM = $this->userInfo[0]['god'];
        $godHideM = $this->userInfo[0]['_god'];
        $godTF = $this->userInfo[1]['god'];
        $godHideF = $this->userInfo[1]['_god'];
        $godHM = array_merge($godHideM['year']['god'], $godHideM['month']['god'], $godHideM['day']['god'], $godHideM['hour']['god']);
        $godHF = array_merge($godHideF['year']['god'], $godHideF['month']['god'], $godHideF['day']['god'], $godHideF['hour']['god']);
        if (($godThsM['bj'] + $godThsM['jc']) >= 3 && ($godThsM['zg'] + $godThsM['qs']) <= 1) {
            $fen = -9;
        } elseif (($godThsM['bj'] + $godThsM['jc']) >= 3 && ($godThsM['zg'] + $godThsM['qs']) >= 3) {
            $fen = -5;
        } elseif (($godThsM['ss'] + $godThsM['sg']) >= 3 && ($godThsM['zy'] + $godThsM['py']) <= 1) {
            $fen = 0;
        } elseif (($godThsM['ss'] + $godThsM['sg']) >= 3 && ($godThsM['zy'] + $godThsM['py']) >= 3) {
            $fen = -2;
        } elseif (in_array('偏财', $godTM) && in_array('偏财', $godHM)) {
            $fen = -4;
        } else {
            $fen = -2;
        }
        if (($godThsF['ss'] + $godThsF['sg']) >= 3 && ($godThsF['zy'] + $godThsF['py']) <= 1) {
            $fen1 = -9;
        } elseif (($godThsF['ss'] + $godThsF['sg']) >= 3 && ($godThsF['zy'] + $godThsF['py']) >= 3) {
            $fen1 = -5;
        } elseif (($godThsF['zc'] + $godThsF['pc']) >= 3 && ($godThsF['bj'] + $godThsF['jc']) <= 1) {
            $fen1 = 0;
        } elseif (($godThsF['zc'] + $godThsF['pc']) >= 3 && ($godThsF['bj'] + $godThsF['jc']) >= 3) {
            $fen1 = 0;
        } elseif (array_intersect(['正官', '七杀'], $godTF) && array_intersect(['正官', '七杀'], $godHF)) {
            $fen1 = -4;
        } else {
            $fen1 = -2;
        }
        $fen += $fen1;
        $fen = 10 + $fen;
        if ($fen < 1) {
            $fen = 1;
        }
        return $fen;
    }

    /**
     * 神煞合分
     * @return int
     */
    protected function getShenShaFen(): int
    {
        $jiNianM = $this->userInfo[0]['base']['jinian'];
        $jiNianF = $this->userInfo[1]['base']['jinian'];
        $ydzM = $jiNianM['y'][1];
        $ydzF = $jiNianF['y'][1];
        $mdzM = $jiNianM['m'][1];
        $mdzF = $jiNianF['m'][1];
        $dtgM = $jiNianM['d'][0];
        $dgzM = implode('', $jiNianM['d']);
        $dgzF = implode('', $jiNianF['d']);
        $hgzM = implode('', $jiNianM['h']);
        $ssM = $this->shaShen->detail($jiNianM, 0);
        $ssF = $this->shaShen->detail($jiNianF, 1);
        $ssM1 = array_merge($ssM['y'], $ssM['m'], $ssM['d'], $ssM['h']);
        $ssF1 = array_merge($ssF['y'], $ssF['m'], $ssF['d'], $ssF['h']);
        $fen = 10;
        if (in_array('孤辰', $ssM1)) {
            $fen--;
        }
        if (in_array('寡宿', $ssF1)) {
            $fen--;
        }
        if (in_array('孤鸾煞', $ssM1)) {
            $fen = $fen - 2;
        }
        if (in_array('孤鸾煞', $ssF1)) {
            $fen = $fen - 2;
        }
        if ($this->checkBaBiSha($ydzM . $mdzM)) {
            $fen--;
        }
        if ($this->checkBaBiSha($ydzF . $mdzF)) {
            $fen = $fen - 5;
        }
        $naYinF = $this->userInfo[1]['na_yin'];
        $ynyFWx = mb_substr($naYinF['year'], 2, 1);
        if ($this->checkWangMenGua($ynyFWx . $mdzF)) {
            $fen = $fen - 3;
        }
        if ($this->checkGuShuiPo($ydzM . $mdzM, 0)) {
            $fen = $fen - 2;
        }
        if ($this->checkGuShuiPo($ydzF . $mdzF, 1)) {
            $fen = $fen - 2;
        }
        if ($this->checkTieSaoZhou($ydzM . $mdzM, 0)) {
            $fen--;
        }
        if ($this->checkTieSaoZhou($ydzF . $mdzF, 1)) {
            $fen--;
        }
        if ($this->checkTianSaoXin($ydzM, $dgzM) || $this->checkTianSaoXin($ydzM, $hgzM)) {
            $fen = $fen - 3;
        }
        $wxAttr = WuXing::GZ_TO_WX;
        $dtgWxF = $wxAttr[$dtgM];
        if ($this->checkDiSaoXin($dtgWxF, $mdzF)) {
            $fen = $fen - 3;
        }
        if (in_array('阴差阳错', $ssM1)) {
            $fen--;
        }
        if (in_array('阴差阳错', $ssF1)) {
            $fen--;
        }
        if (in_array('红艳', $ssM1)) {
            $fen--;
        }
        if (in_array('红艳', $ssF1)) {
            $fen--;
        }
        if (in_array('羊刃', $ssM1)) {
            $fen -= 4;
        }
        if (in_array('羊刃', $ssF1)) {
            $fen -= 4;
        }
        if (in_array('元辰', $ssM1)) {
            $fen--;
        }
        if (in_array('元辰', $ssF1)) {
            $fen--;
        }
        if (in_array('咸池', $ssM1)) {
            $fen--;
        }
        if (in_array('咸池', $ssF1)) {
            $fen -= 3;
        }
        if ($fen < 1) {
            $fen = 1;
        }
        return $fen;
    }

    /**
     * 地扫星
     * @param string $dwx 日主五行
     * @param string $mdz 月支
     * @return bool
     */
    protected function checkDiSaoXin(string $dwx, string $mdz): bool
    {
        $list = [
            '金' => ['午', '未', '申'],
            '木' => ['卯', '辰', '巳'],
            '水' => ['酉', '戌', '亥'],
            '火' => ['子', '丑', '寅'],
            '土' => ['卯', '辰', '巳'],
        ];
        return in_array($mdz, $list[$dwx]);
    }

    /**
     * 天扫星
     * @param string $str 年干
     * @param string $gz 日柱或时柱
     * @return bool
     */
    protected function checkTianSaoXin(string $str, string $gz): bool
    {
        $list = [
            '甲癸未', '乙壬午', '丙辛巳', '丁庚辰', '戊己卯',
            '己戊寅', '庚丁丑', '辛丙子', '壬乙亥', '癸甲戌',
        ];
        return in_array($str . $gz, $list);
    }

    /**
     * 铁扫帚（需要分男女）
     * @param string $str 年支+月支
     * @param int $sex 性别
     * @return bool
     */
    protected function checkTieSaoZhou(string $str, int $sex): bool
    {
        $list = [
            [
                '子寅', '丑未', '寅巳', '卯卯', '辰寅', '巳未',
                '午巳', '未卯', '申寅', '酉未', '戌巳', '亥卯',
            ],
            [
                '子未', '丑巳', '寅辰', '卯寅', '辰未', '巳巳',
                '午辰', '未寅', '申未', '酉巳', '戌辰', '亥寅',
                '子丑', '丑戌', '寅申', '卯酉', '辰丑', '巳戌',
                '午申', '未酉', '申丑', '酉戌', '戌申', '亥酉',
            ],
        ];
        return in_array($str, $list[$sex]);
    }

    /**
     * 骨髓破
     * @param string $str 年支+月支
     * @param int $sex 性别
     * @return bool
     */
    protected function checkGuShuiPo(string $str, int $sex): bool
    {
        $list = [
            ['子卯', '丑辰', '寅亥', '卯午', '辰丑', '巳寅', '午酉', '未戌', '申巳', '酉子', '戌未', '亥申'],
            ['子未', '丑巳', '寅辰', '卯寅', '辰未', '巳巳', '午辰', '未寅', '申未', '酉巳', '戌辰', '亥寅'],
        ];
        return in_array($str, $list[$sex]);
    }

    /**
     * 望门寡
     * @param string $str 年纳音五行+月支
     * @return bool
     */
    protected function checkWangMenGua(string $str): bool
    {
        $list = ['金申', '木巳', '水亥', '火寅', '土巳'];
        return in_array($str, $list);
    }

    /**
     * 八败煞
     * @param string $str 年支+月支
     * @return bool
     */
    protected function checkBaBiSha(string $str): bool
    {
        $list = [
            '子未', '丑戌', '寅丑', '卯丑', '辰未', '巳未', '午丑', '未辰', '申戌', '酉戌', '戌辰', '亥辰',
        ];
        return in_array($str, $list);
    }

    /**
     * 纳音合婚分数
     * @return float
     */
    protected function getNaYinFen(): float
    {
        $list = [
            '桑柘木剑锋金' => 4, '海中金覆灯火' => 7.5, '天上火涧下水' => 8.5, '泉中水大驿土' => 4.5, '大林木金箔金' => 7.5, '大溪水沙中土' => 8.5, '海中金霹雳火' => 4.5,
            '白腊金炉中火' => 4, '山头火天河水' => 4.5, '大林木钗钏金' => 7.5, '剑锋金桑柘木' => 4, '覆灯火海中金' => 7.5, '涧下水天上火' => 8.5, '大驿土泉中水' => 4.5,
            '金箔金大林木' => 7.5, '沙中土大溪水' => 8.5, '霹雳火海中金' => 4.5, '炉中火白腊金' => 4, '天河水山头火' => 4.5, '钗钏金大林木' => 7.5,
        ];
        $list1 = [
            '火火' => 5, '火金' => 4.5, '火木' => 9, '火水' => 7.5, '火土' => 7.5, '金火' => 6, '金金' => 7, '金木' => 7, '金水' => 10, '金土' => 9,
            '木火' => 8.5, '木金' => 7, '木木' => 5.5, '木水' => 9, '木土' => 5.5, '水火' => 4.5, '水金' => 9, '水木' => 9, '水水' => 7, '水土' => 6,
            '土火' => 9, '土金' => 8.5, '土木' => 4, '土水' => 5, '土土' => 10,
        ];
        $nyM = $this->userInfo[0]['na_yin']['year'];
        $nyF = $this->userInfo[1]['na_yin']['year'];
        if (isset($list[$nyM . $nyF])) {
            $fen = $list[$nyM . $nyF];
        } else {
            $wx = mb_substr($nyM, 2, 1);
            $wx1 = mb_substr($nyF, 2, 1);
            $fen = $list1[$wx . $wx1] ?? 0;
        }
        return $fen;
    }

    /**
     * 属相合婚
     * @return float
     */
    protected function getShuXiangFen(): float
    {
        $list = [
            [6.5, 10, 6.5, 3, 7.5, 6.5, 1, 0, 8, 5, 6.5, 6.5],
            [10, 6.5, 6.5, 6.5, 5, 7, 0, 1, 6.5, 7.5, 3, 6.5],
            [6.5, 6.5, 6.5, 6.5, 6.5, 0, 8, 6.5, 1, 6.5, 7, 10],
            [3, 6.5, 6.5, 6.5, 0, 6.5, 5, 7, 6.5, 1, 10, 8],
            [7.5, 5, 6.5, 0, 3, 6.5, 6.5, 6.5, 7, 10, 1, 6.5],
            [6.5, 7, 0, 6.5, 6.5, 6.5, 6.5, 6.5, 7, 8, 6.5, 1],
            [1, 0, 8, 5, 6.5, 6.5, 3, 10, 6.5, 6.5, 7.5, 6.5],
            [0, 1, 6.5, 7.5, 6.5, 6.5, 10, 6.5, 6.5, 6.5, 5, 7],
            [8, 6.5, 1, 6.5, 7, 10, 6.5, 6.5, 6.5, 6.5, 6.5, 0],
            [5, 7.5, 6.5, 1, 10, 8, 6.5, 6.5, 6.5, 3, 0, 6.5],
            [6.5, 6.5, 7, 10, 1, 6.5, 7.5, 3, 6.5, 0, 6.5, 6.5],
            [6.5, 6.5, 10, 7.5, 6.5, 1, 6.5, 7, 0, 6.5, 6.5, 3],
            [6.5, 6.5, 10, 7.5, 6.5, 1, 6.5, 7, 0, 6.5, 6.5, 3],
        ];
        $ydzM = $this->userInfo[0]['base']['jinian']['y'][1];
        $ydzF = $this->userInfo[1]['base']['jinian']['y'][1];
        $dzArr = Calendar::DI_ZHI;
        $ydzMi = (int)array_search($ydzM, $dzArr);
        $ydzFi = (int)array_search($ydzF, $dzArr);
        return $list[$ydzMi][$ydzFi] ?? 0;
    }

    /**
     * 日柱合分
     * @return float
     */
    protected function getFenByDay(): float
    {
        $infoM = $this->userInfo[0];
        $infoF = $this->userInfo[1];
        $jiNianM = $infoM['base']['jinian'];
        $jiNianF = $infoF['base']['jinian'];
        $xyM = $infoM['xy'];
        $xyF = $infoF['xy'];
        $gxM = $infoM['gx'];
        $gxF = $infoF['gx'];
        $yongXiM = [$xyM['xy']['yong'], $xyM['xy']['xi']];
        $yongXiF = [$xyF['xy']['yong'], $xyF['xy']['xi']];
        $wxAttr = WuXing::GZ_TO_WX;
        $dtgM = $jiNianM['d'][0];
        $dtgF = $jiNianF['d'][0];
        $ddzM = $jiNianM['d'][1];
        $ddzF = $jiNianF['d'][1];
        $ddzMWx = $wxAttr[$ddzM];
        $ddzFWx = $wxAttr[$ddzF];
        $listTg = [
            '甲甲' => 3.5, '甲乙' => 3.5, '甲丙' => 5, '甲丁' => 4.5, '甲戊' => 1, '甲己' => 5, '甲庚' => 0,
            '甲辛' => 3.5, '甲壬' => 5, '甲癸' => 4.5, '乙甲' => 3.5, '乙乙' => 3.5, '乙丙' => 4.5, '乙丁' => 5,
            '乙戊' => 3.5, '乙己' => 1, '乙庚' => 5, '乙辛' => 0, '乙壬' => 4.5, '乙癸' => 5, '丙甲' => 5,
            '丙乙' => 4.5, '丙丙' => 3.5, '丙丁' => 3.5, '丙戊' => 5, '丙己' => 4.5, '丙庚' => 1, '丙辛' => 5,
            '丙壬' => 0, '丙癸' => 3.5, '丁甲' => 4.5, '丁乙' => 5, '丁丙' => 3.5, '丁丁' => 3.5, '丁戊' => 4.5,
            '丁己' => 5, '丁庚' => 3.5, '丁辛' => 1, '丁壬' => 5, '丁癸' => 0, '戊甲' => 1, '戊乙' => 3.5,
            '戊丙' => 5, '戊丁' => 4.5, '戊戊' => 3.5, '戊己' => 3.5, '戊庚' => 5, '戊辛' => 4.5, '戊壬' => 1,
            '戊癸' => 5, '己甲' => 5, '己乙' => 1, '己丙' => 4.5, '己丁' => 5, '己戊' => 3.5, '己己' => 3.5,
            '己庚' => 4.5, '己辛' => 5, '己壬' => 3.5, '己癸' => 1, '庚甲' => 0, '庚乙' => 5, '庚丙' => 1,
            '庚丁' => 3.5, '庚戊' => 5, '庚己' => 4.5, '庚庚' => 3.5, '庚辛' => 3.5, '庚壬' => 5, '庚癸' => 4.5,
            '辛甲' => 3.5, '辛乙' => 0, '辛丙' => 5, '辛丁' => 1, '辛戊' => 4.5, '辛己' => 5, '辛庚' => 3.5,
            '辛辛' => 3.5, '辛壬' => 4.5, '辛癸' => 5, '壬甲' => 5, '壬乙' => 4.5, '壬丙' => 0, '壬丁' => 5,
            '壬戊' => 1, '壬己' => 3.5, '壬庚' => 5, '壬辛' => 4.5, '壬壬' => 3.5, '壬癸' => 3.5, '癸甲' => 4.5,
            '癸乙' => 5, '癸丙' => 3.5, '癸丁' => 0, '癸戊' => 5, '癸己' => 1, '癸庚' => 4.5, '癸辛' => 5,
            '癸壬' => 3.5, '癸癸' => 3.5,
        ];
        $fen = $listTg[$dtgM . $dtgF] ?? 0;
        // 相合 1 相冲 2 相破 3 相刑 4 相害 5 生地 6 墓地 7 其它 8
        // 合为喜用 1 合为忌 2  男喜用 女忌 3   男忌 女喜用 4
        // 他支冲克  无 1 都有 2  男有 女无 3  男无女有 4
        $listDz = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 3, 124 => 3, 131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 1, 23 => 0.5, 24 => 0.5, 31 => 1, 32 => 2, 33 => 1.5, 34 => 1.5, 41 => 0.5, 42 => 1.5, 43 => 1, 44 => 1, 51 => 0, 52 => 0.5, 53 => 0.25, 54 => 0.25,
            611 => 5, 612 => 2, 613 => 3, 614 => 3, 621 => 0, 622 => 4, 623 => 3, 624 => 3, 631 => 3, 632 => 3, 633 => 1, 634 => 4, 641 => 3, 642 => 3, 643 => 4, 644 => 1,
            711 => 5, 712 => 2, 713 => 3, 714 => 3, 721 => 0, 722 => 4, 723 => 3, 724 => 3, 731 => 3, 732 => 3, 733 => 1, 734 => 4, 741 => 3, 742 => 3, 743 => 4, 744 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        $he = BaziCommon::getHehua($ddzM . $ddzFWx);
        $ckM = array_intersect(['冲', '被克'], $gxM['d']['d']) ? 1 : 2;// 1有 2 无
        $ckF = array_intersect(['冲', '被克'], $gxF['d']['d']) ? 1 : 2;
        $list11 = [22 => 1, 11 => 2, 12 => 3, 21 => 4];
        $num3 = $list11[$ckM . $ckF];
        // 1 喜用 2 忌闲仇
        $b1 = in_array($ddzMWx, $yongXiF) ? 1 : 2;
        $b2 = in_array($ddzFWx, $yongXiM) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b1 . $b2];
        $sanShen = $this->getSanHeSm($ddzM . $ddzF);
        $sanDi = $this->getSanHeSm($ddzM . $ddzF, 1);
        $str = 0;
        if ($he) {
            $num2 = $this->getGx3($he);
            $str = (int)("1{$num2}{$num3}");
        } elseif (BaziCommon::getXianChong($ddzM . $ddzF)) {
            $str = (int)("2{$num4}");
        } elseif (BaziCommon::getXianPo($ddzM . $ddzF)) {
            $str = (int)("3{$num4}");
        } elseif (BaziCommon::getXianXin($ddzM . $ddzF)) {
            $str = (int)("4{$num4}");
        } elseif (BaziCommon::getXianHai($ddzM . $ddzF)) {
            $str = (int)("5{$num4}");
        } elseif ($sanShen) {
            $num2 = $this->getGx3($sanShen);
            $str = (int)("6{$num2}{$num3}");
        } elseif ($sanDi) {
            $num2 = $this->getGx3($sanDi);
            $str = (int)("7{$num2}{$num3}");
        } else {
            $str = (int)("8{$num4}");
        }
        $fen1 = $listDz[$str] ?? 0;
        $fen += $fen1;
        return $fen;
    }

    /**
     * 年柱合分
     * @return float
     */
    protected function getFenByYear(): float
    {
        $infoM = $this->userInfo[0];
        $infoF = $this->userInfo[1];
        $jiNianM = $infoM['base']['jinian'];
        $jiNianF = $infoF['base']['jinian'];
        $xyM = $infoM['xy'];
        $xyF = $infoF['xy'];
        $gxM = $infoM['gx'];
        $gxF = $infoF['gx'];
        $yongXiM = [$xyM['xy']['yong'], $xyM['xy']['xi']];
        $yongXiF = [$xyF['xy']['yong'], $xyF['xy']['xi']];
        $wxAttr = WuXing::GZ_TO_WX;
        $ytgM = $jiNianM['y'][0];
        $ytgMWx = $wxAttr[$ytgM];
        $ytgF = $jiNianF['y'][0];
        $ytgFWx = $wxAttr[$ytgF];
        $he = BaziCommon::getHehua($ytgM . $ytgF);
        $chong = BaziCommon::getXianChong($ytgM . $ytgF);
        $ke = BaziCommon::getXianKe($ytgM . $ytgF);
        $shen = BaziCommon::getWuxingGuanXi($ytgMWx, $ytgFWx);
        $shen1 = BaziCommon::getWuxingGuanXi($ytgFWx, $ytgMWx);
        $ckM = array_intersect(['冲', '被克'], $gxM['y']['t']) ? 1 : 0;
        $ckF = array_intersect(['冲', '被克'], $gxF['y']['t']) ? 1 : 0;
        if ($ckM && $ckF) {
            $num3 = 2;
        } elseif (!$ckM && !$ckF) {
            $num3 = 1;
        } elseif ($ckM && !$ckF) {
            $num3 = 3;
        } else {
            $num3 = 4;
        }
        // 天干  相合 1 相冲 2 相克 3 相生 4 无关系 5
        // 天干 为喜用  1 都是忌 2 男喜用 女忌 3 男忌 女喜用 4
        // 不被他柱 冲克 1 都被冲克 2 男冲克 女无 3 男无 女冲克 4
        $listTg = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 2, 124 => 2, 131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 2, 23 => 1, 24 => 1, 31 => 0, 32 => 2, 33 => 1, 34 => 1,
            411 => 5, 412 => 2, 413 => 3, 414 => 3, 421 => 0, 422 => 4, 423 => 3, 424 => 3, 431 => 3, 432 => 3, 433 => 1, 434 => 4, 441 => 3, 442 => 3, 443 => 4, 444 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        // 地支  相合 1 相冲 2 相破 3 相刑 4 相害 5 生地 6 墓地 7 其它 8
        $listDz = [
            111 => 5, 112 => 2, 113 => 3, 114 => 3, 121 => 0, 122 => 4, 123 => 3, 124 => 3, 131 => 3, 132 => 3, 133 => 1, 134 => 4, 141 => 3, 142 => 3, 143 => 4, 144 => 1,
            21 => 0, 22 => 1, 23 => 0.5, 24 => 0.5, 31 => 1, 32 => 2, 33 => 1.5, 34 => 1.5, 41 => 0.5, 42 => 1.5, 43 => 1, 44 => 1, 51 => 0, 52 => 0.5, 53 => 0.25, 54 => 0.25,
            611 => 5, 612 => 2, 613 => 3, 614 => 3, 621 => 0, 622 => 4, 623 => 3, 624 => 3, 631 => 3, 632 => 3, 633 => 1, 634 => 4, 641 => 3, 642 => 3, 643 => 4, 644 => 1,
            711 => 5, 712 => 2, 713 => 3, 714 => 3, 721 => 0, 722 => 4, 723 => 3, 724 => 3, 731 => 3, 732 => 3, 733 => 1, 734 => 4, 741 => 3, 742 => 3, 743 => 4, 744 => 1,
            81 => 3, 82 => 2, 83 => 2.5, 84 => 2.5,
        ];
        $b11 = in_array($ytgMWx, $yongXiF) ? 1 : 2;
        $b12 = in_array($ytgFWx, $yongXiM) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b11 . $b12];
        if ($he) {
            $num2 = $this->getGx3($he);
            $str = (int)("1{$num2}{$num3}");
        } elseif ($chong) {
            $str = (int)("2{$num4}");
        } elseif ($ke) {
            $str = (int)("3{$num4}");
        } elseif ($shen == '生' || $shen1 == '生') {
            $shenTg = $shen ? $ytgFWx : $ytgMWx;
            $num2 = $this->getGx3($shenTg);
            $str = (int)("4{$num2}{$num3}");
        } else {
            $str = (int)("8{$num4}");
        }
        $fen = $listTg[$str] ?? 3;
        $ydzM = $jiNianM['y'][1];
        $ydzF = $jiNianF['y'][1];
        $ydzMWx = $wxAttr[$ydzM];
        $ydzFWx = $wxAttr[$ydzF];
        $ckM = array_intersect(['冲', '被克'], $gxM['y']['d']) ? 1 : 0;
        $ckF = array_intersect(['冲', '被克'], $gxF['y']['d']) ? 1 : 0;
        if ($ckM && $ckF) {
            $num3 = 2;
        } elseif (!$ckM && !$ckF) {
            $num3 = 1;
        } elseif ($ckM && !$ckF) {
            $num3 = 3;
        } else {
            $num3 = 4;
        }
        $heDz = BaziCommon::getHehua($ydzM . $ydzF);
        $str2 = 0;
        $b11 = in_array($ydzMWx, $yongXiF) ? 1 : 2;
        $b12 = in_array($ydzFWx, $yongXiM) ? 1 : 2;
        $list10 = [11 => 1, 22 => 2, 12 => 3, 21 => 4];
        $num4 = $list10[$b11 . $b12];
        $sanShen = $this->getSanHeSm($ydzMWx . $ydzFWx);
        $sanDi = $this->getSanHeSm($ydzMWx . $ydzFWx, 1);
        if ($heDz) {
            $num2 = $this->getGx3($heDz);
            $str2 = (int)("1{$num2}{$num3}");
        } elseif (BaziCommon::getXianChong($ydzM . $ydzF)) {
            $str2 = (int)("2{$num4}");
        } elseif (BaziCommon::getXianPo($ydzM . $ydzF) || BaziCommon::getXianPo($ydzF . $ydzM)) {
            $str2 = (int)("3{$num4}");
        } elseif (BaziCommon::getXianXin($ydzM . $ydzF)) {
            $str2 = (int)("4{$num4}");
        } elseif (BaziCommon::getXianHai($ydzM . $ydzF)) {
            $str2 = (int)("5{$num4}");
        } elseif ($sanShen) {
            $num2 = $this->getGx3($sanShen);
            $str2 = (int)("6{$num2}{$num3}");
        } elseif ($sanDi) {
            $num2 = $this->getGx3($sanDi);
            $str2 = (int)("7{$num2}{$num3}");
        } else {
            $str2 = (int)("8{$num4}");
        }
        $fen1 = $listDz[$str2] ?? 0;
        $fen += $fen1;
        return $fen;
    }

    /**
     * 生墓地半三合
     * @param string $str 地支+地支
     * @param int $type 0 生地 1 墓地
     * @return string
     */
    protected function getSanHeSm(string $str, int $type = 0): string
    {
        $str = Utils::sortGz($str);
        $list = [
            // 生地
            ['子申' => '水', '卯亥' => '木', '寅午' => '火', '巳酉' => '金'],
            // 墓地
            ['子辰' => '水', '卯未' => '木', '午戌' => '火', '丑酉' => '金'],
        ];
        return $list[$type][$str] ?? '';
    }

    /**
     * 五行和喜用关系
     * @param string $wx
     * @return int
     */
    protected function getGx3(string $wx): int
    {
        $infoM = $this->userInfo[0];
        $infoF = $this->userInfo[1];
        $xyM = $infoM['xy'];
        $xyF = $infoF['xy'];
        $yongXiM = [$xyM['xy']['yong'], $xyM['xy']['xi']];
        $yongXiF = [$xyF['xy']['yong'], $xyF['xy']['xi']];
        // 合为喜用 1 合为忌 2  男喜用 女忌 3   男忌 女喜用 4
        $b1 = in_array($wx, $yongXiF);
        $b2 = in_array($wx, $yongXiM);
        $res = 0;
        if ($b1 && $b2) {
            $res = 1;
        } elseif (!$b1 && !$b2) {
            $res = 2;
        } elseif ($b1 && !$b2) {
            $res = 3;
        } elseif ($b2 && !$b1) {
            $res = 4;
        }
        return $res;
    }

    /**
     * 旺夫旺妻
     * @param int $sex
     * @return string
     * @throws Exception
     */
    protected function getWangFuQi(int $sex): string
    {
        $info = $this->userInfo[$sex];
        $xy = $info['xy'];
        $wangDu = $info['wangdu'];
        $jiNian = $info['base']['jinian'];
        $jnWx = $info['jnwx'];
        $dzArr = array_column($jiNian, 1);
        $dgz = implode('', $jiNian['d']);
        $mdz = $jiNian['m'][1];
        $hdz = $jiNian['h'][1];
        $ddz = $jiNian['d'][1];
        $shenShaRes = $this->shaShen->detail($jiNian, $sex);
        $godTzs = $info['god_tzs'];
        $godThs = $info['god_ths'];
        $godT = $info['god'];
        $godZ = $info['godz'];
        $godHide = $info['_god'];
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $result = [];
        if ($sex) {
            // 身旺格，日支为正官或七杀为喜用神
            if ($wangDu == '身旺格') {
                if (in_array($godZ['day'], ['正官', '七杀']) && in_array('官杀', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                    $result[] = '身旺，日坐官星为喜用代表着旺夫运强，这样命格的女性，事业运都会较为不错，且事业能给自己带来不少的财富，丈夫也会因此得到自身的资助。';
                }
            }
            // 日支为正财或偏财，且为喜用神
            if (in_array($godZ['day'], ['正财', '偏财']) && in_array('才财', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $result[] = '日坐官星为喜用神意味着旺夫运强，在结婚后自身会得到丈夫的支持和帮助，使得事业和财运都蒸蒸日上。这类命格的女性，在婚后通常都会支持丈夫，帮助丈夫的事业，助旺丈夫的运势。';
            }
            // 日柱坐下的神煞为天乙贵人
            if (in_array('天乙贵人', $shenShaRes['d'])) {
                $result[] = '日坐天乙贵人意味着旺夫运强，这类命格的女性结婚后善于管理家庭，愿意将所有的心思都放在家庭上，并且在各方面都能够为丈夫排忧解难，家庭生活会比较和谐美满。';
            }
            // 日坐偏财，且正官或偏官为用神
            if ($godZ['day'] == '偏财' && $xy['shen']['yong'] = '官杀') {
                $result[] = '命理学认为，如果日主偏强、财官为用，那么这类女性通常比较自信独立，积极乐观，能够给丈夫带来积极的帮助和影响。她们善于打理家庭中的琐事，愿意将心思放在家庭上，从而助旺丈夫的运势。';
            }
            if ($godZ['day'] == '正官') {
                $result[] = '日坐正官的女性有着较强的责任心，善于处理家庭事务，愿意为自己的丈夫分忧解难。这种性格特质能够帮助她们在事业上取得成功，让丈夫没有后顾之忧。';
            } elseif ($godZ['day'] == '食神') {
                $result[] = '日坐食神的女性一生财运旺盛，物质生活比较富足。结婚后，她们也能够给丈夫带来财富和运气，助旺丈夫的运势。';
            }
            // 只有一个正官或一个七杀（看藏干）
            if (($godThs['zg'] + $godThs['qs']) == 1) {
                $result[] = '夫星纯正不杂意味着女性的性格温柔贤惠，对待家庭富有责任心，追求幸福和睦的家庭。她们事事都与丈夫同心同力，在各个方面帮助丈夫。';
            }
            // 天干同时有正财，正官，正印
            $arr1 = array_intersect(['正财', '正官', '正印'], $godT);
            if (count($arr1) == 3) {
                $result[] = '财官印俱全的女性比较温柔亲切，善解人意，善于沟通。她们会让丈夫在日常的生活中感觉温暖和舒适。此外，这类女性对待丈夫和家庭都富有责任心，在各个方面都能够对丈夫有所帮助。';
            }
            // 天干正官或七杀对应的五行被月支生扶
            $wxM1 = $jnWx['wx']['m'][1];
            foreach ($godT as $k => $v) {
                if (in_array($v, ['正官', '七杀'])) {
                    $strK = substr($k, 0, 1);
                    $wx = $jnWx['wx'][$strK][0];
                    if (in_array(BaziCommon::getWuxingGuanXi($wxM1, $wx), ['生', '扶'])) {
                        $result[] = '官星得令的女性心地善良，诚实守信，人缘也较为不错。在结婚后，也都以家庭为重，愿意将心思花在家庭上面，对家人和自己负责。这样为人处事的风格也会给丈夫带来积极影响。';
                        break;
                    }
                }
            }
            // 天干有财星，官星以及印星
            if (array_intersect(['正财', '偏财'], $godT) && array_intersect(['正官', '七杀'], $godT) && array_intersect(['正印', '偏印'], $godT)) {
                $result[] = '八字财、官、印俱现意味着旺夫运强，这类命格的女性通常温柔贤惠、平易近人、富有责任心。在结婚后，她们愿意将自己的重心放在家庭上，在各个方面都能够对丈夫有所帮助。';
            }
            // 正官数量大于伤官数量
            if ($godTzs['zg'] > $godTzs['sg']) {
                $result[] = '官强伤弱意味着旺夫运好，这类命格的女性心思细腻，性格比较独立，不依赖他人，为人处事都有自己的主见。在结婚后，能助旺自己的家庭，对丈夫的事业和财运都有所帮助。';
            }
            // 调用格局判断，身旺且原局无正官和七杀
            if ($wangDu == '身旺格' && ($godTzs['zg'] + $godTzs['qs']) == 0) {
                $result[] = '身旺无官杀意味着旺夫运一般，这类命格的女性对婚后生活的要求较高，结婚后可能会和丈夫因为意见不合而发生争吵。婚姻生活中，应多注意体贴对方，互相理解才能够保持幸福。';
            }
            // 地支包含辰戌丑未
            $arr1 = array_intersect(['辰', '戌', '丑', '未'], $dzArr);
            if (count($arr1) == 4) {
                $result[] = '辰戌丑未全意味着旺夫运一般，这类命格的人外貌出众，性格随和，交际广泛，有不少的异性朋友，所以在结婚后会让丈夫有危机感，长此以往就会导致矛盾的发生。建议在结婚后要与异性保持一定的距离，让伴侣时刻感受到自己的爱。';
            }
            // 原局伤官数量大于等于三
            if ($godTzs['sg'] >= 3) {
                $result[] = '八字伤官旺意味着旺夫运一般，这类命格的女性比较自我，在感情中经常会忽略对方的想法，过于固执己见则会导致双发发生矛盾。建议在婚姻生活中，要时刻关注对方的需求，遇到问题学会换位思考，这样才能更加和睦。';
            }
            // 日支和时支存在相刑
            if (BaziCommon::getXianXin($ddz . $hdz)) {
                $result[] = '日时支相刑意味着旺夫运不太理想，这类命格的女性追求完美的婚姻生活，结婚后可能因为一些方面达不到自己的要求，导致和丈夫发生矛盾。建议在婚姻生活里，多体谅对方，脚踏实地的去追求幸福生活。';
            }
            // 地支同时存在卯酉
            if (in_array('卯', $dzArr) && in_array('酉', $dzArr)) {
                $result[] = '这类命格的女性，旺夫运不太理想的女性，婚后可能过于依赖对方，对不满的事情容易责备对方，引发夫妻矛盾。互相理解和知足能让婚姻更幸福。';
            }
            // 日柱为甲寅  日柱为戊申  日柱为甲辰或甲戌
            switch ($dgz) {
                case '甲寅':
                    $result[] = '日柱为甲寅的女性旺夫运不佳，婚后可能因固执己见而与丈夫争吵、冷战。学会在婚姻中示弱，换位思考能增进甜蜜和幸福。';
                    break;
                case '戊申':
                    $result[] = '日柱为戊申的女性旺夫运一般，对家庭生活期望高，可能因丈夫未达期望而产生矛盾。多站在对方角度思考能让婚姻更美满。';
                    break;
                case '甲辰':
                case '甲戌':
                    $result[] = '这类命格的女性，旺夫运普通，对婚姻有高要求，容易钻牛角尖，影响夫妻和睦。婚后要知足，多为对方考虑，换位思考能让婚姻更幸福。';
                    break;
            }
            // 用神为印枭，喜神为官杀
            if ($xy['shen']['yong'] == '印枭' && $xy['shen']['xi'] == '官杀') {
                $result[] = '印星为用神，官星为喜神的女性旺夫运强，温柔体贴，善于持家，婚后以家庭为重，是丈夫的得力助手。';
            }
            // 伤官数量大于等于4(看藏干)
            if ($godThs['sg'] >= 4) {
                $result[] = '伤官重重的女性旺夫运欠佳，对婚姻期望高，可能因不满现状而与丈夫争吵，影响夫妻和睦。互相体谅和换位思考能让婚姻更美好。';
            }
            // 日支和时支相冲
            if (BaziCommon::getXianChong($ddz . $hdz)) {
                $result[] = '日时相冲的女性旺夫运不理想，婚后可能过于自私，忽视丈夫感受，导致夫妻不和。互相体谅和站在对方角度思考能增进婚姻美满。';
            }
            $terrain = $this->female->getTerrain();
            // 日支为官星，且坐下神煞为死绝墓
            if (in_array($godZ['day'], ['正官', '七杀']) && in_array($terrain['day'], ['死', '绝', '墓'])) {
                $result[] = '这类命格的女性对伴侣和婚姻的要求比较高，结婚后可能因为各方面达不到自己的要求而和伴侣发生争执，甚至会让伴侣感觉难堪。建议结婚后要注重实际，懂得知足，多体谅丈夫，这样才能有和谐美满的婚姻。';
            }
            // 日支为伤官
            if ($godZ['day'] == '伤官') {
                $result[] = '这类命格的女性大多对婚姻的期待较高，并且追求完美，结婚后可能会发现很多事情并不是自己想象中的那样，因为达不到预期，所以容易和丈夫发生一些争执。若是能够懂得知足，互相体谅，婚后生活则会越来越好。';
            }
            // 伤宫为喜用神，且八字中伤官带财，而不见官星的命格
            if (in_array('食伤', [$xy['shen']['yong'], $xy['shen']['xi']])) {
                foreach ($godT as $k => $v) {
                    $arr1 = [$v, $godZ[$k]];
                    $arr2 = array_intersect(['正财', '偏财'], $arr1);
                    if (in_array('伤官', $arr1) && !empty($arr2)) {
                        $result[] = '这类命格的女性大多温柔贤惠，善于照顾家庭，能帮助自己的丈夫解决很多烦恼，助旺丈夫的运势。她们对待感情比较细腻，愿意为家庭付出，是理想的妻子人选。';
                        break;
                    }
                }
            }
            // 日禄归时(日主天干为甲，生在寅时，时支的寅字就是甲木天干的禄神)
            if ($jiNian['d'][0] == '甲' && $jiNian['h'][0] == '寅') {
                $result[] = '这类命格的女性一生运势都比较好，婚后能助旺丈夫的运势，在事业和财富上都能够帮助丈夫，且多半能生贵子，婚姻生活比较幸福美满。不过需要注意的是，虽然她们有着不错的运势，但也需要夫妻之间相互理解和支持，才能让婚姻更加稳定和幸福。';
            }
            if (empty($result)) {
                $result[] = '你的旺夫程度一般，你对家庭的态度可能比较平淡，对待另一半有时会有些冷淡，这可能会给双方关系带来一定影响。建议你多关注伴侣的需求和感受，增强彼此之间的沟通和互动，以增进夫妻间的感情。';
            }
        } else {
            // 日柱坐下的神煞为天乙贵人
            if (in_array('天乙贵人', $shenShaRes['d'])) {
                $result[] = '日坐贵人命格的男命有着极好的运气，特别是在结婚后可能会遇到不少贵人的帮助，也能让妻子的运势不断上升，助旺妻子的运势。他们通常非常顾家和关心家人，是值得信赖和依靠的对象。';
            }
            // 日支为正财或偏财，且为喜用神
            if (in_array($godZ['day'], ['正财', '偏财']) && in_array('才财', [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $result[] = '日坐正财为喜用神代表着旺妻运强，在结婚后自身会得到妻子或者妻家的鼎力相助，无论是事业还是人脉都能得到开拓和发展。这类命格的男性，在婚后通常都会支持妻子，帮助妻子的事业，助旺妻子的运势。他们非常重视家庭的稳定和经济状况的改善。';
            }
            // 只有一个正财或一个偏财（看藏干）
            if (($godThs['zc'] + $godThs['pc']) == 1) {
                $result[] = '正偏财不杂意味着旺妻运强，这类命格的男性对妻子非常专一，对家庭富有责任心。他们通常非常勤奋努力工作赚钱养家糊口，同时也会尽力维护家庭的和睦与稳定。因此他们的妻子可以享受到安稳富足的生活。';
            }
            // 日支为食伤或伤官
            if (in_array($godZ['day'], ['食神', '伤官'])) {
                $result[] = '日坐食伤意味着旺妻运好，这类命格的男性心思都比较细腻，在感情方面也非常注重细节。在婚后更愿意将心思花在家庭上，对妻子的各方面都呵护到位。他们通常非常顾家和关心家人，是理想的伴侣选择。';
            }
            // 原局中正偏印数量大于等于三个
            if (($godTzs['zy'] + $godTzs['py']) >= 3) {
                $result[] = '印多意味着旺妻运一般，这类命格的男性在结婚后经常会因为不够体贴妻子而导致妻子伤心难过。建议夫妻之间应多站在对方的立场去思考问题，这样才能更加和睦地相处。';
            }
            // 地支包含辰戌丑未
            $tmp1 = array_intersect(['辰', '戌', '丑', '未'], $dzArr);
            if (count($tmp1) == 4) {
                $result[] = '辰戌丑未全意味着旺妻运一般，这类命格的人交友广泛，人缘较好，有不少的异性朋友。建议在结婚后要和异性保持距离，对待伴侣一心一意。他们比较特立独行，喜欢我行我素，容易忽略妻子的感受，因此会导致婚姻不够和谐。若能够互相体谅，认真倾听对方的建议，婚姻生活才会变得越来越幸福。';
            }
            // 日柱为甲辰或甲戌
            if (in_array($dgz, ['甲辰', '甲戌'])) {
                $result[] = '这类命格的男性，旺妻运一般，在婚姻中比较特立独行。他们责任心较强，愿意将心思花在家庭上，对妻子也十分体贴。但是需要注意的是，他们的固执性格可能会导致夫妻之间产生矛盾。建议在结婚后多为妻子着想，认真倾听妻子的建议和感受，这样才能使婚姻更加稳定。';
            }
            // 正财和偏财为用神，且同柱十二长生有帝旺
            if ($xy['shen']['yong'] == '才财') {
                $terrain = $this->male->getTerrain();
                $bool = false;
                foreach ($godT as $k => $v) {
                    if (in_array($v, ['正财', '偏财']) || in_array($godZ[$k], ['正财', '偏财'])) {
                        if ($terrain[$k] == '帝旺') {
                            $bool = true;
                            break;
                        }
                    }
                }
                if ($bool) {
                    $result[] = '妻星为喜用，生旺之地，乃旺妻之吉兆。此类男士，责任心甚强，婚后倾心于家，体贴入微，婚姻和谐，幸福美满。';
                }
            }
            // 日支为比劫
            if (in_array($godZ['day'], ['比肩', '劫财'])) {
                $result[] = '日支比劫意味着旺妻运一般，这类命格的男性比较固执。在结婚后可能会比较大男子主义，容易忽略对方的感受。建议在结婚后多关注妻子的需求和感受，多与妻子沟通交流，尊重妻子的意见和想法。这样可以让夫妻之间的相处变得更加和谐美满。';
            }
            // 月支或时支冲日支
            if (BaziCommon::getXianChong($mdz . $ddz) || BaziCommon::getXianChong($hdz . $ddz)) {
                $result[] = '日支受年、月支之冲意味着旺妻运不太理想。这类命格的男性人缘较好，异性朋友多。结婚后可能会因为和异性过于亲密而导致妻子没有安全感。建议在结婚后和异性保持一定的距离，避免让妻子感到不安。同时也要注意自己的言行举止，不要做出任何伤害妻子的事情。';
            }
            // 日支和时支相刑
            if (BaziCommon::getXianXin($ddz . $hdz)) {
                $result[] = '日时相刑意味着旺妻运不太理想。这类命格的男性在感情方面比较粗心大意，容易忽略妻子的需求。若是能更加体贴对方，注重生活中的细节，婚姻生活也会变得更加美好。';
            }
            // 日支和时支相冲
            if (BaziCommon::getXianChong($ddz . $hdz)) {
                $result[] = '日时相冲意味着旺妻运不理想。这类命格的男性性格过于自我，对任何事情都有一种不在乎的态度。结婚后可能对婚姻或伴侣不够重视，导致夫妻关系紧张。建议结婚后多关注家庭，体贴妻子，这样才能让婚姻生活变得温馨。';
            }
            // 原局中正财和偏财数量大于等于三，但是身弱格或从弱格
            if (($godTzs['zc'] + $godTzs['pc']) >= 3 && in_array($wangDu, ['身弱格', '从弱格'])) {
                $result[] = '此类命格男士，感情中易显自私，常忽略伴侣感受，婚后可能因不够体贴而致夫妻不和。建议婚后多关注家庭，体贴妻子，以美满婚姻生活。';
            }
            // 天干有正财且其他天干无偏财且同柱地支无偏财
            if (!in_array('偏财', $godT)) {
                foreach ($godT as $k => $v) {
                    if ($v == '正财' && $godZ[$k] != '偏财') {
                        $result[] = '此命格男性，感情专一，婚后照顾妻子无微不至，给予妻子强烈安全感。总体而言，能让妻子感到安心，具有较强旺妻特质。';
                        break;
                    }
                }
            }
            // 妻宫为食伤
            if (in_array($godZ['day'], ['食神', '伤官'])) {
                $result[] = '这类命格男性，性格温柔善良，心思细腻，婚后体贴妻子，对家庭和妻子负责，能在各方面扶助妻子。';
            }
            // 食神伤官数>=4
            if (($godTzs['ss'] + $godTzs['sg']) >= 4) {
                $result[] = '此类男性，心思细腻，善于捕捉微妙情绪，浪漫满怀，婚后照顾妻子情绪，夫妻情深意切，和睦相处。';
            }
            // 男命身旺，且食神和伤官数>=3或正财偏财数>=3
            if ($wangDu == '身旺格' && ($godTzs['ss'] + $godTzs['sg']) >= 3 && ($godTzs['zc'] + $godTzs['pc']) >= 3) {
                $result[] = '这类此命格男性，风流倜傥，婚后亦难收敛，易惹桃花，或有损家庭和睦。建议收敛心性，专注家庭，以确保婚姻美满。';
            }
            if (empty($result)) {
                $result[] = '旺妻运中等，个性独立，不轻易妥协，亦不迁就对方。追求付出与回报之平衡，若伴侣能互相尊重，平等相待，则家庭氛围融洽。';
            }
        }
        return current($result);
    }

    /**
     * 根据性别获得财富
     * @param int $sex 性别
     * @return string[]
     */
    protected function getCaiFuBySex($sex): array
    {
        $info = $this->userInfo[$sex];
        $fate = $info['fate'];
        $terrainArr = Ex::getTerrainData();
        $jiNian = $info['base']['jinian'];
        $ytg = $jiNian['y'][0];
        $mtg = $jiNian['m'][0];
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $dgz = implode('', $jiNian['d']);
        $cur = $fate['curr'];
        $next = $fate['curr'] + 1;
        if ($next > 7) {
            $next = 7;
        }
        $xy = $info['xy'];
        $gzStr = $fate['eight']['chronlogy_year'][$cur];
        $gzNextStr = $fate['eight']['chronlogy_year'][$next];
        $gz = Utils::mbStrSplit($gzStr);
        $gzNext = Utils::mbStrSplit($gzNextStr);
        $keT = BaziCommon::getXianKe($gz[0] . $dtg);
        $tong = $gz[0] == $dtg;
        $xin = BaziCommon::getXianXin($gz[1] . $ddz);
        $chong = BaziCommon::getXianChong($gz[1] . $ddz);
        $terrain = $terrainArr[$dtg . $gz[1]];
        $keTNext = BaziCommon::getXianKe($gzNext[0] . $dtg);
        $tongNext = $gzNext[0] == $dtg;
        $xinNext = BaziCommon::getXianXin($gzNext[1] . $ddz);
        $chongNext = BaziCommon::getXianChong($gzNext[1] . $ddz);
        $wxAttr = WuXing::GZ_TO_WX;
        $sanHe = BaziCommon::getSanHeDz2([[$gz[1], $ddz]]);
        $gzTgWx = $wxAttr[$gz[0]];
        $gzDzWx = $wxAttr[$gz[1]];
        $gzTgWxN = $wxAttr[$gzNext[0]];
        $gzDzWxN = $wxAttr[$gzNext[1]];
        $ddzWx = $wxAttr[$ddz];
        $dtgWx = $wxAttr[$dtg];
        $sf = WuXing::getWuxingGuanXi($ddzWx, $dtgWx);
        $boolSf = in_array($sf, ['生', '扶']);
        $kw = explode(',', $info['kw']);
        $hehuaNext = BaziCommon::getHehua($gzNext[0] . $dtg);
        $sanHeNext = BaziCommon::getSanHeDz2([[$gzNext[1], $ddz]]);
        if ($sex) {
            if (!$keT && !$xin && !$chong && !$tong) {
                $key1 = 151;
            } elseif (in_array($gzTgWx, [$xy['xy']['yong'], $xy['xy']['xi']]) && $dgz == $gzStr) {
                $key1 = 153;
            } elseif ($gzTgWx == $xy['xy']['xi'] && BaziCommon::getGodName($gz[0] . $dtg)) {
                $key1 = 154;
            } elseif ($gzTgWx == $xy['xy']['yong'] && $mtg == $gz[0]) {
                $key1 = 155;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $mtg == $gz[0]) {
                $key1 = 156;
            } elseif ($gzTgWx == $xy['xy']['xi'] && $mtg == $gz[0]) {
                $key1 = 157;
            } elseif (empty(array_intersect([$xy['xy']['xi'], $xy['xy']['qiu']], [$gzTgWx, $gzDzWx]))) {
                $key1 = 158;
            } elseif ($dtg == $gz[0]) {
                $key1 = 152;
            } else {
                $key1 = 150;
            }
            if (!in_array($jiNian['y'][1], $kw)) {
                $key2 = $boolSf ? 251 : 252;
            } else {
                $key2 = 250;
            }

            if (!$keTNext && !$tongNext && !$xinNext && !$chongNext) {
                $key3 = 351;
            } elseif ($gzTgWxN == $xy['xy']['yong'] && BaziCommon::getHehua($gzNext[0] . $dtg)) {
                $key3 = 353;
            } elseif ($gzTgWxN == $xy['xy']['ji'] && $gzNext[0] == $dtg) {
                $key3 = 354;
            } elseif (BaziCommon::getXianChong($gzNext[1] . $jiNian['m'][1])) {
                $key3 = 355;
            } elseif ($gzTgWxN == $xy['xy']['yong'] && $gzDzWxN == $xy['xy']['ji'] && empty($hehuaNext) && !$xinNext && !$chongNext) {
                $key3 = 356;
            } elseif ($gzTgWxN == $xy['xy']['ji'] && $gzNext[1] == $ddz) {
                $key3 = 357;
            } elseif ($gzNext[0] == $dtg) {
                $key3 = 352;
            } else {
                $key3 = 350;
            }
        } else {
            if (!$keT && !$xin && !$chong && !$tong) {
                $key1 = 101;
            } elseif (in_array($gzTgWx, [$xy['xy']['yong'], $xy['xy']['xi'], $xy['xy']['qiu']])) {
                $key1 = 102;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $sanHe[2]) {
                $key1 = 103;
            } elseif ($gzTgWx == $xy['xy']['xi'] && BaziCommon::getHehua($ytg . $mtg)) {
                $key1 = 104;
            } elseif ($gzTgWx == $xy['xy']['qiu'] && $gz[0] == $dtg) {
                $key1 = 105;
            } elseif ($gzTgWx == $xy['xy']['xi'] && $terrain == '绝') {
                $key1 = 106;
            } else {
                $key1 = 100;
            }
            if (!in_array($jiNian['y'][1], $kw)) {
                $key2 = $boolSf ? 201 : 202;
            } else {
                $key2 = 200;
            }
            if (!$keT && !$xin && !$chong && !$tong) {
                $key3 = 301;
            } elseif (!$chongNext) {
                $key3 = 302;
            } elseif ($sanHeNext[2]) {
                $key3 = 303;
            } elseif ($gzTgWx == $xy['xy']['ji'] && $gzNext[0] == $dtg) {
                $key3 = 304;
            } elseif ($gzTgWx == $xy['xy']['yong'] && $gzNext[0] == $dtg) {
                $key3 = 305;
            } else {
                $key3 = 300;
            }
        }
        $list = [
            101 => '婚前男方财运平平，工作稳定但难以承担家庭重负。若肯吃苦耐劳，或有可观收入。夫妻共同努力，方可积累财富。',
            201 => '男方条件虽未达伴侣家庭期望，但得贤良伴侣，愿协调关系，共筑美满家庭。',
            301 => '婚后数年，男方财运一般，努力付出或有所回报。工作环境稳定，家庭经济时有紧张。夫妻共同打拼，方能积累更多财富。',
            151 => '婚前经济自足，承担家庭开销或有困难。伴侣相互扶持，共担生活压力，方可达衣食无忧之境。',
            251 => '夫妻宫和谐，与夫家关系融洽，婚姻多得支持。可能获得房产、汽车等资产援助，伴侣亦愿共享财务资源。',
            351 => '婚后女方财运一般，满足生活需求但难以支撑家庭。节俭开支，可享安稳生活。提高经济能力需夫妻共同打拼，相互扶持。',
            102 => '男方婚前财运平平，工作或有阻碍。然能力出众，把握机遇，事业可进展顺利，为婚姻奠定财富基础。',
            202 => '与配偶娘家关系和睦，婚姻多得支持。或获车房等物资援助，伴侣慷慨大方，共享财富。',
            302 => '婚后数年财运不佳，事业收入遇困境，易与上司产生矛盾，降职风险高。需冷静思考，把握时机调整心态。',
            152 => '女方婚前财运亨通，事业有成并扩展人脉。工作思路清晰灵活得亲友相助是积累财富之良机。',
            352 => '婚后运势旺盛得长辈或贵人扶持事业创新高峰。工作发展顺利副业收入丰厚积累超越同龄人之财富。',
            303 => '婚后数年运势低迷工作急躁难以发挥能力易犯错误。需调整心态避免冷漠错失机遇与帮助。',
            153 => '婚后生活美满得长辈或职场贵人相助人际关系广阔。事业发展顺利灵活变通带来新机遇是积累财富之佳期。',
            103 => '婚前运势旺盛事业发展顺利收入丰厚易积累财富。身边兄弟朋友是贵人时常得到帮助。',
            104 => '命喜星合婚前大运不佳男方工作难以发挥才能易犯错误影响收入。心态未调整好言行易冷淡烦躁疏远身边人影响机会与助力。',
            154 => '女方婚前运势欠佳经济自足但难以支撑家庭开销。事业需付出更多努力得父母有限支持日后仍需与伴侣共同努力打拼。',
            353 => '婚后运势一般财富受多方面因素影响。婚后数年事业或遇阻碍工作变动需家人支持帮助。',
            155 => '女方婚后运势旺盛事业发展迅速个人能力突出有责任心投入工作打下良好经济基础积蓄丰厚。',
            156 => '婚前急于积累财富心态急切事业易受阻碍忽略亲友难以得到帮助。开销大增影响婚前财富积累需调整心态。',
            354 => '婚后因增加家庭经济收入而心态浮躁易为财所困忽略亲友关系工作中常犯小错影响事业发展。需调整状态限制开销。',
            105 => '男方婚前运势欠佳工作竞争激烈遇困难难以与同事建立信任交际开支增加。需调整状态防小人陷害寻找解决方法。',
            304 => '婚后运势不佳事业易受小人阻碍同事间缺乏信任面对失误易自责情绪压抑生活压力大交际开销增加。需调整状态与伴侣多沟通交流。',
            157 => '女方婚后运势旺盛能力强负责工作遇困难能灵活解决迎来事业高速发展期通常拥有比同龄人更丰富的基础。',
            355 => '婚后数年财运平淡精神紧张易因琐事胡思乱想陷入负面情绪。美妆服装开销大易受商家陷阱影响经济遇困。需调整心态理性消费与伴侣共同规划家庭财务。',
            106 => '男方整体运势欠佳事业或停滞不前工作状态差做事急躁自我负担重易产生压力。需调整情绪平和度过困难时期。',
            305 => '婚后运势旺盛有机会得到长辈或职场贵人相助事业发展顺利副业易有成就家庭积蓄丰厚。',
            158 => '女方婚前经济自足但支撑家庭稍显吃力建议与伴侣共同商议同居及开支规划避免无谓花销做好理财储蓄为婚姻打下良好基础。',
            356 => '婚后心态转变注重家庭节约开销学会比价购物夫妻话题围绕家常。事业心或有所下降或受阻于家庭因素影响难以有大突破可学习新技能或提升学历以备后用。',
            159 => '婚前运势不佳事业竞争激烈工作状态不稳定易受他人言行影响投入工作困难收入受损日常生活压力大或因病破财需调整情绪加强沟通。',
            252 => '男方家庭支持有限初期负担家庭吃力需个人努力创造婚姻价值婆媳相处需多加忍耐协调以维护家庭和谐。',
            357 => '婚后运势不佳工作易受他人影响完成度事业竞争激烈收入或受损生活压力大或因病破财需调整心态减轻压力以应对困境。',
            200 => '虽条件未达伴侣家庭期望但得贤良伴侣愿意协调关系共同经营家庭与生活以创造美满未来。',
            150 => '女方婚前运势欠佳经济能力有限难以满足家庭开销需求事业需付出更多努力日常工作得父母有限支援日后仍需与伴侣共同努力以改善经济状况。',
            100 => '男方婚前财运一般满足个人需求但难以承担家庭重任积累财富需夫妻共同努力相互鼓励虽与理想有差距但不愁衣食生活安稳。',
            250 => '婚后男方家庭给予部分帮助但初期负担仍重需个人努力创造更多婚姻价值婆媳关系需耐心协调以维护家庭和睦。',
            300 => '婚后数年男方财运一般付出努力可有所回报工作环境稳定家庭经济虽有压力但可承担积累更多财富仍需夫妻共同打拼相互扶持共进。',
            350 => '女方婚后财运不理想满足基本生活需求但与理想有差距若不追求高质量生活亦可过得安稳滋润提高经济能力仍需夫妻共同经营相互包容鼓励共渡难关。',
        ];
        return [
            $list[$key1], $list[$key2], $list[$key3],
        ];
    }

    /**
     * 用户基本盘信息
     * @param int $sex 性别
     * @return array
     * @throws Exception
     */
    protected function getBasePan(int $sex): array
    {
        $otime = strtotime($this->orginData['otime']);
        $yearC = date('Y', $otime);
        $lunar = $sex ? $this->female : $this->male;
        $base = $lunar->getLunarByBetween();
        // 几岁
        $base['age'] = $yearC - (int)$lunar->dateTime->format('Y') + 1;
        $jiNian = $base['jinian'];
        $godHide = $lunar->_getGod();
        $godT = $lunar->getGod();
        $godZ = [
            'year' => $godHide['year']['god'][0],
            'month' => $godHide['month']['god'][0],
            'day' => $godHide['day']['god'][0],
            'hour' => $godHide['hour']['god'][0],
        ];
        $fate = $lunar->getFate();
        $i = BaziExt::getKeyWithArray($yearC, $fate['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $fate['curr'] = $i;
        $fenXi = $lunar->getWxFenxi();
        $godTz = array_merge(array_values($godT), array_values($godZ));
        $godTzS = $this->getGodSum($godTz);
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $godTH = array_merge(array_values($godT), $godH2);
        $godTHS = $this->getGodSum($godTH);
        $baziEx = new BaziEx($lunar);
        return [
            'base' => $base,
            'god' => $godT,
            '_god' => $godHide,
            'godz' => $godZ,
            'god_tzs' => $godTzS,
            'god_ths' => $godTHS,
            'na_yin' => $lunar->getNayin(),
            'terrain' => $lunar->getTerrain(),
            'jnwx' => $this->getJnWx($jiNian),
            'wr' => BaziExt::getWr($jiNian['d'][0], $jiNian['d'][1]),
            'gx' => $this->getJnGx($jiNian),
            'wangdu' => $baziEx->getWangDu(),
            'kw' => Huangli::getKongWangbyGz($jiNian['d'][0] . $jiNian['d'][1]),
            'kwy' => Huangli::getKongWangbyGz($jiNian['y'][0] . $jiNian['y'][1]),
            'xy' => [
                'xy' => $fenXi['xy'],
                'shen' => $fenXi['shen'],
            ],
            'fate' => $fate,
            'jiaoyun' => $this->getJiaoYun($fate['age'], $lunar->dateTime->format('Y-m-d H:i:s')),
        ];
    }

    /**
     * 交运
     * @param array $fateAge 大运年龄数组
     * @param string $time 用户生日
     * @return array
     * @throws Exception
     */
    protected function getJiaoYun(array $fateAge, string $time): array
    {
        $jiaoYunStr = "+{$fateAge['year']} year {$fateAge['month']} month {$fateAge['day']} day +{$fateAge['hour']} hour";
        $tmpJaoTime = strtotime($jiaoYunStr, strtotime($time));
        $time = date('Y-m-d H:00:00', $tmpJaoTime);
        $lunar = Ex::date($time);
        $base = $lunar->getLunarByBetween();
        return [
            'gongli' => [
                'y' => (int)$lunar->dateTime->format('Y'),
                'm' => (int)$lunar->dateTime->format('m'),
                'd' => (int)$lunar->dateTime->format('d'),
                'h' => (int)$lunar->dateTime->format('h'),
            ],
            'nongli' => $base['nongli'],
            '_nongli' => $base['_nongli'],
            'h' => $base['jinian']['h'][1],
        ];
    }

    /**
     * 干支关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnGx(array $jiNian): array
    {
        $result = [];
        foreach ($jiNian as $k => $v) {
            $arr = [];
            $result[$k]['t'] = [];
            $result[$k]['d'] = [];
            foreach ($jiNian as $k1 => $v1) {
                if ($k == $k1) {
                    continue;
                }
                foreach ($jiNian as $k2 => $v2) {
                    if ($k2 == $k1 || $k2 == $k) {
                        continue;
                    }
                    $arr[] = [$v[1], $v1[1], $v2[1]];
                }
                $strTg = $v[0] . $v1[0];
                $strDz = $v[1] . $v1[1];
                if (BaziCommon::xianHeTg($strTg)) {
                    $result[$k]['t'][] = '合';
                }
                if (BaziCommon::getXianKe($strTg)) {
                    $result[$k]['t'][] = '克';
                }
                if (BaziCommon::getXianChong($strTg)) {
                    $result[$k]['t'][] = '冲';
                }
                if (BaziCommon::liuHeDz($strDz)) {
                    $result[$k]['d'][] = '六合';
                }
                if (BaziCommon::getXianKe($strDz)) {
                    $result[$k]['d'][] = '克';
                }
                if (BaziCommon::getXianXin($strDz)) {
                    $result[$k]['d'][] = '刑';
                }
                if (BaziCommon::getXianKe($v1[1] . $v[1])) {
                    $result[$k]['d'][] = '被克';
                }
                if (BaziCommon::getXianChong($strDz)) {
                    $result[$k]['d'][] = '冲';
                }
            }
            $sanHeRes = BaziCommon::getSanHeDz2($arr);
            if (!empty($sanHeRes[3])) {
                $result[$k]['d'][] = '三合';
            }
            if (!empty($sanHeRes[2])) {
                $result[$k]['d'][] = '半三合';
            }
            $result[$k]['t'] = array_values($result[$k]['t']);
            $result[$k]['d'] = array_values($result[$k]['d']);
        }

        return $result;
    }

    /**
     * 获得纪年五行的相关数据 不含藏干
     * @param array $jiNian
     * @return array
     */
    protected function getJnWx(array $jiNian): array
    {
        $jnWx = [];
        $jnNum = [
            '金' => ['wx' => '金', 'num' => 0], '木' => ['wx' => '木', 'num' => 0],
            '水' => ['wx' => '水', 'num' => 0], '火' => ['wx' => '火', 'num' => 0], '土' => ['wx' => '土', 'num' => 0],
        ];
        $jnYy = [];
        $wxAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $tmpWx = $wxAttr[$v1];
                $jnWx[$k][$k1] = $tmpWx;
                $jnNum[$tmpWx]['num']++;
                $jnYy[$k][$k1] = BaziExt::getYinYang($v1) ? '阳' : '阴';
            }
        }
        return [
            'wx' => $jnWx, 'yy' => $jnYy, 'num' => array_values($jnNum),
        ];
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'ss' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }
}
