<?php
// +----------------------------------------------------------------------
// | Liunian2019.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\controller\v1;

use app\lib\bazi\BaziExt;
use app\lib\bazi\ShenSha;
use app\lib\ziwei\ZiweiData;
use app\model\more\Dayun;
use app\traits\liunian\Base2019Traits;
use app\validate\ValidateBase;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Liunian2019
{
    use Base2019Traits;

    /**
     * 日期相关
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 紫薇相关
     * @var ZiweiData
     */
    protected ZiweiData $ziwei;

    /**
     * 2019年，纪年
     * @var array
     */
    protected array $liuNian = [
        2019, ['己', '亥'],
    ];

    /**
     * 当前大运纪年
     * @var array
     */
    protected array $curDaYunYear = [];

    /**
     * 天干地支五行
     * @var array
     */
    protected array $wuXingAttr = [];

    /**
     * 用户纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 十二地支
     * @var string[]
     */
    protected array $dz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * 天干
     * @var string[]
     */
    protected array $tg = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"];

    /**
     * 阴阳
     * @var string
     */
    protected string $yinyang = '阴';

    /**
     * 流年2019
     * @return array
     * @throws \DateMalformedStringException
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index()
    {
        $data = [
            // 出生时间
            'time' => input('time', '', 'trim'),
            // 性别 男为0，女为1
            'sex' => input('sex', 0, 'intval'),
            // 订单年份
            'oyear' => input('oyear', date('Y'), 'intval'),
            // 来源标识重庆万年历时为wnl
            'source' => input('source', '', 'trim'),
        ];
        $validate = new ValidateBase();
        $validate->rule(
            [
                'time|出生时间' => ['require', 'isDateOrTime:出生时间'],
                'sex|性别' => ['require', 'in:0,1'],
                'oyear|测算年份' => ['require', 'between:2019,2030'],
            ]
        );
        if (true !== $validate->check($data)) {
            return ['status' => 0, 'msg' => $validate->getError()];
        }
        $this->lunar = Ex::date($data['time'])->sex($data['sex']);
        $data['time'] = $this->lunar->dateTime->getTimestamp();
        $year = $data['oyear'];// 当前时间
        $this->liuNian = [
            $data['oyear'],
            BaziExt::getGanZhi($year),
        ];
        $this->ziwei = new ZiweiData('无名', $data['time'], $data['sex']);
        $this->wuXingAttr = $this->lunar->wuXingAttr;
        $base = $this->lunar->getLunarByBetween();
        $this->jiNian = $base['jinian'];
        $dgz = $base['jinian']['d'];
        $hour = '';
        if (date('s', $data['time']) == 0) {
            $hour = $this->lunar->getLunarGanzhiHour()[1] . '时';
        }
        // 年龄
        $age = $year + 1 - (int)(date('Y', $data['time']));
        $fate = $this->lunar->getFate();
        $i = BaziExt::getKeyWithArray($year, $fate['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $shiShengKey = $fate['eight']['_god'][$i];
        $yearNameCur = $fate['eight']['chronlogy_year'][$i];
        $yearNameCurList = preg_split('/(?<!^)(?!$)/u', $yearNameCur);
        $this->curDaYunYear = $yearNameCurList;
        // 岁运
        $suiYun = $this->lunar->getGodNameByTg($dgz[0], '戊');

        $curryear = $fate['eight']['year'][$i];
        $yearNameCurStr = $yearNameCur . '年（' . $curryear . '年-' . ($curryear + 10) . '年）';
        $daYunShi = $this->getDaYunShi($dgz, $yearNameCurList);
        // 设阴阳
        $tmpindex = array_search($base['jinian']['y'][0], $this->tg);
        if ($tmpindex % 2 == 0) {
            $this->yinyang = '阳';
        } else {
            $this->yinyang = '阴';
        }
        $shShaInfo = new ShenSha($this->lunar, $this->liuNian);
        return [
            'hour' => $hour,
            'age' => $age,
            'fateIndex' => $i,
            // 纪年
            'base' => $base,
            // 命宫
            'minggong' => implode('', $shShaInfo->getMingGong()),
            // 身宫
            'shengong' => $this->getShenGong($base),
            // 天干十神
            'god' => $this->lunar->getGod(),
            // 地支十神
            '_god' => $this->lunar->_getGod(),
            // 纳音
            'nayin' => $this->lunar->getNayin(),
            // 地势
            'terrain' => $this->lunar->getTerrain(),
            // 旺相休求死
            'wxxqs' => $this->lunar->getWxxqs(),
            // 喜用神
            'like' => $data['source'] == 'wnl' ? $this->lunar->getLikeGod2() : $this->lunar->getLikeGod(),
            // 获取胎元
            'fetus' => $this->lunar->getFetus(),
            // 获取日空
            'empty' => $this->lunar->getEmptyDay(),
            // 大运
            'fate' => $fate,
            'dianping' => [
                'year' => $yearNameCurStr,
                'shi' => $daYunShi,
                'info' => $this->getDianPin($shiShengKey, $suiYun),
            ],
            // 神煞
            'shensha' => $this->getShenSha(),
            // 财富
            'caifu' => $this->getCaiFu($base['jinian']['y'][1], $this->liuNian[1][1]),
            // 事业
            'shiye' => $this->getShiYe(),
            // 感情
            'ganqing' => $this->getGanQing(),
            // 友情(人际)
            'youqing' => $this->getYouQing(),
            // 健康
            'jiankan' => $this->getJianKan(),
            // 学业
            'xueye' => $this->getXueYe(),
            // 家庭
            'jiating' => $this->getJiaTing(),
            // 主星影响和预示
            'zhuxing' => $this->getZhuXing(),
            // 开运
            'kaiyun' => $this->getKaiYun(),
            // 流月
            'liuyue' => $this->getLiuyue(),
        ];
    }

    /**
     * 返回大运诗
     * @param array $dgz 日干支
     * @param array $yearNameCurList 当有大运干支
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function getDaYunShi($dgz, $yearNameCurList): array
    {
        $res = [];
        $d1 = $dgz[0] . $yearNameCurList[0];
        $d2 = $dgz[0] . $yearNameCurList[1];
        $data = Dayun::where('gz', 'in', [$d1, $d2])
            ->field('info')
            ->cache(300)
            ->select();
        foreach ($data as $v) {
            $res = array_merge_recursive($res, $v['info']);
        }
        return $res;
    }

    /**
     * 开运
     * @return array
     */
    private function getKaiYun(): array
    {
        $year = $this->liuNian[0];
        $list = [1, 2, 3, 4, 5, 6, 7, 8, 9];
        $listPos = ['中宫', '西北', '正西', '东北', '正南', '正北', '西南', '正东', '东南'];
        $listPos2 = ['中央', '西北', '正西', '东北', '正南', '正北', '西南', '正东', '东南'];
        $diff = abs($year - 2017) % 9;
        $index = $year >= 2017 ? (9 - $diff) : $diff;
        $currList = [];
        foreach ($list as $k => $v) {
            $tmpIndex = ($index + $k) % 9;
            $currList[$list[$tmpIndex]] = $k;
        }
        return [
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]], $listPos2[$currList[1]]],
            [$listPos[$currList[3]], $listPos[$currList[3]], $listPos[$currList[3]], $listPos[$currList[3]]],
            [$listPos[$currList[9]], $listPos[$currList[9]]],
            [],
            [$listPos[$currList[4]], $listPos[$currList[8]]],
            [$listPos[$currList[8]], $listPos[$currList[8]]],
            [$listPos[$currList[3]], $listPos[$currList[3]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]], $listPos[$currList[1]]], //重复
            [$listPos[$currList[5]], $listPos2[$currList[2]], $listPos[$currList[5]], $listPos[$currList[5]], $listPos2[$currList[2]]],
            [$listPos[$currList[1]], $listPos2[$currList[1]]],
        ];
    }

    /**
     * 身宫
     * @param $Lunar
     * @return string
     */
    private function getShenGong($Lunar): string
    {
        $dz = ['', '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        $lists = [
            '寅' => ['丙寅', '戊寅', '庚寅', '壬寅', '甲寅'],
            '卯' => ['丁卯', '己卯', '辛卯', '癸卯', '乙卯'],
            '辰' => ['戊辰', '庚辰', '壬辰', '甲辰', '丙辰'],
            '巳' => ['己巳', '辛巳', '癸巳', '乙巳', '丁巳'],
            '午' => ['庚午', '壬午', '甲午', '丙午', '戊午'],
            '未' => ['辛未', '癸未', '乙未', '丁未', '己未'],
            '申' => ['壬申', '甲申', '丙申', '戊申', '庚申'],
            '酉' => ['癸酉', '乙酉', '丁酉', '己酉', '辛酉'],
            '戌' => ['甲戌', '丙戌', '戊戌', '庚戌', '壬戌'],
            '亥' => ['乙亥', '丁亥', '己亥', '辛亥', '癸亥'],
            '子' => ['丙子', '戊子', '庚子', '壬子', '甲子'],
            '丑' => ['丁丑', '己丑', '辛丑', '癸丑', '乙丑'],
        ];
        $start = $Lunar['_nongli']['m'];
        $hIndex = array_search($Lunar['jinian']['h'][1], $dz);
        $dIndex = 0;
        if ($hIndex > 10) {
            $dIndex = $hIndex;
        } else {
            $dIndex = 10 - $hIndex;
        }
        $sIndex = ($start - $dIndex + 12) % 12;
        $sdz = $dz[$sIndex];
        if ($sIndex == 0) {
            $sdz = '亥';
        }
        $tg = $Lunar['jinian']['y'][0];
        $tgIndex = 0;
        if (in_array($tg, ["甲", "己"])) {
            $tgIndex = 0;
        } elseif (in_array($tg, ["乙", "庚"])) {
            $tgIndex = 1;
        } elseif (in_array($tg, ["丙", "辛"])) {
            $tgIndex = 2;
        } elseif (in_array($tg, ["丁", "壬"])) {
            $tgIndex = 3;
        } else {
            $tgIndex = 4;
        }
        return $lists[$sdz][$tgIndex];
    }
}
