<?php
// +----------------------------------------------------------------------
// |  ValidateBase
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\validate;

use app\model\baobaoqm\Xing;
use think\Validate;

class ValidateBase extends Validate
{
    /**
     * 验证是否是时间字符串
     * @param string $value
     * @param string $msg
     * @return bool|string
     */
    protected function isDateOrTime(string $value, string $msg = '')
    {
        if (empty($value)) {
            return true;
        }
        $start = strtotime('1900-01-01');
        $end = strtotime('2099-12-31');
        $msg = empty($msg) ? false : $msg;
        if (!preg_match('/^-?\d+$/', $value)) {
            $value = strtotime($value);
        }
        if (false !== $value) {
            if ($value > $start && $value <= $end) {
                return true;
            }
        }
        return $msg ? $msg . '只能在1900到2099年之间，格式为Y-m-d或Y-m-d H:i:s' : $msg;
    }

    /**
     * 姓氏验证规则
     * @param string $value
     * @return bool|string
     */
    protected function lastName(string $value)
    {
        $xingList = Xing::getlists();
        return in_array($value, $xingList) ? true : '暂不支持这个姓';
    }
}
