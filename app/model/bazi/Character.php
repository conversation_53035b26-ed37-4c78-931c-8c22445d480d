<?php
/**
 * 八字排盘之性格
 */

namespace app\model\bazi;

use app\model\BaseModel;

/**
 * Class app\model\bazi\Character
 * @property int $id 主键ID
 * @property string $animal 五行生肖
 * @property string $content 结果
 * @property string $year 年柱
 */
class Character extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'BaziCharacter',
        ];
    }

}
