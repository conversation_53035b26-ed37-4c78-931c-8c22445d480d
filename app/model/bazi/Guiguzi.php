<?php
// +----------------------------------------------------------------------
// | Guiguzi.鬼谷子两头钳-八字工具
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\bazi;

use app\model\BaseModel;

/**
 * Class app\model\bazi\Guiguzi
 * @property array $liuyun 批六运
 * @property array $pi 批语
 * @property int $id
 * @property string $gua 卦象
 * @property string $gui 鬼谷子批命
 * @property string $hgz 时干支
 * @property string $jie 解语
 * @property string $mingge 命格
 * @property string $short 其他
 * @property string $star 星辰
 * @property string $ytg 年干
 */
class Guiguzi extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'pi' => 'array',
                'liuyun' => 'array',
            ],
        ];
    }
}
