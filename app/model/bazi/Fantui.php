<?php
// +----------------------------------------------------------------------
// | Fantui. 八字反推表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\bazi;

use app\model\BaseModel;

/**
 * Class app\model\bazi\Fantui
 * @property array $gongli 日期
 * @property int $id
 * @property string $m 月干支
 * @property string $y 年干支
 */
class Fantui extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'Bazifantui',
            'type' => [
                'gongli' => 'array',
            ],
        ];
    }
}
