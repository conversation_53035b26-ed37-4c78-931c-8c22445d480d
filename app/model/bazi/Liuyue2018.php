<?php
// +----------------------------------------------------------------------
// | Liuyue2018. 2018流月运程
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/9/30
// +----------------------------------------------------------------------

namespace app\model\bazi;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class app\model\bazi\Liuyue2018
 * @property array $detail 详细
 * @property float $caiyun
 * @property float $health 健康运势指数
 * @property float $shiye
 * @property float $zonghe 综合运势指数
 * @property int $id
 * @property int $yue 月份
 * @property string $color 颜色
 * @property string $jiwei 吉位
 * @property string $sx 生肖
 * @property string $title
 */
class Liuyue2018 extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'BaziLiuyue2018',
            'type' => [
                'zonghe' => 'float',
                'health' => 'float',
                'caiyun' => 'float',
                'shiye' => 'float',
                'detail' => 'array',
            ],
        ];
    }

    /**
     * 查询流月数据
     * @param string $shengXiao 生肖
     * @param bool $isCache 缓存
     * @return array|bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info($shengXiao, $isCache = true)
    {
        $cacheKeyName = "bazi/Liuyue2018/info/{$shengXiao}";
        $data = cache($cacheKeyName);
        if (empty($data) || false === $isCache) {
            $list = self::where('sx', '=', $shengXiao)
                ->select();
            if ($list->isEmpty()) {
                return false;
            }
            $data = [];
            foreach ($list as $v) {
                $data[] = $v->visible(['yue', 'title', 'jiwei', 'color', 'zonghe', 'health', 'caiyun', 'shiye', 'detail'])->toArray();
            }
            cache($cacheKeyName, $data, 600);
        }
        return $data;
    }
}
