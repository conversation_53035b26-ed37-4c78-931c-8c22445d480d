<?php
// +----------------------------------------------------------------------
// | Wealth
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\bazi;

use app\model\BaseModel;

/**
 * Class app\model\bazi\Wealth
 * @property int $id
 * @property string $content 结果内容
 * @property string $curren_yeard 现在的年地支
 * @property string $result 总结
 * @property string $yeard 年地支
 */
class Wealth extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'BaziWealth',
        ];
    }
}
