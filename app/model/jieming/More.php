<?php
// +----------------------------------------------------------------------
// | More.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\jieming;

use app\model\BaseModel;

/**
 * Class app\model\jieming\More
 * @property int $id 主键ID
 * @property string $cause 事业
 * @property string $character 性格
 * @property string $health 健康
 * @property string $marriage 婚姻
 * @property string $night 晚景
 * @property string $three 三才属性
 */
class More extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'JiemingMore',
        ];
    }
}
