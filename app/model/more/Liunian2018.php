<?php
// +----------------------------------------------------------------------
// | Liunian2018.流年2018
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/10/11
// +----------------------------------------------------------------------

namespace app\model\more;

use app\model\BaseModel;

/**
 * Class app\model\more\Liunian2018
 * @property array $fortune 运势
 * @property int $id 自增ID
 * @property string $cause 事业
 * @property string $day 日柱
 * @property string $health 健康
 * @property string $marriage 感情
 * @property string $summary 年运总结
 * @property string $wealth 财运
 */
class Liunian2018 extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LiunianData2018',
            'type' => [
                'fortune' => 'array',
            ],
        ];
    }
}
