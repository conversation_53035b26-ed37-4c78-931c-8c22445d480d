<?php
// +----------------------------------------------------------------------
// | Dayunkoujue.流年大运口诀
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/10/11
// +----------------------------------------------------------------------

namespace app\model\more;

use app\model\BaseModel;

/**
 * Class app\model\more\Dayun
 * @property array $info 口诀
 * @property int $id
 * @property string $gz 干支
 */
class Dayun extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LiunianDayun',
            'type' => [
                'info' => 'json',
            ],
        ];
    }
}
