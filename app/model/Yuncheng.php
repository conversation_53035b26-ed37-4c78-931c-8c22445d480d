<?php
// +----------------------------------------------------------------------
// | Yuncheng.生肖运程接口
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class app\model\Yuncheng
 * @property array $caiyun 财运
 * @property array $ganqing 感情
 * @property array $shiye 事业
 * @property int $id
 * @property int $type 类型 0日 1月
 * @property string $shen 日干与流日干的十神
 * @property string $shensha 流日神煞
 */
class Yuncheng extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'shiye' => 'array',
                'caiyun' => 'array',
                'ganqing' => 'array',
            ],
        ];
    }

    /**
     * 获得运程数据
     * @param int $type 类型 0 日运势 1月运势
     * @param string $shensha type(0 流日神煞,1 小限神煞)
     * @param string $shen type(0 日干与流年干的十神,1 月限神煞)
     * @param bool $isCache
     * @return array|bool|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info($type, $shensha, $shen, $isCache = true)
    {
        $cacheKeyName = "model/Yuncheng/{$type}-{$shensha}-{$shen}";
        $cache = cache($cacheKeyName);
        if (empty($cache) || false == $isCache) {
            $info = self::where('type', '=', $type)
                ->where('shensha', '=', $shensha)
                ->where('shen', '=', $shen)
                ->field(['id', 'shiye', 'caiyun', 'ganqing'])
                ->find();
            if (empty($info)) {
                return false;
            }
            $cache = $info->toArray();
            cache($cacheKeyName, serialize($cache), 300);
        } else {
            $cache = unserialize($cache);
        }
        return $cache;
    }

    /**
     * 获得日运程数据
     * @param string $shenSha
     * @param string $shen
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getDayInfo(string $shenSha, string $shen): array
    {
        $list = self::where('type', 0)
            ->field('id,type,shensha,shen,shiye,caiyun,ganqing')
            ->cache(600)
            ->select();
        $result = [];
        foreach ($list as $v) {
            if ($v['shensha'] == $shenSha && $v['shen'] == $shen) {
                $result = $v->toArray();
                break;
            }
        }
        return $result;
    }
}
