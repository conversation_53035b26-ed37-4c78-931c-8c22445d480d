<?php
// +----------------------------------------------------------------------
// | Duanyu.八字断语
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\Duanyu
 * @property int $catid 类型
 * @property int $id
 * @property int $sex 性别 0 男 1 女 2不限
 * @property string $content 内容
 * @property string $create_time 创建时间
 * @property string $keyword 标识
 * @property string $source 来源
 * @property string $title 算法标题
 * @property string $update_time 更新时间
 */
class Duanyu extends BaseModel
{
    /**
     * 根据分类获得断语列表
     * @param int $catId 分类Id
     * @param bool $isCache 是否缓存
     * @return array|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getLists(int $catId, bool $isCache = true)
    {
        $cacheKeyName = "model/Duanyu/getLists/{$catId}";
        $list = Cache::get($cacheKeyName, false);
        if (false === $list || false === $isCache) {
            $data = self::where('catid', '=', $catId)
                ->field('id,catid,keyword,title,sex,content')
                ->select();
            $list = [];
            foreach ($data as $info) {
                $key1 = $info['title'];
                if (empty($key1)) {
                    continue;
                }
                $sex = $info['sex'];
                $list[$key1][$sex][] = $info['content'];
            }
            Cache::set($cacheKeyName, $list, 600);
        }
        return $list;
    }
}
