<?php

// +----------------------------------------------------------------------
// | ToolBbqm 宝宝取名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 陈志云
// +----------------------------------------------------------------------

namespace app\model\babynamed;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class app\model\babynamed\ToolBbqm
 * @property int $id
 * @property int $sort 排序
 * @property string $family
 * @property string $gender
 * @property string $namelist
 */
class ToolBbqm extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'sort' => 'integer',
                'gender' => 'integer',
            ],
        ];
    }

    /**
     * 状态获取器
     * @param $value
     * @return string
     */
    public function getGenderAttr($value)
    {
        return $value == 0 ? '男' : '女';
    }

    /**
     * 返回姓名ID映射
     * @param $family
     * @param int $gender 性别 男 0 女 1
     * @return self
     */
    public static function ifNamExists($family, $gender)
    {
        return self::cache(300)->where('family', $family)->where('gender', $gender)->value('id');
    }

    /**
     * 根据姓名ID和性别搜索名字大全
     * @param int $id 姓名ID
     * @param int $gender 性别 男 0 女 1
     * @return array|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getNameList($id, $gender)
    {
        return self::cache(300)
            ->where('id', $id)
            ->where('gender', $gender)
            ->find();
    }
}
