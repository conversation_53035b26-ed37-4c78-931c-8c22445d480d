<?php
// +----------------------------------------------------------------------
// |  QmjyUse
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\QmjyUse
 * @property int $id
 * @property int $jingxuan 精选 0否 1是
 * @property int $listorder 排序
 * @property int $sex 性别 0 男 1 女 2 不限
 * @property int $stype 宜用范围 1 音标 2 非音标 3 汉字组合
 * @property int $typem 名字属性 1【人名】2【企业名字】3【店铺名字】
 * @property int $wx 五行
 * @property string $create_time 创建时间
 * @property string $explain 用名解释
 * @property string $py 拼音带音标
 * @property string $py1 原拼音（带音标）
 * @property string $py2 拼音不带音标
 * @property string $reason 宜用原因
 * @property string $title 汉字组合
 * @property-read array $py2_name
 * @property-read array $py_name
 */
class QmjyUse extends BaseModel
{
    /**
     * 获得带音标拼音
     * @return array
     */
    protected function getPyNameAttr(): array
    {
        $py = $this->py;
        return $py ? explode('|', $py) : [];
    }

    /**
     * 获得不带音标的拼音
     * @return array
     */
    protected function getPy2NameAttr()
    {
        $py = $this->py2;
        return $py ? explode('|', $py) : [];
    }
}
