<?php
// +----------------------------------------------------------------------
// |  QmjyZi 起名建议宜用字库
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\lib\WxAttr;
use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\QmjyZi
 * @property int $bihua 简体笔画
 * @property int $bihuakx 康熙笔画
 * @property int $id
 * @property int $pos 位置 0不限
 * @property int $sex 性别 0 男 1 女 2 不限
 * @property int $stype 使用属性 1人名, 2 企业名字, 3 店铺名字, 12 人名+企业名字, 13 人名+店铺名字, 23 企业名字+店铺名字, 123 人名+企业名字+店铺名字
 * @property int $wx 五行 0无 1金 2 木 3 水 4 火 5 土
 * @property string $big5 繁体字
 * @property string $bushou 部首
 * @property string $explain 用字解释
 * @property string $py 拼音带音标
 * @property string $py1 原始拼音(带音标)
 * @property string $py2 拼音不带音标
 * @property string $zi 简体字
 * @property-read array $py2_name
 * @property-read array $py_name
 * @property-read string $sex_name
 * @property-read string $stype_name
 * @property-read string $wx_name
 */
class QmjyZi extends BaseModel
{
    /**
     * 使用属性
     * @var string[]
     */
    public static array $stypeList = [
        1 => '人名',
        2 => '企业名字',
        3 => '店铺名字',
        12 => '人名+企业名字',
        13 => '人名+店铺名字',
        23 => '企业名字+店铺名字',
        123 => '人名+企业名字+店铺名字',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'id' => 'integer',
                'sex' => 'integer',
                'bihua' => 'integer',
                'bihuakx' => 'integer',
                'wx' => 'integer',
                'pos' => 'integer',
                'stype' => 'integer',
            ],
        ];
    }

    /**
     * 性别
     * @return string
     */
    protected function getSexNameAttr(): string
    {
        $sex = $this->sex;
        $list = [0 => '男', 1 => '女', 2 => '不限'];
        return $list[$sex] ?? '未知';
    }

    /**
     * 获得五行名
     * @return string
     */
    protected function getWxNameAttr(): string
    {
        $list = WxAttr::getNumWxArr();
        $value = $this->getData('wx');
        return $list[$value] ?? '无';
    }

    /**
     * 获得使用属性名
     * @return string
     */
    protected function getStypeNameAttr(): string
    {
        $stype = $this->stype;
        return self::$stypeList[$stype] ?? '未知';
    }

    /**
     * 获得拼音带音标
     * @return array
     */
    protected function getPyNameAttr(): array
    {
        $py = $this->py;
        return self::pyStrToArray($py);
    }

    /**
     * 获得拼音不带音标
     * @return array
     */
    protected function getPy2NameAttr(): array
    {
        $py = $this->py2;
        return self::pyStrToArray($py);
    }

    /**
     * 拼音字符串转数组
     * @param string $str
     * @return array
     */
    public static function pyStrToArray(string $str): array
    {
        $str = trim($str);
        if (empty($str)) {
            return [];
        }
        $str = str_replace(['、', '，', ' '], ',', $str);
        return explode(',', $str);
    }
}
