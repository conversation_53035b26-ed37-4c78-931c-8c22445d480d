<?php
// +----------------------------------------------------------------------
// | Cnword. 中文字典
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/8/31
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\lib\qumingdafen\Bihua;
use app\model\BaseModel;
use Overtrue\Pinyin\Pinyin;
use think\App;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\baobaoqm\Cnword
 * @property array $detail3 扩展参数
 * @property bool $popular 常用 0 否 1 是
 * @property int $bihua 简体笔画
 * @property int $bihua1 繁体笔画
 * @property int $bihua2 康熙繁体笔画
 * @property int $code
 * @property int $id
 * @property int $jiegou 结构 1：上三包围 2：上下 3：上中下 4：下三包围 5：全包围 6：单一 7：右上包围 8：品字 9：左上包围 10：左下包围 11：左中右 12：左右 13：独体 14：镶嵌
 * @property string $big5 繁体字
 * @property string $bushou 部首
 * @property string $detail 基本解释
 * @property string $detail2 详细解释
 * @property string $py 拼音
 * @property string $py2 拼音含标点
 * @property string $shengmu 声母
 * @property string $wubi 五笔
 * @property string $wx 五行属性
 * @property string $yunmu 韵母
 * @property string $zi 简体字包含异形字
 * @property string $zi2 汉字
 * @property-read string $jiegou_name
 */
class Cnword extends BaseModel
{
    /**
     * 结构列表
     * @var string[]
     */
    public static $listJieGou = [
        1 => '上三包围', 2 => '上下', 3 => '上中下', 4 => '下三包围', 5 => '全包围',
        6 => '单一', 7 => '右上包围', 8 => '品字', 9 => '左上包围', 10 => '左下包围',
        11 => '左中右', 12 => '左右', 13 => '独体', 14 => '镶嵌',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'code' => 'integer',
                'bihua' => 'integer',
                'bihua2' => 'integer',
                'wubi' => 'string',
                'detail' => 'string',
                'detail2' => 'string',
                'detail3' => 'array',
            ],
        ];
    }

    /**
     * 获得结构名
     * @return string
     */
    protected function getJiegouNameAttr()
    {
        $value = $this->jiegou;
        return self::$listJieGou[$value] ?? '未知';
    }

    /**
     * 查字信息
     * @param string $zi
     * @param bool $isCache
     * @return array|mixed|object|App
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $zi, bool $isCache = true)
    {
        if (empty($zi)) {
            return [];
        }
        $cacheKeyName = self::getCacheKeyName($zi);
        $data = cache($cacheKeyName);
        if (empty($data) || false === $isCache) {
            $info = self::where('zi', '=', $zi)
                ->field('zi, zi2, big5, bihua, bihua2,bushou,jiegou, py, py2, wx, detail,detail3')
                ->find();
            if (empty($info)) {
                return [];
            }
            $data = $info->toArray();
            $data['detail'] = preg_replace('#(\s*<br\s*/?>\s*){2,}#', '<br>', $data['detail']);
            cache($cacheKeyName, $data, 3600);
        }
        return $data;
    }

    /**
     * 获得字的缓存名
     * @param string $zi
     * @return string
     */
    public static function getCacheKeyName(string $zi): string
    {
        return "model/baobaoqm/Cnword/{$zi}";
    }

    /**
     * 根据字数组获得字的信息
     * @param array $arr
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getInfo2(array $arr): array
    {
        $res = [];
        foreach ($arr as $k => $v) {
            $cacheKeyName = self::getCacheKeyName($v);
            $data = Cache::get($cacheKeyName, false);
            if (false !== $data) {
                $res[$v] = $data;
                unset($arr[$k]);
            } else {
                $res[$v] = [];
            }
        }
        $arr = array_values($arr);
        if (empty($arr)) {
            return $res;
        }
        $list = self::where('zi', 'in', $arr)
            ->field('zi, zi2, big5, bihua, bihua2,bushou,jiegou, py, py2, wx, detail,detail3')
            ->select();
        $arr1 = [];
        foreach ($list as $info) {
            $arr1[] = $info['zi'];
            $data = $info->toArray();
            $data['detail'] = preg_replace('#(\s*<br\s*/?>\s*){2,}#', '<br>', $data['detail']);
            $cacheKeyName = self::getCacheKeyName($data['zi']);
            Cache::set($cacheKeyName, $data, 3600);
            $res[$data['zi']] = $data;
        }
        $arr1 = array_keys($res);
        $arr2 = array_diff($arr, $arr1);
        if ($arr2) {
            $modelBihua = new Bihua();
            foreach ($arr2 as $v) {
                $cacheKeyName = self::getCacheKeyName($v);
                $tmpBiHua = (int)$modelBihua->find($v);
                $tmpBiHua = $tmpBiHua > 0 ? $tmpBiHua : 10;
                $data = [
                    'zi' => $v,
                    'zi2' => $v,
                    'big5' => $v,
                    'bihua' => $tmpBiHua,
                    'bihua2' => $tmpBiHua,
                    'bushou' => '',
                    'jiegou' => 0,
                    'py' => Pinyin::sentence($v, 'none')->join(),
                    'py2' => Pinyin::sentence($v)->join(''),
                    'wx' => '',
                    'detail' => '',
                    'detail3' => [],
                ];
                $res[$v] = $data;
                Cache::set($cacheKeyName, $data, 3600);
            }
        }
        return $res;
    }
}
