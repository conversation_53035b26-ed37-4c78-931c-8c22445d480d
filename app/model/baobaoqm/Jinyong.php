<?php
// +----------------------------------------------------------------------
// |  Jinyong
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\Jinyong
 * @property int $id
 * @property int $typem 名字属性 1【人名】2【企业名字】3【店铺名字】
 * @property string $mings 要禁用的名字，多个逗号隔开
 * @property string $py 拼音含音标
 * @property string $py_no 拼音不含音标
 */
class Jinyong extends BaseModel
{
}
