<?php
// +----------------------------------------------------------------------
// | LunyuDetail 论语名详情模型
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\LunyuDetail
 * @property int $id
 * @property int $mid 论语名id
 * @property string $author 作者
 * @property string $books 书名
 * @property string $chapters 章节
 * @property string $explain 释义
 * @property string $source 出处
 * @property string $yuyi 寓意
 */
class LunyuDetail extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'id' => 'integer',
                'mid' => 'integer',
            ],
        ];
    }
}
