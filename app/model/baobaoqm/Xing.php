<?php
// +----------------------------------------------------------------------
// | Xing. 姓氏表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/8/31
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\Xing
 * @property int $id
 * @property int $num 名字数
 * @property string $detail 具体统计
 * @property string $title 姓
 */
class Xing extends BaseModel
{
    /**
     * 获得名字列表
     * @return array
     */
    public static function getlists(): array
    {
        return self::cache(600)->column('title', 'id');
    }
}
