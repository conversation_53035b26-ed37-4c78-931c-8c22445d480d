<?php
// +----------------------------------------------------------------------
// | Ming3. 名字列表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/8/31
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\lib\WxAttr;
use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class app\model\baobaoqm\Ming3
 * @property int $gender 性别 0男 1 女 2不限
 * @property int $id
 * @property int $status 状态 1 启用 0 禁用
 * @property int $stype 类型  0宜 1 忌 2不限
 * @property int $wx 五行 1金2木3水4火5土
 * @property string $bushou 字1部首
 * @property string $bushou2 字2部首
 * @property string $xing 姓ID列表id以两个|隔开
 * @property string $zi 字1
 * @property string $zi2 字2
 * @property-read array $xing_ids
 * @property-read array $xing_text
 * @property-read string $gender_name
 * @property-read string $stype_text
 * @property-read string $wx_name
 */
class Ming3 extends BaseModel
{
    /**
     * 名五行名
     * @return string
     */
    protected function getWxNameAttr(): string
    {
        $wx = $this->getData('wx');
        return WxAttr::getNumToWx($wx);
    }

    /**
     * 获得宜忌分组名
     * @return string
     */
    protected function getStypeTextAttr(): string
    {
        $type = $this->getData('stype');
        $arr = [0 => '宜', 1 => '忌', 2 => '不限'];
        return $arr[$type] ?: $arr[2];
    }

    /**
     * 获得适宜分类名称数组
     * @return array
     */
    protected function getXingTextAttr(): array
    {
        $yi = $this->getData('xing');
        if (empty($yi)) {
            return [];
        }
        $yi = trim($yi, '|');
        $arr = explode('|', $yi);
        $res = [];
        $cat = Xing::getlists();
        foreach ($arr as $v) {
            if (!isset($cat[$v])) {
                continue;
            }
            $res[] = $cat[$v] ?? '';
        }
        return $res;
    }

    /**
     * 获得适宜分类id数组
     * @return array
     */
    protected function getXingIdsAttr(): array
    {
        $yi = $this->getData('xing');
        if (empty($yi)) {
            return [];
        }
        $cat = Xing::getlists();
        $ids = array_keys($cat);
        $yi = trim($yi, '|');
        $yiArr = explode('|', $yi);
        $res = array_intersect($ids, $yiArr);
        return array_values($res);
    }

    /**
     * 性别
     * @return string
     */
    protected function getGenderNameAttr(): string
    {
        $gender = $this->getData('gender');
        $list = ['男', '女', '不限'];
        return $list[$gender] ?? '其它';
    }

    /**
     * 获得
     * @param array $wxArr
     * @param array $genderArr
     * @param int $localtime
     * @param int $limit
     * @return array
     */
    public static function lists(array $wxArr, array $genderArr, int $localtime = 0, int $limit = 1000)
    {
        $list = [];
        foreach ($wxArr as $k => $v) {
            $wxls = self::where('wx', $v)
                ->whereIn('gender', $genderArr)
                ->where('status', 1)
                ->cache(3600)
                ->orderRand()
                ->limit($limit)
                ->select();
            if ($wxls->isEmpty()) {
                continue;
            }
            $list = array_merge($list, $wxls->toArray());
        }
        shuffle($list);
        $list = array_slice($list, 0, $limit);
        return $list;
    }

    /**
     * 获得名字总数
     * @param array $wxArr
     * @param array $genderArr
     * @return int
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function listbyWx(array $wxArr, array $genderArr)
    {
        $list = Ming3Num::lists();
        $num = 0;
        foreach ($wxArr as $wx) {
            foreach ($genderArr as $gender) {
                $tmpKey = "{$gender}|{$wx}|1";
                $tmpNum = $list[$tmpKey] ?? 0;
                $num += $tmpNum;
            }
        }
        return $num;
    }
}
