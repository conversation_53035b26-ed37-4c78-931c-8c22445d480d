<?php
// +----------------------------------------------------------------------
// | Industry
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\baobaoqm\Industry
 * @property int $five_line
 * @property int $id
 * @property int $num2 2个字数
 * @property int $num3 3个字数
 * @property int $num4 4个字数
 * @property int $pid 父ID
 * @property string $pinyin 拼音
 * @property string $title 名字
 * @property string $wx 五行
 * @property-read string $pid_name
 */
class Industry extends BaseModel
{
    /**
     * 顶级分类
     * @var string[]
     */
    public static array $topList = [
        9 => '居民服务业', 19 => '批发业', 20 => '零售业', 21 => '装饰公司装饰设计公司', 22 => '商务服务业', 23 => '房屋和土木工程建筑业', 24 => '文化艺术业',
        25 => '通信设备、计算机及其他电子设备厂', 26 => '餐饮业', 27 => '住宿业', 28 => '农业', 29 => '渔业', 31 => '交通运输设备厂', 32 => '非金属矿物制品业',
        33 => '其他商务公司', 34 => '食品厂', 35 => '金属制品业', 36 => '纺织业', 37 => '货运物流', 38 => '化学原料及化学制品厂', 39 => '纺织服装、鞋、帽厂',
        40 => '工艺品及其他厂', 41 => '科技交流和推广服务业', 42 => '房地产中介公司', 44 => '塑料制品业', 45 => '计算机服务业', 46 => '电气机械及器材厂', 47 => '木材加工及木竹藤棕草制品业',
        48 => '物业管理公司', 49 => '租赁业', 50 => '专用设备厂', 51 => '软件业', 52 => '农副食品加工厂', 53 => '通用设备厂', 54 => '美容美发', 55 => '银行业',
        56 => '家具厂', 57 => '专业技术服务业', 58 => '建筑安装业', 59 => '造纸及纸制品业', 60 => '畜牧业', 61 => '其他金融业', 62 => '卫生', 63 => '皮革毛皮羽毛(绒)及其制品业',
        64 => '饮料厂', 65 => '装卸搬运和其他运输服务业', 66 => '医药厂', 67 => '小学', 68 => '电信和其他信息传输服务业', 69 => '仪器仪表及文化、办公用机械厂', 71 => '修理与维护',
        72 => '文教体育用品厂', 73 => '娱乐业', 74 => '其他服务业', 75 => '印刷业和记录媒介复制业', 76 => '农、林、牧、渔服务业', 77 => '非金属矿', 79 => '有色金属冶炼及压延加工厂',
        81 => '保洁公司', 82 => '林业', 83 => '研究与试验发展', 84 => '黑色金属冶炼及压延加工厂', 85 => '煤炭开采和洗选业', 86 => '废弃资源和废旧材料回收加工厂', 87 => '橡胶制品厂',
        89 => '环境管理业', 90 => '其他教育', 91 => '体育', 92 => '石油和天然气开采业', 93 => '仓储业', 94 => '石油加工炼焦及核燃料加工厂', 95 => '燃气生产和供应业', 96 => '水上运输业',
        97 => '证券业', 98 => '铁路运输业', 99 => '婚介礼仪', 100 => '水的生产和供应业', 101 => '家政公司', 102 => '城市公共交通业', 103 => '干洗店', 104 => '新闻出版业',
        105 => '婚纱摄影数码冲印', 106 => '洗浴按摩场所', 108 => '广播、电视、电影和音像业', 111 => '航空运输业', 112 => '水利管理业', 114 => '有色金属矿', 119 => '地质勘查业',
        122 => '烟草制品业', 124 => '其他房地产活动',
    ];

    /**
     * 父分类名
     * @return string
     */
    protected function getPidNameAttr(): string
    {
        $pid = $this->pid;
        return self::$topList[$pid] ?? '';
    }

    /**
     * 获得所有分类
     * @param bool $isCache
     * @return array|mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getLists(bool $isCache = true)
    {
        $cacheKeyName = 'industry/lists_all';
        $result = Cache::get($cacheKeyName, false);
        if (false === $result || false === $isCache) {
            $list = self::field('id,pid,wx,title,pinyin')
                ->append(['pid_name'])
                ->select();
            $result = $list->isEmpty() ? [] : $list->toArray();
            Cache::set($cacheKeyName, $result, 300);
        }
        return $result;
    }

    /**
     * 获得行业信息
     * @param int $id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(int $id): array
    {
        $list = self::getLists();
        $list = array_column($list, null, 'id');
        return $list[$id] ?? [];
    }
}
