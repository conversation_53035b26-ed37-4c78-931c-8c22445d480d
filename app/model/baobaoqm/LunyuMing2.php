<?php
// +----------------------------------------------------------------------
// |  LunyuMing2
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;
use think\model\relation\HasMany;

/**
 * Class app\model\baobaoqm\LunyuMing2
 * @property int $gender 性别 0 男 1 女 2 不限
 * @property int $id
 * @property int $jingxuan 精选 0 否 1 是
 * @property int $listorder 排序
 * @property string $create_time 创建时间
 * @property string $ming 名字
 * @property string $update_time 更新时间
 * @property string $wx 五行
 * @property string $xings 推荐姓氏
 * @property-read \app\model\baobaoqm\LunyuDetail[] $detail
 */
class LunyuMing2 extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'gender' => 'integer',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 一对多关联详情
     * @return HasMany
     */
    public function detail()
    {
        return $this->hasMany(LunyuDetail::class, 'mid', 'id');
    }
}
