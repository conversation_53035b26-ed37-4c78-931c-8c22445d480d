<?php
// +----------------------------------------------------------------------
// |  Ming3Num ming3按性别，五行 状态的统计数据
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\baobaoqm\Ming3Num
 * @property int $gender 性别 0男 1 女 2不限
 * @property int $id
 * @property int $num 名字统计数
 * @property int $status 状态
 * @property int $wx 五行 1金2木3水4火5土
 */
class Ming3Num extends BaseModel
{
    /**
     * 获得名字数据
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function lists()
    {
        $cacheKeyName = 'model/ming3_num/lists';
        $result = Cache::get($cacheKeyName, false);
        if (false === $result) {
            $list = self::where('status', 1)->select();
            $result = [];
            foreach ($list as $v) {
                $tmpKey = "{$v['gender']}|{$v['wx']}|{$v['status']}";
                $result[$tmpKey] = $v['num'];
            }
            Cache::set($cacheKeyName, $result, 1200);
        }
        return $result;
    }
}
