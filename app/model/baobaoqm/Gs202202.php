<?php
// +----------------------------------------------------------------------
// |  Gs202202 公司名 （2022-08）
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\lib\Utils;
use app\lib\WxAttr;
use app\model\astro\Region;
use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * Class app\model\baobaoqm\Gs202202
 * @property int $catwx 可使用的行业五行
 * @property int $fen_gua 卦象分
 * @property int $fen_sc 三才五格分
 * @property int $id
 * @property int $sex 性别 0 男 1 女 2不限
 * @property int $type 行业 0 宜 1忌 2 不限
 * @property int $type2 城市 0 宜 1忌 2 不限
 * @property int $users 用户群体 0男 1 女 2 不限
 * @property int $wx 五行
 * @property string $areas 城市id列表
 * @property string $cats 分类id
 * @property string $ming 名字
 * @property-read array $areas_ids
 * @property-read array $areas_name
 * @property-read array $cats_name
 * @property-read array $cats_no
 * @property-read string $catwx_name
 * @property-read string $sex_name
 * @property-read string $type2_name
 * @property-read string $type_name
 * @property-read string $wx_name
 */
class Gs202202 extends BaseModel
{
    /**
     * 情别
     * @var string[]
     */
    public static array $sexList = [
        0 => '男', 1 => '女', 2 => '不限',
    ];

    /**
     * 类型
     * @var string[]
     */
    public static array $typeList = [
        0 => '宜', 1 => '忌', 2 => '不限',
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'catid' => 'integer',
                'sex' => 'integer',
                'wx' => 'integer',
                'type' => 'integer',
                'fen_sc' => 'integer',
                'fen_gua' => 'integer',
                'type2' => 'integer',
            ],
        ];
    }

    /**
     * 性别
     * @return string
     */
    protected function getSexNameAttr(): string
    {
        $sex = $this->sex;
        return self::$sexList[$sex] ?? '未知';
    }

    /**
     * 获得五行中文
     * @return string
     */
    protected function getWxNameAttr(): string
    {
        $wx = $this->wx;
        return WxAttr::getNumToWx($wx);
    }

    /**
     * 分类五行
     * @return string
     */
    protected function getCatwxNameAttr(): string
    {
        $value = $this->catwx;
        return WxAttr::getNumToWx($value);
    }

    /**
     * 行业宜忌
     * @return string
     */
    protected function getTypeNameAttr(): string
    {
        $sex = $this->sex;
        return self::$typeList[$sex] ?? '未知';
    }

    /**
     * 城市宜忌
     * @return string
     */
    protected function getType2NameAttr(): string
    {
        $sex = $this->sex;
        return self::$typeList[$sex] ?? '未知';
    }

    /**
     * 获得忌用行业id数组
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getCatsNoAttr(): array
    {
        $type = $this->type;
        if ($type == 2) {
            return [];
        }
        $cats = $this->cats;
        $catArr = $cats ? explode(',', $cats) : [];
        if (empty($catArr)) {
            return [];
        }
        if ($type == 1) {
            return $catArr;
        }
        $catWx = $this->catwx;
        $catWx = WxAttr::getNumToWx($catWx);
        $catWxArr = Utils::mbStrSplit($catWx);
        $listInd = Industry::getLists();
        $result = [];
        foreach ($listInd as $rs) {
            if (!in_array($rs['wx'], $catWxArr)) {
                continue;
            }
            if (!in_array($rs['id'], $catArr)) {
                $result[] = $rs['id'];
            }
        }
        return $result;
    }

    /**
     * 获得城市ids
     * @return array
     */
    protected function getAreasIdsAttr(): array
    {
        $area = $this->areas;
        $type2 = $this->type2;
        if ($type2 == 2 || empty($area)) {
            return [];
        }
        return explode(',', $area);
    }

    /**
     * 获得地区名列表
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getAreasNameAttr(): array
    {
        $area = $this->areas;
        $type2 = $this->type2;
        if ($type2 == 2 || empty($area)) {
            return [];
        }
        $arr = explode(',', $area);
        $list = Region::getAllList();
        $res = [];
        foreach ($arr as $code) {
            if (isset($list[$code])) {
                $res[] = $list[$code]['name'];
            }
        }
        return $res;
    }

    /**
     * 获得分类
     * @return array
     */
    protected function getCatsNameAttr(): array
    {
        $ids = $this->cats;
        $idsArr = $ids ? explode(',', $ids) : [];
        $list = Industry::getAllData();
        $list = array_column($list, null, 'id');
        $result = [];
        foreach ($idsArr as $v) {
            if (!isset($list[$v])) {
                continue;
            }
            $tmp = $list[$v];
            $result[] = $tmp['id'] . '|' . $tmp['wx'] . '|' . $tmp['title'];
        }
        return $result;
    }
}
