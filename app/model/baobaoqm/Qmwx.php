<?php
// +----------------------------------------------------------------------
// |  Qmwx
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\baobaoqm\Qmwx
 * @property int $dislove 不喜欢数
 * @property int $id
 * @property int $jin 禁用库过滤 0 无 1 排除
 * @property int $love 喜欢数
 * @property int $num 输出数
 * @property int $rate 喜欢率  喜欢数除输出数
 * @property int $sex 性别 0 男 1 女
 * @property int $status 状态 0禁用 1启用
 * @property int $wx 五行 1金 2木 3水 4火 5土
 * @property string $explain 用名解释
 * @property string $py 拼音
 * @property string $py1 原拼音
 * @property string $py_no 拼音不带音标
 * @property string $title 名字
 */
class Qmwx extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'id' => 'integer',
                'sex' => 'integer',
                'status' => 'integer',
                'wx' => 'integer',
                'love' => 'integer',
                'dislove' => 'integer',
                'num' => 'integer',
                'rate' => 'integer',
            ],
        ];
    }

    /**
     * 根据五行和性别随机获得数据
     * @param array $wxNum
     * @param int $sex
     * @param int $num
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function lists(array $wxNum, int $sex, int $num)
    {
        sort($wxNum);
        $cacheKeyName = "model/wxqm/{$sex}|" . implode('_', $wxNum);
        $list = Cache::get($cacheKeyName, false);
        $where = [
            ['status', '=', 1],
            ['wx', 'in', $wxNum],
            ['sex', '=', $sex],
        ];
        if (false === $list) {
            $count = self::where($where)->cache(3600)->count('id');
            $limit = 2000;
            $maxPage = ceil($count / $limit);
            $pageId = $maxPage <= 1 ? 1 : rand(1, ($maxPage - 1));
            $start = ($pageId - 1) * $limit;
            $limit = (($maxPage - 1) == $pageId) ? $limit * 2 : $limit;
            $subSql = self::where($where)
                ->field('id')
                ->limit($start, 1)
                ->order(['id' => 'asc'])
                ->buildSql();
            $list = self::where($where)
                ->whereRaw("id >= ({$subSql})")
                ->field('id,title,jin,sex,wx,py,py_no,py1,explain')
                ->limit($limit)
                ->order(['id' => 'asc'])
                ->select();
            $list = $list->where('jin', 0)->toArray();
            Cache::set($cacheKeyName, $list, 600);
        }
        shuffle($list);
        return array_slice($list, 0, $num);
    }
}
