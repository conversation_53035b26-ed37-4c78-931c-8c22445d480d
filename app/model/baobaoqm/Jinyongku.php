<?php
// +----------------------------------------------------------------------
// |  JinyongKu 禁用组合库
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\baobaoqm;

use app\model\BaseModel;

/**
 * Class app\model\baobaoqm\Jinyongku
 * @property int $id
 * @property int $stype 禁用范围 1 音标 2 非音标 3 汉字组合
 * @property int $typem 名字属性 1【人名】2【企业名字】3【店铺名字】
 * @property string $create_time 创建时间
 * @property string $py 拼音含音标
 * @property string $py2 拼音
 * @property string $reason 原因
 * @property string $title 名字列表
 * @property string $update_time 更新时间
 */
class Jinyongku extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                // stype 禁用范围 1 音标 2 非音标 3 汉字组合
                'stype' => 'integer',
                // typem 名字属性 1【人名】2【企业名字】3【店铺名字】
                'typem' => 'integer',
            ],
        ];
    }
}
