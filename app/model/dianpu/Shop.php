<?php
// +----------------------------------------------------------------------
// | ShopCat.店铺名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2021-05-19
// +----------------------------------------------------------------------

namespace app\model\dianpu;

use app\model\BaseModel;

/**
 * Class app\model\dianpu\Shop
 * @property int $hanwx 行业五行
 * @property int $id
 * @property int $wx 名五行
 * @property int $y_type 类型 宜 0 1 忌
 * @property string $ming 名字
 * @property string $yi 宜行业
 * @property-read array $yi_text
 */
class Shop extends BaseModel
{
    /**
     * 五行对应的数字
     * @var array
     */
    public static array $wxToNum = [
        '金' => 1, '木' => 2, '水' => 3, '火' => 4, '土' => 5,
    ];

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'hanwx' => 'integer',
                'wx' => 'integer',
                'y_type' => 'integer',
            ],
        ];
    }

    /**
     * 获得适宜分类id数组
     * @return array
     */
    public function getYiTextAttr(): array
    {
        $yi = $this->getData('yi');
        if (empty($yi)) {
            return [];
        }
        $yi = trim($yi, '|');
        return explode('|', $yi);
    }
}
