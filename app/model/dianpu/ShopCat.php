<?php
// +----------------------------------------------------------------------
// | ShopCat.店铺分类
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2021-05-19
// +----------------------------------------------------------------------

namespace app\model\dianpu;

use app\model\BaseModel;
use Psr\SimpleCache\InvalidArgumentException;

/**
 * Class app\model\dianpu\ShopCat
 * @property int $id
 * @property int $num2 2个字名字
 * @property int $num3 3个字名字
 * @property int $num_all 总名字数
 * @property string $py 拼音
 * @property string $title 名称
 * @property string $wx 五行
 * @property-read int|mixed $wx_num
 */
class ShopCat extends BaseModel
{
    /**
     * 获得五行对应数字
     * @return int|mixed
     */
    public function getWxNumAttr()
    {
        $wx = $this->getData('wx');
        $arr = Shop::$wxToNum;
        return $arr[$wx] ?? 0;
    }

    /**
     * 获得所有店铺分类
     * @param bool $bool
     * @return array
     * @throws InvalidArgumentException
     */
    public static function getLists(bool $bool = true)
    {
        $cacheKeyName = 'shopcat/lists';
        // 清理缓存
        if (!$bool) {
            self::master()
                ->getConnection()
                ->getCache()
                ->delete($cacheKeyName);
        }
        return ShopCat::where('id', '>', 0)
            ->limit(5000)
            ->cache($cacheKeyName, 600)
            ->column('title', 'id');
    }
}
