<?php
// +----------------------------------------------------------------------
// | Taluopai.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/8/10
// +----------------------------------------------------------------------

namespace app\model\taluopai;

use app\model\BaseModel;
use common\traits\model\CacheInfoTraits;

/**
 * Class app\model\taluopai\Taluopai
 * @property array $d 牌意
 * @property array $f0 逆位男
 * @property array $f1 逆位女
 * @property array $z0 正位男
 * @property array $z1 正位反
 * @property int $id
 * @property string $key 关键字
 * @property string $title 牌标题
 */
class Taluopai extends BaseModel
{
    use CacheInfoTraits;

    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'd' => 'array',
                'z0' => 'array',
                'z1' => 'array',
                'f0' => 'array',
                'f1' => 'array',
            ],
        ];
    }
}
