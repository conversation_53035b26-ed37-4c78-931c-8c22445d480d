<?php
// +----------------------------------------------------------------------
// | HuangliMing 黄历名词
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\huangli;

use app\model\BaseModel;

/**
 * Class app\model\huangli\Ming
 * @property int $catid 分组id
 * @property int $id
 * @property int $jx 吉凶 0不限 1 吉 2凶
 * @property string $ji 忌规则
 * @property string $jieshi 别称 解释
 * @property string $suanfa 算法
 * @property string $title 名词
 * @property string $yi 宜规则
 */
class Ming extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'HuangliMing',
            'type' => [
                'catid' => 'integer',
                'jx' => 'integer',
            ],
        ];
    }
}
