<?php
// +----------------------------------------------------------------------
// | Holiday.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\rili;

use app\model\BaseModel;

/**
 * Class app\model\rili\Holiday
 * @property int $id
 * @property int $listorder 排序
 * @property int $type 类型
 * @property int $year 年份
 * @property string $activity 各地活动
 * @property string $create_time 创建时间
 * @property string $d 日期
 * @property string $des 简介
 * @property string $end_time 结束时间
 * @property string $source 来源
 * @property string $start_time 开始时间
 * @property string $title 节日名
 * @property string $update_time 更新时间
 * @property string $work 推广活动
 * @property-read int $number_day
 */
class Holiday extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'year' => 'integer',
                'listorder' => 'integer',
                'start_time' => 'string',
                'end_time' => 'string',
                'create_time' => 'integer',
                'update_time' => 'integer',
            ],
        ];
    }

    /**
     * 获得放假天数
     * @return int
     */
    protected function getNumberDayAttr()
    {
        if (empty($this->end_time) || empty($this->start_time) || $this->end_time == $this->start_time) {
            return 0;
        }
        return (int)((strtotime($this->end_time) - strtotime($this->start_time)) / 86400) + 1;
    }

    /**
     * 根据年份查找数据
     * @param int $year 年份
     * @param bool $isCache 缓存
     * @return array
     */
    public static function infoByYear(int $year, bool $isCache = true)
    {
        $cacheKeyName = "Holiday/infoByYear/{$year}";
        // 清理缓存
        if (!$isCache) {
            self::master()
                ->getConnection()
                ->getCache()
                ->delete($cacheKeyName);
        }
        $list = self::where('year', $year)
            ->order('d asc,listorder desc')
            ->cache($cacheKeyName, 86400)
            ->column('id,title,d,start_time,end_time,listorder', 'id');
        foreach ($list as $k => $v) {
            $list[$k]['number_day'] = (int)(((strtotime($v['end_time']) - strtotime($v['start_time'])) / 86400) + 1);
        }
        return $list;
    }
}
