<?php
// +----------------------------------------------------------------------
// | Region.区域表 查经纬度
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\astro;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;
use think\model\Collection;

/**
 * Class app\model\astro\Region
 * @property int $code
 * @property int $parent_code 省份code
 * @property string $lat 纬度
 * @property string $lng 经度
 * @property string $name 地区名
 */
class Region extends BaseModel
{
    /**
     * 获得数据
     * @param string $name 地区名称
     * @param int $parentCode 父code
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $name, int $parentCode = 0): array
    {
        $list = self::getAllList();
        $obj = Collection::make($list);
        $data = $obj->where('parent_code', '=', $parentCode)
            ->where('name', '=', $name)
            ->toArray();
        if ($data) {
            $data = array_values($data);
            return $data[0];
        }
        return [];
    }

    /**
     * 获得当前数据信息
     * @param string $province
     * @param string $city
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getCityInfo(string $province, string $city): array
    {
        $provinceInfo = self::info($province, 0);

        if (empty($provinceInfo)) {
            return [];
        }
        $cityInfo = self::info($city, $provinceInfo['code']);

        $result = $provinceInfo;
        if ($cityInfo) {
            $result = $cityInfo;
        }
        $result['provinces'] = $provinceInfo['name'];
        return $result;
    }

    /**
     * 获得经纬度
     * @param string $provinces 省份
     * @param string $city 城市
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getlngLat(string $provinces, string $city): array
    {
        $info = self::getCityInfo($provinces, $city);
        if (empty($info)) {
            return [
                'lng' => 121.47,
                'lat' => 31.24,
            ];
        }
        return [
            'lng' => number_format($info['lng'], 2),
            'lat' => number_format($info['lat'], 2),
        ];
    }

    /**
     * 获得全部的省地区数据
     * @return array
     */
    public static function getAllList(): array
    {
        $cacheKeyName = 'model/Region/all';
        $data = Cache::get($cacheKeyName, []);
        if (empty($data)) {
            $data = self::order('code asc')
                ->column('code,name,parent_code,lng,lat', 'code');
            Cache::set($cacheKeyName, $data, 600);
        }
        return $data;
    }

    /**
     * 根据编码获得地区信息
     * @param string $code
     * @return array
     */
    public static function getInfoByCode(string $code): array
    {
        $list = self::getAllList();
        return $list[$code] ?? [];
    }

    /**
     * 根据code编码获得省地市数组
     * @param string $code
     * @return array
     */
    public static function getAreaByCode(string $code): array
    {
        $list = self::getAllList();
        $len = strlen($code);
        $res = [];
        for ($i = 0; $i < 6; $i = $i + 2) {
            $tmp = substr($code, 0, $i + 2);
            if (!isset($list[$tmp])) {
                continue;
            }
            $res[$tmp] = $list[$tmp];
        }
        if ($len > 6) {
            $tmp = substr($code, 0, 9);
            if (isset($list[$tmp])) {
                $res[$tmp] = $list[$tmp];
            }
            if (isset($list[$code])) {
                $res[$code] = $list[$code];
            }
        }
        return array_values($res);
    }
}
