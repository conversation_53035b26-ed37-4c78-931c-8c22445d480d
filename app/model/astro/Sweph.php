<?php
// +----------------------------------------------------------------------
// | Sweph.星历数据
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\astro;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\astro\Sweph
 * @property string $ceres 谷神星
 * @property string $chiron 凯龙星
 * @property string $d 日期
 * @property string $juno 婚神星
 * @property string $jupiter 木星
 * @property string $lilith 莉莉斯
 * @property string $mars 火星
 * @property string $mercury 水星
 * @property string $moon 月亮
 * @property string $neptune 海王星
 * @property string $node 北交点
 * @property string $pallas 智神星
 * @property string $pluto 冥王星
 * @property string $saturn 土星
 * @property string $southnode 南交点
 * @property string $sun 太阳
 * @property string $t 时间
 * @property string $uranus 天王星
 * @property string $venus 金星
 * @property string $vesta 灶神星
 */
class Sweph extends BaseModel
{
    /**
     * 当前日期数据
     * @param string $d 时间字符串 1990-01-01
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $d): array
    {
        $timeAmp = strtotime($d);
        $m = (int)date('m', $timeAmp);
        $y = (int)date('Y', $timeAmp);
        if ($m <= 6) {
            $start = "{$y}-01-01";
            $end = "{$y}-06-30";
        } else {
            $start = "{$y}-07-01";
            $end = "{$y}-12-31";
        }
        // $start = date('Y-01-01', $timeAmp);
        // $end = date('Y-12-31', $timeAmp);
        $cacheKeyName = "sweph/lists/{$start}";
        $list = Cache::get($cacheKeyName, false);
        if (false === $list) {
            $list = self::where('d', '>=', $start)
                ->where('d', '<=', $end)
                ->order('d asc')
                ->select()
                ->toArray();
            $list = array_column($list, null, 'd');
            Cache::set($cacheKeyName, $list, rand(600, 900));
        }

        return $list[$d] ?? [];
    }

    /**
     * 解析数据
     * @param $string
     * @return array
     */
    public static function getDegreeAndSign($string): array
    {
        $degrees = substr($string, 0, 2);
        $minutes = substr($string, 4, 2);
        $sign = substr($string, 2, 2);
        $sec = (int)substr($string, 6, 2);
        $r = substr($string, 8, 2);
        return [
            'deg' => $degrees,
            'min' => $minutes,
            'sign' => $sign,
            'sec' => $sec,
            'r' => $r,
        ];
    }
}
