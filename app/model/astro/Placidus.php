<?php
// +----------------------------------------------------------------------
// | Placidus.宫位表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\model\astro;

use app\model\BaseModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;

/**
 * Class app\model\astro\Placidus
 * @property array $detail 北纬度数下的值
 * @property int $sec 当地恒星时，秒数
 * @property string $secstr 当地恒星时
 * @property string $sign 天顶星座
 * @property string $signdeg 天顶度数
 * @property string $signdegs 天顶精确度数
 */
class Placidus extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'type' => [
                'detail' => 'array',
            ],
        ];
    }

    /**
     * 当前秒的数据
     * @param string $d 秒数
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function info(string $d): array
    {
        $listSec = [79702, 72524, 64800, 57076, 49860, 43200, 36502, 29324, 21600, 13876, 6698, 0];
        $searchD = 0;
        foreach ($listSec as $v) {
            if ($d >= $v) {
                $searchD = $v;
                break;
            }
        }
        $cacheKeyName = "plaicdus/lists30/{$searchD}";
        $list = Cache::get($cacheKeyName, false);
        if (false == $list) {
            $list = self::where('sec', '>=', $searchD)
                ->order('sec asc')
                ->limit(30)
                ->select()
                ->toArray();
            $list = array_column($list, null, 'sec');
            Cache::set($cacheKeyName, $list, rand(600, 700));
        }
        return $list[$d] ?? [];
    }

    /**
     * 当前秒的前后数据
     * @param int $second 秒数
     * @param int $lat 纬度
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getTwoInfo(int $second, int $lat): array
    {
        $latList = [];
        if ($lat > 60) {
            $lat = 60;
            $latList = ['b' => 60, 'a' => 0];
        } elseif ($lat < 5) {
            $lat = 0;
            $latList = ['b' => 0, 'a' => 5];
        } elseif ($lat < 10) {
            $lat = 5;
            $latList = ['b' => 5, 'a' => 10];
        } elseif ($lat < 15) {
            $lat = 10;
            $latList = ['b' => 10, 'a' => 15];
        } elseif ($lat < 20) {
            $lat = 15;
            $latList = ['b' => 15, 'a' => 20];
        } else {
            $latList = ['b' => $lat, 'a' => $lat + 1];
        }
        $numberList = [];
        if ($second >= 86180) {
            $numberList = [
                'b' => 86180,
                'a' => 0,
            ];
        } else {
            $list = [
                0, 220, 440, 661, 881, 1101, 1322, 1542, 1763, 1984, 2205, 2427, 2648, 2870, 3092, 3315, 3537, 3760, 3984, 4207, 4432, 4656, 4881, 5106, 5332, 5559, 5785, 6013, 6241, 6469, 6698, 6928, 7158, 7388, 7620, 7852, 8084, 8318, 8552, 8786, 9021, 9257, 9494, 9731, 9969, 10208, 10448, 10688, 10929, 11170, 11413, 11656, 11900, 12144, 12389, 12635, 12882, 13130, 13378, 13626, 13876, 14126, 14377, 14629, 14881, 15134, 15387, 15641, 15896, 16151, 16406, 16663, 16919, 17177, 17434, 17692, 17951, 18210, 18469, 18729, 18989, 19249, 19510, 19770, 20031, 20331, 20554, 20815, 21077, 21338, 21600, 21862, 22080, 22385, 22646, 22907, 23167, 23430, 23690, 23951, 24211, 24451, 24751, 24990, 25249, 25508, 25766, 26023, 26281, 26537, 26794, 27049, 27304, 27559, 27813, 28066, 28319, 28571, 28823, 29074, 29324, 29574, 29822, 30070, 30318, 30565, 30811, 31056, 31300, 31544, 31787, 32030, 32271, 32512, 32752, 32992, 33231, 33469, 33706, 33943, 34179, 34414, 34648, 34882, 35116, 35348, 35580, 35812, 36042, 36272, 36502, 36731, 36959, 37187, 37415, 37641, 37868, 38094, 38319, 38544, 38768, 38993, 39216, 39440, 39663, 39885, 40108, 40330, 40552, 40773, 40995, 41216, 41437, 41658, 41878, 42099, 42319, 42539, 42760, 42980, 43200, 43420, 43640, 43861, 44081, 44301, 44522, 44742, 44963, 45184, 45405, 45627, 45848, 46070, 46292, 46515, 46737, 46960, 47184, 47407, 47632, 47856, 48081, 48306, 48472, 48759, 48985, 49213, 49441, 49669, 49860, 50128, 50358, 50588, 50820, 51052, 51284, 51518, 51752, 51986, 52221, 52457, 52694, 52931, 53169, 53408, 53648, 53888, 54129, 54370, 54613, 54856, 55100, 55344, 55589, 55835, 56082, 56330, 56578, 56826, 57076, 57326, 57577, 57829, 58081, 58334, 58587, 58841, 59096, 59351, 59606, 59863, 60119, 60377, 60634, 60892, 61151, 61410, 61669, 61929, 62189, 62449, 62710, 62970, 63231, 63493, 63754, 64015, 64277, 64538, 64800, 65062, 65323, 65585, 65846, 66107, 66369, 66630, 66890, 67151, 67411, 67671, 67931, 68190, 68449, 68708, 68966, 69223, 69481, 69737, 69994, 70249, 70504, 70759, 71013, 71266, 71519, 71771, 72023, 72274, 72524, 72774, 73022, 73270, 73518, 73765, 74011, 74256, 74500, 74744, 74987, 75230, 75471, 75712, 75952, 76192, 76431, 76669, 76906, 77143, 77379, 77614, 77848, 78082, 78316, 78548, 78780, 79012, 79242, 79472, 79702, 79931, 80159, 80387, 80615, 80841, 81068, 81294, 81519, 81744, 81968, 82193, 82416, 82640, 82863, 83085, 83308, 83530, 83752, 83973, 84195, 84416, 84637, 84858, 85078, 85299, 85519, 85739, 85960, 86180,
            ];
            foreach ($list as $k => $v) {
                if (!isset($list[$k + 1])) {
                    continue;
                }
                if ($second >= $v && $second < $list[$k + 1]) {
                    $numberList = [
                        'b' => $v,
                        'a' => $list[$k + 1],
                    ];
                    break;
                }
            }
        }
        $result = [];
        foreach ($numberList as $k => $v) {
            $info = self::info($v);
            $detail = $info['detail'][$lat] ?? $info['detail'][0];
            $detailA = $info['detail'][$latList['a']] ?? $info['detail'][0];
            $result[$k] = [
                'sec' => $info['sec'],
                'sign' => $info['sign'],
                'signdeg' => $info['signdeg'],
                'signdegs' => $info['signdegs'],
                // XI		XII		A		II		III
                'detail' => $detail,
                'lat' => $latList,
                // 后一个
                'detail_a' => $detailA,
            ];
        }
        return $result;
    }

    /**
     * 解析数据
     * @param $string
     * @return array
     */
    public static function getDegreeAndSign($string): array
    {
        $degrees = substr($string, 0, 2);
        $minutes = substr($string, 4, 2);
        $sign = substr($string, 2, 2);
        return [
            'deg' => $degrees,
            'min' => $minutes,
            'sign' => $sign,
            'r' => '',
        ];
    }
}
