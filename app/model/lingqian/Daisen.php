<?php
// +----------------------------------------------------------------------
// | 灵签之 黄大仙
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\lingqian;

use app\model\BaseModel;

/**
 * Class app\model\lingqian\Daisen
 * @property int $id
 * @property string $content 内容
 * @property string $futrue 前程
 * @property string $health 健康
 * @property string $marriage 婚姻
 * @property string $thisyear 流年
 * @property string $title 签条
 * @property string $wealth 财运
 */
class Daisen extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LingqianDaisen',
        ];
    }
}
