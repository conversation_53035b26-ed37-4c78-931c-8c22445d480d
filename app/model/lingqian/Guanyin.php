<?php
// +----------------------------------------------------------------------
// | 灵签之 观音
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\lingqian;

use app\model\BaseModel;

/**
 * Class app\model\lingqian\Guanyin
 * @property array $explain 解曰
 * @property array $shi_info 诗内容
 * @property int $id
 * @property string $cause 事业
 * @property string $content 内容
 * @property string $diangu 典故
 * @property string $ji 吉凶
 * @property string $marriage 婚姻
 * @property string $notes 诠释
 * @property string $shi_title 签标题
 * @property string $study 学业
 * @property string $title 签条
 * @property string $wealth 财运
 */
class Guanyin extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LingqianGuanyin',
            'type' => [
                'shi_info' => 'json',
                'explain' => 'json',
            ],
        ];
    }
}
