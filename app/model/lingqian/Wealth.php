<?php
// +----------------------------------------------------------------------
// | 灵签之 财神
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\lingqian;

use app\model\BaseModel;

/**
 * Class app\model\lingqian\Wealth
 * @property int $id
 * @property string $content 内容
 * @property string $health 健康
 * @property string $marriage 婚姻
 * @property string $note 注解
 * @property string $study 学业
 * @property string $title 签条
 * @property string $wealth 财运
 */
class Wealth extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LingqianWealth',
        ];
    }
}
