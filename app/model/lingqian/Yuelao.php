<?php
// +----------------------------------------------------------------------
// | 灵签之 月老
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace app\model\lingqian;

use app\model\BaseModel;

/**
 * Class app\model\lingqian\Yuelao
 * @property array $content 解析
 * @property int $id 签数
 * @property string $content2 内容
 * @property string $title 签名
 */
class Yuelao extends BaseModel
{
    /**
     * 配置
     * @return array
     */
    protected function getOptions(): array
    {
        return [
            'name' => 'LingqianYuelao',
            'type' => [
                'content' => 'array',
            ],
        ];
    }
}
