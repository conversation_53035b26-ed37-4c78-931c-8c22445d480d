<?php

/**
 * This file is auto-generated.
 */

declare(strict_types=1);

namespace rpc\contract\api;

use think\swoole\rpc\client\Service;

interface ApiInterface extends Service
{
    /**
     * 获取授权信息
     * @param int $appId 授权ID
     * @return array
     */
    public function accessInfo(int $appId): array;


    /**
	 * 接口合法性和权限验证
	 * @param int $appId 授权ID
	 * @param string $apiPath 接口
	 * @param array $params 需要带入的计算参数
	 * @param string $clientSign 客户端计算出的sign
	 * @param int $apiVersion 接口版本
	 * @return bool
	 */
	public function apiVerification(
		int $appId,
		string $apiPath,
		array $params,
		string $clientSign,
		int $apiVersion = 1
	): bool;


	/**
	 * 是否有接口请求权限
	 * @param int $appId 授权ID
	 * @param string $apiPath 接口
	 * @param int $apiVersion 接口版本
	 * @return bool
	 */
	public function isAccess(int $appId, string $apiPath, int $apiVersion = 1): bool;


	/**
	 * 服务端根据参数计算生成sign
	 * @param int $appId 授权ID
	 * @param array $params 需要带入的计算参数
	 * @return string
	 */
	public function generateServerSign(int $appId, array $params): string;
}

interface FormsInterface extends Service
{
    /**
     * 获取表单信息
     * @param int $formId 表单ID
     * @return array
     */
    public function info(int $formId): array;


    /**
	 * 获取表单字段列表
	 * @param int $formId
	 * @return array
	 */
	public function getFieldList(int $formId): array;


	/**
	 * 获取表单数据
	 * @param int $formId
	 * @param int $page
	 * @param int $limit
	 * @param array|null $order
	 * @param array $whereArr
	 * @param bool $isReal
	 * @return array
	 */
	public function getFormData(
		int $formId,
		int $page,
		int $limit,
		?array $order = null,
		array $whereArr = [],
		bool $isReal = false
	): array;
}
return ['api' => ['rpc\contract\api\ApiInterface', 'rpc\contract\api\FormsInterface']];
