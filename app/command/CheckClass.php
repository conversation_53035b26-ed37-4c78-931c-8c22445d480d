<?php
// +----------------------------------------------------------------------
// | CheckClass 检测类工具
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command;

use FilesystemIterator;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\App;
use think\helper\Str;

class CheckClass extends Command
{
    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:checkClass')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addArgument('dir', Argument::OPTIONAL, '检测目录', 'app')
            ->addOption('strict', 's', Option::VALUE_NONE, '严格模式，同时检查未使用的导入')
            ->setDescription('检测类');
    }

    /**
     * 检测不存在的类引用
     * php think tool:checkClass missing app/lib/xx --strict
     * @param Input $input
     * @param Output $output
     * @return void
     */
    public function missing(Input $input, Output $output)
    {
        $dir = $input->getArgument('dir');
        $strict = $input->getOption('strict');

        $output->writeln("<info>开始检测目录: {$dir}</info>");
        $output->writeln("<info>严格模式: " . ($strict ? '是' : '否') . "</info>");

        // 获取项目根目录
        $rootPath = App::getRootPath();
        $targetDir = $rootPath . $dir;

        if (!is_dir($targetDir)) {
            $output->error("目录不存在: {$targetDir}");
            return;
        }

        // 扫描所有PHP文件
        $phpFiles = $this->scanPhpFiles($targetDir);
        $output->writeln("<info>找到 " . count($phpFiles) . " 个PHP文件</info>");

        $missingClasses = [];
        $unusedImports = [];
        $totalFiles = count($phpFiles);
        $processedFiles = 0;

        foreach ($phpFiles as $file) {
            $processedFiles++;
            $output->write("\r<comment>处理进度: {$processedFiles}/{$totalFiles} - " . basename($file) . "</comment>");

            $result = $this->analyzeFile($file, $strict);

            if (!empty($result['missing'])) {
                $missingClasses[$file] = $result['missing'];
            }

            if ($strict && !empty($result['unused'])) {
                $unusedImports[$file] = $result['unused'];
            }
        }

        $output->writeln("\n");

        // 输出结果
        $this->outputResults($output, $missingClasses, $unusedImports, $strict);
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }

    /**
     * 扫描目录下的所有PHP文件
     * @param string $dir
     * @return array
     */
    protected function scanPhpFiles(string $dir): array
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, FilesystemIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }

        return $files;
    }

    /**
     * 分析单个文件
     * @param string $file
     * @param bool $strict
     * @return array
     */
    protected function analyzeFile(string $file, bool $strict = false): array
    {
        $content = file_get_contents($file);
        if ($content === false) {
            return ['missing' => [], 'unused' => []];
        }
        $namespace = $this->getNamespaceFromPath($file);
        $namespaceBase = explode('\\', $namespace);
        array_pop($namespaceBase);
        $namespacePath = App::getRootPath() . implode('/', $namespaceBase);
        $namespaceBase = implode('\\', $namespaceBase);

        // 解析文件中的类引用
        $imports = $this->parseImports($content);
        $usedClasses = $this->parseUsedClasses($content, $imports);
        // 去除重复的类名
        foreach ($usedClasses as $k => $class) {
            // 单纯类名
            if (!Str::contains($class, '\\')) {
                // 根据命名空间判断类文件是否存在
                if (file_exists($namespacePath . "/{$class}.php")) {
                    unset($usedClasses[$k]);
                    continue;
                }
                // 判断是否已经use了
                foreach ($imports as $className) {
                    // 是否别名
                    if (Str::contains($className, ' as ')) {
                        $asArr = explode(' as ', $className);
                        if ($asArr[1] == $class) {
                            unset($usedClasses[$k]);
                        }
                    } else {
                        if (Str::endsWith($className, "\\{$class}")) {
                            unset($usedClasses[$k]);
                        }
                    }
                }
            }
        }

        // 检查缺失的类
        $missingClasses = [];
        $allClasses = array_merge($imports, $usedClasses);

        foreach (array_unique($allClasses) as $className) {
            if (!$this->classExists($className)) {
                $missingClasses[] = $className;
            }
        }

        // 检查未使用的导入（严格模式）
        $unusedImports = [];
        if ($strict) {
            foreach ($imports as $import) {
                $shortName = $this->getShortClassName($import);
                if (!$this->isClassUsedInContent($content, $shortName, $import)) {
                    $unusedImports[] = $import;
                }
            }
        }

        return [
            'missing' => $missingClasses,
            'unused' => $unusedImports,
        ];
    }

    /**
     * 解析文件中的import语句
     * @param string $content
     * @return array
     */
    protected function parseImports(string $content): array
    {
        $imports = [];

        // 匹配 use 语句
        if (preg_match_all('/^\s*use\s+([^;]+);/m', $content, $matches)) {
            foreach ($matches[1] as $match) {
                // 处理多个类的导入，如 use A, B, C;
                $classes = explode(',', $match);
                foreach ($classes as $class) {
                    $class = trim($class);
                    // 移除 as 别名
                    if (Str::contains($class, ' as ')) {
                        // $asArr = explode(' as ', $class);
                        // $class = trim($asArr[0]);
                        // $classArr = explode('\\', $class);
                        // array_pop($classArr);
                        // $imports[] = implode('\\', $classArr) . '\\' . $asArr[1];
                    }
                    if (!empty($class)) {
                        $imports[] = ltrim($class, '\\');
                    }
                }
            }
        }
        // 去除重复的类名
        $imports2 = $imports;
        foreach ($imports as $k => $class) {
            if (!Str::contains($class, '\\')) {
                foreach ($imports2 as $import) {
                    // 是否别名
                    if (Str::contains($import, ' as ')) {
                        $asArr = explode(' as ', $import);
                        if ($asArr[1] == $class) {
                            unset($imports[$k]);
                        }
                    } else {
                        if (Str::endsWith($import, "\\{$class}")) {
                            unset($imports[$k]);
                        }
                    }
                }
            }
        }
        return $imports;
    }

    /**
     * 解析文件中使用的类
     * @param string $content
     * @return array
     */
    protected function parseUsedClasses(string $content): array
    {
        $usedClasses = [];

        // 移除注释和字符串，避免误匹配
        $cleanContent = $this->removeCommentsAndStrings($content);

        // 匹配 new ClassName()
        if (preg_match_all('/\bnew\s+([A-Za-z_][A-Za-z0-9_\\\\]*)/i', $cleanContent, $matches)) {
            $usedClasses = array_merge($usedClasses, $matches[1]);
        }

        // 匹配 ClassName::method()
        if (preg_match_all('/\b([A-Za-z_][A-Za-z0-9_\\\\]*)::/i', $cleanContent, $matches)) {
            $usedClasses = array_merge($usedClasses, $matches[1]);
        }

        // 匹配 instanceof ClassName
        if (preg_match_all('/\binstanceof\s+([A-Za-z_][A-Za-z0-9_\\\\]*)/i', $cleanContent, $matches)) {
            $usedClasses = array_merge($usedClasses, $matches[1]);
        }

        // 匹配类型提示
        if (preg_match_all('/\b([A-Za-z_][A-Za-z0-9_\\\\]*)\s+\$[a-zA-Z_]/i', $cleanContent, $matches)) {
            foreach ($matches[1] as $match) {
                // 排除PHP关键字
                if (!in_array(strtolower($match), [
                    'float', 'int', 'string', 'bool', 'array', 'object', 'callable', 'iterable', 'void', 'mixed',
                    'null', 'false', 'true', 'as', 'return', 'and', 'or', 'clone', 'protected', 'private', 'public',
                ])) {
                    $usedClasses[] = $match;
                }
            }
        }

        // 清理结果
        $cleanedClasses = [];
        foreach ($usedClasses as $class) {
            $class = trim($class, '\\');
            if (!empty($class) && !in_array(strtolower($class), ['self', 'parent', 'static'])) {
                $cleanedClasses[] = $class;
            }
        }

        return array_unique($cleanedClasses);
    }

    /**
     * 移除注释和字符串内容
     * @param string $content
     * @return string
     */
    protected function removeCommentsAndStrings(string $content): string
    {
        // 移除单行注释
        $content = preg_replace('/\/\/.*$/m', '', $content);

        // 移除多行注释
        $content = preg_replace('/\/\*.*?\*\//s', '', $content);

        // 移除字符串内容（简单处理）
        $content = preg_replace('/"[^"\\\\]*(?:\\\\.[^"\\\\]*)*"/', '""', $content);
        $content = preg_replace("/'[^'\\\\]*(?:\\\\.[^'\\\\]*)*'/", "''", $content);

        return $content;
    }

    /**
     * 检查类是否存在
     * @param string $className
     * @return bool
     */
    protected function classExists(string $className): bool
    {
        // 检查是否为完全限定名
        if (str_starts_with($className, '\\')) {
            $className = substr($className, 1);
        }

        // 处理as别名
        if (Str::contains($className, ' as ')) {
            $asArr = explode(' as ', $className);
            $className = trim($asArr[0]);
        }

        // 尝试类自动加载
        if (class_exists($className, true) || interface_exists($className, true) || trait_exists($className, true)) {
            return true;
        }

        return false;
    }

    /**
     * 获取类的短名称
     * @param string $className
     * @return string
     */
    protected function getShortClassName(string $className): string
    {
        $parts = explode('\\', $className);
        return end($parts);
    }

    /**
     * 检查类是否在内容中被使用
     * @param string $content
     * @param string $shortName
     * @param string $fullName
     * @return bool
     */
    protected function isClassUsedInContent(string $content, string $shortName, string $fullName): bool
    {
        // 移除use语句后检查
        $contentWithoutUse = preg_replace('/^\s*use\s+[^;]+;/m', '', $content);

        // 检查短名称是否被使用
        $patterns = [
            '/\bnew\s+' . preg_quote($shortName, '/') . '\b/i',
            '/\b' . preg_quote($shortName, '/') . '::/i',
            '/\binstanceof\s+' . preg_quote($shortName, '/') . '\b/i',
            '/\b' . preg_quote($shortName, '/') . '\s+\$/i',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $contentWithoutUse)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 输出检测结果
     * @param Output $output
     * @param array $missingClasses
     * @param array $unusedImports
     * @param bool $strict
     * @return void
     */
    protected function outputResults(Output $output, array $missingClasses, array $unusedImports, bool $strict): void
    {
        $output->writeln("\n" . str_repeat('=', 60));
        $output->writeln("<info>检测结果汇总</info>");
        $output->writeln(str_repeat('=', 60));

        if (empty($missingClasses) && empty($unusedImports)) {
            $output->writeln("<info>✓ 未发现问题</info>");
            return;
        }

        // 输出缺失的类
        if (!empty($missingClasses)) {
            $output->writeln("\n<error>发现不存在的类引用:</error>");
            $output->writeln(str_repeat('-', 40));

            $totalMissing = 0;
            foreach ($missingClasses as $file => $classes) {
                $relativePath = $this->getRelativePath($file);
                $output->writeln("\n<comment>文件: {$relativePath}</comment>");

                foreach ($classes as $class) {
                    $output->writeln("  <error>✗ {$class}</error>");
                    $totalMissing++;
                }
            }

            $output->writeln("\n<error>总计发现 {$totalMissing} 个不存在的类引用</error>");
        }

        // 输出未使用的导入（严格模式）
        if ($strict && !empty($unusedImports)) {
            $output->writeln("\n<comment>发现未使用的导入:</comment>");
            $output->writeln(str_repeat('-', 40));

            $totalUnused = 0;
            foreach ($unusedImports as $file => $imports) {
                $relativePath = $this->getRelativePath($file);
                $output->writeln("\n<comment>文件: {$relativePath}</comment>");

                foreach ($imports as $import) {
                    $output->writeln("  <comment>⚠ {$import}</comment>");
                    $totalUnused++;
                }
            }

            $output->writeln("\n<comment>总计发现 {$totalUnused} 个未使用的导入</comment>");
        }

        $output->writeln("\n" . str_repeat('=', 60));
    }

    /**
     * 获取相对路径
     * @param string $file
     * @return string
     */
    protected function getRelativePath(string $file): string
    {
        $rootPath = App::getRootPath();
        if (str_starts_with($file, $rootPath)) {
            return substr($file, strlen($rootPath));
        }
        return $file;
    }

    /**
     * 通过解析文件路径获取命名空间
     * @param string $filePath PHP文件路径
     * @return string|null 命名空间路径
     */
    protected function getNamespaceFromPath(string $filePath): ?string
    {
        $root = App::getRootPath();
        $root = realpath($root) . DIRECTORY_SEPARATOR;
        $filePath = realpath($filePath);

        // 处理 app 目录下的文件
        if (str_starts_with($filePath, $root . 'app' . DIRECTORY_SEPARATOR)) {
            $relative = str_replace(
                [$root, '.php'],
                '',
                $filePath
            );

            return 'app\\' . str_replace(
                    DIRECTORY_SEPARATOR,
                    '\\',
                    substr($relative, 4) // 跳过 "app/"
                );
        }

        // 处理核心框架文件
        if (str_starts_with($filePath, $root . 'vendor' . DIRECTORY_SEPARATOR . 'topthink')) {
            // 类似 vendor/topthink/framework/src/think/App.php → think\App
            $parts = explode(DIRECTORY_SEPARATOR, $filePath);
            $start = array_search('topthink', $parts) + 2; // 跳过 vendor/topthink/[package]/
            $end = count($parts) - 1;

            $namespace = implode('\\', array_slice($parts, $start, $end - $start));
            return substr($namespace, 0, -4); // 移除 .php
        }

        return null;
    }
}
