<?php
// +----------------------------------------------------------------------
// | Xzyunshi 星座运势 https://zxz.linggx365.cn/yunshi
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\spider;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\App;
use zjkal\TimeHelper;

class Xzyunshi extends Command
{
    /**
     * 星座
     * @var string[]
     */
    protected array $xingzuo = ['白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座', '天秤座', '天蝎座', '射手座', '摩羯座', '水瓶座', '双鱼座'];

    /**
     * 星座对应生日日期
     * @var int[]
     */
    protected array $zodiacBirthdayMap = [
        19950404000000, 19950505000000, 19950606000000, 19950707000000, 19950807000000, 19950907000000,
        19951008000000, 19951107000000, 19951206000000, 19950105000000, 19950203000000, 19950305000000,
    ];

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('spider:xzyunshi')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addOption('start', null, Option::VALUE_OPTIONAL, '开始', '')
            ->addOption('end', null, Option::VALUE_OPTIONAL, '结束', '')
            ->addOption('xingzuo', null, Option::VALUE_OPTIONAL, '星座', '')
            ->setDescription('灵机-星座运势');
    }

    /**
     * 按日
     * php think spider:xzyunshi day --start=2025-01-01 --end=2025-12-31 --xingzuo=水瓶座
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function day(Input $input, Output $output)
    {
        $start = $input->getOption('start') ?: date('Y-m-d', strtotime('-7 day'));
        $end = $input->getOption('end') ?: $start;
        $xingzuo = $input->getOption('xingzuo');
        if ($xingzuo) {
            $xingzuoId = $this->getXingzuo($xingzuo, true);
        }
        $diffDays = TimeHelper::diffDays($start, $end);
        if ($diffDays < 0) {
            $output->error('日期范围错误');
            return;
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm/result/zhun_xing_zuo';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'version' => '1.5.1',
            ],
            'form_params' => [
                'optionalList' => '12',
                'yunshiTime' => '',
                'homePlace' => '北京市-北京市-朝阳区',
                'birthday' => '',
            ],
        ];

        $data = [];
        for ($i = 0; $i <= $diffDays; $i++) {
            $time = strtotime($start) + $i * 86400;
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['yunshiTime'] = date('Ymd000000', $time);
            // 指定星座
            if (isset($xingzuoId)) {
                $xingzuoName = $this->getXingzuo($xingzuoId, false);
                $output->info("开始星座：{$xingzuoName}");

                $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$xingzuoId];
                $response = $this->httpClient()->post($apiUrl, $options);

                $body = $response->getBody()->getContents();
                $data[date('Y-m-d', $time)][$xingzuoName] = $body;
            } else {
                foreach ($this->xingzuo as $k => $v) {
                    $xingzuoName = $this->getXingzuo($k, false);
                    $output->info("开始星座：{$xingzuoName}");

                    $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$k];
                    $response = $this->httpClient()->post($apiUrl, $options);

                    $body = $response->getBody()->getContents();
                    $data[date('Y-m-d', $time)][$xingzuoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'day');
            $data = [];
        }
        $output->info('ok');
    }

    /**
     * 按日2
     * php think spider:xzyunshi day2 --start=1995-01-01 --end=1995-12-31
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function day2(Input $input, Output $output)
    {
        $start = $input->getOption('start') ?: date('Y-m-d', strtotime('-7 day'));
        $end = $input->getOption('end') ?: $start;
        $diffDays = TimeHelper::diffDays($start, $end);
        if ($diffDays < 0) {
            $output->error('日期范围错误');
            return;
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm/result/zhun_xing_zuo';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'version' => '1.5.1',
            ],
            'form_params' => [
                'optionalList' => '7',
                'homePlace' => '北京市-北京市-东城区',
                'birthday' => '',
                'name' => '张艳',
            ],
        ];

        $data = [];
        for ($i = 0; $i <= $diffDays; $i++) {
            $time = strtotime($start) + $i * 86400;
            $output->info("开始处理日期：" . date('Y-m-d', $time));

            for ($H = 0; $H < 24; $H++) {
                $HH = str_pad($H, 2, '0', STR_PAD_LEFT);
                $options['form_params']['birthday'] = date("Ymd{$HH}0000", $time);
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();
                $data[date("Y-m-d {$HH}:00:00", $time)][] = $body;
                usleep(10);
            }
            $output->info('保存结果');
            $this->saveFile($data, 'day2');

            usleep(100);
            $data = [];
        }
        $output->info('ok');
    }

    /**
     * 按周
     * php think spider:xzyunshi weeks --start=2025 --xingzuo=水瓶座
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function weeks(Input $input, Output $output)
    {
        $year = $input->getOption('start') ?: date('Y');
        $xingzuo = $input->getOption('xingzuo');
        if ($xingzuo) {
            $xingzuoId = $this->getXingzuo($xingzuo, true);
        }
        $current = strtotime("first monday of January {$year}");
        $mondays = [];
        while (date('Y', $current) == $year) {
            $mondays[] = date('Y-m-d', $current);
            // 递增一周
            $current = strtotime('+1 week', $current);
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm/result/zhun_xing_zuo';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'version' => '1.5.1',
            ],
            'form_params' => [
                'optionalList' => '13',
                'yunshiTime' => '',
                'homePlace' => '北京市-北京市-朝阳区',
                'birthday' => '',
            ],
        ];

        $data = [];
        foreach ($mondays as $week => $day) {
            $time = strtotime($day);
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['yunshiTime'] = date('Ymd000000', $time);
            // 指定星座
            if (isset($xingzuoId)) {
                $xingzuoName = $this->getXingzuo($xingzuoId, false);
                $output->info("开始星座：{$xingzuoName}");

                $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$xingzuoId];
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();
                $data["{$year}/{$week}"][$xingzuoName] = $body;
            } else {
                foreach ($this->xingzuo as $k => $v) {
                    $xingzuoName = $this->getXingzuo($k, false);
                    $output->info("开始星座：{$xingzuoName}");

                    $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$k];
                    $response = $this->httpClient()->post($apiUrl, $options);
                    $body = $response->getBody()->getContents();
                    $data["{$year}/{$week}"][$xingzuoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'weeks');
        }
        $output->info('ok');
    }

    /**
     * 按月
     * php think spider:xzyunshi month --start=2025 --xingzuo=水瓶座
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function month(Input $input, Output $output)
    {
        $year = $input->getOption('start') ?: date('Y');
        $xingzuo = $input->getOption('xingzuo');
        if ($xingzuo) {
            $xingzuoId = $this->getXingzuo($xingzuo, true);
        }
        $current = strtotime("first monday of January {$year}");
        $monthly = [];
        while (date('Y', $current) == $year) {
            $monthly[] = date('Y-m-01', $current);
            // 递增一月
            $current = strtotime('+1 month', $current);
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'version' => '1.5.1',
            ],
            'form_params' => [
                'yunshiTime' => '',
                'constellation' => '0',
                'gender' => 'male',
                'birthday' => '199504040000',
                'home_place' => '北京市-北京市-朝阳区',
                'activity' => 'xing_zuo_yue_yun',
                'process_time' => '199504',
            ],
        ];

        $data = [];
        foreach ($monthly as $i => $day) {
            $month = $i + 1;
            $time = strtotime($day);
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['yunshiTime'] = date('Ymd000000', $time);
            // 指定星座
            if (isset($xingzuoId)) {
                $xingzuoName = $this->getXingzuo($xingzuoId, false);
                $output->info("开始星座：{$xingzuoName}");

                $options['form_params']['process_time'] = date('Ym', $time);
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();

                $data["{$year}/{$month}"][$xingzuoName] = $body;
            } else {
                foreach ($this->xingzuo as $k => $v) {
                    $xingzuoName = $this->getXingzuo($k, false);
                    $output->info("开始星座：{$xingzuoName}");

                    $options['form_params']['process_time'] = date('Ym', $time);
                    $response = $this->httpClient()->post($apiUrl, $options);
                    $body = $response->getBody()->getContents();

                    $data["{$year}/{$month}"][$xingzuoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'month');
        }
        $output->info('ok');
    }

    /**
     * 按年
     * php think spider:xzyunshi year --start=2024 --end=2025 --xingzuo=水瓶座
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function year(Input $input, Output $output)
    {
        $year = $input->getOption('start') ?: date('Y');
        $end = $input->getOption('end') ?: $year;
        $xingzuo = $input->getOption('xingzuo');
        if ($xingzuo) {
            $xingzuoId = $this->getXingzuo($xingzuo, true);
        }
        $yearList = [];
        for ($i = $year; $i <= $end; $i++) {
            $yearList[] = (int)$i;
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm/result/zhun_xing_zuo';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'version' => '1.5.1',
            ],
            'form_params' => [
                'optionalList' => '14',
                'yunshiTime' => '',
                'homePlace' => '北京市-北京市-朝阳区',
                'birthday' => '',
            ],
        ];

        $data = [];
        foreach ($yearList as $i => $year) {
            $time = strtotime("{$year}-01-01");
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['yunshiTime'] = date('Ymd000000', $time);
            // 指定星座
            if (isset($xingzuoId)) {
                $xingzuoName = $this->getXingzuo($xingzuoId, false);
                $output->info("开始星座：{$xingzuoName}");

                $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$xingzuoId];
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();

                $data["{$year}"][$xingzuoName] = $body;
            } else {
                foreach ($this->xingzuo as $k => $v) {
                    $xingzuoName = $this->getXingzuo($k, false);
                    $output->info("开始星座：{$xingzuoName}");

                    $options['form_params']['birthday'] = $this->zodiacBirthdayMap[$k];
                    $response = $this->httpClient()->post($apiUrl, $options);
                    $body = $response->getBody()->getContents();

                    $data["{$year}"][$xingzuoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'year');
        }
        $output->info('ok');
    }

    /**
     * 数据打平
     * php think spider:xzyunshi process
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function process(Input $input, Output $output)
    {
        $runtimePath = runtime_path();
        $result = [];
        $dirs = glob($runtimePath . 'xzyunshi_*', GLOB_ONLYDIR);
        foreach ($dirs as $dir) {
            $this->processDirectory($dir, $result);
        }

        foreach ($result as $file => $content) {
            array2file($content, $file);
        }
    }

    /**
     * 递归处理目录以查找所有txt文件
     * @param string $dir 目录路径
     * @param array &$result 参考结果数组
     */
    protected function processDirectory($dir, &$result)
    {
        $txtFiles = glob($dir . '/*.txt');

        foreach ($txtFiles as $file) {
            $this->processTxtFile($file, $result);
        }

        $subdirs = glob($dir . '/*', GLOB_ONLYDIR);

        foreach ($subdirs as $subdir) {
            $this->processDirectory($subdir, $result);
        }
    }

    /**
     * 处理包含PHP数组字符串的txt文件
     * @param string $file 文件路径
     * @param array &$result 参考结果数组
     */
    protected function processTxtFile($file, &$result)
    {
        // 阅读文件内容
        $content = file_get_contents($file);

        // 评估PHP数组字符串以获取实际数组
        $array = eval('return ' . $content . ';');

        if (is_array($array)) {
            // 数组扁平化同时保留键
            $this->flattenArray($array, $file, $result);
        }
    }

    /**
     * 将多维数组展平同时保留键
     * @param array $array 要展平的数组
     * @param array &$result 参考结果数组
     * @param string $prefix 嵌套数组的关键前缀
     */
    protected function flattenArray($array, $file, &$result, $prefix = '')
    {
        foreach ($array as $key => $value) {
            $newKey = $prefix ? $prefix . '.' . $key : $key;

            if (is_array($value)) {
                $this->flattenArray($value, $file, $result, $newKey);
            } else {
                $result[$file][$newKey] = $value;
            }
        }
    }

    /**
     * 保存内容
     * @param array $data
     * @param string $act
     * @return void
     */
    protected function saveFile(array $data, string $act = 'day')
    {
        $pathBase = App::getRuntimePath() . "data/xzyunshi_{$act}/";
        foreach ($data as $dir => $list) {
            $path = $pathBase . $dir . '/';
            foreach ($list as $xingzuo => $content) {
                if (!is_dir($path)) {
                    if (!mkdir($path, 0777, true)) {
                        die("目录创建失败");
                    }
                }
                $fileName = $xingzuo . '.txt';
                array2file(json_decode($content, true), $path . $fileName);
            }
        }
    }

    /**
     * 获取星座id或者名称
     * @param int|string $xingzuo
     * @param bool $isKey
     * @return int|string
     */
    protected function getXingzuo(int | string $xingzuo, bool $isKey = false)
    {
        if (is_numeric($xingzuo)) {
            return $isKey ? $xingzuo : $this->xingzuo[$xingzuo];
        } else {
            $key = (int)array_search($xingzuo, $this->xingzuo);
            return $isKey ? $key : $this->xingzuo[$key];
        }
    }

    /**
     * 获取客户端
     * @param array $config
     * @return Client
     */
    protected function httpClient(array $config = [])
    {
        $config = array_merge(
            [
                'verify' => false,
            ],
            $config
        );
        return new Client($config);
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error('{$act} 方法不存在');
            return;
        }
        $this->{$act}($input, $output);
    }
}
