<?php
// +----------------------------------------------------------------------
// | Sxxingge 生肖性格 https://zsx.linggx365.cn/index
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\spider;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\App;

class Sxxingge extends Command
{
    /**
     * 生肖
     * @var string[]
     */
    protected array $shenxiao = ['子鼠', '丑牛', '寅虎', '卯兔', '辰龙', '巳蛇', '午马', '未羊', '申猴', '酉鸡', '戌狗', '亥猪'];

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('spider:sxxingge')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addOption('start', null, Option::VALUE_OPTIONAL, '开始', '')
            ->addOption('end', null, Option::VALUE_OPTIONAL, '结束', '')
            ->addOption('shengxiao', null, Option::VALUE_OPTIONAL, '生肖', '')
            ->setDescription('灵机-生肖性格');
    }

    /**
     * 今年生肖性格
     * php think spider:sxxingge year --shengxiao=子鼠
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function year(Input $input, Output $output)
    {
        $shengxiao = $input->getOption('shengxiao');
        if ($shengxiao) {
            $shengxiaoId = $this->getShenxiao($shengxiao, true);
        }
        $year = date('Y');
        $apiUrl = 'https://zsx.linggx365.cn/api/algorithm';
        $options = [
            'headers' => [
                'Referer' => 'https://zsx.linggx365.cn/index',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'platform' => 'h5',
            ],
            'form_params' => [
                'activity' => 'ba_zi_zhun_sheng_xiao',
                'optionalList' => '6',

                'year' => '2025',
                'animalsIndex' => '0',
            ],
        ];

        $data = [];

        $options['form_params']['year'] = $year;
        // 指定生肖
        if (isset($shengxiaoId)) {
            $shengxiaoName = $this->getShenxiao($shengxiaoId, false);
            $output->info("开始生肖：{$shengxiaoName}");

            $options['form_params']['animalsIndex'] = $shengxiaoId;
            $response = $this->httpClient()->post($apiUrl, $options);

            $body = $response->getBody()->getContents();
            $data["{$year}-{$shengxiaoName}"][$shengxiaoName] = $body;

            $bodyData = json_decode($body, true);
            // 年列表
            $yearList = $bodyData['data']['xing_ge']['info']['year_list'];
            foreach ($yearList as $y){
                $keyName = "{$y}{$shengxiaoName}年运详解";
                $output->info("年运详解：{$keyName}");
                $options['form_params']['optionalList'] = '5';
                $options['form_params']['animalsYear'] = $y;
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();
                $data["{$year}-{$shengxiaoName}"][$keyName] = $body;
                usleep(5);
            }
        } else {
            foreach ($this->shenxiao as $k => $v) {
                $shengxiaoName = $this->getShenxiao($k, false);
                $output->info("开始生肖：{$shengxiaoName}");

                $options['form_params']['animalsIndex'] = $k;
                $response = $this->httpClient()->post($apiUrl, $options);

                $body = $response->getBody()->getContents();
                $data["{$year}-{$shengxiaoName}"][$shengxiaoName] = $body;

                $bodyData = json_decode($body, true);
                // 年列表
                $yearList = $bodyData['data']['xing_ge']['info']['year_list'];
                foreach ($yearList as $y){
                    $keyName = "{$y}{$shengxiaoName}年运详解";
                    $output->info("年运详解：{$keyName}");
                    $options2 = $options;
                    $options2['form_params']['optionalList'] = '5';
                    $options2['form_params']['animalsYear'] = $y;
                    $response = $this->httpClient()->post($apiUrl, $options2);
                    $body = $response->getBody()->getContents();
                    $data["{$year}-{$shengxiaoName}"][$keyName] = $body;
                    usleep(5);
                }

                usleep(10);
            }
        }
        usleep(100);
        $output->info('保存结果');
        $this->saveFile($data, 'year');

        $output->info('ok');
    }

    /**
     * 保存内容
     * @param array $data
     * @param string $act
     * @return void
     */
    protected function saveFile(array $data, string $act = 'day')
    {
        $pathBase = App::getRuntimePath() . "data/sxxingge_{$act}/";
        foreach ($data as $dir => $list) {
            $path = $pathBase . $dir . '/';
            foreach ($list as $xingzuo => $content) {
                if (!is_dir($path)) {
                    if (!mkdir($path, 0777, true)) {
                        die('目录创建失败');
                    }
                }
                $fileName = $xingzuo . '.txt';
                array2file(json_decode($content, true), $path . $fileName);
            }
        }
    }

    /**
     * 获取生肖id或者名称
     * @param int|string $shenxiao
     * @param bool $isKey
     * @return int|string
     */
    protected function getShenxiao(int | string $shenxiao, bool $isKey = false)
    {
        if (is_numeric($shenxiao)) {
            return $isKey ? $shenxiao : $this->shenxiao[$shenxiao];
        } else {
            $key = (int)array_search($shenxiao, $this->shenxiao);
            return $isKey ? $key : $this->shenxiao[$key];
        }
    }

    /**
     * 获取客户端
     * @param array $config
     * @return Client
     */
    protected function httpClient(array $config = [])
    {
        $config = array_merge(
            [
                'verify' => false,
            ],
            $config
        );
        return new Client($config);
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error('{$act} 方法不存在');
            return;
        }
        $this->{$act}($input, $output);
    }
}
