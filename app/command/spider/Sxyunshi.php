<?php
// +----------------------------------------------------------------------
// | Sxyunshi 生肖运势 https://zsx.linggx365.cn/index
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command\spider;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\App;
use zjkal\TimeHelper;

class Sxyunshi extends Command
{
    /**
     * 生肖
     * @var string[]
     */
    protected array $shenxiao = ['子鼠', '丑牛', '寅虎', '卯兔', '辰龙', '巳蛇', '午马', '未羊', '申猴', '酉鸡', '戌狗', '亥猪'];

    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('spider:sxyunshi')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addOption('start', null, Option::VALUE_OPTIONAL, '开始', '')
            ->addOption('end', null, Option::VALUE_OPTIONAL, '结束', '')
            ->addOption('shengxiao', null, Option::VALUE_OPTIONAL, '生肖', '')
            ->setDescription('灵机-生肖运势');
    }

    /**
     * 今日、明日、本周
     * php think spider:sxyunshi day --start=2025-01-01 --end=2025-12-31 --shengxiao=子鼠
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function day(Input $input, Output $output)
    {
        $start = $input->getOption('start') ?: date('Y-m-d', strtotime('-7 day'));
        $end = $input->getOption('end') ?: $start;
        $shengxiao = $input->getOption('shengxiao');
        if ($shengxiao) {
            $shengxiaoId = $this->getShenxiao($shengxiao, true);
        }
        $diffDays = TimeHelper::diffDays($start, $end);
        if ($diffDays < 0) {
            $output->error('日期范围错误');
            return;
        }
        $apiUrl = 'https://zsx.linggx365.cn/api/algorithm';
        $options = [
            'headers' => [
                'Referer' => 'https://zsx.linggx365.cn/index',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
                'platform' => 'h5',
            ],
            'form_params' => [
                'activity' => 'ba_zi_zhun_sheng_xiao',
                'optionalList' => '0,1,2',

                'year' => '2023',
                'animalsIndex' => '2',
                'time' => '202505200000',
            ],
        ];

        $data = [];
        for ($i = 0; $i <= $diffDays; $i++) {
            $time = strtotime($start) + $i * 86400;
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['year'] = date('Y', $time);
            $options['form_params']['time'] = date('Ymd000000', $time);
            // 指定生肖
            if (isset($shengxiaoId)) {
                $shengxiaoName = $this->getShenxiao($shengxiaoId, false);
                $output->info("开始生肖：{$shengxiaoName}");

                $options['form_params']['animalsIndex'] = $shengxiaoId;
                $response = $this->httpClient()->post($apiUrl, $options);

                $body = $response->getBody()->getContents();
                $data[date('Y-m-d', $time)][$shengxiaoName] = $body;
            } else {
                foreach ($this->shenxiao as $k => $v) {
                    $shengxiaoName = $this->getShenxiao($k, false);
                    $output->info("开始生肖：{$shengxiaoName}");

                    $options['form_params']['animalsIndex'] = $k;
                    $response = $this->httpClient()->post($apiUrl, $options);

                    $body = $response->getBody()->getContents();
                    $data[date('Y-m-d', $time)][$shengxiaoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'day');
            $data = [];
        }
        $output->info('ok');
    }

    /**
     * 月
     * php think spider:sxyunshi month --start=2025 --shengxiao=子鼠
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function month(Input $input, Output $output)
    {
        $year = $input->getOption('start') ?: date('Y');
        $shengxiao = $input->getOption('shengxiao');
        if ($shengxiao) {
            $shengxiaoId = $this->getShenxiao($shengxiao, true);
        }
        $current = strtotime("first monday of January {$year}");
        $monthly = [];
        while (date('Y', $current) == $year) {
            $monthly[] = date('Y-m-01', $current);
            // 递增一月
            $current = strtotime('+1 month', $current);
        }
        $apiUrl = 'https://zsx.linggx365.cn/api/algorithm';
        $options = [
            'headers' => [
                'Referer' => 'https://zsx.linggx365.cn/index',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
            ],
            'form_params' => [
                'activity' => 'ba_zi_zhun_sheng_xiao',
                'year' => '2025',
                'optionalList' => '3',
                'animalsIndex' => '7',
                'month' => '5',
            ],
        ];

        $data = [];
        foreach ($monthly as $i => $day) {
            $month = $i + 1;
            $time = strtotime($day);
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['year'] = date('Y', $time);
            $options['form_params']['month'] = (int)date('m', $time);
            // 指定生肖
            if (isset($shengxiaoId)) {
                $shengxiaoName = $this->getShenxiao($shengxiaoId, false);
                $output->info("开始生肖：{$shengxiaoName}");

                $options['form_params']['animalsIndex'] = $shengxiaoId;
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();

                $data["{$year}/{$month}"][$shengxiaoName] = $body;
            } else {
                foreach ($this->shenxiao as $k => $v) {
                    $shengxiaoName = $this->getShenxiao($k, false);
                    $output->info("开始生肖：{$shengxiaoName}");

                    $options['form_params']['animalsIndex'] = $k;
                    $response = $this->httpClient()->post($apiUrl, $options);
                    $body = $response->getBody()->getContents();

                    $data["{$year}/{$month}"][$shengxiaoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'month');
        }
        $output->info('ok');
    }

    /**
     * 按年
     * php think spider:sxyunshi year --start=2024 --end=2025 --shengxiao=水瓶座
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws GuzzleException
     */
    protected function year(Input $input, Output $output)
    {
        $year = $input->getOption('start') ?: date('Y');
        $end = $input->getOption('end') ?: $year;
        $shengxiao = $input->getOption('shengxiao');
        if ($shengxiao) {
            $shengxiaoId = $this->getShenxiao($shengxiao, true);
        }
        $yearList = [];
        for ($i = $year; $i <= $end; $i++) {
            $yearList[] = (int)$i;
        }
        $apiUrl = 'https://zxz.linggx365.cn/api/algorithm';
        $options = [
            'headers' => [
                'Referer' => 'https://zxz.linggx365.cn/yunshi?channel=sw_whxl_00004',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:137.0) Gecko/20100101 Firefox/137.0',
            ],
            'form_params' => [
                'activity' => 'ba_zi_zhun_sheng_xiao',
                'optionalList' => '4',

                'year' => '2024',
                'animalsIndex' => '0',
            ],
        ];

        $data = [];
        foreach ($yearList as $i => $year) {
            $time = strtotime("{$year}-01-01");
            $output->info("开始处理日期：" . date('Y-m-d', $time));
            $options['form_params']['year'] = date('Y', $time);
            // 指定生肖
            if (isset($shengxiaoId)) {
                $xingzuoName = $this->getShenxiao($shengxiaoId, false);
                $output->info("开始生肖：{$xingzuoName}");

                $options['form_params']['animalsIndex'] = $shengxiaoId;
                $response = $this->httpClient()->post($apiUrl, $options);
                $body = $response->getBody()->getContents();

                $data["{$year}"][$xingzuoName] = $body;
            } else {
                foreach ($this->shenxiao as $k => $v) {
                    $xingzuoName = $this->getShenxiao($k, false);
                    $output->info("开始生肖：{$xingzuoName}");

                    $options['form_params']['animalsIndex'] = $k;
                    $response = $this->httpClient()->post($apiUrl, $options);
                    $body = $response->getBody()->getContents();

                    $data["{$year}"][$xingzuoName] = $body;
                    usleep(10);
                }
            }
            usleep(100);
            $output->info('保存结果');
            $this->saveFile($data, 'year');
        }
        $output->info('ok');
    }

    /**
     * 保存内容
     * @param array $data
     * @param string $act
     * @return void
     */
    protected function saveFile(array $data, string $act = 'day')
    {
        $pathBase = App::getRuntimePath() . "data/sxyunshi_{$act}/";
        foreach ($data as $dir => $list) {
            $path = $pathBase . $dir . '/';
            foreach ($list as $xingzuo => $content) {
                if (!is_dir($path)) {
                    if (!mkdir($path, 0777, true)) {
                        die('目录创建失败');
                    }
                }
                $fileName = $xingzuo . '.txt';
                array2file(json_decode($content, true), $path . $fileName);
            }
        }
    }

    /**
     * 获取生肖id或者名称
     * @param int|string $shenxiao
     * @param bool $isKey
     * @return int|string
     */
    protected function getShenxiao(int | string $shenxiao, bool $isKey = false)
    {
        if (is_numeric($shenxiao)) {
            return $isKey ? $shenxiao : $this->shenxiao[$shenxiao];
        } else {
            $key = (int)array_search($shenxiao, $this->shenxiao);
            return $isKey ? $key : $this->shenxiao[$key];
        }
    }

    /**
     * 获取客户端
     * @param array $config
     * @return Client
     */
    protected function httpClient(array $config = [])
    {
        $config = array_merge(
            [
                'verify' => false,
            ],
            $config
        );
        return new Client($config);
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error('{$act} 方法不存在');
            return;
        }
        $this->{$act}($input, $output);
    }
}
