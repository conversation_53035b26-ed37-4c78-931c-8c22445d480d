<?php
// +----------------------------------------------------------------------
// | Tests 单元测试工具
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command;

use PhpParser\Node\Expr\Array_;
use PhpParser\Node\Expr\ArrayItem;
use PhpParser\Node\Scalar\String_;
use PhpParser\Node\Stmt\PropertyProperty;
use PhpParser\NodeTraverser;
use PhpParser\NodeVisitor\CloningVisitor;
use PhpParser\NodeVisitorAbstract;
use PhpParser\ParserFactory;
use PhpParser\PhpVersion;
use PhpParser\PrettyPrinter\Standard;
use ReflectionClass;
use ReflectionException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\facade\App;
use think\helper\Str;
use think\Http;
use think\Request;
use think\Response;

class Tests extends Command
{
    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:tests')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addArgument('file', Argument::REQUIRED, '测试类')
            ->setDescription('单元测试工具');
    }

    /**
     * 更新单元测试 预先测试结果
     * php think tool:tests saveBeforeData ./tests/qyapp/v1/WxcyTest.php
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws ReflectionException
     */
    protected function saveBeforeData(Input $input, Output $output)
    {
        $file = $input->getArgument('file');
        $cleaned = preg_replace('/^(\.\/|\/|\.\.\/)/', '', $file);
        if (Str::endsWith($cleaned, '.php')) {
            $sourceFile = App::getRootPath() . $cleaned;
            $this->updateBeforeData($sourceFile);
        } else {
            $dirPath = realpath(App::getRootPath() . $cleaned);
            if (!is_dir($dirPath)) {
                $output->error("目录 {$cleaned} 不存在");
                return;
            }
            $phpList = $this->listPhpFilesGlob($dirPath);
            foreach ($phpList as $phpFile) {
                $this->updateBeforeData($phpFile);
            }
        }
        $output->info('ok');
    }

    /**
     * 查找指定目录下的所有php文件
     * @param string $path
     * @return array
     */
    protected function listPhpFilesGlob(string $path)
    {
        $files = [];
        $directories = [$path];
        while (count($directories)) {
            $dir = array_shift($directories);
            foreach (glob($dir . '/*') as $item) {
                if (is_dir($item)) {
                    $directories[] = $item;
                } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'php') {
                    $files[] = $item;
                }
            }
        }
        return $files;
    }

    /**
     * 更新单元测试里结果值
     * 注意：暂时不支持多结果
     * @param string $sourceFile
     * @return void
     * @throws ReflectionException
     */
    protected function updateBeforeData(string $sourceFile)
    {
        $this->output->comment("开始处理 {$sourceFile} 单元测试 \$beforeData 值");
        // 解析类名
        $fileContent = file_get_contents($sourceFile);
        preg_match('/namespace\s+(.+?);.*?class\s+(\w+)/s', $fileContent, $matches);
        $fullClassName = ($matches[1] ?? '') . '\\' . ($matches[2] ?? '');

        // 实例化
        $obj = new $fullClassName;

        // 反射
        $reflection = new ReflectionClass($obj);
        // 获取单元测试接口地址
        try {
            $apiUrl = $reflection->getProperty('apiUrl')->getValue($obj);
        } catch (ReflectionException $e) {
            $this->output->warning('不是单元测类，跳过');
            return;
        }
        if (empty($apiUrl)) {
            $this->output->error('获取接口为空');
            return;
        }
        unset($obj);
        // 接口地址是否数组
        $apiUrlIsArr = is_array($apiUrl);

        // 请求接口
        if ($apiUrlIsArr) {
            $newValue = [];
            foreach ($apiUrl as $uri) {
                $this->output->info("开始请求接口 {$uri}");
                $response = $this->apiGet($uri);
                $responseData = json_decode($response->getContent(), true);
                $newValue[] = json_encode($responseData['data'], JSON_UNESCAPED_UNICODE);
            }
        } else {
            $this->output->info("开始请求接口 {$apiUrl}");
            $response = $this->apiGet($apiUrl);
            $responseData = json_decode($response->getContent(), true);
            $newValue = json_encode($responseData['data'], JSON_UNESCAPED_UNICODE);
        }


        // 创建一个解析器
        $parser = (new ParserFactory)->createForVersion(PhpVersion::fromString('8.0'));
        // 解析PHP代码
        $ast = $parser->parse($fileContent);
        $tokens = $parser->getTokens();

        // 在修改AST之前运行CloningVisitor
        $traverser = new NodeTraverser(new CloningVisitor());
        $newStmts = $traverser->traverse($ast);

        // 修改属性值
        $traverser = new NodeTraverser();
        $traverser->addVisitor(
            new class($newValue) extends NodeVisitorAbstract {
                private string | array $newValue;

                public function __construct($value)
                {
                    $this->newValue = $value;
                }

                public function leaveNode($node)
                {
                    // 定位目标属性
                    if ($node instanceof PropertyProperty && $node->name->name === 'beforeData') {
                        // 动态替换为外部传入的值
                        if (is_string($this->newValue)) {
                            $node->default = new String_($this->newValue);
                        } else {
                            $newValue = [];
                            foreach ($this->newValue as $key => $value) {
                                $newValue[] = new ArrayItem(
                                    new String_($value),
                                    is_integer($key) ? null : new String_($key)
                                );
                            }
                            $node->default = new Array_($newValue);
                        }
                    }
                    return $node;
                }
            }
        );
        $modifiedAst = $traverser->traverse($newStmts);

        // 生成新类文件
        $printer = new Standard(
            [
                // 保持原样
                'shortArraySyntax' => true,
                // 可选：指定换行符（默认根据系统自动选择）
                'newline' => PHP_EOL,
            ]
        );
        // 尽量保持原有格式：https://github.com/nikic/PHP-Parser/blob/master/doc/component/Pretty_printing.markdown
        $newCode = $printer->printFormatPreserving($modifiedAst, $ast, $tokens);

        // 做一些替换，保持排版
        $newCode = str_replace(
            ['beforeData = [\'', '\'];', '\', \''],
            ["beforeData = [\n        '", "'\n    ];", "', \n        '"],
            $newCode
        );

        file_put_contents($sourceFile, $newCode);

        $this->output->info("  \$beforeData -> 更新成功");
    }

    /**
     * 接口请求
     * @param string $uri 接口路径
     * @return Response
     */
    protected function apiGet(string $uri, array $parameters = [])
    {
        // url分析
        $uriParse = parse_url($uri);
        $host = $uriParse['host'] ?? '';
        $path = $uriParse['path'] ?? '';
        $method = 'GET';
        // GET参数处理
        if ($method == 'GET' && !empty($uriParse['query'])) {
            parse_str($uriParse['query'] ?? '', $queryArr);
            $parameters = array_merge($queryArr, $parameters);
        }
        // server
        $server = [];
        $server['REQUEST_METHOD'] = $method;
        $server['QUERY_STRING'] = $uriParse['query'] ?? '';
        $server['REQUEST_URI'] = $path;
        $server['PATH_INFO'] = $path;
        $server['HTTP_HOST'] = $host;
        /**
         * @var Request $currentRequest
         */
        $currentRequest = App::make('request', [], true);
        App::delete('middleware');
        $currentRequest->withServer($server)
            ->setMethod($method)
            ->withGet($parameters)
            ->setBaseUrl($server['REQUEST_URI'])
            ->setPathinfo(ltrim($server['PATH_INFO'], '/'));
        /**
         * @var Http $http
         */
        $http = App::make('http', []);
        $response = $http->run($currentRequest);
        unset($http, $currentRequest);
        return $response;
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
