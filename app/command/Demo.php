<?php
// +----------------------------------------------------------------------
// | Demo
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\command;

use OpenSpout\Common\Exception\IOException;
use OpenSpout\Reader\CSV\Options;
use OpenSpout\Reader\CSV\Reader;
use OpenSpout\Reader\Exception\ReaderNotOpenedException;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\App;

class Demo extends Command
{
    /**
     * 配置
     * @return void
     */
    protected function configure()
    {
        // 指令配置
        $this->setName('tool:demo')
            ->addArgument('act', Argument::REQUIRED, '执行动作')
            ->addOption('name', null, Option::VALUE_OPTIONAL, '文件名', '')
            ->setDescription('测试');
    }

    /**
     * 把csv数据分割成数组
     * php think tool:demo splitCsv --name=神奇答案之书-女生版.csv
     * @param Input $input
     * @param Output $output
     * @return void
     * @throws IOException
     * @throws ReaderNotOpenedException
     */
    protected function splitCsv(Input $input, Output $output)
    {
        $fileName = $input->getOption('name') ?: 'data.csv';
        $filePath = App::getRuntimePath() . $fileName;
        if (!is_file($filePath)) {
            $output->error('文件不存在');
            return;
        }
        $options = new Options();
        $options->ENCODING = 'GB2312';
        $reader = new Reader($options);
        $reader->open($filePath);
        $data = [];
        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $row) {
                $data[] = $row->toArray();
            }
        }
        $reader->close();
        // 生成数组代码
        $code = $this->prettyArrayCode($data);
        $customCode = "<?php\n\$array = " . $code . ";\n";
        file_put_contents(App::getRuntimePath() . 'prettyArrayCode.php', $customCode);
        $output->info('ok');
    }

    /**
     * 调试
     * php think tool:demo demo
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function demo(Input $input, Output $output)
    {

    }

    /**
     * 美化数组代码
     * @param array $data
     * @param int $indent
     * @return string
     */
    protected function prettyArrayCode(array $data, int $indent = 0): string
    {
        $padding = str_repeat(' ', $indent * 4);
        $lines = ["["];

        foreach ($data as $key => $value) {
            $formattedKey = is_int($key) ? $key : "'$key'";

            if (is_array($value)) {
                $inner = $this->prettyArrayCode($value, $indent + 1);
                $lines[] = "{$padding}    {$formattedKey} => {$inner},";
            } else {
                $formattedValue = var_export($value, true);
                $lines[] = "{$padding}    {$formattedKey} => {$formattedValue},";
            }
        }

        $lines[] = "{$padding}]";
        return implode("\n", $lines);
    }

    /**
     * 执行
     * @param Input $input
     * @param Output $output
     * @return void
     */
    protected function execute(Input $input, Output $output)
    {
        $act = $input->getArgument('act');
        if (!method_exists($this, $act)) {
            $output->error("{$act} 方法不存在");
            return;
        }
        $this->{$act}($input, $output);
    }
}
