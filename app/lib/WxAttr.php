<?php
// +----------------------------------------------------------------------
// | WxAttr.五行相关表
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib;

class WxAttr
{
    /**
     * 五行对应的数字
     * @var array
     */
    public static array $wxToNum = [
        '金' => 1, '木' => 2, '水' => 3, '火' => 4, '土' => 5,
    ];

    /**
     * 获得五行列表
     * @return array
     */
    public static function getNumWxArr(): array
    {
        $arr = self::$wxToNum;
        return array_flip($arr);
    }

    /**
     * 把五行转成数字字符串
     * @param string $wx
     * @return string
     */
    public static function getWxToNum(string $wx): string
    {
        $wxArr = Utils::mbStrSplit($wx);
        $arr = self::$wxToNum;
        $res = [];
        foreach ($wxArr as $v) {
            $res[] = $arr[$v] ?? '';
        }
        return implode('', $res);
    }

    /**
     * 五行数字转五行中文
     * @param int $num
     * @return string
     */
    public static function getNumToWx(int $num): string
    {
        $wxArr = Utils::mbStrSplit((string)$num);
        $arr = array_flip(self::$wxToNum);
        $res = [];
        foreach ($wxArr as $v) {
            $res[] = $arr[$v] ?? '';
        }
        return implode('', $res);
    }
}
