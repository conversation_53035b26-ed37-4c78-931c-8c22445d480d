<?php
// +----------------------------------------------------------------------
// | Hehun. 合婚
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazi;

class Hehun
{
    /**
     * 涵数功能：算出所属宫位(男)
     * @param int $x
     * @return int
     */
    public function safeConvert(int $x): int
    {
        $r = 100 - $x;
        $s = $r % 9;
        if ($s == 5) {
            $s = 2;
        }
        return $s;
    }

    /**
     * 涵数功能：算出所属宫位(女)
     * @param int $x
     * @return int
     */
    public function safeConvertA(int $x): int
    {
        $r = $x - 4;
        $s = $r % 9;
        if ($s == 5) {
            $s = 8;
        }
        return abs($s);
    }

    /**
     * 涵数功能：排出年干支
     * @param string $i
     * @return array|string
     */
    public function shengChen(string $i)
    {
        $shengchen = [
            '00' => '庚辰', '01' => '辛丑', '02' => '壬寅', '03' => '癸卯', '04' => '甲辰', '05' => '乙已', '06' => '丙午',
            '07' => '丁未', '08' => '戊申', '09' => '己酉', '10' => '庚戌', '11' => '辛亥', '12' => '壬子', '13' => '癸丑',
            '14' => '甲寅', '15' => '乙卯', '16' => '丙辰', '17' => '丁巳', '18' => '戊午', '19' => '己未', '20' => '庚申',
            '21' => '辛酉', '22' => '壬戌', '23' => '癸亥', '24' => '甲子', '25' => '乙丑', '26' => '丙寅', '27' => '丁卯',
            '28' => '戊辰', '29' => '己巳', '30' => '庚午', '31' => '辛未', '32' => '壬申', '33' => '癸酉', '34' => '甲戌',
            '35' => '乙亥', '36' => '丙子', '37' => '丁丑', '38' => '戊寅', '39' => '己卯', '40' => '庚辰', '41' => '辛巳',
            '42' => '壬午', '43' => '癸未', '44' => '甲申', '45' => '乙酉', '46' => '丙戌', '47' => '丁亥', '48' => '戊子',
            '49' => '己丑', '50' => '庚寅', '51' => '辛卯', '52' => '壬辰', '53' => '癸巳', '54' => '甲午', '55' => '乙未',
            '56' => '丙申', '57' => '丁酉', '58' => '戊戌', '59' => '己亥', '60' => '庚子', '61' => '辛丑', '62' => '壬寅',
            '63' => '癸卯', '64' => '甲辰', '65' => '乙巳', '66' => '丙午', '67' => '丁未', '68' => '戊申', '69' => '己酉',
            '70' => '庚戌', '71' => '辛亥', '72' => '壬子', '73' => '癸丑', '74' => '甲寅', '75' => '乙卯', '76' => '丙辰',
            '77' => '丁巳', '78' => '戊午', '79' => '己未', '80' => '庚申', '81' => '辛酉', '82' => '壬戌', '83' => '癸亥',
            '84' => '甲子', '85' => '乙丑', '86' => '丙寅', '87' => '丁卯', '88' => '戊辰', '89' => '己巳', '90' => '庚午',
            '91' => '辛未', '92' => '壬申', '93' => '癸酉', '94' => '甲戌', '95' => '乙亥', '96' => '丙子', '97' => '丁丑',
            '98' => '戊寅', '99' => '己卯',
        ];
        if ($i == 'all') {
            $res = $shengchen;
        } else {
            $res = $shengchen[$i];
        }
        return $res;
    }

    /**
     * @param int $i
     * @return string
     */
    public function mgong(int $i)
    {
        $mgong = [
            0 => '离', 1 => '坎', 2 => '坤', 3 => '震', 4 => '巽', 5 => '坤', 6 => '乾', 7 => '兑', 8 => '艮', 9 => '离',
        ];
        return $mgong[$i] ?? '离';
    }

    /**
     * @param int $i
     * @return string
     */
    public function mgongA(int $i)
    {
        $mgong = [
            0 => '离', 1 => '坎', 2 => '坤', 3 => '震', 4 => '巽', 5 => '艮', 6 => '乾', 7 => '兑', 8 => '艮', 9 => '离',
        ];
        return $mgong[$i] ?? '离';
    }

    /**
     * 安全过滤
     * @param string $s
     * @return array|string|string[]
     */
    public function safew(string $s)
    {
        $s = str_replace("|", "", $s);
        $s = str_replace("<", "", $s);
        $s = str_replace(">", "", $s);
        $s = str_replace("\r", "", $s);
        $s = str_replace("\t", "", $s);
        $s = str_replace("\n", "", $s);
        $s = str_replace(" ", "", $s);
        $s = str_replace("?", "", $s);
        $s = str_replace("'", "", $s);
        $s = str_replace('"', "", $s);
        $s = str_replace(".", "", $s);
        return $s;
    }

    /**
     * 东西四宅涵数 dxsz
     * @param int $i
     * @return string
     */
    public function dxsz(int $i): string
    {
        return in_array($i, [0, 1, 3, 4, 9]) ? '东' : '西';
    }

    /**
     * 东西四宅涵数 dxszA
     * @param int $i
     * @return string
     */
    public function dxszA(int $i): string
    {
        return in_array($i, [0, 1, 3, 4, 9]) ? '东' : '西';
    }

    /**
     * 方位
     * @param int $i
     * @return string
     */
    public function fangwei(int $i): string
    {
        $list = [
            0 => '坐南向北', 1 => '坐北向南', 2 => '坐西南向东北', 3 => '坐东向西', 4 => '坐东南向西北',
            5 => '坐西南向东北', 6 => '坐西北向东南', 7 => '坐西向东', 8 => '坐东北向西南', 9 => '坐南向北',
        ];
        return $list[$i] ?? $list[0];
    }

    /**
     * 方位
     * @param int $i
     * @return string
     */
    public function fangweiA(int $i): string
    {
        $list = [
            0 => '坐南向北', 1 => '坐北向南', 2 => '坐西南向东北', 3 => '坐东向西', 4 => '坐东南向西北',
            5 => '坐东北向西南', 6 => '坐西北向东南', 7 => '坐西向东', 8 => '坐东北向西南', 9 => '坐南向北',
        ];
        return $list[$i] ?? $list[0];
    }

    /**
     * 验证是否是邮箱
     * @param string $email
     * @return bool
     */
    public function isemail(string $email): bool
    {
        return strlen($email) > 8 && preg_match("/^[-_.[:alnum:]]+@((([[:alnum:]]|[[:alnum:]][[:alnum:]-]*[[:alnum:]])\.)+([a-z]{2,4})|(([0-9][0-9]?|[0-1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5])\.){3}([0-9][0-9]?|[0-1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5]))$/i", $email);
    }

    /**
     * @param string $social_id
     * @return bool
     */
    public function issocialid(string $social_id): bool
    {
        return strlen($social_id) < 14;
    }

    /**
     * 断月令旺弱
     * @param int $x
     * @param int $y
     * @return int
     */
    public function pdyl(int $x, int $y): int
    {
        // 子 31 ,丑 32 ,寅 33 ,卯 34 ,辰 35 巳 36 ,午 37 ,未 38 ,申 39 ,酉 40 ,戌 41 ,亥 30
        switch ($x) {
            case 31:
                $s = in_array($y, [31, 39, 40, 30]) ? 0 : 1;
                break;
            case 32:
                $s = in_array($y, [31, 32, 35, 36, 37]) ? 0 : 1;
                break;
            case 33:
            case 34: // 卯
                $s = in_array($y, [31, 33, 34, 30]) ? 0 : 1;
                break;
            case 35:
                $s = in_array($y, [32, 35, 36, 37, 38]) ? 0 : 1;
                break;
            case 36: // 巳
            case 37: // 午
                $s = in_array($y, [33, 34, 36, 37]) ? 0 : 1;
                break;
            case 38: // 未
                $s = in_array($y, [35, 36, 37, 38]) ? 0 : 1;
                break;
            case 39: // 申
                $s = in_array($y, [32, 39, 40, 41]) ? 0 : 1;
                break;
            case 40: // 酉
                $s = in_array($y, [32, 35, 38, 39, 40, 41]) ? 0 : 1;
                break;
            case 41: // 戌
                $s = in_array($y, [36, 37, 38, 41]) ? 0 : 1;
                break;
            case 30: // 亥
                $s = in_array($y, [31, 39, 40, 30]) ? 0 : 1;
                break;
            default:
                $s = 0;
                break;
        }
        return $s;
    }

    /**
     * 起运基数
     * @return array
     */
    public function qyjs(): array
    {
        $qyjs = [
            'a' => [
                //0~10 十神名称
                0 => '正印', 1 => '比肩', 2 => '劫财', 3 => '食神', 4 => '伤官', 5 => '偏财', 6 => '正财', 7 => '七杀', 8 => '正官', 9 => '偏印', 20 => '癸', 21 => '甲', 22 => '乙', 23 => '丙', 24 => '丁', 25 => '戊', 26 => '己', 27 => '庚', 28 => '辛', 29 => '壬', 30 => '亥', 31 => '子', 32 => '丑', 33 => '寅', 34 => '卯', 35 => '辰', 36 => '巳', 37 => '午', 38 => '未', 39 => '申', 40 => '酉', 41 => '戌',
            ],
            'b' => [
                30 => '猪', 31 => '鼠', 32 => '牛', 33 => '虎', 34 => '兔', 35 => '龙', 36 => '蛇', 37 => '马', 38 => '羊', 39 => '猴', 40 => '鸡', 41 => '狗',
            ],
            'c' => [
                30 => '水', 31 => '水', 32 => '水', 33 => '木', 34 => '木', 35 => '木', 36 => '火', 37 => '火', 38 => '火', 39 => '金', 40 => '金', 41 => '金',
            ],
            // 天干
            'd' => [
                20 => 'e', 21 => 'a', 22 => 'a', 23 => 'b', 24 => 'b', 25 => 'c', 26 => 'c', 27 => 'd', 28 => 'd', 29 => 'e',
            ],
            'da' => [
                20 => '0', 21 => '1', 22 => '0', 23 => '1', 24 => '0', 25 => '1', 26 => '0', 27 => '1', 28 => '0', 29 => '1',
            ],
            'e' => [
                20 => '火', 21 => '土', 22 => '金', 23 => '水', 24 => '木', 25 => '火', 26 => '土', 27 => '金', 28 => '水', 29 => '木',
            ],
        ];
        return $qyjs;
    }

    /**
     * 年月日时，各干支十神的确定
     * @param int $year
     * @param int $month
     * @param int $day
     * @param int $t_ime
     * @return array
     */
    public function baziHehun(int $year, int $month, int $day, int $t_ime): array
    {
        $yearday = 0;
        $mz = 0;
        $yg = 0;
        $yz = 0;
        $yzg = 0;
        $dzg = 0;
        $mzg = 0;
        $tzg = 0;
        $qyjs = 0;
        // 确定节气 yz 月支  起运基数 qyjs
        $md = $month * 100 + $day;
        if ($md >= 101 and $md <= 105) {
            $mz = 1;
            $qyjs = (30 + $day - 4) / 3;
        } elseif ($md >= 106 and $md <= 203) {
            $mz = 2;
            $qyjs = (($month - 1) * 30 + $day - 6) / 3;
        } elseif ($md >= 204 and $md <= 305) {
            $mz = 3;
            $qyjs = (($month - 2) * 30 + $day - 4) / 3;
        } elseif ($md >= 306 and $md <= 404) {
            $mz = 4;
            $qyjs = (($month - 3) * 30 + $day - 6) / 3;
        } elseif ($md >= 405 and $md <= 504) {
            $mz = 5;
            $qyjs = (($month - 4) * 30 + $day - 5) / 3;
        } elseif ($md >= 505 and $md <= 605) {
            $mz = 6;
            $qyjs = (($month - 5) * 30 + $day - 5) / 3;
        } elseif ($md >= 606 and $md <= 706) {
            $mz = 7;
            $qyjs = (($month - 6) * 30 + $day - 6) / 3;
        } elseif ($md >= 707 and $md <= 807) {
            $mz = 8;
            $qyjs = (($month - 7) * 30 + $day - 7) / 3;
        } elseif ($md >= 808 and $md <= 907) {
            $mz = 9;
            $qyjs = (($month - 8) * 30 + $day - 8) / 3;
        } elseif ($md >= 908 and $md <= 1007) {
            $mz = 10;
            $qyjs = (($month - 9) * 30 + $day - 8) / 3;
        } elseif ($md >= 1008 and $md <= 1106) {
            $mz = 11;
            $qyjs = (($month - 10) * 30 + $day - 8) / 3;
        } elseif ($md >= 1107 and $md <= 1207) {
            $mz = 0;
            $qyjs = (($month - 11) * 30 + $day - 7) / 3;
        } elseif ($md >= 1208 and $md <= 1231) {
            $mz = 1;
            $qyjs = ($day - 8) / 3;
        }

        // 确定年干和年支 yg 年干 yz 年支
        if ($md >= 101 and $md <= 203) {
            $yg = ($year - 4) % 10;
            $yz = ($year - 4) % 12;
        } elseif ($md >= 204 and $md <= 1231) {
            $yg = ($year - 3) % 10;
            $yz = ($year - 3) % 12;
        }

        // 确定月干 mg 月干
        if ($mz > 2 and $mz <= 11) {
            $mg = ($yg * 2 + $mz + 8) % 10;
        } else {
            $mg = ($yg * 2 + $mz) % 10;
        }
        /*
         *从公元0年到目前年份的天数 yearlast
         *计算某月某日与当年1月0日的时间差（以日为单位）yearday
         */
        $yearlast = ($year - 1) * 5 + ($year - 1) / 4 - ($year - 1) / 100 + ($year - 1) / 400;

        for ($i = 1; $i < $month; $i++) {
            switch ($i) {
                case 1:
                case 3:
                case 5:
                case 7:
                case 8:
                case 10:
                case 12:
                    $yearday = $yearday + 31;
                    break;
                case 4:
                case 6:
                case 9:
                case 11:
                    $yearday = $yearday + 30;
                    break;
                case 2:
                    if ($year % 4 == 0 and $year % 100 <> 0 or $year % 400 == 0) {
                        $yearday = $yearday + 29;
                    } else {
                        $yearday = $yearday + 28;
                    }
                    break;
            }
        }
        $yearday = $yearday + $day;
        // 计算日的六十甲子数 day60
        $day60 = ($yearlast + $yearday + 6015) % 60;
        // 确定 日干 dg  日支  dz
        $dg = $day60 % 10;
        $dz = $day60 % 12;
        // 确定 时干 tg 时支 tz
        $tz = ($t_ime + 3) / 2 % 12;
        // tg = (dg*2+tz+8)Mod10
        if ($tz == 0) {
            $tg = ($dg * 2 + $tz) % 10;
        } else {
            $tg = ($dg * 2 + $tz + 8) % 10;
        }
        /*
         * 到此，已经完成把 年月日时 转换成为 生辰八字的任务
         * 确定各地支所纳天干
         * 年支纳干 yzg 月支纳干 mzg  日支纳干 dzg 时支纳干 tzg
         * 年支纳干 yzg
         */
        $list = [
            1 => 0, 2 => 6, 3 => 1, 4 => 2, 5 => 5, 6 => 3,
            7 => 4, 8 => 6, 9 => 7, 10 => 8, 11 => 5, 0 => 9,
        ];
        // 年支纳干 yzg
        $yzg = $list[$yz] ?? 0;
        // 月支纳干 mzg
        $mzg = $list[$mz] ?? 0;
        // 日支纳干 dzg
        $dzg = $list[$dz] ?? 0;
        // 时支纳干 tzg
        $tzg = $list[$tz] ?? 0;
        /*
         * 到此，完成各地支所纳天干的确定任务
         * 确定各支对应的十神
         */
        // 年干十神 $ygs
        $ygs = (($yg + 11 - $dg) + (($dg + 1) % 2) * (($yg + 10 - $dg) % 2) * 2) % 10;
        // 月干十神 $mgs
        $mgs = (($mg + 11 - $dg) + (($dg + 1) % 2) * (($mg + 10 - $dg) % 2) * 2) % 10;
        // 时干十神 $dgs
        $tgs = (($tg + 11 - $dg) + (($dg + 1) % 2) * (($tg + 10 - $dg) % 2) * 2) % 10;
        // 年支十神 yzs
        $yzs = (($yzg + 11 - $dg) + (($dg + 1) % 2) * (($yzg + 10 - $dg) % 2) * 2) % 10;
        // 月支十神 mzs;
        $mzs = (($mzg + 11 - $dg) + (($dg + 1) % 2) * (($mzg + 10 - $dg) % 2) * 2) % 10;
        // 日支十神 dzs
        $dzs = (($dzg + 11 - $dg) + (($dg + 1) % 2) * (($dzg + 10 - $dg) % 2) * 2) % 10;
        // 时支十神 tzs
        $tzs = (($tzg + 11 - $dg) + (($dg + 1) % 2) * (($tzg + 10 - $dg) % 2) * 2) % 10;
        /*
         * 到此，完成年月日时，各干支十神的确定
         * 确定起运数
         */
        return [
            'qyjs' => $qyjs, 'md' => $md, 'yg' => $yg, 'yz' => $yz, 'mg' => $mg, 'yearlast' => $yearlast,
            'yearday' => $yearday, 'day60' => $day60, 'dg' => $dg, 'dz' => $dz, 'tz' => $tz, 'tg' => $tg,
            'mz' => $mz, 'yzg ' => $yzg, 'mzg' => $mzg, 'dzg ' => $dzg, 'tzg' => $tzg, 'ygs' => $ygs,
            'mgs' => $mgs, 'tgs' => $tgs, 'yzs' => $yzs, 'mzs' => $mzs, 'dzs' => $dzs, 'tzs' => $tzs,
        ];
    }
}
