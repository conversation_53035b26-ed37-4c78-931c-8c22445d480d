<?php
// +----------------------------------------------------------------------
// | ShenSha.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazi;

use calendar\Calendar;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\SolarTerm;

class ShenSha
{
    /**
     * 神煞列表
     * @var string[]
     */
    public static $shenShaList = ['太岁', '太阳', '丧门', '太阴勾绞', '五鬼官符', '月德小耗', '岁破大耗', '紫微龙德', '飞廉白虎', '天德福星', '天狗吊客', '六害病符'];

    /**
     * 十二地支
     * @var string[]
     */
    protected array $dz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * 天干
     * @var string[]
     */
    protected array $tg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

    /**
     * 阴阳
     * @var string
     */
    protected string $yinyang = '阴';

    /**
     * 流年，2019年，纪年
     * @var array
     */
    protected array $liuNian = [];

    /**
     * 纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 当前大运序号
     * @var int
     */
    protected int $indexDaYun = 0;

    /**
     * 性别 0 为男 1 为女
     * @var int
     */
    protected int $sex = 0;

    /**
     * 八字基础
     * @var array
     */
    protected array $base = [];

    /**
     * @var Ex|null
     */
    protected ?Ex $lunar = null;

    /**
     * 构造函数
     * @param Ex $lunar 日历相关类
     * @param array $liuNian
     * @throws Exception
     */
    public function __construct(Ex $lunar, array $liuNian = [2019, ['己', '亥']])
    {
        $this->liuNian = $liuNian;
        $this->sex = $lunar->sex;
        $this->base = $lunar->getLunarByBetween();
        $this->lunar = $lunar;
        $this->jiNian = $this->base['jinian'];
        $fate = $this->lunar->getFate();
        $i = BaziExt::getKeyWithArray($liuNian[0], $fate['eight']['year']);
        $i = $i < 0 ? 0 : $i;
        $this->indexDaYun = $i;
        $tmpIndex = array_search($this->base['jinian']['y'][0], $this->tg);
        if ($tmpIndex % 2 == 0) {
            $this->yinyang = '阳';
        }
    }

    /**
     * 命宫
     * @return array
     */
    public function getMingGong(): array
    {
        $dz = Calendar::DI_ZHI;
        $jiNian = $this->jiNian;
        // 从子上起正月，逆查到生月。
        // 月支位置
        //$yueIndex = array_search($jiNian['m'][1], $dz);
        $yueIndex = $this->base['_nongli']['m'];
        $dzIndex = (12 + 1 - $yueIndex) % 12;
        $shiIndex = array_search($jiNian['h'][1], $dz);

        if ($shiIndex >= 3) {
            $dzIndex = ($dzIndex + 12 - $shiIndex + 3) % 12;
        } else {
            $dzIndex = ($dzIndex + 3 - $shiIndex) % 12;
        }
        $dzM = $dz[$dzIndex];
        $list = $this->getlistData($jiNian['y'][0]);
        $result = [];
        foreach ($list as $v) {
            $tmp = preg_split('/(?<!^)(?!$)/u', $v);
            if ($tmp[1] == $dzM) {
                $result = $tmp;
                break;
            }
        }
        return $result;
    }

    /**
     * 大限列表
     * @return array
     */
    public function getDaXian(): array
    {
        $sex = $this->sex;
        $list2 = [
            ['丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑'],
            ['戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑'],
            ['庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑'],
            ['壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑'],
            ['甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥', '甲子', '乙丑'],
        ];
        $mingGong = implode('', $this->getMingGong());
        foreach ($list2 as $v) {
            if (in_array($mingGong, $v)) {
                $list = $v;
                break;
            }
        }
        $result = [];
        $index = (int)array_search($mingGong, $list);
        $orders = 0;// 排序  阳男阴女顺行，阴男阳女逆行
        $tmp = $this->yinyang . ($sex ? '女' : '男');
        if (in_array($tmp, ['阳男', '阴女'])) {
            $orders = 1;
        }
        for ($i = 0; $i < 12; $i++) {
            if ($orders) {
                //$tmpIindex = ($index - $i - 1 + 12) % 12;
                $tmpIindex = ($i + $index + 1) % 12;
            } else {
                //$tmpIindex = ($i + $index + 1) % 12;
                $tmpIindex = ($index - $i + 12) % 12;
            }
            $result[$i] = $list[$tmpIindex];
        }

        return $result;
    }

    /**
     * 获得小限神煞和月煞
     * @return array
     */
    public function getXiaoXianShenSha(): array
    {
        // 地支
        $dz = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
        // 小限十二宫神煞表
        $xx12gssb = [
            '子' => [
                '子' => '太岁',
                '丑' => '太阳',
                '寅' => '丧门',
                '卯' => '太阴勾绞',
                '辰' => '五鬼官符',
                '巳' => '月德小耗',
                '午' => '岁破大耗',
                '未' => '紫微龙德',
                '申' => '飞廉白虎',
                '酉' => '天德福星',
                '戌' => '天狗吊客',
                '亥' => '六害病符',
            ],
            '丑' => [
                '子' => '六害病符',
                '丑' => '太岁',
                '寅' => '太阳',
                '卯' => '丧门',
                '辰' => '太阴勾绞',
                '巳' => '五鬼官符',
                '午' => '月德小耗',
                '未' => '岁破大耗',
                '申' => '紫微龙德',
                '酉' => '飞廉白虎',
                '戌' => '天德福星',
                '亥' => '天狗吊客',
            ],
            '寅' => [
                '子' => '天狗吊客',
                '丑' => '六害病符',
                '寅' => '太岁',
                '卯' => '太阳',
                '辰' => '丧门',
                '巳' => '太阴勾绞',
                '午' => '五鬼官符',
                '未' => '月德小耗',
                '申' => '岁破大耗',
                '酉' => '紫微龙德',
                '戌' => '飞廉白虎',
                '亥' => '天德福星',
            ],
            '卯' => [
                '子' => '天德福星',
                '丑' => '天狗吊客',
                '寅' => '六害病符',
                '卯' => '太岁',
                '辰' => '太阳',
                '巳' => '丧门',
                '午' => '太阴勾绞',
                '未' => '五鬼官符',
                '申' => '月德小耗',
                '酉' => '岁破大耗',
                '戌' => '紫微龙德',
                '亥' => '飞廉白虎',
            ],
            '辰' => [
                '子' => '飞廉白虎',
                '丑' => '天德福星',
                '寅' => '天狗吊客',
                '卯' => '六害病符',
                '辰' => '太岁',
                '巳' => '太阳',
                '午' => '丧门',
                '未' => '太阴勾绞',
                '申' => '五鬼官符',
                '酉' => '月德小耗',
                '戌' => '岁破大耗',
                '亥' => '紫微龙德',
            ],
            '巳' => [
                '子' => '紫微龙德',
                '丑' => '飞廉白虎',
                '寅' => '天德福星',
                '卯' => '天狗吊客',
                '辰' => '六害病符',
                '巳' => '太岁',
                '午' => '太阳',
                '未' => '丧门',
                '申' => '太阴勾绞',
                '酉' => '五鬼官符',
                '戌' => '月德小耗',
                '亥' => '岁破大耗',
            ],
            '午' => [
                '子' => '岁破大耗',
                '丑' => '紫微龙德',
                '寅' => '飞廉白虎',
                '卯' => '天德福星',
                '辰' => '天狗吊客',
                '巳' => '六害病符',
                '午' => '太岁',
                '未' => '太阳',
                '申' => '丧门',
                '酉' => '太阴勾绞',
                '戌' => '五鬼官符',
                '亥' => '月德小耗',
            ],
            '未' => [
                '子' => '月德小耗',
                '丑' => '岁破大耗',
                '寅' => '紫微龙德',
                '卯' => '飞廉白虎',
                '辰' => '天德福星',
                '巳' => '天狗吊客',
                '午' => '六害病符',
                '未' => '太岁',
                '申' => '太阳',
                '酉' => '丧门',
                '戌' => '太阴勾绞',
                '亥' => '五鬼官符',
            ],
            '申' => [
                '子' => '五鬼官符',
                '丑' => '月德小耗',
                '寅' => '岁破大耗',
                '卯' => '紫微龙德',
                '辰' => '飞廉白虎',
                '巳' => '天德福星',
                '午' => '天狗吊客',
                '未' => '六害病符',
                '申' => '太岁',
                '酉' => '太阳',
                '戌' => '丧门',
                '亥' => '太阴勾绞',
            ],
            '酉' => [
                '子' => '太阴勾绞',
                '丑' => '五鬼官符',
                '寅' => '月德小耗',
                '卯' => '岁破大耗',
                '辰' => '紫微龙德',
                '巳' => '飞廉白虎',
                '午' => '天德福星',
                '未' => '天狗吊客',
                '申' => '六害病符',
                '酉' => '太岁',
                '戌' => '太阳',
                '亥' => '丧门',
            ],
            '戌' => [
                '子' => '丧门',
                '丑' => '太阴勾绞',
                '寅' => '五鬼官符',
                '卯' => '月德小耗',
                '辰' => '岁破大耗',
                '巳' => '紫微龙德',
                '午' => '飞廉白虎',
                '未' => '天德福星',
                '申' => '天狗吊客',
                '酉' => '六害病符',
                '戌' => '太岁',
                '亥' => '太阳',
            ],
            '亥' => [
                '子' => '太阳',
                '丑' => '丧门',
                '寅' => '太阴勾绞',
                '卯' => '五鬼官符',
                '辰' => '月德小耗',
                '巳' => '岁破大耗',
                '午' => '紫微龙德',
                '未' => '飞廉白虎',
                '申' => '天德福星',
                '酉' => '天狗吊客',
                '戌' => '六害病符',
                '亥' => '太岁',
            ],
        ];
        // 月限十二宫神煞表
        $yx12gssb = [
            '子' => [
                '寅' => '丧门',
                '卯' => '太阴勾绞',
                '辰' => '五鬼官符',
                '巳' => '月德小耗',
                '午' => '岁破大耗',
                '未' => '紫微龙德',
                '申' => '飞廉白虎',
                '酉' => '天德福星',
                '戌' => '天狗吊客',
                '亥' => '六害病符',
                '子' => '太岁',
                '丑' => '太阳',
            ],
            '丑' => [
                '寅' => '太阳',
                '卯' => '丧门',
                '辰' => '太阴勾绞',
                '巳' => '五鬼官符',
                '午' => '月德小耗',
                '未' => '岁破大耗',
                '申' => '紫微龙德',
                '酉' => '飞廉白虎',
                '戌' => '天德福星',
                '亥' => '天狗吊客',
                '子' => '六害病符',
                '丑' => '太岁',
            ],
            '寅' => [
                '寅' => '太岁',
                '卯' => '太阳',
                '辰' => '丧门',
                '巳' => '太阴勾绞',
                '午' => '五鬼官符',
                '未' => '月德小耗',
                '申' => '岁破大耗',
                '酉' => '紫微龙德',
                '戌' => '飞廉白虎',
                '亥' => '天德福星',
                '子' => '天狗吊客',
                '丑' => '六害病符',
            ],
            '卯' => [
                '寅' => '六害病符',
                '卯' => '太岁',
                '辰' => '太阳',
                '巳' => '丧门',
                '午' => '太阴勾绞',
                '未' => '五鬼官符',
                '申' => '月德小耗',
                '酉' => '岁破大耗',
                '戌' => '紫微龙德',
                '亥' => '飞廉白虎',
                '子' => '天德福星',
                '丑' => '天狗吊客',
            ],
            '辰' => [
                '寅' => '天狗吊客',
                '卯' => '六害病符',
                '辰' => '太岁',
                '巳' => '太阳',
                '午' => '丧门',
                '未' => '太阴勾绞',
                '申' => '五鬼官符',
                '酉' => '月德小耗',
                '戌' => '岁破大耗',
                '亥' => '紫微龙德',
                '子' => '飞廉白虎',
                '丑' => '天德福星',
            ],
            '巳' => [
                '寅' => '天德福星',
                '卯' => '天狗吊客',
                '辰' => '六害病符',
                '巳' => '太岁',
                '午' => '太阳',
                '未' => '丧门',
                '申' => '太阴勾绞',
                '酉' => '五鬼官符',
                '戌' => '月德小耗',
                '亥' => '岁破大耗',
                '子' => '紫微龙德',
                '丑' => '飞廉白虎',
            ],
            '午' => [
                '寅' => '飞廉白虎',
                '卯' => '天德福星',
                '辰' => '天狗吊客',
                '巳' => '六害病符',
                '午' => '太岁',
                '未' => '太阳',
                '申' => '丧门',
                '酉' => '太阴勾绞',
                '戌' => '五鬼官符',
                '亥' => '月德小耗',
                '子' => '岁破大耗',
                '丑' => '紫微龙德',
            ],
            '未' => [
                '寅' => '紫微龙德',
                '卯' => '飞廉白虎',
                '辰' => '天德福星',
                '巳' => '天狗吊客',
                '午' => '六害病符',
                '未' => '太岁',
                '申' => '太阳',
                '酉' => '丧门',
                '戌' => '太阴勾绞',
                '亥' => '五鬼官符',
                '子' => '月德小耗',
                '丑' => '岁破大耗',
            ],
            '申' => [
                '寅' => '岁破大耗',
                '卯' => '紫微龙德',
                '辰' => '飞廉白虎',
                '巳' => '天德福星',
                '午' => '天狗吊客',
                '未' => '六害病符',
                '申' => '太岁',
                '酉' => '太阳',
                '戌' => '丧门',
                '亥' => '太阴勾绞',
                '子' => '五鬼官符',
                '丑' => '月德小耗',
            ],
            '酉' => [
                '寅' => '月德小耗',
                '卯' => '岁破大耗',
                '辰' => '紫微龙德',
                '巳' => '飞廉白虎',
                '午' => '天德福星',
                '未' => '天狗吊客',
                '申' => '六害病符',
                '酉' => '太岁',
                '戌' => '太阳',
                '亥' => '丧门',
                '子' => '太阴勾绞',
                '丑' => '五鬼官符',
            ],
            '戌' => [
                '寅' => '五鬼官符',
                '卯' => '月德小耗',
                '辰' => '岁破大耗',
                '巳' => '紫微龙德',
                '午' => '飞廉白虎',
                '未' => '天德福星',
                '申' => '天狗吊客',
                '酉' => '六害病符',
                '戌' => '太岁',
                '亥' => '太阳',
                '子' => '丧门',
                '丑' => '太阴勾绞',
            ],
            '亥' => [
                '寅' => '太阴勾绞',
                '卯' => '五鬼官符',
                '辰' => '月德小耗',
                '巳' => '岁破大耗',
                '午' => '紫微龙德',
                '未' => '飞廉白虎',
                '申' => '天德福星',
                '酉' => '天狗吊客',
                '戌' => '六害病符',
                '亥' => '太岁',
                '子' => '太阳',
                '丑' => '丧门',
            ],
        ];
        // 命宫
        $mingGong = $this->getMingGong();
        // 命宫地支
        $mingGongDz = $mingGong[1];
        // 干支纪月与节气区间表
        $gzjyjqqjb = [
            '寅' => '立春-惊蛰',
            '卯' => '惊蛰-清明',
            '辰' => '清明-立夏',
            '巳' => '立夏-芒种',
            '午' => '芒种-小暑',
            '未' => '小暑-立秋',
            '申' => '立秋-白露',
            '酉' => '白露-寒露',
            '戌' => '寒露-立冬',
            '亥' => '立冬-大雪',
            '子' => '大雪-小寒',
            // 这里的立春，下一年的立春
            '丑' => '小寒-立春',
        ];
        // 流年节气数据
        $jieQi = SolarTerm::getAllJieQi($this->liuNian[0]);
        $jieQiNext = SolarTerm::getAllJieQi($this->liuNian[0] + 1);

        // 命宫在地支的位置
        $mGdzIndex = (int)array_search($mingGongDz, $dz);
        // 纪年 年地支，在 地支的位置
        $uDzindex = (int)array_search($this->jiNian['y'][1], $dz);
        // 流年 辰 ，在 地支的位置
        $liuDzIndex = (int)array_search($this->liuNian[1][1], $dz);
        // 小限地支位置
        $xiaoDzIndex = (($mGdzIndex + $uDzindex + 12) - $liuDzIndex) % 12;
        // 小限神煞
        $xiaoxianShenSha = $xx12gssb[$this->liuNian[1][1]][$dz[$xiaoDzIndex]];
        // 月限十二神煞
        $y12ss = $yx12gssb[$dz[$xiaoDzIndex]];
        // 月限十二神煞所属节气时间
        $y12ssjq = [];
        foreach ($y12ss as $gz => $name) {
            $jqqj = explode('-', $gzjyjqqjb[$gz]);
            $fwDatetime = [
                $jieQi[$jqqj[0]],
                $jieQi[$jqqj[1]],
            ];
            if (in_array($gz, ['子'])) {
                $fwDatetime = [
                    $jieQi[$jqqj[0]],
                    $jieQiNext[$jqqj[1]],
                ];
            } elseif (in_array($gz, ['丑'])) {
                $fwDatetime = [
                    $jieQiNext[$jqqj[0]],
                    $jieQiNext[$jqqj[1]],
                ];
            }
            $y12ssjq[] = [
                'gz' => $gz,
                'fw_cn' => $jqqj,
                'fw_datetime' => $fwDatetime,
            ];
        }

        return [
            // 小限神煞
            'xiaosha' => $xiaoxianShenSha,
            // 月限十二神煞
            'yuesha' => array_values($y12ss),
            // 月限十二神煞节气数据
            'yuesha_jq_month' => $y12ssjq,
        ];
    }

    /**
     * 获得流年神煞
     * @return string
     */
    public function getYearXian(): string
    {
        $dz = Calendar::DI_ZHI;
        $list5 = self::$shenShaList;
        $jiNian = $this->lunar->getLunarTganDzhi();
        $ydzIndex = (int)array_search($jiNian['y'][1], $dz);
        $liuDzIndex = (int)array_search($this->liuNian[1][1], $dz);
        $index = (12 - $ydzIndex + $liuDzIndex) % 12;
        return $list5[$index];
    }

    /**
     * 求大限 小限使用
     * @param string $tg 天干
     * @return array
     */
    protected function getlistData(string $tg): array
    {
        switch ($tg) {
            case '甲':
            case '己':
                $list = ['丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑'];
                break;
            case '乙':
            case '庚':
                $list = ['戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑'];
                break;
            case '丙':
            case '辛':
                $list = ['庚寅', '辛卯', '壬辰', '癸巳', '甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑'];
                break;
            case '丁':
            case '壬':
                $list = ['壬寅', '癸卯', '甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑'];
                break;
            default:
                $list = ['甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥', '甲子', '乙丑'];
                break;
        }
        return $list;
    }

    /**
     * 获得日煞
     * @param string $yueShenSha 月限神煞
     * @param int $day 农历日
     * @return string
     */
    public static function getShenShaByDay(string $yueShenSha, int $day): string
    {
        $list = self::$shenShaList;
        $index = (int)array_search($yueShenSha, $list);
        $dayIndex = ($index + $day - 1) % 12;
        return $list[$dayIndex];
    }
}
