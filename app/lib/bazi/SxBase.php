<?php
// +----------------------------------------------------------------------
// | SxBase.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazi;

use calendar\Calendar;

class SxBase
{
    /**
     * @var array 生肖拼音
     */
    protected array $sxPy = ['shu', 'niu', 'hu', 'tu', 'long', 'she', 'ma', 'yang', 'hou', 'ji', 'gou', 'zhu'];

    /**
     * 生肖拼音转中文
     * @param string $py
     * @return string
     */
    public function pyToCn(string $py): string
    {
        $id = $this->pYtoNum($py);
        if (false === $id) {
            return '';
        }
        return Calendar::C_ZODIAC[$id];
    }

    /**
     * 生肖拼音转序号
     * @param string $py
     * @return bool|int
     */
    public function pytoNum(string $py)
    {
        $sxPy = $this->sxPy;
        if (!in_array($py, $sxPy)) {
            return false;
        }
        $id = (int)array_search($py, $sxPy);
        return $id;
    }

    /**
     * 生肖转序号
     * @param string $sx
     * @return int
     */
    public function cnToNum(string $sx): int
    {
        return (int)array_search($sx, Calendar::C_ZODIAC);
    }

    /**
     * 数字转中文生肖
     * @param int $num
     * @return string
     */
    public function numToCn(int $num = 0): string
    {
        $list = Calendar::C_ZODIAC;
        return $list[$num] ?? '';
    }

    /**
     * 根据生肖获得 生肖拼间 序号，生肖
     * @param string $sx
     * @return array
     */
    public function getSxArr(string $sx): array
    {
        $num1 = $this->cnToNum($sx);
        $listPy = $this->sxPy;
        return [
            'name' => $sx, 'py' => $listPy[$num1], 'num' => $num1 + 1,
        ];
    }

    /**
     * 获得全部生肖
     * @return array
     */
    public function getAllSx(): array
    {
        return Calendar::C_ZODIAC;
    }

    /**
     * 根据地支获取生肖
     * @param string $dz
     * @return string
     */
    public function getsxByDz(string $dz): mixed
    {
        $listDz = Calendar::DI_ZHI;
        $dzIndex = array_search($dz, $listDz);
        if (false === $dzIndex) {
            return '';
        }
        $listZ = Calendar::C_ZODIAC;
        return $listZ[$dzIndex] ?? '';
    }

    /**
     * 根据生肖获得地支
     * @param string $sx
     * @return string
     */
    public function getDzBySx(string $sx): string
    {
        $listDz = Calendar::DI_ZHI;
        $listZ = Calendar::C_ZODIAC;
        $dzIndex = (int)array_search($sx, $listZ);
        return $listDz[$dzIndex];
    }

    /**
     * 获得生肖描述
     * @param string $sx 生肖
     * @return string
     */
    public function getBase(string $sx): string
    {
        $id = (int)array_search($sx, Calendar::C_ZODIAC);
        $list = [
            '属鼠性柔和，为个坦诚，单纯，具有敏悦的直观，以感觉来判断事物的能力强，属性急、急功型、虚荣心强，常因异性之事而失败，经济、财运佳。',
            '属牛沉默寡言，为人正直、纯朴、不愿伪装表面，富于耐性，勤劳，努力坚毅的习惯，思考力强，常坚持已见，易失去益友，富于同情心，有老大气概，做事很精细，晚年将鸿图大展。',
            '属虎富于正义感，讲义理，男性外刚而内柔，女性则外柔而内刚，具有组织的才能，富于发明，革命性的开拓精神，热心公益，就女性而言，是个不让须眉型。',
            '属兔为人乐观、快活、不愿过拘束的生活，追求理想而前进，但因实践能力薄弱，故事多不成，凡有新流行，就是走在尖端，防卫观念非常敏锐。',
            '属龙人品高尚，刚毅，有强烈的向上心，属聪明才智型，富于热情，缺乏思考，耐心，事常半途而废，表面虽冷，其实内心有极强的仁侠骨气，亲切而处处为人着想。',
            '属蛇具有周蜜的思考力，立定志愿后必勇往迈进，表面虽坦诚，其实是神经质，猜疑心强的人，智能高，研究心强，热心探索未知的世界，具有审美感，是个艺术天才。',
            '属马为人豪爽、活泼、直感力，推断力强，头脑灵活、机警迅速，对任何事都很坦率，正直，善于口才，好辩、对事物的好恶差距大，很容易走极端，是个性急、任性的人。',
            '属羊柔和而稳重，有深重的人情味，重仁义的好人，具有细腻的思考力，有毅力，可得一技之长，表面柔和而内心却是坚持已见，反抗精神强的人，防卫本能极优。',
            '属猴幽默、机智、活泼、所以多方面的才能常超越人群、人缘好、但重名利，独占欲强，处事敏捷，自尊心强，手灵活，善于摹仿，开放性而宽厚。',
            '属鸡表现力强，能注意到事务的细节为人温和、谦虚而谨慎、有强烈的经济观念，但虚荣心强、爱享受、喜派头，走流行、对异性的诱惑常无法克制。',
            '属狗为人正直、守规矩、有责任感，对上司、长辈敬重，服从，工作认真，自我观念极浓，缺乏通融性，发表力，所以常失去许多美好的事物，防卫意识强。',
            '属猪崇尚义理，人情，纯情，律已甚严、缺乏应变能力，经济观念发达，具有消极性、理财力，平时沉默寡言，独断独出心裁行，常因异性的事而起',
        ];
        return $list[$id];
    }

    /**
     * 生肖相冲
     * @param string $sx 生肖中文
     * @return array
     */
    public function getChong(string $sx): array
    {
        $list = ['鼠马', '牛羊', '虎猴', '兔鸡', '龙狗', '蛇猪'];
        $str = '';
        foreach ($list as $v) {
            if (!str_contains($v, $sx)) {
                continue;
            }
            $str = str_replace($sx, '', $v);
            break;
        }
        if (empty($str)) {
            $str = '鼠';
        }
        return $this->getSxArr($str);
    }

    /**
     * 根据年份获得生肖
     * @param int $y
     * @return string
     */
    public function getSxByYear(int $y): string
    {
        $data = Calendar::C_ZODIAC;
        $index = ($y - 1900) % 12;
        return $data[$index];
    }

    /**
     * 根据出生年份获得年龄
     * @param int $y 出生年份
     * @param int $oyear 不传的话为当前年份
     * @return int
     */
    public function getAge(int $y, int $oyear = 0): int
    {
        if (($oyear == 0)) {
            $oyear = (int)date('Y');
        }
        return $oyear - $y + 1;
    }

    /**
     * 根据生肖获得对应年份和年龄
     * @param string $sx 生肖中文
     * @param int $oYear 年份
     * @return array
     */
    public function getYearList(string $sx, int $oYear = 0): array
    {
        $num = $this->cnToNum($sx);
        $startYear = 1900 + $num;
        if ($oYear == 0) {
            $oYear = (int)date('Y');
        }
        $res = [];
        for ($i = $startYear; $i <= $oYear; $i = $i + 12) {
            $res[$i] = $this->getAge($i);
        }
        asort($res);
        $yearList = array_slice(array_keys($res), 0, 8);
        $age = array_slice($res, 0, 8);
        return [
            'y' => $yearList, 'a' => $age,
        ];
    }

    /**
     * 根据生肖获得三合
     * @param string $sx
     * @return array
     */
    public function getShangHe(string $sx): array
    {
        $list = [
            '鼠' => ['龙', '猴'], '牛' => ['蛇', '鸡'], '虎' => ['马', '狗'], '兔' => ['猪', '羊'],
            '龙' => ['鼠', '猴'], '蛇' => ['鸡', '牛'], '马' => ['虎', '狗'], '羊' => ['兔', '猪'],
            '猴' => ['鼠', '龙'], '鸡' => ['蛇', '牛'], '狗' => ['虎', '马'], '猪' => ['兔', '羊'],
        ];
        $data = $list[$sx] ?? $list['鼠'];
        $res = [];
        foreach ($data as $v) {
            $res[] = $this->getSxArr($v);
        }
        return $res;
    }

    /**
     * 根据生肖获得六合生肖
     * @param string $sx
     * @return array
     */
    public function getLiuHe(string $sx): array
    {
        $list = [
            '牛', '鼠', '猪', '狗', '鸡', '猴', '羊', '马', '蛇', '龙', '兔', '虎',
        ];
        $num = $this->cnToNum($sx);
        $sx1 = $list[$num];
        return $this->getSxArr($sx1);
    }

    /**
     * 获得相刑数据
     * @param string $sx
     * @return array
     */
    public function getXianXin(string $sx): array
    {
        $list = [
            ['兔'], ['牛', '狗'], ['蛇', '猴'], ['鼠'], ['龙'], ['虎'], ['马'], ['牛', '狗'], ['虎', '蛇'], ['鸡'], ['羊', '牛'], ['猪'],
        ];
        $num = $this->cnToNum($sx);
        $data = $list[$num];
        $res = [];
        foreach ($data as $v) {
            $res[] = $this->getSxArr($v);
        }
        return $res;
    }

    /**
     * 获得相克数据
     * @param string $sx
     * @return array
     */
    public function getXianKe(string $sx): array
    {
        $list = [
            ['羊', '马', '兔', '鸡'], ['龙', '马', '羊', '狗', '兔'], ['蛇', '猴'], ['鼠', '牛', '龙', '鸡', '马'],
            ['狗', '牛', '龙', '兔'], ['虎', '猴', '猪'], ['鼠', '牛', '兔', '马'], ['鼠', '牛', '狗'], ['虎', '猪', '蛇'],
            ['兔', '鸡', '狗'], ['牛', '龙', '羊', '鸡'], ['猪', '猴', '蛇'],
        ];
        $num = $this->cnToNum($sx);
        $data = $list[$num];
        $res = [];
        foreach ($data as $v) {
            $res[] = $this->getSxArr($v);
        }
        return $res;
    }

    /**
     * 相破
     * @param string $sx 生肖
     * @return array
     */
    public function getxianPo(string $sx): array
    {
        $list = ['鼠鸡', '牛龙', '虎猪', '兔马', '蛇猴', '羊狗'];
        $str = '';
        foreach ($list as $v) {
            if (!str_contains($v, $sx)) {
                continue;
            }
            $str = str_replace($sx, '', $v);
            break;
        }
        if (empty($str)) {
            $str = '鼠';
        }
        return $this->getSxArr($str);
    }
}
