<?php
// +----------------------------------------------------------------------
// | 八字扩展
// +----------------------------------------------------------------------
// | Copyright (c) 2025 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazi;

use calendar\Ex;
use calendar\plugin\WuXing;

class BaziEx
{
    /**
     * @var Ex|null
     */
    protected ?Ex $lunar = null;

    /**
     * 八字数组
     * @var array
     */
    protected array $jiNian;

    /**
     * 初始化
     * @param Ex $lunar
     */
    public function __construct(Ex $lunar)
    {
        $this->lunar = $lunar;
        $this->jiNian = $lunar->getLunarTganDzhi();
    }

    /**
     * 设置初始值
     * @param Ex $lunar
     * @return $this
     */
    public function setLunar(Ex $lunar)
    {
        $this->lunar = $lunar;
        $this->jiNian = $lunar->getLunarTganDzhi();
        return $this;
    }

    /**
     * 旺度
     * @return string
     */
    public function getWangDu(): string
    {
        $jiNian = $this->jiNian;
        // 日空
        $tempEmptyDay = $this->lunar->getEmptyDay();
        // 1 各地支是否在日空亡数组里 0 在 1 不在
        $tempEmptyDayArray = [];
        $jiNianWx = [];
        $wuXingAttr = BaziExt::$wuXingAttr;
        foreach ($jiNian as $key => $val) {
            $jiNianWx[$key] = [$wuXingAttr[$val[0]], $wuXingAttr[$val[1]]];
            $tempEmptyDayArray[$key] = in_array($val[1], $tempEmptyDay) ? 0 : 1;
        }
        // 2.确定月干，时干，日支是否有力
        $day1 = $this->getShenFu($jiNian['m'][1], $jiNian['d'][1]);// 月支+日支
        $day2 = $this->getShenFu($jiNian['h'][1], $jiNian['d'][1]);// 时支+日支
        $month1 = $this->getShenFu($jiNian['y'][0], $jiNian['m'][0]);// 年干+月干
        $ydzToDtg = $this->getShenFu($jiNian['y'][1], $jiNian['d'][0]);// 年支对日干
        $ydzToMdz = $this->getShenFu($jiNian['y'][1], $jiNian['m'][1]);// 年支+月支
        $month2 = $this->getShenFu($jiNian['m'][1], $jiNian['m'][0]);// 月支+月干
        $mtgToDtg = $this->getShenFu($jiNian['m'][0], $jiNian['d'][0]);// 月干+日干
        $htgToDtg = $this->getShenFu($jiNian['h'][0], $jiNian['d'][0]);// 时干+日干
        $ddzToDtg = $this->getShenFu($jiNian['d'][1], $jiNian['d'][0]);// 日支+日干
        $ddzToMdz = $this->getShenFu($jiNian['d'][1], $jiNian['m'][1]);// 日支对月支
        $hdzToHtg = $this->getShenFu($jiNian['h'][1], $jiNian['h'][0]);// 时支对时干
        $hdzToDtg = $this->getShenFu($jiNian['h'][1], $jiNian['d'][0]);// 时支对日干
        // 有力无力，原始状态 1 有力 0 无力
        $testPros = [
            // 'year' => $this->getShenFu($jiNian['y'][0], $jiNian['y'][1]),//
            // 如果月支不为空，则看年干以及月支对月干是否有生扶作用。否，为无力。是，为有力。
            // 如果月支为空，则看年干对月干是否有生扶作用，否，无力。是，有力。
            'month' => $tempEmptyDayArray['m'] ? (($month1 || $month2) ? 1 : 0) : $month1,
            // 如果时支不为空，时支对时干是否有生扶作用，是，为有力。否，为无力。
            // 如果时支为空，这时干为有力。
            'hour' => ($hdzToHtg && $tempEmptyDayArray['h']) ? 1 : 0,
            // 如果月支不为空，则看月支以及时支对日支是否有生扶作用。否，为无力。是，为有力。
            // 如果月支为空，则看时支对日支是否有生扶作用。否，为无力。是，为有力。
            // 如果月支为空，时支也为空。则，日支为有力。
            'day' => (($day1 || $day2)) ? 1 : 0,
        ];
        if (!$tempEmptyDayArray['m']) {
            if (!$tempEmptyDayArray['h']) {
                $testPros['day'] = 1;
            } else {
                $testPros['day'] = $day2;
            }
        }

        // 生扶 1或克泄耗 0
        $testPros1 = [
            'month' => $mtgToDtg, // 月干+日干
            'hour' => $htgToDtg, // 时干+日干
            'day' => $ddzToDtg, // 时干+日干
        ];
        $testPros2 = [
            'month' => ($testPros['month'] && $mtgToDtg) ? 1 : 0,
            'hour' => ($testPros['hour'] && $htgToDtg) ? 1 : 0,
            'day' => ($testPros['day'] && $ddzToDtg) ? 1 : 0,
        ];

        // 判断月令是否两次受制 看年支和日支对月支是否有克泄耗。(对应克泄耗表格）
        $two = 0;
        if (!$ydzToMdz) {
            // 年地支+月地支
            $two++;
        }
        if (!$ddzToMdz) {
            $two++;
        }
        if ($tempEmptyDayArray['m'] == 0) {
            $two = 1;
        }
        if (!$tempEmptyDayArray['m'] && !$tempEmptyDayArray['y']) {
            $wr = $this->getWr($jiNian['d'][0], $jiNian['d'][1]);
        } elseif ($tempEmptyDayArray['m']) {
            // 日主旺弱
            $wr = $this->getWr($jiNian['d'][0], $jiNian['m'][1]);
        } else {
            $wr = $this->getWr($jiNian['d'][0], $jiNian['y'][1]);
        }

        // 月支对日干是否起生扶作用 是为1  不是为0
        // $mDtg = $this->getShenFu($jiNian['m'][1], $jiNian['d'][0]);
        $testPros2Str = implode('', $testPros2);
        $keyList = [
            $two == 2 ? 2 : 1, // 两次受制 2 一次或0为 1
            $wr == '旺' ? 1 : 0, // 旺 1 弱 0
            $testPros2Str,
        ];
        $keyStr = implode('|', $keyList);
        //  0身旺格  1身弱格  2从旺格  3从弱格
        // $list = ['身旺格', '身弱格', '从旺格', '从弱格'];
        $result = '';
        switch ($keyList[0] . $keyList[1]) {
            case '21':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif (in_array($testPros2Str, ['110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($testPros2Str != '000' && $ydzToDtg && $ddzToDtg) {
                    $result = '身旺格';
                } else {
                    $result = '身弱格';
                }
                break;
            case '20':
                if ($testPros2Str == '000') {
                    $result = array_sum($testPros1) == 3 ? '从弱格' : '身弱格';
                } elseif (in_array($testPros2Str, ['111', '110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($ydzToDtg && $ddzToDtg) {
                    $result = '身旺格';
                } else {
                    if ($ydzToDtg && $ddzToDtg) {
                        $result = '身旺格';
                    } else {
                        $result = '身弱格';
                    }
                }
                break;
            case '11':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif ($testPros2Str == '000') {
                    $result = '身旺格';
                } elseif (array_sum($testPros2) == 2) {
                    $tmpNum3 = $ydzToDtg + $ddzToDtg + $hdzToDtg;
                    $result = ($tmpNum3 >= 2) ? '从旺格' : '身旺格';
                } else {
                    $tmpNum3 = $ydzToDtg + $ddzToDtg + $hdzToDtg;
                    $tmpNum4 = $mtgToDtg + $htgToDtg + $ddzToDtg;
                    $result = ($tmpNum3 >= 2 && $tmpNum4 >= 2) ? '从旺格' : '身旺格';
                }
                break;
            case '10':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif (in_array($testPros2Str, ['110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($testPros2Str == '000') {
                    $result = array_sum($testPros1) < 2 ? '从弱格' : '身弱格';
                } else {
                    $result = array_sum($testPros1) == 3 ? '从弱格' : '身弱格';
                }
                break;
        }
        return $result;
    }

    /**
     * $gz1对$gz2判断是否起好的作用 1 好的 0 坏的
     * @param string $gz1 干支1
     * @param string $gz2 干支2
     * @return int
     */
    public function getShenFu(string $gz1, string $gz2): int
    {
        $xianfuList = ['金金', '金水', '木木', '木火', '水水', '水木', '火火', '火土', '土土', '土金'];
        $wuXingAttr = BaziExt::$wuXingAttr;
        if (in_array($wuXingAttr[$gz1] . $wuXingAttr[$gz2], $xianfuList)) {
            return 1;
        }
        return 0;
    }

    /**
     * 获得旺弱
     * @param string $tg 纪年天干
     * @param string $dz 地支
     * @return string
     */
    public function getWr(string $tg, string $dz): string
    {
        $list = [
            '甲' => ['寅' => 1, '卯' => 1, '辰' => 0, '巳' => 0, '午' => 0, '未' => 0, '申' => 0, '酉' => 0, '戌' => 0, '亥' => 1, '子' => 1, '丑' => 0],
            '乙' => ['寅' => 1, '卯' => 1, '辰' => 0, '巳' => 0, '午' => 0, '未' => 0, '申' => 0, '酉' => 0, '戌' => 0, '亥' => 1, '子' => 1, '丑' => 0],
            '丙' => ['寅' => 1, '卯' => 1, '辰' => 0, '巳' => 1, '午' => 1, '未' => 0, '申' => 0, '酉' => 0, '戌' => 0, '亥' => 0, '子' => 0, '丑' => 0],
            '丁' => ['寅' => 1, '卯' => 1, '辰' => 0, '巳' => 1, '午' => 1, '未' => 0, '申' => 0, '酉' => 0, '戌' => 0, '亥' => 0, '子' => 0, '丑' => 0],
            '戊' => ['寅' => 0, '卯' => 0, '辰' => 1, '巳' => 1, '午' => 1, '未' => 1, '申' => 0, '酉' => 0, '戌' => 1, '亥' => 0, '子' => 0, '丑' => 1],
            '己' => ['寅' => 0, '卯' => 0, '辰' => 1, '巳' => 1, '午' => 1, '未' => 1, '申' => 0, '酉' => 0, '戌' => 1, '亥' => 0, '子' => 0, '丑' => 1],
            '庚' => ['寅' => 0, '卯' => 0, '辰' => 1, '巳' => 0, '午' => 0, '未' => 1, '申' => 1, '酉' => 1, '戌' => 1, '亥' => 0, '子' => 0, '丑' => 1],
            '辛' => ['寅' => 0, '卯' => 0, '辰' => 1, '巳' => 0, '午' => 0, '未' => 1, '申' => 1, '酉' => 1, '戌' => 1, '亥' => 0, '子' => 0, '丑' => 1],
            '壬' => ['寅' => 0, '卯' => 0, '辰' => 0, '巳' => 0, '午' => 0, '未' => 0, '申' => 1, '酉' => 1, '戌' => 0, '亥' => 1, '子' => 1, '丑' => 0],
            '癸' => ['寅' => 0, '卯' => 0, '辰' => 0, '巳' => 0, '午' => 0, '未' => 0, '申' => 1, '酉' => 1, '戌' => 0, '亥' => 1, '子' => 1, '丑' => 0],
        ];
        $res = $list[$tg][$dz] ?? 1;
        return $res ? '旺' : '弱';
    }

    /**
     * 喜用忌闲仇
     * @return array
     */
    public function getxiYongJi(): array
    {
        $wangDu = $this->getWangDu();
        return $this->getXyByWD($wangDu);
    }

    /**
     * 根据旺度获得喜用忌闲仇
     * @param string $wangDu
     * @return array
     */
    protected function getXyByWD(string $wangDu): array
    {
        $jiNian = $this->jiNian;
        $wuXingAttr = BaziExt::$wuXingAttr;
        $dWx = $wuXingAttr[$jiNian['d'][0]];
        $list = [
            '身旺格' => '生', '从旺格' => '泄', '身弱格' => '耗', '从弱格' => '生',
        ];
        $yongShen = WuXing::getWxGuanxi($dWx, $list[$wangDu]);
        $xiShen = WuXing::getWxGuanxi($yongShen, '泄');
        // 喜神生用神，用神生闲神，闲神生仇神，仇神生忌神
        $xianShen = WuXing::getWxGuanxi($yongShen, '生');
        $qiuShen = WuXing::getWxGuanxi($xianShen, '生');
        $jiShen = WuXing::getWxGuanxi($qiuShen, '生');
        return [
            'yong' => $yongShen, 'xi' => $xiShen, 'xian' => $xianShen, 'qiu' => $qiuShen, 'ji' => $jiShen,
        ];
    }

    /**
     * 八字同类和异类
     * @return array
     */
    public function getBaziFenXin(): array
    {
        $temp = [
            '金' => [['土'], ['水', '火', '木']],
            '木' => [['水'], ['金', '火', '土']],
            '水' => [['金'], ['火', '木', '土']],
            '火' => [['木'], ['土', '水', '金']],
            '土' => [['火'], ['水', '木', '金']],
        ];
        // 五行数值表
        $likeGodNum = $this->lunar->getWuxingNum();
        $totalAll = array_sum($likeGodNum);
        $jiNian = $this->lunar->getLunarTganDzhi();
        // 异类
        $unKing = [];
        $likeGodDay = $this->lunar->wuXingAttr[$jiNian['d'][0]];

        $unKingTotal = 0;
        foreach ($temp[$likeGodDay][1] as $val) {
            $unKing[$val] = [
                'wx' => $val,
                'fen' => round($likeGodNum[$val], 3),
            ];
            $unKingTotal += round($likeGodNum[$val], 3);
        }
        $unKingTotal = round($unKingTotal, 3);
        $kingTotal = round($likeGodNum[$likeGodDay] + $likeGodNum[$temp[$likeGodDay][0][0]], 3);
        $differ = $kingTotal - $unKingTotal;
        if ($differ > 0) {
            $differTitle = '偏旺';
        } elseif ($differ == 0) {
            $differTitle = '平和';
        } else {
            $differTitle = '偏弱';
        }
        $tmp2 = [];
        foreach ($likeGodNum as $k => $v) {
            $tmp2[] = [$k, $v, round(($v / $totalAll) * 100)];
        }
        if ($differ > 0.1 || $differ == 0.1) {
            $unKing2 = array_column($unKing, 'fen', 'wx');
            $likeGod = array_search(min($unKing2), $unKing2);
        } elseif ($differ >= 0) {
            $likeGod = $likeGodDay;
        } else {
            $likeGod = $likeGodNum[$likeGodDay] <= $likeGodNum[$temp[$likeGodDay][0][0]] ? $likeGodDay : $temp[$likeGodDay][0][0];
        }
        return [
            'fraction' => $tmp2,
            // 同类
            'king' => [
                'wx' => $likeGodDay . $temp[$likeGodDay][0][0],
                'fen' => $kingTotal,
            ],
            'unking' => [
                'wx' => implode('', array_column($unKing, 'wx')),
                'fen' => $unKingTotal,
            ],
            // 旺衰得分
            'differ' => [
                'fen' => round($differ, 3), 'title' => $differTitle,
            ],
            'like_god' => $likeGod,
        ];
    }

    /**
     * 滴天髓格局
     * @return string
     */
    public function getWangDu2(): string
    {
        $jiNian = $this->jiNian;
        // 1 各地支是否在日空亡数组里 0 在 1 不在
        $jiNianWx = [];
        $wuXingAttr = BaziExt::$wuXingAttr;
        foreach ($jiNian as $key => $val) {
            $jiNianWx[$key] = [$wuXingAttr[$val[0]], $wuXingAttr[$val[1]]];
        }
        // 2.确定月干，时干，日支是否有力
        $day1 = $this->getShenFu($jiNian['m'][1], $jiNian['d'][1]);// 月支+日支
        $day2 = $this->getShenFu($jiNian['h'][1], $jiNian['d'][1]);// 时支+日支
        $month1 = $this->getShenFu($jiNian['y'][0], $jiNian['m'][0]);// 年干+月干
        $ydzToDtg = $this->getShenFu($jiNian['y'][1], $jiNian['d'][0]);// 年支对日干
        $ydzToMdz = $this->getShenFu($jiNian['y'][1], $jiNian['m'][1]);// 年支+月支
        $month2 = $this->getShenFu($jiNian['m'][1], $jiNian['m'][0]);// 月支+月干
        $mtgToDtg = $this->getShenFu($jiNian['m'][0], $jiNian['d'][0]);// 月干+日干
        $htgToDtg = $this->getShenFu($jiNian['h'][0], $jiNian['d'][0]);// 时干+日干
        $ddzToDtg = $this->getShenFu($jiNian['d'][1], $jiNian['d'][0]);// 日支+日干
        $ddzToMdz = $this->getShenFu($jiNian['d'][1], $jiNian['m'][1]);// 日支对月支
        $hdzToHtg = $this->getShenFu($jiNian['h'][1], $jiNian['h'][0]);// 时支对时干
        $hdzToDtg = $this->getShenFu($jiNian['h'][1], $jiNian['d'][0]);// 时支对日干
        // 有力无力，原始状态 1 有力 0 无力
        $testPros = [
            // 'year' => $this->getShenFu($jiNian['y'][0], $jiNian['y'][1]),//
            // 如果月支不为空，则看年干以及月支对月干是否有生扶作用。否，为无力。是，为有力。
            // 如果月支为空，则看年干对月干是否有生扶作用，否，无力。是，有力。
            'month' => ($month1 || $month2) ? 1 : 0,
            // 时支对时干是否有生扶作用，是，为有力。否，为无力。
            'hour' => $hdzToHtg ? 1 : 0,
            // 看月支以及时支对日支是否有生扶作用。否，为无力。是，为有力。
            'day' => (($day1 || $day2)) ? 1 : 0,
        ];

        // 生扶 1或克泄耗 0
        $testPros1 = [
            'month' => $mtgToDtg, // 月干+日干
            'hour' => $htgToDtg, // 时干+日干
            'day' => $ddzToDtg, // 时干+日干
        ];
        $testPros2 = [
            'month' => ($testPros['month'] && $mtgToDtg) ? 1 : 0,
            'hour' => ($testPros['hour'] && $htgToDtg) ? 1 : 0,
            'day' => ($testPros['day'] && $ddzToDtg) ? 1 : 0,
        ];

        // 判断月令是否两次受制 看年支和日支对月支是否有克泄耗。(对应克泄耗表格）
        $two = 0;
        if (!$ydzToMdz) {
            // 年地支+月地支
            $two++;
        }
        if (!$ddzToMdz) {
            $two++;
        }
        $wr = $this->getWr($jiNian['d'][0], $jiNian['y'][1]);

        // 月支对日干是否起生扶作用 是为1  不是为0
        // $mDtg = $this->getShenFu($jiNian['m'][1], $jiNian['d'][0]);
        $testPros2Str = implode('', $testPros2);
        $keyList = [
            $two == 2 ? 2 : 1, // 两次受制 2 一次或0为 1
            $wr == '旺' ? 1 : 0, // 旺 1 弱 0
            $testPros2Str,
        ];
        $keyStr = implode('|', $keyList);
        //  0身旺格  1身弱格  2从旺格  3从弱格
        // $list = ['身旺格', '身弱格', '从旺格', '从弱格'];
        $result = '';
        switch ($keyList[0] . $keyList[1]) {
            case '21':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif (in_array($testPros2Str, ['110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($testPros2Str != '000' && $ydzToDtg && $ddzToDtg) {
                    $result = '身旺格';
                } else {
                    $result = '身弱格';
                }
                break;
            case '20':
                if ($testPros2Str == '000') {
                    $result = array_sum($testPros1) == 3 ? '从弱格' : '身弱格';
                } elseif (in_array($testPros2Str, ['111', '110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($ydzToDtg && $ddzToDtg) {
                    $result = '身旺格';
                } else {
                    if ($ydzToDtg && $ddzToDtg) {
                        $result = '身旺格';
                    } else {
                        $result = '身弱格';
                    }
                }
                break;
            case '11':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif ($testPros2Str == '000') {
                    $result = '身旺格';
                } elseif (array_sum($testPros2) == 2) {
                    $tmpNum3 = $ydzToDtg + $ddzToDtg + $hdzToDtg;
                    $result = ($tmpNum3 >= 2) ? '从旺格' : '身旺格';
                } else {
                    $tmpNum3 = $ydzToDtg + $ddzToDtg + $hdzToDtg;
                    $tmpNum4 = $mtgToDtg + $htgToDtg + $ddzToDtg;
                    $result = ($tmpNum3 >= 2 && $tmpNum4 >= 2) ? '从旺格' : '身旺格';
                }
                break;
            case '10':
                if ($testPros2Str == '111') {
                    $result = '从旺格';
                } elseif (in_array($testPros2Str, ['110', '011', '101'])) {
                    $result = '身旺格';
                } elseif ($testPros2Str == '000') {
                    $result = array_sum($testPros1) < 2 ? '从弱格' : '身弱格';
                } else {
                    $result = array_sum($testPros1) == 3 ? '从弱格' : '身弱格';
                }
                break;
        }
        return $result;
    }

    /**
     * 滴天髓格局喜用神
     * @return array
     */
    public function getxiYongJi2(): array
    {
        $wangDu = $this->getWangDu2();
        $xy = $this->getXyByWD($wangDu);
        $jiNian = $this->jiNian;
        $wuXingAttr = BaziExt::$wuXingAttr;
        $dWx = $wuXingAttr[$jiNian['d'][0]];
        $shenArr = $this->getXyShen($xy, $dWx);
        return [
            'wangdu' => $wangDu,
            'xy' => $xy,
            'shen' => $shenArr,
        ];
    }

    /**
     * 根据喜用忌闲仇获得相关十神
     * @param array $xyWxArr 喜用忌闲仇
     * @param string $dWx 五行
     * @return array
     */
    public function getXyShen(array $xyWxArr, string $dWx): array
    {
        $list = [
            '金' => ['金' => '比劫', '木' => '才财', '水' => '食伤', '火' => '官杀', '土' => '印枭'],
            '木' => ['金' => '官杀', '木' => '比劫', '水' => '印枭', '火' => '食伤', '土' => '才财'],
            '水' => ['金' => '印枭', '木' => '食伤', '水' => '比劫', '火' => '才财', '土' => '官杀'],
            '火' => ['金' => '才财', '木' => '印枭', '水' => '官杀', '火' => '比劫', '土' => '食伤'],
            '土' => ['金' => '食伤', '木' => '官杀', '水' => '才财', '火' => '印枭', '土' => '比劫'],
        ];
        $shenArr = [];
        foreach ($xyWxArr as $k => $v) {
            $shenArr[$k] = $list[$dWx][$v];
        }
        return $shenArr;
    }

    /**
     * 强度表喜用忌闲仇
     * @return array
     */
    public function getXiyongJi3(): array
    {
        $yongShen = $this->lunar->getLikeGod2();
        $xiShen = WuXing::getWxGuanxi($yongShen, '泄');
        // 喜神生用神，用神生闲神，闲神生仇神，仇神生忌神
        $xianShen = WuXing::getWxGuanxi($yongShen, '生');
        $qiuShen = WuXing::getWxGuanxi($xianShen, '生');
        $jiShen = WuXing::getWxGuanxi($qiuShen, '生');
        $arr = [
            'yong' => $yongShen,
            'xi' => $xiShen,
            'xian' => $xianShen,
            'qiu' => $qiuShen,
            'ji' => $jiShen,
        ];
        $jiNian = $this->jiNian;
        $wuXingAttr = BaziExt::$wuXingAttr;
        $dWx = $wuXingAttr[$jiNian['d'][0]];
        $shen = $this->getXyShen($arr, $dWx);
        return [
            'xy' => $arr,
            'shen' => $shen,
        ];
    }

    /**
     * 强度表喜用忌闲仇
     * 2021-09-15调整喜用神算法
     * @return array
     */
    public function getXiyongJi4(): array
    {
        $fenXi = $this->lunar->getWxFenxi();
        return [
            'xy' => $fenXi['xy'],
            'shen' => $fenXi['shen'],
        ];
    }
}
