<?php
// +----------------------------------------------------------------------
// | Nongli.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>> 2017/5/10
// +----------------------------------------------------------------------

namespace app\lib\bazi;

use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\SolarTerm;

class Nongli
{
    /**
     * @var string[]
     */
    public static array $tg = [
        '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸',
    ];

    /**
     * @var string[]
     */
    public static array $dz = [
        '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥',
    ];

    /**
     * @var string[]
     */
    public static array $jq = [
        '立春', '雨水', '惊蛰', '春分', '清明', '谷雨', '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
        '立秋', '处暑', '白露', '秋分', '寒露', '霜降', '立冬', '小雪', '大雪', '冬至', '小寒', '大寒',
    ];

    /**
     * @var string[]
     */
    public static array $weekDay = [
        '星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六',
    ];

    /**
     * @var array
     */
    public static array $nongliri = [
        null, '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '十',
        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
        '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十',
        '三十一',
    ];

    /**
     * @var array
     */
    public static array $benBaGua = [
        1 => [4 => 1, 3 => 1, 2 => 1, 1 => 1],
        2 => [4 => 2, 3 => 2, 2 => 1, 1 => 1],
        3 => [4 => 3, 3 => 1, 2 => 2, 1 => 1],
        4 => [4 => 4, 3 => 2, 2 => 2, 1 => 1],
        5 => [4 => 5, 3 => 1, 2 => 1, 1 => 2],
        6 => [4 => 6, 3 => 2, 2 => 1, 1 => 2],
        7 => [4 => 7, 3 => 1, 2 => 2, 1 => 2],
        8 => [4 => 8, 3 => 2, 2 => 2, 1 => 2],
    ];


    /**
     * 是否闰年
     * @param int $y 年
     * @return int 返回1(代表闰年）或0(非闰年)
     */
    public static function isYrun(int $y): int
    {
        // 是否闰年: 返回1(代表闰年）或0(非闰年)
        return ($y % 100 == 0 ? $result = ($y % 400 == 0 ? 1 : 0) : $result = ($y % 4 == 0 ? 1 : 0));
    }

    /**
     * 农历
     * @param string $pbzday 时间
     * @param string $ce 条件
     * @return false|mixed|string|void
     * @throws Exception
     */
    public static function nongLi(string $pbzday, string $ce)
    {
        $tadey = $pbzday;
        $year = substr($tadey, 0, 4);
        $month = (int)(substr($tadey, 5, 2));
        $day = (int)(substr($tadey, 8, 2));
        $hour = (int)(substr($tadey, 11, 2));
        $minute = (int)(substr($tadey, 14, 2));
        $nongLi = self::getNongli($year, $month, $day, $hour);
        if (false == $nongLi) {
            return false;
        }
        $nowday = $nongLi[0] . '(' . $nongLi[5] . ')年' . $nongLi[1] . '月' . $nongLi['2'] . $nongLi[3];
        if ($ce == 'quan') {
            return ($nowday);
        }
        if ($ce == 'nian') {
            return $nongLi[0];
        }
        if ($ce == 'yue') {
            return $nongLi[1];
        }
        if ($ce == 'ri') {
            return $nongLi[2];
        }
    }

    /**
     * 获得农历
     * @param int $year 年
     * @param int $month 月
     * @param int $day 日
     * @param int $hour 时
     * @return array|bool array [0] => 丙申 [1] => 十二 [2] => 初四 [3] => 早子 [4] => 2016 [5] => 猴
     * @throws Exception
     */
    public static function getNongli(int $year, int $month, int $day, int $hour)
    {
        if ($year < 1910 || $year > 2080) {
            return false;
        }
        $time = "{$year}-{$month}-{$day} {$hour}:00:00";
        $lunar = Ex::date($time);
        $base = $lunar->getLunarByBetween();
        return [
            implode('', $base['jinian']['y']),
            str_replace('月', '', $base['nongli']['m']),
            $base['nongli']['d'],
            $base['jinian']['h'][1],
            $base['_nongli']['y'],
            $base['shengxiao'],
        ];
    }

    /**
     * 节气
     * @param int $y 年
     * @param int $m 月
     * @param int $d 日
     * @param int $h 时
     * @param int $mins 分
     * @return string
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public static function makeJq(int $y, int $m, int $d, int $h, int $mins)
    {
        $times = mktime($h, $mins, 0, (int)$m, (int)$d, $y);
        $timeStr = date('Y-m-d H:i:s', $times);
        $jq = SolarTerm::getJieQiCur($timeStr);
        return date('Y年m月d日H时i分', strtotime($jq['current'][1])) . $jq['current'][0];
    }

    /**
     * 求地支序号
     * @param string $gzx
     * @return false|int|string
     */
    public static function getDzOrder(string $gzx)
    {
        $dzs = substr($gzx, -3);
        $dz = self::$dz;
        $xu = array_search($dzs, $dz);
        return $xu;
    }

    /**
     * 求天干序号
     * @param string $gzx
     * @return false|int|string
     */
    public static function getTgorder(string $gzx)
    {
        $tg = self::$tg;
        $tgs = substr($gzx, 0, 3);
        $xu = array_search($tgs, $tg);
        return $xu;
    }
}
