<?php
// +----------------------------------------------------------------------
// | NineStar 九星飞盘算法
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

use app\lib\bazi\BaziExt;
use calendar\exceptions\Exception;
use calendar\Lunar;
use calendar\SolarTerm;

class NineStar
{
    /**
     * 八字相关
     * @var array
     */
    public array $base;

    /**
     * 公历
     * @var array
     */
    public array $solar;

    /**
     * 节气顺逆序号
     * @var int
     */
    protected int $jqIndex = -1;

    /**
     * 上中下元
     * @var string
     */
    protected string $yuanY = '';

    /**
     * 序号和颜色
     * @var array
     */
    protected array $numberColor = [
        '一白', '二黑', '三碧',
        '四绿', '五黄', '六白',
        '七赤', '八白', '九紫',
    ];

    /**
     * 序号对应的风水位置
     * @var array|string[]
     */
    protected array $numberFswz = [
        '桃花位', '病符位', '是非位',
        '文昌位', '五黄煞', '偏财位',
        '破财位', '正财位', '喜神位',
    ];

    /**
     * 序号对应的作用
     * @var array|string[]
     */
    protected array $numberZy = [
        '偏财 升职 桃花',
        '小病 忧愁 抑郁',
        '是非口舌 情绪',
        '文昌 学业 事业',
        '病毒 灾祸 血光',
        '偏财 贵人 桃花',
        '官非 破财 贼劫',
        '正财 加薪 置业',
        '添丁 喜庆 姻缘',
    ];

    /**
     * 序号对应的旧作用
     * @var array|string[]
     */
    protected array $numberZyOld = [
        '主感情、人缘',
        '主疾病、伤痛',
        '主是非、官灾',
        '主事业、官位',
        '主凶灾、祸患',
        '主横财、贵人',
        '主破财、漏财',
        '主财运、置业',
        '主姻缘、感情',
    ];

    /**
     * 玄空九星（玄空风水）
     * @var array
     */
    protected array $nameXuanKong = [
        '贪狼', '巨门', '禄存',
        '文曲', '廉贞', '武曲',
        '破军', '左辅', '右弼',
    ];

    /**
     * 序号对应的五行
     * @var array
     */
    protected array $wuXing = [
        '水', '土', '木',
        '木', '土', '金',
        '金', '土', '火',
    ];

    /**
     * 北斗九星
     * @var array
     */
    protected array $nameBeiDou = [
        '天枢', '天璇', '天玑',
        '天权', '玉衡', '开阳',
        '摇光', '洞明', '隐元',
    ];

    /**
     * 奇门九星（奇门遁甲，也称天盘九星）
     * @var array
     */
    protected array $nameQiMen = [
        '天蓬', '天芮', '天冲',
        '天辅', '天禽', '天心',
        '天柱', '天任', '天英',
    ];

    /**
     * 太乙九神（太乙神数|河图洛书九星
     * @var array|array[]
     */
    protected array $starTaiYi = [
        1 => [
            'star' => '太乙星', 'jx' => '吉神',
            'info' => ['门中太乙明，星官号贪狼，横财皆喜旺，婚姻大吉昌。先天兑西方 后天坎北上', '出入无阻挡，参谒见贤良，此行三五里，黑衣别阴阳。应贪狼之宿 其号为文昌'],
        ],
        2 => [
            'star' => '摄提星', 'jx' => '凶神',
            'info' => ['门前见摄提，百事必忧疑，相生犹自可，相克祸必临。先天坎北方 后天坤西南', '死门并相会，老妇哭悲啼，求谋并吉事，尽皆不相宜。应巨门之宿 其号为病符'],
        ],
        3 => [
            'star' => '轩辕星', 'jx' => '安神',
            'info' => ['出入会轩辕，凡事必缠牵，相生全不美，相克更忧煎。先天艮东北 后天震东方', '远行多不利，博彩散家财，求谋红黑事，皆不尽心怀。应禄存之宿 其号为蚩尤'],
        ],
        4 => [
            'star' => '招摇星', 'jx' => '安神',
            'info' => ['招摇号木星，当之事莫行，相克行人阻，阴人口舌迎。先天坤西南 后天巽东南', '梦寐多惊惧，屋响斧自鸣，阴阳消息理，万法弗违情。应文曲之宿 其号为文昌'],
        ],
        5 => [
            'star' => '天符星', 'jx' => '凶神',
            'info' => ['五鬼为天符，当门阴女谋，相克无好事，行路阻中途。其位镇中央 威扬于八面', '走失难寻觅，道逢有尼姑，此星当门值，万事有灾除。应廉贞之宿 号为正关煞'],
        ],
        6 => [
            'star' => '青龙星', 'jx' => '吉神',
            'info' => ['神光跃青龙，财气喜重重，求谋百事全，横财也兴隆。先天离南方 后天乾西北', '更逢相生旺，休言克破凶，见贵安营寨，万事总吉同。应武曲之宿 其号为官贵'],
        ],
        7 => [
            'star' => '咸池星', 'jx' => '凶神',
            'info' => ['吾将为咸池，当之尽不宜，出入多不利，相克有灾情。先天巽东南 后天兑正西', '博彩废田财，祸起求财路，动用虚惊退，反复逆风吹。应破军之宿 其号为肃煞'],
        ],
        8 => [
            'star' => '太阴星', 'jx' => '吉神',
            'info' => ['坐临太阴星，百祸不相侵，求谋悉成就，知交有觅寻。先天乾西北 后天艮东北', '回风归来路，恐有殃伏起，密语中记取，慎乎莫轻行。应左辅之宿 其号为财星'],
        ],
        9 => [
            'star' => '天乙星', 'jx' => '吉神',
            'info' => ['迎来天乙星，相逢百事兴，运用和合庆，茶酒喜相迎。先天震正东 后天离正南', '求谋并嫁娶，好合有天成，祸福如神验，吉凶甚分明。应右弼之宿 其号为吉庆'],
        ],
    ];

    /**
     * 宫飞顺序
     * @var string[]
     */
    protected array $listGong = [
        '中', '乾', '兑',
        '艮', '离', '坎',
        '坤', '震', '巽',
    ];

    /**
     * 名称对应的方位
     * @var string[]
     */
    protected array $listPos = [
        '巽' => '东南', '离' => '正南', '坤' => '西南',
        '震' => '正东', '中' => '正中', '兑' => '正西',
        '艮' => '东北', '坎' => '正北', '乾' => '西北',
    ];

    /**
     * 名称对应的位置
     * @var int[]
     */
    protected array $listPosNum = [
        '巽' => 1, '离' => 2, '坤' => 3,
        '震' => 4, '中' => 5, '兑' => 6,
        '艮' => 7, '坎' => 8, '乾' => 9,
    ];

    /**
     * 构造函数
     * @param string $time
     * @throws Exception
     * @throws \DateInvalidTimeZoneException
     * @throws \DateMalformedStringException
     */
    public function __construct(string $time)
    {
        $lunar = Lunar::date($time);
        $nongli = $lunar->getNongLi();
        // 公历
        $this->solar = $lunar->getSolarArr();
        // 节气，获得当前节气的前后区间
        $jieqiArr = SolarTerm::getJieQiCur($time);
        // 基础数据
        $this->base = [
            'nongli' => [
                'y' => $nongli['cn_y'],
                'm' => $nongli['cn_m'],
                'd' => $nongli['cn_d'],
            ],
            '_nongli' => [
                'y' => (int)$nongli['y'],
                'm' => (int)$nongli['m'],
                'd' => (int)$nongli['d'],
            ],
            // 是否闰月
            'leap' => $nongli['leap'],
            // 纪年
            'jinian' => $lunar->getLunarTganDzhi(),
            // 节气
            'jieqi' => $jieqiArr,
        ];
        // 当前节气
        $jieqi = $jieqiArr['current'][0];
        $listJieQiArr = [
            // 顺
            ['冬至', '小寒', '大寒', '立春'],
            ['雨水', '惊蛰', '春分', '清明'],
            ['谷雨', '立夏', '小满', '芒种'],
            // 逆
            ['夏至', '小暑', '大暑', '立秋'],
            ['处暑', '白露', '秋分', '寒露'],
            ['霜降', '立冬', '小雪', '大雪'],
        ];
        $jieqiKey = 0;
        foreach ($listJieQiArr as $k => $v) {
            if (in_array($jieqi, $v)) {
                $jieqiKey = $k;
            }
        }
        $this->jqIndex = $jieqiKey;
        $this->setYuanYear();
    }

    /**
     * 排盘
     * @return array
     */
    public function getPiaipan(): array
    {
        $pan = [
            'y' => $this->getYearPan(),
            'm' => $this->getMonthPan(),
            'd' => $this->getDayPan(),
            'h' => $this->getHourPan(),
        ];
        $result = [];
        foreach ($pan as $k => $v) {
            foreach ($v as $gong => $v1) {
                $offset = $v1 - 1;
                $result[$gong]['name'] = $gong;
                $result[$gong]['pos'] = $this->listPos[$gong];
                $result[$gong]['pos_num'] = $this->listPosNum[$gong];
                $data = [
                    'num' => $v1,
                    'title' => $this->numberColor[$offset],
                    // 五行
                    'wx' => $this->wuXing[$offset],
                    'jx' => $this->getJx($v1),
                    // 玄空
                    'xuankong' => $this->nameXuanKong[$offset],
                    // 洛书
                    'luoshu' => $this->starTaiYi[$v1],
                    // 奇门
                    'qi_meng' => $this->nameQiMen[$offset],
                    // 北斗
                    'beidou' => $this->nameBeiDou[$offset],
                    // 风水位置
                    'fswz' => $this->numberFswz[$offset],
                    // 作用
                    'zhuoyong' => explode(' ', $this->numberZy[$offset]),
                    // 旧作用
                    'old_zhuoyong' => explode(' ', $this->numberZyOld[$offset]),
                ];
                $result[$gong][$k] = $data;
            }
        }
        $result = array_column($result, null, 'pos_num');
        ksort($result);
        return array_values($result);
    }

    /**
     * 年基础盘(宫位对应数字序号)
     * @return array
     */
    public function getYearPan(): array
    {
        $jiNainYearStr = implode('', $this->base['jinian']['y']);
        // 干支在60甲子中的位置
        $yearIndex = $this->getJiaZiIndex($jiNainYearStr);
        $startKey = $yearIndex % 9;

        $listYuan = $this->getYuanYearUse();
        $startYearNum = $listYuan[$startKey];
        $result = [];
        $listGong = $this->listGong;
        foreach ($listGong as $k => $v) {
            $tmp = ($startYearNum + $k) % 9;
            $num = $tmp ?: 9;
            $result[$v] = $num;
        }
        return $result;
    }

    /**
     * 获得月基础盘(宫位对应数字序号)
     * @return array
     */
    public function getMonthPan(): array
    {
        $listMonth = [
            [8, 7, 6, 5, 4, 3, 2, 1, 9, 8, 7, 6],
            [5, 4, 3, 2, 1, 9, 8, 7, 6, 5, 4, 3],
            [2, 1, 9, 8, 7, 6, 5, 4, 3, 2, 1, 9],
        ];
        $ydz = $this->base['jinian']['y'][1];
        $numM = array_search($this->base['jinian']['m'][1], ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑']) + 1;
        // 时间
        if (in_array($ydz, ['子', '午', '卯', '酉'])) {
            $monthKey = $listMonth[0][$numM - 1];
        } elseif (in_array($ydz, ['辰', '戌', '丑', '未'])) {
            $monthKey = $listMonth[1][$numM - 1];
        } else {
            $monthKey = $listMonth[2][$numM - 1];
        }
        $listGong = $this->listGong;
        $result = [];
        foreach ($listGong as $k => $v) {
            $tmp = ($monthKey + $k) % 9;
            $result[$v] = $tmp ?: 9;
        }
        return $result;
    }

    /**
     * 获得日基础盘(宫位对应数字序号)
     * @return array
     */
    public function getDayPan()
    {
        $jiNainDayStr = implode('', $this->base['jinian']['d']);
        // 干支在60甲子中的位置
        $dayIndex = $this->getJiaZiIndex($jiNainDayStr);
        $startKey = $dayIndex % 9;
        $listYuan = $this->getDayUse();
        $start = $listYuan[$startKey];
        $result = [];
        $listGong = $this->listGong;
        foreach ($listGong as $k => $v) {
            $tmp = ($start + $k) % 9;
            $num = $tmp ?: 9;
            $result[$v] = $num;
        }
        return $result;
    }

    /**
     * 获得时基础盘(宫位对应数字序号)
     * @return array
     */
    public function getHourPan()
    {
        $listHour = [
            // 顺
            [1, 2, 3, 4, 5, 6, 7, 8, 9, 1, 2, 3],
            [4, 5, 6, 7, 8, 9, 1, 2, 3, 4, 5, 6],
            [7, 8, 9, 1, 2, 3, 4, 5, 6, 7, 8, 9],
            // 逆
            [9, 8, 7, 6, 5, 4, 3, 2, 1, 9, 8, 7],
            [6, 5, 4, 3, 2, 1, 9, 8, 7, 6, 5, 4],
            [3, 2, 1, 9, 8, 7, 6, 5, 4, 3, 2, 1],
        ];
        $ddz = $this->base['jinian']['d'][1];
        $hdz = $this->base['jinian']['h'][1];
        $numH = array_search($hdz, ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']) + 1;
        // 取得ID
        if (in_array($ddz, ['子', '午', '卯', '酉'])) {
            $id = 0;
        } elseif (in_array($ddz, ['辰', '戌', '丑', '未'])) {
            $id = 1;
        } else {
            $id = 2;
        }
        // 顺 1顺 0逆
        $isShun = $this->jqIndex < 3 ? 1 : 0;
        $pos = $isShun ? $id : ($id + 3);
        $hourKey = $listHour[$pos][$numH - 1];
        $listGong = $this->listGong;
        $result = [];
        foreach ($listGong as $k => $v) {
            $tmp = ($hourKey + $k) % 9;
            $result[$v] = $tmp ?: 9;
        }
        return $result;
    }

    /**
     * 获得节气所用的数据列表(日)
     * @return int[]
     */
    protected function getDayUse(): array
    {
        $list = [
            // 顺
            [1, 2, 3, 4, 5, 6, 7, 8, 9],
            [7, 8, 9, 1, 2, 3, 4, 5, 6],
            [4, 5, 6, 7, 8, 9, 1, 2, 3],
            // 逆
            [9, 8, 7, 6, 5, 4, 3, 2, 1],
            [3, 2, 1, 9, 8, 7, 6, 5, 4],
            [6, 5, 4, 3, 2, 1, 9, 8, 7],
        ];
        return $list[$this->jqIndex] ?? $list[0];
    }

    /**
     * 指定干支在60甲子中的位置
     * @param string $gz
     * @return int
     */
    protected function getJiaZiIndex(string $gz): int
    {
        $jiaZi = array_flip(BaziExt::JIA_ZI);
        return $jiaZi[$gz] ?? 0;
    }

    /**
     * 设置年上中下元
     * @return void
     */
    private function setYuanYear()
    {
        $year = $this->solar['y'];
        if ($year >= 1864 && $year <= 1923) {
            $yuan = '上元';
        } elseif ($year >= 1924 && $year <= 1983) {
            $yuan = '中元';
        } else {
            $yuan = '下元';
        }
        $this->yuanY = $yuan;
    }

    /**
     * 获得九星吉凶
     * @param int $num
     * @return string
     */
    private function getJx(int $num): string
    {
        $list = ['吉', '凶', '凶', '吉', '凶', '吉', '凶', '吉', '吉'];
        $key = ($num - 1) % 9;
        return $list[$key];
    }

    /**
     * 获得年
     * @return int[]
     */
    private function getYuanYearUse(): array
    {
        $list = [
            '上元' => [1, 9, 8, 7, 6, 5, 4, 3, 2],
            '中元' => [4, 3, 2, 1, 9, 8, 7, 6, 5],
            '下元' => [7, 6, 5, 4, 3, 2, 1, 9, 8],
        ];
        return $list[$this->yuanY];
    }
}
