<?php
// +----------------------------------------------------------------------
// | ShaShen.煞神
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

class ShaShen
{
    /**
     * 八字纪年
     * @var array
     */
    protected array $jiNian;
    /**
     * 性别 0 男 1 女
     * @var int
     */
    protected int $sex;

    /**
     * 获得八字神煞
     * @param array $jiNian 纪年
     * @param int $sex 性别
     * @return array[]
     */
    public function detail(array $jiNian, int $sex): array
    {
        $ygz = implode('', $jiNian['y']);
        $mgz = implode('', $jiNian['m']);
        $dgz = implode('', $jiNian['d']);
        $hgz = implode('', $jiNian['h']);
        $result = ['y' => [], 'm' => [], 'd' => [], 'h' => []];
        $ytg = $jiNian['y'][0];
        $ydz = $jiNian['y'][1];
        $mtg = $jiNian['m'][0];
        $mdz = $jiNian['m'][1];
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $htg = $jiNian['h'][0];
        $hdz = $jiNian['h'][1];
        $yinYangStr = $this->getYinYang($ytg);
        $yinYangStr .= $sex ? '女' : '男';
        foreach ($jiNian as $k => $v) {
            $tmpStr = implode('', $v);
            if ($this->getTianYiGuiRen($ytg, $v[1]) || $this->getTianYiGuiRen($dtg, $v[1])) {
                $result[$k][] = '天乙贵人';
            }
            if ($this->getTianGuan($ytg . $v[1]) || $this->getTianGuan($dtg . $v[1])) {
                $result[$k][] = '天官贵人';
            }
            if ($this->getTaiJi($ytg . $v[1]) || $this->getTaiJi($dtg . $v[1])) {
                $result[$k][] = '太极贵人';
            }
            if ($this->getWenChang($ytg . $v[1]) || $this->getWenChang($dtg . $v[1])) {
                $result[$k][] = '文昌贵人';
            }
            if ($this->getGuoYin($ytg . $v[1]) || $this->getGuoYin($dtg . $v[1])) {
                $result[$k][] = '国印贵人';
            }
            if ($this->getLuShen($dtg . $v[1])) {
                $result[$k][] = '禄神';
            }
            if ($this->getYangRen($dtg . $v[1])) {
                $result[$k][] = '羊刃';
            }
            if ($this->getJinYu($dtg . $v[1])) {
                $result[$k][] = '金舆';
            }
            if (in_array($k, ['d', 'h']) && $this->getJinShen($tmpStr)) {
                $result[$k][] = '金神贵人';
            }
            if ($this->getFuXing($ytg . $v[1]) || $this->getFuXing($dtg . $v[1])) {
                $result[$k][] = '福星贵人';
            }
            if ($this->getTianChu($ytg . $v[1]) || $this->getTianChu($dtg . $v[1])) {
                $result[$k][] = '天厨贵人';
            }
            if ($this->getLiuXia($dtg . $v[1]) || $this->getLiuXia($ytg . $v[1])) {
                $result[$k][] = '流霞';
            }
            if ($this->getHongYan($dtg . $v[1])) {
                $result[$k][] = '红艳';
            }
            if ($this->getCiGuan($ytg, $tmpStr) || $this->getCiGuan($dtg, $tmpStr)) {
                $result[$k][] = '词馆';
            }
            if ($this->getYuTang($ytg . $v[1])) {
                $result[$k][] = '玉堂';
            }
            if ($this->getXueTang($ytg . $v[1]) || $this->getXueTang($dtg . $v[1])) {
                $result[$k][] = '学堂';
            }
            if (in_array($k, ['d', 'h']) && $this->getJieDu($ytg . $v[1])) {
                $result[$k][] = '节度贵人';
            }
            if ($this->getYueDe($mdz . $v[0])) {
                $result[$k][] = '月德贵人';
            }
            if ($this->getYueDeHe($mdz . $v[0])) {
                $result[$k][] = '月德合';
            }
            if ($this->getTianDe($mdz . $v[0])) {
                $result[$k][] = '天德贵人';
            }
            if ($this->getTianDeHe($mdz . $v[0])) {
                $result[$k][] = '天德合';
            }
            if (($k !== 'y' && $this->getYiMa($ydz . $v[1])) || ($k !== 'd' && $this->getYiMa($ddz . $v[1]))) {
                $result[$k][] = '驿马';
            }
            if (($k !== 'y' && $this->getHuaGai($ydz . $v[1])) || ($k !== 'd' && $this->getHuaGai($ddz . $v[1]))) {
                $result[$k][] = '华盖';
            }
            if ($this->getDeGuiRen($mdz . $v[0]) && $this->getXiuGuiRen($mdz . $v[0])) {
                $result[$k][] = '德秀贵人';
            }
            if ($k !== 'y' && $this->getHongLuan($ydz . $v[1])) {
                $result[$k][] = '红鸾';
            }
            if ($this->getTianXi($ydz . $v[1])) {
                $result[$k][] = '天喜';
            }
            if ($this->getXianChi($ydz . $v[1])) {
                $result[$k][] = '咸池';
            }
            if ($this->getGuChen($ydz . $v[1])) {
                $result[$k][] = '孤辰';
            }
            if ($this->getGuaShu($ydz . $v[1])) {
                $result[$k][] = '寡宿';
            }
            // 丧门的具体查法以年支或日支为主，查其余三支
            if (($k !== 'y' && $this->getSangMen($ydz . $v[1])) || ($k !== 'd' && $this->getSangMen($ddz . $v[1]))) {
                $result[$k][] = '丧门';
            }
            if (($k !== 'y' && $this->getPiMa($ydz . $v[1])) || ($k !== 'd' && $this->getPiMa($ddz . $v[1]))) {
                $result[$k][] = '披麻';
            }
            if ($this->getDiaoKe($ydz . $v[1]) || $this->getDiaoKe($ddz . $v[1])) {
                $result[$k][] = '吊客';
            }
            if (($k !== 'y' && $this->getWangShen($ydz . $v[1])) || ($k !== 'd' && $this->getWangShen($ddz . $v[1]))) {
                $result[$k][] = '亡神';
            }
            if ($k !== 'y' && $this->getZaiSha($ydz . $v[1])) {
                $result[$k][] = '灾煞';
            }
            if ($k !== 'm' && $this->getTianYi($mdz . $v[1])) {
                $result[$k][] = '天医';
            }
            if ($this->getLiuE($ydz . $v[1])) {
                $result[$k][] = '六厄';
            }
            if ($k !== 'y' && $this->getYuanChen($yinYangStr, $ydz . $v[1])) {
                $result[$k][] = '元辰';
            }
            if ($k !== 'y' && $this->getGouSha($yinYangStr, $ydz . $v[1])) {
                $result[$k][] = '勾煞';
            }
            if ($k !== 'y' && $this->getJiaoSha($yinYangStr, $ydz . $v[1])) {
                $result[$k][] = '绞煞';
            }
            if ($k !== 'y' && $this->getJiangXing($ydz . $v[1])) {
                if (!in_array('将星', $result['y'])) {
                    $result['y'][] = '将星';
                }
                if (!in_array('将星', $result[$k])) {
                    $result[$k][] = '将星';
                }
            }
            if ($k !== 'd' && $this->getJiangXing($ddz . $v[1])) {
                if (!in_array('将星', $result['d'])) {
                    $result['d'][] = '将星';
                }
                if (!in_array('将星', $result[$k])) {
                    $result[$k][] = '将星';
                }
            }
        }
        if ($this->getKuiGang($dgz)) {
            $result['d'][] = '魁罡贵人';
        }
        // 天福贵人以日干查年月时支（同时满足）
        if ($this->getTianFu($dtg, $ygz) && $this->getTianFu($dtg, $mgz) && $this->getTianFu($dtg, $hgz)) {
            $result['d'][] = '天福贵人';
        }
        if ($this->getShiErDaBai($dgz)) {
            $result['d'][] = '十恶大败';
        }
        if ($this->getYinChaYangChuo($dgz)) {
            $result['d'][] = '阴差阳错';
        }
        if ($this->getBaZhuan($dgz)) {
            $result['d'][] = '八专';
        }
        if ($this->getJiuChou($dgz)) {
            $result['d'][] = '九丑';
        }
        if ($this->getShiLing($dgz)) {
            $result['d'][] = '十灵日';
        }
        // 四柱的日主或时柱上同时出现乙巳、丁巳、辛亥、戊申、壬寅、戊午、壬子、丙午中的两个
        if ($this->getGuLuanSha($dgz, $hgz) && $dgz !== $hgz) {
            $result['d'][] = '孤鸾煞';
        }
        if ($this->getJieLuKongWang($dtg . $hdz)) {
            $result['h'][] = '截路空亡';
        }
        if ($this->getTianShe($mdz, $dgz)) {
            $result['d'][] = '天赦';
        }
        if ($this->getMaierSha($ydz . $hdz)) {
            $result['h'][] = '埋儿煞';
        }
        $dzArr = array_column($jiNian, 1);
        if ($this->getTianLuo($dzArr)) {
            foreach ($jiNian as $k => $v) {
                if (in_array($v[1], ['戌', '亥'])) {
                    $result[$k][] = '天罗';
                }
            }
        }
        if ($this->getDiWang($dzArr)) {
            foreach ($jiNian as $k => $v) {
                if (in_array($v[1], ['辰', '巳'])) {
                    $result[$k][] = '地网';
                }
            }
        }
        $bool1 = $this->getSanQiGuiRen($ydz . $mdz . $ddz);
        if ($bool1) {
            $result['y'][] = '三奇贵人';
            $result['m'][] = '三奇贵人';
            $result['d'][] = '三奇贵人';
        }
        if ($this->getSanQiGuiRen($mdz . $ddz . $hdz)) {
            if (!$bool1) {
                $result['m'][] = '三奇贵人';
                $result['d'][] = '三奇贵人';
            }
            $result['h'][] = '三奇贵人';
        }
        if ($this->getChongTianSha($ygz, $mgz)) {
            $result['y'][] = '冲天杀';
            $result['m'][] = '冲天杀';
        }
        if ($this->getChongTianSha($dgz, $hgz)) {
            $result['d'][] = '冲天杀';
            $result['h'][] = '冲天杀';
        }
        if ($this->getSiFei($mdz, $dgz)) {
            $result['d'][] = '四废';
        }
        if ($this->getYinYanSha($dgz)) {
            $result['d'][] = '阴阳煞';
        }
        if ($this->getTongZiMing($mdz, $ddz)) {
            $result['d'][] = '童子煞';
        }
        if ($this->getTongZiMing($mdz, $hdz)) {
            $result['h'][] = '童子煞';
        }
        return $result;
    }

    /**
     * 根据干支和纪年获得神煞
     * @param array $gz 干支
     * @param array $jiNian 纪年
     * @return array
     */
    public function liuNianToBazi(array $gz, array $jiNian): array
    {
        $gzStr = implode('', $gz);
        $ytg = $jiNian['y'][0];
        $ydz = $jiNian['y'][1];
        $mdz = $jiNian['m'][1];
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $result = [];
        $str = $dtg . $gz[1];
        $ytgToDz = $ytg . $gz[1];
        if ($this->getTianYiGuiRen($dtg, $gz[1]) || $this->getTianYiGuiRen($ytg, $gz[1])) {
            $result[] = '天乙贵人';
        }
        if ($this->getTianGuan($str) || $this->getTianGuan($ytgToDz)) {
            $result[] = '天官贵人';
        }
        if ($this->getTaiJi($str) || $this->getTaiJi($ytgToDz)) {
            $result[] = '太极贵人';
        }
        if ($this->getWenChang($str) || $this->getWenChang($ytgToDz)) {
            $result[] = '文昌贵人';
        }
        if ($this->getGuoYin($str) || $this->getGuoYin($ytgToDz)) {
            $result[] = '国印贵人';
        }
        if ($this->getLuShen($str)) {
            $result[] = '禄神';
        }
        if ($this->getYangRen($str)) {
            $result[] = '羊刃';
        }
        if ($this->getJinYu($str)) {
            $result[] = '金舆';
        }
        if ($this->getFuXing($str) || $this->getFuXing($ytgToDz)) {
            $result[] = '福星贵人';
        }
        if ($this->getTianFu($dtg, $gzStr)) {
            $result[] = '天福贵人';
        }
        if ($this->getTianChu($str) || $this->getTianChu($ytgToDz)) {
            $result[] = '天厨贵人';
        }
        if ($this->getLiuXia($str)) {
            $result[] = '流霞';
        }
        if ($this->getHongYan($str)) {
            $result[] = '红艳';
        }
        if ($this->getCiGuan($dtg, $gzStr)) {
            $result[] = '词馆';
        }
        if ($this->getYuTang($ytgToDz)) {
            $result[] = '玉堂';
        }
        if ($this->getXueTang($str) || $this->getXueTang($ytgToDz)) {
            $result[] = '学堂';
        }
        if ($this->getJiangXing($ddz . $gz[1]) || $this->getJiangXing($ydz . $gz[1])) {
            $result[] = '将星';
        }
        if ($this->getJieSha($ddz . $gz[1]) || $this->getJieSha($ydz . $gz[1])) {
            $result[] = '劫煞';
        }
        if ($this->getYueDe($mdz . $gz[0])) {
            $result[] = '月德贵人';
        }
        if ($this->getYueDeHe($mdz . $gz[0])) {
            $result[] = '月德合';
        }
        if ($this->getTianDe($mdz . $gz[0])) {
            $result[] = '天德贵人';
        }
        if ($this->getTianDeHe($mdz . $gz[0])) {
            $result[] = '天德合';
        }
        if ($this->getYiMa($ydz . $gz[1]) || $this->getYiMa($ddz . $gz[1])) {
            $result[] = '驿马';
        }
        if ($this->getHuaGai($ydz . $gz[1]) || $this->getHuaGai($ddz . $gz[1])) {
            $result[] = '华盖';
        }
        if ($this->getDeGuiRen($mdz . $gz[0]) && $this->getXiuGuiRen($mdz . $gz[0])) {
            $result[] = '德秀贵人';
        }
        if ($this->getHongLuan($ydz . $gz[1])) {
            $result[] = '红鸾';
        }
        if ($this->getTianXi($ydz . $gz[1])) {
            $result[] = '天喜';
        }
        if ($this->getXianChi($ydz . $gz[1])) {
            $result[] = '咸池';
        }
        if ($this->getGuChen($ydz . $gz[1])) {
            $result[] = '孤辰';
        }
        if ($this->getGuaShu($ydz . $gz[1])) {
            $result[] = '寡宿';
        }
        if ($this->getSangMen($ydz . $gz[1]) || $this->getSangMen($ddz . $gz[1])) {
            $result[] = '丧门';
        }
        if ($this->getPiMa($ydz . $gz[1]) || $this->getPiMa($ddz . $gz[1])) {
            $result[] = '披麻';
        }
        if ($this->getDiaoKe($ydz . $gz[1]) || $this->getDiaoKe($ddz . $gz[1])) {
            $result[] = '吊客';
        }
        if ($this->getWangShen($ydz . $gz[1]) || $this->getWangShen($ddz . $gz[1])) {
            $result[] = '亡神';
        }
        if ($this->getZaiSha($ydz . $gz[1])) {
            $result[] = '灾煞';
        }
        return $result;
    }

    /**
     * 天乙贵人(以年干+各柱地支 或日干+各柱地支)
     * @param string $tg 天干
     * @param string $dz 地支
     * @return bool
     */
    public function getTianYiGuiRen(string $tg, string $dz): bool
    {
        $list = [
            '甲' => ['丑', '未'], '戊' => ['丑', '未'], '乙' => ['子', '申'], '己' => ['子', '申'], '丙' => ['亥', '酉'],
            '丁' => ['亥', '酉'], '庚' => ['寅', '午'], '辛' => ['寅', '午'], '壬' => ['卯', '巳'], '癸' => ['卯', '巳'],
        ];
        $dzList = $list[$tg];
        return in_array($dz, $dzList);
    }

    /**
     * 天官贵人(以年干或日干+各柱地支)
     * @param string $str 年干或日干+各柱地支
     * @return bool
     */
    public function getTianGuan(string $str): bool
    {
        $list = ['甲未', '乙辰', '丙巳', '丁酉', '戊戌', '己卯', '庚亥', '辛申', '壬寅', '癸午'];
        return in_array($str, $list);
    }

    /**
     * 太极贵人(以年干或日干+各柱地支)
     * @param string $str 年干或日干+各柱地支
     * @return bool
     */
    public function getTaiJi(string $str): bool
    {
        $list = ['甲子', '乙午', '丙卯', '丁酉', '戊辰', '戊戌', '戊丑', '戊未', '己辰', '己戌', '己丑', '己未', '庚亥', '辛寅', '壬申', '癸巳'];
        return in_array($str, $list);
    }

    /**
     * 文昌贵人(以年干或日干+各八字地支)
     * @param string $str 年干或日干+各八字地支
     * @return bool
     */
    public function getWenChang(string $str): bool
    {
        $list = ['甲巳', '乙午', '丙申', '戊申', '丁酉', '己酉', '庚亥', '辛子', '壬寅', '癸卯'];
        return in_array($str, $list);
    }

    /**
     * 国印贵人(以年干或日干+各八字地支)
     * @param string $str 以年干或日干+各八字地支
     * @return bool
     */
    public function getGuoYin(string $str): bool
    {
        $list = ['甲戌', '乙亥', '丙丑', '丁寅', '戊丑', '己寅', '庚辰', '辛巳', '壬未', '癸申'];
        return in_array($str, $list);
    }

    /**
     * 禄神(日干+各八字日支)
     * @param string $str 日干+各八字日支
     * @return bool
     */
    public function getLuShen(string $str): bool
    {
        $list = ['甲寅', '乙卯', '丙巳', '戊巳', '丁午', '己午', '庚申', '辛酉', '壬亥', '癸子'];
        return in_array($str, $list);
    }

    /**
     * 羊刃(以日干+八字地支)
     * @param string $str 以日干+八字地支
     * @return bool
     */
    public function getYangRen(string $str): bool
    {
        $list = ['甲卯', '乙寅', '丙午', '戊午', '丁巳', '己巳', '庚酉', '辛申', '壬子', '癸亥'];
        return in_array($str, $list);
    }

    /**
     * 截路空亡(日干+时支)
     * @param string $str 日干+时支
     * @return bool
     */
    public function getJieLuKongWang(string $str): bool
    {
        $list = [
            '甲申', '乙午', '丙辰', '丁寅', '戊子', '己申', '庚午', '辛辰', '壬寅', '癸子',
            '甲酉', '乙未', '丙巳', '丁卯', '戊丑', '己酉', '庚未', '辛巳', '壬卯', '癸丑',
        ];
        return in_array($str, $list);
    }

    /**
     * 金舆(以日干+各八字地支)
     * @param string $str 日干+八字地支
     * @return bool
     */
    public function getJinYu(string $str): bool
    {
        $list = ['甲辰', '乙巳', '丙未', '丁申', '戊未', '己申', '庚戌', '辛亥', '壬丑', '癸寅'];
        return in_array($str, $list);
    }

    /**
     * 金神贵人(根据日柱干支或时柱干支)
     * @param string $str 日柱干支或时柱干支
     * @return bool
     */
    public function getJinShen(string $str): bool
    {
        $list = ['乙丑', '己巳', '癸酉'];
        return in_array($str, $list);
    }

    /**
     * 福星贵人(年干或日干+各八字地支)
     * @param string $str 天干+地支
     * @return bool
     */
    public function getFuXing(string $str): bool
    {
        $list = ['甲寅', '乙卯', '丙寅', '丁亥', '戊申', '己未', '庚午', '辛巳', '壬辰', '癸卯', '甲子', '乙丑', '丙子', '癸丑'];
        return in_array($str, $list);
    }

    /**
     * 魁罡贵人(日柱干支)
     * @param string $str 日柱干支
     * @return bool
     */
    public function getKuiGang(string $str): bool
    {
        $list = ['庚辰', '庚戌', '壬辰', '壬戌'];
        return in_array($str, $list);
    }

    /**
     * 天福贵人 (日干+年、月或日柱干支)
     * @param string $rg 日柱
     * @param string $gz 干支
     * @return bool
     */
    public function getTianFu(string $rg, string $gz): bool
    {
        $list = ['甲辛酉', '乙庚申', '丙壬亥', '丁癸子', '戊乙卯', '己甲寅', '庚丁午', '辛丙巳', '壬己午', '癸戊巳'];
        return in_array($rg . $gz, $list);
    }

    /**
     * 天厨贵人(年干或日干+八字地支)
     * @param string $str 天干+地支
     * @return bool
     */
    public function getTianChu(string $str): bool
    {
        $list = [
            '甲巳', '乙午', '丙巳', '丁午', '戊申', '己酉', '庚亥', '辛子', '壬寅', '癸卯',
        ];
        return in_array($str, $list);
    }

    /**
     * 十恶大败(生辰八字四柱的日柱是这其中一个，就是命带十恶大败了)
     * @param string $gz 干支
     * @return bool
     */
    public function getShiErDaBai(string $gz): bool
    {
        $list = ['甲辰', '乙巳', '丙申', '丁亥', '戊戌', '己丑', '庚辰', '辛巳', '壬申', '癸亥'];
        return in_array($gz, $list);
    }

    /**
     * 阴差阳错
     * @param string $rgz 日干支
     * @return bool
     */
    public function getYinChaYangChuo(string $rgz): bool
    {
        $list = ['丙子', '丙午', '丁丑', '丁未', '戊寅', '戊申', '辛卯', '辛酉', '壬辰', '壬戌', '癸巳', '癸亥'];
        return in_array($rgz, $list);
    }

    /**
     * 流霞 (日干+各柱地支)
     * @param string $str 天干+地支
     * @return bool
     */
    public function getLiuXia(string $str): bool
    {
        $list = ['甲酉', '乙戌', '丙未', '丁申', '戊巳', '己午', '庚辰', '辛卯', '壬亥', '癸寅'];
        return in_array($str, $list);
    }

    /**
     * 红艳(出生日干查四支，见之者为是)
     * @param string $str 日干+地支
     * @return bool
     */
    public function getHongYan(string $str): bool
    {
        $list = ['甲午', '乙申', '丙寅', '丁未', '戊辰', '己辰', '庚戌', '辛酉', '壬子', '癸申'];
        return in_array($str, $list);
    }

    /**
     * 孤鸾煞
     * 四柱的日主或时柱上同时出现乙巳、丁巳、辛亥、戊申、壬寅、戊午、壬子、丙午中的两个
     * @param string $rgz 日柱干支
     * @param string $hgz 时柱干支
     * @return bool
     */
    public function getGuLuanSha(string $rgz, string $hgz): bool
    {
        $list = [
            '乙巳', '丁巳', '辛亥', '戊申', '壬寅', '戊午', '壬子', '丙午',
        ];
        return in_array($rgz, $list) && in_array($hgz, $list);
    }

    /**
     * 词馆(以年干或日干+各柱八字干支)
     * @param string $tg 年干或日干
     * @param string $gz 各自干支
     * @return bool
     */
    public function getCiGuan(string $tg, string $gz): bool
    {
        $list = [
            '甲庚寅', '乙辛卯', '丙乙巳', '丁戊午', '戊丁巳', '己庚午', '庚壬申', '辛癸酉', '壬癸亥', '癸壬戌',
        ];
        return in_array($tg . $gz, $list);
    }

    /**
     * 玉堂(以年干查地支)
     * @param string $str 年干+地支
     * @return bool
     */
    public function getYuTang(string $str): bool
    {
        $list = [
            '甲丑', '乙子', '丙亥', '丁酉', '戊未', '己申', '庚未', '辛午', '壬巳', '癸卯',
        ];
        return in_array($str, $list);
    }

    /**
     * 学堂(以年干、日干查八字地支)
     * @param string $str 天干+地支
     * @return bool
     */
    public function getXueTang(string $str): bool
    {
        $list = [
            '甲亥', '乙午', '丙寅', '丁酉', '戊寅', '己酉', '庚巳', '辛子', '壬申', '癸卯',
        ];
        return in_array($str, $list);
    }

    /**
     * 八专
     * @param string $rgz 日干支
     * @return bool
     */
    public function getBaZhuan(string $rgz): bool
    {
        $list = [
            '甲寅', '乙卯', '丁未', '己未', '辛酉', '壬戌', '癸丑',
        ];
        return in_array($rgz, $list);
    }

    /**
     * 九丑(日干支)
     * @param string $rgz 日干+日支
     * @return bool
     */
    public function getJiuChou(string $rgz): bool
    {
        $list = [
            '乙卯', '戊子', '己卯', '辛酉', '壬子', '乙酉', '戊午', '己酉', '辛卯', '壬午',
        ];
        return in_array($rgz, $list);
    }

    /**
     * 节度贵人(以年干查日、时支)
     * @param string $str 年干+日或时支
     * @return bool
     */
    public function getJieDu(string $str): bool
    {
        $list = [
            '甲巳', '乙未', '丙巳', '丁未', '戊巳', '己未', '庚亥', '辛丑', '壬亥', '癸丑',
        ];
        return in_array($str, $list);
    }

    /**
     * 十灵日(以日干支判断)
     * @param string $str 日干+日支
     * @return bool
     */
    public function getShiLing(string $str): bool
    {
        $list = [
            '甲申', '乙酉', '丙子', '丁丑', '戊午', '己丑', '庚寅', '辛卯', '壬午', '癸未',
        ];
        return in_array($str, $list);
    }

    /**
     * 将星(以年、日支查其余各支)
     * @param string $str
     * @return bool
     */
    public function getJiangXing(string $str): bool
    {
        $list = ['子子', '丑酉', '寅午', '卯卯', '辰子', '巳酉', '午午', '未卯', '申子', '酉酉', '戌午', '亥卯'];
        return in_array($str, $list);
    }

    /**
     * 劫煞(以年支或日支为主，其他地支见者为是。)
     * @param string $str 年支+月，日时支或 日支+年，月和时支
     * @return bool
     */
    public function getJieSha(string $str): bool
    {
        $list = [
            '子巳', '丑寅', '寅亥', '卯申', '辰巳', '巳寅', '午亥', '未申', '申巳', '酉寅', '戌亥', '亥申',
        ];
        return in_array($str, $list);
    }

    /**
     * 月德贵人(以月支查八字天干)
     * @param string $str 月支+天干
     * @return bool
     */
    public function getYueDe(string $str): bool
    {
        $list = [
            '子壬', '丑庚', '寅丙', '卯甲', '辰壬', '巳庚', '午丙', '未甲', '申壬', '酉庚', '戌丙', '亥甲',
        ];
        return in_array($str, $list);
    }

    /**
     * 月德合（以月支查八字天干）
     * @param string $str 月支+各天干
     * @return bool
     */
    public function getYueDeHe(string $str): bool
    {
        $list = [
            '子丁', '丑乙', '寅辛', '卯己', '辰丁', '巳乙', '午辛', '未己', '申丁', '酉乙', '戌辛', '亥己',
        ];
        return in_array($str, $list);
    }

    /**
     * 天德贵人(以月支查八字天干)
     * @param string $str 月支+各天干
     * @return bool
     */
    public function getTianDe(string $str): bool
    {
        $list = [
            '子己', '丑庚', '寅丁', '卯申', '辰壬', '巳辛', '午亥', '未甲', '申癸', '酉寅', '戌丙', '亥乙',
        ];
        return in_array($str, $list);
    }

    /**
     * 天德合(以月支查八字天干)
     * @param string $str 月支+各天干
     * @return bool
     */
    public function getTianDeHe(string $str): bool
    {
        $list = [
            '子申', '丑乙', '寅壬', '卯巳', '辰丁', '巳丙', '午寅', '未己', '申戊', '酉亥', '戌辛', '亥庚',
        ];
        return in_array($str, $list);
    }

    /**
     * 驿马(以年支或日支查八字地支)
     * @param string $str 年支+各地支或日支+各地支
     * @return bool
     */
    public function getYiMa(string $str): bool
    {
        $list = [
            '子寅', '丑亥', '寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳',
        ];
        return in_array($str, $list);
    }

    /**
     * 天赦(以月支查日柱)
     * @param string $mdz 月支
     * @param string $rgz 日干支
     * @return bool
     */
    public function getTianShe(string $mdz, string $rgz): bool
    {
        $list = [
            '子甲子', '丑甲子', '寅戊寅', '卯戊寅', '辰戊寅', '巳甲午', '午甲午', '未甲午', '申戊申', '酉戊申', '戌戊申', '亥甲子',
        ];
        return in_array($mdz . $rgz, $list);
    }

    /**
     * 华盖（以年支或日支查八字地支）
     * @param string $str
     * @return bool
     */
    public function getHuaGai(string $str): bool
    {
        $list = [
            '子辰', '丑丑', '寅戌', '卯未', '辰辰', '巳丑', '午戌', '未未', '申辰', '酉丑', '戌戌', '亥未',
        ];
        return in_array($str, $list);
    }

    /**
     * 德贵人(以生月为主，看四柱天干)
     * @param string $str
     * @return bool
     */
    public function getDeGuiRen(string $str): bool
    {
        $list = [
            '子壬', '子癸', '子戊', '子己', '寅丙', '寅丁', '辰壬', '辰癸', '辰戊', '辰己', '午丙', '午丁', '申壬', '申癸', '申戊', '申己', '戌丙', '戌丁',
        ];
        return in_array($str, $list);
    }

    /**
     * 秀贵人(以生月为主，看四柱天干)
     * @param string $str 月支+各柱天干
     * @return bool
     */
    public function getXiuGuiRen(string $str): bool
    {
        $list = [
            '子丙', '子辛', '子甲', '子己', '辰丙', '辰辛', '辰甲', '辰己', '申丙', '申辛', '申甲', '申己', '寅戊', '寅癸', '午戊', '午癸', '戌戊', '戌癸',
        ];
        return in_array($str, $list);
    }

    /**
     * 红鸾（以年支查八字地支）
     * @param string $str 年支+其它柱地支
     * @return bool
     */
    public function getHongLuan(string $str): bool
    {
        $list = [
            '子卯', '丑寅', '寅丑', '卯子', '辰亥', '巳戌', '午酉', '未申', '申未', '酉午', '戌巳', '亥辰',
        ];
        return in_array($str, $list);
    }

    /**
     * 天喜(以年支查八字地支)
     * @param string $str 年支+其它柱地支
     * @return bool
     */
    public function getTianXi(string $str): bool
    {
        $list = [
            '子酉', '丑申', '寅未', '卯午', '辰巳', '巳辰', '午卯', '未寅', '申丑', '酉子', '戌亥', '亥戌',
        ];
        return in_array($str, $list);
    }

    /**
     * 咸池桃花（以年支查八字地支）
     * @param string $str 年支+其它地支
     * @return bool
     */
    public function getXianChi(string $str): bool
    {
        $list = [
            '子酉', '丑午', '寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子',
        ];
        return in_array($str, $list);
    }

    /**
     * 孤辰（以年支查八字地支）
     * @param string $str 年支+其它地支
     * @return bool
     */
    public function getGuChen(string $str): bool
    {
        $list = [
            '子寅', '丑寅', '寅巳', '卯巳', '辰巳', '巳申', '午申', '未申', '申亥', '酉亥', '戌亥', '亥寅',
        ];
        return in_array($str, $list);
    }

    /**
     * 寡宿(以年支查八字地支)
     * @param string $str 年支+各柱地支
     * @return bool
     */
    public function getGuaShu(string $str): bool
    {
        $list = [
            '子戌', '丑戌', '寅丑', '卯丑', '辰丑', '巳辰', '午辰', '未辰', '申未', '酉未', '戌未', '亥戌',
        ];
        return in_array($str, $list);
    }

    /**
     * 丧门(以年或日支查其余地支)
     * @param string $str 年支或日支+其它地支
     * @return bool
     */
    public function getSangMen(string $str): bool
    {
        $list = [
            '子寅', '丑卯', '寅辰', '卯巳', '辰午', '巳未', '午申', '未巳', '申戌', '酉亥', '戌子', '亥丑',
        ];
        return in_array($str, $list);
    }

    /**
     * 吊客(以年、日支查八字地支)
     * @param string $str 年支或日支+其它各柱地支
     * @return bool
     */
    public function getDiaoKe(string $str): bool
    {
        $list = [
            '子戌', '丑亥', '寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 亡神(以出生年支或日支查四支，见之者为是)
     * @param string $str 年支或日支+其它柱地支
     * @return bool
     */
    public function getWangShen(string $str): bool
    {
        $list = [
            '子亥', '丑申', '寅巳', '卯寅', '辰亥', '巳申', '午巳', '未寅', '申亥', '酉申', '戌巳', '亥寅',
        ];
        return in_array($str, $list);
    }

    /**
     * 灾煞(以年支查其余八字地支)
     * @param string $str 年支+ 其它柱地支
     * @return bool
     */
    public function getZaiSha(string $str): bool
    {
        $list = [
            '子午', '丑卯', '寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 埋儿煞(以年支查时支)
     * @param string $str 年支+时支
     * @return bool
     */
    public function getMaierSha(string $str): bool
    {
        $list = [
            '子丑', '丑卯', '寅申', '卯丑', '辰卯', '巳申', '午丑', '未卯', '申申', '酉丑', '戌卯', '亥申',
        ];
        return in_array($str, $list);
    }

    /**
     * 天医(月支查其余地支)
     * @param string $str
     * @return bool
     */
    public function getTianYi(string $str): bool
    {
        $list = ['子亥', '丑子', '寅丑', '卯寅', '辰卯', '巳辰', '午巳', '未午', '申未', '酉申', '戌酉', '亥戌'];
        return in_array($str, $list);
    }

    /**
     * 天罗(凡是八字中带有戌亥两个字，就算是犯了天罗)
     * @param array $arr
     * @return bool
     */
    public function getTianLuo(array $arr): bool
    {
        return in_array('戌', $arr) && in_array('亥', $arr);
    }

    /**
     * 地网(凡是八字中有辰巳两个字，就算是犯了地网)
     * @param array $arr
     * @return bool
     */
    public function getDiWang(array $arr): bool
    {
        return in_array('辰', $arr) && in_array('巳', $arr);
    }

    /**
     * 六厄(年支看其他地支)
     * @param string $str 年支+其它柱地支
     * @return bool
     */
    public function getLiuE(string $str): bool
    {
        $list = ['子卯', '丑子', '寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午'];
        return in_array($str, $list);
    }

    /**
     * 根据天干或地支获得 阴阳
     * @param string $str 天干或地支
     * @return string
     */
    public function getYinYang(string $str): string
    {
        $list = [
            '甲', '丙', '戊', '庚', '壬', '子', '寅', '辰', '午', '申', '戌',
        ];
        return in_array($str, $list) ? '阳' : '阴';
    }

    /**
     * 元辰
     * 阳男阴女，其元辰是：子年见未，丑年见申，寅年见酉，卯年见戌，辰年见亥，巳年见子，午年见丑，未年见寅，申年见卯，酉年见辰，戌年见巳，亥年见午。
     * 阴男阳女，其元辰是：子年见巳，丑年见午，寅年见未，卯年见申，辰年见酉，巳年见戌，午年见亥，未年见子，申年见丑，酉年见寅，戌年见卯，亥年见辰。
     * @param string $str 阳男,阴女,阴男,阳女
     * @param string $str2 年地支+其它支
     * @return bool
     */
    public function getYuanChen(string $str, string $str2): bool
    {
        if (in_array($str, ['阳男', '阴女'])) {
            $list = ['子未', '丑申', '寅酉', '卯戌', '辰亥', '巳子', '午丑', '未寅', '申卯', '酉辰', '戌巳', '亥午'];
        } else {
            // 阴男阳女
            $list = ['子巳', '丑午', '寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌午', '亥辰'];
        }
        return in_array($str2, $list);
    }

    /**
     * 三奇贵人(天上三奇甲戊庚；地上三奇癸壬辛；人中三奇乙丙丁)
     * @param string $str 年月日干或月日时干
     * @return bool
     */
    public function getSanQiGuiRen(string $str): bool
    {
        $list = ['甲戊庚', '癸壬辛', '乙丙丁'];
        return in_array($str, $list);
    }

    /**
     * 勾煞(以年支为主查其他的地支)
     * @param string $str 阳男,阴女,阴男,阳女
     * @param string $str2 年支+其它柱地支
     * @return bool
     */
    public function getGouSha(string $str, string $str2): bool
    {
        if (in_array($str, ['阳男', '阴女'])) {
            $list = ['子卯', '丑辰', '寅巳', '卯午', '辰未', '巳申', '午酉', '未戌', '申亥', '酉子', '戌丑', '亥寅'];
        } else {
            // 阴男阳女
            $list = ['子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申'];
        }
        return in_array($str2, $list);
    }

    /**
     * 绞煞(以年支为主查其他的地支)
     * @param string $str 阳男,阴女,阴男,阳女
     * @param string $str2 年支+其它地支
     * @return bool
     */
    public function getJiaoSha(string $str, string $str2): bool
    {
        if (in_array($str, ['阳男', '阴女'])) {
            $list = ['子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申'];
        } else {
            // 阴男阳女
            $list = ['子卯', '丑辰', '寅巳', '卯午', '辰未', '巳申', '午酉', '未戌', '申亥', '酉子', '戌丑', '亥寅'];
        }
        return in_array($str2, $list);
    }

    /**
     * 冲天杀(年干支与月干支相同，日干支与时干支相同)
     * @param string $str 年干支或日干支
     * @param string $str2 月干支或时干支
     * @return bool
     */
    public function getChongTianSha(string $str, string $str2)
    {
        return $str === $str2;
    }

    /**
     * 四废
     * 凡春天出生，八字日柱为庚申或辛酉;夏天出生，八字日柱为壬子或癸亥;秋天出生，八字日柱为甲寅或乙卯;冬天出生，八字日柱为丙午或丁巳，均视为命带“四废”神煞。
     * @param string $mdz 月地支
     * @param string $rgz 日干支
     * @return bool
     */
    public function getSiFei(string $mdz, string $rgz): bool
    {
        switch ($mdz) {
            case '子':
            case '丑':
            case '亥':
                $list = ['丙午', '丁巳'];
                break;
            case '寅':
            case '卯':
            case '辰':
                $list = ['庚申', '辛酉'];
                break;
            case '巳':
            case '午':
            case '未':
                $list = ['壬子', '癸亥'];
                break;
            default:
                $list = ['甲寅', '乙卯'];
                break;
        }
        return in_array($rgz, $list);
    }

    /**
     * 阴阳煞
     * @param string $rgz 日干支
     * @return bool
     */
    public function getYinYanSha(string $rgz): bool
    {
        $list = ['丙子', '戊午'];
        return in_array($rgz, $list);
    }

    /**
     * 披麻(以年支或日支查询其余地支)
     * @param string $str 年支或日支+其它支
     * @return bool
     */
    public function getPiMa(string $str): bool
    {
        $list = ['子酉', '丑戌', '寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申'];
        return in_array($str, $list);
    }

    /**
     * 判断是否童子命
     * @param string $mdz 月地支
     * @param string $dz 日地支或时地支
     * @return bool
     */
    public function getTongZiMing(string $mdz, string $dz): bool
    {
        $list = [
            '庚' => ['寅', '子'], '卯' => ['寅', '子'], '辰' => ['寅', '子'],
            '申' => ['寅', '子'], '酉' => ['寅', '子'], '戌' => ['寅', '子'],
            '巳' => ['卯', '未', '辰'], '午' => ['卯', '未', '辰'], '未' => ['卯', '未', '辰'],
            '亥' => ['卯', '未', '辰'], '子' => ['卯', '未', '辰'], '丑' => ['卯', '未', '辰'],
        ];
        if (!isset($list[$mdz])) {
            return false;
        }
        $list1 = $list[$mdz];
        return in_array($dz, $list1);
    }
}
