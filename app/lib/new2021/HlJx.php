<?php
// +----------------------------------------------------------------------
// | HlJx 黄历吉凶
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class HlJx
{
    // 出处   ao 鳌头通书  ze 择吉秘要  xie 协纪辩方  xiang 象吉通书  yu 玉匣记

    /**
     * 根据月支+日支(干)得吉神
     * @var array|array[]
     */
    protected array $listJi1 = [
        '天德' => ['寅丁', '卯申', '辰壬', '巳辛', '午亥', '未甲', '申癸', '酉寅', '戌丙', '亥乙', '子巳', '丑庚'], // ao ze
        '天德合' => ['寅壬', '卯巳', '辰丁', '巳丙', '午寅', '未己', '申戊', '酉亥', '戌辛', '亥庚', '子申', '丑乙'], // ao ze
        '月德' => ['寅丙', '卯甲', '辰壬', '巳庚', '午丙', '未甲', '申壬', '酉庚', '戌丙', '亥甲', '子壬', '丑庚'], // ao ze xie
        '月德合' => ['寅辛', '卯己', '辰丁', '巳乙', '午辛', '未己', '申丁', '酉乙', '戌辛', '亥己', '子丁', '丑乙'], // ao ze xie
        '月恩' => ['寅丙', '卯丁', '辰庚', '巳己', '午戊', '未辛', '申壬', '酉癸', '戌庚', '亥乙', '子甲', '丑辛'], // ao ze xie
        '月空' => ['寅壬', '卯庚', '辰丙', '巳甲', '午壬', '未庚', '申丙', '酉甲', '戌壬', '亥庚', '子丙', '丑甲'], // ao ze xie
        '月财' => ['寅午', '卯乙', '辰巳', '巳未', '午酉', '未亥', '申午', '酉乙', '戌巳', '亥未', '子酉', '丑亥'], // ao ze
        '要安' => ['寅寅', '卯申', '辰卯', '巳酉', '午辰', '未戌', '申巳', '酉亥', '戌午', '亥子', '子未', '丑丑'], // ao ze xie
        '金堂' => ['寅辰', '卯戌', '辰巳', '巳亥', '午午', '未子', '申未', '酉丑', '戌申', '亥寅', '子酉', '丑卯'], // ao ze xie
        '圣心' => ['寅亥', '卯巳', '辰子', '巳午', '午丑', '未未', '申寅', '酉申', '戌卯', '亥酉', '子辰', '丑戌'], // ao ze xie
        '益后' => ['寅子', '卯午', '辰丑', '巳未', '午寅', '未申', '申卯', '酉酉', '戌辰', '亥戌', '子巳', '丑亥'], // ao ze xie
        '续世' => ['寅丑', '卯未', '辰寅', '巳申', '午卯', '未酉', '申辰', '酉戌', '戌巳', '亥亥', '子午', '丑子'], // ao ze xie
        '福生' => ['寅酉', '卯卯', '辰戌', '巳辰', '午亥', '未巳', '申子', '酉午', '戌丑', '亥未', '子寅', '丑申'], // ao ze xie
        '普护' => ['寅申', '卯寅', '辰酉', '巳卯', '午戌', '未辰', '申亥', '酉巳', '戌子', '亥午', '子丑', '丑未'], // ao ze xie
        '敬心' => ['寅未', '卯丑', '辰申', '巳寅', '午酉', '未卯', '申戌', '酉辰', '戌亥', '亥巳', '子子', '丑午'], // ao ze
        '敬安' => ['寅未', '卯丑', '辰申', '巳寅', '午酉', '未卯', '申戌', '酉辰', '戌亥', '亥巳', '子子', '丑午'], // ao ze
        '玉宇' => ['寅卯', '卯酉', '辰辰', '巳戌', '午巳', '未亥', '申午', '酉子', '戌未', '亥丑', '子申', '丑寅'], // ze xie
        '六合' => ['寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子'], // ao ze xie
        '三合' => [
            '寅午', '寅戌', '卯未', '卯亥', '辰子', '辰申', '巳丑', '巳酉', '午寅', '午戌', '未卯', '未亥',
            '申子', '申辰', '酉丑', '酉巳', '戌寅', '戌午', '亥卯', '亥未', '子辰', '子申', '丑巳', '丑酉',
        ], // ao ze xie
        '五富' => ['寅亥', '卯寅', '辰巳', '巳申', '午亥', '未寅', '申巳', '酉申', '戌亥', '亥寅', '子巳', '丑申'], // ao ze xie
        '天仓' => ['寅寅', '卯丑', '辰子', '巳亥', '午戌', '未酉', '申申', '酉未', '戌午', '亥巳', '子辰', '丑卯'], // ao ze xie
        '天富' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'], // ao ze
        '吉庆' => ['寅酉', '卯寅', '辰亥', '巳辰', '午丑', '未午', '申卯', '酉申', '戌巳', '亥戌', '子未', '丑子'], // ze
        '幽微' => ['寅亥', '卯辰', '辰丑', '巳午', '午卯', '未申', '申巳', '酉戌', '戌未', '亥子', '子酉', '丑寅'], // xie
        '满德' => ['寅寅', '卯未', '辰辰', '巳酉', '午午', '未亥', '申申', '酉丑', '戌戌', '亥卯', '子子', '丑巳'], // ze
        '活曜' => ['寅巳', '卯戌', '辰未', '巳子', '午酉', '未寅', '申亥', '酉辰', '戌丑', '亥午', '子卯', '丑申'], // ze xiang
        '解神' => ['寅申', '卯申', '辰戌', '巳戌', '午子', '未子', '申寅', '酉寅', '戌辰', '亥辰', '子午', '丑午'], // ao ze xie xiang
        '驿马' => ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'], // ao ze xie
        '天后' => ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'], // xie
        '阳德' => ['寅戌', '卯子', '辰寅', '巳辰', '午午', '未申', '申戌', '酉子', '戌寅', '亥辰', '子午', '丑申'], // ao ze xie xiang
        '阴德' => ['寅酉', '卯未', '辰巳', '巳卯', '午丑', '未亥', '申酉', '酉未', '戌巳', '亥卯', '子丑', '丑亥'], // ao ze xie xiang
        '王日' => ['寅寅', '卯寅', '辰寅', '巳巳', '午巳', '未巳', '申申', '酉申', '戌申', '亥亥', '子亥', '丑亥'], // xie
        '官日' => ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子'], // ao ze xie
        '守日' => ['寅辰', '卯辰', '辰辰', '巳未', '午未', '未未', '申戌', '酉戌', '戌戌', '亥丑', '子丑', '丑丑'], // xie
        '相日' => ['寅巳', '卯巳', '辰巳', '巳申', '午申', '未申', '申亥', '酉亥', '戌亥', '亥寅', '子寅', '丑寅'], // ao ze xie
        '民日' => ['寅午', '卯午', '辰午', '巳酉', '午酉', '未酉', '申子', '酉子', '戌子', '亥卯', '子卯', '丑卯'], // ao ze xie
        '天马' => ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'], // ao xie ze
        '天财' => ['寅辰', '卯午', '辰申', '巳戌', '午子', '未寅', '申辰', '酉午', '戌申', '亥戌', '子子', '丑寅'], // ao ze yu
        '时德' => ['寅午', '卯午', '辰午', '巳辰', '午辰', '未辰', '申子', '酉子', '戌子', '亥寅', '子寅', '丑寅'], // ao ze xie xiang
        '临日' => ['寅午', '卯亥', '辰申', '巳丑', '午戌', '未卯', '申子', '酉巳', '戌寅', '亥未', '子辰', '丑酉'], // ze xie
        '天官' => ['寅戌', '卯子', '辰寅', '巳辰', '午午', '未申', '申戌', '酉子', '戌寅', '亥辰', '子午', '丑申'], // yu
        '吉期' => ['寅卯', '卯辰', '辰巳', '巳午', '午未', '未申', '申酉', '酉戌', '戌亥', '亥子', '子丑', '丑寅'], // ao ze xie
        '兵宝' => ['寅卯', '卯辰', '辰巳', '巳午', '午未', '未申', '申酉', '酉戌', '戌亥', '亥子', '子丑', '丑寅'], // xie
        '天巫' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'], // xie
        '福德' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'], // xie
        '支德' => ['寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午'], // xie
        '天医' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'], // xie
        '生气' => ['寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉', '子戌', '丑亥'], // xie
        '时阳' => ['寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉', '子戌', '丑亥'], // xie
        '六仪' => ['寅辰', '卯卯', '辰寅', '巳丑', '午子', '未亥', '申戌', '酉酉', '戌申', '亥未', '子午', '丑巳'], // xie
        '天狗守塘' => ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子'], // xie
        '神后' => ['寅卯', '卯申', '辰巳', '巳戌', '午未', '未子', '申酉', '酉寅', '戌亥', '亥辰', '子丑', '丑午'], // xie
        '元嘉' => ['寅申', '卯丑', '辰戌', '巳卯', '午子', '未巳', '申寅', '酉未', '戌辰', '亥酉', '子午', '丑亥'], // xie
        '天寿星' => ['寅丑', '卯卯', '辰巳', '巳未', '午酉', '未丑', '申亥', '酉卯', '戌巳', '亥未', '子酉', '丑亥'], // xie
        '天嗣星' => ['寅未', '卯酉', '辰亥', '巳丑', '午卯', '未巳', '申未', '酉酉', '戌亥', '亥丑', '子卯', '丑巳'], // xie
        '雷公' => ['寅子', '卯寅', '辰辰', '巳午', '午申', '未戌', '申子', '酉寅', '戌辰', '亥午', '子申', '丑戌'], // xie
        '地财' => ['寅巳', '卯未', '辰酉', '巳亥', '午丑', '未卯', '申巳', '酉未', '戌酉', '亥亥', '子丑', '丑卯'], // xie
        '宝光' => ['寅巳', '卯未', '辰酉', '巳亥', '午丑', '未卯', '申巳', '酉未', '戌酉', '亥亥', '子丑', '丑卯'], // xie
        '丰旺' => ['寅寅', '卯寅', '辰寅', '巳巳', '午巳', '未巳', '申申', '酉申', '戌申', '亥亥', '子亥', '丑亥'], // xie
        '恩胜' => ['寅巳', '卯巳', '辰巳', '巳申', '午申', '未申', '申亥', '酉亥', '戌亥', '亥寅', '子寅', '丑寅'], // xie
        '成勋' => ['寅午', '卯午', '辰午', '巳酉', '午酉', '未酉', '申子', '酉子', '戌子', '亥卯', '子卯', '丑卯'], // xie
        '执储' => ['寅丑', '卯卯', '辰巳', '巳未', '午酉', '未丑', '申亥', '酉卯', '戌巳', '亥未', '子酉', '丑亥'], // xie
        '天宝' => ['寅辰', '卯午', '辰申', '巳戌', '午子', '未寅', '申辰', '酉午', '戌申', '亥戌', '子子', '丑寅'], // xie
        '天玉' => ['寅未', '卯酉', '辰亥', '巳丑', '午卯', '未巳', '申未', '酉酉', '戌亥', '亥丑', '子卯', '丑巳'], // xie
        '天成' => ['寅未', '卯酉', '辰亥', '巳丑', '午卯', '未巳', '申未', '酉酉', '戌亥', '亥丑', '子卯', '丑巳'],
        // '阳时方' => ['寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉位', '戌申', '亥酉', '子戌', '丑亥'],
        '福厚' => ['寅寅', '卯寅', '辰寅', '巳巳', '午巳', '未巳', '申申', '酉申', '戌申', '亥亥', '子亥', '丑亥'],
        '天喜' => [
            '寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉',
            '卯戌', '辰戌', '午丑', '未丑', '酉辰', '戌辰', '子辰', '丑辰',
        ], // xie
        '兵福' => ['寅寅', '卯卯', '辰辰', '巳巳', '午午', '未未', '申申', '酉酉', '戌戌', '亥亥', '子子', '丑丑'], // xie
        '天对' => ['寅巳', '卯未', '辰酉', '巳亥', '午丑', '未卯', '申巳', '酉未', '戌酉', '亥亥', '子丑', '丑卯'],
        '禄库' => ['寅戌', '卯子', '辰寅', '巳辰', '午午', '未申', '申戌', '酉子', '戌寅', '亥辰', '子午', '丑申'],
        '天库' => ['寅未', '卯酉', '辰亥', '巳丑', '午卯', '未巳', '申未', '酉酉', '戌亥', '亥丑', '子卯', '丑巳'],
        '天地解' => ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未 '],
        '喝散神' => ['寅巳', '卯巳', '辰巳', '巳申', '午申', '未申', '申亥', '酉亥', '戌亥', '亥寅', '子寅', '丑寅'],
        '人仓' => ['寅未', '卯辰', '辰丑', '巳戌', '午未', '未辰', '申丑', '酉戌', '戌未', '亥辰', '子丑', '丑戌'],
        '太阳' => ['寅酉', '卯子', '辰卯', '巳午', '午酉', '未子', '申卯', '酉午', '戌酉', '亥子', '子卯', '丑午'],
        '未马日' => ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'],
        '时阴' => ['寅午', '卯未', '辰申', '巳酉', '午戌', '未亥', '申子', '酉丑', '戌寅', '亥卯', '子辰', '丑巳'],
    ];

    /**
     * 吉神2
     * @var array
     */
    protected array $listJi2 = [
        // 日柱干支
        'dgz' => [
            '天瑞' => ['戊寅', '己卯', '庚寅', '辛巳', '壬子'], // ao ze xiang
            '天恩' => ['甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己卯', '庚辰', '辛巳', '壬午', '癸未', '己酉', '庚戌', '辛亥', '壬子', '癸丑'], // ao ze xie xiang
            '宝日' => ['丁未', '丁丑', '丙戌', '甲午', '庚子', '壬寅', '癸卯', '乙巳', '戊申', '己酉', '辛亥', '丙辰'], // xie
            '义日' => ['甲子', '丙寅', '丁卯', '己巳', '辛未', '壬申', '癸酉', '乙亥', '庚辰', '辛丑', '庚戌', '戊午'], // xie
            '制日' => ['乙丑', '甲戌', '壬午', '戊子', '庚寅', '辛卯', '癸巳', '乙未', '丙申', '丁酉', '己亥', '甲辰'], // xie ze
            '专日' => ['戊辰', '己丑', '戊戌', '丙午', '壬子', '甲寅', '乙卯', '丁巳', '己未', '庚申', '辛酉', '癸亥'], // xie ze
            '伐日' => ['庚午', '丙子', '戊寅', '己卯', '辛巳', '癸未', '甲申', '乙酉', '丁亥', '壬辰', '癸丑', '壬戌'], // xie
            '鸣吠' => ['甲午', '丙午', '庚午', '壬午', '甲申', '丙申', '庚申', '壬申', '乙酉', '丁酉', '己酉', '辛酉', '癸酉'], // ao
            '鸣吠对' => ['丙子', '庚子', '壬子', '甲寅', '丙寅', '庚寅', '壬寅', '乙卯', '丁卯', '辛卯', '癸卯'], // xie
            '天福' => ['己卯', '辛巳', '庚寅', '辛卯', '壬辰', '癸巳', '己亥', '庚子', '辛丑', '乙巳', '丁巳', '庚申'], // ao
            '上吉' => ['辛未', '壬申', '癸酉', '己卯', '壬午', '甲申', '壬寅', '甲辰', '丙午', '己酉', '庚戌', '丙辰', '己未', '庚申', '辛酉'], // xie
            '七圣' => [
                '丙寅', '丁卯', '戊辰', '己巳', '壬申', '癸酉', '甲戌', '乙亥', '丙子', '丁丑', '庚辰', '辛巳', '甲申', '乙酉', '戊子', '己丑', '庚寅',
                '辛卯', '甲午', '乙未', '戊戌', '己亥', '壬寅', '癸卯', '甲辰', '乙巳', '戊申', '己酉', '庚戌', '壬子', '癸丑', '甲寅', '乙卯', '戊午',
                '己未', '庚申', '辛酉',
            ], // xie
            '神在' => [
                '甲子', '乙丑', '丁卯', '戊辰', '辛未', '壬申', '癸酉', '甲戌', '丁丑', '己卯', '丙辰', '丁巳', '丙午', '丁未', '戊申', '乙酉', '庚戌',
                '乙卯', '丙辰', '丁巳', '戊午', '己未', '辛酉', '癸亥',
            ], // xie
            '又吉' => ['甲子', '庚午', '癸酉', '庚辰', '乙酉', '甲午', '乙巳', '甲申', '壬午', '乙未', '癸卯', '丙午', '丁未', '戊申', '甲寅', '戊午'],
            '蚕吉' => ['甲子', '丁卯', '庚午', '壬午', '戊午'],
            '天明' => ['辛未', '壬申', '癸酉', '己卯', '壬申', '甲申', '壬寅', '甲辰', '丙午', '己酉', '庚戌', '丙辰', '己未', '庚申', '辛酉'],
            '大明吉日' => ['辛未', '壬申', '癸酉', '丁丑', '己卯', '壬午', '甲申', '丁亥', '壬辰', '乙未', '壬寅', '甲辰', '乙巳', '丙午', '己酉', '庚戌', '辛亥', '丙辰', '己未', '庚申', '辛酉'],
            '五帝生' => ['甲子', '甲辰', '戊子', '壬子'],
            '地虎不食' => ['壬申', '癸酉', '壬午', '甲申', '乙酉', '壬辰', '丁酉', '甲辰', '丙午', '己酉', '丙辰', '己未', '庚申', '辛酉'],
            '天聋地哑' => ['丙寅', '戊辰', '丙子', '丙申', '庚子', '壬子', '丙辰', '为天聋日', '乙丑', '丁卯', '己卯', '辛巳', '乙未', '丁酉', '己亥', '辛丑', '辛亥', '癸丑', '辛丑'],
        ],
        // 月支+日干支
        'mdz_dgz' => [
            '天赦' => ['寅戊寅', '卯戊寅', '辰戊寅', '巳甲午', '午甲午', '未甲午', '申戊申', '酉戊申', '戌戊申', '亥甲子', '子甲子', '丑甲子'], // ao ze xie xiang
            '天愿' => ['寅乙亥', '卯甲戌', '辰乙酉', '巳丙申', '午丁未', '未戊午', '申己巳', '酉庚辰', '戌辛卯', '亥壬寅', '子癸丑', '丑甲子'], // xie
        ],
        // 日支
        'ddz' => [
            '除神' => ['申', '酉'], // xie
            '五合' => ['寅', '卯'], // xie
        ],
    ];

    /**
     * 月支+日支(干) 凶神
     * @var array
     */
    protected array $listXiong1 = [
        '土府' => ['寅寅', '卯卯', '辰辰', '巳巳', '午午', '未未', '申申', '酉酉', '戌戌', '亥亥', '子子', '丑丑'], // xie
        '小时' => ['寅寅', '卯卯', '辰辰', '巳巳', '午午', '未未', '申申', '酉酉', '戌戌', '亥亥', '子子', '丑丑'], // xie
        '天狗' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'], // xie
        '死神' => ['寅巳', '卯午', '辰未', '巳申', '午酉', '未戌', '申亥', '酉子', '戌丑', '亥寅', '子卯', '丑辰'], // xie
        '死气' => ['寅午', '卯未', '辰申', '巳酉', '午戌', '未亥', '申子', '酉丑', '戌寅', '亥卯', '子辰', '丑巳'], // xie
        '官符' => ['寅午', '卯未', '辰申', '巳酉', '午戌', '未亥', '申子', '酉丑', '戌寅', '亥卯', '子辰', '丑巳'], // xie
        '小耗' => ['寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午'], // xie
        '大耗' => ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'], // xie
        '血支' => ['寅丑', '卯寅', '辰卯', '巳辰', '午巳', '未午', '申未', '酉申', '戌酉', '亥戌', '子亥', '丑子'], // xie
        '天罡' => ['寅巳', '卯子', '辰未', '巳寅', '午酉', '未辰', '申亥', '酉午', '戌丑', '亥申', '子卯', '丑戌'], // xie ao
        '河魁' => ['寅亥', '卯午', '辰丑', '巳申', '午卯', '未戌', '申巳', '酉子', '戌未', '亥寅', '子酉', '丑辰'], // xie ao
        '月建' => ['寅寅', '卯卯', '辰辰', '巳巳', '午午', '未未', '申申', '酉酉', '戌戌', '亥亥', '子子', '丑丑'], // xie
        '月厌' => ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'], // xie ao
        '地火' => ['寅戌', '卯酉', '辰申', '巳未', '午午', '未巳', '申辰', '酉卯', '戌寅', '亥丑', '子子', '丑亥'], // xie
        '厌对' => ['寅辰', '卯卯', '辰寅', '巳丑', '午子', '未亥', '申戌', '酉酉', '戌申', '亥未', '子午', '丑巳'], // xie
        '四击' => ['寅戌', '卯戌', '辰戌', '巳丑', '午丑', '未丑', '申辰', '酉辰', '戌辰', '亥未', '子未', '丑未'], // xie
        '九空' => ['寅辰', '卯丑', '辰戌', '巳未', '午辰', '未丑', '申戌', '酉未', '戌辰', '亥丑', '子戌', '丑未'], // xie
        '九坎' => ['寅辰', '卯丑', '辰戌', '巳未', '午卯', '未子', '申酉', '酉午', '戌寅', '亥亥', '子申', '丑巳'], // xie
        '九焦' => ['寅辰', '卯丑', '辰戌', '巳未', '午卯', '未子', '申酉', '酉午', '戌寅', '亥亥', '子申', '丑巳'], // xie
        '复日' => ['寅甲', '卯乙', '辰戊', '巳丙', '午丁', '未己', '申庚', '酉辛', '戌戊', '亥壬', '子癸', '丑己'], // xie
        '劫煞' => ['寅亥', '卯申', '辰巳', '巳寅', '午亥', '未申', '申巳', '酉寅', '戌亥', '亥申', '子巳', '丑寅'], // xie
        '灾煞' => ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'], // xie
        '天狱' => ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'], // xie
        '天火' => ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'], // xie
        '月煞' => ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'], // xie
        '月虚' => ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'], // xie
        '月刑' => ['寅巳', '卯子', '辰辰', '巳申', '午午', '未丑', '申寅', '酉酉', '戌未', '亥亥', '子卯', '丑戌'], // xie
        '月害' => ['寅巳', '卯辰', '辰卯', '巳寅', '午丑', '未子', '申亥', '酉戌', '戌酉', '亥申', '子未', '丑午'], // xie
        '大时' => ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'], // xie
        '大败' => ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'], // xie
        '咸池' => ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'], // xie ai
        '游祸' => ['寅巳', '卯寅', '辰亥', '巳申', '午巳', '未寅', '申亥', '酉申', '戌巳', '亥寅', '子亥', '丑申'], // xie
        '天吏' => ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'], // xie
        '致死' => ['寅酉', '卯午', '辰卯', '巳子', '午酉', '未午', '申卯', '酉子', '戌酉', '亥午', '子卯', '丑子'], // xie
        '无翘' => ['寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅', '子丑', '丑子'], // xie
        '天贼' => ['寅丑', '卯子', '辰亥', '巳戌', '午酉', '未申', '申未', '酉午', '戌巳', '亥辰', '子卯', '丑寅'], // xie
        '兵禁' => ['寅寅', '卯子', '辰戌', '巳申', '午午', '未辰', '申寅', '酉子', '戌戌', '亥申', '子午', '丑辰'], // xie
        '土符' => ['寅丑', '卯巳', '辰酉', '巳寅', '午午', '未戌', '申卯', '酉未', '戌亥', '亥辰', '子申', '丑子'], // xie
        '大煞' => ['寅戌', '卯巳', '辰午', '巳未', '午寅', '未卯', '申辰', '酉亥', '戌子', '亥丑', '子申', '丑酉'],
        '归忌' => ['寅丑', '卯寅', '辰子', '巳丑', '午寅', '未子', '申丑', '酉寅', '戌子', '亥丑', '子寅', '丑子'],
        '往亡' => ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'],
        '孤辰' => ['寅巳', '卯巳', '辰巳', '巳申', '午申', '未申', '申亥', '酉亥', '戌亥', '亥寅', '子寅', '丑寅'],
        '寡宿' => ['寅丑', '卯丑', '辰丑', '巳辰', '午辰', '未辰', '申未', '酉未', '戌未', '亥戌', '子戌', '丑戌'],
        '红沙' => ['寅巳', '卯酉', '辰丑', '巳巳', '午酉', '未丑', '申巳', '酉酉', '戌丑', '亥巳', '子酉', '丑丑'],
        '不举' => ['寅子', '卯子', '辰子', '巳卯', '午卯', '未卯', '申午', '酉午', '戌午', '亥酉', '子酉', '丑有'],
        '斧头煞' => ['寅辰', '卯辰', '辰辰', '巳未', '午未', '未未', '申戌', '酉戌', '戌戌', '亥丑', '子丑', '丑丑'],
        '地寡' => ['寅酉', '卯酉', '辰酉', '巳子', '午子', '未子', '申卯', '酉卯', '戌卯', '亥午', '子午', '丑午'],
        '蚩尤' => ['寅寅', '卯辰', '辰午', '巳申', '午戌', '未子', '申寅', '酉辰', '戌午', '亥申', '子戌', '丑子'],
        '飞流' => ['寅卯', '卯巳', '辰未', '巳酉', '午亥', '未丑', '申卯', '酉巳', '戌未', '亥酉', '子亥', '丑丑'],
        '口舌' => ['寅辰', '卯酉', '辰午', '巳亥', '午申', '未丑', '申戌', '酉卯', '戌子', '亥巳', '子寅', '丑未'],
        '大凶' => ['寅戌', '卯卯', '辰子', '巳巳', '午寅', '未未', '申辰', '酉酉', '戌午', '亥亥', '子申', '丑丑'],
        '天劫' => ['寅丑', '卯午', '辰卯', '巳申', '午巳', '未戌', '申未', '酉子', '戌酉', '亥寅', '子亥', '丑辰'],
        '大祸' => ['寅未', '卯子', '辰酉', '巳寅', '午亥', '未辰', '申丑', '酉午', '戌卯', '亥申', '子巳', '丑戌'],
        '天棒' => ['寅午', '卯申', '辰戌', '巳子', '午寅', '未辰', '申午', '酉申', '戌戌', '亥子', '子寅', '丑辰'],
        '天寡' => ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子'],
        '天岳' => ['寅申', '卯戌', '辰子', '巳寅', '午辰', '未午', '申申', '酉戌', '戌子', '亥寅', '子辰', '丑午'],
        '狱日' => ['寅午', '卯午', '辰午', '巳戌', '午戌', '未戌', '申丑', '酉丑', '戌丑', '亥辰', '子辰', '丑辰'],
        '徒日' => ['寅午', '卯午', '辰午', '巳戌', '午戌', '未戌', '申丑', '酉丑', '戌丑', '亥辰', '子辰', '丑辰'],
        '隶日宅空' => ['寅申', '卯申', '辰申', '巳亥', '午亥', '未亥', '申寅', '酉寅', '戌寅', '亥巳', '子巳', '丑巳'],
        '罪日' => ['寅亥', '卯亥', '辰亥', '巳寅', '午寅', '未寅', '申巳', '酉巳', '戌巳', '亥申', '子申', '丑申'],
        '元武' => ['寅酉', '卯亥', '辰丑', '巳卯', '午巳', '未未', '申酉', '酉亥', '戌丑', '亥卯', '子巳', '丑未'],
        '阴私' => ['寅酉', '卯亥', '辰丑', '巳卯', '午巳', '未未', '申酉', '酉亥', '戌丑', '亥卯', '子巳', '丑未'],
        '土勃' => ['寅亥', '卯丑', '辰卯', '巳巳', '午未', '未酉', '申亥', '酉丑', '戌卯', '亥巳', '子未', '丑酉'],
        '受死' => ['寅戌', '卯辰', '辰亥', '巳巳', '午子', '未午', '申丑', '酉未', '戌寅', '亥申', '子卯', '丑酉'],
        '地贼' => ['寅子', '卯子', '辰亥', '巳戌', '午酉', '未午', '申午', '酉午', '戌巳', '亥辰', '子卯', '丑子'],
        '五鬼' => ['寅午', '卯寅', '辰辰', '巳酉', '午卯', '未申', '申丑', '酉酉', '戌子', '亥亥', '子未', '丑戌'],
        '荒芜' => ['寅巳', '卯酉', '辰丑', '巳申', '午子', '未辰', '申亥', '酉卯', '戌未', '亥寅', '子午', '丑戌'],
        '财离' => ['寅辰', '卯丑', '辰戌', '巳未', '午寅', '未子', '申酉', '酉午', '戌亥', '亥卯', '子申', '丑巳'],
        '天地争雄' => [
            '寅巳', '寅午', '卯亥', '卯子', '辰午', '辰未', '巳子', '巳丑', '午未', '午申', '未丑', '未寅',
            '申申', '申酉', '酉寅', '酉卯', '戌酉', '戌戌', '亥卯', '亥辰', '子戌', '子亥', '丑辰', '丑巳',
        ],
        '天翻' => ['寅巳', '卯辰', '辰申', '巳巳', '午丑', '未子', '申亥', '酉戌', '戌卯', '亥午', '子寅', '丑巳'],
        '地覆' => ['寅亥', '卯戌', '辰酉', '巳申', '午卯', '未午', '申酉', '酉辰', '戌酉', '亥辰', '子未', '丑卯'],
        '天隔' => ['寅寅', '卯子', '辰戌', '巳申', '午午', '未辰', '申寅', '酉子', '戌戌', '亥申', '子午', '丑辰'],
        '天地破败' => ['寅卯', '卯寅', '辰丑', '巳子', '午亥', '未戌', '申酉', '酉申', '戌未', '亥午', '子巳', '丑辰'],
        '白浪' => ['寅寅', '卯卯', '辰辰', '巳巳', '午午', '未未', '申申', '酉酉', '戌戌', '亥亥', '子子', '丑丑'],
        '覆舟' => ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'],
        '殃败' => ['寅卯', '卯寅', '辰丑', '巳子', '午亥', '未戌', '申酉', '酉申', '戌未', '亥午', '子巳', '丑辰'],
        '蛟龙' => ['寅未', '卯申', '辰戌', '巳申', '午戌', '未丑', '申辰', '酉未', '戌辰', '亥申', '子子', '丑巳'],
        '水隔' => ['寅戌', '卯申', '辰午', '巳辰', '午寅', '未子', '申戌', '酉申', '戌午', '亥辰', '子寅', '丑子'],
        '四激' => ['寅丑', '卯丑', '辰丑', '巳戌', '午戌', '未戌', '申辰', '酉辰', '戌辰', '亥未', '子未', '丑未'],
        '日流财' => ['寅亥', '卯申', '辰巳', '巳寅', '午卯', '未午', '申子', '酉酉', '戌丑', '亥未', '子辰', '丑戌'],
        '天穷' => ['寅子', '卯寅', '辰午', '巳酉', '午子', '未寅', '申午', '酉酉', '戌子', '亥寅', '子午', '丑酉'],
        '勾绞' => ['寅亥', '卯午', '辰丑', '巳申', '午卯', '未戌', '申巳', '酉子', '戌未', '亥寅', '子酉', '丑辰'],
        '天瘟' => ['寅未', '卯戌', '辰辰', '巳寅', '午午', '未子', '申酉', '酉申', '戌巳', '亥亥', '子丑', '丑卯'],
        '六不成' => ['寅寅', '卯午', '辰戌', '巳巳', '午酉', '未丑', '申申', '酉子', '戌辰', '亥亥', '子卯', '丑未'],
        '牢日' => ['寅酉', '卯酉', '辰酉', '巳子', '午子', '未子', '申卯', '酉卯', '戌卯', '亥午', '子午', '丑午'],
        '死别' => ['寅戌', '卯戌', '辰戌', '巳丑', '午丑', '未丑', '申辰', '酉辰', '戌辰', '亥未', '子未', '丑未'],
        '伏罪' => ['寅亥', '卯亥', '辰亥', '巳寅', '午寅', '未寅', '申巳', '酉巳', '戌巳', '亥申', '子申', '丑申'],
        '刑狱' => ['寅丑', '卯丑', '辰丑', '巳辰', '午辰', '未辰', '申未', '酉未', '戌未', '亥戌', '子戌', '丑戌'],
        '徒隶' => ['寅申', '卯申', '辰申', '巳亥', '午亥', '未亥', '申寅', '酉寅', '戌寅', '亥巳', '子巳', '丑巳'],
        '披麻' => ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'],
        '月破' => ['寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳', '子午', '丑未'],
        '天雄' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'],
        '地雄' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'],
        '人隔' => ['寅酉', '卯未', '辰巳', '巳卯', '午丑', '未亥', '申酉', '酉未', '戌巳', '亥卯', '子丑', '丑亥'],
        '月火' => ['寅巳', '卯辰', '辰卯', '巳寅', '午丑', '未子', '申亥', '酉戌', '戌酉', '亥申', '子未', '丑午'],
        '卧尸' => ['寅子', '卯酉', '辰未', '巳申', '午巳', '未辰', '申卯', '酉寅', '戌丑', '亥午', '子戌', '丑亥'],
        '狼藉' => ['寅子', '卯酉', '辰午', '巳卯', '午子', '未酉', '申午', '酉卯', '戌子', '亥酉', '子午', '丑卯'],
        // '死气官符' => ['寅午', '卯未', '辰申', '巳酉', '午戌', '未亥', '申子', '酉丑', '戌寅', '亥卯', '子辰', '丑巳'],
        '破败' => ['寅申', '卯戌', '辰子', '巳寅', '午辰', '未午', '申申', '酉戌', '戌子', '亥寅', '子辰', '丑午'],
        '土瘟' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'],
        '鬼火' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'],
        '地隔' => ['寅辰', '卯寅', '辰子', '巳戌', '午申', '未午', '申辰', '酉寅', '戌子', '亥戌', '子申', '丑午'],
        '月建转杀' => ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子'],
        '龙口' => ['寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子', '子酉', '丑午'],
        '池耗' => ['寅申', '卯戌', '辰子', '午辰', '未丑', '申申', '酉戌', '戌子', '子辰', '丑丑'],
        '地耗' => ['寅辰', '卯卯', '辰寅', '巳未', '午子', '未巳', '申戌', '酉酉', '戌申', '亥丑', '子午', '丑亥'],
        '四部' => ['寅午', '卯午', '辰午', '巳卯', '午卯', '未卯', '申子', '酉子', '戌子', '亥酉', '子酉', '丑酉'],
        '龙会' => ['寅未', '卯戌', '辰亥', '巳亥', '午丑', '未戌', '申未', '酉卯', '戌丑', '亥丑', '子戌', '丑卯'],
        '林隔' => ['寅卯', '卯丑', '辰亥', '巳酉', '午未', '未巳', '申卯', '酉丑', '戌亥', '亥酉', '子未', '丑巳'],
        '山隔' => ['寅未', '卯巳', '辰卯', '巳丑', '午亥', '未酉', '申未', '酉巳', '戌卯', '亥丑', '子亥', '丑酉'],
        '天狗食畜' => ['寅子', '卯子', '辰子', '巳卯', '午卯', '未卯', '申午', '酉午', '戌午', '亥酉', '子酉', '丑酉'],
        '土忌' => ['寅寅', '卯巳', '辰申', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'],
        '地破' => ['寅亥', '卯子', '辰丑', '巳寅', '午卯', '未辰', '申巳', '酉午', '戌未', '亥申', '子酉', '丑戌'],
        '鲁班杀' => ['寅子', '卯子', '辰子', '巳卯', '午卯', '未卯', '申午', '酉午', '戌午', '亥酉', '子酉', '丑酉'],
        '龙虎' => ['寅巳', '卯亥', '辰午', '巳子', '午未', '未丑', '申申', '酉寅', '戌酉', '亥卯', '子戌', '丑辰'],
        '木马杀' => ['寅巳', '卯未', '辰酉', '巳申', '午戌', '未子', '申亥', '酉丑', '戌卯', '亥寅', '子辰', '丑午'],
        '独火' => ['寅巳', '卯辰', '辰卯', '巳寅', '午丑', '未子', '申亥', '酉戌', '戌酉', '亥申', '子未', '丑午'],
        '划削血刃' => ['寅亥', '卯申', '辰巳', '巳寅', '午卯', '未午', '申未', '酉酉', '戌戌', '亥丑', '子子', '丑辰'],
        '鲁班跌扑煞' => ['寅寅', '卯申', '辰巳', '巳亥', '午卯', '未午', '申酉', '酉子', '戌辰', '亥未', '子戌', '丑丑'],
        '冰消瓦解' => ['寅巳', '卯子', '辰丑', '巳寅', '午卯', '未戌', '申亥', '酉午', '戌未', '亥申', '子酉', '丑辰'],
        '日虚月空' => ['寅丑', '卯戌', '辰未', '巳辰', '午丑', '未戌', '申未', '酉辰', '戌丑', '亥戌', '子未', '丑辰'],
        '天火狼藉' => ['寅子', '卯卯', '辰午', '巳酉', '午子', '未卯', '申午', '酉酉', '戌子', '亥卯', '子午', '丑酉'],
        '空宅' => ['寅申', '卯申', '辰申', '巳寅', '午寅', '未寅', '申巳', '酉巳', '戌巳', '亥亥', '子亥', '丑亥'],
        '刀砧煞' => [
            '寅亥', '寅子', '卯亥', '卯子', '辰亥', '辰子', '巳寅', '巳卯', '午寅', '午卯', '未寅', '未卯',
            '申巳', '申午', '酉巳', '酉午', '戌巳', '戌午', '亥申', '亥酉', '子申', '子酉', '丑申', '丑酉',
        ],
        '蛇会' => ['寅午', '卯午', '寅未', '卯未', '辰戌', '巳戌', '午戌', '未戌', '申丑', '酉丑', '戌辰', '亥戌', '子戌', '丑亥'],
        '招摇' => ['寅辰', '卯卯', '辰寅', '巳丑', '午子', '未亥', '申戌', '酉酉', '戌申', '亥未', '子午', '丑巳'], // xie
        '毁败' => ['寅寅', '卯寅', '辰辰', '巳辰', '午午', '未午', '申申', '酉申', '戌戌', '亥戌', '子子', '丑子'],
        '丰至' => ['寅申', '卯申', '辰戌', '巳戌', '午子', '未子', '申寅', '酉寅', '戌辰', '亥辰', '子午', '丑午'],
        '徵冲' => ['寅酉', '卯酉', '辰亥', '巳亥', '午丑', '未丑', '申卯', '酉卯', '戌巳', '亥巳', '子未', '丑未'],
        '重折' => ['寅卯', '卯卯', '辰巳', '巳巳', '午未', '未未', '申酉', '酉酉', '戌亥', '亥亥', '子丑', '丑丑'],
        '火隔' => ['寅午', '卯辰', '辰寅', '巳子', '午戌', '未申', '申午', '酉辰', '戌寅', '亥子', '子戌', '丑申'],
        '次地火' => ['寅巳', '卯午', '辰未', '巳申', '午酉', '未戌', '申亥', '酉子', '戌丑', '亥寅', '子卯', '丑辰'],
        '血忌' => ['寅丑', '卯未', '辰寅', '巳申', '午卯', '未酉', '申辰', '酉戌', '戌巳', '亥亥', '子午', '丑子'],
        '净栏煞' => ['寅未', '卯申', '辰酉', '巳戌', '午亥', '未子', '申丑', '酉寅', '戌卯', '亥辰', '子巳', '丑午'],
        '畜官' => ['寅午', '卯未', '辰申', '巳酉', '午戌', '未亥', '申子', '酉丑', '戌寅', '亥卯', '子辰', '丑巳'],
        '二火血' => ['寅丑', '卯未', '辰寅', '巳申', '午卯', '未酉', '申辰', '酉戌', '戌巳', '亥亥', '子午', '丑子'],
        '牛飞廉' => ['寅午', '卯午', '辰申', '巳申', '午戌', '未戌', '申子', '酉子', '戌寅', '亥寅', '子辰', '丑辰'],
        '牛腹胀' => ['寅戌', '卯戌', '辰戌', '巳丑', '午丑', '未丑', '申辰', '酉辰', '戌辰', '亥未', '子未', '丑未'],
        '飞廉' => ['寅戌', '卯巳', '辰午', '巳未', '午寅', '未卯', '申辰', '酉亥', '戌子', '亥丑', '子申', '丑酉'],
        '焦坎' => ['寅辰', '卯丑', '辰戌', '巳未', '午卯', '未子', '申酉', '酉午', '戌寅', '亥亥', '子申', '丑巳'],
        // '千斤煞' => ['寅巽', '卯巽', '辰巽', '巳坤', '午坤', '未坤', '申乾', '酉乾', '戌乾', '亥艮', '子艮', '丑艮'],
        '天狗食色' => ['寅子', '卯子', '辰子', '巳卯', '午卯', '未卯', '申午', '酉午', '戌午', '亥酉', '子酉', '丑酉'],
        '月游官府' => ['寅申', '卯巳', '辰寅', '巳亥', '午申', '未巳', '申寅', '酉亥', '戌申', '亥巳', '子寅', '丑亥'],
        '鬼哭' => ['寅未', '卯戌', '辰辰', '巳寅', '午午', '未子', '申酉', '酉申', '戌巳', '亥亥', '子丑', '丑卯'],
        '神号' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'],
        '神隔' => ['寅巳', '卯卯', '辰丑', '巳亥', '午酉', '未未', '申巳', '酉卯', '戌丑', '亥亥', '子酉', '丑未'],
        '鬼隔' => ['寅申', '卯午', '辰辰', '巳寅', '午子', '未戌', '申申', '酉午', '戌辰', '亥寅', '子子', '丑戌'],
        '破家煞' => ['寅巳', '卯子', '辰丑', '巳申', '午卯', '未戌', '申亥', '酉午', '戌未', '亥寅', '子酉', '丑辰'],
        '鬼神空' => ['寅申', '卯申', '辰申', '巳寅', '午寅', '未寅', '申巳', '酉巳', '戌巳', '亥亥', '子亥', '丑亥'],
        '穴天狗' => ['寅辰', '卯巳', '辰午', '巳未', '午申', '未酉', '申戌', '酉亥', '戌子', '亥丑', '子寅', '丑卯'],
        '天狗下食' => ['寅子', '卯丑', '辰寅', '巳卯', '午辰', '未巳', '申午', '酉未', '戌申', '亥酉', '子戌', '丑亥'],
        '死日' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'],
        '重丧' => ['寅甲', '卯乙', '辰己', '巳丙', '午丁', '未己', '申庚', '酉辛', '戌己', '亥壬', '子癸', '丑己'],
        '报怨煞' => ['寅丙', '卯申', '辰庚', '巳丙', '午甲', '未壬', '申庚', '酉丙', '戌甲', '亥壬', '子庚', '丑丙'],
        '撞命煞' => ['寅甲', '卯壬', '辰庚', '巳丙', '午甲', '未壬', '申庚', '酉丙', '戌甲', '亥壬', '子庚', '丑丙'],
        '土禁' => ['寅亥', '卯亥', '辰亥', '巳寅', '午寅', '未寅', '申巳', '酉巳', '戌巳', '亥申', '子申', '丑申'],
        '白虎煞' => ['寅戌', '卯亥', '辰子', '巳丑', '午寅', '未卯', '申辰', '酉巳', '戌午', '亥未', '子申', '丑酉'],
        '地皇' => ['寅午', '卯巳', '辰辰', '巳卯', '午寅', '未丑', '申子', '酉亥', '戌戌', '亥酉', '子申', '丑未 '],
        '怨煞' => ['寅寅', '卯亥', '辰申', '巳巳', '午寅', '未亥', '申申', '酉巳', '戌寅', '亥亥', '子申', '丑巳'],
        // '游都神' => ['寅丙', '卯丁', '辰坤', '巳庚', '午辛', '未乾', '申壬', '酉癸', '戌艮', '亥甲', '子乙', '丑巽'],
        // '四季天煞' => ['寅酉', '卯酉', '辰酉', '巳子', '午子', '未子', '申卯', '酉卯', '戌卯', '亥午', '子午', '丑午'],
        // '地辂' => ['寅酉', '卯戌', '辰亥', '巳子', '午丑', '未寅', '申卯', '酉辰', '戌巳', '亥午', '子未 ', '丑申'],
        // '崩腾' => ['寅辛', '卯乙', '辰丙', '巳壬', '午庚', '未甲', '申癸', '酉丁', '戌巳', '亥酉', '子坤', '丑乾'],
        // '帝车' => ['寅寅', '卯寅', '辰寅', '巳巳', '午巳', '未巳', '申申', '酉申', '戌申', '亥亥', '子亥', '丑亥'],
        // '帝舍' => ['寅辰', '卯辰', '辰辰', '巳未', '午未', '未未', '申戌', '酉戌', '戌戌', '亥丑', '子丑', '丑丑'],
        // '帝辂' => ['寅卯', '卯卯', '辰卯', '巳午', '午午', '未午', '申酉', '酉酉', '戌酉', '亥子', '子子', '丑子'],
        // '镇天火煞' => ['寅卯', '卯寅', '辰丑', '巳子', '午亥', '未戌', '申酉', '酉申', '戌未', '亥午', '子巳', '丑辰'],
        // '五贪' => ['寅巳', '卯申', '辰亥', '巳寅', '午巳', '未申', '申亥', '酉寅', '戌巳', '亥申', '子亥', '丑寅'],
        '月火血' => ['寅申', '卯亥', '辰寅', '巳巳', '午酉', '未子', '申卯', '酉午', '戌戌', '亥丑', '子辰', '丑未'],
        '横天赤口' => ['寅辰', '卯卯', '辰寅', '巳丑', '午子', '未亥', '申戌', '酉酉', '戌申', '亥未', '子午', '丑巳'],
        '玉堂' => ['寅卯', '卯酉', '辰辰', '巳戌', '午巳', '未亥', '申午', '酉子', '戌未', '亥丑', '子申', '丑寅'],
        '傍四废' => [
            '寅庚', '寅辛', '卯庚', '卯辛', '辰庚', '辰辛', '巳壬', '巳癸', '午壬', '午癸', '未壬', '未癸',
            '申甲', '申乙', '酉甲', '酉乙', '戌甲', '戌乙', '亥丙', '亥丁', '子丙', '子丁', '丑丙', '丑丁',
        ],
        '流财方' => [
            '寅甲', '寅庚', '卯丁', '卯癸', '辰甲', '辰庚', '巳丁', '巳癸', '午乙', '午辛', '未丙', '未壬',
            '申乙', '申辛', '酉丁', '酉癸', '戌丙', '戌壬', '亥甲', '亥庚', '子乙', '子辛', '丑壬', '丑丙',
        ],
        '牛勾绞' => [
            '寅申', '寅酉', '卯申', '卯酉', '辰申', '辰酉', '巳亥', '巳子', '午亥', '午子', '未亥', '未子',
            '申寅', '申卯', '酉寅', '酉卯', '戌寅', '戌卯', '亥巳', '亥午', '子巳', '子午', '丑巳', '丑午',
        ],
        '四季忌用' => ['寅未', '卯未', '辰未', '巳戌', '午戌', '未戌', '申丑', '酉丑', '戌丑', '亥辰', '子辰', '丑辰'],
    ];

    /**
     * 凶神数据列表
     * @var array|array[]
     */
    protected array $listXiong2 = [
        'mdz_dgz' => [
            '阴阳大会' => ['寅甲戌', '卯乙酉', '午丙午', '申丁巳', '酉庚辰', '戌辛卯', '子壬子', '丑癸亥'], //xie
            '阴阳小会' => ['卯己卯', '辰戊辰', '巳己巳', '午戊午', '戌己酉', '亥戊戌', '子己亥', '丑戊子'], //xie
            '行狠' => ['辰甲申', '巳乙未', '戌庚寅', '亥辛丑'], //xie
            '了戾' => ['辰丙申', '巳丁未', '戌壬寅', '亥癸丑'], //xie
            '孤辰' => ['辰戊申', '辰庚申', '辰壬申', '巳己未', '巳辛未', '巳癸未', '戌甲寅', '戌丙寅', '戌戊寅', '亥乙丑', '亥丁丑', '亥己丑'], //xie
            '单阴' => ['辰戊辰'], //xie
            '纯阴' => ['亥己亥'], //xie
            '孤阳' => ['戌戊戌'], //xie
            '纯阳' => ['巳己巳'], //xie
            '岁薄' => ['巳丙午', '巳戊午', '亥壬子', '亥戊子'], //xie
            '逐阵' => ['未丙午', '未戊午', '丑壬子', '丑戊子'], //xie
            '阴阳交破' => ['巳癸亥', '亥丁巳'], //xie
            '阴阳击冲' => ['午壬子', '子丙午'], //xie
            '阳破阴冲' => ['午', '未癸丑', '申', '酉', '戌', '丑丁未'], //xie
            '阴位' => ['辰庚辰', '戌甲戌'], //xie
            '阴道冲阳' => ['寅', '卯己酉', '酉己卯', '戌'], //xie
            '三阴' => ['寅辛酉', '申乙卯'], //xie
            '阳错' => ['寅甲寅', '卯乙卯', '辰甲辰', '巳丁巳', '巳己巳', '未丁未', '未己未', '申庚申', '酉辛酉', '戌庚戌', '亥癸亥', '丑癸丑'], //xie
            '阴错' => ['寅庚戌', '卯辛酉', '辰庚甲', '巳丁未', '巳己未', '未丁巳', '未己巳', '申甲辰', '酉乙卯', '戌甲寅', '亥癸丑', '丑癸亥'], //xie
            '阴阳俱错' => ['午丙午', '丑壬子'], //xie
            '绝阴' => ['巳戊辰'], //xie
            '绝阳' => ['亥戊戌'], //xie
            '五墓' => ['寅乙未', '卯乙未', '辰戊辰', '巳丙戌', '午丙戌', '未戊辰', '申辛丑', '酉辛丑', '戌戊辰', '亥壬辰', '子壬辰', '丑戊辰'], //xie
            '四耗' => ['寅壬子', '卯壬子', '辰壬子', '巳乙卯', '午乙卯', '未乙卯', '申戊午', '酉戊午', '戌戊午', '亥辛酉', '子辛酉', '丑辛酉'], //xie
            '四废' => ['寅庚申', '寅辛酉', '卯庚申', '卯辛酉', '辰庚申', '辰辛酉', '巳壬子', '巳癸亥', '午壬子', '午癸亥', '未壬子', '未癸亥', '申甲寅', '申乙卯', '酉甲寅', '酉乙卯', '戌甲寅', '戌乙卯', '亥丙午', '亥丁巳', '子丙午', '子丁巳', '丑丙午', '丑丁巳'], //xie
            '四忌' => ['寅甲子', '卯甲子', '辰甲子', '巳丙子', '午丙子', '未丙子', '申庚子', '酉庚子', '戌庚子', '亥壬子', '子壬子', '丑壬子'], //xie
            '四穷' => ['寅乙亥', '卯乙亥', '辰乙亥', '巳丁亥', '午丁亥', '未丁亥', '申辛亥', '酉辛亥', '戌辛亥', '亥癸亥', '子癸亥', '丑癸亥'], //xie
            '八龙' => ['寅甲子', '寅乙亥', '卯甲子', '卯乙亥', '辰甲子', '辰乙亥'], //xie
            '七鸟' => ['巳丙子', '巳丁亥', '午丙子', '午丁亥', '未丙子', '未丁亥'], //xie
            '九虎' => ['申庚子', '申辛亥', '酉庚子', '酉辛亥', '戌庚子', '戌辛亥'], //xie
            '六蛇' => ['亥壬子', '亥癸亥', '子壬子', '子癸亥', '丑壬子', '丑癸亥'], //xie
            '地囊' => ['寅庚子', '寅庚午', '卯乙未', '卯癸丑', '辰甲子', '辰壬午', '巳己卯', '巳己酉', '午甲辰', '午壬戌', '未丙辰', '未丙戌', '申丁巳', '申丁亥', '酉丙寅', '酉丙申', '戌辛丑', '戌辛未', '亥戊寅', '亥戊申', '子辛卯', '子辛酉', '丑乙卯', '丑癸酉'], //xie
            '亡赢' => ['寅甲寅', '卯甲午', '辰甲戌', '巳丁卯', '午丁巳', '未庚辰', '申庚寅', '酉庚子', '戌戊辰', '亥癸亥', '子癸巳', '丑癸卯'], //xie
            '五穷' => [
                '寅甲子', '寅乙亥', '卯甲子', '卯乙亥', '辰甲子', '辰乙亥', '巳丙子', '巳丁亥', '午丙子', '午丁亥', '未丙子', '未丁亥',
                '申庚子', '申辛亥', '酉庚子', '酉辛亥', '戌庚子', '戌辛亥', '亥壬子', '亥癸亥', '子壬子', '子癸亥', '丑壬子', '丑癸亥',
            ],
            '虚败' => ['寅己酉', '卯己酉', '辰己酉', '巳甲子', '午甲子', '未甲子', '申辛卯', '酉辛卯', '戌辛卯', '亥庚午', '子庚午', '丑庚午'],
            '绝烟火' => ['寅丁卯', '卯甲子', '辰癸酉', '巳庚午', '午丁卯', '未甲子', '申癸酉', '酉庚午', '戌丁卯', '亥甲子', '子癸酉', '丑庚午'],
            '天地正转' => ['寅癸卯', '卯癸卯', '辰癸卯', '巳丙午', '午丙午', '未丙午', '申丁酉', '酉丁酉', '戌丁酉', '亥庚子', '子庚子', '丑庚子'],
            '天地转杀' => [
                '寅乙卯', '寅辛卯', '卯乙卯', '卯辛卯', '辰乙卯', '辰辛卯', '巳丙午', '巳戊午', '午丙午', '午戊午', '未丙午', '未戊午',
                '申辛酉', '申癸酉', '酉辛酉', '酉癸酉', '戌辛酉', '戌癸酉', '亥壬子', '亥丙子', '子壬子', '子丙子', '丑壬子', '丑丙子',
            ],
            '天转地转' => [
                '寅乙卯', '寅辛卯', '卯乙卯', '卯辛卯', '辰乙卯', '辰辛卯', '巳丙午', '巳戊午', '午丙午', '午戊午', '未丙午', '未戊午',
                '申辛酉', '申癸酉', '酉辛酉', '酉癸酉', '戌辛酉', '戌癸酉', '亥壬子', '亥丙子', '子壬子', '子丙子', '丑壬子', '丑丙子',
            ],
            '五凤' => ['寅庚寅', '卯庚寅', '辰庚寅', '巳庚午', '午庚午', '未庚午', '申庚申', '酉庚申', '戌庚申', '亥乙酉', '子乙酉', '丑乙酉'],
            '四旺' => ['寅乙卯', '卯乙卯', '辰乙卯', '巳丙午', '午丙午', '未丙午', '申辛酉', '酉辛酉', '戌辛酉', '亥壬子', '子壬子', '丑壬子'],
            '四时大墓' => ['寅乙未', '卯乙未', '辰乙未', '巳丙戌', '午丙戌', '未丙戌', '申辛丑', '酉辛丑', '戌辛丑', '亥壬辰', '子壬辰', '丑壬辰'],
        ],
        'dgz' => [
            '人民离' => ['戊申', '己酉'],
            '九土鬼' => ['乙酉', '癸巳', '辛丑', '丁巳', '庚戌', '甲午', '壬寅', '己酉', '戊午'],
            '江河离' => ['壬申', '癸酉'],
            '子胥死日' => ['壬辰'],
            '河伯死日' => ['庚辰'],
            '九丑' => ['戊子', '戊午', '壬子', '壬午', '乙卯', '己卯', '辛卯', '己酉', '辛酉'],
            '触水龙' => ['丙子', '癸丑', '癸未'],
            '八专' => ['甲寅', '丁未', '己未', '庚申', '癸丑'],
            '无禄' => ['甲辰', '乙巳', '庚辰', '辛巳', '丙申', '戊戌', '丁亥', '己丑', '壬申', '癸亥'],
            '离窠' => ['丁卯', '戊辰', '己巳', '壬申', '戊寅', '辛巳', '壬午', '戊子', '己丑', '戊戌', '己亥', '辛丑', '戊申', '辛亥', '戊午', '壬戌', '癸亥'],
            '猖鬼败亡' => ['丁卯', '戊辰', '戊寅', '辛巳', '戊子', '己丑', '戊戌', '己亥', '辛丑', '庚戌', '戊午', '壬戌'],
            '红嘴朱雀' => ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            '阴错阳差' => ['辛卯', '壬辰', '癸巳', '丙午', '丁未', '戊申', '辛酉', '壬戌', '癸亥', '丙子', '丁丑', '戊寅'],
            //'大煞白虎入中宫' => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            '阴将' => ['壬子', '壬寅', '癸卯', '癸亥', '甲子', '甲寅', '乙卯', '乙丑', '乙亥'],
            '阳将' => ['辛酉', '辛未', '辛巳', '庚申', '庚午', '戊午', '己酉', '己未', '己巳', '丁酉', '丁未', '丁巳'],
            '阴阳俱将' => ['壬午', '壬申', '癸巳', '癸未', '癸酉', '甲午', '甲申', '乙未', '乙酉', '乙巳'],
            '鸡缓' => ['丁卯', '甲戌', '辛丑', '戊子', '乙未', '壬寅', '乙酉', '丙辰', '癸酉'],
            '大杀白虎入中宫' => ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            '黄獭星' => ['丁丑', '癸未', '庚寅', '壬辰', '甲午', '己亥', '丁未', '甲寅', '戊午', '壬戌'],
            '耗总' => ['庚辰', '辛巳', '丙戌', '丁亥', '庚戌', '辛亥', '丙辰', '丁巳'],
            '红嘴朱雀入中宫' => ['乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            '破群' => ['戊辰', '己卯', '庚寅', '壬辰', '甲寅', '庚申'],
            '泉渴' => ['辛巳', '己丑', '庚寅', '壬辰', '戊申'],
            '泉闭' => ['戊辰', '辛巳', '己丑', '庚寅', '甲寅'],
            '离巢' => ['戊辰', '己已', '丁卯', '戊寅', '辛巳', '戊子', '己丑，辛卯', '戊戌', '己亥', '戊午', '壬戌', '癸亥'],
            '五不归' => ['己卯', '辛巳', '丙戌', '壬辰', '丙申', '己酉', '辛亥。壬子', '丙辰', '庚申', '辛酉'],
        ],
        'ddz' => [
            '五离' => ['申', '酉'],
            '重日' => ['巳', '亥'], //xie
        ],
    ];

    /**
     * 初始时间
     * @var Huangli
     */
    protected Huangli $huangli;

    /**
     * 基础数据
     * @var array
     */
    protected array $baseData = [];

    /**
     * 初始化
     * @param Huangli $huangli
     * @throws Exception
     */
    public function __construct(Huangli $huangli)
    {
        $this->huangli = $huangli;
        $year = (int)$huangli->dateTime->format('Y');
        $jiNian = $huangli->getLunarTganDzhi();
        $nongli = $huangli->getNongLi();
        $monthDays = $huangli->getLunarDays($nongli['y'], $nongli['m'], $nongli['leap']);
        $base['n_days'] = $monthDays;
        $base['leap'] = $nongli['leap'];
        $xingSu = $huangli->getXingSu();
        $this->baseData = [
            'jinian' => $jiNian,
            'n_days' => $monthDays,
            'leap' => $nongli['leap'],
            'nongli' => $nongli,
            'xingsu' => $xingSu[0],
        ];
        $this->setJieQi($year);
    }

    /**
     * 设置节气
     * @param int $year
     * @return bool
     */
    public function setJieQi(int $year): bool
    {
        $this->baseData['jieqi'] = SolarTerm::getAllJieQi($year);
        return true;
    }

    /**
     * 设置基础数据
     * @param string $key 键名
     * @param array|string $data
     * @return bool
     */
    public function setBaseData(string $key, array | string $data): bool
    {
        $this->baseData[$key] = $data;
        return true;
    }

    /**
     * 获得相应的十神
     * @param bool $type 默认吉神 false 凶神
     * @return array
     * @throws Exception
     */
    public function getShen(bool $type = true): array
    {
        if ($type) {
            $list = $this->listJi1;
            $list1 = $this->listJi2;
        } else {
            $list = $this->listXiong1;
            $list1 = $this->listXiong2;
        }

        $jiNian = $this->baseData['jinian'];
        $mdz = $jiNian['m'][1];
        $ddz = $jiNian['d'][1];
        $dtg = $jiNian['d'][0];
        $str1 = $mdz . $ddz;
        $str2 = $mdz . $dtg;
        $dgz = $dtg . $ddz;
        $res1 = $this->getShenbyList($list, $str1, $str2);
        $res2 = $this->getShenbyList($list1['mdz_dgz'], $mdz . $dgz);
        $res3 = $this->getShenbyList($list1['ddz'], $ddz);
        $res4 = $this->getShenbyList($list1['dgz'], $dgz);
        $res = array_merge($res1, $res2, $res3, $res4);
        if ($type) {
            if ($this->bingji($mdz, $ddz)) {
                $res[] = '兵吉';
            }
            if ($this->muCang($mdz, $ddz)) {
                $res[] = '母仓';
            }
            if ($this->WangRi4($mdz, $ddz) || $this->WangRi4($mdz, $dtg)) {
                $res[] = '旺日';
            }
            if ($this->siXian($mdz, $dtg)) {
                $res[] = '四相';
            }
            if ($this->tianGui($mdz, $dtg)) {
                $res[] = '天贵';
            }
            if ($this->yinYangBuJian($mdz, $dgz)) {
                $res[] = '阴阳不将';
            }
            if ($this->gzXiangRi($mdz, $dgz)) {
                $res[] = '干支相日';
            }
        } else {
            if ($this->siLi()) {
                $res[] = '四离';
            }
            if ($this->siJue()) {
                $res[] = '四绝';
            }
            if ($this->qiWangwang()) {
                $res[] = '气往亡';
            }
            if ($this->wuXu($mdz, $ddz)) {
                $res[] = '五虚';
            }
            if ($this->tuGongJiFang($mdz, $ddz)) {
                $res[] = '土公忌';
            }
            if ($this->getHuoXing($mdz, $dgz)) {
                $res[] = '火星';
            }
            if ($this->baFeng($mdz, $dgz)) {
                $res[] = '八风';
            }
            $res1 = $this->getXiongByNongli();
            $res = array_merge($res, $res1);
            if ($this->getMieMo()) {
                $res[] = '灭没';
            }
            if ($this->getHuliXin()) {
                $res[] = '狐狸星';
            }
            if ($this->getBaJie()) {
                $res[] = '八节';
            }
            if ($this->getDiKuShaRi()) {
                $res[] = '帝酷煞日';
            }
            if ($this->getMiRi()) {
                $res[] = '密日';
            }
            $res2 = $this->getXiong2();
            if ($res2) {
                $res = array_merge($res, $res2);
            }
            $res3 = $this->getXiong3();
            if ($res3) {
                $res = array_merge($res, $res3);
            }
            if ($this->getFuDuan()) {
                $res[] = '伏断';
            }
        }
        return $res;
    }

    /**
     * 获得凶神
     * @return array
     * @throws Exception
     */
    public function getXiongShen(): array
    {
        return $this->getShen(false);
    }

    /**
     *  兵吉  《ao》《xie》
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    public function bingJi(string $mdz, string $ddz): bool
    {
        $list = [
            '寅' => '子、丑、寅、卯', '卯' => '亥、子、丑、寅', '辰' => '戌、亥、子、丑', '巳' => '酉、戌、亥、子',
            '午' => '申、酉、戌、亥', '未' => '未、甲、酉、戌', '申' => '午、未、申、酉', '酉' => '巳、午、未、申',
            '戌' => '辰、巳、午、未', '亥' => '卯、辰、巳、午', '子' => '寅、卯、辰、巳', '丑' => '丑、寅、卯、辰',
        ];
        $list1 = explode('、', $list[$mdz]);
        return in_array($ddz, $list1);
    }

    /**
     * 母仓 (ao ze xie xiang)
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    public function muCang(string $mdz, string $ddz): bool
    {
        $list = [
            '寅' => ['亥', '子'], '卯' => ['亥', '子'], '辰' => ['亥', '子'], '巳' => ['寅', '卯'], '午' => ['寅', '卯'], '未' => ['寅', '卯'],
            '申' => ['辰', '戌', '丑', '未'], '酉' => ['辰', '戌', '丑', '未'], '戌' => ['辰', '戌', '丑', '未'],
            '亥' => ['申', '酉'], '子' => ['申', '酉'], '丑' => ['申', '酉'],
        ];
        return in_array($ddz, $list[$mdz]);
    }

    /**
     * 旺日 (ao ze)
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    public function WangRi4(string $mdz, string $ddz)
    {
        $list = [
            '寅、卯、辰' => ['甲', '乙', '寅', '卯'], '巳、午、未' => ['丙', '丁', '巳', '午'], '申、酉、戌' => ['庚', '辛', '申', '酉'], '亥、子、丑' => ['壬', '癸', '子', '亥'],
        ];
        $bool = false;
        foreach ($list as $k => $v) {
            if (in_array($ddz, $v) && in_array($mdz, explode('、', $k))) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 四相 (ze xie)
     * @param string $mdz 月地支
     * @param string $rtg 日天干
     * @return bool
     */
    public function siXian(string $mdz, string $rtg): bool
    {
        if (in_array($mdz, ['寅', '卯', '辰']) && in_array($rtg, ['丙', '丁'])) {
            return true;
        }
        if (in_array($mdz, ['巳', '午', '未']) && in_array($rtg, ['戊', '己'])) {
            return true;
        }
        if (in_array($mdz, ['申', '酉', '戌']) && in_array($rtg, ['壬', '癸'])) {
            return true;
        }
        if (in_array($mdz, ['亥', '子', '丑']) && in_array($rtg, ['甲', '乙'])) {
            return true;
        }
        return false;
    }

    /**
     * 天贵 xie
     * @param string $mdz 月支
     * @param string $rtg +日干
     * @return bool
     */
    public function tianGui(string $mdz, string $rtg): bool
    {
        $list = [
            '寅' => ['甲', '乙'], '卯' => ['甲', '乙'], '辰' => ['甲', '乙'], '巳' => ['丙', '丁'], '午' => ['丙', '丁'], '未' => ['丙', '丁'],
            '申' => ['庚', '辛'], '酉' => ['庚', '辛'], '戌' => ['庚', '辛'], '亥' => ['壬', '癸'], '子' => ['壬', '癸'], '丑' => ['壬', '癸'],
        ];
        return in_array($rtg, $list[$mdz]);
    }

    /**
     * 阴阳不将  《xie》
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    public function yinYangBuJian(string $mdz, string $dgz): bool
    {
        $list = [
            '寅' => ['丙寅', '丁卯', '丙子', '丁丑', '己卯', '丁亥', '己丑', '庚寅', '辛卯', '己庚', '庚子', '辛丑', '辛亥'],
            '卯' => ['乙丑', '丙寅', '乙亥', '丙子', '丁丑', '丙戌', '丁亥', '己丑', '庚寅', '己亥', '庚子', '庚戌'],
            '辰' => ['甲子', '乙丑', '甲戌', '乙亥', '丙子', '丁丑', '乙酉', '丙戌', '丁亥', '己丑', '丁酉', '己亥', '己酉'],
            '巳' => ['甲子', '甲戌', '乙亥', '丙子', '甲申', '乙酉', '丙戌', '丁亥', '戊子', '丙甲', '丁酉', '戊戌', '戊申'],
            '午' => ['癸酉', '甲戌', '乙亥', '癸未', '甲申', '乙酉', '丙戌', '乙未', '丙申', '戊戌', '戊申', '癸亥'],
            '未' => ['壬申', '壬戌', '癸酉', '甲戌', '壬午', '癸未', '甲申', '乙酉', '甲午', '乙未', '乙巳', '戊申', '戊午'],
            '申' => ['壬申', '癸酉', '壬午', '癸未', '甲申', '乙酉', '癸巳', '甲午', '乙未', '乙巳', '戊申', '戊午'],
            '酉' => ['午辰', '辛未', '壬申', '戊午', '辛巳', '壬午', '癸未', '甲申', '壬辰', '癸巳', '甲午', '甲辰', '戊申'],
            '戌' => ['戊辰', '庚午', '辛未', '庚辰', '辛巳', '壬午', '癸未', '甲申', '壬辰', '癸巳', '甲午', '甲辰', '戊申'],
            '亥' => ['己巳', '庚午', '己卯', '庚辰', '辛巳', '壬午', '庚寅', '辛卯', '壬辰', '癸巳', '壬寅', '癸卯'],
            '子' => ['丁卯', '己巳', '丁丑', '己卯', '庚辰', '辛巳', '己丑', '庚寅', '辛卯', '壬辰', '辛丑', '壬寅', '丁巳'],
            '丑' => ['丙寅', '丁卯', '丙子', '丁丑', '己卯', '庚辰', '己丑', '庚寅', '辛卯', '庚子', '辛丑', '丙辰'],
        ];
        return in_array($dgz, $list[$mdz]);
    }

    /**
     * 干支旺日 xie
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    public function gzWangRi(string $mdz, string $dgz): bool
    {
        $list = [
            '寅' => ['甲寅', '乙卯'], '卯' => ['甲寅', '乙卯'], '辰' => ['甲寅', '乙卯'], '巳' => ['丁巳', '丙午'],
            '午' => ['丁巳', '丙午'], '未' => ['丁巳', '丙午'], '申' => ['庚申', '辛酉'], '酉' => ['庚申', '辛酉'],
            '戌' => ['庚申', '辛酉'], '亥' => ['癸亥', '壬子'], '子' => ['癸亥', '壬子'], '丑' => ['癸亥', '壬子'],
        ];
        return in_array($dgz, $list[$mdz]);
    }

    /**
     * 干支相日 xie
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    public function gzXiangRi(string $mdz, string $dgz): bool
    {
        $list = [
            '寅' => ['丁巳', '丙午'], '卯' => ['丁巳', '丙午'], '辰' => ['丁巳', '丙午'], '巳' => ['戊辰', '戊戌'],
            '午' => ['戊辰', '戊戌'], '未' => ['戊辰', '戊戌'], '申' => ['壬子', '癸亥'], '酉' => ['壬子', '癸亥'],
            '戌' => ['壬子', '癸亥'], '亥' => ['甲寅', '乙卯'], '子' => ['甲寅', '乙卯'], '丑' => ['甲寅', '乙卯'],
        ];
        return in_array($dgz, $list[$mdz]);
    }

    // ==================== 下面部分是凶神 ======================

    /**
     * 四离 xie ao
     * @return bool
     * @throws \Exception
     */
    public function siLi(): bool
    {
        $list = ['冬至' => -1, '夏至' => -1, '春分' => -1, '秋分' => -1];
        return $this->checkDayByJQ($list);
    }

    /**
     * 四绝 //xie ao
     * @return bool
     * @throws \Exception
     */
    public function siJue(): bool
    {
        $list = ['立春' => -1, '立夏' => -1, '立秋' => -1, '立冬' => -1];
        return $this->checkDayByJQ($list);
    }

    /**
     * 气往亡
     * @return bool
     * @throws \Exception
     */
    public function qiWangwang()
    {
        // 立春后七日，惊蛰后十四日，清明后二十一日，立夏后八日，芒种后十六日，小暑后二十四日，立秋后九日，白露后十八日，寒露后二十七日，立冬后十日，大雪后二十日，小寒后三十日
        $list = [
            '立春' => 7, '惊蛰' => 14, '清明' => 21, '立夏' => 8, '芒种' => 16, '小暑' => 24, '立秋' => 9, '白露' => 18, '寒露' => 27, '立冬' => 10, '大雪' => 20, '小寒' => 30,
        ];
        return $this->checkDayByJQ($list);
    }

    /**
     * 根据农历月和日输出凶神
     * @return array
     */
    public function getXiongByNongli(): array
    {
        // 农历日 xie
        $list = [
            '月忌日' => [5, 14, 23],
            '四不祥' => [4, 7, 16, 19, 28],
            '天百穿' => [1, 3, 5, 11, 13, 16, 17, 19, 21, 27, 29, 30],
            '天百空' => [5, 7, 13, 16, 17, 19, 21, 27, 29],
            '土公箭' => [7, 17, 27],
            '横天朱雀' => [1, 9, 17, 25],
            //'天空亡'=>[1,8,9,16,7,15,6,14,5,13,4,12,3,11,2,10,17,25,20,24,23,22,30,21,29,28,19,27,18,26],
        ];
        // 农历月+ 日 xie
        $list1 = [
            '长星' => ['1_7', '2_4', '3_1', '4_9', '5_15', '6_10', '7_8', '8_2', '9_3', '9_4', '10_1', '11_12', '12_9'],
            '短星' => ['1_21', '2_19', '3_16', '4_25', '5_25', '6_20', '7_22', '8_18', '8_19', '9_16', '9_17', '10_14', '11_22', '12_25'],
            '四方耗' => ['1_2', '2_3', '3_4', '4_5', '5_2', '6_3', '7_4', '8_5', '9_2', '10_3', '11_4', '12_5'],
            '赤口' => [
                '1_3', '1_9', '1_15', '1_21', '1_27', '2_2', '2_8', '2_14', '2_20', '2_26', '3_1', '3_7', '3_13',
                '3_19', '3_25', '4_6', '4_12', '4_18', '4_24', '4_30', '5_5', '5_11', '5_17', '5_23', '5_29', '6_4', '6_10',
                '6_16', '6_22', '6_28', '7_3', '7_9', '7_15', '7_21', '7_27', '8_2', '8_8', '8_14', '8_20', '8_26', '9_1', '9_7', '9_13',
                '9_19', '9_25', '10_6', '10_12', '10_18', '10_24', '10_30', '11_5', '11_11', '11_17', '11_23', '11_29', '12_4', '12_10', '12_16', '12_22', '12_28',
            ],
            '天休废' => [
                '1_4', '1_9', '2_13', '2_18', '3_22', '3_27', '4_4', '4_9', '5_13', '5_18', '6_22', '6_27',
                '7_4', '7_9', '8_13', '8_18', '9_22', '9_27', '10_4', '10_9', '11_13', '11_18', '12_22', '12_27',
            ],
            '六壬空亡' => [
                '1_5', '1_11', '1_17', '1_23', '1_29', '2_4', '2_10', '2_16', '2_22', '2_28', '3_3', '3_9', '3_15', '3_21', '3_27', '4_2',
                '4_8', '4_14', '4_20', '4_26', '5_1', '5_7', '5_13', '5_19', '5_25', '6_6', '6_12', '6_18', '6_24', '6_30', '7_5', '7_11',
                '7_17', '7_23', '7_29', '8_4', '8_10', '8_16', '8_22', '9_3', '9_9', '9_15', '9_21', '9_27', '10_2', '10_8', '10_14', '10_20',
                '10_26', '11_1', '11_7', '11_13', '11_19', '11_25', '12_6', '12_12', '12_18', '12_24', '12_30',
            ],
            '土忌' => ['1_6', '2_13', '3_12', '4_8', '5_16', '6_24', '7_9', '8_27', '9_10', '10_14', '11_20', '12_16'],
            '鬼贼' => ['1_2', '2_3', '4_5', '5_2', '6_3', '8_3', '9_10', '10_2', '11_9', '12_7'],
            '天乙绝气' => ['1_6', '2_7', '3_8', '4_9', '5_10', '6_11', '7_12', '8_13', '9_14', '10_15', '11_16', '12_17', '8_9'],
            '瘟星入月' => ['1_6', '2_5', '3_3', '4_25', '5_24', '6_23', '7_20', '8_27', '9_17', '10_13', '11_12', '12_11'],
        ];
        $list2 = [
            // 大月 0 小月 1
            '水浪日' => [[1, 7, 23, 30], [3, 7, 12, 26]],
            '田痕忌' => [[6, 8, 22, 23], [8, 11, 13, 17]],
            '水痕' => [[1, 7, 11, 17, 23, 30], [3, 7, 12, 26]],
            '土痕' => [[3, 5, 7, 15, 18], [1, 2, 6, 22, 26, 27]],
            '鹦鹉星' => [[5, 10, 19], [5, 8]],
            '山痕' => [[2, 8, 12, 17, 20], [5, 14, 21, 23]],
            '债木星' => [[3, 11, 19, 27], [2, 10, 18, 26]],
            '争讼日' => [[6, 14, 22], [7, 15, 23]],
            '金痕' => [[5, 6, 7, 27], [2, 28, 29]],
            '土公占' => [[3, 5, 8], [1, 10, 28]],
            '惊定' => [[5, 17, 29], [8, 20]],
        ];
        $nD = $this->baseData['nongli']['d'] ?? 0;
        $nM = $this->baseData['nongli']['m'] ?? 0;
        $str = $nM . '_' . $nD;
        $res = [];
        foreach ($list as $k => $v) {
            if (in_array($nD, $v)) {
                $res[] = $k;
            }
        }
        foreach ($list1 as $k => $v) {
            if (in_array($str, $v)) {
                $res[] = $k;
            }
        }
        foreach ($list2 as $k => $v) {
            if ($this->baseData['n_days'] >= 30) {
                $tmp = $v[0];
            } else {
                $tmp = $v[1];
            }
            if (in_array($nD, $tmp)) {
                $res[] = $k;
            }
        }
        return $res;
    }

    /**
     * 土公忌方 xie
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    public function tuGongJiFang(string $mdz, string $ddz): bool
    {
        $list = [
            ['寅', '卯', '辰'], ['巳', '午', '未'], ['申', '酉', '戌'], ['亥', '子', '丑'],
        ];
        $bool = false;
        foreach ($list as $v) {
            if (in_array($mdz, $v) && in_array($ddz, $v)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 八风
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    public function baFeng(string $mdz, string $dgz): bool
    {
        $list = [
            '寅' => ['丁丑', '己酉'], '卯' => ['丁丑', '己酉'], '辰' => ['丁丑', '己酉'], '巳' => ['甲申', '甲辰'],
            '午' => ['甲申', '甲辰'], '未' => ['甲申', '甲辰'], '申' => ['丁未', '丁未'], '酉' => ['丁未', '丁未'],
            '戌' => ['丁未', '丁未'], '亥' => ['甲戌', '甲寅'], '子' => ['甲戌', '甲寅'], '丑' => ['甲戌', '甲寅'],
        ];
        return in_array($dgz, $list[$mdz]);
    }

    /**
     * 五虚 xie
     * @param string $mdz 月支
     * @param string $ddz 日支
     * @return bool
     */
    public function wuXu(string $mdz, string $ddz): bool
    {
        $list = [
            '寅' => ['巳', '酉', '丑'], '卯' => ['巳', '酉', '丑'], '辰' => ['巳', '酉', '丑'], '巳' => ['申', '子', '辰'],
            '午' => ['申', '子', '辰'], '未' => ['申', '子', '辰'], '申' => ['亥', '卯', '未'], '酉' => ['亥', '卯', '未'],
            '戌' => ['亥', '卯', '未'], '亥' => ['寅', '午', '戌'], '子' => ['寅', '午', '戌'], '丑' => ['寅', '午', '戌'],
        ];
        return in_array($ddz, $list[$mdz]);
    }

    /**
     * 火星
     * @param string $mdz 月支
     * @param string $dgz 日干支
     * @return bool
     */
    public function getHuoXing(string $mdz, string $dgz): bool
    {
        $list = [
            '寅' => ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            '卯' => ['甲子', '癸丑', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            '辰' => ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳'],
            '巳' => ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            '午' => ['甲子', '癸丑', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            '未' => ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳。'],
            '申' => ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            '酉' => ['甲子', '癸丑', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            '戌' => ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳。'],
            '亥' => ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            '子' => ['甲子', '癸丑', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            '丑' => ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳'],
        ];
        return in_array($dgz, $list[$mdz]);
    }

    /**
     * 根据节气偏移数组判断跟当前时间是否相同
     * @param array $list 节气偏移数组
     * @return bool
     * @throws \Exception
     */
    protected function checkDayByJQ(array $list): bool
    {
        $date = $this->huangli->dateTime->format('Y-m-d');
        $bool = false;
        foreach ($list as $k => $v) {
            $t = $this->baseData['jieqi'][$k] ?? '';
            if (empty($t)) {
                continue;
            }
            $t1 = new \DateTime($t);
            $str = $v > 0 ? '+' . $v : (string)$v;
            $t1->modify($str . ' day');
            $d1 = $t1->format('Y-m-d');
            if ($date == $d1) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 根据指定数组检查数据是否存在
     * @param array $list 检查的数组
     * @param string $str 条件1
     * @param string $str2 条件2
     * @return array
     */
    protected function getShenbyList(array $list, string $str, string $str2 = ''): array
    {
        $res = [];
        foreach ($list as $k => $v) {
            if (in_array($str, $v)) {
                $res[] = $k;
                continue;
            }
            if ($str2 && in_array($str2, $v)) {
                $res[] = $k;
            }
        }
        return $res;
    }

    /**
     * 灭没
     * @return bool
     * @throws \Exception
     */
    protected function getMieMo(): bool
    {
        $list = [
            '寅弦', '卯虚', '辰晦', '巳娄', '午朔', '未角', '申望', '酉亢', '戌虚', '亥鬼', '子盈', '丑牛',
        ];
        $xingSu = $this->huangli->getXingSu();
        $str = mb_substr($xingSu[0], 0, 1);
        $mdz = $this->baseData['jinian']['m'][1];
        return in_array($mdz . $str, $list);
    }

    /**
     * 狐狸星
     * @return bool
     */
    protected function getHuliXin(): bool
    {
        $jianChu = $this->huangli->getJianChu();
        $nDays = $this->baseData['n_days'];
        if ($nDays == 30 && $jianChu == '危') {
            return true;
        }
        if ($nDays < 30 && $jianChu == '定') {
            return true;
        }
        return false;
    }

    /**
     * 八节
     * @return bool
     * @throws \Exception
     */
    protected function getBaJie(): bool
    {
        $list = ['立春', '立夏', '立秋', '立冬', '春分', '秋分', '夏至', '冬至'];
        $date = $this->huangli->dateTime->format('Y-m-d');
        $bool = false;
        $listJq = $this->baseData['jieqi'];
        foreach ($list as $v) {
            $t = $listJq[$v];
            $t1 = new \DateTime($t);
            $d1 = $t1->format('Y-m-d');
            if ($date == $d1) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 月支+日支 凶日
     * @return array
     */
    protected function getXiong2(): array
    {
        $mdz = $this->baseData['jinian']['m'][1];
        $ddz = $this->baseData['jinian']['d'][1];
        $list = [
            '六畜瘦' => [
                '寅、卯、辰' => '巳、酉、丑', '巳、午、未' => '寅、午、戌', '申、酉、戌' => '亥、卯、未', '亥、子、丑' => '申、子、辰',
            ],
            '木髓杀' => [
                '寅' => '辰、申', '卯' => '子、寅', '辰' => '申、戌', '巳' => '午、未', '午' => '卯、辰', '未' => '寅、申',
                '申' => '巳、酉', '酉' => '午、未', '戌' => '寅、申', '亥' => '卯、亥', '子' => '巳、酉', '丑' => '未、辰',
            ],
            // '伤胎神'=>[
            //     '寅、卯、辰' => '子、午', '巳、午、未' => '丑、未', '申、酉、戌' => '辰、戌', '亥、子、丑' => '巳、亥',
            // ],
        ];
        $res = [];
        foreach ($list as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $arr = explode('、', $v1);
                $arrK = explode('、', $k1);
                if (in_array($mdz, $arrK) && in_array($ddz, $arr)) {
                    $res[] = $k;
                }
            }
        }
        return $res;
    }

    /**
     * 月支+日干 凶日
     * @return array
     */
    protected function getXiong3(): array
    {
        $mdz = $this->baseData['jinian']['m'][1];
        $dtg = $this->baseData['jinian']['d'][0];
        $list = [
            '四时忌方' => [
                '寅' => '丁、癸', '卯' => '丁、癸', '辰' => '丁、癸', '巳' => '甲、庚', '午' => '甲、庚', '未' => '甲、庚',
                '申' => '丙、壬', '酉' => '丙、壬', '戌' => '丙、壬', '亥' => '乙、辛', '子' => '乙、辛', '丑' => '乙、辛',
            ],
        ];
        $res = [];
        foreach ($list as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $arr = explode('、', $v1);
                $arrK = explode('、', $k1);
                if (in_array($mdz, $arrK) && in_array($dtg, $arr)) {
                    $res[] = $k;
                }
            }
        }
        return $res;
    }

    /**
     * 帝酷煞日
     * @return bool
     */
    protected function getDiKuShaRi(): bool
    {
        $list = [
            '1_3', '2_9', '3_10', '4_11', '5_12', '6_10', '7_12', '8_13', '9_13', '10_8', '11_15', '12_15',
        ];
        $nD = $this->baseData['nongli']['d'] ?? 0;
        $nM = $this->baseData['nongli']['m'] ?? 0;
        $str = $nM . '_' . $nD;
        $mdz = $this->baseData['jinian']['m'][1];
        $dgz = implode('', $this->baseData['jinian']['d']);
        $list1 = [
            '寅' => ['庚申'], '卯' => ['辛卯', '辛亥'], '辰' => ['甲戌', '庚戌'], '巳' => ['癸亥'], '午' => ['壬子'], '未' => ['癸丑'],
            '申' => ['甲寅'], '酉' => ['乙卯'], '戌' => ['甲辰'], '亥' => ['丁巳'], '子' => ['丙午'], '丑' => ['乙未', '丁未'],
        ];
        return in_array($str, $list) || in_array($dgz, $list1[$mdz]);
    }

    /**
     * 密日
     * @return bool
     */
    protected function getMiRi(): bool
    {
        $list = ['房', '虚', '昴', '星'];
        $xs = $this->baseData['xingsu'];
        $str = mb_substr($xs, 0, 1);
        return in_array($str, $list);
    }

    /**
     * 伏断
     * @return bool
     */
    protected function getFuDuan(): bool
    {
        $list = ['子虚', '丑斗', '寅室', '卯女', '辰箕', '巳房', '午角', '未张', '申鬼', '酉觜', '戌胃', '亥璧'];
        $xingSu = mb_substr($this->baseData['xingsu'], 0, 1);
        $ddz = $this->baseData['jinian']['d'][1];
        return in_array($ddz . $xingSu, $list);
    }
}
