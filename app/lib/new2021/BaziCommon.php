<?php
// +----------------------------------------------------------------------
// | baziCommon.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

use app\lib\bazi\BaziExt;
use app\lib\bazi\SxBase;
use calendar\Calendar;
use calendar\Ex;
use calendar\Huangli;
use calendar\plugin\WuXing;
use calendar\SolarTerm;
use calendar\traits\BaziBase;
use think\facade\Cache;

class BaziCommon
{
    use BaziBase;

    /**
     * @var Calendar|null
     */
    protected ?Calendar $calendar = null;

    /**
     * 根据天干+地支或天干+天干的获得原局十神名
     * @param string $str 天干+地支或天干+天干 如 丁甲
     * @return string
     */
    public function getGodName(string $str): string
    {
        $res = '';
        $list = [
            '正印' => [
                '丁甲', '丙乙', '己丙', '戊丁', '辛戊', '庚己', '癸庚', '壬辛', '乙壬', '甲癸',
                '甲子', '庚丑', '丁寅', '丙卯', '辛辰', '己巳', '戊午', '庚未', '癸申', '壬酉', '辛戌', '乙亥',
            ],
            '偏印' => [
                '丙甲', '丁乙', '戊丙', '己丁', '庚戊', '辛己', '壬庚', '癸辛', '甲壬', '乙癸',
                '乙子', '辛丑', '丙寅', '丁卯', '庚辰', '戊巳', '己午', '辛未', '壬申', '癸酉', '庚戌', '甲亥',
            ],
            '正官' => [
                '己甲', '戊乙', '辛丙', '庚丁', '癸戊', '壬己', '乙庚', '甲辛', '丁壬', '丙癸',
                '丙子', '壬丑', '己寅', '戊卯', '癸辰', '辛巳', '庚午', '壬未', '乙申', '甲酉', '癸戌', '丁亥',
            ],
            '七杀' => [
                '戊甲', '己乙', '庚丙', '辛丁', '壬戊', '癸己', '甲庚', '乙辛', '丙壬', '丁癸',
                '丁子', '癸丑', '戊寅', '己卯', '壬辰', '庚巳', '辛午', '癸未', '甲申', '乙酉', '壬戌', '丙亥',
            ],
            '正财' => [
                '辛甲', '庚乙', '癸丙', '壬丁', '乙戊', '甲己', '丁庚', '丙辛', '己壬', '戊癸',
                '戊子', '甲丑', '辛寅', '庚卯', '乙辰', '癸巳', '壬午', '甲未', '丁申', '丙酉', '乙戌', '己亥',
            ],
            '偏财' => [
                '庚甲', '辛乙', '壬丙', '癸丁', '甲戊', '乙己', '丙庚', '丁辛', '戊壬', '己癸',
                '己子', '乙丑', '庚寅', '辛卯', '甲辰', '壬巳', '癸午', '乙未', '丙申', '丁酉', '甲戌', '戊亥',
            ],
            '伤官' => [
                '癸甲', '壬乙', '乙丙', '甲丁', '丁戊', '丙己', '己庚', '戊辛', '辛壬', '庚癸',
                '庚子', '丙丑', '癸寅', '壬卯', '丁辰', '乙巳', '甲午', '丙未', '己申', '戊酉', '丁戌', '辛亥',
            ],
            '食神' => [
                '壬甲', '癸乙', '甲丙', '乙丁', '丙戊', '丁己', '戊庚', '己辛', '庚壬', '辛癸',
                '辛子', '丁丑', '壬寅', '癸卯', '丙辰', '甲巳', '乙午', '丁未', '戊申', '己酉', '丙戌', '庚亥',
            ],
            '劫财' => [
                '乙甲', '甲乙', '丁丙', '丙丁', '己戊', '戊己', '辛庚', '庚辛', '癸壬', '壬癸',
                '壬子', '戊丑', '乙寅', '甲卯', '己辰', '丁巳', '丙午', '戊未', '辛申', '庚酉', '己戌', '癸亥',
            ],
            '比肩' => [
                '甲甲', '乙乙', '丙丙', '丁丁', '戊戊', '己己', '庚庚', '辛辛', '壬壬', '癸癸',
                '癸子', '己丑', '甲寅', '乙卯', '戊辰', '丙巳', '丁午', '己未', '庚申', '辛酉', '戊戌', '壬亥',
            ],
        ];
        foreach ($list as $k => $v) {
            if (in_array($str, $v)) {
                $res = $k;
                break;
            }
        }
        return $res;
    }

    /**
     * 根据天干+天干或地支+地支获得合化关系
     * @param string $string
     * @return string
     */
    public function getHehua(string $string): string
    {
        $listHehua = [
            '土' => ['甲己', '己甲', '子丑', '丑子', '午未', '未午'],
            '金' => ['乙庚', '庚乙', '辰酉', '酉辰'],
            '水' => ['丙辛', '辛丙', '巳申', '申巳'],
            '木' => ['丁壬', '壬丁', '寅亥', '亥寅'],
            '火' => ['戊癸', '癸戊', '卯戌', '戌卯'],
        ];
        $res = '';
        foreach ($listHehua as $k => $v) {
            if (in_array($string, $v)) {
                $res = $k;
                break;
            }
        }
        return $res;
    }

    /**
     * 天干或地支相克
     * @param string $str 天干+天干或地支+地支 如(甲戊,亥巳)
     * @return bool
     */
    public function getXianKe(string $str): bool
    {
        $list = [
            '甲戊', '乙己', '丙庚', '丁辛', '戊壬', '己癸', '庚甲', '辛乙', '壬丙', '癸丁',
            '亥巳', '巳申', '申寅', '子巳', '午申', '酉寅', '亥午', '巳酉', '申卯', '子午',
            '午酉', '酉卯', '辰亥', '戌亥', '丑亥', '未亥', '辰子', '戌子', '丑子', '未子',
            '寅辰', '寅戌', '寅丑', '寅未', '卯辰', '卯戌', '卯丑', '卯未',
        ];
        return in_array($str, $list);
    }

    /**
     * 根据天干+天干或地支+地支来判断是否相冲
     * @param string $str 天干+天干或地支+地支
     * @return bool
     */
    public function getXianChong(string $str): bool
    {
        $list = [
            '甲庚', '乙辛', '丙壬', '丁癸', '庚甲', '辛乙', '壬丙', '癸丁',
            '子午', '丑未', '寅申', '卯酉', '辰戌', '巳亥', '午子', '未丑', '申寅', '酉卯', '戌辰', '亥巳',
        ];
        return in_array($str, $list);
    }

    /**
     * 地支三合或三会
     * @param array $arr 地支数组 二维
     * @param int $bool 0三合 1 三会
     * @return array
     */
    public function getSanHeDz(array $arr, int $bool = 0): array
    {
        $result = $this->getSanHeDz2($arr, $bool);
        $result3 = $result[3];
        $result2 = $result[2];
        if (!empty($result3)) {
            return array_values($result3);
        }
        return array_values($result2);
    }

    /**
     * 地支三合或三会 包含 全和半
     * @param array $arr 地支数组 二维
     * @param int $bool 0三合 1 三会
     * @return array[]
     */
    public function getSanHeDz2(array $arr, int $bool = 0): array
    {
        $listSanHe = ['水' => ['子', '辰', '申'], '金' => ['丑', '巳', '酉'], '火' => ['寅', '午', '戌'], '木' => ['卯', '亥', '未']];
        $listSanHui = ['水' => ['子', '丑', '亥'], '木' => ['寅', '卯', '辰'], '火' => ['巳', '午', '未'], '金' => ['申', '酉', '戌']];
        $list = $bool ? $listSanHui : $listSanHe;
        $name = $bool ? '三会' : '三合';
        $result3 = [];
        $result2 = [];
        foreach ($arr as $v) {
            foreach ($list as $k1 => $v1) {
                $tmp = array_unique(array_intersect($v1, $v));
                $num = count($tmp);
                $str = implode('', $tmp);
                switch ($num) {
                    case 3:
                        $result3[$str] = [
                            'title' => '地支' . $str . $name . '成局',
                            'gz' => $str,
                            'name' => $name,
                            'wx' => $k1,
                        ];
                        break;
                    case 2:
                        $result2[$str] = [
                            'title' => '地支' . $str . '半' . $name,
                            'gz' => $str,
                            'name' => '半' . $name,
                            'wx' => $k1,
                        ];
                        break;
                }
            }
        }
        return [
            3 => $result3,
            2 => $result2,
        ];
    }

    /**
     * 地支相破
     * @param string $str 地支+地支
     * @return bool
     */
    public function getXianPo(string $str): bool
    {
        $list = [
            '子酉', '丑辰', '寅亥', '卯午', '辰丑', '巳申', '午卯', '未戌', '申巳', '酉子', '戌未', '亥寅',
            '酉子', '辰丑', '亥寅', '午卯', '丑辰', '申巳', '卯午', '戌未', '巳申', '子酉', '未戌', '寅亥',
        ];
        return in_array($str, $list);
    }

    /**
     * 地支相害
     * @param string $str 地支+地支
     * @return bool
     */
    public function getXianHai(string $str): bool
    {
        $list = ['子未', '丑午', '寅巳', '卯辰', '申亥', '酉戌', '未子', '午丑', '巳寅', '辰卯', '亥申', '戌酉'];
        return in_array($str, $list);
    }

    /**
     * 地支拌合
     * @param string $str 地支+地支
     * @return bool
     */
    public function getBanHeDz(string $str): bool
    {
        $list = [
            '申子', '子辰', '亥卯', '卯未', '寅午', '午戌', '巳酉', '酉丑',
            '子申', '辰子', '卯亥', '未卯', '午寅', '戌午', '酉巳', '丑酉',
        ];
        return in_array($str, $list);
    }

    /**
     * 地支拱合
     * @param string $str
     * @return bool
     */
    public function getGongHeDz(string $str): bool
    {
        $list = ['亥未', '寅戌', '巳丑', '申辰', '未亥', '戌寅', '丑巳', '辰申'];
        return in_array($str, $list);
    }

    /**
     * 地支拱会
     * @param string $str
     * @return bool
     */
    public function getGongHuiDz(string $str): bool
    {
        $list = ['寅辰', '巳未', '申戌', '亥丑', '辰寅', '未巳', '戌申', '丑亥'];
        return in_array($str, $list);
    }

    /**
     * 地支相刑
     * @param string $str 地支+地支
     * @return bool
     */
    public function getXianXin(string $str): bool
    {
        $list = [
            '子卯', '卯子', '未丑', '丑未', '丑戌', '戌丑', '戌未', '未戌', '寅巳', '巳寅', '寅申', '申寅', '申巳', '巳申', '辰辰', '午午', '酉酉', '亥亥',
        ];
        return in_array($str, $list);
    }

    /**
     * 地支三刑
     * @param string $dz 地支
     * @param string $dz2 地支
     * @param string $dz3 地支
     * @return bool
     */
    public function getSanXin(string $dz, string $dz2, string $dz3): bool
    {
        $list = [
            ['丑', '戌', '未'],
            ['寅', '巳', '申'],
        ];
        $res = false;
        $arr = [$dz, $dz2, $dz3];
        foreach ($list as $v) {
            $tmp = array_unique(array_intersect($v, $arr));
            $num = count($tmp);
            $str = implode('', $arr);
            if ($num == 3) {
                $res = true;
                break;
            }
        }
        return $res;
    }

    /**
     * 天干或地支争合(三个天干或地支成合，即出现两个争相合的一个局面)
     * @param string $string 三个天干或三个地支
     * @return bool
     */
    public function getZhengHe(string $string): bool
    {
        $list = [
            '甲己甲', '乙庚乙', '丙辛丙', '丁壬丁', '戊癸戊', '己甲己', '庚乙庚', '辛丙辛', '壬丁壬', '癸戊癸',
            '子丑子', '丑子丑', '寅亥寅', '卯戌卯', '辰酉辰', '巳申巳', '午未午', '未午未', '申巳申', '酉辰酉', '戌卯戌', '亥寅亥',
        ];
        return in_array($string, $list);
    }

    /**
     * 天干或地支暗合(天干+天干或地支+地支)
     * @param string $string 天干+天干或地支+地支 天干+地支
     * @return string
     */
    public function getAnHe(string $string): string
    {
        $list = [
            '通禄合' => ['丁亥', '戊子', '辛巳', '壬午', '癸巳', '甲午'],
            '余刃合' => ['丙戌', '庚辰'],
            '通禄暗合' => ['子巳', '寅午', '巳子', '巳酉', '午寅', '酉巳'],
            '通合' => ['丑寅', '寅丑'],
            '通合、通禄暗合' => ['卯申', '午亥', '申卯', '亥午'],
        ];
        $res = '';
        foreach ($list as $k => $v) {
            if (in_array($string, $v)) {
                $res = $k;
                break;
            }
        }
        return $res;
    }

    /**
     * 妒合(三个天干)
     * @param string $string 三个天干
     * @return bool
     */
    public function getDuHe(string $string): bool
    {
        $list = [
            '甲己乙', '丙辛丁', '戊癸己', '庚乙辛', '壬丁癸', '乙己甲', '丁辛丙', '己癸戊', '辛乙庚', '癸丁壬',
        ];
        return in_array($string, $list);
    }

    /**
     * 两柱干支是否鸳鸯合
     * @param string $str 同柱干支
     * @param string $str1 同柱干支
     * @return bool
     */
    public function getYuanYangHe(string $str, string $str1): bool
    {
        $list = [
            ['甲子', '己丑'], ['甲寅', '己亥'], ['甲辰', '己酉'], ['甲午', '己未'], ['甲申', '己巳'], ['甲戌', '己卯'],
            ['乙丑', '庚子'], ['乙卯', '庚寅'], ['乙巳', '庚辰'], ['乙未', '庚午'], ['乙酉', '庚申'], ['乙亥', '庚戌'],
            ['丙子', '辛丑'], ['丙寅', '辛亥'], ['丙辰', '辛酉'], ['丙午', '辛未'], ['丙申', '辛巳'], ['丙戌', '辛卯'],
            ['丁丑', '壬子'], ['丁卯', '壬寅'], ['丁巳', '壬辰'], ['丁未', '壬午'], ['丁酉', '壬申'], ['丁亥', '壬戌'],
            ['戊子', '癸丑'], ['戊寅', '癸亥'], ['戊辰', '癸酉'], ['戊午', '癸未'], ['戊申', '癸巳'], ['戊戌', '癸卯'],
        ];
        $bool = false;
        $string = $str . $str1;
        foreach ($list as $k => $v) {
            $tmp = [$v[0] . $v[1], $v[1] . $v[0]];
            if (!in_array($string, $tmp)) {
                continue;
            }
            $bool = true;
            break;
        }
        return $bool;
    }

    /**
     * 根据年支+月支或月支+日支或日支+时支判断是否闸合
     * @param string $str 年支+月支或月支+日支或日支+时支
     * @return bool
     */
    public function getZhaHe(string $str): bool
    {
        $list = [
            '寅戌', '亥未', '申辰', '巳丑',
        ];
        return in_array($str, $list);
    }

    /**
     * 八字合化和相克
     * @param array $jiNian
     * @return array[]
     */
    public function getBaziHeKe(array $jiNian): array
    {
        $hehuaTg = [];
        $hehuaDz = [];
        $keyList = [];// 键名缓存
        $tgKe = [];
        $dzKe = [];
        foreach ($jiNian as $k => $v) {
            $tgList[] = $v[0];
            $dzList[] = $v[1];
            foreach ($jiNian as $k1 => $v1) {
                $tmpTg = $v[0] . $v1[0];
                $tmpDz = $v[1] . $v1[1];
                if ($k == $k1) {
                    continue;
                }
                $tmpArr = [$k, $k1];
                sort($tmpArr);
                $tmpStr = implode('', $tmpArr);
                // 地支相克
                if ($this->getXianKe($tmpDz)) {
                    $dzKe[$tmpDz] = $tmpDz;
                }
                // 地支相克结束
                if (in_array($tmpStr, $keyList)) {
                    continue;
                }
                $keyList[] = $tmpStr;
                // 天干相克
                if ($this->getXianKe($tmpTg)) {
                    $tgKe[$tmpTg] = $tmpTg;
                }
                // 天干相克结束
                // 合化开始
                $tmpTgHeHua = $this->getHehua($tmpTg);
                if (!empty($tmpTgHeHua)) {
                    $hehuaTg[$tmpTg] = $tmpTg . '合化' . $tmpTgHeHua;
                }
                $tmpDzHeHua = $this->getHehua($tmpDz);
                if (!empty($tmpDzHeHua)) {
                    $hehuaDz[$tmpDz] = $tmpDz . '合化' . $tmpDzHeHua;
                }
                // 合化结束
            }
        }
        return [
            'hehua' => [
                't' => array_values($hehuaTg),
                'd' => array_values($hehuaDz),
            ],
            'xianke' => [
                't' => array_values($tgKe),
                'd' => array_values($dzKe),
            ],

        ];
    }

    /**
     * 八字相冲
     * @param array $jiNian 八字
     * @param string $actionKey 要取数据 anhe,chong,po,hai,xin
     * @return array[]|false
     */
    public function getBaziAct(array $jiNian, string $actionKey)
    {
        $list = [
            'chong' => 'getXianChong',
            'anhe' => 'getAnHe',
            'po' => 'getXianPo',
            'hai' => 'getXianHai',
            'xin' => 'getXianXin',
        ];
        if (!isset($list[$actionKey])) {
            return false;
        }
        $action = $list[$actionKey];
        $tgRes = [];
        $dzRes = [];
        $keyList = [];
        foreach ($jiNian as $k => $v) {
            foreach ($jiNian as $k1 => $v1) {
                $tmpTg = $v[0] . $v1[0];
                $tmpDz = $v[1] . $v1[1];
                if ($k == $k1) {
                    continue;
                }
                $tmpArr = [$k, $k1];
                sort($tmpArr);
                $tmpStr = implode('', $tmpArr);
                if (in_array($tmpStr, $keyList)) {
                    continue;
                }
                $keyList[] = $tmpStr;
                $tmpRes = $this->$action($tmpTg);
                if ($tmpRes) {
                    $tmpData = $tmpTg;
                    if (is_string($tmpRes)) {
                        $tmpData = $tmpTg . $tmpRes;
                    }
                    $tgRes[$tmpData] = $tmpData;
                }
                $tmpRes1 = $this->$action($tmpDz);
                if ($tmpRes1) {
                    $tmpData1 = $tmpDz;
                    if (is_string($tmpRes1)) {
                        $tmpData1 = $tmpDz . $tmpRes1;
                    }
                    $dzRes[$tmpData1] = $tmpData1;
                }
            }
        }
        return [
            't' => array_values($tgRes),
            'd' => array_values($dzRes),
        ];
    }

    /**
     * 地支六合
     * @param string $str 两个地支组成的字符串
     * @return bool
     */
    public function liuHeDz(string $str): bool
    {
        $list = [
            '子丑', '丑子', '寅亥', '卯戌', '辰酉', '巳申', '午未', '未午', '申巳', '酉辰', '戌卯', '亥寅',
        ];
        return in_array($str, $list);
    }

    /**
     * 天干相合
     * @param string $str 两个天干
     * @return bool
     */
    public function xianHeTg(string $str): bool
    {
        $list = [
            '甲己', '乙庚', '丙辛', '丁壬', '戊癸', '己甲', '庚乙', '辛丙', '壬丁', '癸戊',
        ];
        return in_array($str, $list);
    }

    /**
     * 获得藏干
     * @param string $dz 地支
     * @param string $tg 天干
     * @return array
     */
    public function getHideGod(string $dz, string $tg): array
    {
        $hideData = [
            '子' => '癸', '丑' => '己癸辛', '寅' => '甲丙戊', '卯' => '乙', '辰' => '戊乙癸',
            '巳' => '丙庚戊', '午' => '丁己', '未' => '己丁乙', '申' => '庚壬戊', '酉' => '辛',
            '戌' => '戊辛丁', '亥' => '壬甲',
        ];
        $str = $hideData[$dz];
        preg_match_all('/./u', $str, $arr);
        $result = [];
        foreach ($arr[0] as $val) {
            $result['hide'][] = $val;
            $result['god'][] = $this->getGodNameByTg($tg, $val);
        }
        return $result;
    }

    /**
     * 身宫
     * @param array $jiNian 八字数据
     * @param int $m 农历数字
     * @return string
     */
    public function getShenGong(array $jiNian, int $m): string
    {
        $dz = Calendar::DI_ZHI;
        array_unshift($dz, '');
        $lists = [
            '寅' => ['丙寅', '戊寅', '庚寅', '壬寅', '甲寅'],
            '卯' => ['丁卯', '己卯', '辛卯', '癸卯', '乙卯'],
            '辰' => ['戊辰', '庚辰', '壬辰', '甲辰', '丙辰'],
            '巳' => ['己巳', '辛巳', '癸巳', '乙巳', '丁巳'],
            '午' => ['庚午', '壬午', '甲午', '丙午', '戊午'],
            '未' => ['辛未', '癸未', '乙未', '丁未', '己未'],
            '申' => ['壬申', '甲申', '丙申', '戊申', '庚申'],
            '酉' => ['癸酉', '乙酉', '丁酉', '己酉', '辛酉'],
            '戌' => ['甲戌', '丙戌', '戊戌', '庚戌', '壬戌'],
            '亥' => ['乙亥', '丁亥', '己亥', '辛亥', '癸亥'],
            '子' => ['丙子', '戊子', '庚子', '壬子', '甲子'],
            '丑' => ['丁丑', '己丑', '辛丑', '癸丑', '乙丑'],
        ];
        $start = $m;
        $hIndex = array_search($jiNian['h'][1], $dz);
        $dIndex = 0;
        if ($hIndex > 10) {
            $dIndex = $hIndex;
        } else {
            $dIndex = 10 - $hIndex;
        }
        $sIndex = ($start - $dIndex + 12) % 12;
        $sdz = $dz[$sIndex];
        if ($sIndex == 0) {
            $sdz = '亥';
        }
        $tg = $jiNian['y'][0];
        $tgIndex = 0;
        if (in_array($tg, ['甲', '己'])) {
            $tgIndex = 0;
        } elseif (in_array($tg, ['乙', '庚'])) {
            $tgIndex = 1;
        } elseif (in_array($tg, ['丙', '辛'])) {
            $tgIndex = 2;
        } elseif (in_array($tg, ['丁', '壬'])) {
            $tgIndex = 3;
        } else {
            $tgIndex = 4;
        }
        return $lists[$sdz][$tgIndex];
    }

    /**
     * 判断是否是阴差阳错
     * @param string $str 日柱干支
     * @return bool
     */
    public function getYinCha(string $str): bool
    {
        $list = ['丙午', '丙子', '丁未', '丁丑', '戊申', '戊寅', '辛酉', '辛卯', '壬戌', '壬辰', '癸巳', '癸亥'];
        return in_array($str, $list);
    }

    /**
     * 获得正冲相关信息
     * @param string $rgz 干支
     * @return array
     */
    public function getChongBygz(string $rgz): array
    {
        $list = [
            '甲子' => '庚午', '甲戌' => '庚辰', '甲申' => '庚寅', '甲午' => '庚子', '甲辰' => '庚戌', '甲寅' => '庚申',
            '乙丑' => '辛未', '乙亥' => '辛巳', '乙酉' => '己卯', '乙未' => '辛丑', '乙巳' => '辛亥', '乙卯' => '辛酉',
            '丙寅' => '壬申', '丙子' => '壬午', '丙戌' => '壬辰', '丙申' => '壬寅', '丙午' => '壬子', '丙辰' => '壬戌',
            '丁卯' => '癸酉', '丁丑' => '癸未', '丁亥' => '癸巳', '丁酉' => '癸卯', '丁未' => '癸丑', '丁巳' => '癸亥',
            '戊辰' => '甲戌', '戊寅' => '甲申', '戊子' => '甲午', '戊戌' => '甲辰', '戊申' => '甲寅', '戊午' => '甲子',
            '己巳' => '乙亥', '己卯' => '乙酉', '己丑' => '乙未', '己亥' => '乙巳', '己酉' => '乙卯', '己未' => '乙丑',
            '庚午' => '丙子', '庚辰' => '丙戌', '庚寅' => '丙申', '庚子' => '丙午', '庚戌' => '丙辰', '庚申' => '丙寅',
            '辛未' => '丁丑', '辛巳' => '丁亥', '辛卯' => '丁酉', '辛丑' => '丁未', '辛亥' => '丁巳', '辛酉' => '丁卯',
            '壬申' => '戊寅', '壬午' => '戊子', '壬辰' => '戊戌', '壬寅' => '戊申', '壬子' => '戊午', '壬戌' => '戊辰',
            '癸酉' => '己卯', '癸未' => '己丑', '癸巳' => '己亥', '癸卯' => '己酉', '癸丑' => '己未', '癸亥' => '己巳',
        ];
        $list1 = [
            '甲子' => '1924、1984', '乙丑' => '1925、1985', '丙寅' => '1926、1986', '丁卯' => '1927、1987',
            '戊辰' => '1928、1988', '己巳' => '1929、1989', '庚午' => '1930、1990', '辛未' => '1931、1991',
            '壬申' => '1932、1992', '癸酉' => '1933、1993', '甲戌' => '1934、1994', '乙亥' => '1935、1995',
            '丙子' => '1936、1996', '丁丑' => '1937、1997', '戊寅' => '1938、1998', '己卯' => '1939、1999',
            '庚辰' => '1940、2000', '辛巳' => '1941、2001', '壬午' => '1942、2002', '癸未' => '1943、2003',
            '甲申' => '1944、2004', '乙酉' => '1945、2005', '丙戌' => '1946、2006', '丁亥' => '1947、2007',
            '戊子' => '1948、2008', '己丑' => '1949、2009', '庚寅' => '1950、2010', '辛卯' => '1951、2011',
            '壬辰' => '1952、2012', '癸巳' => '1953、2013', '甲午' => '1954、2014', '乙未' => '1955、2015',
            '丙申' => '1956、2016', '丁酉' => '1957、2017', '戊戌' => '1958、2018', '己亥' => '1959、2019',
            '庚子' => '1960、2020', '辛丑' => '1961、2021', '壬寅' => '1962、2022', '癸卯' => '1963、2023',
            '甲辰' => '1964、2024', '乙巳' => '1965、2025', '丙午' => '1966、2026', '丁未' => '1967、2027',
            '戊申' => '1968、2028', '己酉' => '1969、2029', '庚戌' => '1970、2030', '辛亥' => '1971、2031',
            '壬子' => '1972、2032', '癸丑' => '1973、2033', '甲寅' => '1974、2034', '乙卯' => '1975、2035',
            '丙辰' => '1976、2036', '丁巳' => '1977、2037', '戊午' => '1978、2038', '己未' => '1979、2039',
            '庚申' => '1980、2040', '辛酉' => '1981、2041', '壬戌' => '1982、2042', '癸亥' => '1983、2043',
        ];
        $key = $list[$rgz] ?? '丙午';
        $dz = mb_substr($key, 1, 1);
        $sxBase = new SxBase();
        return [
            'gz' => $key,
            'sx' => $sxBase->getsxByDz($dz),
            'year' => $list1[$key],
        ];
    }

    /**
     * 根据干支获得排盘数据
     * @param array $gz
     * @param array $jiNian
     * @return array
     */
    public function getBasePan(array $gz, array $jiNian): array
    {
        $dtg = $jiNian['d'][0];
        $str = implode('', $gz);
        $shaShenLib = new ShaShen();
        $terrainArr = Ex::getTerrainData();
        return [
            // 干支
            'gz' => $gz,
            'gz1' => [
                BaziExt::getYinYang($gz[0]) ? '阳' : '阴',
                BaziExt::getYinYang($gz[1]) ? '阳' : '阴',
            ],
            // 天干十神
            'god' => $this->getGodName($dtg . $gz[0]),
            // 地支主气十神
            'godz' => $this->getGodName($dtg . $gz[1]),
            // 地支主气十神
            'god2' => $this->getGodName($dtg . $gz[1]),
            // 藏干
            '_god' => $this->getHideGod($gz[1], $dtg),
            // 纳音
            'na_yin' => $this->getNaYinByGz($str),
            // 十二长生(地势)
            'terrain' => $terrainArr[$dtg . $gz[1]] ?? '',
            // 自坐(十二长生)
            'zi_zuo' => $terrainArr[$str],
            // 空亡
            'kongwang' => Huangli::getKongWangbyGz($str),
            // 神煞
            'shensha' => $shaShenLib->liuNianToBazi($gz, $jiNian),
        ];
    }

    /**
     * 获得流年数据
     * @param int $year
     * @return array
     */
    public function getLiuNiangz(int $year): array
    {
        $gz = BaziExt::getGanZhi($year);
        $result = [
            'year' => $year,
            'gz' => $gz,
        ];
        $yearTg = $gz[0];
        // 天干
        $tianGan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        // 地支
        $diZhi = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
        if ($yearTg === '甲' || $yearTg === '己') {
            $index = 2;
        } elseif ($yearTg === '乙' || $yearTg === '庚') {
            $index = 4;
        } elseif ($yearTg === '丙' || $yearTg === '辛') {
            $index = 6;
        } elseif ($yearTg === '丁' || $yearTg === '壬') {
            $index = 8;
        } else {
            $index = 0;
        }
        $list = [];
        $monthList = $this->getGongliByJieQi($year);
        foreach ($diZhi as $k => $v) {
            $tgK = ($index + $k) % 10;
            $tmpJq = $monthList[$k];
            $list[($k + 1)] = [
                'm' => ($k + 1),
                'gz' => [$tianGan[$tgK], $v],
                'start' => $tmpJq[0],
                'mid' => $tmpJq[1],
                'end' => $tmpJq[2],
                'jqt' => date('n/j', strtotime($tmpJq[0][1])),
            ];
        }
        $result['m'] = $list;
        return $result;
    }

    /**
     * 按照节气划分月份
     * @param int $year
     * @return array
     */
    public function getGongliByJieQi(int $year): array
    {
        $cacheKeyName = "bazi_ex/getGongliByJieQi/{$year}";
        $result = Cache::get($cacheKeyName, false);
        if (false !== $result) {
            return $result;
        }
        $jieqi = SolarTerm::getAllJieQi($year);
        $jieqi2 = SolarTerm::getAllJieQi($year + 1);
        $result = [];
        $m = 0;
        $TeamArr = SolarTerm::SOLAR_TERM;
        foreach ($TeamArr as $k => $v) {
            $ii = $k % 2;
            if ($ii == 0) {
                $m++;
            }
            $gl = $k > 21 ? $jieqi2[$v] : $jieqi[$v];
            $result[$m][] = [$v, $gl, $this->getNongliNum($gl)];

            if ($ii == 1) {
                $k1 = $k + 1;
                if ($k1 > 23) {
                    $jqName = '立春';
                    $gl = $jieqi2[$jqName];
                } else {
                    $jqName = $TeamArr[$k1];
                    $gl = $k1 > 21 ? $jieqi2[$jqName] : $jieqi[$jqName];
                }
                $result[$m][] = [$jqName, $gl, $this->getNongliNum($gl)];
            }
        }
        $result = array_values($result);
        Cache::set($cacheKeyName, $result, 600);
        return $result;
    }

    /**
     * 获得五行之间的关系
     * @param string $wx
     * @param string $wx1
     * @return string
     */
    public function getWuxingGuanXi(string $wx, string $wx1): string
    {
        return WuXing::getWuxingGuanXi($wx, $wx1);
    }

    /**
     * 获得五行相应关系对应的五行
     * @param string $wx 五行
     * @param string $gx 相互关系
     * @return string
     */
    public function getWxGuanxi(string $wx, string $gx = '生'): string
    {
        return WuXing::getWxGuanxi($wx, $gx);
    }

    /**
     * 获得原局十神
     * @param array $jiNian
     * @return array
     */
    public function getGod2(array $jiNian): array
    {
        return [
            'year' => $this->getGodName($jiNian['d'][0] . $jiNian['y'][1]),
            'month' => $this->getGodName($jiNian['d'][0] . $jiNian['m'][1]),
            'day' => $this->getGodName($jiNian['d'][0] . $jiNian['d'][1]),
            'hour' => $this->getGodName($jiNian['d'][0] . $jiNian['h'][1]),
        ];
    }

    /**
     * 获得农历信息
     * @param string $time
     * @return string
     */
    public function getNongliNum(string $time = ''): string
    {
        try {
            $calendar = $this->getCalendar();
            $t = strtotime($time);
            $y = (int)date('Y', $t);
            $m = (int)date('m', $t);
            $d = (int)date('d', $t);
            $res = $calendar->solar2Lunar($y, $m, $d);
            $is = $res[3] ? '闰' : '';
            return "{$res[0]}-{$is}{$res[1]}-{$res[2]}";
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 建禄
     * @param string $str 天干+地支
     * @return bool
     */
    public function checkJianLu(string $str): bool
    {
        $list = ['甲寅', '乙卯', '丙巳', '丁午', '戊巳', '己午', '庚申', '辛酉', '壬亥', '癸子'];
        return in_array($str, $list);
    }

    /**
     * 阳刃
     * @param string $dtg 日干
     * @param string $mdz 月支
     * @return bool
     */
    public function checkYangRen(string $dtg, string $mdz): bool
    {
        // 日干	月支
        $yueList = ['甲卯', '丙巳', '戊巳', '庚酉', '壬子'];
        return in_array($dtg . $mdz, $yueList);
    }

    /**
     * 根据地支获得相冲地支
     * @param string $dz
     * @return string
     */
    public function getChongDz(string $dz): string
    {
        $list = [
            '子' => '午', '丑' => '未', '寅' => '申', '卯' => '酉', '辰' => '戌', '巳' => '亥',
            '午' => '子', '未' => '丑', '申' => '寅', '酉' => '卯', '戌' => '辰', '亥' => '巳',
        ];
        return $list[$dz] ?? '';
    }

    /**
     * 根据地支获得相刑地支
     * @param string $dz
     * @return array
     */
    public function getXingByDz(string $dz): array
    {
        if (in_array($dz, ['辰', '午', '酉', '亥'])) {
            return [$dz];
        }
        $list = [['子', '卯'], ['寅', '巳', '申'], ['丑', '未', '戌']];
        $result = [];
        foreach ($list as $v) {
            if (in_array($dz, $v)) {
                foreach ($v as $v1) {
                    if ($v1 == $dz) {
                        continue;
                    }
                    $result[] = $v1;
                }
                break;
            }
        }
        return $result;
    }

    /**
     * 获得日历类
     * @return Calendar
     */
    private function getCalendar(): Calendar
    {
        $calendar = $this->calendar;
        if ($calendar instanceof Calendar) {
            return $calendar;
        }
        $this->calendar = new Calendar();
        return $this->calendar;
    }
}
