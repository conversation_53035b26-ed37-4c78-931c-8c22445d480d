<?php
// +----------------------------------------------------------------------
// | Feipan 飞盘
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

use calendar\Ex;
use calendar\exceptions\Exception;

class Feipan
{
    /**
     * 飞盘 根据日期获得结果
     * @param string $time
     * @return array
     * @throws Exception
     */
    public function info(string $time): array
    {
        // 年
        $list1 = [
            '上元' => [
                [1, 9, 8, 7, 6, 5, 4, 3, 2],
                ['一白', '九紫', '八白', '七赤', '六白', '五黄', '四绿', '三碧', '二黑'],
            ],
            '中元' => [
                [4, 3, 2, 1, 9, 8, 7, 6, 5],
                ['四绿', '三碧', '二黑', '一白', '九紫', '八白', '七赤', '六白', '五黄'],
            ],
            '下元' => [
                [7, 6, 5, 4, 3, 2, 1, 9, 8],
                ['七赤', '六白', '五黄', '四绿', '三碧', '二黑', '一白', '九紫', '八白'],
            ],
        ];
        // 宫飞顺序
        $listGong = [
            '中', '乾', '兑', '艮', '离', '坎', '坤', '震', '巽',
        ];

        $listJieQiArr = [
            ['冬至', '小寒', '大寒', '立春'],
            ['雨水', '惊蛰', '春分', '清明'],
            ['谷雨', '立夏', '小满', '芒种'],
            // 逆
            ['夏至', '小暑', '大暑，立秋'],
            ['处暑', '白露', '秋分', '寒露'],
            ['霜降', '立冬', '小雪', '大雪'],
        ];

        $listJieQi = [
            [1, 2, 3, 4, 5, 6, 7, 8, 9],
            [7, 8, 9, 1, 2, 3, 4, 5, 6],
            [4, 5, 6, 7, 8, 9, 1, 2, 3],
            [9, 8, 7, 6, 5, 4, 3, 2, 1],
            [3, 2, 1, 9, 8, 7, 6, 5, 4],
            [6, 5, 4, 3, 2, 1, 9, 8, 7],
        ];
        $listMonth = [
            [8, 7, 6, 5, 4, 3, 2, 1, 9, 8, 7, 6],
            [5, 4, 3, 2, 1, 9, 8, 7, 6, 5, 4, 3],
            [2, 1, 9, 8, 7, 6, 5, 4, 3, 2, 1, 9],
        ];
        $listLiuYear = [
            '', '贪狼星', '巨门星', '禄存星', '文曲星', '廉贞星', '武曲星', '破军星', '左辅星', '右弼星',
        ];
        $listLiuYearWx = [
            '', '水', '土', '木', '木', '土', '金', '金', '土', '火',
        ];
        $lunar = Ex::date($time);
        $base = $lunar->getLunarByBetween();
        $nongliShu = $base['_nongli'];
        $year = $lunar->dateTime->format('Y');
        if ($year >= 1864 && $year <= 1923) {
            $yuan = '上元';
        } elseif ($year >= 1924 && $year <= 1983) {
            $yuan = '中元';
        } else {
            $yuan = '下元';
        }
        $jiNian = $base['jinian'];
        $jiNainYearStr = implode('', $jiNian['y']);
        $jiNianDayStr = implode('', $jiNian['d']);
        $YearKey = $this->getKeyByJiNian($jiNainYearStr);
        $dayKey = $this->getKeyByJiNian($jiNianDayStr);
        $listYuan = $list1[$yuan];
        $startYearNum = $listYuan[0][$YearKey];
        // 年
        $result = [];
        $listPos = [
            '巽' => '东南', '离' => '正南', '坤' => '西南',
            '震' => '正东', '中' => '正中', '兑' => '正西',
            '艮' => '东北', '坎' => '正北', '乾' => '西北',
        ];
        $listPosNum = [
            '巽' => 1, '离' => 2, '坤' => 3,
            '震' => 4, '中' => 5, '兑' => 6,
            '艮' => 7, '坎' => 8, '乾' => 9,
        ];
        foreach ($listGong as $k => $v) {
            $tmp = ($startYearNum + $k) % 9;
            $result[$v]['name'] = $v;
            $result[$v]['pos'] = $listPos[$v];
            $result[$v]['pos_num'] = $listPosNum[$v];
            $num = $tmp ?: 9;
            $result[$v]['y'] = $num;
            $key = array_search($num, $listYuan[0]);
            $result[$v]['y_title'] = $listYuan[1][$key];
            $result[$v]['y_star'] = $listLiuYear[$num];
            $result[$v]['y_wx'] = $listLiuYearWx[$num];
        }
        if (in_array($jiNian['y'][1], ['子', '午', '卯', '酉'])) {
            $monthKey = $listMonth[0][$nongliShu['m'] - 1];
        } elseif (in_array($jiNian['y'][1], ['辰', '戌', '丑', '未'])) {
            $monthKey = $listMonth[1][$nongliShu['m'] - 1];
        } else {
            $monthKey = $listMonth[2][$nongliShu['m'] - 1];
        }
        foreach ($listGong as $k => $v) {
            $tmp = ($monthKey + $k) % 9;
            $num = $tmp ?: 9;
            $result[$v]['m'] = $tmp ?: 9;
            $key = array_search($num, $listYuan[0]);
            $result[$v]['m_title'] = $listYuan[1][$key];
            $result[$v]['m_star'] = $listLiuYear[$num];
            $result[$v]['m_wx'] = $listLiuYearWx[$num];
        }
        $jieqiArr = $lunar->getJieQiCur();
        $jieqi = $jieqiArr['current'][0];
        $jieqiKey = 0;
        foreach ($listJieQiArr as $k => $v) {
            if (in_array($jieqi, $v)) {
                $jieqiKey = $k;
            }
        }
        // 顺 1顺 0逆
        $isShun = $jieqiKey < 3 ? 1 : 0;
        // $startDayNum = $listJieQi[$jieqiKey][$dayKey];
        foreach ($listGong as $k => $v) {
            if ($isShun) {
                $tmp = ($dayKey + $k) % 9;
            } else {
                $tmp = ($dayKey - $k + 9) % 9;
            }
            // $tmp=$tmp ?: 9;
            $num = $listJieQi[$jieqiKey][$tmp];
            $result[$v]['d'] = $num;
            $key = array_search($num, $listYuan[0]);
            $result[$v]['d_title'] = $listYuan[1][$key];
            $result[$v]['d_star'] = $listLiuYear[$num];
            $result[$v]['d_wx'] = $listLiuYearWx[$num];
        }
        $result = array_column($result, null, 'y');
        ksort($result);
        return $result;
    }

    /**
     * 根据干支纪年获得序号
     * @param string $str
     * @return int
     */
    private function getKeyByJiNian(string $str): int
    {
        $list = [
            ['甲子', '癸酉', '壬午', '辛卯', '庚子', '己酉', '戊午'],
            ['乙丑', '甲戌', '癸未', '壬辰', '辛丑', '庚戌', '己未'],
            ['丙寅', '乙亥', '甲申', '癸巳', '壬寅', '辛亥', '庚申'],
            ['丁卯', '丙子', '乙酉', '甲午', '癸卯', '壬子', '辛酉'],
            ['戊辰', '丁丑', '丙戌', '乙未', '甲辰', '癸丑', '壬戌'],
            ['己巳', '戊寅', '丁亥', '丙申', '乙巳', '甲寅', '癸亥'],
            ['庚午', '己卯', '戊子', '丁酉', '丙午', '乙卯'],
            ['辛未', '庚辰', '己丑', '戊戌', '丁未', '丙辰'],
            ['壬申', '辛巳', '庚寅', '己亥', '戊申', '丁巳'],
        ];
        $res = 0;
        foreach ($list as $k => $v) {
            if (in_array($str, $v)) {
                $res = $k;
                break;
            }
        }
        return $res;
    }
}
