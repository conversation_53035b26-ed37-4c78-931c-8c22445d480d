<?php
// +----------------------------------------------------------------------
// | Flower.桃花
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021;

use app\lib\new2021\facade\BaziCommon;

class Flower
{
    /**
     * 桃花数组
     * @var array
     */
    protected array $taohuaArr = [
        'tian_xi' => ['子酉', '丑申', '寅未', '卯午', '辰巳', '巳辰', '午卯', '未寅', '申丑', '酉子', '戌亥', '亥戌'],
        'hong_luan' => ['子卯', '丑寅', '寅丑', '卯子', '辰亥', '巳戌', '午酉', '未申', '申未', '酉午', '戌巳', '亥辰'],
        'yu_men' => ['子酉', '丑午', '寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子'],
        'qian_wai' => ['申酉', '子酉', '辰酉', '寅卯', '午卯', '戌卯', '丑午', '巳午', '酉午', '亥子', '卯子', '未子'],
        'xian_chi' => ['子酉', '丑午', '寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子'],
        'hong_yan' => ['甲午', '乙申', '丙寅', '丁未', '戊辰', '己辰', '庚戌', '辛酉', '壬子', '癸申'],
        'dao_cha' => [
            '子亥', '子卯', '子未', '丑申', '丑子', '丑辰', '寅巳', '寅酉', '寅丑', '卯寅', '卯午', '卯戍',
            '辰亥', '辰卯', '辰未', '巳申', '巳子', '巳辰', '午巳', '午酉', '午丑', '未寅', '未午', '未戍',
            '申亥', '申卯', '申未', '酉申', '酉子', '酉辰', '戌巳', '戌酉', '戌丑', '亥寅', '亥午', '亥戍',
        ],
        'mu_yu' => ['甲子', '乙巳', '丙卯', '丁申', '戊卯', '己申', '庚午', '辛亥', '壬酉', '癸寅'],
        'ba_zhuan' => ['甲寅', '乙卯', '丁未', '己未', '庚申', '辛酉', '戊戌', '癸丑'],
        'jiu_chou' => ['戊子', '戊午', '壬子', '壬午', '乙卯', '乙酉', '辛卯', '辛酉', '己卯', '己酉'],
        'qian_nei' => ['子酉', '丑午', '寅卯', '卯子', '辰酉', '巳午', '午卯', '未子', '申酉', '酉午', '戌卯', '亥子'],
        'zheng_yuan' => [
            ['甲戊', '乙己', '丙庚', '丁辛', '戊甲', '己癸', '庚甲', '辛乙', '壬丙', '癸丁', '甲己', '乙戊', '丙辛', '丁庚', '戊癸', '己甲', '庚乙', '辛甲', '壬丁', '癸丙'],
            ['甲庚', '乙辛', '丙壬', '丁癸', '己乙', '庚丙', '辛丁', '壬戊', '癸己', '甲辛', '乙庚', '丙癸', '丁壬', '戊乙', '己甲', '庚丁', '辛丙', '壬己', '癸戊'],
        ],
    ];

    /**
     * 玉门桃花
     * @param array $jiNian
     * @return int
     */
    public function yuMen(array $jiNian): int
    {
        $list = $this->taohuaArr['yu_men'];
        $list1 = [$jiNian['d'][1] . $jiNian['y'][1], $jiNian['d'][1] . $jiNian['m'][1], $jiNian['d'][1] . $jiNian['h'][1]];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 墙外桃花
     * @param array $jiNian
     * @return bool
     */
    public function qianWai(array $jiNian): bool
    {
        // 以日支查时支
        $key = $jiNian['d'][1] . $jiNian['h'][1];
        $list = $this->taohuaArr['qian_wai'];
        return in_array($key, $list);
    }

    /**
     * 咸池桃花
     * 以年支或日支查其他地支
     * @param array $jiNian
     * @return int
     */
    public function xianChi(array $jiNian): int
    {
        $list = [$jiNian['y'][1] . $jiNian['m'][1], $jiNian['y'][1] . $jiNian['h'][1]];
        $list1 = $this->taohuaArr['xian_chi'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 红艳桃花
     * @param array $jiNian
     * @return int
     */
    public function hongYan(array $jiNian): int
    {
        $list = [$jiNian['d'][0] . $jiNian['y'][1], $jiNian['d'][0] . $jiNian['m'][1], $jiNian['d'][0] . $jiNian['d'][1], $jiNian['d'][0] . $jiNian['h'][1]];
        $list1 = $this->taohuaArr['hong_yan'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 倒插桃花
     * 以年支查月日时支（月日时需同时满足）
     */
    public function daoCha(array $jiNian): bool
    {
        $list = $this->taohuaArr['dao_cha'];
        $ydz = $jiNian['y'][1];
        $num = 0;
        foreach ($jiNian as $k => $v) {
            if ($k == 'y') {
                continue;
            }
            if (in_array($ydz . $v[1], $list)) {
                $num++;
            }
        }
        return $num == 3;
    }

    /**
     * 残枝桃花
     * @param array $jiNian
     * @return bool
     */
    public function canZhi(array $jiNian): bool
    {
        $bool = false;
        if (in_array($jiNian['d'][1], ['申', '子', '辰']) && in_array($jiNian['m'][1], ['巳', '午', '未']) && $jiNian['h'][1] == '巳') {
            $bool = true;
        } elseif (in_array($jiNian['d'][1], ['寅', '午', '戌']) && in_array($jiNian['m'][1], ['亥', '子', '丑']) && $jiNian['h'][1] == '亥') {
            $bool = true;
        } elseif (in_array($jiNian['d'][1], ['巳', '酉', '丑']) && in_array($jiNian['m'][1], ['寅', '卯', '辰']) && $jiNian['h'][1] == '寅') {
            $bool = true;
        } elseif (in_array($jiNian['d'][1], ['亥', '卯', '未']) && in_array($jiNian['m'][1], ['申', '酉', '戌']) && $jiNian['h'][1] == '申') {
            $bool = true;
        }
        return $bool;
    }

    /**
     * 沐浴桃花
     * 以日干查八字地支
     * @param array $jiNian
     * @return int
     */
    public function muYu(array $jiNian): int
    {
        $list = [$jiNian['d'][0] . $jiNian['y'][1], $jiNian['d'][0] . $jiNian['m'][1], $jiNian['d'][0] . $jiNian['d'][1], $jiNian['d'][0] . $jiNian['h'][1]];
        $list1 = $this->taohuaArr['mu_yu'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 滚浪桃花
     * 日干和时干相合，日支和时支相刑（同时满足）
     * @param array $jiNian
     * @return bool
     */
    public function gunLang(array $jiNian): bool
    {
        $str = $jiNian['d'][0] . $jiNian['h'][0];
        $str1 = $jiNian['d'][1] . $jiNian['h'][1];
        $bool = BaziCommon::xianHeTg($str);
        $bool1 = BaziCommon::getXianXin($str1);

        return $bool && $bool1;
    }

    /**
     * 遍野桃花
     * 四柱四支之中蕴含子、午、卯、酉，即遍野桃花
     * @param array $jiNian
     * @return bool
     */
    public function bianYe(array $jiNian): bool
    {
        $list = ['子', '午', '卯', '酉'];
        $bool = true;
        $list1 = [];
        foreach ($jiNian as $v) {
            $list1[$v[1]] = 1;
        }
        foreach ($list as $v) {
            if (!isset($list1[$v])) {
                $bool = false;
                break;
            }
        }
        return $bool;
    }

    /**
     * 裸体桃花
     * @param array $jiNian
     * @return bool
     */
    public function luoti(array $jiNian): bool
    {
        $gz = implode('', $jiNian['d']);
        $list = ['甲子', '庚午', '丁卯', '癸酉'];
        return in_array($gz, $list);
    }

    /**
     * 八专桃花
     * @param array $jiNian
     * @return bool
     */
    public function baZhuan(array $jiNian): bool
    {
        $gz = implode('', $jiNian['d']);
        $list = $this->taohuaArr['ba_zhuan'];
        return in_array($gz, $list);
    }

    /**
     * 九丑桃花
     * @param array $jiNian
     * @return bool
     */
    public function jiuChou(array $jiNian): bool
    {
        $list = $this->taohuaArr['jiu_chou'];
        $gz = implode('', $jiNian['d']);
        return in_array($gz, $list);
    }

    /**
     * 墙内桃花
     * 以月支看其他地支
     * @param array $jiNian
     * @return int
     */
    public function qianNei(array $jiNian): int
    {
        $list = [$jiNian['d'][1] . $jiNian['y'][1], $jiNian['d'][1] . $jiNian['m'][1]];
        $list1 = $this->taohuaArr['qian_nei'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 红鸾桃花
     * 以年支查八字地支
     * @param array $jiNian
     * @return int
     */
    public function hongLuan(array $jiNian): int
    {
        $list = [$jiNian['y'][1] . $jiNian['m'][1], $jiNian['y'][1] . $jiNian['d'][1], $jiNian['y'][1] . $jiNian['h'][1]];
        $list1 = $this->taohuaArr['hong_luan'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 天喜桃花
     * 以年支查八字地支
     * @param array $jiNian
     * @return int
     */
    public function tianXi(array $jiNian): int
    {
        $list = [$jiNian['y'][1] . $jiNian['m'][1], $jiNian['y'][1] . $jiNian['d'][1], $jiNian['y'][1] . $jiNian['h'][1]];
        $list1 = $this->taohuaArr['tian_xi'];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 正缘桃花
     * @param array $jiNian 纪年
     * @param int $sex 性别
     * @return int
     */
    public function zhengYuan(array $jiNian, int $sex): int
    {
        $list = [$jiNian['d'][0] . $jiNian['y'][0], $jiNian['d'][0] . $jiNian['m'][0], $jiNian['d'][0] . $jiNian['h'][0]];
        $list1 = $this->taohuaArr['zheng_yuan'][$sex];
        return $this->getTaoHuaNum($list, $list1);
    }

    /**
     * 获得桃花数
     * @param array $list 数据
     * @param array $list1 筛选条件
     * @return int
     */
    private function getTaoHuaNum(array $list, array $list1): int
    {
        $res = array_intersect($list1, $list);
        return count($res);
    }

    /**
     * 检查桃花
     * @param string $str 地支+地支
     * @param string $key 桃花英文 tian_xi,hong_luan,yu_men,qian_wai,xian_chi,hong_yan,dao_cha,mu_yu,ba_zhuan,jiu_chou,qian_nei,zheng_yuan
     * @param int $sex
     * @return bool
     */
    public function checkTaohua(string $str, string $key = 'tian_xi', int $sex = 0): bool
    {
        $list = $this->taohuaArr[$key] ?? [];
        if ($key === 'zheng_yuan') {
            $list = $list[$sex];
        }
        return in_array($str, $list);
    }
}
