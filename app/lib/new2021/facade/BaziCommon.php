<?php
// +----------------------------------------------------------------------
// | BaziCommon 门面
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\new2021\facade;

use think\Facade;

/**
 * Class BaziCommon
 * @see \app\lib\new2021\BaziCommon
 * @package app\lib\new2021\facade
 * @mixin \app\lib\new2021\BaziCommon
 * @method static string getGodName(string $str) 根据天干+地支或天干+天干的获得原局十神名
 * @method static string getHehua(string $str) 根据天干+天干或地支+地支获得合化关系
 * @method static bool getXianKe(string $str)  天干或地支相克(如甲戊, 亥巳)
 * @method static bool getXianChong(string $str) 根据天干+天干或地支+地支来判断是否相冲
 * @method static array getSanHeDz(array $arr, int $bool = 0)) 地支三合或三会  0三合 1三会
 * @method static array getSanHeDz2(array $arr, int $bool = 0))地支三合或三会 包含 全和半  0三合 1三会
 * @method static bool getXianPo(string $str) 地支相破
 * @method static bool getXianHai(string $str) 地支相害(地支+地支)
 * @method static array getBanHeDz(string $str) 根据地支判断是否拌合
 * @method static bool getXianXin(string $str) 地支相刑(地支+地支)
 * @method static bool getSanXin(string $dz, string $dz2, string $dz3) 地支三刑(三地支)
 * @method static bool getZhengHe(string $str) 天干或地支争合(三个天干或地支成合，即出现两个争相合的一个局面)
 * @method static bool getAnHe(string $str) 天干或地支暗合(天干+天干或地支+地支)
 * @method static bool getDuHe(string $str) 妒合(三个天干)
 * @method static bool getYuanYangHe(string $str, string $str1) 两柱干支是否鸳鸯合
 * @method static bool getZhaHe(string $str) 根据年支+月支或月支+日支或日支+时支判断是否闸合
 * @method static bool liuHeDz(string $str) 地支六合
 * @method static bool xianHeTg(string $str) 天干相合
 * @method static array getHideGod(string $dz, string $tg) 获得藏干 根据地支+日干
 * @method static string getShenGong(array $jiNian, int $m) 获得身宫 八字 +农历数字
 * @method static bool getYinCha(string $str) 判断是否是阴差阳错 日柱干支
 * @method static array getChongBygz(string $str) 根据干支获得正冲相关信息
 * @method static array getLiuNiangz(int $year) 获得流年数据
 * @method static array getBasePan(array $gz, array $jiNian) 根据干支获得排盘数据
 * @method static array getGongliByJieQi(int $year) 按照节气划分月份
 * @method static string getNongliNum(string $time = '') 获得农历信息
 * @method static string getWuxingGuanXi(string $wx, string $wx1) 获得五行之间的关系
 * @method static array getGod2(array $jiNian) 获得原局十神
 * @method static string getWxGuanxi(string $wx, string $gx = '生') 获得五行相应关系对应的五行
 * @method Static bool checkJianLu(string $str) 根据天干+地支判断是否建禄
 * @method static bool checkYangRen(string $dtg, string $mdz) 根据日干+月支判断是否为阳刃
 * @method static string getChongDz(string $dz) 根据地支获得相冲地支
 * @method static array getXingByDz(string $dz) 根据地支获得相刑地支
 * @method static array getGongHeDz(string $str) 根据地支判断是否拱合
 * @method static array getGongHuiDz(string $str) 根据地支判断是否拱会
 * @method static array getBaziHeKe(array $jiNian) 获得八字的合化关系
 * @method static array getBaziAct(array $jiNian, string $type) 获得八字的相冲相破相害关系
 * @method static array getBaziSanHe(array $jiNian) 获得八字的三合关系
 */
class BaziCommon extends Facade
{
    /**
     * 获取当前Facade对应类名（或者已经绑定的容器对象标识）
     * @access protected
     * @return string
     */
    protected static function getFacadeClass()
    {
        return \app\lib\new2021\BaziCommon::class;
    }
}
