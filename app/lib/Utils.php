<?php
// +----------------------------------------------------------------------
// | 通用方法
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib;

use calendar\Calendar;

class Utils
{
    /**
     * 将字符串分割为数组
     * @param string $str 字符串
     * @return array 分割得到的数组
     */
    public static function mbStrSplit(string $str): array
    {
        return preg_split('/(?<!^)(?!$)/u', $str);
    }

    /**
     * 二维数组排序
     * @param array $arrays 必需。规定输入的数组
     * @param string $sortKey 要排序键名
     * @param int $sortOrder 可选。规定排列顺序。可能的值是 SORT_ASC 和 SORT_DESC
     * @param int $sortType 可选。规定排序类型。可能的值是SORT_REGULAR、SORT_NUMERIC和SORT_STRING。
     * @return bool|array
     */
    public static function mySort(array $arrays, string $sortKey, int $sortOrder = SORT_ASC, int $sortType = SORT_NUMERIC)
    {
        if (empty($arrays)) {
            return [];
        }
        if (is_array($arrays)) {
            foreach ($arrays as $array) {
                if (is_array($array)) {
                    $key_arrays[] = $array[$sortKey];
                } else {
                    return false;
                }
            }
        } else {
            return false;
        }
        array_multisort($key_arrays, $sortOrder, $sortType, $arrays);
        return $arrays;
    }

    /**
     * 格式化有声调的拼音 输出 不带声调拼音，声调,声母，韵母
     * @param string $pinyin
     * @return array
     */
    public static function formatPinYin(string $pinyin): array
    {
        $replacements = [
            'üē' => ['ue', 1], 'üé' => ['ue', 2], 'üě' => ['ue', 3], 'üè' => ['ue', 4],
            'ā' => ['a', 1], 'ē' => ['e', 1], 'ī' => ['i', 1], 'ō' => ['o', 1], 'ū' => ['u', 1], 'ǖ' => ['yu', 1],
            'á' => ['a', 2], 'é' => ['e', 2], 'í' => ['i', 2], 'ó' => ['o', 2], 'ú' => ['u', 2], 'ǘ' => ['yu', 2],
            'ǎ' => ['a', 3], 'ě' => ['e', 3], 'ǐ' => ['i', 3], 'ǒ' => ['o', 3], 'ǔ' => ['u', 3], 'ǚ' => ['yu', 3],
            'à' => ['a', 4], 'è' => ['e', 4], 'ì' => ['i', 4], 'ò' => ['o', 4], 'ù' => ['u', 4], 'ǜ' => ['yu', 4],
        ];
        $listShengMu = [
            'b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's', 'y', 'w',
        ];
        $res = ['py' => $pinyin, 'py_no' => '', 'tone' => 0, 'sm' => '', 'ym' => ''];
        foreach ($replacements as $unicode => $replacement) {
            if (str_contains($pinyin, $unicode)) {
                $umlaut = $replacement[0];

                // https://zh.wikipedia.org/wiki/%C3%9C
                if ('yu' == $umlaut) {
                    $umlaut = 'v';
                }
                $pyNoTone = str_replace($unicode, $umlaut, $pinyin);
                $tmp2 = substr($pyNoTone, 0, 2);
                $tmp1 = substr($pyNoTone, 0, 1);
                $length = strlen($pyNoTone);
                if (in_array($tmp2, $listShengMu)) {
                    $sm = $tmp2;
                    $ym = substr($pyNoTone, 2, $length);
                } elseif (in_array($tmp1, $listShengMu)) {
                    $sm = $tmp1;
                    $ym = substr($pyNoTone, 1, $length);
                } else {
                    $sm = '';
                    $ym = $pyNoTone;
                }

                $res = [
                    'py' => $pinyin,
                    'py_no' => $pyNoTone,
                    'tone' => $replacement[1],
                    'sm' => $sm,
                    'ym' => $ym,
                ];
            }
        }

        return $res;
    }

    /**
     * 根据干支排序
     * @param string $str
     * @return string
     */
    public static function sortGz(string $str): string
    {
        $arr1 = self::mbStrSplit($str);
        $res = self::sortbyGz($arr1);
        return implode('', $res);
    }

    /**
     * 根据提供的干支数组从小排到大
     * @param array $arr1 干支数组
     * @return array
     */
    public static function sortbyGz(array $arr1): array
    {
        $arr = array_merge(Calendar::TIAN_GAN, Calendar::DI_ZHI);
        $arrK = [];
        foreach ($arr1 as $k => $v) {
            $num = array_search($v, $arr);
            if (false === $num) {
                continue;
            }
            $arrK[$k] = $num;
        }
        sort($arrK);
        $res = [];
        foreach ($arrK as $v) {
            $res[] = $arr[$v];
        }
        return $res;
    }

    /**
     * 拼音拆分
     * @param $str
     * @return array
     */
    public static function splitFunc($str): array
    {
        $vowel = ['a', 'e', 'i', 'o', 'u', 'v'];
        $result = '';
        $i = 0;
        for ($i = 0; $i < strlen($str); $i++) {
            if (in_array($str[$i], $vowel)) {
                $result .= $str[$i];
                continue;
            } else {
                if ($str[$i] != 'n') {
                    if ($i == 0) {
                        $result .= $str[$i];
                    } else {
                        $result .= ' ' . $str[$i];
                    }
                    if ((($i + 1) < strlen($str) and $str[$i] == 'z' or $str[$i] == 'c' or $str[$i] == 's') and ($str[$i + 1] == 'h')) {
                        $result .= 'h';
                        $i++;
                    }
                    continue;
                } else {                 // 是n,继续向后
                    if ($i == strlen($str) - 1) {
                        $result .= $str[$i];
                        continue;
                    } else {
                        $i++;
                    }   // 继续向后

                    if (in_array($str[$i], $vowel)) {   // 如果是元音,从n前分开
                        if ($i == 1) {
                            $result .= 'n' . $str[$i];
                            continue;
                        } else {
                            $result .= ' ' . 'n' . $str[$i];
                            continue;
                        }
                    } // 如果是辅音字母
                    else {
                        if ($str[$i] == 'g') {
                            if ($i == strlen($str) - 1) {
                                $result .= 'n' . $str[$i];
                                continue;
                            } else {
                                $i++;
                            }  // 继续向后

                            if (in_array($str[$i], $vowel)) {
                                $result .= 'n' . ' ' . 'g' . $str[$i];
                                continue;
                            } else {
                                $result .= 'n' . 'g' . ' ' . $str[$i];
                                if (($i + 1) < strlen($str) and ($str[$i] == 'z' or $str[$i] == 'c' or $str[$i] == 's') and
                                    ($str[$i + 1] == 'h')) {
                                    $result .= 'h';
                                    $i++;
                                }
                                continue;
                            }
                        } else {   // 不是g的辅音字母,从n后分开
                            $result .= 'n' . ' ' . $str[$i];
                            if (($i + 1) < strlen($str) and ($str[$i] == 'z' or $str[$i] == 'c' or $str[$i] == 's') and
                                ($str[$i + 1] == 'h')) {
                                $result .= 'h';
                                $i++;
                            }
                            continue;
                        }
                    }
                }
            }
        }
        $arr = explode(' ', $result);
        return array_values(array_filter($arr));
    }

    /**
     * 数字转换为中文
     * @param int $num 目标数字
     * @return string
     */
    public static function numToWord(int $num): string
    {
        $chiNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        $chiUni = ['', '十', '百', '千', '万', '亿', '十', '百', '千'];

        $chiStr = '';

        $num_str = (string)$num;

        $count = strlen($num_str);
        $last_flag = true; // 上一个 是否为0
        $zero_flag = true; // 是否第一个
        $temp_num = null; // 临时数字

        $chiStr = '';// 拼接结果
        if ($count == 2) {// 两位数
            $temp_num = $num_str[0];
            $chiStr = $temp_num == 1 ? $chiUni[1] : $chiNum[$temp_num] . $chiUni[1];
            $temp_num = $num_str[1];
            $chiStr .= $temp_num == 0 ? '' : $chiNum[$temp_num];
        } elseif ($count > 2) {
            $index = 0;
            for ($i = $count - 1; $i >= 0; $i--) {
                $temp_num = $num_str[$i];
                if ($temp_num == 0) {
                    if (!$zero_flag && !$last_flag) {
                        $chiStr = $chiNum[$temp_num] . $chiStr;
                        $last_flag = true;
                    }
                } else {
                    $chiStr = $chiNum[$temp_num] . $chiUni[$index % 9] . $chiStr;
                    $zero_flag = false;
                    $last_flag = false;
                }
                $index++;
            }
        } else {
            $chiStr = $chiNum[$num_str[0]];
        }
        return $chiStr;
    }

    /**
     * 组合 返回所有的组合情况
     * @param array $a 要组合的数构成的数组
     * @param int $m 数量
     * @return array
     */
    public static function combination(array $a, int $m)
    {
        $r = [];
        $n = count($a);
        if ($m <= 0 || $m > $n) {
            return $r;
        }
        for ($i = 0; $i < $n; $i++) {
            $t = [$a[$i]];
            if ($m == 1) {
                $r[] = $t;
            } else {
                $b = array_slice($a, $i + 1);
                $c = self::combination($b, $m - 1);
                foreach ($c as $v) {
                    $r[] = array_merge($t, $v);
                }
            }
        }
        return $r;
    }

    /**
     * 全排列
     * @param array $array 初始数组
     * @param int $m 组合数量
     * @return array
     */
    public static function arrangement(array $array, int $m)
    {
        $r = [];

        $n = count($array);
        if ($m <= 0 || $m > $n) {
            return $r;
        }

        for ($i = 0; $i < $n; $i++) {
            $b = $array;
            $t = array_splice($b, $i, 1);
            if ($m == 1) {
                $r[] = $t;
            } else {
                $c = self::arrangement($b, $m - 1);
                foreach ($c as $v) {
                    $r[] = array_merge($t, $v);
                }
            }
        }

        return $r;
    }

    /**
     * 实现二维数组的笛卡尔积组合
     * @param array $arr 要进行笛卡尔积的二维数组
     * @param string $split 分割符
     * @return array|mixed|null
     */
    public static function dikaer(array $arr, string $split = '')
    {
        $result = array_shift($arr);
        while ($arr2 = array_shift($arr)) {
            $arr1 = $result;
            $result = [];
            foreach ($arr1 as $v) {
                foreach ($arr2 as $v2) {
                    $result[] = $v . $split . $v2;
                }
            }
        }
        return $result;
    }

    /**
     * 根据数字计算结果
     * @param int $jg
     * @return string
     */
    public static function numberChk(int $jg): string
    {
        switch ($jg) {
            case 0:
                $ct = '大展鸿图，信用得固，名利双收，可获成功|吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 1:
                $ct = '根基不固，摇摇欲坠，一盛一衰，劳而无获|凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 2:
                $ct = '根深蒂固，蒸蒸日上，如意吉祥，百事顺遂|吉|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 3:
                $ct = '前途坎坷，苦难折磨，非有毅力，难望成功|凶|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 4:
                $ct = '阴阳和合，生意兴隆，名利双收，后福重重|吉|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 5:
                $ct = '万宝集门，天降幸运，立志奋发，得成大功|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 6:
                $ct = '独营生意，和气吉祥，排除万难，必获成功|吉|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 7:
                $ct = '努力发达，贯彻志望，不忘进退，可望成功|吉|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 8:
                $ct = '虽抱奇才，有才无命，独营无力，财力难望|凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 9:
                $ct = '乌云遮月，暗淡无光，空费心力，徒劳无功|凶|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 10:
                $ct = '草木逢春，枝叶沾露，稳健着实，必得人望|吉|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 11:
                $ct = '薄弱无力，孤立无援，外祥内苦，谋事难成|凶|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 12:
                $ct = '天赋吉运，能得人望，善用智慧，必获成功|吉|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 13:
                $ct = '忍得若难，必有后福，是成是败，惟靠紧毅|凶|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 14:
                $ct = '谦恭做事，外得人和，大事成就，一门兴隆|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 15:
                $ct = '能获众望，成就大业，名利双收，盟主四方|吉|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 16:
                $ct = '排除万难，有贵人助，把握时机，可得成功|吉|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 17:
                $ct = '经商做事，顺利昌隆，如能慎始，百事亨通|吉|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 18:
                $ct = '成功虽早，慎防亏空，内外不合，障碍重重|凶|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 19:
                $ct = '智商志大，历尽艰难，焦心忧劳，进得两难|凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 20:
                $ct = '先历困苦，后得幸福，霜雪梅花，春来怒放|吉|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 21:
                $ct = '秋草逢霜，怀才不遇，忧愁怨苦，事不如意|凶|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 22:
                $ct = '旭日升天，名显四方，渐次进展，终成大业|吉|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 23:
                $ct = '绵绣前程，须靠自力，多用智谋，能奏大功|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 24:
                $ct = '天时地利，只欠人和，讲信修睦，即可成功|吉|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 25:
                $ct = '波澜起伏，千变万化，凌架万难，必可成功|凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 26:
                $ct = '一成一败，一盛一衰，惟靠谨慎，可守成功|凶带吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 27:
                $ct = '鱼临旱地，难逃恶运，此数大凶，不如更名|凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 28:
                $ct = '如龙得云，青云直上，智谋奋进，才略奏功|吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 29:
                $ct = '吉凶参半，得失相伴，投机取巧，吉凶无常|凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 30:
                $ct = '此数大吉，名利双收，渐进向上，大业成就|吉|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 31:
                $ct = '池中之龙，风云际会，一跃上天，成功可望|吉|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 32:
                $ct = '意气用事，人和必失，如能慎始，必可昌隆|吉|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 33:
                $ct = '灾难不绝，难望成功，此数大凶，不如更名|凶|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 34:
                $ct = '中吉之数，进退保守，生意安稳，成就普通|吉|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 35:
                $ct = '波澜得叠，常陷穷困，动不如静，有才无命|凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 36:
                $ct = '逢凶化吉，吉人天相，风调雨顺，生意兴隆|吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 37:
                $ct = '名虽可得，利则难获，艺界发展，可望成功|凶带吉|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 38:
                $ct = '云开见月，虽有劳碌，光明坦途，指日可望|吉|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 39:
                $ct = '一成一衰，沉浮不定，知难而退，自获天佑|吉带凶|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 40:
                $ct = '天赋吉运，德望兼备，继续努力，前途无限|吉|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 41:
                $ct = '事业不专，十九不成，专心进取，可望成功|吉带凶|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 42:
                $ct = '雨夜之花，外祥内苦，忍耐自重，转凶为吉|吉带凶|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 43:
                $ct = '虽用心计，事难遂愿，贪功冒进，必招失败|凶|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 44:
                $ct = '杨柳遇春，绿叶发枝，冲破难关，一举成名|吉|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 45:
                $ct = '坎坷不平，艰难重重，若无耐心，难望有成|凶|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 46:
                $ct = '有贵人助，可成大业，虽遇不幸，浮沉不定|吉|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 47:
                $ct = '美化丰实，鹤立鸡群，名利俱全，繁荣富贵|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 48:
                $ct = '遇吉则吉，遇凶则凶，惟靠谨慎，逢凶化吉|凶|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 49:
                $ct = '吉凶互见，一成一败，凶中有吉，吉中有凶|吉带凶|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 50:
                $ct = '一盛一衰，浮沉不常，自重自处，可保平安|吉带凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 51:
                $ct = '草木逢春，雨过天晴，渡过难关，即获得成功|吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 52:
                $ct = '盛衰参半，外祥内苦，先吉后凶，先凶后吉|吉带凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 53:
                $ct = '虽倾全力，难望成功，此数大凶，最好改名|凶|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 54:
                $ct = '外观昌隆，内隐祸患，克服难关，开出泰运|吉带凶|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 55:
                $ct = '事与愿违，终难成功，欲速不达，有始无终|凶|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 56:
                $ct = '虽有困难，时来运转，旷野枯草，春来花开|吉|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 57:
                $ct = '半凶半吉，浮沉多端，始凶终吉，能保成功|凶带吉|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 58:
                $ct = '遇事猜疑，难望成事，大刀阔斧，始可有成|凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 59:
                $ct = '黑暗无光，心迷意乱，出尔反尔，难定方针|凶|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 60:
                $ct = '运遮半月，内隐风波，应有谨慎，始保平安|吉带凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 61:
                $ct = '烦闷懊恼，事业难展，自防灾祸，始免困境|凶|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 62:
                $ct = '万物化育，繁荣之象，专心一意，必能成功|吉|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 63:
                $ct = '见异思迁，十九不成，徒劳无功，不如更名|凶|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 64:
                $ct = '吉运自来，能享盛名，把握时机，必获成功|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 65:
                $ct = '黑夜温长，进退维谷，内外不和，信用缺乏|凶|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 66:
                $ct = '独营事业，事事如意，功成名就，富贵自来|吉|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 67:
                $ct = '思虎虑周祥，计书力行，不失先机，可望成功|吉|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 68:
                $ct = '动摇不安，常陷逆境，不得时运，难得利润|凶|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 69:
                $ct = '惨淡经营，难免贫困，此数不吉，最好改名|凶|[高度戒备难交心型]，其具体表现为：经常处于戒备状态，对任何事都没法放松处理，孤僻性情难以交朋结友。对于爱情，就更加会慎重处理。任何人必须经过你长期观察及通过连番考验，方会减除戒备与你交往。';
                break;
            case 70:
                $ct = '吉凶参半，惟赖勇气，贯彻力行，始可成功|吉带凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 71:
                $ct = '利害混集，凶多吉少，得而复失，难以安顺|凶|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            case 72:
                $ct = '安乐自来，自然吉祥，力行不懈，终必成功|吉|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 73:
                $ct = '利不及费，坐食山空，如无章法，难望成功|凶|[独断独行/吸引人型]，其具体表现为：为人独断独行，事事自行作主解决，鲜有求助他人。而这份独立个性，正正就是吸引异性的特质。但其实心底里，却是渴望有人可让他/她依赖。';
                break;
            case 74:
                $ct = '吉中带凶，欲速不达，进不如守，可保安祥|吉带凶|[热情/善变梦想家型]，其具体表现为：对人热情无遮掩，时常梦想可以谈一场戏剧性恋爱，亲身体会个中悲欢离合的动人经历，是个大梦想家。但对于感情却易变卦。';
                break;
            case 75:
                $ct = '此数大凶，破产之象，宜速改名，以避厄运|凶|[自我牺牲/性格被动型]，其具体表现为：惯于无条件付出，从不祈求有回报，有为了成全他人不惜牺牲自己的情操。但讲到本身的爱情观，却流于被动，往往因为内敛而错过大好姻缘。';
                break;
            case 76:
                $ct = '先苦后甘，先甜后苦，如能守成，不致失败|吉带凶|[要面包不要爱情型]，其具体表现为：责任心重，尤其对工作充满热诚，是个彻头彻尾工作狂。但往往因为过分专注职务，而忽略身边的家人及朋友，是个宁要面包不需要爱情的理性主义者。';
                break;
            case 77:
                $ct = '有得有失，华而不实，须防劫财，始保安顺|吉带凶|[不善表达/疑心大型]，其具体表现为：在乎身边各人对自己的评价及观感，不善表达个人情感，是个先考虑别人感受，再作出相应配合的内敛一族。对于爱侣，经常存有怀疑之心。';
                break;
            case 78:
                $ct = '如走夜路，前途无光，希望不大，劳而无功|凶|[大胆行事冲动派型]，其具体表现为：爱好追寻刺激，有不理后果大胆行事的倾向。崇尚自由奔放的恋爱，会拼尽全力爱一场，是就算明知无结果都在所不惜的冲动派。';
                break;
            case 79:
                $ct = '得而复失，枉费心机，守成无贪，可保安稳|吉带凶|[好奇心旺求知欲强型]，其具体表现为：好奇心极度旺盛，求知欲又强，有打烂沙盘问到笃的锲而不舍精神。此外，你天生有语言天分，学习外文比一般人更易掌握。';
                break;
            case 80:
                $ct = '最极之数，还本归元，能得繁荣，发达成功|吉|[做事喜好凭直觉型]，其具体表现为：有特强的第六灵感，性格率直无机心，深得朋辈爱戴。感情路上多采多姿。做事喜好凭个人直觉及预感做决定。';
                break;
            default:
                $ct = '天机不可泄露|天机不可泄露|天机不可泄露';
                break;
        }
        return $ct;
    }
}
