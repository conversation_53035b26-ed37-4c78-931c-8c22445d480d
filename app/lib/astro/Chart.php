<?php
// +----------------------------------------------------------------------
// | Chart. 星盘绘图
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\astro;

class Chart
{
    /**
     * 空白画布
     * @var int
     */
    protected int $sizeOfRect = 640;

    /**
     * 中圆直径
     * @var int
     */
    protected int $diameter = 500;

    /**
     * diameter of circle drawn(外圆直径)
     * @var int
     */
    protected int $outerOuterDiameter = 600;

    /**
     * diameter of circle drawn(中径与内径之间的距离)
     * @var int
     */
    protected int $innerDiameterOffset = 90;

    /**
     * 内圆直径
     * @var float|int
     */
    protected float | int $innerDiameter;

    /**
     * distance between outer-outer diameter and diameter(外径与直径之间的距离)
     * @var float|int
     */
    protected float | int $outerDiameterDistance;

    /**
     * 设置中圈里的行星图标
     * @var int
     */
    protected int $distFromDiameter1 = 30;

    /**
     * 显示中圈里行星所在的线
     * @var int
     */
    protected int $distFromDiameter1a = 12;

    /**
     * distance outer planet glyph is from circumference of wheel
     * @var int
     */
    protected int $distFromDiameter2 = 48;

    /**
     * distance outer planet glyph is from circumference of wheel - for line
     * @var int
     */
    protected int $distFromDiameter2a = 28;

    /**
     * radius of circle drawn(圆半径)
     * @var float|int
     */
    protected float | int $radius;

    /**
     * center of circle (圆心)
     * @var float|int
     */
    protected float | int $centerPt;

    /**
     * 每个宫角度
     * @var int
     */
    protected int $degInEachHouse = 30;

    /**
     * 行星数据
     * @var array
     */
    protected array $planet;

    /**
     * 宫数据
     * @var array
     */
    protected array $hc;

    /**
     * add a planet
     * @var int
     */
    protected int $maxNumPlInEachHouse = 6;

    /**
     * 初始化
     * @param array $planet 行星数据
     * @param array $hc 宫数据
     */
    public function __construct(array $planet, array $hc)
    {
        $this->innerDiameter = $this->diameter - $this->innerDiameterOffset * 2;
        $this->outerDiameterDistance = ($this->outerOuterDiameter - $this->diameter) / 2;
        $this->radius = $this->diameter / 2;
        $this->centerPt = $this->sizeOfRect / 2;
        $this->planet = $planet;
        $this->hc = $hc;
    }

    /**
     * 设置圆直径
     * @param int $out 外圆
     * @param int $diameter 内圆
     * @param int $innerDiameterOffset 中圆与内圆距离
     */
    public function setCircle(int $out, int $diameter, int $innerDiameterOffset = 90)
    {
        $this->outerOuterDiameter = $out;
        $this->diameter = $diameter;
        $this->innerDiameterOffset = $innerDiameterOffset;
        $this->innerDiameter = $this->diameter - $this->innerDiameterOffset * 2;
        $this->outerDiameterDistance = ($this->outerOuterDiameter - $this->diameter) / 2;
        $this->radius = $this->diameter / 2;
    }

    /**
     * 获得绘圆数据
     * @return array
     */
    public function getChart()
    {
        $info = [
            'setViewBox' => '15, 15,610 ,610',
            // 外圆 圆心坐标x,圆心坐标y,半径,填充，线
            'line_circle_1' => [$this->centerPt, $this->centerPt, $this->outerOuterDiameter / 2],
            // 中圆 中圆心坐标x,圆心坐标y,半径
            'line_circle_2' => [$this->centerPt, $this->centerPt, $this->radius],
            // 内圆
            'line_circle_3' => [$this->centerPt, $this->centerPt, $this->radius - $this->innerDiameterOffset / 2],
            'line_circle_4' => [$this->centerPt, $this->centerPt, $this->innerDiameter / 2],
        ];
        // draw the horizontal line for the Ascendant(画出上升的水平线)
        $x1 = -($this->outerOuterDiameter / 2) * cos(deg2rad(0));
        $y1 = -($this->outerOuterDiameter / 2) * sin(deg2rad(0));

        $x2 = -($this->innerDiameter / 2) * cos(deg2rad(0));
        $y2 = -($this->innerDiameter / 2) * sin(deg2rad(0));
        $info['line_asc'][] = [
            round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
            $this->centerPt, $this->centerPt,
        ];
        $info['line_asc'][] = [
            round(-$x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
            $this->centerPt, $this->centerPt,
        ];
        // asc和des的点
        $info['dot_asc'] = [
            round($x2 + $this->centerPt, 3), round($y1 + $this->centerPt, 3), 'red',
        ];
        $info['dot_des'] = [
            round(-$x2 + $this->centerPt, 3), round($y1 + $this->centerPt, 3), 'red',];
        // (外环符号之间的分界线)
        // 起始角度
        $offsetFromStartOfSign = $this->hc[0]['deg'] + $this->hc[0]['min'] / 60;
        for ($i = 0; $i <= 11; $i++) {
            $tmpSign = $i * 30 + $offsetFromStartOfSign;
            $x1 = -$this->radius * cos(deg2rad($tmpSign));
            $y1 = -$this->radius * sin(deg2rad($tmpSign));

            $x2 = -($this->radius + $this->outerDiameterDistance) * cos(deg2rad($tmpSign));
            $y2 = -($this->radius + $this->outerDiameterDistance) * sin(deg2rad($tmpSign));
            // 外环符号之间的分界线
            $info['line_sign_12'][] = [
                round(($x1 + $this->centerPt), 3), round(($y1 + $this->centerPt), 3),
                round(($x2 + $this->centerPt), 3), round(($y2 + $this->centerPt), 3),
            ];
        }
        $ascendant = $this->hc[0]['degs'];
        $halfHourseNum = $this->getHourseNumDiff();
        // 画出房子的轮廓线(中圈)
        for ($i = 1; $i <= 12; $i = $i + 1) {
            $angle = $ascendant - $this->hc[$i - 1]['degs'];
            $x1 = -$this->radius * cos(deg2rad($angle));
            $y1 = -$this->radius * sin(deg2rad($angle));

            $x2 = -($this->radius - $this->innerDiameterOffset) * cos(deg2rad($angle));
            $y2 = -($this->radius - $this->innerDiameterOffset) * sin(deg2rad($angle));
            // 画分割线
            if ($i != 1 && $i != 10) {
                $info['house_cups_line'][] = [
                    round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
                    //round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3)
                    $this->centerPt, $this->centerPt,
                ];
            }
            if ($i == 12) {
                $angle1 = -360;
            } else {
                $angle1 = $ascendant - $this->hc[$i]['degs'];
            }
            $halfAngle = ($angle - $angle1) / 2;
            // 填充数字
            $xy = $this->displayHouseCuspNumber($i, -$angle + $halfHourseNum[$i], $this->radius - $this->innerDiameterOffset / 2);
            $info['house_cups_number'][] = [
                $i, round($xy[0] + $this->centerPt, 3), round($xy[1] + $this->centerPt, 3),
            ];
        }
        // draw the near-vertical line for the MC（为MC画一条接近垂直的线）
        $angle = $ascendant - $this->hc[9]['degs'];

        $x1 = ($this->outerOuterDiameter / 2) * cos(deg2rad($angle));
        $y1 = ($this->outerOuterDiameter / 2) * sin(deg2rad($angle));

        $x2 = -($this->outerOuterDiameter / 2) * cos(deg2rad($angle));
        $y2 = -($this->outerOuterDiameter / 2) * sin(deg2rad($angle));
        // mc线
        $info['line_mc'][] = [
            round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
            round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3),
        ];
        $dotICX = ($this->innerDiameter / 2) * cos(deg2rad($angle));
        $dotICY = ($this->innerDiameter / 2) * sin(deg2rad($angle));
        $info['dot_ic'] = [
            round($dotICX + $this->centerPt, 3), round($dotICY + $this->centerPt, 3), 'red',
        ];
        $dotMCX = -($this->innerDiameter / 2) * cos(deg2rad($angle));
        $dotMCY = -($this->innerDiameter / 2) * sin(deg2rad($angle));
        $info['dot_mc'] = [
            round($dotMCX + $this->centerPt, 3), round($dotMCY + $this->centerPt, 3), 'red',
        ];
        // mc 文字坐标
        $info['pan_mc'] = [
            round(-($this->radius - 15) * cos(deg2rad($angle)) + $this->centerPt, 3),
            round(-($this->radius - 15) * sin(deg2rad($angle)) + $this->centerPt, 3),
            'MC',
        ];
        // IC 文字坐标
        $info['pan_ic'] = [
            round(($this->radius - 15) * cos(deg2rad($angle)) + $this->centerPt, 3),
            round(($this->radius - 15) * sin(deg2rad($angle)) + $this->centerPt, 3),
            'IC',
        ];
        // 计算标尺
        $spokeLength = 10;
        $spokeLengthMin = 4;
        $cnt = 0;
        for ($i = $offsetFromStartOfSign; $i <= $offsetFromStartOfSign + 359; $i = $i + 1) {
            $x1 = -$this->radius * cos(deg2rad($i));
            $y1 = -$this->radius * sin(deg2rad($i));

            if ($cnt % 5 == 0) {
                $x2 = -($this->radius - $spokeLength) * cos(deg2rad($i));
                $y2 = -($this->radius - $spokeLength) * sin(deg2rad($i));
            } else {
                $x2 = -($this->radius - $spokeLengthMin) * cos(deg2rad($i));
                $y2 = -($this->radius - $spokeLengthMin) * sin(deg2rad($i));
            }
            $info['line_spoke'][] = [
                round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
                round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3),
            ];
            $cnt = $cnt + 1;
        }
        // 安12星座
        $cwSignGlyph = 14;
        $chSignGlyph = 12;
        $gapSignGlyph = -20;
        // 星座对应的守护星
        $listGuardStar = [
            'AR' => 'mars', 'TA' => 'venus', 'GE' => 'mercury', 'CN' => 'moon', 'LE' => 'sun', 'VI' => 'mercury',
            'LI' => 'venus', 'SC' => 'pluto', 'SG' => 'jupiter', 'CP' => 'saturn', 'AQ' => 'uranus', 'PI' => 'neptune',
        ];
        for ($i = 1; $i <= 12; $i++) {
            $angle_to_use = deg2rad((($i - 1) * 30) + 15 - $ascendant);

            $center_pos_x = -$cwSignGlyph / 2;
            $center_pos_y = $chSignGlyph / 2;

            $offset_pos_x = $center_pos_x * cos($angle_to_use);
            $offset_pos_y = $center_pos_y * sin($angle_to_use);

            $x1 = $center_pos_x + $offset_pos_x + ((-$this->radius + $gapSignGlyph) * cos($angle_to_use));
            $y1 = $center_pos_y + $offset_pos_y + (($this->radius - $gapSignGlyph) * sin($angle_to_use));

            $tmpSignGlyph = AstrologyUtil::$zodiacSign[$i - 1];
            $tmpSignGlyph['x'] = round($x1 + $this->centerPt, 3);
            $tmpSignGlyph['y'] = round($y1 + $this->centerPt, 3);
            $tmpSignGlyph['size'] = 10;
            $info['pan_sign_glyph'][] = [
                $tmpSignGlyph['x'], $tmpSignGlyph['y'], $tmpSignGlyph[3],
                $tmpSignGlyph[2], $tmpSignGlyph[0], $tmpSignGlyph[5],
            ];
            // 守护星
            $angleToUse1 = deg2rad((($i - 1) * 30) + 7 - $ascendant);
            $x2 = $center_pos_x + $offset_pos_x + ((-$this->radius + $gapSignGlyph) * cos($angleToUse1));
            $y2 = $center_pos_y + $offset_pos_y + (($this->radius - $gapSignGlyph) * sin($angleToUse1));
            $guardStarEN = $listGuardStar[$tmpSignGlyph[4]];
            $guardStar = $tmpPlGlyph = AstrologyUtil::$planet[$guardStarEN];
            $info['pan_guard_glyph'][] = [
                round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3),
                $guardStar[1], $guardStar[4], $guardStar[2], $guardStar[3],
            ];
        }
        // 安放行星
        $planetInfo = $this->getplanetChart();
        $info['pan_planets_glyph'] = $planetInfo['pan_planets_glyph'];
        $info['pan_planets_glyph_line'] = $planetInfo['pan_planets_glyph_line'];
        $info['pan_planets_aspect_lines'] = $planetInfo['pan_planets_aspect_lines'];
        return $info;
    }

    /**
     * 行星图表的点和线
     * @return array
     */
    protected function getplanetChart()
    {
        $result = [];
        // 按角度降序排列行星（从360到0的降序排序）
        $planetDegs = array_column($this->planet, 'degs');
        $longitude = $planetDegs;
        arsort($planetDegs);
        $sortPos = array_keys($planetDegs);// 按角度降序排列角度列表
        $sort = array_values($planetDegs);// 行星序号
        // 每个宫里有多少个行星
        $nopih = AstrologyUtil::countPlanetsInEachHouse($sort, $sortPos);
        $spotFilled = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        $houseNum = 0;
        $numPlanets = count($sort);
        $maxNumPlInEachHouse = $this->maxNumPlInEachHouse;
        $degInEachHouse = $this->degInEachHouse;
        $ascendant = $this->hc[0]['degs'];
        // add planet glyphs around circle(放行星符号)
        $flag = false;
        $planetAngle = [];
        $angleToUseArr = [];
        for ($i = $numPlanets - 1; $i >= 0; $i--) {
            // $sort 按经度从360降到0的降序
            // $sort_pos() 与上面的经度相对应
            $temp = $houseNum;
            $houseNum = floor($sort[$i] / 30) + 1;              // get house (sign) planet is in
            $houseNum = $houseNum % 12;
            if ($houseNum == 0) {
                $houseNum = 12;
            }
            if ($temp != $houseNum) {
                // 这颗行星与上一颗行星不同，这颗行星是这所宫里的第一个，
                $planetsDone = 1;
            }

            // 让这个行星的指数在可能的xx不同位置在轮子上的位置
            $fromCusp = $this->reduceBelow30($sort[$i]);
            if (($fromCusp >= 360 - 1 / 36000) && ($fromCusp <= 360 + 1 / 36000)) {
                $fromCusp = 0;
            }

            $indexy = floor($fromCusp * $maxNumPlInEachHouse / $degInEachHouse);

            // 同一宫内的其他行星等，根据需要调整指数.
            if ($indexy >= $maxNumPlInEachHouse - $nopih[$houseNum]) {
                if ($maxNumPlInEachHouse - $indexy - $nopih[$houseNum] + $planetsDone <= 0) {
                    if ($indexy - $nopih[$houseNum] + $planetsDone < 0) {
                        $indexy = $maxNumPlInEachHouse - $nopih[$houseNum];
                    } else {
                        $tmp = ((($houseNum - 1) * $maxNumPlInEachHouse) + $indexy);
                        if (isset($spotFilled[$tmp]) && $spotFilled[$tmp] == 0) {
                            $indexy = $maxNumPlInEachHouse - $nopih[$houseNum] + $planetsDone - 1;
                        } else {
                            $indexy = $maxNumPlInEachHouse - $nopih[$houseNum];
                        }
                    }
                }

                if ($indexy < 0) {
                    $indexy = 0;
                }
            }

            $tmpSpotIndex = (($houseNum - 1) * $maxNumPlInEachHouse) + $indexy;

            // 看看中圆的这个位置是否已经填满
            if (isset($spotFilled[$tmpSpotIndex]) && $spotFilled[$tmpSpotIndex] == 1) {
                // 是, so push the planet up one position
                $indexy++;
            }

            // 将此位置标记为已填补
            $spotFilled[$tmpSpotIndex] = 1;

            // set the final index
            $chart_idx = $tmpSpotIndex;

            // 取上面的索引并将其转换为一个角度
            // $planetAngle[$sort_pos[$i]] = ($chart_idx * (3 * $degInEachHouse) / (3 * $maxNumPlInEachHouse)) + ($degInEachHouse / (2 * $maxNumPlInEachHouse));            // needed for aspect lines
            $planetAngle[$sortPos[$i]] = $sort[$i];

            $angleToUse = $planetAngle[$sortPos[$i]] - $ascendant;         // needed for placing info on chartwheel

            // 表示我们在这所宫里至少做过一颗行星（实际上算了我们这座宫里的行星）
            $planetsDone++;
            $radius2 = $this->radius - ($this->innerDiameterOffset / 2) - ($this->distFromDiameter2) - 10;
            // 显示选题
            $angleToUse = deg2rad($angleToUse);
            $NewangleToUse = $this->getPlanetAngle($angleToUse, $angleToUseArr);
            $angleToUseArr[] = $NewangleToUse;
            //            if ($flag == false) {
            //                $xy = $this->displayPlanetGlyph($angleToUse, $this->radius - $this->innerDiameterOffset / 2 - $this->distFromDiameter1);
            //            } else {
            //                $xy = $this->displayPlanetGlyph($angleToUse, $this->radius - $this->innerDiameterOffset / 2 - ($this->distFromDiameter2));
            //            }
            $xy = $this->displayPlanetGlyph($NewangleToUse, $radius2);
            // pr($this->radius - $this->innerDiameterOffset / 2);
            $planetEnS = AstrologyUtil::getPlanetEnS();
            $planNameEnS = $planetEnS[$sortPos[$i]];
            $tmpPlGlyph = AstrologyUtil::$planet[$planNameEnS];
            $tmpPlGlyph['x'] = round($xy[0] + $this->centerPt, 3);
            $tmpPlGlyph['y'] = round($xy[1] + $this->centerPt, 3);
            $tmpPlGlyph['size'] = 16;
            $result['pan_planets_glyph'][] = [
                $tmpPlGlyph['x'], $tmpPlGlyph['y'], $tmpPlGlyph[1], $tmpPlGlyph[4], $tmpPlGlyph[2], $tmpPlGlyph[3],
            ];
            // 点坐标
            $glyphRadis = $this->radius - $this->innerDiameterOffset;
            $glyphRadis2 = $this->radius - $this->innerDiameterOffset + 20;
            $tmpglyph2x = (-$glyphRadis) * cos($angleToUse);
            $tmpglyph2y = ($glyphRadis) * sin($angleToUse);
            $tmpglyph3x = (-$glyphRadis2) * cos($NewangleToUse);
            $tmpglyph3y = ($glyphRadis2) * sin($NewangleToUse);
            // 行星在内圈的坐标
            $result['pan_planets_glyph_line'][] = [
                round($tmpglyph2x + $this->centerPt, 3), round($tmpglyph2y + $this->centerPt, 3),
                round($tmpglyph3x + $this->centerPt, 3), round($tmpglyph3y + $this->centerPt, 3),
                $tmpPlGlyph[4],
            ];
            //            $planetLineRadis = $this->radius - $this->innerDiameterOffset / 2;
            //            //draw line from planet to circumference(从行星到圆周画线)
            //            $distFromDiameter1a=$this->distFromDiameter1a;
            //            $distFromDiameter2a=$this->distFromDiameter2a;
            //            if ($flag == false) {
            //                $x1 = (-$this->radius + $distFromDiameter1a) * cos($angleToUse);
            //                $y1 = ($this->radius - $distFromDiameter1a) * sin($angleToUse);
            //                $x2 = (-$this->radius + 6) * cos($angleToUse);
            //                $y2 = ($this->radius - 6) * sin($angleToUse);
            //            } else {
            //                $x1 = (-$this->radius + $distFromDiameter2a) * cos($angleToUse);
            //                $y1 = ($this->radius - $distFromDiameter2a) * sin($angleToUse);
            //                $x2 = (-$this->radius + 6) * cos($angleToUse);
            //                $y2 = ($this->radius - 6) * sin($angleToUse);
            //            }
            //            $result['pan_planets_glyph_line'][] = [
            //                round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
            //                round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3)
            //            ];

            $flag = !$flag;
        }
        // 画线（aspect）
        for ($i = 0; $i <= $numPlanets - 1; $i++) {
            for ($j = $i + 1; $j <= $numPlanets - 1; $j++) {
                $q = 0;
                $da = abs($longitude[$sortPos[$i]] - $longitude[$sortPos[$j]]);

                if ($da > 180) {
                    $da = 360 - $da;
                }
                // set orb - 8 if Sun or Moon, 6 if not Sun or Moon
                if ($sortPos[$i] == 0 || $sortPos[$i] == 1 || $sortPos[$j] == 0 || $sortPos[$j] == 1) {
                    $orb = 8;
                } else {
                    $orb = 6;
                }

                // is there an aspect within orb?
                if ($da <= $orb) {
                    $q = 1;
                } elseif (($da <= (60 + $orb)) && ($da >= (60 - $orb))) {
                    $q = 6;
                } elseif (($da <= (90 + $orb)) && ($da >= (90 - $orb))) {
                    $q = 4;
                } elseif (($da <= (120 + $orb)) && ($da >= (120 - $orb))) {
                    $q = 3;
                } elseif (($da <= (150 + $orb)) && ($da >= (150 - $orb))) {
                    $q = 5;
                } elseif ($da >= (180 - $orb)) {
                    $q = 2;
                }

                if ($q > 0) {
                    if ($q == 1 || $q == 3 || $q == 6) {
                        $aspectColor = 'green';
                    } elseif ($q == 4 || $q == 2) {
                        $aspectColor = 'red';
                    } elseif ($q == 5) {
                        $aspectColor = 'blue';
                    }

                    if ($q != 1) {
                        //non-conjunctions

                        $x1 = (-$this->radius + $this->innerDiameterOffset) * cos(deg2rad($planetAngle[$sortPos[$i]] - $ascendant));
                        $y1 = ($this->radius - $this->innerDiameterOffset) * sin(deg2rad($planetAngle[$sortPos[$i]] - $ascendant));
                        $x2 = (-$this->radius + $this->innerDiameterOffset) * cos(deg2rad($planetAngle[$sortPos[$j]] - $ascendant));
                        $y2 = ($this->radius - $this->innerDiameterOffset) * sin(deg2rad($planetAngle[$sortPos[$j]] - $ascendant));


                        $result['pan_planets_aspect_lines'][] = [
                            round($x1 + $this->centerPt, 3), round($y1 + $this->centerPt, 3),
                            round($x2 + $this->centerPt, 3), round($y2 + $this->centerPt, 3), $aspectColor,
                        ];
                    }
                }
            }
        }
        return $result;
    }


    /**
     * 获得行星座标
     * @param float $angleToUse 角度
     * @param float|int $radii 半径
     * @return array
     */
    protected function displayPlanetGlyph(float $angleToUse, float | int $radii)
    {
        $cwPlGlyph = 16;
        $chPlGlyph = 16;
        $gapPlGlyph = -30;

        // 定义的字形的宽度和高度
        // 得到距离，以字形的绝对中间是起点
        $center_pos_x = -$cwPlGlyph / 2;
        $center_pos_y = $chPlGlyph / 2;

        // 得到移动中心点的偏移量
        $offset_pos_x = $center_pos_x * cos($angleToUse);
        $offset_pos_y = $center_pos_y * sin($angleToUse);

        // 得到最终坐标
        $xy[0] = $center_pos_x + $offset_pos_x + ((-$radii + $gapPlGlyph) * cos($angleToUse));
        $xy[1] = $center_pos_y + $offset_pos_y + (($radii - $gapPlGlyph) * sin($angleToUse));
        return $xy;
    }

    /**
     * 获得数字位置
     * @param int $num 数字
     * @param float $angle 角度
     * @param float $radii 半径
     * @return array
     */
    protected function displayHouseCuspNumber(int $num, float $angle, float $radii)
    {
        if ($num < 10) {
            $char_width = 10;
        } else {
            $char_width = 26;
        }
        $half_char_width = $char_width / 2;
        $char_height = 12;
        $half_char_height = $char_height / 2;

        //puts center of character right on circumference of circle
        $xpos0 = -$half_char_width;
        $ypos0 = $char_height;
        $xpos0 = 0;
        if ($num == 1) {
            $x_adj = -cos(deg2rad($angle)) * $char_width;//deg2rad角度转换为弧度。
            $y_adj = sin(deg2rad($angle)) * $char_height;
        } elseif ($num == 2) {
            $x_adj = -cos(deg2rad($angle)) * $half_char_width;
            $y_adj = sin(deg2rad($angle)) * $char_height;
        } elseif ($num == 3) {
            $xpos0 = $half_char_width;
            $x_adj = -cos(deg2rad($angle)) * $half_char_width;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 4) {
            $xpos0 = $char_width;
            $x_adj = -cos(deg2rad($angle)) * $half_char_width;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 5) {
            $xpos0 = $char_width;
            $x_adj = -cos(deg2rad($angle)) * $half_char_width;
            $ypos0 = $half_char_height;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 6) {
            $xpos0 = $char_width;
            $x_adj = -cos(deg2rad($angle)) * $half_char_width;
            $ypos0 = -$half_char_height;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 7) {
            $x_adj = -cos(deg2rad($angle)) * $char_width;
            $ypos0 = -$half_char_height;
            $y_adj = -sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 8) {
            $x_adj = -cos(deg2rad($angle)) * $char_width;
            $ypos0 = -$half_char_height;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 9) {
            $xpos0 = -$char_width;
            $x_adj = -cos(deg2rad($angle)) * $char_width;
            $ypos0 = -$half_char_height;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        } elseif ($num == 10) {
            $xpos0 = -$char_width + 12;
            $x_adj = -cos(deg2rad($angle)) * ($char_width);
            $ypos0 = $half_char_height - 10;
            $y_adj = sin(deg2rad($angle)) * $char_height;
        } elseif ($num == 11) {
            $xpos0 = -$char_width / 2;
            $x_adj = -cos(deg2rad($angle)) * $char_width;
            $y_adj = sin(deg2rad($angle)) * $char_height;
        } elseif ($num == 12) {
            $x_adj = -cos(deg2rad($angle)) * $char_width;
            $y_adj = sin(deg2rad($angle)) * $half_char_height;
        }

        $xy[0] = $xpos0 + $x_adj - ($radii * cos(deg2rad($angle)));
        $xy[1] = $ypos0 + $y_adj + ($radii * sin(deg2rad($angle)));

        return ($xy);
    }

    /**
     * 处理度数保证值 小于30（就是一个宫的度数）
     * @param float|int $longitude 度数
     * @return int
     */
    protected function reduceBelow30(float | int $longitude)
    {
        $lng = $longitude;

        while ($lng >= 30) {
            $lng = $lng - 30;
        }

        return $lng;
    }

    /**
     * 外圈各个星座范围 起始角度，终止角度 图标 英文简写 中文名
     * @return array
     */
    protected function getOutCircleDeg()
    {
        $offsetFromStartOfSign = $this->hc[0]['deg'] + $this->hc[0]['min'] / 60;

        $startKey = array_search($this->hc[0]['sign'], AstrologyUtil::$zodiaSignEnS);
        $res = [];
        for ($i = 0; $i <= 11; $i++) {
            $tmpSign = $i * 30 + $offsetFromStartOfSign;
            $tmpSign1 = (($i + 1) % 12) * 30 + $offsetFromStartOfSign;
            $key = ($startKey - 1 - $i + 12) % 12;
            $sign = AstrologyUtil::$zodiacSign[$key];
            $res[] = [
                // 起始角度，终止角度 图标 英文简写 中文名
                $tmpSign, $tmpSign1, $sign[3], $sign[4], $sign[0], $sign[5],
            ];
        }
        return $res;
    }

    /**
     * 中圈线在外圈的取值列表
     * @return array
     */
    public function getcenterInCircleDeg()
    {
        $outCircleDeg = $this->getOutCircleDeg();
        $ascendant = $this->hc[0]['degs'];
        $res = [];
        foreach ($this->hc as $k => $v) {
            $angle = AstrologyUtil::parseDegToSec($ascendant) - AstrologyUtil::parseDegToSec($v['degs']);
            $angle = (int)($angle + 360 * 60 * 60) % (360 * 60 * 60);
            $deg = AstrologyUtil::parseSecondToDeg($angle);
            // 把 度，分 ，秒 转成度数数字
            $angle = AstrologyUtil::parseDegree($deg['deg'], $deg['min'], $deg['sec']);
            $tmpRes = $this->getGongInSign($angle, $outCircleDeg);
            $tmpRes['num'] = $k + 1;
            $res[] = $tmpRes;
        }
        return $res;
    }

    /**
     * 求当前宫所在星座
     * @param float $angle
     * @param array $outCircleDeg 外圈
     * @return array
     */
    protected function getGongInSign(float $angle, array $outCircleDeg)
    {
        $res = [];
        $current = $outCircleDeg[0];
        foreach ($outCircleDeg as $v) {
            if ($v[0] > $v[1]) {
                if ($v[0] <= $angle || $v[1] > $angle) {
                    $current = $v;
                    break;
                }
                continue;
            }
            if ($angle >= $v[0] && $angle < $v[1]) {
                $current = $v;
                break;
            }
        }
        //
        $tmp = AstrologyUtil::parseDegToSec($angle) - AstrologyUtil::parseDegToSec($current[0]);
        $sec = (int)($tmp + 360 * 60 * 60) % (360 * 60 * 60);
        $sec = $sec % (30 * 60 * 60);
        $sec = (30 * 60 * 60) - $sec;
        $deg = AstrologyUtil::parseSecondToDeg($sec);
        $res = $deg;
        unset($res['all']);
        $res['glyph'] = $v[2];
        $res['cn'] = $v[5];
        return $res;
    }

    /**
     * @param array $planet2
     * @param array $hc2
     * @return array
     */
    public function getChartByUser2(array $planet2, array $hc2)
    {
        $info = [];
        $ascendant = $this->hc[0]['degs'];
        $ascendant2 = $hc2[0]['degs'];
        $out = $this->diameter;
        $diameter = $this->diameter - $this->innerDiameterOffset * 2;
        $radis = $diameter / 2;

        $mcDeg = $ascendant - ($hc2[9]['degs'] + $ascendant2);
        // mc线
        $info['line_mc'][] = $this->getSvgLine($mcDeg, $diameter, $this->centerPt);
        // mc 文字坐标
        $info['pan_mc'] = [
            round(-($radis - 15) * cos(deg2rad($mcDeg)) + $this->centerPt, 3),
            round(-($radis - 15) * sin(deg2rad($mcDeg)) + $this->centerPt, 3),
            'MC',
        ];
        $ascDeg = $ascendant - $ascendant2;
        $info['line_asc'][] = $this->getSvgLine($ascDeg, $diameter, $this->centerPt);
        $info['pan_asc'] = [
            round(-($radis - 15) * cos(deg2rad($ascDeg)) + $this->centerPt, 3),
            round(-($radis - 15) * sin(deg2rad($ascDeg)) + $this->centerPt, 3),
            'Asc',
        ];
        $planetSvg = $this->getplanetChart1($planet2, $hc2);
        $info['pan_planets_glyph'] = $planetSvg['pan_planets_glyph'];
        return $info;
    }

    /**
     * 获得线
     * @param float $angle 角度
     * @param int $outerOuterDiameter 线半径
     * @param int $centerPt 圆心
     * @return array
     */
    protected function getSvgLine(float $angle, int $outerOuterDiameter, int $centerPt)
    {
        $x1 = ($outerOuterDiameter / 2) * cos(deg2rad($angle));
        $y1 = ($outerOuterDiameter / 2) * sin(deg2rad($angle));

        $x2 = -($outerOuterDiameter / 2) * cos(deg2rad($angle));
        $y2 = -($outerOuterDiameter / 2) * sin(deg2rad($angle));
        return [
            round($x1 + $centerPt, 3), round($y1 + $centerPt, 3),
            round($x2 + $centerPt, 3), round($y2 + $centerPt, 3),
        ];
    }

    /**
     * @param array $planet
     * @param array $hc
     * @return array
     */
    protected function getplanetChart1(array $planet, array $hc)
    {
        $result = [];
        // 按角度降序排列行星（从360到0的降序排序）
        $planetDegs = array_column($planet, 'degs');
        $longitude = $planetDegs;
        arsort($planetDegs);
        $sortPos = array_keys($planetDegs);// 按角度降序排列角度列表
        $sort = array_values($planetDegs);// 行星序号
        // 每个宫里有多少个行星
        $nopih = AstrologyUtil::countPlanetsInEachHouse($sort, $sortPos);

        $spotFilled = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        $houseNum = 0;
        $numPlanets = count($sort);
        $maxNumPlInEachHouse = $this->maxNumPlInEachHouse;
        $degInEachHouse = $this->degInEachHouse;
        $ascendant = $this->hc[0]['degs'] - $hc[0]['degs'];
        // add planet glyphs around circle(放行星符号)
        $flag = false;
        $planetAngle = [];
        for ($i = $numPlanets - 1; $i >= 0; $i--) {
            // $sort 按经度从360降到0的降序
            // $sort_pos() 与上面的经度相对应
            $temp = $houseNum;
            $houseNum = floor($sort[$i] / 30) + 1;              // get house (sign) planet is in
            $houseNum = $houseNum % 12;
            if ($houseNum == 0) {
                $houseNum = 12;
            }
            if ($temp != $houseNum) {
                // 这颗行星与上一颗行星不同，这颗行星是这所宫里的第一个，
                $planetsDone = 1;
            }

            // 让这个行星的指数在可能的xx不同位置在轮子上的位置
            $fromCusp = $this->reduceBelow30($sort[$i]);
            if (($fromCusp >= 360 - 1 / 36000) && ($fromCusp <= 360 + 1 / 36000)) {
                $fromCusp = 0;
            }

            $indexy = floor($fromCusp * $maxNumPlInEachHouse / $degInEachHouse);

            // 同一宫内的其他行星等，根据需要调整指数.
            if ($indexy >= $maxNumPlInEachHouse - $nopih[$houseNum]) {
                if ($maxNumPlInEachHouse - $indexy - $nopih[$houseNum] + $planetsDone <= 0) {
                    if ($indexy - $nopih[$houseNum] + $planetsDone < 0) {
                        $indexy = $maxNumPlInEachHouse - $nopih[$houseNum];
                    } else {
                        $tmp = ((($houseNum - 1) * $maxNumPlInEachHouse) + $indexy);
                        if (isset($spotFilled[$tmp]) && $spotFilled[$tmp] == 0) {
                            $indexy = $maxNumPlInEachHouse - $nopih[$houseNum] + $planetsDone - 1;
                        } else {
                            $indexy = $maxNumPlInEachHouse - $nopih[$houseNum];
                        }
                    }
                }

                if ($indexy < 0) {
                    $indexy = 0;
                }
            }

            $tmpSpotIndex = (($houseNum - 1) * $maxNumPlInEachHouse) + $indexy;

            // 看看中圆的这个位置是否已经填满
            if (isset($spotFilled[$tmpSpotIndex]) && $spotFilled[$tmpSpotIndex] == 1) {
                // 是, so push the planet up one position
                $indexy++;
            }

            // 将此位置标记为已填补
            $spotFilled[$tmpSpotIndex] = 1;

            // set the final index
            $chart_idx = $tmpSpotIndex;

            // 取上面的索引并将其转换为一个角度
            //$planetAngle[$sort_pos[$i]] = ($chart_idx * (3 * $degInEachHouse) / (3 * $maxNumPlInEachHouse)) + ($degInEachHouse / (2 * $maxNumPlInEachHouse));                // needed for aspect lines
            $planetAngle[$sortPos[$i]] = $sort[$i];

            $angleToUse = $planetAngle[$sortPos[$i]] - $ascendant;         // needed for placing info on chartwheel

            // 表示我们在这所宫里至少做过一颗行星（实际上算了我们这座宫里的行星）
            $planetsDone++;

            // 显示选题
            $angleToUse = deg2rad($angleToUse);


            if ($flag == false) {
                $xy = $this->displayPlanetGlyph($angleToUse, $this->innerDiameter / 2 - $this->distFromDiameter1);
            } else {
                $xy = $this->displayPlanetGlyph($angleToUse, $this->innerDiameter / 2 - ($this->distFromDiameter2));
            }
            $planetEnS = AstrologyUtil::getPlanetEnS();
            $planNameEnS = $planetEnS[$sortPos[$i]];
            $tmpPlGlyph = AstrologyUtil::$planet[$planNameEnS];
            $tmpPlGlyph['x'] = round($xy[0] + $this->centerPt, 3);
            $tmpPlGlyph['y'] = round($xy[1] + $this->centerPt, 3);
            $tmpPlGlyph['size'] = 16;
            $result['pan_planets_glyph'][] = [
                $tmpPlGlyph['x'], $tmpPlGlyph['y'], $tmpPlGlyph[1], $tmpPlGlyph[4], $tmpPlGlyph[2], $tmpPlGlyph[3],
            ];

            $flag = !$flag;
        }
        return $result;
    }

    /**
     * 宫数字之间的距离差值
     * @return array
     */
    private function getHourseNumDiff()
    {
        $ascendant = $this->hc[0]['degs'];
        $list = [];
        for ($i = 1; $i <= 12; $i = $i + 1) {
            $angle = $ascendant - $this->hc[$i - 1]['degs'];
            $list[$i] = $angle > 0 ? (360 - $angle) : (-(int)$angle + 360) % 360;
        }
        $res = [];
        foreach ($list as $k => $v) {
            if ($k == 12) {
                $tmp = (360 - $v) / 2;
            } else {
                $tmp = ($list[$k + 1] - $v) / 2;
            }
            $res[$k] = (int)$tmp;
        }
        return $res;
    }

    /**
     * 判断两星角度是否符合条件
     * @param int $diff
     * @return int
     */
    private function getXianWeiKey(int $diff)
    {
        $result = 0;
        if ($diff <= 4 && $diff >= 0) {
            $result = 0;
        } elseif ($diff >= 57 && $diff <= 62) {
            $result = 1;
        } elseif ($diff >= 87 && $diff <= 93) {
            $result = 2;
        } elseif ($diff >= 117 && $diff <= 123) {
            $result = 3;
        } elseif ($diff >= 177 && $diff <= 183) {
            $result = 4;
        }
        return $result;
    }

    /**
     * 获得行星角度重合数
     * @param float|int $deg 角度
     * @param array $arr 角度数组
     * @return float
     */
    private function getPlanetAngle(float | int $deg, array $arr)
    {
        foreach ($arr as $v) {
            $diff = abs($v - $deg);
            if ($diff < 0.08) {
                $deg = $v + 0.08;
            }
        }
        return $deg;
    }
}
