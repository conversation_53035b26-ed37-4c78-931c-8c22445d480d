<?php
// +----------------------------------------------------------------------
// | AstrologyUtil 星盘
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\astro;

use app\model\astro\Placidus;
use app\model\astro\Sweph;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class AstrologyUtil
{
    /**
     * 十二星座
     * @var array|array[]
     */
    public static array $zodiacSign = [
        ['Aries', 0, 'red', 'a', 'AR', '白羊座'],
        ['Taurus', 30, 'green', 's', 'TA', '金牛座'],
        ['Gemini', 60, 'brown', 'd', 'GE', '双子座'],
        ['Cancer', 90, 'blue', 'f', 'CN', '巨蟹座'],
        ['Leo', 120, 'red', 'g', 'LE', '狮子座'],
        ['Virgo', 150, 'green', 'h', 'VI', '处女座'],
        ['Libra', 180, 'brown', 'j', 'LI', '天秤座'],
        ['Scorpio', 210, 'blue', 'k', 'SC', '天蝎座'],
        ['Sagittaurius', 240, 'red', 'l', 'SG', '射手座'],
        ['Capricorn', 270, 'green', 'z', 'CP', '摩羯座'],
        ['Aquarius', 300, 'brown', 'x', 'AQ', '水瓶座'],
        ['Pisces', 330, 'blue', 'c', 'PI', '双鱼座'],
    ];

    /**
     * @var array|string[]
     */
    public static array $zodiaSignEnS = [
        'AR', 'TA', 'GE', 'CN', 'LE', 'VI', 'LI', 'SC', 'SG', 'CP', 'AQ', 'PI',
    ];

    /**
     * 行星命名数组
     * @var array
     */
    public static array $planet = [
        'sun' => [3, 'Q', 'Sun', '太阳', 'red'],
        'moon' => [4, 'W', 'Moon', '月亮', 'blue'],
        'mercury' => [5, 'E', 'Mercury', '水星', 'green'],
        'venus' => [6, 'R', 'Venus', '金星', 'brown'],
        'mars' => [7, 'T', 'Mars', '火星', 'red'],
        'jupiter' => [8, 'Y', 'Jupiter', '木星', 'red'],
        'saturn' => [9, 'U', 'Saturn', '土星', 'brown'],
        'uranus' => [10, 'I', 'Uranus', '天王星', 'green'],
        'neptune' => [11, 'O', 'Neptune', '海王星', 'blue'],
        'pluto' => [12, 'P', 'Pluto', '冥王星', 'blue'],
        'node' => [13, 'v', 'Node', '北交点', 'gray'], // ascending(上升点)
        'southnode' => [14, '?', 'SouthNode', '南交点', 'gray'], // descending
    ];

    /**
     * 行星英文名
     * @var string[]
     */
    public static array $planetList = [
        'sun', 'moon', 'mercury', 'venus', 'mars', 'jupiter', 'saturn', 'uranus', 'neptune', 'pluto',
    ];

    /**
     * 星座对应的守护星
     * @var string[]
     */
    public static array $guardStar = [
        'AR' => 'mars', 'TA' => 'venus', 'GE' => 'mercury', 'CN' => 'moon', 'LE' => 'sun', 'VI' => 'mercury',
        'LI' => 'venus', 'SC' => 'pluto', 'SG' => 'jupiter', 'CP' => 'saturn', 'AQ' => 'uranus', 'PI' => 'neptune',
    ];

    /**
     * 时间区
     * @var int
     */
    protected int $timeZone = 8;

    /**
     * 当天的数据
     * @var array
     */
    protected array $swephData = [];

    /**
     * 第二天的数据
     * @var array
     */
    protected array $swephDataNext = [];

    /**
     * 经度
     * @var float
     */
    protected float $longitude = 0.00;

    /**
     * 纬度
     * @var float
     */
    protected float $latitude = 0.00;

    /**
     * 传入的时间
     * @var string
     */
    protected string $utNow = '';

    /**
     * 当地恒星时
     * @var array
     */
    protected array $localSid = [];

    /**
     * lmt
     * @var array
     */
    protected array $lmt = [];

    /**
     * 宫位计算mc
     * @var array
     */
    protected array $mc = [];

    /**
     * 宫盘
     * @var array
     */
    protected array $houseCup = [];

    /**
     * 初始化
     * @param string $utdate 日期2018-10-12
     * @param string $utNow 时间 12:00:00
     * @param float $longitude 经度
     * @param float $latitude 纬度
     * @param int $timeZone
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct(string $utdate, string $utNow, float $longitude, float $latitude, int $timeZone = 8)
    {
        $this->timeZone = $timeZone;
        $utdate = date('Y-m-d', strtotime($utdate));
        $this->swephData = Sweph::info($utdate);
        $utdateNext = date('Y-m-d', strtotime($utdate) + 86400);
        $this->swephDataNext = Sweph::info($utdateNext);
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        // $this->utDate = $utdate;
        $this->utNow = $utNow;
        $this->lmt = $this->getLmt();
        $this->localSid = $this->getLocalSid();
    }

    /**
     * 1获得Lmt
     * @return array
     */
    public function getLmt(): array
    {
        if ($this->lmt) {
            return $this->lmt;
        }
        // 8（时区，此为变量）*15。(15度为固定度数)=120。
        $degrees = 15 * $this->timeZone;
        $diff = abs($degrees - $this->longitude);

        $du = (int)$diff * 4;// 1。*4min（4min为固定值）=4min 把度数转化为分钟;
        $min = (int)(($diff - (int)$diff) * 60) * 4;// 换算分秒 （度*60*4）
        // 地方时差(秒)
        $diffSec = $du * 60 + $min;
        // 原出生时间的基础上+地方时差
        $second = $this->parseStrToSecond($this->utNow);
        if ($degrees > $this->longitude) {
            $time = $second - $diffSec;
            if ($time < 0) {
                $time = ($time + 86400) % 86400;
            }
        } else {
            $time = $diffSec + $second;
        }
        return $this->parseSecondToTimeArr($time);
    }

    /**
     * 2求地方恒星时
     * @return array
     */
    public function getLocalSid(): array
    {
        if ($this->localSid) {
            return $this->localSid;
        }
        // 星历表中的恒星时
        $swehSidSec = $this->parseStrToSecond($this->swephData['t']);//A
        $lmt = $this->getLmt();//B
        // C）固定公式：9.86*（LMT 时+LMT 分/60）
        $tmp = round(9.86 * ($lmt['h'] + $lmt['i'] / 60));// C 求lmt 分秒数
        // D）固定公式：经度*2/3°
        $lngSec = round((int)($this->longitude) * 2 / 3);
        // E）当地恒星时公式：A+B+C-D
        $localSidSec = $swehSidSec + $lmt['all'] + $tmp - $lngSec;
        return $this->parseSecondToTimeArr($localSidSec);
    }

    /**
     * 3 宫位计算mc
     * @return array all是秒
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getMC(): array
    {
        if ($this->mc) {
            return $this->mc;
        }
        /**
         * 1、找出宫位表在同一当地恒星时区间、相邻纬度所对应宫头度数，同一纬度区间、相邻当地恒星时所对应的宫头度数
         * 2、计算在同一时间下纬度差异所影响的宫头度数差异
         * 3、计算纬度差值在所记录纬度区间的占比
         * 4、分别得出在不同纬度下，同一时间的宫位度数（纬度所带来的位置差异）
         * 5、再以不同纬度下，同一时间的宫位度数的差值乘以常数小数。即（地方时所带来的位置差异距离）
         * 6、再以之前多计算纬度差异后的位置+地方时差异距离，即所测时间、地点的宫头位置
         * 7、计算出六个宫头（1.2.3.10.11.12）位置以及各宫大小，反向推出其余六宫的大小
         */
        // 后当地恒星时 - 前当地恒星时			（恒星时之间的差）
        $localSid = $this->getLocalSid();
        $sidtwoData = Placidus::getTwoInfo($localSid['all'], (int)($this->latitude));
        // 1.恒星时之差
        $first = abs($sidtwoData['a']['sec'] - $sidtwoData['b']['sec']);
        if ($sidtwoData['b']['sec'] > 86180) {
            $first = 86400 - 86180;
        }
        // 2 出生时的当地恒星时-前当地恒星时			判断出生时的恒星时与前一恒星时的差
        $secondDiff = abs($localSid['all'] - $sidtwoData['b']['sec']);
        // 3 2 值/1值
        $tmpNum = $secondDiff / $first;
        // 4 固定公式：60*3的值
        $fourRes = round(60 * $tmpNum, 2);
        $min = (int)$fourRes;
        $secRes = $sidtwoData['b']['signdeg'] * 3600 + $min * 60 + round(($fourRes - $min) * 60);
        $res = $this->parseSecondToDeg($secRes);
        $res['sign'] = $sidtwoData['b']['sign'];
        $this->mc = $res;
        return $res;
    }

    /**
     * 宫盘
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getHouseCup(): array
    {
        if ($this->houseCup) {
            return $this->houseCup;
        }
        $localSid = $this->localSid;
        $sidtwoData = Placidus::getTwoInfo($localSid['all'], (int)($this->latitude));
        // 2 宫位表中前后纬度相减，得出纬度的差

        $gongLatDif = abs($sidtwoData['b']['lat']['a'] - $sidtwoData['b']['lat']['b']);

        // 3.出生地纬度-前纬度；（出生地点的纬度与前一个纬度的纬度差）
        $latDiff = $this->latitude - (int)($this->latitude);
        // 1 用前第一宫宫头 - 后第一宫宫头

        $firstList = $this->getHouseDiff($sidtwoData['b']['detail'], $sidtwoData['b']['detail_a']);
        // 2
        $secondList = $this->getHouseDiff($sidtwoData['a']['detail'], $sidtwoData['a']['detail_a']);

        // 求4(3*1)和5(3*2) 单位分
        $fourList = [];
        $fiveList = [];
        foreach ($firstList as $k => $v) {
            $fourList[$k] = $v[0] * $latDiff;
        }
        foreach ($secondList as $k => $v) {
            $fiveList[$k] = $v[0] * $latDiff;
        }
        // 前第一宫头-④或⑤ 得⑥和⑦
        $sixList = $this->getHouseDiffSix($sidtwoData['b']['detail'], $fourList);
        $sevenList = $this->getHouseDiffSix($sidtwoData['a']['detail'], $fiveList);
        // ⑦-⑥的值 *0.1411 得⑧ ⑥+⑧得最后
        $lastList = [];
        foreach ($sevenList as $k => $v) {
            $eight = ($v - $sixList[$k]) * 0.1411;
            $last = floor($eight) + $sixList[$k];
            $lastList[$k] = [
                'deg' => floor($last / 60),
                'min' => $last % 60,
                'sign' => $firstList[$k][1],
            ];
        }
        $mc = $this->getMC();
        $startSign = array_search($lastList[2]['sign'], self::$zodiaSignEnS);
        $houseCup = [
            0 => $lastList[2],
            1 => $lastList[3],
            2 => $lastList[4],
            3 => [
                'deg' => $mc['deg'],
                'min' => $mc['min'],
                'sign' => self::$zodiaSignEnS[(array_search($mc['sign'], self::$zodiaSignEnS) + 6) % 12],
            ],
            4 => [
                'deg' => $lastList[0]['deg'],
                'min' => $lastList[0]['min'],
                'sign' => self::$zodiaSignEnS[(array_search($lastList[0]['sign'], self::$zodiaSignEnS) + 6) % 12],
            ],
            5 => [
                'deg' => $lastList[1]['deg'],
                'min' => $lastList[1]['min'],
                'sign' => self::$zodiaSignEnS[(array_search($lastList[1]['sign'], self::$zodiaSignEnS) + 6) % 12],
            ],
            6 => [
                'deg' => $lastList[2]['deg'],
                'min' => $lastList[2]['min'],
                'sign' => self::$zodiaSignEnS[($startSign + 6) % 12],
            ],
            7 => [
                'deg' => $lastList[3]['deg'],
                'min' => $lastList[3]['min'],
                'sign' => self::$zodiaSignEnS[(array_search($lastList[3]['sign'], self::$zodiaSignEnS) + 6) % 12],
            ],
            8 => [
                'deg' => $lastList[4]['deg'],
                'min' => $lastList[4]['min'],
                'sign' => self::$zodiaSignEnS[(array_search($lastList[4]['sign'], self::$zodiaSignEnS) + 6) % 12],
            ],
            9 => [
                'deg' => $mc['deg'],
                'min' => $mc['min'],
                'sign' => $mc['sign'],
            ],
            10 => $lastList[0],
            11 => $lastList[1],
        ];
        foreach ($houseCup as $k => $v) {
            $tmpSignKey = (int)array_search($v['sign'], self::$zodiaSignEnS);
            $tmpDegs = self::parseDegree($v['deg'] + $tmpSignKey * 30, $v['min']);
            $houseCup[$k]['degs'] = $tmpDegs;
        }
        $this->houseCup = $houseCup;
        return $houseCup;
    }

    /**
     * 行星落点
     * @return array
     */
    public function getPlanet(): array
    {
        // 1.计算出生时的UT时间的秒数
        // $utSecond = ($this->parseStrToSecond($this->utNow) - $this->timeZone * 3600 + 86400) % 86400;
        $utSecond = $this->parseStrToSecond($this->utNow) - $this->timeZone * 3600;
        // 2.出生时占一天的比例(常数小数)
        $utScaleDay = round($utSecond / 86400, 4);
        $list = [];
        $planetEnS = self::getPlanetEnS();
        $zodiaSignEnS = self::$zodiaSignEnS;
        foreach ($planetEnS as $v) {
            $next = Sweph::getDegreeAndSign($this->swephDataNext[$v]);
            $curr = Sweph::getDegreeAndSign($this->swephData[$v]);
            $currSecTm = round($curr['sec'] / 60, 3);
            // 用后一天的行星位置 - 当天的行星位置=（一天内行星移动位置）
            $nextPos = $next['deg'] * 60 + $next['min'] + round($next['sec'] / 60, 3);
            $curPos = ($curr['deg'] * 60 + $curr['min'] + $currSecTm);

            if ($curr['sign'] == $next['sign']) {
                $diff = $next['deg'] * 60 + $next['min'] + round($next['sec'] / 60, 3) - ($curr['deg'] * 60 + $curr['min'] + $currSecTm);
            } else {
                $curSignI = array_search($curr['sign'], $zodiaSignEnS);
                $nexSignI = array_search($next['sign'], $zodiaSignEnS);
                $signDiff = $nexSignI - $curSignI;
                if ($signDiff == 1 || $signDiff == -11) {
                    // 顺行
                    $diff = $nextPos + 30 * 60 - $curPos;
                } else {
                    // 逆行
                    $diff = $nextPos - 30 * 60 - $curPos;
                }
            }
            // 各个行星的度数差*常数小数2算出的结果（当天行星移动到后一天的行星位置的移动距离）
            $remove = $diff * $utScaleDay;// 单位分
            // 当天00:00时的行星位置加上出生时间与00:00的移动位置     出生时行星位置
            $pos = $curr['deg'] * 60 + $curr['min'] + $currSecTm + $remove;
            $posSecond = floor($pos * 60);
            $tmpRes = $this->parseSecondToDeg($posSecond);
            $tmpRes['sign'] = $curr['sign'];
            $tmpKey = array_search($tmpRes['sign'], self::$zodiaSignEnS);
            if ($tmpRes['deg'] >= 30) {
                $tmpRes['deg'] = $tmpRes['deg'] - 30;
                $tmpKey = ($tmpKey + 1) % 12;
                $tmpRes['sign'] = self::$zodiaSignEnS[$tmpKey];
            }
            $tmpRes['degs'] = self::parseDegree($tmpRes['deg'] + $tmpKey * 30, $tmpRes['min'], $tmpRes['sec']);
            $tmpRes['planet'] = $v;
            $tmpRes['sui'] = $diff > 0 ? 1 : 0;

            unset($tmpRes['all']);
            $list[] = $tmpRes;
        }
        return $list;
    }

    /**
     * 输出行星信息
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws Exception
     * @throws ModelNotFoundException
     */
    public function getPlanetInfo(): array
    {
        $planet = $this->getPlanet();
        $hc = $this->getHouseCup();
        $hourseNum = $this->getHourseNum();
        $resPlanet = [];
        foreach ($planet as $k => $v) {
            $planetList = self::$planet[$v['planet']];
            $signKey = array_search($v['sign'], AstrologyUtil::$zodiaSignEnS);
            $signInfo = self::$zodiacSign[$signKey];
            $tmpDegs = (int)($v['degs'] - $hc[0]['degs'] + 360) % 360;
            $hs = $this->getHsNum($tmpDegs, $hourseNum);
            $resPlanet[] = [
                'hs' => $hs,
                'deg' => $v['deg'],
                'min' => $v['min'],
                'sec' => $v['sec'],
                'planet' => [
                    'cn' => $planetList[3],
                    'glyph' => $planetList[1],
                    'en' => $planetList[2],
                ],
                'sign' => [
                    // ['Aries', 0, 'red', '♈', 'AR', '白羊座']
                    'cn' => $signInfo[5],
                    'en' => $signInfo[0],
                    'glyph' => $signInfo[3],
                    'en_s' => $signInfo[4],
                    'number' => $signInfo[1],
                ],
                'degs' => $v['degs'],
                'sui' => $v['sui'],
            ];
        }
        return $resPlanet;
    }

    /**
     * 获得行星属性
     * @return array
     */
    public function getPlanetAttr(): array
    {
        /**
         *  火元素     土元素     风元素     水元素
         *  白羊座     魔羯座     天秤座     巨蟹座     主要星座
         *  狮子座     金牛座     水瓶座     天蝎座     固定星座
         *  射手座     处女座     双子座     双鱼座     变动星座
         */
        // 根据属性划分
        $list = [
            'huo' => ['AR', 'LE', 'SG'],
            'tu' => ['CP', 'TA', 'VI'],
            'feng' => ['LI', 'AQ', 'GE'],
            'shui' => ['CN', 'SC', 'PI'],
        ];
        // 三分法 主要 固定和变动
        $list1 = [
            'base' => ['AR', 'CP', 'LI', 'CN'],
            'fixed' => ['LE', 'TA', 'AQ', 'SC'],
            'change' => ['SG', 'VI', 'GE', 'PI'],
        ];
        // 阳属性星座列表
        $listYang = ['AR', 'GE', 'LE', 'LI', 'SG', 'AQ'];
        // 初始化
        $res = [
            'wx' => ['huo' => 0, 'tu' => 0, 'feng' => 0, 'shui' => 0], // 火 土 风 水 个数
            'base' => ['base' => 0, 'fixed' => 0, 'change' => 0], // 三分
            'yang' => 0, // 阳
            'yin' => 0, // 阴
            's' => 0, // 南
            'n' => 0, // 北
            'e' => 0, // 东
            'w' => 0, // 西
        ];
        $planet = $this->getPlanet();
        $planetList = self::$planetList;
        foreach ($planet as $v) {
            if (!in_array($v['planet'], $planetList)) {
                continue;
            }
            foreach ($list as $k1 => $v1) {
                if (in_array($v['sign'], $v1)) {
                    $res['wx'][$k1]++;
                    break;
                }
            }
            foreach ($list1 as $k2 => $v2) {
                if (in_array($v['sign'], $v2)) {
                    $res['base'][$k2]++;
                    break;
                }
            }
            if (in_array($v['sign'], $listYang)) {
                $res['yang']++;
            } else {
                $res['yin']++;
            }
            if ($v['degs'] < 180) {
                $res['n']++;
            } else {
                $res['s']++;
            }
            if ($v['degs'] < 90 || $v['degs'] > 270) {
                $res['w']++;
            } else {
                $res['e']++;
            }
        }
        return $res;
    }

    /**
     * 用前第一宫宫头 - 后第一宫宫头
     * @param array $before
     * @param array $affter
     * @return array 返回 【角度 和星座】
     */
    protected function getHouseDiff(array $before, array $affter): array
    {
        $res = [];
        foreach ($before as $k => $v) {
            $b = Sweph::getDegreeAndSign($v);
            $a = Sweph::getDegreeAndSign($affter[$k]);
            $aSec = round($a['sec'] / 60, 3);
            $bSec = round($b['sec'] / 60, 3);
            $diff = ($b['deg'] * 60 + $b['min'] + $bSec) - ($a['deg'] * 60 + $a['min'] + $aSec);
            $res[] = [abs($diff), $b['sign']];
        }
        return $res;
    }

    /**
     * 算宫头第6步
     * @param array $array 第一宫宫头数组
     * @param array $diff 第5步差值④或⑤
     * @return array 单位为分
     */
    protected function getHouseDiffSix(array $array, array $diff): array
    {
        $res = [];
        foreach ($array as $k => $v) {
            $b = Sweph::getDegreeAndSign($v);
            $bSec = round($b['sec'] / 60, 3);
            $diffTmp = ($b['deg'] * 60 + $b['min'] + $bSec) - floor($diff[$k]);
            $res[$k] = $diffTmp;
        }
        return $res;
    }

    /**
     * 把时间字符串转成秒数
     * @param string $timeStr 时间字符串 00:00:00
     * @return int
     */
    public function parseStrToSecond(string $timeStr): int
    {
        $second = strtotime("2000-01-01 {$timeStr}");
        if ($second == false) {
            return 0;
        }
        $second1 = strtotime('2000-01-01 00:00:00');
        return (int)($second - $second1);
    }

    /**
     * 把秒转换成 时分秒数组
     * @param int $second
     * @return array
     */
    public static function parseSecondToTimeArr(int $second): array
    {
        $hour = (int)($second / 3600);
        $diff = $second % 3600;
        $min = (int)($diff / 60);
        $sec = $diff % 60;
        $tmpSecond = $second;
        if ($second >= 86400) {
            $tmpSecond = $second - 86400;
        }
        return [
            'h' => $hour % 24,
            'i' => $min,
            's' => $sec,
            'all' => $tmpSecond,
        ];
    }

    /**
     * 分转化成角 分 秒
     * @param int $second
     * @return array
     */
    public static function parseSecondToDeg(int $second): array
    {
        $deg = (int)($second / 3600);
        $diff = $second % 3600;
        $min = (int)($diff / 60);
        $sec = $diff % 60;
        return [
            'deg' => $deg,
            'min' => $min,
            'sec' => $sec,
            'all' => $second,
        ];
    }

    /**
     * 把 度，分 ，秒 转成度数
     * @param int $degree
     * @param int $minute
     * @param int $second
     * @return float
     */
    public static function parseDegree(int $degree, int $minute = 0, int $second = 0): float
    {
        $number = $degree + $minute / 60 + $second / 3600;
        return round($number, 5);
    }

    /**
     * 每个宫里有多少个行星
     * @param array $sort 角度
     * @param array $sortPos 行星列表(按角度降序排列的序号)
     * @return array
     */
    public static function countPlanetsInEachHouse(array $sort, array $sortPos): array
    {
        // 计算每个宫里的行星数量
        // 取消在程序中未初始化的任何变量
        // 重置每个宫里的行星数量
        for ($i = 1; $i <= 12; $i++) {
            $nopih[$i] = 0;
        }
        $numPlanets = count($sort);
        // 看宫里有多少行星
        for ($i = 0; $i <= $numPlanets - 1; $i++) {
            // get sign planet is in, since the sign and the house are the same
            $pNum = $sortPos[$i];
            $temp = floor($sort[$pNum] / 30) + 1;
            $temp = $temp % 12;
            if ($temp == 0) {
                $temp = 12;
            }
            $nopih[$temp]++;
        }
        return $nopih;
    }

    /**
     * 把带小数的度数转成秒
     * @param float $deg
     * @return float|integer
     */
    public static function parseDegToSec(float $deg): float | int
    {
        $res = 0;
        $degInt = floor($deg);
        $degD = $deg - $degInt;// 度小数部分
        $min = $degD * 60;
        $minInt = floor($min);
        $minD = $min - $minInt;// 分小数部分
        $sec = floor($minD * 60);
        return $degInt * 60 * 60 + $min * 60 + $sec;
    }

    /**
     * 获得数据库里的星座表全部数据用的键名(天体)
     */
    public static function getPlanetEnS(): array
    {
        $planetEnS = self::$planetList;
        $planetEnS[] = 'node';
        return $planetEnS;
    }

    /**
     * 获得每个宫的起始和结束区间
     * @return array
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getHourseNum(): array
    {
        $hc = $this->getHouseCup();
        $ascendant = $hc[0]['degs'];
        $list = [];
        for ($i = 1; $i <= 12; $i = $i + 1) {
            $angle = $ascendant - $hc[$i - 1]['degs'];
            $list[$i] = $angle > 0 ? (360 - $angle) : (-(int)$angle + 360) % 360;
        }
        $result = [];
        foreach ($list as $k => $v) {
            if ($k == 12) {
                $result[$k] = [$v, 360];
                continue;
            }
            $result[$k] = [$v, $list[$k + 1]];
        }
        return $result;
    }

    /**
     * 行星在哪个宫
     * @param float|int $degs 角度
     * @param array $arr
     * @return int
     */
    private function getHsNum(float | int $degs, array $arr): int
    {
        $ret = 1;
        foreach ($arr as $k => $v) {
            if ($degs >= $v[0] && $degs < $v[1]) {
                $ret = $k;
                break;
            }
        }
        return $ret;
    }
}
