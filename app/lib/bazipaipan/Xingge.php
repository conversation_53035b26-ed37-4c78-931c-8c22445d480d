<?php
// +----------------------------------------------------------------------
// | 八字排盘-性格分析
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use calendar\Ex;
use calendar\Huangli;
use calendar\plugin\WuXing;

class Xingge
{
    /**
     * 用户八字
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 用户神煞
     * @var array
     */
    protected array $shenShaRes = [];

    /**
     * 喜用神
     * @var array
     */
    protected array $xy = [];

    /**
     * @var string
     */
    protected string $wr = '';

    /**
     * 旺度
     * @var string
     */
    protected string $wangDu = '';

    /**
     * 初始化
     * @param array $jiNian
     * @param array $userGod
     * @param array $shenShaRes
     * @param array $xy
     */
    public function __construct(array $jiNian, array $userGod, array $shenShaRes, array $xy)
    {
        $this->jiNian = $jiNian;
        $this->userGod = $userGod;
        $this->shenShaRes = $shenShaRes;
        $this->xy = $xy;
    }

    /**
     * 设置旺度
     * @param string $wangDu
     */
    public function setWangDu(string $wangDu)
    {
        $this->wangDu = $wangDu;
    }

    /**
     * 设置喜用忌闲仇
     * @param array $xy
     */
    public function setXy(array $xy)
    {
        $this->xy = $xy;
    }

    /**
     * 设置旺弱
     * @param string $wr
     */
    public function setWr(string $wr)
    {
        $this->wr = $wr;
    }

    /**
     * 获得性格
     * @return array
     */
    public function getXingGe(): array
    {
        $jiNian = $this->jiNian;
        $jnGx1 = $this->getJnwx($jiNian);
        $jnWx = [];
        $jnWxCount = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $jnYy = [];
        $yangTgNum = 0;
        $yangDzNum = 0;
        $gxDzArr = [];
        foreach ($jnGx1 as $k => $v) {
            $jnWx[$k] = $v['wx'];
            $jnWxCount[$v['wx'][0]]++;
            $jnWxCount[$v['wx'][1]]++;
            $jnYy[$k] = $v['yy'];
            if ($v['yy'][0] == '阳') {
                $yangTgNum++;
            }
            if ($v['yy'][1] == '阳') {
                $yangDzNum++;
            }
            $gxDzArr[$k] = $this->getGxToDz($k, $jiNian);
        }
        $dtg = $jiNian['d'][0];
        $ddz = $jiNian['d'][1];
        $dgz = $dtg . $ddz;
        $mdz = $jiNian['m'][1];
        $ydz = $jiNian['y'][1];
        $hdz = $jiNian['h'][1];
        $useGod = $this->userGod;
        $godT = $useGod['god_t'];
        $godZ = $useGod['god_z'];
        $godHide = $useGod['god_hide'];
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $godH2Dz = array_merge($godHide['year']['hide'], $godHide['month']['hide'], $godHide['day']['hide'], $godHide['hour']['hide']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godH2DzWxCount = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $godH2DzWx = [];
        $wxAttr = WuXing::GZ_TO_WX;

        foreach ($godH2Dz as $v) {
            $wxTmp = $wxAttr[$v];
            $godH2DzWxCount[$wxTmp]++;
            $godH2DzWx[] = $wxTmp;
        }
        $godTH = array_merge(array_values($godT), $godH2);
        $godZH = array_merge(array_values($godZ), $godH2);
        $godTHSum = array_count_values($godTH);
        $godZHSum = array_count_values($godZH);
        $godTZSum = array_count_values($godTZ);
        $godTzSumSs = $godTZSum['食神'] ?? 0;
        $godTzSumSg = $godTZSum['伤官'] ?? 0;
        $godTHQs = $godTHSum['七杀'] ?? 0;
        $godTHSumZy = $godTHSum['正印'] ?? 0;
        $godTHSumPy = $godTHSum['偏印'] ?? 0;
        $godTHSumZc = $godTHSum['正财'] ?? 0;
        $godTHSumPc = $godTHSum['偏财'] ?? 0;
        $result = [];
        $resKey = [];
        $key1 = "日元为{$dtg}";
        $resKey[] = $key1;
        $list1 = [
            '甲' => '甲或乙', '乙' => '甲或乙', '丙' => '丙丁', '丁' => '丙丁', '戊' => '戊己', '己' => '戊己', '庚' => '庚辛', '辛' => '庚辛', '壬' => '壬癸', '癸' => '壬癸',
        ];
        $resKey[] = "日元为{$list1[$dtg]}";

        if ($dtg == '甲' && in_array($mdz, ['寅', '卯', '辰'])) {
            $resKey[] = $key1;
        }
        // 月支十神
        $key1 = "月支为{$godZ['month']}";
        $key1 .= $godZ['month'] == '偏印' ? '(不看藏干)' : '';
        $resKey[] = $key1;
        // 月柱十二长生
        $terrain = $this->getTerrain();
        $resKey[] = "月柱十二长生为{$terrain['month']}";
        if (in_array($terrain['day'], ['死', '绝'])) {
            $resKey[] = '日柱十二长生为死或绝';
        }
        // 天干属中阳属性>=3
        if ($yangTgNum >= 3) {
            $resKey[] = '天干属中阳属性>=3';
        }
        // 喜用神为七杀或正官
        $xy = $this->xy;
        if ($xy['shen']['yong'] == '官杀' || $xy['shen']['xi'] == '官杀') {
            $resKey[] = '喜用神为七杀或正官';
        } elseif ($xy['shen']['yong'] == '印枭' || $xy['shen']['xi'] == '印枭') {
            $resKey[] = '喜用神为印';
        }
        // 日元五行属
        $resKey[] = "日元五行属{$jnWx['d'][0]}";
        // 日元五行属火且月支五行为木
        if ($jnWx['d'][0] === '火' && $jnWx['m'][1] === '木') {
            $resKey[] = '日元五行属火且月支五行为木';
        }
        if ($godTHQs >= 3) {
            $resKey[] = '八字七杀>=3(包含藏干)';
        }
        // 八字命局水属性大于等于三且土小于等于一
        if ($jnWxCount['水'] >= 3 && $jnWxCount['土'] <= 1) {
            $resKey[] = '八字命局水属性大于等于三且土小于等于一';
        }
        $wangDu = $this->wangDu;
        if (in_array($wangDu, ['身旺格', '从旺格'])) {
            $resKey[] = '身旺格或从旺格';
        } else {
            if ($wangDu == '从弱格') {
                $resKey[] = '从弱格';
            }
            $resKey[] = '身弱或从弱格';
        }
        if (($yangTgNum + $yangDzNum) == 8) {
            $resKey[] = '八字原局柱中阴阳属性皆为阳';
        }
        if ($godH2DzWxCount['水'] >= 3) {
            $resKey[] = '八字五行水个数大于等于三(看藏干)';
        }
        if ($godH2DzWxCount['火'] >= 3) {
            $resKey[] = '八字五行火个数大于等于三(看藏干)';
        }
        if (in_array($hdz, ['子', '午', '卯', '酉'])) {
            $resKey[] = '时支为子午卯酉';
        } elseif (in_array($hdz, ['辰', '戌', '丑', '未'])) {
            $resKey[] = '时支为辰戌丑未';
        } else {
            $resKey[] = '时支为寅申巳亥';
        }
        // 八字命局火数量>=4
        if ($jnWxCount['火'] >= 4) {
            $resKey[] = '八字命局火数量>=4';
        }
        // 日元旺，印星大于等于三且为忌神
        $wangRuoDay = $this->wr;
        if ($wangRuoDay === '旺' && $xy['shen']['ji'] === '印枭' && ($godTHSumZy + $godTHSumPy) >= 3) {
            $resKey[] = '日元旺，印星大于等于三且为忌神';
        }
        if ($wangRuoDay == '弱' && $jnWx['d'][0] == '水' && $jnWx['d'][1] == '火') {
            $resKey[] = '日元弱且日柱天干和地支五行为水和火';
        }
        // 天干有偏财
        if (in_array('偏财', $godT)) {
            $resKey[] = '天干有偏财';
        }
        if (($godTHSumZc + $godTHSumPc) >= 4) {
            // 正财或偏财数量>=4
            $resKey[] = '正财或偏财数量>=4';
        }
        if (in_array('正财', $godT) && in_array('偏财', $godT)) {
            $resKey[] = '天干同时存在正财和偏财';
        }
        if (!in_array('正官', $godTZ) && !in_array('七杀', $godTZ)) {
            $resKey[] = '八字原局没有正官和七杀';
        }
        $list2 = [
            '比劫' => ['比肩', '劫财'], '印枭' => ['正印', '偏印'],
            '官杀' => ['正官', '七杀'], '才财' => ['正财', '偏财'], '食伤' => ['食神', '伤官'],
        ];
        $arr = $list2[$xy['shen']['ji']];
        // 正官为忌神
        // 比肩为忌神
        // 劫财为忌神
        // 正财为忌神
        // 偏印为忌神
        // 伤官为忌神且同柱有劫财
        // 偏财为忌神
        foreach ($arr as $v) {
            $resKey[] = $v . '为忌神';
        }
        if ($xy['shen']['yong'] == '官杀' || $xy['shen']['xi'] == '官杀') {
            $resKey[] = '正官为喜神或用神';
        } elseif ($xy['shen']['yong'] == '食伤' || $xy['shen']['xi'] == '食伤') {
            $resKey[] = '食神为喜神或用神';
            // 天干有伤官且地支藏干也有藏干且伤官为喜神或用神
            if (in_array('伤官', $godT) && in_array('伤官', $godH2)) {
                $resKey[] = '天干有伤官且地支藏干也有藏干且伤官为喜神或用神';
            }
        } elseif ($xy['shen']['yong'] == '才财' || $xy['shen']['xi'] == '才财') {
            $resKey[] = '偏财为喜神或用神';
        } else {
            $resKey[] = '伤官为喜神或用神';
        }
        $shenSha = $this->shenShaRes;
        // 神煞有魁罡贵人
        // 神煞有华盖
        // 食神同柱下有文昌神煞
        // 驿马神煞对应地支为空亡
        $boolKui = false;
        $boolHua = false;
        $yiMaKey = [];
        $wenCanKey = [];
        $list3 = ['y' => 'year', 'm' => 'month', 'd' => 'day', 'h' => 'hour'];
        unset($shenSha['o']);
        foreach ($shenSha as $k => $v) {
            if (in_array('魁罡贵人', $v)) {
                $boolKui = true;
            }
            if (in_array('华盖', $v)) {
                $boolHua = true;
            }
            if (in_array('驿马', $v)) {
                $yiMaKey[] = $k;
            }
            if (in_array('文昌', $v)) {
                $wenCanKey[] = $list3[$k];
            }
        }
        if ($boolKui) {
            $resKey[] = '神煞有魁罡贵人';
            if (BaziCommon::getXianChong($ydz . $ddz)) {
                $resKey[] = '魁罡贵人同柱地支被冲';
            }
            if (in_array('冲', $gxDzArr['d'])) {
                $resKey[] = '日柱有魁罡贵人且日支被它柱所冲';
            }
        }
        if ($boolHua) {
            $resKey[] = '神煞有华盖';
        }
        $boolYiMa = false;
        $bool1 = false;
        $boolYangRen = false;
        $boolWenCan = false;
        // 正官和正印或偏印同柱
        $boolZhenYin = false;
        // 食神同柱十二长生为帝旺
        $ssTerrain = false;
        // 天干正印且同柱地支为偏印
        $zyPy = false;
        // 同柱天干地支为偏印和食神
        $pySs = false;
        // 身弱格，天干有偏印且偏印同柱地支有偏印
        $pyRuo = false;
        // 同柱天干和地支十神皆为偏财
        $pcIn1 = false;
        // 天干比肩且同柱地支为正官
        $zgIn1 = false;
        // 正印或偏印柱名
        $tianDeKey = [];
        // 同柱八字同柱有偏印和劫财
        $pyJc = false;
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            $arrT2 = array_intersect(['正官', '正印'], [$v, $godZ[$k]]);
            $arrT3 = array_intersect(['正官', '偏印'], [$v, $godZ[$k]]);
            $arrT4 = array_intersect(['食神', '偏印'], [$v, $godZ[$k]]);
            if (count($arrT2) == 2 || count($arrT3) == 2) {
                $boolZhenYin = true;
            }
            $arrT1 = array_intersect(['伤官', '劫财'], [$v, $godZ[$k]]);
            if (count($arrT1) == 2 && in_array('伤官', $arr)) {
                $bool1 = true;
            }
            if (in_array($k, $wenCanKey) && ($v == '食神' || $godZ[$k] == '食神')) {
                $boolWenCan = true;
            }
            if (in_array($v, $arr) || in_array($godZ[$k], $arr)) {
                // 忌神对应柱下有羊刃
                if (in_array('羊刃', $shenSha[$k1])) {
                    $boolYangRen = true;
                }
            }
            if (($v == '食神' || $godZ[$k] == '食神') && $terrain[$k] == '帝旺') {
                $ssTerrain = true;
            }
            if ($v == '正印' || $godZ[$k] == '偏印') {
                $tianDeKey[] = substr($k, 0, 1);
                $zyPy = true;
            }
            if (count($arrT4) == 2) {
                $pySs = true;
            }
            if ($wangDu == '身弱格' && $v == $godZ[$k] && $v == '偏印') {
                $pyRuo = true;
            }
            if ($v == $godZ[$k] && $v == '正官') {
                $zgIn1 = true;
            }
            if ($v == $godZ[$k] && $v == '偏财') {
                $pcIn1 = true;
            }
            $arrT4 = array_intersect(['劫财', '偏印'], [$v, $godZ[$k]]);
            if (count($arrT4) == 2) {
                $pyJc = true;
            }
        }
        // 正印或偏印同柱有天德贵人
        $boolYinDe = false;
        foreach ($tianDeKey as $v) {
            if (in_array('天德贵人', $shenSha[$v])) {
                $boolYinDe = true;
                break;
            }
        }
        if ($boolYinDe) {
            $resKey[] = '正印或偏印同柱有天德贵人';
        }
        if ($pyJc) {
            $resKey[] = '同柱八字同柱有偏印和劫财';
        }
        if ($pyRuo) {
            $resKey[] = '身弱格，天干有偏印且偏印同柱地支有偏印';
        }
        if ($pcIn1) {
            $resKey[] = '同柱天干和地支十神皆为偏财';
        }
        if ($zgIn1) {
            $resKey[] = '天干比肩且同柱地支为正官';
        }
        if ($ssTerrain) {
            $resKey[] = '食神同柱十二长生为帝旺';
        }
        if ($pySs) {
            $resKey[] = '同柱天干地支为偏印和食神';
        }
        if ($zyPy) {
            $resKey[] = '天干正印且同柱地支为偏印';
        }
        if ($boolZhenYin) {
            $resKey[] = '正官和正印或偏印同柱';
        }
        if ($boolYangRen) {
            $resKey[] = '忌神对应柱下有羊刃';
        }
        if ($boolWenCan) {
            $resKey[] = '食神同柱下有文昌神煞';
        }
        if ($bool1) {
            $resKey[] = '伤官为忌神且同柱有劫财';
        }
        if (in_array('阴差阳错', $shenSha['d'])) {
            $resKey[] = '日柱神煞为阴差阳错';
        }
        $boolYiMaZG = false;
        // 驿马同柱地支被它柱刑冲破害
        $boolYma2 = false;
        // 驿马同柱地支不被它柱刑冲迫害
        $boolYma3 = true;
        // 驿马同柱地支被它柱所冲
        $boolYma4 = false;
        foreach ($yiMaKey as $v) {
            $kongW1 = explode(',', Huangli::getKongWangbyGz(implode('', $jiNian[$v])));
            if (in_array($ydz, $kongW1)) {
                $boolYiMa = true;
            }
            $tmp1 = $list3[$v];
            if ($godT[$tmp1] == '正官') {
                $boolYiMaZG = true;
            }
            $gx1 = $gxDzArr[$v];
            if (in_array('冲', $gx1)) {
                $boolYma4 = true;
            }
            $gxT1 = array_intersect(['刑', '冲', '破', '害'], $gx1);
            if ($gxT1) {
                $boolYma3 = false;
            }
            if (count($gxT1) == 4) {
                $boolYma2 = true;
            }
        }
        if ($boolYma2) {
            $resKey[] = '驿马同柱地支被它柱刑冲破害';
        }
        if ($yiMaKey && $boolYma3) {
            $resKey[] = '驿马同柱地支不被它柱刑冲迫害';
        }
        if ($boolYma4) {
            $resKey[] = '驿马同柱地支被它柱所冲';
        }
        if ($boolYiMa) {
            $resKey[] = '驿马神煞对应地支为空亡';
        }
        if ($boolYiMaZG) {
            $resKey[] = '驿马同柱天干为正官';
        }

        // 日柱对应神煞有贵人
        foreach ($shenSha['d'] as $v) {
            if (str_contains($v, '贵人')) {
                $resKey[] = '日柱对应神煞有贵人';
                break;
            }
        }
        // 天干有印星且为喜用神
        if (($xy['shen']['yong'] == '印枭' || $xy['shen']['xi'] == '印枭') && array_intersect(['正印', '偏印'], $godT)) {
            $resKey[] = '天干有印星且为喜用神';
        }
        // 同柱天干和地支五行属性都为水或火
        foreach ($jnWx as $v) {
            if ($v[0] == $v[1] && in_array($v[0], ['水', '火'])) {
                $resKey[] = '同柱天干和地支五行属性都为水或火';
                break;
            }
        }

        // 华盖同柱地支空亡且为忌神(未接入)
        // 身旺且印星>=4(看藏干)
        if (($godTHSumZy + $godTHSumPy) >= 4) {
            $resKey[] = '印星>=4(看藏干)';
            //印星>=4(看藏干)
            if ($wangDu === '身旺格') {
                $resKey[] = '身旺且印星>=4(看藏干)';
            }
        }
        // 地支子午卯酉占三个
        $dzArr = array_column($jiNian, 1);
        $arr2 = array_intersect(['子', '午', '卯', '酉'], $dzArr);
        if (count($arr2) >= 3) {
            $resKey[] = '地支子午卯酉占三个';
        }
        // 地支为寅申巳亥
        $arr2 = array_intersect(['寅', '申', '巳', '亥'], $dzArr);
        if (count($arr2) == 4) {
            $resKey[] = '地支为寅申巳亥';
        }
        // 日柱为戊己
        if (($dtg . $ddz) == '戊己') {
            $resKey[] = '日柱为戊己';
        }
        // 日干为戊
        if ($dtg == '戊') {
            $resKey[] = '日干为戊';
        }
        if (in_array('子', $dzArr) && in_array('卯', $dzArr)) {
            $resKey[] = '地支有子卯';
        }
        if (in_array('辰', $dzArr) && in_array('巳', $dzArr)) {
            $resKey[] = '地支有辰巳';
        }
        if (in_array('午', $dzArr) && in_array('巳', $dzArr)) {
            $resKey[] = '地支中有巳和午';
        }
        // 八字地支有戌和亥
        if (in_array('戌', $dzArr) && in_array('亥', $dzArr)) {
            $resKey[] = '八字地支有戌和亥';
        }
        $dzNums = array_count_values($dzArr);
        $shuNum = $dzNums['戌'] ?? 0;
        if ($shuNum >= 2) {
            $resKey[] = '八字地支戌>=2';
        }
        // 日柱为甲戌，乙丑，戊寅，己卯
        if (in_array($dgz, ['甲戌', '乙丑', '戊寅', '己卯'])) {
            $resKey[] = '日柱为甲戌，乙丑，戊寅，己卯';
        }
        // 日干为乙
        if ($dtg === '乙') {
            $resKey[] = '日干为乙';
        }
        // 天干有乙和庚
        $tgArr = array_column($jiNian, 0);
        if (in_array('乙', $tgArr) && in_array('庚', $tgArr)) {
            $resKey[] = '天干有乙和庚';
        }
        // 天干同时存在壬或戊
        if (in_array('壬', $tgArr) && in_array('戊', $tgArr)) {
            $resKey[] = '天干同时存在壬或戊';
        }

        // 八字原局阴属性和阳属性相同
        if (($yangDzNum + $yangTgNum) == 4) {
            $resKey[] = '八字原局阴属性和阳属性相同';
        }
        if (($yangDzNum + $yangTgNum) == 0) {
            $resKey[] = '八字原局全都是阴属性';
        }
        // 月干或时干为正印
        if ($godT['month'] == '正印' || $godT['hour'] == '正印') {
            $resKey[] = '月干或时干为正印';
        }
        // 天干有正官和七杀
        if (in_array('正官', $godT) && in_array('七杀', $godT)) {
            $resKey[] = '天干有正官和七杀';
        }
        if (in_array('正印', $godT) && in_array('食神', $godH2)) {
            $resKey[] = '天干有正印，且地支藏干有食神';
        }
        if ($godT['month'] == '伤官') {
            $resKey[] = '月干十神为伤官';
        }
        // 食神数量>=3
        if ($godTzSumSs >= 3) {
            $resKey[] = '食神数量>=3';
        }
        if (in_array('伤官', $godT) && in_array('正官', $godT)) {
            $resKey[] = '八字天干同时存在伤官和正官';
        }
        if ($wangDu == '身弱格' && $jnWxCount['水'] >= 4 && $jnWxCount['木'] <= 2) {
            $resKey[] = '身弱水数量>=4且木<=2';
        }
        // 伤官数量>=4
        if ($godTzSumSg >= 4) {
            $resKey[] = '伤官数量>=4';
        }
        $gx2 = WuXing::getWuxingGuanXi($this->xy['xy']['yong'], $jnWx['d'][0]);
        if (in_array($wangDu, ['身旺格', '从旺格']) && in_array($gx2, ['生', '扶'])) {
            $resKey[] = '身旺或从旺且用神生扶日元';
        }
        return array_unique($resKey);
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnwx(array $jiNian): array
    {
        $result = [];
        $wuXingAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        return $result;
    }

    /**
     * 地支与其它地的关系
     * @param string $key 键名
     * @param array $jiNian 纪年
     */
    protected function getGxToDz(string $key, array $jiNian): array
    {
        $dz = $jiNian[$key][1];
        $result = [];
        foreach ($jiNian as $k => $v) {
            if ($k === $key) {
                continue;
            }
            $dz1 = $v[1];
            $str = $dz . $dz1;
            if (BaziCommon::getXianXin($str)) {
                $result[0] = '刑';
            }
            if (BaziCommon::getXianChong($str)) {
                $result[1] = '冲';
            }
            if (BaziCommon::getXianPo($str)) {
                $result[2] = '破';
            }
            if (BaziCommon::getXianHai($str)) {
                $result[2] = '害';
            }
        }
        return array_values($result);
    }

    /**
     * 获取地势
     * @return array
     */
    public function getTerrain(): array
    {
        $tgdz = $this->jiNian;
        $terrain = Ex::getTerrainData();
        return [
            'year' => $terrain[$tgdz['d'][0] . $tgdz['y'][1]] ?? '',
            'month' => $terrain[$tgdz['d'][0] . $tgdz['m'][1]] ?? '',
            'day' => $terrain[$tgdz['d'][0] . $tgdz['d'][1]] ?? '',
            'hour' => $terrain[$tgdz['d'][0] . $tgdz['h'][1]] ?? '',
        ];
    }
}
