<?php
// +----------------------------------------------------------------------
// | 八字排盘-健康
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Health
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 用户基础信息
     * @var array
     */
    protected array $base;

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @var array[]
     */
    protected array $shaShenRes;

    /**
     * @var array
     */
    protected array $xy;

    /**
     * 初始化
     * @param string $birthday 生日：1998-03-20 10:0:00
     * @param int $gender 性别：0男 1 女
     * @throws Exception
     */
    public function __construct(string $birthday, int $gender = 0)
    {
        // 农历类对象
        $this->lunar = Ex::date($birthday)->sex($gender);
        $this->base = $this->lunar->getLunarByBetween();
        $godT = $this->lunar->getGod();
        $godH = $this->lunar->_getGod();
        $godZ = [
            'year' => $godH['year']['god'][0],
            'month' => $godH['month']['god'][0],
            'day' => $godH['day']['god'][0],
            'hour' => $godH['hour']['god'][0],
        ];
        $this->userGod = [
            't' => $godT,
            'd' => $godZ,
            'h' => $godH,
        ];
        $jiNian = $this->base['jinian'];
        $jnWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $jnWx[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        $this->base['jn_wx'] = $jnWx;
        $this->shaShen = new ShaShen();
        $shaShenRes = $this->shaShen->detail($jiNian, $gender);
        $this->shaShenRes = $shaShenRes;
    }

    /**
     * 设置喜用忌闲仇
     * @param array $xy
     */
    public function setXy(array $xy)
    {
        $this->xy = $xy;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function detail()
    {
        $base = $this->base;
        $xy = $this->xy;
        $jiNian = $base['jinian'];
        $ydz = $jiNian['y'][1];
        $mdz = $jiNian['m'][1];
        $ddz = $jiNian['d'][1];
        $hdz = $jiNian['h'][1];
        $ytg = $jiNian['y'][0];
        $mtg = $jiNian['m'][0];
        $dtg = $jiNian['d'][0];
        $htg = $jiNian['h'][0];
        $tgArr = array_column($jiNian, 0);
        $dzArr = array_column($jiNian, 1);
        $ytgWx = $base['jn_wx']['y']['wx'][0];
        $ydzWx = $base['jn_wx']['y']['wx'][1];
        $mtgWx = $base['jn_wx']['m']['wx'][0];
        $mdzWx = $base['jn_wx']['m']['wx'][1];
        $dtgWx = $base['jn_wx']['d']['wx'][0];
        $ddzWx = $base['jn_wx']['d']['wx'][1];
        $htgWx = $base['jn_wx']['h']['wx'][0];
        $hdzWx = $base['jn_wx']['h']['wx'][1];
        $shaShenRes = $this->shaShenRes;
        $dgz = $dtg . $ddz;
        $mgz = $mtg . $mdz;
        $ygz = $ytg . $ydz;
        $hgz = $htg . $hdz;
        $godT = $this->userGod['t'];
        $godTS = $this->getGodSum(array_values($godT));
        $godZ = $this->userGod['d'];
        $godH = $this->userGod['h'];
        $terrain = $this->lunar->getTerrain();
        $godH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['day']['god'], $godH['hour']['god']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTZS = $this->getGodSum($godTZ);
        $godH2S = $this->getGodSum($godH2);
        $godTH = array_merge(array_values($godT), array_values($godH2));
        $godTHS = $this->getGodSum($godTH);
        $kW = explode(',', Huangli::getKongWangbyGz($dgz));
        $arrGx = [
            'y' => $this->getGx1('y', $jiNian),
            'm' => $this->getGx1('m', $jiNian),
            'd' => $this->getGx1('d', $jiNian),
            'h' => $this->getGx1('h', $jiNian),
        ];
        $baziEx = new BaziEx($this->lunar);
        $wangDu = $baziEx->getWangDu();
        $listWx = [
            '金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0,
        ];
        $listWx2 = $listWx;
        $listTgWx = $listWx;
        foreach ($base['jn_wx'] as $k => $v) {
            $listWx[$v['wx'][0]]++;
            $listWx[$v['wx'][1]]++;
            $listWx2[$v['wx'][0]]++;
            $listTgWx[$v['wx'][0]]++;
            $tmp = implode('', $v['wx']);
            if ($tmp == '火水') {
                $resKey[] = '天干五行为火且同柱地支五行为水';
            }
        }
        $resKey = [];
        if (in_array($wangDu, ['身弱格', '从弱格'])) {
            $resKey[] = '身弱格或从弱格';
        }
        if ($listWx['金'] <= 2) {
            $resKey[] = '命局金数<=2(看藏干)';
        }
        if ($dtgWx == '火' && in_array($mdzWx, ['巳', '午', '未'])) {
            $resKey[] = '日元五行为火且月支为巳午未其一';
        }
        $arr1 = [
            ['正官', '七杀'], ['正财', '偏财'], ['比肩', '劫财'], ['食神', '伤官'], ['正印', '偏印'],
        ];
        if (in_array($godT['day'], $arr1[4]) || in_array($godZ['day'], $arr1[4])) {
            $resKey[] = '日柱十神为枭印';
        }
        if (in_array('辛', $tgArr) && in_array('丑', $dzArr)) {
            $resKey[] = '天干无辛且地支有丑';
        }
        if (in_array($godZ['month'], $arr1[0])) {
            $resKey[] = '月支十神为正官或七杀';
        }
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($godH as $k => $v) {
            foreach ($v['hide'] as $v1) {
                $wx1 = $wuXingAttr[$v1];
                $listWx2[$wx1]++;
            }
        }
        if ($listWx2['火'] <= 2) {
            $resKey[] = '命局火数<=2(看藏干)';
        }
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            if ($v == '伤官') {
                switch ($jiNian[$k1][0]) {
                    case '癸':
                        $resKey[] = '天干或地支癸水为伤官';
                        break;
                    case '丙':
                        $resKey[] = '伤官为丙火';
                        break;
                    case '戊':
                        $resKey[] = '伤官为戊土';
                        break;
                    case '庚':
                        $resKey[] = '伤官为庚金';
                        break;
                    case '壬':
                        $resKey[] = '伤官为壬水';
                        break;
                    case '辛':
                        $resKey[] = '伤官为辛金';
                        break;
                    case '乙':
                        $resKey[] = '伤官对应五行为乙木';
                        break;
                    case '丁':
                        $resKey[] = '八字中伤官为丁火';
                        break;
                }
            } elseif ($v == '正印') {
                if (in_array('羊刃', $shaShenRes[$k1])) {
                    $resKey[] = '天干有正印且同柱有羊刃';
                }
            }
            if ($godZ[$k] == '伤官') {
                if ($jiNian[$k1][1] == '己') {
                    $resKey[] = '伤官为己土';
                }
            }
            if ($base['jn_wx'][$k1]['wx'][0] == '金' && $base['jn_wx'][$k1]['wx'][1] == '土') {
                $resKey[] = '天干为金且同柱地支为土';
            }
            if (($v == '伤官' && $base['jn_wx'][$k1]['wx'][0] == '土') || ($godZ[$k] == '伤官' || $base['jn_wx'][$k1]['wx'][1] == '土')) {
                $resKey[] = '八字伤官对应五行未土';
            }
            if (($v == '伤官' && $base['jn_wx'][$k1]['wx'][0] == '木') || ($godZ[$k] == '伤官' || $base['jn_wx'][$k1]['wx'][1] == '木')) {
                $resKey[] = '伤官对应五行为木';
            }
        }
        if ($dtgWx == '金' && in_array($mdz, ['申', '酉', '戌'])) {
            $resKey[] = '日元五行为金且月支为申酉戌其一';
        }
        $naYin = $this->lunar->getNayin();
        $listWxN = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        foreach ($naYin as $v) {
            $naYinYwx = mb_substr($naYin['year'], 2, 1);
            $listWxN[$naYinYwx]++;
        }
        if ($listWxN['水'] >= 2 || $listWxN['火'] >= 2) {
            $resKey[] = '十二纳音水或火数>=2';
        }
        if ($listWxN['土'] <= 2 || $listWxN['金'] <= 2) {
            $resKey[] = '十二纳音土或金数<=2';
        }
        $jnNum = $this->getGzNum($jiNian);
        if ($listWx2['金'] >= 4) {
            if ($jnNum['壬'] <= 1) {
                $resKey[] = '命局壬水数<=1且金数>=4(看藏干)';
            }
            if ($jnNum['癸'] <= 1) {
                $resKey[] = '命局癸水数<=1且金数>=4(看藏干)';
            }
        }
        if ($listWx['金'] > $listWx['土']) {
            $resKey[] = '八字中金＞土';
        }
        if ($listWx['土'] > $listWx['火']) {
            $resKey[] = '八字中土＞火';
        }
        if ($dgz == '乙酉' && $hgz == '乙酉') {
            $resKey[] = '日柱和时柱都为乙酉';
        }
        $bool1 = 0;
        foreach ($jiNian as $k => $v) {
            $tmp = implode('', $v);
            if (in_array($tmp, ['甲申', '乙酉'])) {
                $bool1 = 1;
            }
            if (in_array($v[0], ['甲', '乙']) && in_array($v[1], ['庚', '辛'])) {
                $resKey[] = '甲或乙柱见庚或辛';
            }
        }
        if ($bool1) {
            $resKey[] = '命局中有甲申或乙酉';
        }
        if (in_array('七杀', $godH['day']['god']) && $base['jn_wx']['d']['gx'] == '生') {
            $resKey[] = '日柱藏干为七杀且有力';
        }
        if ($listWx['金'] >= 4 && $ddzWx == '木') {
            $resKey[] = '日主是木且金数>=4';
        }
        if ($wangDu == '从旺格') {
            $resKey[] = '从旺格';
        }
        if ($listWx2['水'] <= 1) {
            $resKey[] = '八字中水≤1（看藏干）';
        }
        if (in_array('卯', $dzArr) || in_array('辰', $dzArr)) {
            $resKey[] = '八字地支有卯和辰';
        }
        if ($listWx2['火'] >= 3 && $xy['xy']['ji'] == '火') {
            $resKey[] = '八字火≥3且火为忌神（看藏干）';
        }
        if (array_intersect(['庚寅', '辛卯'], [$dgz, $hgz])) {
            $resKey[] = '日柱或时柱是庚寅或辛卯';
        }
        if (array_intersect(['辛卯', '己卯'], [$dgz, $hgz])) {
            $resKey[] = '日柱或时柱是辛卯或己卯';
        }
        if ($listWx2['木'] <= 1) {
            $resKey[] = '八字中木≤1（看藏干）';
        }
        if ($jnNum['亥'] >= 2 || $jnNum['子'] >= 2) {
            $resKey[] = '八字中亥或子≥2（不看藏干）';
        }
        if (in_array($ytg, ['丙', '丁']) && in_array($dtg, ['壬', '癸'])) {
            $resKey[] = '年干为丙或丁且日干为壬或癸';
        }
        if ($listWx2['土'] <= 1) {
            $resKey[] = '八字中土≤1（看藏干）';
        }
        if ($listWx['水'] > $listWx['火']) {
            $resKey[] = '八字中水＞火';
        }
        if (in_array('癸', $tgArr) && in_array('丁', $tgArr)) {
            $resKey[] = '四柱天干有癸和丁';
        }
        if ($godT['month'] == '正印') {
            $resKey[] = '月干为正印';
        }
        if ($jnNum['丁'] >= 2 && in_array('未', $dzArr)) {
            $resKey[] = '八字中丁>=2且有未';
        }
        if ($listWx['木'] <= 1 || $listWx['木'] >= 4) {
            $resKey[] = '八字中亥或子≥2（不看藏干）';
        }
        if ($listWx2['金'] <= 3 && $listWx2['水'] <= 3) {
            $resKey[] = '命局金和水数<=3(看藏干)';
        }
        if ($listWx['土'] > $listWx['火']) {
            $resKey[] = '八字土＞火';
        }
        if ($godTZS['py'] >= 2 && $xy['shen']['ji'] == '印枭') {
            $resKey[] = '枭神≥2且枭神五行为忌神';
        }
        if ($godTZS['qs'] == 1 && $godTZS['pc'] == 3 && $listTgWx['火'] == 2) {
            $resKey[] = '命局中有一个七杀，三个偏财，天干两个火';
        }
        if ($godTHS['py'] >= 4) {
            $resKey[] = '八字中偏印≥4（看藏干）';
        }
        $wrDay = BaziExt::getWr($dtg, $ddz);
        if ($wrDay == '弱') {
            if (($godTZS['sh'] + $godTZS['sg']) >= 3) {
                $resKey[] = '日元弱且食和伤总数≥3';
            }
        }
        if ($jnNum['辰'] > 0 && $jnNum['戌'] > 0) {
            $resKey[] = '八字地支有辰戌';
        }
        if ($jnNum['申'] > 0 && $jnNum['巳'] > 0) {
            $resKey[] = '地支有申和巳';
        }
        if ($dtgWx == '木' && in_array($mdz, ['寅', '卯', '辰'])) {
            $resKey[] = '日元五行属木且月支为寅卯辰其一';
        }
        if ($listWx['木'] > $listWx['火']) {
            $resKey[] = '八字中木＞火';
        }
        if ($dtgWx == '土' && $listWx['木'] >= 3) {
            $resKey[] = '日元为土且木≥3（不看藏干）';
        }
        if ($jnNum['子'] > 0 && $jnNum['午'] > 0) {
            $resKey[] = '八字中有子与午';
        }
        if ($godTZS['zc'] == 0 && $godTZS['pc'] == 0) {
            $resKey[] = '八字无财';
        }
        if ($jnNum['卯'] > 0 && $jnNum['酉'] > 0) {
            $resKey[] = '地支有卯酉';
        }
        if ($terrain['day'] == '长生') {
            $resKey[] = '日坐长生';
        }
        if (($ydz . $mdz) == '寅申' || ($mdz . $ydz) == '寅申') {
            $resKey[] = '寅申年月相冲';
        }
        if ($listWx['火'] >= 4) {
            $resKey[] = '八字中火≥4';
        }
        if ($listWx['木'] > $listWx['土']) {
            $resKey[] = '八字中木＞土';
        }
        if ($listWx['火'] > $listWx['金']) {
            $resKey[] = '八字中火＞金';
        }
        if ($listWx['火'] > $listWx['木']) {
            $resKey[] = '八字中火＞木';
        }
        if (($listWx['木'] + $listWx['水']) > ($listWx['土'] + $listWx['火'])) {
            $resKey[] = '八字中木和水数＞土和火数';
        }
        if ($dtgWx == '土' && in_array($mdz, ['巳', '午', '未'])) {
            $resKey[] = '日元五行属土且月支为巳午未';
        }
        if (in_array('偏印', $godT) && in_array('', $godT)) {
            $resKey[] = '天干有偏印和食神';
        }
        if ($godT['month'] == '比肩' && in_array('羊刃', $shaShenRes['d']) && $arrGx['d']['chong']) {
            $resKey[] = '月天为比肩且日柱有羊刃且日支被冲';
        }
        if ($ddz == '未' && $arrGx['d']['chong']) {
            $resKey[] = '日支为未且被它支所冲';
        }
        if (in_array($godZ['day'], ['食神', '伤官']) && $arrGx['d']['chong'] && $arrGx['d']['ke']) {
            $resKey[] = '日支为食伤且被冲克';
        }
        if (in_array($mdz, ['亥', '子', '丑']) && $listWx['火'] == 0) {
            $resKey[] = '月支亥子丑且命局无火';
        }
        if ($listWx['火'] >= 3) {
            if ($listWx['土'] <= 1 || $listWx['水'] >= 2) {
                $resKey[] = '八字中火≥3且土≤1或水≥2';
            }
            if ($listWx['土'] <= 0) {
                $resKey[] = '八字中火≥3且土≤0（不看藏干）';
            }
            if ($listWx['土'] <= 1) {
                $resKey[] = '八字中火≥3且土≤1（不看藏干）';
            }
        }
        $sex = $this->lunar->sex;
        if ($sex) {
            if ($listWx['水'] <= 1) {
                $resKey[] = '命主为女且原局缺水（水<=1）';
            }
            if ($listWx['火'] <= 1) {
                $resKey[] = '命主为女且原局缺火（火<=1）';
            }
            if ($godTZS['pc'] < 1 || $godTZS['zc'] < 1) {
                $resKey[] = '命主为女且四柱十神无偏财或正财';
            }
            if (in_array('卯', $dzArr) && in_array('酉', $dzArr)) {
                $resKey[] = '命主为女且八字地支有卯酉';
            }
            if (in_array('勾煞', $shaShenRes['d']) || in_array('勾煞', $shaShenRes['h'])) {
                $arrT1 = [$xy['xy']['ji'], $xy['xy']['qiu']];
                if (in_array($ddzWx, $arrT1) || in_array($hdzWx, $arrT1)) {
                    $resKey[] = '日柱或时柱有勾煞且日支或时支对应五行为忌神或仇神';
                }
            }
            $yanRenArr = [];
            foreach ($godT as $k => $v) {
                $k1 = substr($k, 0, 1);
                if ($godZ[$k] == '伤官') {
                    if ($arrGx[$k1]['chong']) {
                        $resKey[] = '地支为伤官且被其它所冲（不看藏干）';
                    }
                }
                $boolYanRen = in_array('羊刃', $shaShenRes[$k1]);
                if ($boolYanRen) {
                    $yanRenArr[] = $k1;
                }
                if (in_array($v, $arr1[4]) || in_array($godZ[$k], $arr1[4])) {
                    if ($boolYanRen) {
                        $resKey[] = '命局中有羊刃并且有印';
                    }
                }
                if ($boolYanRen && $v = '正印') {
                    $resKey[] = '命局中有羊刃并且是在天干为正印的柱上';
                }
                if ($v == '劫财' || $godZ[$k] == '劫财') {
                    if ($boolYanRen) {
                        $resKey[] = '同柱中有羊刃和劫财';
                    }
                }
                if (($v == '食神' && $godZ[$k] == '偏印') || ($v == '偏印' && $godZ[$k] == '食神')) {
                    $resKey[] = '同柱上有食神也有枭';
                }
            }
            if ($jnNum['癸'] <= 1 && $jnNum['丁'] <= 1) {
                $resKey[] = '癸水和丁火数<=1';
            }
            $jieQi = $this->lunar->getJieQiCur();
            if ($ddzWx == '金') {
                if ($wangDu == '身弱格') {
                    $resKey[] = '女命日主为金且身弱';
                }
                if ($dtgWx == '金') {
                    if ($jieQi['current'][0] == '立冬') {
                        $resKey[] = '女命日主为金且节气为立冬';
                    }
                }
            }
            if ($godT['year'] == '伤官' && $godZ['year'] == '伤官') {
                $resKey[] = '年干支皆伤官';
            }
            if (in_array('', $godH['year']['god'])) {
                $resKey[] = '女命年柱藏干有伤官';
            }
            if ($godH2S['sg'] >= 2) {
                $resKey[] = '地支藏干伤官数>=2';
            }
            if ($arrGx['d']['chong'] && $arrGx['h']['chong']) {
                $resKey[] = '日支和时支被它支所冲';
            }
            if ($dtgWx == '金' && $listWx['火'] >= 2) {
                $resKey[] = '女命日元属性为金，且八字中有火多（＞＝2）';
            }
            if (($listWx['火'] >= 2 && $listWx['土'] < 2) || $listWx['水'] >= 2) {
                $resKey[] = '八字火（＞＝2）盛土衰（＜2）或水多（＞＝2）';
            }
            if (($listWx['火'] >= 2 && $listWx['土'] < 2)) {
                $resKey[] = '八字中火盛（＞＝2）土弱（＜2）';
            }
            if ($dtg == '丙') {
                if ($jieQi['current'][0] == '立冬') {
                    $resKey[] = '女命日元为丙火，且生于节气为立冬';
                }
            }
            if ($jnNum['庚'] > 0 && $jnNum['寅'] > 0 && $jnNum['卯'] > 0) {
                $resKey[] = '女命天干有庚，地支有寅卯';
            }
            $month = (int)$base['_nongli']['m'];
            if ($dtgWx == '水' && in_array($month, [10, 11, 12])) {
                $resKey[] = '女命为水命并且出生季节为冬';
            }
            if (in_array('羊刃', $shaShenRes['h'])) {
                $resKey[] = '时干出现羊刃';
            }
            if ($dtgWx == '水' && $listWx['土'] >= 2) {
                $resKey[] = '日元属性为水，且八字属性多土（＞＝2）';
            }
            if ($jnNum['戊'] && $jnNum['己'] && $jnNum['寅'] && $jnNum['卯']) {
                $resKey[] = '干有戊己，支有寅卯';
            }
            if (in_array($month, [10, 11, 12]) && $listWx['火'] == 0) {
                $resKey[] = '女命冬天生且八字五行无火';
            }
            if (in_array('羊刃', $shaShenRes['d'])) {
                $resKey[] = '日柱上带有阳刃';
            }
            if ($jnNum['庚'] && $jnNum['辛'] && $jnNum['申'] && $jnNum['酉']) {
                $resKey[] = '八字有庚辛申酉四字';
            }
            if ($godT['day'] == '伤官' || $godZ['day'] == '伤官') {
                $resKey[] = '日柱上有伤官';
            }
            if ($dtgWx == '金' && $listWx['火'] > 2) {
                $resKey[] = '日元是金，并且八字属性火多（＞2）';
            }
            if ($godTZS['py'] >= 2) {
                $resKey[] = '命局中带多枭（＞＝2）';
            }
            if ($yanRenArr) {
                $resKey[] = '命局中带羊刃';
            }
            if ($jnNum['子'] && $jnNum['未']) {
                $resKey[] = '干支有子未';
            }
            // 木火相生而太旺
            // 日元为木且相邻的火旺
            // 命局中三合成火局，并且日元为庚
        }

        // 日元中和（强度表处判断）
        // 官杀同透天干
        // 伤官与官或杀在天干、伤官见官、官杀在支被刑冲
        // 地支有相刑或三刑
        // 地支有三刑且无六合
        // 四柱中有三刑
        // 柱临天月二德且为喜用
        // 命局中伤官透干而气盛
        // 八字以土为喜用，天干透己土而遭甲木克合

        // 命局中伤官、七煞、羊刃并显

        // 犯羊刃杀
        return array_values(array_unique($resKey));
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'sh' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }

    /**
     * 获得纪年数量
     * @param array $jiNian
     * @return int[]
     */
    protected function getGzNum(array $jiNian): array
    {
        $list = [
            '甲' => 0, '乙' => 0, '丙' => 0, '丁' => 0, '戊' => 0, '己' => 0, '庚' => 0, '辛' => 0, '壬' => 0, '癸' => 0,
            '子' => 0, '丑' => 0, '寅' => 0, '卯' => 0, '辰' => 0, '巳' => 0, '午' => 0, '未' => 0, '申' => 0, '酉' => 0, '戌' => 0, '亥' => 0,
        ];
        foreach ($jiNian as $v) {
            $list[$v[0]]++;
            $list[$v[1]]++;
        }
        return $list;
    }

    /**
     * 获得某柱和其它柱的冲刑害破合等关系
     * @param string $k
     * @param array $jiNian
     * @return array
     */
    protected function getGx1(string $k, array $jiNian): array
    {
        $dz1 = $jiNian[$k][1];
        $tg1 = $jiNian[$k][0];
        $result = [
            'chong' => [], 'xin' => [], 'hai' => [], 'ke' => [], 'liu_he' => [], 'he_t' => [], 'ke_t' => [],
        ];
        foreach ($jiNian as $k1 => $v) {
            if ($k1 == $k) {
                continue;
            }
            $str = $v[1] . $dz1;
            if (BaziCommon::getXianChong($str)) {
                $result['chong'][] = $k1;
            }
            if (BaziCommon::getXianXin($str)) {
                $result['xin'][] = $k1;
            }
            if (BaziCommon::getXianHai($str)) {
                $result['hai'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['po'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['ke'][] = $k1;
            }
            if (BaziCommon::liuHeDz($str)) {
                $result['liuhe'][] = $k1;
            }
            if (BaziCommon::xianHeTg($tg1 . $v[0])) {
                $result['he_t'][] = $k1;
            }
            if (BaziCommon::getXianKe($tg1 . $v[0])) {
                $result['ke_t'][] = $k1;
            }
        }
        return $result;
    }
}
