<?php
// +----------------------------------------------------------------------
// | 八字排盘-家庭子女
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;

class Family
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 用户基础信息
     * @var array
     */
    protected array $base;

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @var array[]
     */
    protected array $shenShaRes;

    /**
     * @var array
     */
    protected array $xy;

    /**
     * 初始化
     * @param string $birthday 生日：1998-03-20 10:0:00
     * @param int $gender 性别：0男 1 女
     * @throws Exception
     */
    public function __construct(string $birthday, int $gender = 0)
    {
        // 农历类对象
        $this->lunar = Ex::date($birthday)->sex($gender);
        $this->base = $this->lunar->getLunarByBetween();
        $godT = $this->lunar->getGod();
        $godH = $this->lunar->_getGod();
        $godZ = [
            'year' => $godH['year']['god'][0],
            'month' => $godH['month']['god'][0],
            'day' => $godH['day']['god'][0],
            'hour' => $godH['hour']['god'][0],
        ];
        $this->userGod = [
            't' => $godT,
            'd' => $godZ,
            'h' => $godH,
        ];
        $jiNian = $this->base['jinian'];
        $jnWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $jnWx[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        $this->base['jn_wx'] = $jnWx;
        $this->shaShen = new ShaShen();
        $shaShenRes = $this->shaShen->detail($jiNian, $gender);
        $this->shenShaRes = $shaShenRes;
    }

    /**
     * 设置喜用忌闲仇
     * @param array $xy
     */
    public function setXy(array $xy)
    {
        $this->xy = $xy;
    }

    /**
     * 详细断语
     * @return array
     */
    public function detail()
    {
        $base = $this->base;
        $xy = $this->xy;
        $jiNian = $base['jinian'];
        $ydz = $jiNian['y'][1];
        $mdz = $jiNian['m'][1];
        $ddz = $jiNian['d'][1];
        $hdz = $jiNian['h'][1];
        $ytg = $jiNian['y'][0];
        $mtg = $jiNian['m'][0];
        $dtg = $jiNian['d'][0];
        $htg = $jiNian['h'][0];
        $tgArr = array_column($jiNian, 0);
        $dzArr = array_column($jiNian, 1);
        $ytgWx = $base['jn_wx']['y']['wx'][0];
        $ydzWx = $base['jn_wx']['y']['wx'][1];
        $mtgWx = $base['jn_wx']['m']['wx'][0];
        $mdzWx = $base['jn_wx']['m']['wx'][1];
        $dtgWx = $base['jn_wx']['d']['wx'][0];
        $ddzWx = $base['jn_wx']['d']['wx'][1];
        $htgWx = $base['jn_wx']['h']['wx'][0];
        $hdzWx = $base['jn_wx']['h']['wx'][1];
        $shenShaRes = $this->shenShaRes;
        $dgz = $dtg . $ddz;
        $godT = $this->userGod['t'];
        $godTS = $this->getGodSum(array_values($godT));
        $godZ = $this->userGod['d'];
        $godH = $this->userGod['h'];
        $terrain = $this->lunar->getTerrain();
        $godH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['day']['god'], $godH['hour']['god']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTZS = $this->getGodSum($godTZ);
        $godTH = array_merge(array_values($godT), array_values($godH2));
        $godTHS = $this->getGodSum($godTH);
        $kW = explode(',', Huangli::getKongWangbyGz($dgz));
        $arrGx = [
            'y' => $this->getGx1('y', $jiNian),
            'm' => $this->getGx1('m', $jiNian),
            'd' => $this->getGx1('d', $jiNian),
            'h' => $this->getGx1('h', $jiNian),
        ];
        $listGuiRen = ['太极贵人', '天官贵人', '太极贵人', '文昌贵人', ' 国印贵人'];
        $xinArr = [
            'y' => [
                'm' => BaziCommon::getXianXin($ydz . $mdz), 'd' => BaziCommon::getXianXin($ydz . $ddz), 'h' => BaziCommon::getXianXin($ydz . $hdz),
            ],
            'm' => [
                'y' => BaziCommon::getXianXin($mdz . $ydz), 'd' => BaziCommon::getXianXin($mdz . $ddz), 'h' => BaziCommon::getXianXin($mdz . $hdz),
            ],
            'd' => [
                'y' => BaziCommon::getXianXin($ddz . $ydz), 'm' => BaziCommon::getXianXin($ddz . $mdz), 'h' => BaziCommon::getXianXin($ddz . $hdz),
            ],
            'h' => [
                'y' => BaziCommon::getXianXin($hdz . $ydz), 'm' => BaziCommon::getXianXin($hdz . $mdz), 'd' => BaziCommon::getXianXin($hdz . $ddz),
            ],
        ];
        $chongArr = [
            'y' => [
                'm' => BaziCommon::getXianChong($ydz . $mdz), 'd' => BaziCommon::getXianChong($ydz . $ddz), 'h' => BaziCommon::getXianChong($ydz . $hdz),
            ],
            'm' => [
                'y' => BaziCommon::getXianChong($mdz . $ydz), 'd' => BaziCommon::getXianChong($mdz . $ddz), 'h' => BaziCommon::getXianChong($mdz . $hdz),
            ],
            'd' => [
                'y' => BaziCommon::getXianChong($ddz . $ydz), 'm' => BaziCommon::getXianChong($ddz . $mdz), 'h' => BaziCommon::getXianChong($ddz . $hdz),
            ],
            'h' => [
                'y' => BaziCommon::getXianChong($hdz . $ydz), 'm' => BaziCommon::getXianChong($hdz . $mdz), 'd' => BaziCommon::getXianChong($hdz . $ddz),
            ],
        ];
        $arr1 = [
            ['正官', '七杀'], ['正财', '偏财'], ['比肩', '劫财'], ['食神', '伤官'], ['正印', '偏印'],
        ];
        $arr2 = [
            ['长生', '冠带', '临官', '帝旺'],
        ];
        $resKey = [];
        if ($chongArr['y']['m'] || $chongArr['d']['m'] || $chongArr['h']['m']) {
            $resKey[] = '月支被冲';
        }
        $bool1 = false;
        $sgN1 = 0;
        $bool2 = 0;
        $yanRenNum = 0;
        $wxxqs = $this->lunar->getWxxqs();
        foreach ($godT as $k => $v) {
            if ($k == 'year') {
                if ($v == '食神' || $godZ[$k] == '食神') {
                    if ($v != '偏印' || $godZ[$k] != '偏印') {
                        $resKey[] = '年柱有食神且无偏印';
                    }
                }
                if ($v == '比肩' || $godZ[$k] == '比肩') {
                    $resKey[] = '年天干和年地支为比肩(不看藏干)';
                    if ($godT['month'] == '正官' || $godZ['month'] == '正官') {
                        $resKey[] = '年干或年支是比肩且月干或月支是正官(不看藏干)';
                    }
                }
                if ($v == '伤官' || in_array('伤官', $godH['year']['god'])) {
                    $resKey[] = '年柱有伤官(看藏干)';
                }
                if ($v == '偏印' || $godZ[$k] == '偏印') {
                    if ($xy['shen']['ji'] == '印枭') {
                        $resKey[] = '年柱有偏印且为忌神（不看藏干）';
                    }
                }
            }
            if (in_array($v, $arr1[0]) || in_array($godZ[$k], $arr1[0])) {
                if (in_array($terrain[$k], $arr2[0])) {
                    $resKey[] = '正官或七杀同柱十二长生为长生、冠带、临官、帝旺其中之一';
                }
            }
            if ($terrain[$k] == '长生') {
                if (array_intersect($arr1[1], $godH[$k]['god'])) {
                    $resKey[] = '十二长生同柱有正财或偏财(看藏干)';
                }
            }
            if ($v == '食神' || in_array('食神', $godH[$k]['god'])) {
                $resKey[] = '天干有食神且同柱藏干亦有食神';
            }
            $strK = substr($k, 0, 1);
            if ($v == '七杀' || in_array('七杀', $godH[$k]['god'])) {
                if ($xinArr[$strK]) {
                    $resKey[] = '七杀同柱地支有刑(看藏干)';
                }
            }
            if ($v == '正印' || $godZ[$k] == '正印') {
                if (in_array($jiNian[$strK][1], ['子', '午', '卯', '酉'])) {
                    $bool1 = true;
                }
                if ($k == 'hour') {
                    if (in_array('印枭', [$xy['shen']['xi'], $xy['shen']['yong']])) {
                        $resKey[] = '时柱上有正印且为喜用';
                    }
                    if ($arrGx[$strK]['chong'] && $arrGx[$strK]['ke']) {
                        $resKey[] = '正印被冲克';
                    }
                }
                if (in_array('华盖', $shenShaRes[$strK])) {
                    $resKey[] = '年柱有华盖或正印同柱有华盖(不看藏干)';
                }
                if ($terrain[$k] == '沐浴') {
                    $resKey[] = '正印同柱十二长生为沐浴(不看藏干)';
                }
            }
            $boolGuiRen = !empty(array_intersect($listGuiRen, $shenShaRes[$strK]));
            $boolTerrain1 = in_array($terrain[$k], ['长生', '冠带', '临官', '帝旺']);
            $booYiMa = in_array('驿马', $shenShaRes[$strK]);
            $boolYanRen = in_array('羊刃', $shenShaRes[$strK]);
            if (in_array($v, $arr1[2]) || in_array($godZ[$k], $arr1[2])) {
                if ($boolGuiRen) {
                    $resKey[] = '比肩或劫财同柱有贵人';
                }
                if (in_array($jiNian[$strK][1], $kW)) {
                    $resKey[] = '比肩或劫财同柱地支为空亡';
                }
                if ($booYiMa) {
                    $resKey[] = '比肩或劫财同柱有驿马';
                }
                if (in_array($jiNian[$strK][1], $kW) || in_array($terrain[$k], ['死', '墓', '绝'])) {
                    $resKey[] = '比肩或劫财同柱地支为空亡或同柱十二长生为死墓绝';
                }
                if (array_intersect(['孤辰', '吊客'], $shenShaRes[$strK])) {
                    $bool2 = 1;
                }
                if ($boolTerrain1) {
                    $resKey[] = '比肩或劫财同柱十二长生为长生、冠带、临官、帝旺中其中之一';
                }
                if ($boolYanRen && $xy['shen']['ji'] == '比劫') {
                    $resKey[] = '比肩或劫财同柱有羊刃且比肩或劫财对应五行为忌神';
                }
            }
            if (in_array($v, $arr1[3]) || in_array($godZ[$k], $arr1[3])) {
                if ($boolGuiRen) {
                    $resKey[] = '贵人同柱有食神或伤官';
                }
                if ($boolTerrain1) {
                    $resKey[] = '食伤同柱十二长生为长生、冠带、临官、帝旺其中之一';
                }
                if ($k == 'hour' && array_intersect(['孤辰', '寡宿'], $shenShaRes['h'])) {
                    $resKey[] = '时柱有食伤或伤官且同柱有孤辰或寡宿';
                }
                if ($arrGx[$strK]['chong']) {
                    $resKey[] = '食神或伤官地支被它支所冲';
                }
                if ($booYiMa) {
                    $resKey[] = '时柱有驿马或食神，伤官同柱有驿马';
                }
            }
            if (($v == '伤官' || $godZ[$k] == '伤官')) {
                if ($boolGuiRen) {
                    $resKey[] = '贵人同柱有伤官';
                }
                if ($boolYanRen && $xy['shen']['ji'] == '食伤') {
                    $resKey[] = '伤官同柱有羊刃且对应五行为忌神';
                }
                $sgN1++;
            }
            if ($v == '食神' && $godZ[$k] == '比肩') {
                if (in_array($base['jn_wx'][$strK]['wx'][1], [$xy['xy']['yong'], $xy['xy']['xi']])) {
                    $resKey[] = '天干食神且同柱地支比肩且比肩对应五行为喜神或用神';
                }
            }
            $boolDz2 = in_array($base['jn_wx'][$strK]['gx2'], ['生', '扶']);
            if ($v == '正官') {
                if ($boolDz2) {
                    $resKey[] = '天干有正官且同柱地支五行生扶天干五行';
                }
                if (in_array($terrain[$k], ['死', '绝'])) {
                    $resKey[] = '天干正官且同柱十二长生为死或绝';
                }
            } elseif ($v == '食神') {
                if ($boolDz2) {
                    $resKey[] = '天干有食神且同柱地支五行生天干五行';
                }
            }
            if (in_array('华盖', $shenShaRes[$strK])) {
                $bool2 = 1;
            }
            if ($boolYanRen) {
                $yanRenNum++;
            }
            if (in_array($v, $arr1[3])) {
                if (in_array($jiNian[$strK][1], ['辰', '戌', '丑', '未'])) {
                    $resKey[] = '地支辰戌丑未同柱天干有食神或伤官';
                }
            }
            if ($v == '正印') {
                if ($godZ[$k] == '劫财') {
                    $resKey[] = '天干正印且同柱地支为劫财';
                } elseif ($godZ[$k] == '正印') {
                    if ($booYiMa) {
                        $resKey[] = '年柱有驿马或正印同柱有驿马';
                    }
                }
            }
            if (in_array($jiNian[$strK][1], ['辰', '戌', '丑', '未'])) {
                if (in_array($godZ[$k], $arr1[3])) {
                    if (!in_array('食神', $godT) || !in_array('伤官', $godT)) {
                        $resKey[] = '天干没有食神或伤官且辰戌丑未中有食神或伤官';
                    }
                }
            }
            if ($v == '偏财' || $godZ[$k] == '偏财') {
                if (in_array($jiNian[$strK][1], $kW)) {
                    $resKey[] = '偏财同柱为空亡';
                }
                if ($boolTerrain1) {
                    $resKey[] = '偏财同柱十二长生为长生、冠带、临官、帝旺其中之一';
                }
            }
            if (in_array($v, $arr1[4]) || in_array($godZ[$k], $arr1[4])) {
                if ($boolTerrain1) {
                    $resKey[] = '正印或偏印同柱十二长生为长生、冠带、临官、帝旺之一';
                }
                if ($terrain[$k] == '长生') {
                    $resKey[] = '正印或偏印同柱十二长生为长生';
                }
                $bool3 = 0;
                if ($wxxqs[0] == $base['jn_wx'][$strK]['wx'][0]) {
                    if ($v == '正印') {
                        $bool3 = 1;
                    }
                    if ($godZ[$k] == '正印') {
                        $bool3 = 1;
                    }
                }
                if ($bool3) {
                    $resKey[] = '正印同柱十二长生为长生、冠带、临官、帝旺其中之一，且正印五行为"旺"（这里的旺所指为旺相休囚死）';
                }
            }
            if ($v == '劫财' || $godZ[$k] == '劫财') {
                if ($xy['shen']['ji'] == '比劫') {
                    $resKey[] = '天干和地支为劫财且劫财对应五行为忌神';
                    if ($k == 'hour') {
                        $resKey[] = '时柱有劫财且为忌神';
                    }
                }
            }
            if ($godZ[$k] == '劫财') {
                if ($xy['shen']['ji'] == '比劫') {
                    if ($arrGx[$strK]['liu_he']) {
                        $resKey[] = '地支劫财被合且对应五行为忌神';
                    }
                }
            }
            if (($v == '伤官' && $godZ[$k] == '劫财') || ($v == '劫财' && $godZ[$k] == '伤官')) {
                if ($xy['shen']['ji'] == '食伤') {
                    $resKey[] = '伤官和劫财同柱且伤官对应五行为忌神';
                }
            }
            if ($v == '食神') {
                if ($arrGx[$strK]['chong'] && $arrGx[$strK]['xin']) {
                    $resKey[] = '天干食神同柱地支被刑冲';
                }
            }
        }
        if (in_array('驿马', $shenShaRes['y'])) {
            $resKey[] = '年柱有驿马或正印同柱有驿马';
        }
        if (in_array('华盖', $shenShaRes['y'])) {
            $resKey[] = '年柱有华盖或正印同柱有华盖(不看藏干)';
        }
        if (in_array($ydz, ['子', '午', '卯', '酉']) || $bool1) {
            $resKey[] = '年支为子午卯酉之一或正印同柱地支有子午卯酉(不看藏干)';
        }
        if ($bool2) {
            $resKey[] = '八字有华盖或比肩，劫财同柱十二神煞为孤辰或吊客';
        }
        if (BaziCommon::getWuxingGuanXi($ytgWx, $dtgWx) == '生') {
            $resKey[] = '年干生日元';
        }
        if (in_array('丙', $tgArr) && in_array('壬', $tgArr)) {
            $resKey[] = '天干有丙、壬';
        }
        if ($xy['shen']['yong'] == '比劫') {
            $resKey[] = '比劫为用神';
        }
        if (in_array($xy['xy']['yong'], $base['jn_wx']['h']['wx'])) {
            $resKey[] = '时柱为用神';
        }
        if ($xinArr['y']['h'] || $xinArr['m']['h'] || $xinArr['d']['h']) {
            $resKey[] = '时支被刑';
        }
        if ($godZ['day'] == '偏印') {
            $resKey[] = '日支为偏印';
        }
        if ($base['jn_wx']['h']['gx'] == '克') {
            $resKey[] = '时干克日干';
        }
        if (in_array('正官', $godT)) {
            $resKey[] = '正官在天干';
        }
        if (($godT['year'] == '七杀' || $godZ['year'] == '七杀') && ($godZ['month'] == '七杀' || $godT['month'] == '七杀')) {
            $resKey[] = '年柱与月柱上有七杀';
        }
        if ($chongArr['m']['d']) {
            $resKey[] = '月支和日支相冲';
        }
        if (in_array($hdz, ['子', '午', '卯', '酉'])) {
            $resKey[] = '时支为子午卯酉';
        }
        if ($base['jn_wx']['y']['wx'][0] == $xy['xy']['yong']) {
            $resKey[] = '年干五行为用神';
        }
        if (BaziCommon::getWuxingGuanXi($mdzWx, $ydzWx) == '生') {
            $resKey[] = '月支五行生年支五行';
        }
        if ($base['jn_wx']['y']['gx'] == '生') {
            $resKey[] = '年干和年支五行相生';
        }
        if (BaziCommon::getWuxingGuanXi($dtgWx, $ytgWx) == '生' || BaziCommon::getWuxingGuanXi($dtgWx, $ydzWx) == '生') {
            $resKey[] = '日元生年柱';
        }
        if (($godTZS['zg'] + $godTZS['qs']) >= 4) {
            $resKey[] = '八字正官和七杀数>=4';
        }
        if (in_array($godT['month'], $arr1[0])) {
            $resKey[] = '月干为正官或七杀';
        }
        if ($xy['shen']['yong'] == '食伤') {
            $resKey[] = '食神为用神';
        }
        if (array_intersect($listGuiRen, $shenShaRes['d'])) {
            $resKey[] = '日柱有贵人';
        }
        if ($godT['hour'] == '七杀' || $godZ['hour'] == '七杀') {
            if ($xy['shen']['xi'] == '官杀') {
                $resKey[] = '时柱有七杀且为喜神';
            }
        }
        if ($xy['shen']['yong'] == '食伤') {
            $resKey[] = '伤官和食神五行为用';
        }
        if ($godZ['day'] == '七杀') {
            $resKey[] = '日支为七杀';
        }
        if (in_array('七杀', $godT)) {
            $resKey[] = '天干有七杀';
        }

        if (array_intersect($listGuiRen, $shenShaRes['m'])) {
            $resKey[] = '月柱有贵人';
        }
        if (($godTZS['zc'] + $godTZS['pc']) >= 3) {
            $resKey[] = '正财和偏财数>=3';
            if (($godTZS['zy'] + $godTZS['py']) <= 2) {
                $resKey[] = '正财和偏财数>=3且正印或偏印数<=2';
            }
        }
        if ($godT['year'] == '比肩' || $godZ['year'] == '比肩') {
            if (in_array($godT['month'], ['正官', '正印', '偏印']) || in_array($godZ['month'], ['正官', '正印', '偏印'])) {
                $resKey[] = '年干或年支为比肩且月干月支无正官，正印和偏印';
            }
        }
        if (BaziCommon::getWuxingGuanXi($mtgWx, $dtgWx) == '生' || BaziCommon::getWuxingGuanXi($mtgWx, $ddzWx) == '生') {
            $resKey[] = '月干五行和日干五行相生或月支五行和日支五行相生';
        }
        $baziEx = new BaziEx($this->lunar);
        $wangDu = $baziEx->getWangDu();
        if (in_array($wangDu, ['身弱格', '从弱格'])) {
            if (($godTZS['zy'] + $godTZS['py']) == 0) {
                $resKey[] = '身弱格或从弱格且八字无正印或偏印(不看藏干)';
            }
            if (in_array($godT['month'], $arr1[4]) && in_array($base['jn_wx']['m']['gx2'], ['生', '扶'])) {
                $resKey[] = '身弱或从弱且月干为正印或偏印且月支对应五行生扶月干五行';
            }
            if ($godTS['bj'] == 0 && $godTS['jc'] == 0) {
                foreach ($godH as $k => $v) {
                    if (array_intersect($arr1[2], $v['god'])) {
                        $strK = substr($k, 0, 1);
                        if ($arrGx[$strK]['chong'] && $arrGx[$strK]['xin']) {
                            $resKey[] = '身弱格或从弱格且天干无比肩、劫财且藏干比肩，劫财对应地支被刑冲';
                        }
                    }
                }
            }
            //
        }
        if ($chongArr['h']['y']) {
            $resKey[] = '时支冲年支';
        }
        if (($godTHS['zg'] + $godTHS['qs']) >= 4) {
            $resKey[] = '正官和七杀数>=4(看藏干)';
        }
        if (in_array('华盖', $shenShaRes['y']) || in_array('华盖', $shenShaRes['m']) || in_array('华盖', $shenShaRes['d']) || in_array('华盖', $shenShaRes['h'])) {
            $resKey[] = '神煞有华盖';
        }
        if ($godTHS['zg'] >= 4 || $godTHS['qs'] >= 4) {
            $resKey[] = '正官或七杀数>=4(看藏干)';
        }
        if ($godTS['zg'] >= 2) {
            $resKey[] = '天干正官数>=2';
        }
        if ($base['jn_wx']['y']['gx2'] == '生' && BaziCommon::getWuxingGuanXi($ydzWx, $hdzWx) == '生') {
            $resKey[] = '年支五行生年干五行且年支五行生时支五行';
        }
        if (BaziCommon::getWuxingGuanXi($ddzWx, $ydzWx) == '生') {
            $resKey[] = '日支生年支';
        }
        if (($godT['hour'] == '食神' || $godZ['hour'] == '食神') && (in_array($htgWx, [$xy['xy']['yong'], $xy['xy']['xi']]) || in_array($hdzWx, [$xy['xy']['yong'], $xy['xy']['xi']]))) {
            $resKey[] = '时柱有食神且食神五行';
        }
        if (in_array($godT['hour'], $arr1[3]) || in_array($godZ['hour'], $arr1[3])) {
            if (array_intersect(['天德', '月德'], $shenShaRes['h'])) {
                $resKey[] = '时柱或食伤同柱有天德或月德';
            }
        }
        if ($base['jn_wx']['y']['gx'] == '生') {
            $resKey[] = '年干支相生，干生支';
            if (BaziCommon::getWuxingGuanXi($ydzWx, $hdzWx) == '生') {
                $resKey[] = '年干五行生年支五行且生时支五行';
            }
        }
        if (in_array('伤官', $godH['day']['god'])) {
            $resKey[] = '日柱地支十神为伤官(不看藏干)';
        }
        if ($godTZS['sg'] >= 3) {
            $resKey[] = '伤官数>=3(看藏干)';
        }
        if ($sgN1 >= 3) {
            $resKey[] = '伤官出现在三柱以上';
        }
        if ($godTZS['sg'] > 0) {
            $resKey[] = '命局有伤官(看藏干)';
        }
        if (in_array($godT['month'], $arr1[3]) && in_array($godT['day'], $arr1[3])) {
            $resKey[] = '月干和日干为食伤';
        }
        if ($godZ['month'] == '伤官') {
            $resKey[] = '月支为伤官(原局)';
        }
        if ($hdz == '寅') {
            $resKey[] = '时支为寅';
        }
        if ($terrain['hour'] == '长生') {
            $resKey[] = '时柱十二长生有长生';
        }
        if ($godT['year'] == '偏印' && $godZ['year'] == '偏印') {
            $resKey[] = '年干支为偏印(不看藏干)';
        }
        if ($godTS['zy'] == 2) {
            $resKey[] = '天干正印数为2';
        }
        if ($yanRenNum > 0) {
            $resKey[] = '八字有羊刃';
        }
        if (in_array($wangDu, ['身旺格', '从旺格'])) {
            if (in_array('正印', $godT)) {
                $resKey[] = '身旺格或从旺格且有正印';
            }
        }
        if ($godT['hour'] == '正印') {
            $resKey[] = '时干为正印';
        }
        if ($godTZS['zy'] >= 3) {
            $resKey[] = '正印数>=3(看藏干)';
        }
        if (in_array($hdz, ['辰', '戌', '丑', '未'])) {
            $resKey[] = '时支为辰戌丑未';
        }
        if (in_array('羊刃', $shenShaRes['h'])) {
            $resKey[] = '时柱有羊刃';
        }
        if ($godT['year'] == '比肩') {
            $resKey[] = '年干为比肩';
        }
        if (($godTZS['bj'] + $godTZS['jc']) >= 4) {
            $resKey[] = '比肩和劫财数>=4';
        }
        if (in_array($godZ['month'], $arr1)) {
            $resKey[] = '月支十神为正官或七杀';
        }
        $naYin = $this->lunar->getNayin();
        $naYinYwx = mb_substr($naYin['year'], 2, 1);
        if (BaziCommon::getWuxingGuanXi($mdzWx, $naYinYwx) == '克') {
            $resKey[] = '月支五行克年柱十二纳音五行';
        }
        if (BaziCommon::getXianChong($ddz . $hdz)) {
            $resKey[] = '日支时支相冲';
        }
        if (in_array('华盖', $shenShaRes['h'])) {
            $resKey[] = '时柱有华盖';
        }
        if ($godTZS['bj'] >= 3) {
            $resKey[] = '八字比肩数>=3';
        }
        if (in_array('戊', $tgArr) || in_array($mtg . $mdz, ['戊寅', '甲寅'])) {
            $resKey[] = '天干有戊或月柱为戊寅，甲寅';
        }
        if (($godTHS['zg'] + $godTHS['qs']) >= 4) {
            $resKey[] = '正官和七杀数>=4(看藏干)且';
        }
        if (in_array($godT['hour'], $arr1[1]) || array_intersect($arr1[1], $godH['hour']['god'])) {
            $resKey[] = '时柱有正财或偏财(看藏干)';
        }
        if (count(array_intersect(['卯', '辰', '巳', '午', '未', '申'], $dzArr)) == 4) {
            $resKey[] = '地支为卯、辰、巳、午、未、申其中四个';
        }
        if (BaziCommon::getWuxingGuanXi($mdzWx, $ytgWx) == '生') {
            $resKey[] = '月支五行生年干五行';
        }
        if (in_array('孤鸾', $shenShaRes['d'])) {
            $resKey[] = '日柱有孤鸾';
        }

        if ($godTZS['pc'] == 0 && ($godTZS['zy'] >= 2 || $godTZS['py'] >= 2)) {
            $resKey[] = '命局无偏财且正印或偏印数>=2';
        }
        if (($godTHS['bj'] + $godTHS['jc']) >= 4) {
            $resKey[] = '比肩和劫财数>=4(看藏干)';
        }
        if ($xy['shen']['ji'] == '食伤') {
            if ($godT['month'] == '伤官' || in_array('伤官', $godH['month']['god'])) {
                $resKey[] = '月柱有伤官(看藏干)且为忌神';
            }
            if ($godT['hour'] == '伤官' && in_array('伤官', $godH['hour']['god'])) {
                $resKey[] = '伤官在时柱且对应五行为忌神(看藏干)';
            }
        } elseif ($xy['shen']['ji'] == '比劫') {
            if (($godTZS['bj'] + $godTZS['jc']) >= 4) {
                $resKey[] = '比肩和劫财数>=4且对应五行为忌神';
            }
            if (($godTZS['bj'] + $godTZS['jc']) > 0) {
                $resKey[] = '比肩或劫财对应五行为忌神';
            }
        } elseif ($xy['shen']['ji'] == '印枭') {
            if ($godZ['hour'] == '偏印') {
                $resKey[] = '时支为偏印且对应五行为忌神';
            }
            if ($godTHS['zy'] > 0 || $godTHS['py'] > 0) {
                $resKey[] = '正偏印五行为忌神';
            }
        }
        if (in_array('禄神', $shenShaRes['m'])) {
            $resKey[] = '月柱有禄神';
        }
        if (in_array('禄神', $shenShaRes['d'])) {
            $resKey[] = '日柱有禄神';
        }
        if ($godTHS['py'] > 0 && $godTHS['zy'] == 0) {
            $resKey[] = '八字命局有偏印而无正印(看藏干)';
        }
        if ($godTZS['bj'] >= 4 || $godTZS['jc'] >= 4) {
            $resKey[] = '八字比肩或劫财数量>=4';
        }
        if (in_array('食神', $godT) || in_array('偏印', $godT)) {
            $resKey[] = '天干有食神或偏印';
        }
        if (in_array('羊刃', $shenShaRes['m'])) {
            $resKey[] = '月柱有羊刃';
        }

        $num1 = 0;
        $numYan = 0;
        foreach ($base['jn_wx'] as $v) {
            if ($v['gx'] == '克') {
                $num1++;
            }
            if ($v['yy'][0] == '阳') {
                $numYan++;
            }
            if ($v['yy'][1] == '阳') {
                $numYan++;
            }
        }
        if ($num1 >= 2) {
            $resKey[] = '四柱中有2-4柱干克支';
        }
        if ($godZ['year'] == '偏印') {
            if (in_array($ydz, [$mdz, $ddz, $hdz])) {
                $resKey[] = '其他支和年支相同且年支十神为偏印';
            }
        }
        $dzArr2 = array_intersect(['寅', '申', '巳', '亥'], $dzArr);
        if (count($dzArr2) == 4) {
            $resKey[] = '寅申巳亥多';
        }
        if (count($dzArr2) >= 2) {
            $resKey[] = '地支寅申巳亥其一数>=2';
        }
        if (($godT['year'] == '正印' && $godT['month'] == '正官') || ($godT['year'] == '正官' && $godT['month'] == '正印')) {
            $resKey[] = '年干正印且月干正官或年干正官且月干正印';
        }
        if ($godTHS['zy'] >= 4) {
            $resKey[] = '正印数>=4(看藏干)';
        }
        if (BaziCommon::getWuxingGuanXi($mdzWx, $dtgWx) == '生') {
            $resKey[] = '月支五行生日干五行';
        }
        if ($godTS['zy'] > 0 && $godTS['py'] > 0) {
            $resKey[] = '天干同时存在正印和偏印';
        }
        if (in_array($base['jn_wx']['d']['gx2'], ['生', '扶'])) {
            if (!in_array($godT['month'], $arr1[4]) && !in_array($godZ['month'], $arr1[4])) {
                $resKey[] = '日支五行生扶日元且月柱天干和地支都未正印或偏印';
            }
        }
        if ($godT['year'] == '食神' && $godT['month'] == '偏印') {
            $resKey[] = '年干为食神且月干为偏印';
        }
        if ($godT['year'] == '偏财' && $godT['month'] == '偏财') {
            $resKey[] = '年干和月干都为偏财';
        }
        switch ($godT['month']) {
            case '偏财':
                $resKey[] = '月干偏财';
                break;
            case '伤官':
                $resKey[] = '月干有伤官';
                break;
            case '比肩':
                $resKey[] = '月干为比肩';
                break;
        }
        if ($godTS['pc'] == 2) {
            $resKey[] = '偏财同柱十二长生为长生、冠带、临官、帝旺其中之一';
        }
        if (in_array($ydz, $kW) && in_array($mdz, $kW)) {
            $resKey[] = '年月地支皆空亡';
        }
        if ($godT['hour'] == '正财' || $godZ['hour'] == '正财') {
            if ($xy['shen']['xi'] == '才财') {
                $resKey[] = '时柱有正财且对应五行为喜神';
            }
        }
        if (in_array($hdz, ['辰', '戌', '丑', '未'])) {
            $resKey[] = '辰戌丑未时辰出生';
        }
        if ($godTZS['zc'] >= 4 || $godTZS['pc'] >= 4) {
            $resKey[] = '正财或偏财数>=4';
        }
        if ($godZ['hour'] == '食神') {
            $resKey[] = '时支为食神';
        } elseif ($godZ['hour'] == '劫财') {
            $resKey[] = '时支为劫财';
        } elseif ($godZ['hour'] == '七杀') {
            if ($godT['hour'] != '食神') {
                $resKey[] = '时支为七杀且时干不为食神';
            }
        } elseif ($godZ['hour'] == '正官') {
            $resKey[] = '时支为正官';
        }

        $dzArr3 = array_intersect(['酉', '戌', '亥', '子', '丑'], $dzArr);
        if (count($dzArr3) == 4) {
            $resKey[] = '四柱地支为酉戌亥子丑的四个';
        }
        if ($godZ['year'] == '正财') {
            if ($godZ['month'] == '正财') {
                $resKey[] = '年支月支皆为正财';
            }
        }
        if (in_array('甲', $tgArr)) {
            if (in_array('', [$ytg . $ydz, $mtg . $mdz, $dgz, $htg . $hdz])) {
                $resKey[] = '有一柱为癸丑且天干有甲';
            }
        }
        if ($godZ['month'] == '正印') {
            if (in_array('d', $arrGx['m']['chong'])) {
                $resKey[] = '月支为正印且与日支冲';
            }
        }
        if ($numYan == 8 || $numYan == 0) {
            $resKey[] = '四柱是纯阳或者纯阴';
        }
        if ($numYan == 8) {
            $resKey[] = '八字纯阳';
        }
        if ($ytg == $mtg && in_array('d', $arrGx['m']['chong'])) {
            $resKey[] = '日干月干相同，日支月支冲';
        }
        if (in_array('天德', $shenShaRes['y']) || in_array('月德', $shenShaRes['y'])) {
            $resKey[] = '年柱有天德或月德';
        }
        if ($godTZS['qs'] >= 3) {
            if ($godTS['bj'] > 0 || $godTS['jc'] > 0) {
                $resKey[] = '七杀数>=3且天干有比肩或劫财';
            }
            if ($godTZS['qs'] >= 4 && $godTS['zy'] == 0) {
                $resKey[] = '七杀数>=4且天干无正印';
            }
        }
        if ($godTZS['jc'] > 0 && in_array('才财', [$xy['shen']['xi'], $xy['shen']['yong']])) {
            $resKey[] = '劫财对应五行为喜神或用神';
        }
        if (($godTZS['sh'] + $godTZS['sg']) > $godTZS['zc'] + $godTZS['qs']) {
            $resKey[] = '食伤和伤官数>正财和七杀';
        }
        $wrDay = BaziExt::getWr($dtg, $ddz);
        if ($wrDay == '旺' && $godTZS['bj'] >= 3) {
            $arr3 = $godT;
            unset($arr3['year']);
            $arr3 = array_values($arr3);

            if (array_intersect($arr1[4], $arr3) && empty(array_intersect($arr1[0], $arr3))) {
                $resKey[] = '日元旺且比肩数>=3且月干，时干，日元有正印或偏印且无正官或七杀';
            }
        }
        $arr3 = array_intersect(['癸', '乙', '壬'], $tgArr);
        if (count($arr3) == 3 && (in_array('卯', $dzArr) && in_array('酉', $dzArr))) {
            $resKey[] = '四柱天干有癸乙壬三干且地支有卯酉';
        }
        if ($godT['year'] == '正印') {
            if ($godTS['zy'] >= 2) {
                $resKey[] = '年干有正印，其他干也有正印';
            }
        }
        if ($godTS['py'] == 2) {
            $resKey[] = '天干有两个偏印';
        }
        if ($arrGx['d']['chong'] && $arrGx['d']['xin'] && $arrGx['d']['ke']) {
            if (in_array('羊刃', $shenShaRes['d'])) {
                $resKey[] = '日支被刑冲破且同柱有羊刃';
            }
        }
        if ($godTHS['qs'] && $godTHS['sh'] == 0 && $godTHS['sg'] == 0) {
            $resKey[] = '命局有七杀而无食神和伤官(看藏干)';
        }
        if ($godZ['hour'] == '偏印' && in_array('', [$xy['shen']['xi'], $xy['shen']['yong']])) {
            $resKey[] = '时支为偏印且为喜神或用神';
        }
        if (in_array('驿马', $shenShaRes['h'])) {
            $resKey[] = '时柱有驿马或食神，伤官同柱有驿马';
        }
        if ($base['jn_wx']['m']['gx'] == '冲' && $base['jn_wx']['d']['gx'] == '冲') {
            $resKey[] = '日月两柱天干与地支冲';
        }

        return array_values(array_unique($resKey));
        // 正印为喜神或用神且对应地支被冲或被合
        // 比肩或劫财同柱有桃花
        // 日元弱且被印星生扶
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'sh' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }

    /**
     * 获得某柱和其它柱的冲刑害破合等关系
     * @param string $k
     * @param array $jiNian
     * @return array
     */
    protected function getGx1(string $k, array $jiNian): array
    {
        $dz1 = $jiNian[$k][1];
        $tg1 = $jiNian[$k][0];
        $result = [
            'chong' => [], 'xin' => [], 'hai' => [], 'ke' => [], 'liu_he' => [], 'he_t' => [], 'ke_t' => [],
        ];
        foreach ($jiNian as $k1 => $v) {
            if ($k1 == $k) {
                continue;
            }
            $str = $v[1] . $dz1;
            if (BaziCommon::getXianChong($str)) {
                $result['chong'][] = $k1;
            }
            if (BaziCommon::getXianXin($str)) {
                $result['xin'][] = $k1;
            }
            if (BaziCommon::getXianHai($str)) {
                $result['hai'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['po'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['ke'][] = $k1;
            }
            if (BaziCommon::liuHeDz($str)) {
                $result['liuhe'][] = $k1;
            }
            if (BaziCommon::xianHeTg($tg1 . $v[0])) {
                $result['he_t'][] = $k1;
            }
            if (BaziCommon::getXianKe($tg1 . $v[0])) {
                $result['ke_t'][] = $k1;
            }
        }
        return $result;
    }
}
