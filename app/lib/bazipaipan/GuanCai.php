<?php
// +----------------------------------------------------------------------
// | 八字排盘-官运财运
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use calendar\Ex;
use calendar\Huangli;
use calendar\plugin\WuXing;

class GuanCai
{
    /**
     * 纪年
     * @var array
     */
    protected array $jiNian = [];

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 喜用
     * @var array
     */
    protected array $xy = [];

    /**
     * 神煞结果
     * @var array
     */
    protected array $shenShaRes = [];

    /**
     * 旺度
     * @var string
     */
    protected string $wangDu = '';

    /**
     * 官运
     * @param array $jiNian 八字纪年
     * @param array $userGod 用户十神
     * @param array $xy 喜用忌闲仇
     * @param array $shenShaRes 神煞结果
     * @param string $wangDu 旺度
     * @return array
     */
    public function getGuanYun(array $jiNian, array $userGod, array $xy, array $shenShaRes, string $wangDu): array
    {
        $this->jiNian = $jiNian;
        $this->userGod = $userGod;
        $this->xy = $xy;
        $this->shenShaRes = $shenShaRes;
        $this->wangDu = $wangDu;
        $godT = $userGod['god_t'];
        $godZ = $userGod['god_z'];
        $godHide = $userGod['god_hide'];
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTH = array_merge(array_values($godT), $godH2);
        $dtg = $jiNian['d'][0];
        $dgz = implode('', $jiNian['d']);
        $userTgs = array_column($jiNian, 0);
        $userDzs = array_column($jiNian, 1);
        $jnWx = $this->getJnwx($jiNian);
        $yiMaBool = false;
        $godS1 = array_count_values($godTH);
        $god11 = array_count_values($godTZ);
        $god20 = array_count_values($godH2);
        $god30 = array_count_values($godT);

        $godS1zy = $godS1['正印'] ?? 0;
        $godS1zg = $godS1['正官'] ?? 0;
        $godS1qs = $godS1['七杀'] ?? 0;
        $godS1zc = $godS1['正财'] ?? 0;
        $godS1pc = $godS1['偏财'] ?? 0;
        $sum11zc = $god11['正财'] ?? 0;
        $sum11pc = $god11['偏财'] ?? 0;
        $god20zg = $god20['正官'] ?? 0;
        $god20qs = $god20['七杀'] ?? 0;
        $god20zy = $god20['正印'] ?? 0;
        $god30qs = $god30['七杀'] ?? 0;
        $god30zg = $god30['正官'] ?? 0;
        $god30zc = $god30['正财'] ?? 0;
        $god30pc = $god30['偏财'] ?? 0;
        $god30py = $god30['偏印'] ?? 0;
        $kW = explode(',', Huangli::getKongWangbyGz(implode('', $jiNian['d'])));

        $key = [];
        if ($god20zg >= 3 || $god20qs >= 3 && in_array('官杀', [$xy['shen']['ji'], $xy['shen']['qiu']])) {
            $key[] = '藏干有正官或者七杀≥3，且对应五行为仇神或忌神';
        }
        if (($godS1zc + $godS1pc) >= 3) {
            $key[] = '正财和偏财数>=3';
        }
        if ($godS1zg == 0) {
            $key[] = '命局无正官(看藏干)';
        }
        if (in_array($godT['year'], ['正官', '七杀']) || array_intersect(['正官', '七杀'], $godHide['year']['god'])) {
            $key[] = '年柱有正官或者七杀';
        }
        if (in_array('官杀', [$xy['shen']['yong'], $xy['shen']['xi']]) && ($godS1zg + $godS1qs) > 0) {
            $key[] = '正官或七杀对应五行为喜神或用神';
        }
        if ($god30qs == 1 && !in_array('七杀', $godH2)) {
            $key[] = '天干七杀为1且地支无七杀';
        }
        if ($god30qs > 0) {
            $key[] = '天干有七杀';
        }
        // 正财或偏财数<=1（看藏干）
        if (($sum11zc + $sum11pc) <= 1) {
            $key[] = '正财或偏财数<=1（看藏干）';
        }
        if ($god30zg > 0) {
            $key[] = '天干有正官';
        }
        if ($god30zg == 1) {
            $key[] = '天干有1个正官';
        } elseif ($god30zg == 2) {
            $key[] = '天干正官数=2';
        }
        if ($godZ['day'] == '正官') {
            $key[] = '日支为正官';
        }
        if ($godT['year'] == '正官') {
            $key[] = '年干有正官';
        }
        if ($god30qs == 0 && $god20qs == 1) {
            $key[] = '天干无七杀且地支七杀数为1（看藏干）';
        }
        if ($god30zc <= 1 && $god30py <= 3 && $god30zg <= 1) {
            $key[] = '正财和偏财数<=1且正官<=1（看藏干）';
        }
        // 八字命局有七杀而无正印
        if ($godS1qs > 0 && $godS1zy == 0) {
            $key[] = '八字命局有七杀而无正印';
        }
        if ($god30qs == 0) {
            $key[] = '原局无一个七杀（看藏干）';
        }
        // 时柱有正官或者偏官
        if (in_array($godT['hour'], ['正官', '偏官']) || array_intersect(['正官', '偏官'], $godHide['hour']['god'])) {
            $key[] = '时柱有正官或者偏官';
        }
        if ($god30zg == 0 && $god20zg > 0) {
            $key[] = '天干无正官且地支有正官（看藏干）';
        }
        if ($god30zg == 0 && $god20zg == 1) {
            $key[] = '天干无正官且地支正官数=1（看藏干）';
        }
        if ($god30zg > 0 && $god20zg > 0) {
            $key[] = '天干有正官且地支已有正官（看藏干）';
        }
        // 天干有1个七杀且地支亦有一个七杀(看藏干)
        if ($god30qs == 1 && $god20qs == 1) {
            $key[] = '天干有1个七杀且地支亦有一个七杀(看藏干)';
        }
        $same1 = [];
        $sameNo1 = [];
        $same2 = 0;
        $same3 = [];
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            $tmpZ1 = $godHide[$k]['god'];
            if (in_array($v, $tmpZ1)) {
                $sameZhu[$v][] = $k;
            }
            if ($tmpZ1[0] == $v) {
                $sameNo1[$v][] = $k;
            }
            $sf2 = in_array($jnWx[$k1]['gx2'], ['生', '扶']);
            $yxDz1 = in_array($jnWx[$k1]['wx'][1], [$xy['xy']['yong'], $xy['xy']['xi']]);
            $hgai = in_array('华盖', $shenShaRes[$k1]);
            if ($yxDz1 && $hgai) {
                $key[] = '华盖同柱地支对应五行为喜神或用神';
            }
            if ($v == '正官') {
                $same2++;
                if ($sf2) {
                    $same2++;
                }
                if ($same2 >= 3) {
                    $key[] = '天干有两个正官且正官同柱地支五行生扶天干五行';
                }
                if ($tmpZ1[0] == '七杀') {
                    $key[] = '天干正官且同柱地支为七杀（不看藏干）';
                }
                $chongT1 = $this->checkChong($k1, $jiNian);
                $xinT1 = $this->checkXin($k1, $jiNian);
                $haiT1 = $this->checkHai($k1, $jiNian);
                if ($chongT1 && $xinT1) {
                    $key[] = '正官同柱地支遇刑冲';
                }
                if (in_array($jiNian[$k1][1], ['子', '午', '卯', '酉'])) {
                    $key[] = '天干有正官且同柱地支为子午卯酉其中';
                }
                if (!in_array('羊刃', $shenShaRes[$k1])) {
                    if (in_array('正印', $godT) || array_search('正印', $godZ) != $k) {
                        $key[] = '正官同柱无羊刃且它柱十神有正印';
                    }
                } else {
                    $key[] = '正官同柱有羊刃';
                }
                if ($chongT1 && $xinT1 && $haiT1) {
                    $key[] = '天干有正官且对应地支与它支有刑冲害';
                }
            } elseif ($v == '伤官') {
                if (in_array($tmpZ1[0], ['正印', '正财']) && $yxDz1) {
                    $key[] = '天干伤官且同柱地支为正印或正财且正印或正财对应五行为喜神或用神（不看藏干）';
                }
            } elseif ($v == '正印') {
                if ($tmpZ1[0] == '正官' && $yxDz1) {
                    $key[] = '天干有正印且同柱地支为正官且正官对应五行为喜神或用神';
                }
                if ($yxDz1 && $hgai) {
                    $key[] = '天干有正印且同柱有华盖且同柱地支五行为喜神或用神';
                }
            }
            if (in_array($v, ['正印', '偏印'])) {
                if (in_array('正印', $tmpZ1) || in_array('偏印', $tmpZ1)) {
                    $key[] = '天干有正财或偏财且正印或偏印，且正印或偏印同柱地支有正印或偏印（看藏干）';
                }
                if ($godS1qs == 0 && !in_array('羊刃', $shenShaRes[$k1])) {
                    $key[] = '正官，正印同柱无羊刃且命局无七杀';
                }
                $same3[] = $jiNian[$k1][1];
            }
            if ($v == '正印') {
                if ($sf2 && in_array('正官', $godT)) {
                    $key[] = '天干有正印且同柱地支五行生扶天干五行且天干有正官';
                }
            }
            if (in_array($v, ['食神', '伤官']) && array_intersect(['食神', '伤官'], $tmpZ1) && in_array($wangDu, ['身弱格', '从弱格'])) {
                $key[] = '身弱或从弱格且天干食神或伤官同柱乙酉食神或伤官(看藏干)';
            }
            if (in_array($v, ['正财', '偏财']) && $yxDz1 && in_array('伤官', $godT)) {
                $key[] = '天干有伤官且有正财或偏财且财星对应五行为喜神或用神';
            }
            if ($k == 'year') {
                if (in_array($v, ['正印', '偏印']) || in_array($tmpZ1[0], ['正印', '偏印'])) {
                    $key[] = '年柱有正印或偏印(不看藏干)';
                }
            } elseif ($k == 'month') {
                if (in_array($v, ['正官', '七杀']) || in_array($tmpZ1[0], ['正官', '七杀'])) {
                    $key[] = '月柱有正官或者七杀';
                }
            }
        }
        if (count($same3) == 2) {
            if ((!BaziCommon::getXianChong(implode('', $same3)) && !BaziCommon::getXianPo(implode('', $same3))) || array_intersect($kW, $same3)) {
                $key[] = '天干有正官和正印且正官正印同柱地支之间无冲破或空亡';
            }
        }
        if (isset($same1['偏印'])) {
            $key[] = '天干有偏印且同柱地支也有偏印(看藏干)';
        }
        if (isset($same1['正印'])) {
            $key[] = '天干正印且同柱地支亦有正印（看藏干）';
        }
        if (isset($sameNo1['正财'])) {
            $key[] = '天干有正财且同柱地支为正财';
        }
        if ($godS1qs == 0 && $godS1zy) {
            $key[] = '命局有正印且无七杀(看藏干)';
        }
        if ($godS1zc == 2) {
            $key[] = '正财数=2';
        }

        if (in_array($wangDu, ['身旺格', '从旺格'])) {
            if (in_array('正印', $godT) && ($god30zc + $god30pc) > 0 && $god20zy > 0) {
                $key[] = '身旺从旺且天干有正财或偏财无正印且地支有正印(看藏干)';
            }
            if ($god30zg == 0 && $god20qs > 0) {
                $key[] = '身旺或从旺且命局无正官且地支藏干有七杀(看藏干)';
            }
        }
        if ($god20zg == 2 && $god30zg == 0) {
            $key[] = '天干无正官且地支正官数=2（看藏干）';
        }
        if (in_array('辛', $userTgs)) {
            $arr = array_intersect(['巳', '酉', '丑'], $userDzs);
            if (count($arr) == 3) {
                $key[] = '天干有辛且地支有巳酉丑';
            }
        }
        if (array_intersect(['正印', '偏印'], $godT) && in_array($dtg, ['壬', '癸'])) {
            $key[] = '日元为壬或癸且天干有正印或偏印';
        }
        if ($god30qs == 1 && $god20qs == 1) {
            $key[] = '天干无七杀且地支七杀数为1（看藏干）';
        }
        $key = array_values($key);
        return $key;
    }

    /**
     * 正财
     * @param array $jiNian
     * @param array $userGod
     * @param array $xy
     * @param array $shenShaRes
     * @param string $wangDu
     * @return array
     */
    public function getZhiYeCaiYun(array $jiNian, array $userGod, array $xy, array $shenShaRes, string $wangDu): array
    {
        $this->jiNian = $jiNian;
        $this->userGod = $userGod;
        $this->xy = $xy;
        $this->shenShaRes = $shenShaRes;
        $this->wangDu = $wangDu;
        $godT = $userGod['god_t'];
        $godZ = $userGod['god_z'];
        $godHide = $userGod['god_hide'];
        $godH2 = array_merge($godHide['year']['god'], $godHide['month']['god'], $godHide['day']['god'], $godHide['hour']['god']);
        //$godH2Dz = array_merge($godHide['year']['hide'], $godHide['month']['hide'], $godHide['day']['hide'], $godHide['hour']['hide']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTH = array_merge(array_values($godT), $godH2);
        $dgz = implode('', $jiNian['d']);
        $jnWx = $this->getJnwx($jiNian);
        $yiMaBool = false;
        $godS1 = array_count_values($godTH);
        $god11 = array_count_values($godTZ);
        $godS1Ss = $godS1['食神'] ?? 0;
        $godS1Sg = $godS1['伤官'] ?? 0;
        $godS1zy = $godS1['正印'] ?? 0;
        $godS1py = $godS1['偏印'] ?? 0;
        $godS1bj = $godS1['比肩'] ?? 0;
        $godS1jc = $godS1['劫财'] ?? 0;
        $godS1zg = $godS1['正官'] ?? 0;
        $godS1qs = $godS1['七杀'] ?? 0;
        $godS1zc = $godS1['正财'] ?? 0;
        $godS1pc = $godS1['偏财'] ?? 0;

        $sum11Ss = $god11['食神'] ?? 0;
        $sum11Sg = $god11['伤官'] ?? 0;
        $key = [];
        if ($xy['xy']['yong'] == '火' || $xy['xy']['xi'] == '火') {
            $key[] = '喜神或用神为火';
        } elseif ($xy['xy']['yong'] == '木' || $xy['xy']['xi'] == '木') {
            $key[] = '喜神或用神为木';
        } elseif ($xy['xy']['yong'] == '金' || $xy['xy']['xi'] == '金') {
            $key[] = '喜神或用神为金';
        } elseif ($xy['xy']['yong'] == '土' || $xy['xy']['xi'] == '土') {
            $key[] = '喜神或用神为土';
        } else {
            $key[] = '喜神或用神为水';
        }
        foreach ($shenShaRes as $k => $v) {
            if (in_array('驿马', $v)) {
                $yiMaBool = true;
            }
        }
        if ($yiMaBool && in_array('正财', $godT)) {
            $key['天干有正财且神煞有驿马'] = '天干有正财且神煞有驿马';
        }
        if (($xy['xy']['yong'] == '火' || $xy['xy']['xi'] == '火') || $yiMaBool) {
            $key[] = '喜神或用神为水或有驿马';
        }
        $key[] = '月支为' . $godZ['month'];
        if ($xy['shen']['yong'] == '才财' || $xy['shen']['xi'] == '才财') {
            $key[] = '正财或偏财对应五行为喜神或用神';
        }
        if (!in_array('偏财', $godTH)) {
            $key[] = '命局无偏财(包藏干)';
        }
        if (array_intersect(['正财', '偏财'], $godH2)) {
            $key[] = '藏干十神有正财或偏财';
        }
        if (($godS1Ss + $godS1Sg) <= 2) {
            $key[] = '食神和伤官数<=2';
        }
        if ($godS1py > 3) {
            $key['八字十神为偏印＞3'] = '八字十神为偏印＞3';
        }
        if (in_array('七杀', $godTH) && in_array('正印', $godTH)) {
            $key[] = '同时存在七杀和正印';
        }
        if ($this->checkGodShen('正印', ['华盖', '文昌'])) {
            $key[] = '正印同柱有华盖或文昌或空亡';
        }
        if ($this->checkGodShen('偏财', ['天医', '禄神'])) {
            $key[] = '偏财同柱有天医和禄神';
        }
        // 偏财同柱禄神
        if ($this->checkGodShen('偏财', ['禄神'])) {
            $key[] = '偏财同柱禄神';
        }
        if ($this->checkGod3('偏印', '劫财', ['ji'])) {
            $key[] = '天干偏印且同柱地支有劫财且为忌神';
        }
        if ($this->checkGod3('食神', '食神')) {
            $key[] = '天干食神且同柱地支食神且食神对应五行为喜神或用神';
        }
        // 天干偏印且同柱地支为比肩且地支五行为忌神
        if ($this->checkGod3('偏印', '比肩')) {
            $key[] = '天干偏印且同柱地支为比肩且为忌神';
        }
        $arr = $godZ;
        $arr2 = $godT;
        $arr3 = [$xy['xy']['yong'], $xy['xy']['xi']];
        unset($arr2['month'], $arr['month']);

        switch ($godZ['month']) {
            case '正官':
                if (array_intersect($arr, ['正财', '偏财', '正印', '偏印'])) {
                    $key[] = '月支为正官且它支有正财/偏财、正印/偏印';
                }
                if ($this->checkGodZ1('比肩')) {
                    $key[] = '月支为正官且它支有比肩且比肩对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('偏印')) {
                    $key[] = '月支为正官且它支有偏印且偏印对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('七杀', ['ji'])) {
                    $key[] = '月支为正官且它支有七杀且七杀为忌神';
                }
                if ($this->checkGodZ1('伤官', ['ji'])) {
                    $key[] = '月支为正官且它支有伤官且为忌神';
                }
                if ($this->checkGodZ1('劫财', ['ji'])) {
                    $key[] = '月支为正官且它支有劫财且为忌神';
                }
                break;
            case '比肩':
                if ($this->checkGodZ1('正财')) {
                    $key[] = '月支为比肩且它支为正财且正财对应的五行为喜神或用神';
                }
                if ($this->checkGodZ1('食神')) {
                    $key[] = '月支为比肩且它支有食神且食神对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('劫财', ['ji'])) {
                    $key[] = '月支为比肩且它支有劫财且劫财对应五行为忌神';
                }
                break;
            case '食神':
                if ($this->checkGodZ1('劫财')) {
                    $key[] = '月支为食神且它只为劫财且劫财对应的五行为喜神或用神';
                }
                if ($this->checkGodZ1('比肩')) {
                    $key[] = '月支为食神且它支有比肩且比肩对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('七杀', ['ji'])) {
                    $key[] = '月支为食神且它支有七杀且七杀对应五行为忌神';
                }
                break;
            case '七杀':
                if ($this->checkGodZ1('正财')) {
                    $key[] = '月支为七杀且它支有正财且正财对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('七杀', ['ji'])) {
                    $key[] = '月支为七杀且它支有七杀且七杀对应五行为忌神';
                }
                break;
            case '偏印':
                if ($this->checkGodZ1('偏印', ['ji'])) {
                    $key[] = '月支为偏印且它支为偏印且它支对应五行为忌神';
                }
                if ($this->checkGodZ1('正财')) {
                    $key[] = '月支为偏印且它支有正财且正财对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('比肩')) {
                    $key[] = '月支为偏印且它支为比肩且比肩对应五行为喜神或用神';
                }
                if ($this->checkGodZ1('偏印')) {
                    $key[] = '月支为偏印且它支有偏印且为喜神或用神';
                }
                if ($godZ['month'] == '偏印') {
                    $key['月干月支十神皆为偏印'] = '月干月支十神皆为偏印';
                    //月干支十神有偏印，且为喜神或用神
                    if (in_array($jnWx['m']['wx'][0], $arr3)) {
                        $key['月干支十神有偏印，且为喜神或用神'] = '月干支十神有偏印，且为喜神或用神';
                    }
                }

                break;
            case '劫财':
                if ($this->checkGodZ1('正印', ['ji'])) {
                    $key[] = '月支为劫财且它支为正印且正印对应五行为忌神';
                }
                if ($this->checkGodZ1('劫财', ['ji'])) {
                    $key[] = '月支为劫财且它支有比肩且比肩对应五行为忌神';
                }
                if (in_array('正财', $arr)) {
                    $key[] = '月支为劫财且它支有正财';
                }
                break;
            case '偏财':
                if (array_intersect($arr, ['正官', '七杀', '正印', '偏印'])) {
                    $key[] = '月支为偏财且它支有官、印';
                }
                if ($godT['month'] == '偏财') {
                    $key[] = '月干月支十神皆为偏财';
                }
                break;
            case '正财':
                if (in_array('七杀', $arr)) {
                    $key[] = '月支为正财且它支有七杀';
                }
                if ($godT['month'] == '正财' && in_array($jnWx['m']['wx'][0], [$xy['xy']['yong'], $xy['xy']['ji']])) {
                    $key[] = '月干或月支为正财且对应五行为喜神或用神';
                }
                if (array_intersect($arr, ['食神', '正官', '七杀', '正印', '偏印'])) {
                    $key[] = '月支为正财且它支有食神、官杀和正偏印';
                }
                if ($this->checkGodZ1('比肩', ['ji'])) {
                    $key[] = '月支为正财且它支有比肩且为忌神';
                }
                if ($godT['month'] == '正财' && array_intersect(['正财', '偏财'], $arr) && array_intersect(['正财', '偏财'], $arr2)) {
                    $key[] = '月柱干支皆为正财且其他干支没有正财或偏财';
                }
                break;
            case '伤官':
                // 月支为伤官且它支有七杀且七杀对应五行为忌神
                if ($this->checkGodZ1('七杀', ['ji'])) {
                    $key[] = '月支为伤官且它支有七杀且七杀对应五行为忌神';
                }
                break;
            case '正印':
                if (in_array($jnWx['m']['wx'][1], [$xy['xy']['yong'], $xy['xy']['ji']])) {
                    $key[] = '月支为正印且为喜神或用神';
                }
                if ($this->checkGodZ1('偏印', ['ji'])) {
                    $key[] = '月支为正印且它支有偏印且偏印对应五行为忌神';
                }
                if ($this->checkGodZ1('比肩', ['ji'])) {
                    $key[] = '月支为正印且它支有比肩且比肩对应五行为忌神';
                }
                if (in_array('伤官', $arr) && in_array('正财', $arr)) {
                    $key[] = '月支为正印且它支有伤官、正财';
                }
                if (array_intersect(['食神', '正官'], $arr)) {
                    $key[] = '月支为正印且它支有食神、正官';
                }
                if (array_intersect(['正官'], $arr)) {
                    $key[] = '月支为正印且它支有正官';
                }
                break;
        }
        $terrainArr = Ex::getTerrainData();
        $terrain = [
            'year' => $terrainArr[$jiNian['d'][0] . $jiNian['y'][1]],
            'month' => $terrainArr[$jiNian['d'][0] . $jiNian['m'][1]],
            'day' => $terrainArr[$jiNian['d'][0] . $jiNian['d'][1]],
            'hour' => $terrainArr[$jiNian['d'][0] . $jiNian['h'][1]],
        ];
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            // 天干有正财或偏财且同柱地支为辰戌丑未
            if (in_array($v, ['正财', '偏财']) && in_array($jiNian[$k1][1], ['辰', '戌', '丑', '未'])) {
                $key['天干有正财或偏财且同柱地支为辰戌丑未'] = '天干有正财或偏财且同柱地支为辰戌丑未';
            }
            $tWx = $jnWx[$k1]['wx'];
            if ($v == '正印' && BaziCommon::getWxGuanxi($tWx[1]) == $tWx[0]) {
                $key['天干正印同柱地支五行生扶'] = '天干正印同柱地支五行生扶';
            }
            if ($v == '劫财' && in_array('羊刃', $shenShaRes[$k1])) {
                $key['天干有劫财且同柱有羊刃'] = '天干有劫财且同柱有羊刃';
            }
            // 天干有正官且同柱有劫煞
            if ($v == '正官' && in_array('劫煞', $shenShaRes[$k1])) {
                $key['天干有正官且同柱有劫煞'] = '天干有正官且同柱有劫煞';
            }
            // 天干有食神且同柱地支有贵人星
            if ($v == '食神' && in_array('贵人星', $shenShaRes[$k1])) {
                $key['天干有食神且同柱地支有贵人星'] = '天干有食神且同柱地支有贵人星';
            }
            if ($v == '伤官' && $godZ[$k] == '偏财') {
                $key['天干伤官且同柱地支为偏财'] = '天干伤官且同柱地支为偏财';
            }
            if ($v == '伤官' && $godZ[$k] == '偏印') {
                $key['天干有伤官且同柱地支为偏印'] = '天干有伤官且同柱地支为偏印';
            }
            if ($v == '偏印' && in_array($terrain[$k], ['衰', '病', '死', '绝'])) {
                $key['天干有偏印且同柱十二长生为衰，病，死，绝之一'] = '天干有偏印且同柱十二长生为衰，病，死，绝之一';
            }
            if ($v == '食神' && $godZ[$k] == '正印' && $tWx[1] == $xy['xy']['yong']) {
                $key['食神与正印同柱且正印为用神'] = '食神与正印同柱且正印为用神';
            } elseif ($v == '正印' && $godZ[$k] == '食神' && $tWx[0] == $xy['xy']['yong']) {
                $key['食神与正印同柱且正印为用神'] = '食神与正印同柱且正印为用神';
            }
            if (in_array('贵人星', $shenShaRes[$k1]) && in_array('驿马', $shenShaRes[$k1]) && in_array('禄神', $shenShaRes[$k1])) {
                $key['同柱有驿马，禄神以及贵人星'] = '同柱有驿马，禄神以及贵人星';
            }
            if (in_array('驿马', $shenShaRes[$k1]) && in_array($godZ[$k], ['正财', '偏财'])) {
                $key['驿马同柱地支五行为正财或偏财'] = '驿马同柱地支五行为正财或偏财';
            }
            if ($v == '偏印' && in_array($terrain[$k], ['长生', '冠带', '临官', '帝旺']) && in_array($tWx[0], [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $key['天干有偏印且同柱十二长生为长生、冠带、临官、帝旺其一且为喜神或用神'] = '天干有偏印且同柱十二长生为长生、冠带、临官、帝旺其一且为喜神或用神';
            }
            if ((in_array($v, ['比肩', '劫财', '伤官']) && $xy['xy']['ji'] == $tWx[0]) || (in_array($godZ[$k], ['比肩', '劫财', '伤官']) && $xy['xy']['ji'] == $tWx[1])) {
                $key['八字有比劫或者伤官，且为忌神'] = '八字有比劫或者伤官，且为忌神';
            }
            if ($v == '食神' && in_array($godZ[$k], ['正官', '七杀'])) {
                $key['天干有食神且它支有正官和七杀(不看藏干)'] = '天干有食神且它支有正官和七杀(不看藏干)';
            }
            if ((in_array($v, ['正财', '偏财']) && in_array($tWx[0], [$xy['xy']['ji'], $xy['xy']['qiu']])) || (in_array($godZ[$k], ['正财', '偏财']) && in_array($tWx[1], [$xy['xy']['ji'], $xy['xy']['qiu']]))) {
                $key['财星对应五行为忌神或仇神'] = '财星对应五行为忌神或仇神';
            }
            if ($v == '偏财' && in_array('偏财', $godHide[$k]['god'])) {
                $key['天干有偏财且地支亦有偏财（看藏干）'] = '天干有偏财且地支亦有偏财（看藏干）';
            }
            if ((in_array($v, ['偏印']) && in_array($tWx[0], [$xy['xy']['xi'], $xy['xy']['yong']])) || (in_array($godZ[$k], ['偏印']) && in_array($tWx[1], [$xy['xy']['yong'], $xy['xy']['xi']]))) {
                $key['十神有偏印，且为喜神或用神'] = '十神有偏印，且为喜神或用神';
            }
            if ((in_array($v, ['比肩', '劫财']) && in_array($tWx[0], [$xy['xy']['xi'], $xy['xy']['yong']])) || (in_array($godZ[$k], ['比肩', '劫财']) && in_array($tWx[1], [$xy['xy']['xi'], $xy['xy']['yong']]))) {
                $key['十神有比肩或者劫财，且为喜神或用神'] = '十神有比肩或者劫财，且为喜神或用神';
            }
        }
        if (in_array($godZ['day'], ['正财', '偏财']) && in_array($terrain['day'], ['长生', '冠带', '临官', '帝旺'])) {
            $key[] = '日支为财星且同柱十二长生为长生、冠带、临官、帝旺其一';
        }
        if (($godT['month'] == '七杀' || $xy['xy']['ji'] != $jnWx['m']['wx'][0]) || ($godZ['month'] == '七杀' || $xy['xy']['ji'] != $jnWx['m']['wx'][1])) {
            $key[] = '月柱有七杀，且对应五行不为忌神';
        }
        if ($this->checkGod2('伤官', '水')) {
            $key[] = '伤官对应五行为水';
        }
        $dzArr = array_column($jiNian, 1);
        $arr2 = array_intersect(['子', '午', '卯', '酉'], $dzArr);
        $arr3 = explode(',', Huangli::getKongWangbyGz(implode('', $jiNian['d'])));
        if (count($arr2) == 4 && in_array($jiNian['y'][1], $arr3)) {
            $key[] = '地支有子午卯酉且地支有空亡';
        }
        if (array_intersect(['正财', '偏财'], $godTH)) {
            $key[] = '十神有正财或偏财';
        }
        if (in_array($wangDu, ['身旺格', '从旺格'])) {
            if (($godS1zy + $godS1py) >= 4) {
                $key['身旺或从旺且正印或偏印数>=4（看藏干）'] = '身旺或从旺且正印或偏印数>=4（看藏干）';
            }
            if (($godS1zc + $godS1py) <= 2) {
                $key['身旺或从旺且正财和偏财数<=2'] = '身旺或从旺且正财和偏财数<=2';
            }
            if (in_array('偏印', $godT) && array_intersect(['正财', '偏财'], $godT)) {
                $key['身旺或从旺且天干有偏印和正财或偏财'] = '身旺或从旺且天干有偏印和正财或偏财';
            }
            if (in_array('七杀', $godT) && array_intersect(['正财', '偏财'], $godT)) {
                $key['身旺或从旺且天干有正财或偏财且有七杀'] = '身旺或从旺且天干有正财或偏财且有七杀';
            }
        }
        if ($godS1zc > $godS1py && $this->checkGod2('正财', $xy['xy']['yong'])) {
            $key['命局中偏财数>正财数且正财对应五行为用神'] = '命局中偏财数>正财数且正财对应五行为用神';
        }
        if (($sum11Sg + $sum11Ss) <= 2) {
            $key[] = '命局食神和伤官数<=2';
        }
        if (($godS1bj + $godS1jc) >= 3) {
            $key[] = '比肩或劫财数>=3(看藏干)';
        }
        if ($this->checkGod2('正官', '水')) {
            $key[] = '正官对应五行为水';
        } elseif ($this->checkGod2('正官', '金')) {
            $key[] = '正官对应五行为金';
        } elseif ($this->checkGod2('正官', '木')) {
            $key[] = '正官对应五行为木';
        } elseif ($this->checkGod2('正官', '火')) {
            $key[] = '正官对应五行为火';
        } elseif ($this->checkGod2('正官', '土')) {
            $key[] = '正官对应五行为土';
        }
        if ($godS1jc >= 3) {
            $key[] = '劫财数>=3（看藏干）';
        }
        if (($godT['month'] == '劫财' || $xy['xy']['ji'] == $jnWx['m']['wx'][0]) || ($godZ['month'] == '劫财' || $xy['xy']['ji'] == $jnWx['m']['wx'][1])) {
            $key[] = '月柱十神有劫财，且为忌神';
        }
        if (in_array('劫财', $godT) && in_array('七杀', $godT)) {
            $key[] = '天干有劫财和七杀';
        }
        if (($godS1zg + $godS1qs) <= 2) {
            $key[] = '命局正官和正印数<=2(看藏干)';
        }
        if (in_array('正财', $godT)) {
            $key[] = '天干有正财';
            if (array_intersect(['正财', '偏财'], $godH2)) {
                $key[] = '天干有正财且地支有正财或偏财(看藏干)';
            }
            if (in_array('正官', $godT)) {
                $key['天干有正官和正财'] = '天干有正官和正财';
            }
        }
        if ($godT['year'] == '正官' && $godZ['year'] == '正官') {
            $key[] = '年干或年支为正官（不看藏干）';
        }
        if (array_intersect(['正财', '偏财'], $godT) && array_intersect(['食神', '伤官'], $godT)) {
            $key['天干有食神或伤官且有正财或偏财'] = '天干有食神或伤官且有正财或偏财';
        }
        if ($wangDu == '从弱格') {
            $key['从弱格'] = '从弱格';
        }
        if (in_array('禄神', $shenShaRes['h'])) {
            $key['禄神在时柱'] = '禄神在时柱';
        }
        if (in_array($wangDu, ['身弱格', '从弱格']) && ($godS1zc + $godS1pc) >= 4) {
            $key['身弱格或从弱格且正财和偏财>=4（看藏干）'] = '身弱格或从弱格且正财和偏财>=4（看藏干）';
            if (in_array('比肩', $godT)) {
                $key['身弱或从弱格且正财和偏财数>=4且天干有比肩'] = '身弱或从弱格且正财和偏财数>=4且天干有比肩';
            }
        }
        if (in_array($dgz, ['戊戌', '壬辰'])) {
            $key['日柱戊戌或壬辰'] = '日柱戊戌或壬辰';
        }
        if ($dgz == '戊辰') {
            $key['日柱戊辰'] = '日柱戊辰';
        }
        if ($dgz == '戊辰') {
            $key['日柱庚申'] = '日柱庚申';
        }
        if (array_intersect(['正财', '偏财'], $godT) && array_intersect(['正官', '七杀'], $godT) && array_intersect(['正印', '偏印'], $godT)) {
            $key['天干同时有正财或偏财且有正官或七杀且有正印或偏印'] = '天干同时有正财或偏财且有正官或七杀且有正印或偏印';
        }
        if (in_array('驿马', $shenShaRes['h'])) {
            if (!$this->checkChong('h', $jiNian) && !$this->checkPo('h', $jiNian)) {
                $key['时柱有驿马且时支无冲无破'] = '时柱有驿马且时支无冲无破';
            }
        }
        if (in_array('正财', $godTH)) {
            $key['命局无正财（看原局）'] = '命局无正财（看原局）';
        }
        if (($godS1Ss + $godS1Sg) >= 4 || ($godS1zc + $godS1pc) >= 4) {
            $key['命局食神和伤官数>=4或正财和偏财>=4'] = '命局食神和伤官数>=4或正财和偏财>=4';
        }
        if ((in_array('食神', $godT) && (($godS1Ss + $godS1jc) > ($godS1bj + $godS1zc)))) {
            $key['天干有食神且劫财和偏财数>比肩和正财数（看藏干）'] = '天干有食神且劫财和偏财数>比肩和正财数（看藏干）';
        }
        if (!in_array('偏财', [$godT['year'], $godT['month']]) && !in_array('偏财', array_merge($godHide['year']['god'], $godHide['month']['god']))) {
            if ('偏财' == $godT['day'] && in_array('偏财', $godHide['day']['god']) && in_array($jnWx['d']['wx'][0], [$xy['xy']['yong'], $xy['xy']['xi']])) {
                $key['年月柱没有偏财，日时柱有偏财且为喜神或用神'] = '年月柱没有偏财，日时柱有偏财且为喜神或用神';
            }
        }
        if (in_array('正官', $godT) && !in_array('正财', $godT)) {
            if (in_array('正财', $godH2) && !in_array('正官', $godH2)) {
                $key['天干有正官且无正财且地支有正财无正官（看藏干）'] = '天干有正官且无正财且地支有正财无正官（看藏干）';
            }
        }
        if ((array_intersect(['正财', '偏财'], $godT) && ($godS1zc + $godS1pc) >= 4)) {
            $key[] = '天干有正财或偏财且正财和偏财数>=4';
        }
        if (($godS1zg + $godS1zy) <= 1) {
            $key[] = '命局正官和正印数<=1(看藏干)';
        }
        if (in_array('正印', $godT) && in_array('七杀', $godT)) {
            $key[] = '天干有正印和七杀';
        }
        if (in_array('正印', $godTH) && in_array('七杀', $godTH)) {
            $key[] = '命局有正印和偏印';
        }
        if (in_array('七杀', $godT) && in_array('正官', $godT)) {
            $key[] = '天干有七杀和正官';
        }
        if (($godS1zc + $godS1py) >= 4 && $godS1zy >= 4) {
            $key[] = '正财和偏财数>=4且正印数>=4（看藏干）';
        }
        if ($godT['year'] == '偏财') {
            if ($godZ['year'] == '偏财') {
                $key[] = '年干年支皆为偏财';
            }
            if (in_array($jnWx['y']['wx'][0], $arr3)) {
                $key[] = '年干十神为偏财，且为喜神或用神';
            }
        }
        if (in_array('正官', $godT) && in_array('正印', $godT)) {
            $key[] = '天干有正官和正印';
        }
        if (in_array('食神', $godT) && in_array('偏印', $godT)) {
            $key[] = '天干有食神和偏印';
        }
        if ($jiNian['d'][1] == '戊') {
            $key[] = '日元为戊';
        }
        // 命局有正财或偏财且偏印
        if (($godS1zc + $godS1pc) > 0 && $godS1py > 0) {
            $key[] = '命局有正财或偏财且偏印';
        }
        return array_values(array_unique($key));
    }

    /**
     * 检查地支相冲
     * @param string $k
     * @param array $jiNian
     * @return bool
     */
    private function checkChong(string $k, array $jiNian): bool
    {
        $str = $jiNian[$k][1];
        $bool = false;
        foreach ($jiNian as $key => $v) {
            if ($key == $k) {
                continue;
            }
            $dz = $v[1];
            if (BaziCommon::getXianChong($str . $dz)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 检查地支相冲
     * @param string $k
     * @param array $jiNian
     * @return bool
     */
    private function checkPo(string $k, array $jiNian): bool
    {
        $str = $jiNian[$k][1];
        $bool = false;
        foreach ($jiNian as $key => $v) {
            if ($key == $k) {
                continue;
            }
            $dz = $v[1];
            if (BaziCommon::getXianPo($str . $dz)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 检查地支相刑
     * @param string $k
     * @param array $jiNian
     * @return bool
     */
    private function checkXin(string $k, array $jiNian): bool
    {
        $str = $jiNian[$k][1];
        $bool = false;
        foreach ($jiNian as $key => $v) {
            if ($key == $k) {
                continue;
            }
            $dz = $v[1];
            if (BaziCommon::getXianXin($str . $dz)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 检查地支相害
     * @param string $k
     * @param array $jiNian
     * @return bool
     */
    private function checkHai(string $k, array $jiNian): bool
    {
        $str = $jiNian[$k][1];
        $bool = false;
        foreach ($jiNian as $key => $v) {
            if ($key == $k) {
                continue;
            }
            $dz = $v[1];
            if (BaziCommon::getXianHai($str . $dz)) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian
     * @return array
     */
    protected function getJnwx(array $jiNian): array
    {
        $result = [];
        $wuXingAttr = WuXing::GZ_TO_WX;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        return $result;
    }

    /**
     * 指定十神同柱下有指定神煞
     * @param string $godName
     * @param array $shen
     * @return bool
     */
    protected function checkGodShen(string $godName, array $shen = []): bool
    {
        $godT = $this->userGod['god_t'];
        $godHide = $this->userGod['god_hide'];
        $shenArr = $this->shenShaRes;
        $bool = false;
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            if ($v == $godName || in_array($godHide, $godHide[$k]['god'])) {
                if (array_intersect($shen, $shenArr[$k1])) {
                    $bool = true;
                    break;
                }
            }
        }
        return $bool;
    }

    /**
     * 天干指定十神且同柱地支有批定十神且为(喜用忌闲仇)神
     * @param string $godName 天干十神名
     * @param string $godName1 地支十神名
     * @param array|string[] $xyKey 喜用忌闲仇数组键名
     * @return bool
     */
    protected function checkGod3(string $godName, string $godName1, array $xyKey = ['yong', 'xi']): bool
    {
        $godZ = $this->userGod['god_z'];
        $godT = $this->userGod['god_t'];
        $xy = $this->xy;
        $arr2 = [];
        foreach ($xyKey as $v) {
            $arr2[] = $xy['xy'][$v];
        }
        $wxAttr = WuXing::GZ_TO_WX;
        $jiNian = $this->jiNian;
        $bool1 = false;
        foreach ($godT as $k => $v) {
            if ($v == $godName && $godZ[$k] == $godName1) {
                $k1 = substr($k, 0, 1);
                $wx = $wxAttr[$jiNian[$k1][1]] ?? '';
                if (in_array($wx, $arr2)) {
                    $bool1 = true;
                    break;
                }
            }
        }
        return $bool1;
    }

    /**
     * 指定十神对应指定五行
     * @param string $godName 十神名
     * @param string $wx1 五行
     * @return bool
     */
    protected function checkGod2(string $godName, string $wx1 = '火'): bool
    {
        $godT = $this->userGod['god_t'];
        $godHide = $this->userGod['god_hide'];
        $wxAttr = WuXing::GZ_TO_WX;
        $jiNian = $this->jiNian;
        $bool = false;
        foreach ($godT as $k => $v) {
            $k1 = substr($k, 0, 1);
            $t1 = $wxAttr[$jiNian[$k1][0]];
            if ($v == $godName && $t1 == $wx1) {
                $bool = true;
                break;
            }
            $t2 = $wxAttr[$jiNian[$k1][1]];
            if (in_array($godName, $godHide[$k]['god']) && $t2 == $wx1) {
                $bool = true;
                break;
            }
        }
        return $bool;
    }

    /**
     * 检查指定十神是否存在且为喜用
     * @param string $godName 十神名
     * @param array $xyKey 喜用数组
     * @param string $key 排除支名
     * @return bool
     */
    protected function checkGodZ1(string $godName, array $xyKey = ['yong', 'xi'], string $key = 'month'): bool
    {
        $godZ = $this->userGod['god_z'];
        unset($godZ[$key]);
        $bool1 = false;
        $jiNian = $this->jiNian;
        $wxAttr = WuXing::GZ_TO_WX;
        $xy = $this->xy;
        $arr2 = [];
        foreach ($xyKey as $v) {
            $arr2[] = $xy['xy'][$v];
        }
        foreach ($godZ as $k => $v) {
            if ($v != $godName) {
                continue;
            }
            $str1 = substr($k, 0, 1);
            $wx = $wxAttr[$jiNian[$str1][1]] ?? '';
            if (in_array($wx, $arr2)) {
                $bool1 = true;
                break;
            }
        }
        return $bool1;
    }
}
