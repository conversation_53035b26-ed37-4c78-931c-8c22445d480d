<?php
// +----------------------------------------------------------------------
// | 八字排盘-职业财运
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use app\lib\Utils;
use app\model\Duanyu;
use calendar\Ex;
use calendar\exceptions\Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Zhiyecaiyun
{
    /**
     * 生日：1998-03-20 10:0:00
     * @var string
     */
    protected string $birthday;

    /**
     * 性别
     * @var int
     */
    protected int $gender;

    /**
     * 喜用神
     * @var string
     */
    protected string $xy;

    /**
     * 基础
     * @var Ex $lunar
     */
    protected Ex $lunar;

    /**
     * 断语列表数据
     * @var array
     */
    protected array $duanyuList = [];

    /**
     * 结果集
     * @var array
     */
    protected array $result = [];

    /**
     * 初始化
     * @param string $birthday 生日：1998-03-20 10:0:00
     * @param int $gender 性别：0男 1 女
     * @param string $xy 喜用神五行：如 金木水
     * @throws Exception
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct(string $birthday, int $gender = 0, string $xy = '')
    {
        $this->birthday = $birthday;
        $this->gender = $gender;
        // 农历类对象
        $this->lunar = Ex::date($birthday)->sex($gender);
        // 断语列表数据
        $this->duanyuList = Duanyu::getLists(3);
        // 喜用神
        $this->xy = $xy;
    }

    /**
     * 喜神或用神
     * 示例：喜神或用神为火
     * @return array
     * @throws Exception
     */
    public function getXyswx(): array
    {
        $baziEx = new BaziEx($this->lunar);
        $xy = $baziEx->getXiyongJi3();
        $arr = Utils::mbStrSplit($this->xy);
        if (count($arr) == 5) {
            $xy['xy'] = [
                'yong' => $arr[0], 'xi' => $arr[1], 'xian' => $arr[4], 'qiu' => $arr[3], 'ji' => $arr[2],
            ];
        }
        $wx = $xy['xy']['yong'];
        $title = "喜神或用神为{$wx}";
        $res = [];
        $res[] = $this->operateResult($title);
        // 喜神或用神的五行
        switch ($wx) {
            case '水':
                $base = $this->lunar->getLunarByBetween();
                $shaShenObj = new ShaShen();
                $jiNian = $base['jinian'];
                // 用户神煞
                $shenSha = $shaShenObj->detail($jiNian, $this->gender);
                unset($shenSha['o']);
                foreach ($shenSha as $v) {
                    if (in_array('驿马', $v)) {
                        $title2 = "喜神或用神为{$wx}或{$v}";
                    }
                }
                break;
        }
        if (isset($title2)) {
            $res[] = $this->operateResult($title2);
        }
        return $res;
    }

    /**
     * 月的地支
     * 示例：月支为正印
     * @return array
     * @throws Exception
     */
    public function getYuezhi(): array
    {
        $base = $this->lunar->getLunarByBetween();
        $jiNian = $base['jinian'];
        $godZ = BaziCommon::getGod2($jiNian);
        // 组合成标题
        $title = "月支为{$godZ['month']}";
        // 结果集
        $res = [];
        $res[] = $this->operateResult($title);
        $baziEx = new BaziEx($this->lunar);
        $xy = $baziEx->getXiyongJi3();
        // 用神
        $yong = $xy['shen']['yong'];
        // 喜神
        $xi = $xy['shen']['xi'];
        // 忌神
        $ji = $xy['shen']['ji'];
        switch ($godZ['month']) {
            case '正印':
                if ($godZ['year'] === '食神' || $godZ['day'] === '食神' || $godZ['hour'] === '食神' ||
                    $godZ['year'] === '正官' || $godZ['day'] === '正官' || $godZ['hour'] === '正官') {
                    $title = '月支为正印且它支有食神、正官';
                    $res[] = $this->operateResult($title);
                    $title = '月支为正印且它支有正官';
                    $res[] = $this->operateResult($title);
                }
                $arr = Utils::mbStrSplit($this->xy);
                if (count($arr) == 5) {
                    $xy['xy'] = [
                        'yong' => $arr[0], 'xi' => $arr[1], 'xian' => $arr[4], 'qiu' => $arr[3], 'ji' => $arr[2],
                    ];
                }
                if ($yong === '印枭' || $xi === '印枭') {
                    $title = '月支为正印且为喜神或用神';
                    $res[] = $this->operateResult($title);
                }
                if ($godZ['year'] === '偏印' || $godZ['day'] === '偏印' || $godZ['hour'] === '偏印') {
                    if ($ji === '印枭') {
                        $title = '月支为正印且它支有偏印且偏印对应五行为忌神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '比肩' || $godZ['day'] === '比肩' || $godZ['hour'] === '比肩') {
                    if ($ji === '比劫') {
                        $title = '月支为正印且它支有比肩且比肩对应五行为忌神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '伤官' || $godZ['day'] === '伤官' || $godZ['hour'] === '伤官' ||
                    $godZ['year'] === '正财' || $godZ['day'] === '正财' || $godZ['hour'] === '正财') {
                    $title = '月支为正印且它支有伤官、正财';
                    $res[] = $this->operateResult($title);
                }
                break;
            case '正官':
                if ($godZ['year'] === '正财' || $godZ['day'] === '正财' || $godZ['hour'] === '正财' ||
                    $godZ['year'] === '偏财' || $godZ['day'] === '偏财' || $godZ['hour'] === '偏财' ||
                    $godZ['year'] === '正印' || $godZ['day'] === '正印' || $godZ['hour'] === '正印' ||
                    $godZ['year'] === '偏印' || $godZ['day'] === '偏印' || $godZ['hour'] === '偏印') {
                    $title = '月支为正官且它支有正财/偏财、正印/偏印';
                    $res[] = $this->operateResult($title);
                }
                if ($godZ['year'] === '比肩' || $godZ['day'] === '比肩' || $godZ['hour'] === '比肩') {
                    if ($yong === '比劫' || $xi === '比劫') {
                        $title = '月支为正官且它支有比肩且比肩对应五行为喜神或用神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '偏印' || $godZ['day'] === '偏印' || $godZ['hour'] === '偏印') {
                    if ($yong === '印枭' || $xi === '印枭') {
                        $title = '月支为正官且它支有偏印且偏印对应五行为喜神或用神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '七杀' || $godZ['day'] === '七杀' || $godZ['hour'] === '七杀') {
                    if ($ji === '杀官') {
                        $title = '月支为正官且它支有七杀且七杀对应五行为忌神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '伤官' || $godZ['day'] === '伤官' || $godZ['hour'] === '伤官') {
                    if ($ji === '食伤') {
                        $title = '月支为正官且它支有伤官且伤官对应五行为忌神';
                        $res[] = $this->operateResult($title);
                    }
                }
                if ($godZ['year'] === '劫财' || $godZ['day'] === '劫财' || $godZ['hour'] === '劫财') {
                    if ($ji === '比劫') {
                        $title = '月支为正官且它支有劫财且劫财对应五行为忌神';
                        $res[] = $this->operateResult($title);
                    }
                }
                break;
        }
        // 获取天干十神
        $godT = $this->lunar->getGod();
        $jnGx1 = $this->jnwx($jiNian);
        if ($godT['year'] === '正官') {
            $title = "正官对应五行为{$jnGx1['y']['wx'][0]}";
            $res[] = $this->operateResult($title);
        }
        if ($godT['month'] === '正官') {
            $title = "正官对应五行为{$jnGx1['m']['wx'][0]}";
            $res[] = $this->operateResult($title);
        }
        if ($godT['hour'] === '正官') {
            $title = "正官对应五行为{$jnGx1['h']['wx'][0]}";
            $res[] = $this->operateResult($title);
        }
        if ($godT['year'] === '伤官') {
            $title = "伤官对应五行为{$jnGx1['y']['wx'][0]}";
            $res[] = $this->operateResult($title);
        }
    }

    /**
     * 处理结果集
     * @param string $title 算法标题
     * @return array
     */
    protected function operateResult(string $title): array
    {
        // 结果集
        $result = $this->duanyuList[$title] ?? [];
        $list = [];
        // 取当前性别对应的结果集
        $list[] = $result[$this->gender] ?? [];
        // 取不区分性别对应的结果集
        $list[] = $result[2] ?? [];
        $list = array_filter($list);
        return $this->comBineResult($list);
    }

    /**
     * 获得纪年五行和关系
     * @param array $jiNian
     * @return array
     */
    protected function jnwx(array $jiNian): array
    {
        $result = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $result[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        return $result;
    }

    /**
     * 结果集合并
     * @param array $result
     * @return array
     */
    protected function comBineResult(array $result = []): array
    {
        $res = [];
        foreach ($result as $item) {
            foreach ($item as $item2) {
                if (!empty($item2)) {
                    $res[] = $item2;
                }
            }
        }
        return $res;
    }
}
