<?php
// +----------------------------------------------------------------------
// | 八字排盘-婚姻
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use calendar\Ex;
use calendar\exceptions\Exception;
use calendar\Huangli;
use calendar\SolarTerm;

class HunYin
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 用户基础信息
     * @var array
     */
    protected array $base = [];

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @var array[]
     */
    protected array $shenShaRes;

    /**
     * 喜用神
     * @var array
     */
    protected array $xy;

    /**
     * 初始化
     * @param string $birthday 生日：1998-03-20 10:0:00
     * @param int $gender 性别：0男 1 女
     * @throws Exception
     */
    public function __construct(string $birthday, int $gender = 0)
    {
        // 农历类对象
        $this->lunar = Ex::date($birthday)->sex($gender);
        $this->base = $this->lunar->getLunarByBetween();
        $godT = $this->lunar->getGod();
        $godH = $this->lunar->_getGod();
        $godZ = [
            'year' => $godH['year']['god'][0],
            'month' => $godH['month']['god'][0],
            'day' => $godH['day']['god'][0],
            'hour' => $godH['hour']['god'][0],
        ];
        $this->userGod = [
            't' => $godT,
            'd' => $godZ,
            'h' => $godH,
        ];
        $jiNian = $this->base['jinian'];
        $jnWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $jnWx[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        $this->base['jn_wx'] = $jnWx;
        $this->shaShen = new ShaShen();
        $shaShenRes = $this->shaShen->detail($jiNian, $gender);
        $this->shenShaRes = $shaShenRes;
    }

    /**
     * 设置喜用忌闲仇
     * @param array $xy
     */
    public function setXy(array $xy)
    {
        $this->xy = $xy;
    }

    /**
     * 输出结果
     * @return array
     */
    public function detail()
    {
        $sex = $this->lunar->sex;
        $godT = $this->userGod['t'];
        $godZ = $this->userGod['d'];
        $godZS = $this->getGodSum($godZ);
        $godH = $this->userGod['h'];
        $godH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['day']['god'], $godH['hour']['god']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTZS = $this->getGodSum($godTZ);
        $godTH = array_merge(array_values($godT), array_values($godH2));
        $godTHS = $this->getGodSum($godTH);
        $godH2S = $this->getGodSum($godH2);
        $godTS = $this->getGodSum($godT);
        $jiNian = $this->base['jinian'];
        $xy = $this->getXy();
        $jnWx = $this->base['jn_wx'];
        $ddz = $jiNian['d'][1];
        $ydz = $jiNian['y'][1];
        $hdz = $jiNian['h'][1];
        $mdz = $jiNian['m'][1];
        $dgz = implode('', $jiNian['d']);
        $ygz = implode('', $jiNian['y']);
        $jnWxD = $jnWx['d'];
        $terrain = $this->lunar->getTerrain();
        $baziEx = new BaziEx($this->lunar);
        $wangDu = $baziEx->getWangDu();
        $wangDuW = in_array($wangDu, ['身旺格', '从旺格']);
        $resKey = [];
        $arrXy1 = [$xy['xy']['yong'], $xy['xy']['xi']];
        $boolXyD1 = in_array($jnWxD['wx'][1], $arrXy1);
        $wR = BaziExt::getWr($jiNian['d'][0], $ddz);
        $kW = explode(',', Huangli::getKongWangbyGz($dgz));
        if (in_array($ydz, ['丑', '子', '亥', '戌', '辰', '酉', '未', '午', '申', '寅'])) {
            $resKey[] = "年支{$ydz}";
        }
        if (BaziCommon::getXianChong($ydz . $ddz)) {
            $resKey[] = '年支和日支相冲';
        }
        if (in_array($godZ['day'], ['比肩', '劫财'])) {
            $resKey[] = '日支为比肩或劫财';
            if ($terrain['day'] == '临官') {
                $resKey[] = '日支为比肩或劫财且同柱十二长生为临官';
            }
        }
        if ($jnWxD['wx'][1] == $xy['xy']['xi']) {
            $resKey[] = '日支五行为喜神';
        } elseif ($jnWxD['wx'][1] == $xy['xy']['ji']) {
            $resKey[] = '日支五行为忌神';
            if ($godZ['day'] == '偏印') {
                $resKey[] = '日支偏印且为忌神';
            } elseif ($godZ['day'] == '七杀') {
                $resKey[] = '日支七杀且为忌神';
            } elseif ($godZ['day'] == '伤官') {
                $resKey[] = '日支伤官且为忌神';
            }
            if (in_array($godZ['day'], ['比肩', '劫财'])) {
                $resKey[] = '日支为比肩或劫财且为忌神';
            }
            if (in_array('驿马', $this->shenShaRes['d'])) {
                $resKey[] = '日柱驿马且日支为忌神';
            }
            if (in_array('阴阳差错', $this->shenShaRes['d'])) {
                $resKey[] = '日柱有阴阳差错且日支五行为忌神';
            }
        } elseif ($jnWxD['wx'][1] == $xy['xy']['xian']) {
            $resKey[] = '日支为闲神';
        }
        if (in_array($jnWxD['wx'][1], [$xy['xy']['ji'], $xy['xy']['qiu']])) {
            $resKey[] = '日支五行为忌神和仇神';
        }
        if ($jnWxD['wx'][0] == $jnWxD['wx'][1]) {
            $resKey[] = '日干和日支五行相同';
        } elseif ($jnWxD['gx'] == '生') {
            $resKey[] = '日干和日支五行相生';
        } elseif ($jnWxD['gx'] == '克') {
            $resKey[] = '日干五行和日支五行相克';
        }
        if (in_array($ddz, ['寅', '申', '巳', '亥'])) {
            $resKey[] = '日支为寅申巳亥';
        } elseif (in_array($ddz, ['子', '午', '卯', '酉'])) {
            $resKey[] = '日支为子午卯酉';
            if ($terrain['day'] == '临官') {
                $resKey[] = '日支子午卯酉且同柱十二长生为临官';
            }
        } else {
            $resKey[] = '日支为辰戌丑未';
        }
        if (in_array('才财', [$xy['shen']['qiu'], $xy['shen']['ji']])) {
            $resKey[] = '财星为仇神或忌神';
        }
        if (BaziCommon::getXianChong($ddz . $hdz)) {
            $resKey[] = '日支和时支相冲';
        }
        $arrGx = [
            'y' => $this->getGx1('y', $jiNian),
            'm' => $this->getGx1('m', $jiNian),
            'd' => $this->getGx1('d', $jiNian),
            'h' => $this->getGx1('h', $jiNian),
        ];
        $dGx = $arrGx['d'];
        if ($boolXyD1) {
            $resKey[] = '日支为喜神或用神';
            if ($dGx['chong']) {
                $resKey[] = '日支为喜神或用神且被它支所冲';
            }
            if ($godZ['day'] == '正印') {
                $resKey[] = '日支为正印且为喜神或用神';
            }
            if (empty($dGx['xin']) && empty($dGx['chong'])) {
                $resKey[] = '日支为喜神或用神且无刑冲';
            }
        }
        if ($dGx['chong']) {
            $resKey[] = '日支被冲';
        }
        if (in_array('m', $dGx['chong'])) {
            $resKey[] = '月支和日支相冲';
        }
        if (in_array('m', $dGx['xin'])) {
            $resKey[] = '日支和时支相刑';
        }
        if ($dGx['chong'] && $dGx['xin'] && empty($dGx['liu_he'])) {
            $resKey[] = '日支被它支所刑冲且地支无合';
        }
        if ($dGx['chong'] && $dGx['xin']) {
            $resKey[] = '日支被刑冲或正官，偏官被刑冲';
        }
        $b1 = false;
        foreach ($dGx['liu_he'] as $v) {
            $str = $jnWx[$v]['wx'][1];
            if (BaziCommon::getWuxingGuanXi($str, $jnWxD['wx'][0])) {
                $b1 = true;
                break;
            }
        }
        if ($b1) {
            $resKey[] = '日支有合且所合五行克日元';
        }
        if (in_array('劫财', $godH['day']['god'])) {
            $resKey[] = '日支藏干十神有劫财';
        }
        if (array_intersect(['比肩', '劫财'], $godTZ)) {
            $resKey[] = '八字中有比肩或劫财';
        }
        if (in_array('', $this->shenShaRes['d'])) {
            $resKey[] = '日柱有孤鸾煞';
        }
        if ($godZ['day'] == '劫财' && in_array($godZ['month'], ['正官', '七杀'])) {
            $resKey[] = '日支劫财，月支正官或七杀';
        }
        if (BaziCommon::getXianKe($jiNian['y'][0] . $jiNian['m'][0]) && BaziCommon::getXianChong($ydz . $mdz)) {
            $resKey[] = '年干和月干相克且年支和月支相冲';
        }
        if (in_array($godZ['day'], ['正财', '偏财'])) {
            $resKey[] = '日支为正财或偏财';
        } elseif (in_array($godZ['day'], ['七杀', '偏印'])) {
            $resKey[] = '日支为七杀或偏印';
        }
        if (in_array($godZ['day'], ['正财', '偏财', '正官', '七杀'])) {
            $resKey[] = '日支为财星或官星';
            if ($boolXyD1) {
                $resKey[] = '日支为财或官且为喜神或用神';
            }
        }
        if (in_array($godZ['day'], ['正印', '偏印'])) {
            $resKey[] = '日支为正印或偏印';
        } elseif (in_array($godZ['day'], ['正官', '七杀'])) {
            $resKey[] = '日支正官或七杀';
        }
        if (in_array($godZ['day'], ['正财', '正官'])) {
            $resKey[] = '日支为正财或正官';
        }
        if (in_array($godZ['day'], ['正官', '七杀', '伤官', '正财'])) {
            $resKey[] = '日支有正官或七杀或伤官或正财';
        }
        $resKey[] = "日支{$godZ['day']}";
        $mStr = '';
        if (in_array($godZ['month'], ['比肩', '劫财'])) {
            $mStr = '且月支比肩或劫财';
        } elseif (in_array($godZ['month'], ['正官', '七杀'])) {
            $mStr = '且月支正官或七杀';
        } elseif (in_array($godZ['month'], ['正印', '偏印'])) {
            $mStr = '且月支正印或偏印';
        } elseif (in_array($godZ['month'], ['正财', '偏财'])) {
            $mStr = '且月支正财或偏财';
        } elseif (in_array($godZ['month'], ['食神', '伤官'])) {
            $mStr = '且月支食神或伤官';
        }
        $resKey[] = "日支{$godZ['day']}{$mStr}";
        switch ($godZ['day']) {
            case '偏印':
                if ($godZS['py'] >= 2) {
                    $resKey[] = '日支偏印且其他支亦有偏印';
                }
                break;
            case '正财':
                if ($godTZS['sg'] <= 2) {
                    $resKey[] = '日支正财且伤官<=2';
                }
                if ($godZS['zg'] == 0) {
                    $resKey[] = '日支正财且没有正官';
                }
                break;
            case '比肩':
                if ($godZS['bj'] >= 2) {
                    $resKey[] = '日支为比肩且其他支还有比肩';
                }
                if ($wR == '弱') {
                    $resKey[] = '日支比肩且日元弱';
                }
                if (array_intersect(['比肩', '劫财'], $godT)) {
                    $resKey[] = '天干有比肩或劫财且日支为比肩';
                }
                break;
            case '七杀':
                if ($godZS['zg'] > 0) {
                    $resKey[] = '日支七杀且它柱还有正官';
                } else {
                    $resKey[] = '日支七杀且无正官';
                }
                break;
            case '正官':
                if (array_intersect(['天德', '月德'], $this->shenShaRes['d'])) {
                    $resKey[] = '日支是正官且同柱有天德或月德';
                }
                break;
            case '伤官':
                if ($wangDuW) {
                    $resKey[] = '日支伤官且身旺或从旺格';
                }
                break;
        }
        $heTg1 = BaziCommon::xianHeTg($jiNian['y'][0] . $jiNian['d'][0]);
        $heTg2 = BaziCommon::xianHeTg($jiNian['m'][0] . $jiNian['h'][0]);
        if ($heTg1 || $heTg2) {
            $resKey[] = '年干和日干或月干和时干有合';
        }
        if (array_intersect(['孤辰', '寡宿'], $this->shenShaRes['d']) && array_intersect(['孤辰', '寡宿'], $this->shenShaRes['h'])) {
            $resKey[] = '日柱和时柱都有孤辰或寡宿';
        }
        $b1 = false;
        $arr2 = [];
        $dzHe = [];
        $b2 = 0;
        foreach ($godT as $k => $v) {
            $tmpCaiZ = in_array($godZ[$k], ['正财', '偏财']);
            $tmpCaiT = in_array($v, ['正财', '偏财']);
            if (in_array($k, ['year', 'month'])) {
                if ($tmpCaiT || $tmpCaiZ) {
                    $b1 = true;
                }
            }
            $str = substr($k, 0, 1);
            $arr2 = array_merge($arr2, $arrGx[$str]['he_t']);
            $dzHe = array_merge($dzHe, $arrGx[$str]['liu_he']);
            if (($tmpCaiT || $tmpCaiZ) && $terrain[$k] == '旺') {
                $resKey[] = '财星同柱十二长生为旺';
            }
            if ($tmpCaiZ && $jiNian[$str][1] == '巳') {
                $b2++;
            }
            if (in_array($v, ['比肩', '劫财']) && array_intersect(['比肩', '劫财'], $godH[$k]['god'])) {
                $resKey[] = '同柱有比肩和劫财(看藏干)';
            }
            if (($tmpCaiT || $tmpCaiZ) && in_array($jnWx[$str]['wx'][1], $arrXy1)) {
                if (in_array('天乙贵人', $this->shenShaRes[$str])) {
                    $resKey[] = '同柱有比肩和劫财(看藏干)';
                }
            }
            if (($tmpCaiT || $tmpCaiZ) && $terrain[$k] == '沐浴') {
                $resKey[] = '桃花神煞或十二长生沐浴同柱有正财或偏财';
            }
            if ($v == '伤官' || $godZ[$k] == '伤官') {
                if (in_array($jiNian[$str][1], $kW)) {
                    $resKey[] = '伤官同柱有空亡';
                }
            }
            if ($v == '正财' || $godZ[$k] == '正财') {
                if (in_array($terrain[$k], ['死', '墓', '绝'])) {
                    $resKey[] = '死墓绝同柱有正财';
                }
            }
            if ($v == '偏财' && in_array($godZ[$k], ['比肩', '劫财'])) {
                if ($jnWx[$str]['wx'][1] == $xy['xy']['ji']) {
                    $resKey[] = '偏财天干对应地支为比肩或劫财且为忌神';
                }
            }
            if ($v == '偏印' && $jnWx[$str]['wx'][1] == $xy['xy']['ji']) {
                $resKey[] = '天干有偏印且为忌神';
            }
            if ($tmpCaiZ && $wangDu == '身旺格' && $arrGx[$str]['chong']) {
                $resKey[] = '身旺且地支财星被冲';
            }
            if ($k == 'hour' && ($tmpCaiT || $tmpCaiZ) && in_array($jnWx['h']['wx'][1], $arrXy1)) {
                $resKey[] = '时支为正财或偏财且为喜神或用神';
            }
            if ($k == 'month') {
                if (in_array($v, ['比肩', '劫财']) || in_array($godZ[$k], ['比肩', '劫财'])) {
                    if (in_array($godT['year'], ['正官', '七杀']) || in_array($godZ['year'], ['正官', '七杀'])) {
                        $resKey[] = '月柱有比肩或劫财且年柱有正官或七杀';
                    }
                }
            }
            if ($v == '偏官' && in_array($jiNian[$str][1], $kW)) {
                $resKey[] = '偏官同柱地支空亡';
            }
            if (in_array($v, ['正官', '七杀']) && $tmpCaiZ) {
                $resKey[] = '天干正官或七杀同柱地支正财或偏财';
            }
        }
        if ($b1) {
            $resKey[] = '年柱或月柱有正财或偏财';
        }
        if ($b2 >= 2) {
            $resKey[] = '地支两个巳且巳为正财或偏财';
        }
        if (in_array('才财', [$xy['shen']['yong'], $xy['shen']['xi']])) {
            $resKey[] = '财星为喜神或用神';
        }
        if ($xy['shen']['ji'] == '比劫') {
            $resKey[] = '比肩劫财为忌神';
            if (array_intersect(['偏财', '正财'], $godT)) {
                $resKey[] = '天干有偏财或正财且比肩、劫财为忌神';
            }
        }
        $arr2 = array_values(array_unique($arr2));
        if ($arr2) {
            $resKey[] = '天干有合';
            if ($dzHe) {
                $resKey[] = '天干有合且地支已有合';
            }
            if (count($arr2) >= 2) {
                $resKey[] = '天干有合且所合者>=2';
            }
        }
        if (($godTZS['zc'] + $godTZS['zg']) == 7) {
            $resKey[] = '命局只有正财或正官';
        }
        if ($wangDu == '身弱格') {
            if (($godTZS['zc'] + $godTZS['pc']) >= 4) {
                $resKey[] = '正偏财数>=4且为身弱格';
            }
            if ($godTZS['pc'] >= 2) {
                $resKey[] = '天干偏财>=2且为身弱格';
            }
        }
        if (($godTZS['zc'] + $godTZS['pc']) >= 4) {
            $resKey[] = '正偏财数>=4';
        }
        // 日柱藏干有偏财且其他柱有正财
        $dayGodH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['hour']['god']);
        if (in_array('偏财', $godH['day']['god']) && in_array('正财', $dayGodH2)) {
            $resKey[] = '日柱藏干有偏财且其他柱有正财';
        }
        if ($godT['year'] == '正财' || $godZ['year'] == '正财') {
            $dayGodH2 = array_merge($godH['day']['god'], $godH['month']['god'], $godH['hour']['god']);
            $dayGodH2[] = $godT['month'];
            $dayGodH2[] = $godT['hour'];
            if (!in_array('正财', $dayGodH2) && in_array('偏财', $dayGodH2)) {
                $resKey[] = '年柱有正财，其他柱无正财且月柱、日柱、时柱有偏财';
            }
        }
        if (($godTZS['zc'] + $godTZS['pc']) >= 5) {
            $resKey[] = '正财和偏财数>=5';
        }
        if (in_array('华盖', $this->shenShaRes['d']) && in_array('神煞', $this->shenShaRes['d'])) {
            $resKey[] = '日柱下有华盖神煞';
            $resKey[] = '日柱有华盖或华盖数>=2';
        }
        // $arrGx
        $arr3 = [
            [$ydz, $mdz, $ddz], [$ydz, $ddz, $hdz], [$ydz, $mdz, $hdz], [$mdz, $ddz, $hdz],
        ];
        $sanHe = BaziCommon::getSanHeDz2($arr3);
        if ($sanHe[3] || $arrGx['y']['liu_he'] || $arrGx['m']['liu_he'] || $arrGx['d']['liu_he'] || $arrGx['h']['liu_he']) {
            $resKey[] = '地支有三合或六合';
        }
        if ($arrGx['y']['he_t'] || $arrGx['m']['he_t'] || $arrGx['d']['he_t']) {
            $resKey[] = '年，月，日干有合';
        }
        // 日支和其他支六合后的五行和日元形成财星或官星
        if (empty($arrGx['y']['chong']) && empty($arrGx['m']['chong']) && empty($arrGx['d']['chong']) && empty($arrGx['h']['chong'])) {
            $resKey[] = '地支无冲';
        }
        if (($godTZS['bj'] + $godTZS['jc']) >= 4) {
            $resKey[] = '比肩、劫财数>=4';
        }
        if ($godTZS['bj'] > ($godTZS['zg'] + $godTZS['qs'])) {
            $b2 = $godTZS['zg'] > 0 ? ($godTZS['bj'] / $godTZS['zg']) : 0;
            $b3 = $godTZS['qs'] > 0 ? ($godTZS['bj'] / $godTZS['qs']) : 0;
            if (in_array($b2, [2, 3]) && in_array($b3, [2, 3])) {
                $resKey[] = '比肩数>正官和七杀且比肩和正官，七杀比例为3:1或2:1';
            }
        }
        if ($wangDu == '身旺格') {
            if (($godTZS['zc'] + $godTZS['pc']) <= 2 || ($godTZS['zg'] + $godTZS['qs']) <= 2) {
                $resKey[] = '身旺财星<=2或命官星<=2';
            }
        }
        if ($arrGx['y']['he_t'] && $arrGx['m']['he_t'] && $arrGx['d']['he_t'] && $arrGx['h']['he_t']) {
            $resKey[] = '四字天干均有合';
        }
        $year = (int)$this->lunar->dateTime->format('Y');
        $jieqi = SolarTerm::getAllJieQi($year);
        $time = $this->lunar->dateTime->getTimestamp();
        if ($time >= strtotime($jieqi['雨水']) && $time < strtotime($jieqi['立夏'])) {
            $resKey[] = '农历生日在雨水和立夏中间';
        }
        if ($ygz == $dgz) {
            $resKey[] = '年柱干支和日柱干支相同';
        }
        if (in_array('咸池', $this->shenShaRes['d'])) {
            $resKey[] = '日柱对应神煞有咸池';
        }
        if (in_array('羊刃', $this->shenShaRes['d'])) {
            $resKey[] = '日柱有羊刃';
            $resKey[] = '日支羊刃或空亡且财星或官星空亡';
            $arr2 = array_merge($this->shenShaRes['y'], $this->shenShaRes['m'], $this->shenShaRes['h']);
            if (in_array('羊刃', $arr2)) {
                $resKey[] = '日支羊刃且其他支还有羊刃';
            }
        }
        if (($godTS['zc'] + $godTS['pc'] + $godTS['zg'] + $godTS['qs']) == 0) {
            $resKey[] = '天干无财星或官星';
        }
        if ($wangDuW && $godH2S['qs'] <= 2) {
            $resKey[] = '身旺格或从旺格且七杀数<=2(看藏干)';
        }
        if (in_array($dgz, ['甲戌', '乙亥'])) {
            $resKey[] = '甲戌、乙亥日元';
        }
        if (($godTZS['zc'] + $godTZS['pc']) == 0) {
            $resKey[] = '八字无正财或偏财';
        }
        $resYin = ['阳' => 0, '阴' => 0];
        foreach ($jnWx as $k => $v) {
            $resYin[$v['yy'][0]]++;
            $resYin[$v['yy'][1]]++;
        }
        if ($resYin['阳'] == 8) {
            $resKey[] = '四柱全阳';
        }
        if (in_array('伤官', $godT)) {
            $resKey[] = '伤官在天干';
        }
        if (($godTZS['zg'] + $godTZS['qs']) >= 3 && '才财' == $xy['shen']['ji']) {
            $resKey[] = '正财为忌神且正官和七杀数>=3';
        }
        if (($godTZS['sh'] + $godTZS['sg']) >= 4) {
            if (in_array('食神', $godTZ) && in_array('伤官', $godTZ) && ($godTZS['zc'] + $godTZS['pc']) == 0) {
                $resKey[] = '食伤和伤官数>=4或命局有食神和伤官且无正财或偏财';
            }
        }
        if ($godTS['pc'] >= 2) {
            $resKey[] = '天干偏财>=2';
        }
        if (count($dGx['liu_he']) >= 2) {
            $resKey[] = '日元被合，且所合者数>=2';
        }
        if (in_array('红鸾', $this->shenShaRes['d']) && in_array('天喜', $this->shenShaRes['d'])) {
            $resKey[] = '日柱有桃花、红鸾、天喜神煞';
        }
        if ($this->getJianLu($dgz)) {
            $resKey[] = '日柱有建禄';
        }
        if ($godT['year'] == '正财' && $godT['month'] == '正财') {
            $resKey[] = '年干和月干都是正财';
        }
        if (in_array('羊刃', $this->shenShaRes['m']) && ($godZ['day'] == '劫财' || in_array('羊刃', $this->shenShaRes['d']))) {
            $resKey[] = '月柱有羊刃且日支为劫财或有羊刃';
        }
        if (in_array('咸池', $this->shenShaRes['m'])) {
            $resKey[] = '月柱有咸池神煞';
        }
        if (($godTZS['py'] + $godTZS['jc']) >= 4) {
            $resKey[] = '八字偏印和劫财数>=4';
        }
        if (($godTZS['zy'] + $godTZS['py'] + $godTZS['bj']) >= 5) {
            $resKey[] = '八字正印，偏印，比肩数>=5';
        }
        if (($godTS['zy'] + $godTS['py'] >= 2) || ($godTZS['zy'] + $godTZS['py'] >= 4)) {
            $resKey[] = '天干印星>=2或正印偏印数>=4';
        }
        if (in_array('伤官', $godH['day']['god'])) {
            $resKey[] = '日柱有伤官(藏干)';
        }
        if ($dgz == '丙子') {
            $resKey[] = '日柱丙子';
        }
        if (in_array($dgz, ['丁巳', '戊申'])) {
            $resKey[] = '日柱为丁巳或戊申';
        } elseif (in_array($dgz, ['甲寅', '戊申'])) {
            $resKey[] = '日柱甲寅或戊申';
        } elseif (in_array($dgz, ['壬午', '癸巳'])) {
            $resKey[] = '日柱壬午或癸巳';
        }
        if (in_array($dgz, ['辛亥', '甲寅'])) {
            $resKey[] = '日柱辛亥或甲寅';
        }
        if ($sex) {
            if ($godZ['month'] == '月支为劫财') {
                $resKey[] = '月支为劫财';
            }
            if ($godTZS['zg'] == $godTZS['sg']) {
                $resKey[] = '正官数=伤官数';
            } elseif ($godTZS['zg'] > $godTZS['sg']) {
                $resKey[] = '正官数>伤官数';
            }
            if ($godTZS['sg'] > 0 && $godTZS['zg'] == 0 && $godTZS['qs'] == 0) {
                $resKey[] = '命局有伤官却无正官和七杀';
            }
            if ($dgz == '乙庚') {
                $resKey[] = '天干有乙庚';
            }
            if ($godTS['zg'] == 0 && ($godTS['sg'] > 0 || $godTS['qs'] > 0)) {
                $resKey[] = '天干无正官且有伤官或七杀';
            }
            if ($godT['hour'] == '七杀' && $godZ['hour'] == '伤官') {
                $resKey[] = '时干为七杀且时支为伤官';
            }
            if (in_array($hdz, ['辰', '戌'])) {
                $resKey[] = '时支为辰或戌';
            }
        }
        if (($godTZS['zg'] + $godTZS['qs']) >= 4) {
            if (in_array('正官', $godT) && in_array('七杀', $godT)) {
                $resKey[] = '天干有正官和七杀或命局正官和七杀数>=4';
            }
            if (($godTZS['zc'] + $godTZS['pc']) >= 3) {
                $resKey[] = '正官和七杀数>=4且财星数>=3';
            }
        }
        if (($godH2S['zg'] + $godH2S['qs']) == 0) {
            $resKey[] = '八字无正官和七杀(看藏干)';
        }
        if (($godTZS['sh'] + $godTZS['zg']) == 0) {
            $resKey[] = '八字无食神和正官';
        }
        if (($godTZS['sg'] + $godTZS['zg']) == 0) {
            $resKey[] = '天干有正官和伤官';
        }
        if (($godTZS['zg'] + $godTZS['qs']) == 0 && $wangDu == '身旺格') {
            $resKey[] = '身旺且无正官和七杀';
        }
        if (($godH2S['zc'] + $godH2S['pc']) >= 4) {
            $resKey[] = '正财和偏财数>=4(看藏干)';
        }
        if (($godH2S['bj'] + $godH2S['jc']) >= 4) {
            $resKey[] = '比肩和劫财数>=4(看藏干)';
        }
        if ($terrain['day'] == '沐浴') {
            $resKey[] = '日柱十二长生为沐浴';
        }
        if ($resYin['阴'] == 8) {
            $resKey[] = '四柱八字无阳';
        }
        if (in_array($ddz, ['戊', '午'])) {
            $resKey[] = '日元为戊或午';
        }
        if (in_array($mdz, ['亥', '子', '丑'])) {
            $resKey[] = '月支为亥子丑';
        }
        $dzArr = array_column($jiNian, 1);
        if (count(array_intersect(['寅', '卯', '辰'], $dzArr)) == 3) {
            $resKey[] = '地支有寅卯辰';
        }
        if ($godZ['month'] == '伤官' && (in_array($godT['year'], ['正官', '七杀']) || in_array($godT['month'], ['正官', '七杀']))) {
            $resKey[] = '月支伤官且年干或月干有正官或七杀';
        }
        $mgz = implode('', $jiNian['m']);
        $hgz = implode('', $jiNian['h']);
        if ($mgz == $dgz || $hgz == $dgz) {
            $resKey[] = '月柱天干地支或时柱天干地支和日柱相同';
        }
        return array_values(array_unique($resKey));
        // 月支伤官且年干或月干有正官或七杀
        // 八字无丁(含藏干)
        // 桃花同柱为正官或七杀
        // 正官五行有化合或有六合
        // 官杀数<=2且正官或七杀同柱十二长生为死或绝
        // 伤官同柱有空亡
        // 比肩数>正官数
        // 日柱时柱有华盖且有正印或偏印
        // 地支有戌且无辰
        // 正官或七杀同柱十二长生为墓
        // 命宫同柱有驿马
        // 月柱有比肩和劫财(看藏干)
        // 天干无正官且比肩和劫财数>=4
        // 天干无正官且无财星命局有伤官
        // 八字无财星，印星且伤官在天干且地支也有伤官(看藏干)
        // 正官数>=2且七杀数>=2
        // 正官数>=3且身弱且年支有正官且时支受刑冲
        // 天干七杀数>=2或地支藏干七杀>=2
        // 八字贵人>=2
        // 正官和正财或偏财同柱
        // 十二长生有帝王且同柱有正官或七杀
        // 子午卯酉>=3
        // 七杀在正官前面
        // 身弱格或从弱格且食神和七杀>=5
        // 印星数>官星数
        // 偏印和正印数>=4且为忌神
        // 偏印和正印数>=4且为忌神
        // 长生同柱有七杀
        // 七杀数>正官
        // 咸池和大耗同柱天干或地支五行为忌
        // 天干有正官和七杀或正官和七杀数>=4
        // 地支有六合且又有三刑
        // 正官或七杀数>=4(看藏干)
        // 时干或时支有合化或六合
        // 天干无正官且印星数>=3且无正财或偏财
        // 桃花同柱有偏官且偏官五行为忌神
        // 官星和财星数>=5(看藏干)
        // 日元弱且七杀数>=3
        // 命局七杀数>=3且天干无正官
    }

    /**
     * 获取喜用忌闲仇
     * @return array
     */
    protected function getXy(): array
    {
        if (empty($this->xy)) {
            $baziEx = new BaziEx($this->lunar);
            $this->xy = $baziEx->getXiyongJi3();
        }
        return $this->xy;
    }

    /**
     * 获得某柱和其它柱的冲刑害破合等关系
     * @param string $k
     * @param array $jiNian
     * @return array
     */
    protected function getGx1(string $k, array $jiNian): array
    {
        $dz1 = $jiNian[$k][1];
        $tg1 = $jiNian[$k][0];
        $result = [
            'chong' => [], 'xin' => [], 'hai' => [], 'ke' => [], 'liu_he' => [], 'he_t' => [], 'ke_t' => [],
        ];
        foreach ($jiNian as $k1 => $v) {
            if ($k1 == $k) {
                continue;
            }
            $str = $v[1] . $dz1;
            if (BaziCommon::getXianChong($str)) {
                $result['chong'][] = $k1;
            }
            if (BaziCommon::getXianXin($str)) {
                $result['xin'][] = $k1;
            }
            if (BaziCommon::getXianHai($str)) {
                $result['hai'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['po'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['ke'][] = $k1;
            }
            if (BaziCommon::liuHeDz($str)) {
                $result['liuhe'][] = $k1;
            }
            if (BaziCommon::xianHeTg($tg1 . $v[0])) {
                $result['he_t'][] = $k1;
            }
            if (BaziCommon::getXianKe($tg1 . $v[0])) {
                $result['ke_t'][] = $k1;
            }
        }
        return $result;
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'sh' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }

    /**
     * 建禄
     * @param string $str 天干+地支
     * @return bool
     */
    protected function getJianLu(string $str): bool
    {
        $list = ['甲寅', '乙卯', '丙巳', '丁午', '戊巳', '己午', '庚申', '辛酉', '壬亥', '癸子'];
        return in_array($str, $list);
    }
}
