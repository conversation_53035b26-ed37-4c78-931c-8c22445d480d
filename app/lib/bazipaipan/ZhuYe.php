<?php
// +----------------------------------------------------------------------
// | 八字排盘-祖业遗产
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziEx;
use app\lib\bazi\BaziExt;
use app\lib\new2021\facade\BaziCommon;
use app\lib\new2021\ShaShen;
use calendar\Ex;
use calendar\exceptions\Exception;

class ZhuYe
{
    /**
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 用户基础信息
     * @var array
     */
    protected array $base = [];

    /**
     * 用户十神
     * @var array
     */
    protected array $userGod = [];

    /**
     * 煞神
     * @var ShaShen
     */
    protected ShaShen $shaShen;

    /**
     * @var array[]
     */
    protected array $shenShaRes;

    /**
     * 喜用神
     * @var array
     */
    protected array $xy;

    /**
     * 初始化
     * @param string $birthday 生日：1998-03-20 10:0:00
     * @param int $gender 性别：0男 1 女
     * @throws Exception
     */
    public function __construct(string $birthday, int $gender = 0)
    {
        // 农历类对象
        $this->lunar = Ex::date($birthday)->sex($gender);
        $this->base = $this->lunar->getLunarByBetween();
        $godT = $this->lunar->getGod();
        $godH = $this->lunar->_getGod();
        $godZ = [
            'year' => $godH['year']['god'][0],
            'month' => $godH['month']['god'][0],
            'day' => $godH['day']['god'][0],
            'hour' => $godH['hour']['god'][0],
        ];
        $this->userGod = [
            't' => $godT,
            'd' => $godZ,
            'h' => $godH,
        ];
        $jiNian = $this->base['jinian'];
        $jnWx = [];
        $wuXingAttr = $this->lunar->wuXingAttr;
        foreach ($jiNian as $k => $v) {
            $tgWx = $wuXingAttr[$v[0]];
            $dzWx = $wuXingAttr[$v[1]];
            $jnWx[$k] = [
                'wx' => [$tgWx, $dzWx],
                'gx' => BaziCommon::getWuxingGuanXi($tgWx, $dzWx),
                'gx2' => BaziCommon::getWuxingGuanXi($dzWx, $tgWx),
                'yy' => [
                    BaziExt::getYinYang($v[0]) ? '阳' : '阴',
                    BaziExt::getYinYang($v[1]) ? '阳' : '阴',
                ],
            ];
        }
        $this->base['jn_wx'] = $jnWx;
        $this->shaShen = new ShaShen();
        $shaShenRes = $this->shaShen->detail($jiNian, $gender);
        $this->shenShaRes = $shaShenRes;
    }

    /**
     * 设置喜用忌闲仇
     * @param array $xy
     */
    public function setXy(array $xy)
    {
        $this->xy = $xy;
    }

    /**
     * 结果
     * @return array
     */
    public function detail(): array
    {
        $sex = $this->lunar->sex;
        $godT = $this->userGod['t'];
        $godZ = $this->userGod['d'];
        $godZS = $this->getGodSum($godZ);
        $godH = $this->userGod['h'];
        $godH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['day']['god'], $godH['hour']['god']);
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTZS = $this->getGodSum($godTZ);
        $godTH = array_merge(array_values($godT), array_values($godH2));
        $godTHS = $this->getGodSum($godTH);
        $godH2S = $this->getGodSum($godH2);
        $godTS = $this->getGodSum($godT);
        $jiNian = $this->base['jinian'];
        $jnWx = $this->base['jn_wx'];
        $ddz = $jiNian['d'][1];
        $ydz = $jiNian['y'][1];
        $hdz = $jiNian['h'][1];
        $mdz = $jiNian['m'][1];
        $likeGod = $this->lunar->getLikeGod();
        $xy = $this->getXy();
        $resKey = [];

        if ($sex) {
            if ($godT['year'] == '偏官') {
                $resKey[] = '年干偏官';
            }
            $arr = ['正财', '偏财', '正官', '七杀'];
            if (!in_array($godT['year'], $arr) && !in_array($godZ['year'], $arr)) {
                if ((in_array($godT['day'], $arr) || in_array($godZ['day'], $arr)) && (in_array($godT['hour'], $arr) || in_array($godZ['hour'], $arr))) {
                    $resKey[] = '年无财官，日时有财官';
                }
            }
            if (in_array('驿马', $this->shenShaRes['y'])) {
                $resKey[] = '年柱有带驿马';
            }
            if ($godT['year'] == '偏财' || $godZ['year'] == '偏财') {
                if ($xy['xy']['ji'] == $likeGod) {
                    $resKey[] = '年柱有带偏财且喜用神为忌神';
                }
            }
            if ($godT['year'] == '食神' || $godZ['year'] == '食神') {
                if ($xy['xy']['ji'] == $likeGod) {
                    $resKey[] = '年柱有食神，且喜用神为忌神';
                }
            }
            if (array_intersect(['正财', '偏财'], $godH['year']['god'])) {
                $resKey[] = '年支财在藏干里';
            }
            if ($godT['year'] == '伤官' || $godZ['year'] == '伤官') {
                $resKey[] = '年柱上有伤官';
                if ($godT['month'] == '伤官' || $godZ['month'] == '伤官') {
                    $resKey[] = '年柱和月柱中都带有伤官';
                }
            }
            if ($godT['year'] == '伤官' && $godZ['year'] == '伤官') {
                $resKey[] = '年柱上伤官多';
            }
            if ($jnWx['y']['wx'][1] == $jnWx['m']['wx'][1] && $jnWx['y']['wx'][1] == $xy['xy']['ji']) {
                $resKey[] = '年支和月支五行相同且为忌神';
            }
            if ($mdz == $ydz) {
                $resKey[] = '月支和年支为同';
            }
        } else {
            if ($godT['year'] == '伤官' || $godZ['year'] == '伤官') {
                $resKey[] = '年柱有伤官';
            }
            $mGx = $this->getGx1('m', $jiNian);
            if ($mGx['chong'] && $mGx['ke']) {
                $resKey[] = '月支被它支冲克';
            }
            if ($godT['year'] == '伤官' || $godZ['year'] == '伤官') {
                $resKey[] = '年柱有伤官';
            }
            if ($godT['year'] == '正财' || $godZ['year'] == '正财') {
                if ($xy['shen']['ji'] == '才财') {
                    $resKey[] = '年柱有正财且为忌神';
                } elseif (in_array('才财', [$xy['shen']['xi'], $xy['shen']['yong']])) {
                    $resKey[] = '年柱有正财且为喜用';
                    $resKey[] = '生年有正财为喜用';
                }
            }
            if ($godT['year'] == '偏财') {
                $resKey[] = '年干为偏财';
            }
            $arr = ['正财', '偏财', '正官', '七杀', '正印', '偏印'];
            $terrain = $this->lunar->getTerrain();
            if (in_array($terrain['year'], ['帝旺', '临官'])) {
                $resKey[] = '年干坐十二长生为帝旺或临官之地';
            }
            if ((in_array($godT['year'], $arr) || array_intersect($arr, $godH['year']['god'])) && (in_array($godT['month'], $arr) || array_intersect($arr, $godH['month']['god']))) {
                $resKey[] = '年柱和月柱有财星，官星，印星（看藏干）';
            }
            $b1 = 0;
            $b2 = 0;
            $b3 = [0, 0];
            foreach ($godT as $k => $v) {
                $caiT = in_array($v, ['正财', '偏财']);
                $caiZ = in_array($godZ[$k], ['正财', '偏财']);
                $cai = $caiT || $caiZ;
                $yinT = in_array($v, ['正印', '偏印']);
                $yinZ = in_array($godZ[$k], ['正印', '偏印']);
                $yin = $yinT || $yinZ;
                $shi = (in_array($v, ['食神', '伤官']) || in_array($godZ[$k], ['食神', '伤官']));
                if ($k == 'year') {
                    if ($xy['shen']['yong'] == '印枭' && ($yinT && $yinZ)) {
                        $resKey[] = '年柱有枭印且为用神';
                    }
                    if (in_array('食伤', [$xy['shen']['xi'], $xy['shen']['yong']]) && ($v == '食神' || $godZ[$k] == '食神')) {
                        $resKey[] = '年柱有食神且为喜神或用神';
                    }
                    if ($cai) {
                        $resKey[] = '年柱为枭印';
                    }
                }
                if (in_array($k, ['year', 'month'])) {
                    if ($cai || $shi) {
                        $b1++;
                    }
                    if ($cai || $yin) {
                        $b2++;
                    }
                }
                if ($k == 'month') {
                    if ($caiT && $caiZ) {
                        $b3[0] = 1;
                    }
                } else {
                    if (!$caiT && !$caiZ) {
                        $b3[1]++;
                    }
                }
            }
            if ($b1 == 2) {
                $resKey[] = '年月两柱为财、食';
            }
            if ($b2 == 2) {
                $resKey[] = '年月两柱为财、官印';
            }
            if ($b3[0] == 1 && $b3[1] == 3) {
                $resKey[] = '月干支皆为财星，其他干支不再出现财星';
            }
            if ($godTS['py'] >= 2) {
                $resKey[] = '天干偏印≥2';
            }
            if (BaziCommon::getXianChong($ydz . $mdz)) {
                $resKey[] = '年月地支相冲';
            }
            if (BaziCommon::getXianChong($ydz . $mdz) || BaziCommon::getXianChong($jiNian['y'][0] . $jiNian['m'][0])) {
                $resKey[] = '年月相冲';
            }
            // 驿马同柱地支无合	0
            // 日元旺且八字中财≥2（看藏干）	0
            // 正印同柱地支被它支冲(看藏干)	0
            // 年柱十二长生为死、绝、墓，或年支被它支刑、冲、克	0
        }
        return $resKey;
    }

    /**
     * 金口直断
     * @return array
     */
    public function getZhiDuan()
    {
        $resKey = [];
        $jiNian = $this->base['jinian'];
        $sex = $this->lunar->sex;
        $dzArr = array_column($jiNian, 1);
        $tgArr = array_column($jiNian, 0);
        $godT = $this->userGod['t'];
        $godZ = $this->userGod['d'];
        $godZS = $this->getGodSum($godZ);
        $godH = $this->userGod['h'];
        $godTZ = array_merge(array_values($godT), array_values($godZ));
        $godTZS = $this->getGodSum($godTZ);
        $godH2 = array_merge($godH['year']['god'], $godH['month']['god'], $godH['day']['god'], $godH['hour']['god']);
        $godHTg = array_merge($godH['year']['hide'], $godH['month']['hide'], $godH['day']['hide'], $godH['hour']['hide']);
        $godTH = array_merge(array_values($godT), array_values($godH2));
        $godTHS = $this->getGodSum($godTH);
        if (in_array('丑', $dzArr) && in_array('未', $dzArr)) {
            $resKey[] = '地支有丑未';
        }
        $godHTgTotal = array_count_values($godHTg);
        $dzTotal = array_count_values($dzArr);
        $num1 = $dzTotal['未'] ?? 0;
        if ($num1 == 3) {
            $resKey[] = '地支有三个未';
        }
        if ($godTHS['py'] >= 4) {
            $resKey[] = '命局有四个偏印（看藏干）';
        }
        if (in_array('比肩', $godT)) {
            $resKey[] = '天干有1个比肩';
        }
        if (in_array('未', $tgArr) || $num1 > 0) {
            $resKey[] = '八字中有未';
        }
        if (in_array('巳', $dzArr) && in_array('亥', $dzArr)) {
            $resKey[] = '八字中有巳亥';
        }
        if (in_array('偏印', $godT)) {
            $resKey[] = '天干有一个枭';
        }
        if (($godTHS['zc'] + $godTHS['pc']) > 0) {
            $resKey[] = '命局有两个财（看藏干）';
        }
        if (in_array('正印', $godT)) {
            $resKey[] = '天干有一个印';
        }
        if ($godTHS['sh'] == 1) {
            $resKey[] = '命局有一个食神（看藏干）';
        }
        if ($godTHS['sg'] == 3) {
            $resKey[] = '命局有三个伤官（看藏干）';
        }
        if ($godTHS['qs'] == 1) {
            $resKey[] = '命局有一个七杀（看藏干）';
        }
        if ($godTHS['zc'] == 1) {
            $resKey[] = '命局有一个正财（看藏干）';
        }
        if ($godTHS['py'] == 2) {
            $resKey[] = '命局有两个枭神（看藏干）';
        }
        if ($godTHS['jc'] == 2) {
            $resKey[] = '命局有两个劫财（看藏干）';
        }
        if ($godTHS['bj'] == 2) {
            $resKey[] = '命局有两个比肩（看藏干）';
        }
        if (in_array('丙', $tgArr) && in_array('壬', $tgArr)) {
            $resKey[] = '天干有丙壬二字';
        }
        if ($godTHS['py'] >= 3) {
            $resKey[] = '命局枭神≥3（看藏干）';
        }
        // 甲、乙、丙、丁、戊、己、庚、辛、壬、癸
        $num1 = $godHTgTotal['戊'] ?? 0;
        if ($num1 >= 3) {
            $resKey[] = '命局戊≥3（看藏干）';
        }
        if ($godTHS['bj'] >= 3) {
            $resKey[] = '命局比肩≥3（看藏干）';
        }
        if ($godTHS['sg'] == 3) {
            $resKey[] = '命局只有一个伤官（看藏干）';
        }
        if ($godTHS['jc'] == 3) {
            $resKey[] = '命局有三个劫财（看藏干）';
        }
        if ($godTZS['sh'] == 3) {
            $resKey[] = '原局有三个食神';
        }
        if ($godTZS['zc'] == 3) {
            $resKey[] = '原局有三个正财';
        }
        if (($godTZS['zc'] + $godTZS['pc'] + $godTZS['zy'] + $godTZS['py']) == 0) {
            $resKey[] = '原局无财无印';
        }
        if ($godT['year'] == '七杀' && in_array('伤官', $godH['year']['god'])) {
            $resKey[] = '年干为七杀且年支藏干有伤官';
        }

        $num1 = $dzTotal['卯'] ?? 0;
        if ($num1 == 3) {
            $resKey[] = '地支有三个卯';
        }
        $num1 = $dzTotal['申'] ?? 0;
        if ($num1 == 2) {
            $resKey[] = '地支有两个申';
        }
        $num1 = $dzTotal['丑'] ?? 0;
        if ($num1 == 2) {
            $resKey[] = '地支有两个丑';
        }
        $tgTotal = array_count_values($tgArr);
        $num1 = $tgTotal['庚'] ?? 0;
        if ($num1 == 3) {
            $resKey[] = '天干有三个庚';
        }
        $ddz = $jiNian['d'][1];
        $hdz = $jiNian['h'][1];
        if ($ddz && $hdz) {
            $resKey[] = '日支为卯且时支为辰';
        }
        if (in_array('庚', $tgArr) && in_array('甲', $tgArr)) {
            if (in_array('子', $tgArr) && in_array('午', $tgArr)) {
                $resKey[] = '四柱有庚甲子午';
            }
        }
        return $resKey;
    }

    /**
     * 获取喜用忌闲仇
     * @return array
     */
    protected function getXy(): array
    {
        if (empty($this->xy)) {
            $baziEx = new BaziEx($this->lunar);
            $this->xy = $baziEx->getXiyongJi3();
        }
        return $this->xy;
    }

    /**
     * 获得某柱和其它柱的冲刑害破合等关系
     * @param string $k
     * @param array $jiNian
     * @return array
     */
    protected function getGx1(string $k, array $jiNian): array
    {
        $dz1 = $jiNian[$k][1];
        $tg1 = $jiNian[$k][0];
        $result = [
            'chong' => [], 'xin' => [], 'hai' => [], 'ke' => [], 'liu_he' => [], 'he_t' => [], 'ke_t' => [],
        ];
        foreach ($jiNian as $k1 => $v) {
            if ($k1 == $k) {
                continue;
            }
            $str = $v[1] . $dz1;
            if (BaziCommon::getXianChong($str)) {
                $result['chong'][] = $k1;
            }
            if (BaziCommon::getXianXin($str)) {
                $result['xin'][] = $k1;
            }
            if (BaziCommon::getXianHai($str)) {
                $result['hai'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['po'][] = $k1;
            }
            if (BaziCommon::getXianPo($str)) {
                $result['ke'][] = $k1;
            }
            if (BaziCommon::liuHeDz($str)) {
                $result['liuhe'][] = $k1;
            }
            if (BaziCommon::xianHeTg($tg1 . $v[0])) {
                $result['he_t'][] = $k1;
            }
            if (BaziCommon::getXianKe($tg1 . $v[0])) {
                $result['ke_t'][] = $k1;
            }
        }
        return $result;
    }

    /**
     * 十神统计
     * @param array $arr
     * @return array
     */
    protected function getGodSum(array $arr): array
    {
        $arr2 = array_count_values($arr);
        return [
            'bj' => $arr2['比肩'] ?? 0, 'jc' => $arr2['劫财'] ?? 0, 'sh' => $arr2['食神'] ?? 0, 'sg' => $arr2['伤官'] ?? 0,
            'pc' => $arr2['偏财'] ?? 0, 'zc' => $arr2['正财'] ?? 0, 'qs' => $arr2['七杀'] ?? 0, 'zg' => $arr2['正官'] ?? 0,
            'py' => $arr2['偏印'] ?? 0, 'zy' => $arr2['正印'] ?? 0,
        ];
    }
}
