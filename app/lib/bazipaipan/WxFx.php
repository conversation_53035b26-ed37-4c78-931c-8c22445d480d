<?php
// +----------------------------------------------------------------------
// | WxFx.五行分析
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\bazipaipan;

use app\lib\bazi\BaziExt;
use calendar\Ex;
use calendar\plugin\WuXing;

class WxFx
{
    /**
     * 用户类
     * @var Ex
     */
    protected Ex $lunar;

    /**
     * 初始化
     * @param Ex $lunar
     */
    public function __construct(Ex $lunar)
    {
        $this->lunar = $lunar;
    }

    /**
     * 李氏五行
     * @return array
     */
    public function getLiShiFx(): array
    {
        $jiNian = $this->lunar->getLunarTganDzhi();
        $god2 = $this->lunar->_getGod();
        $list = [
            '寅' => [1.571, 1.548, 0.924, 0.716, 0.862],
            '卯' => [2, 1.414, 0.5, 0.707, 1],
            '辰' => [1.166, 1.074, 1.421, 1.161, 0.8],
            '巳' => [0.862, 1.571, 1.548, 0.924, 1.716],
            '午' => [0.912, 1.7, 1.59, 0.774, 0.645],
            '未' => [0.924, 1.341, 1.674, 1.069, 0.612],
            '申' => [0.795, 0.674, 1.012, 1.641, 1.498],
            '酉' => [0.5, 0.707, 1, 2, 1.414],
            '戌' => [0.674, 1.012, 1.641, 1.498, 0.795],
            '亥' => [1.59, 0.774, 0.645, 0.912, 1.7],
            '子' => [1.414, 0.5, 0.707, 1, 2],
            '丑' => [0.898, 0.821, 1.512, 1.348, 1.041],
        ];
        $mdz = $jiNian['m'][1];
        // $mtg = $jiNian['m'][0];
        $list1 = [
            '木' => $list[$mdz][0], '火' => $list[$mdz][1], '土' => $list[$mdz][2], '金' => $list[$mdz][3], '水' => $list[$mdz][4],
        ];
        // 藏干分数
        $list2 = [
            '子癸' => 100, '丑己' => 60, '寅甲' => 60, '卯乙' => 100, '辰戊' => 60, '巳丙' => 60, '午丁' => 70, '未己' => 60, '申庚' => 60,
            '酉辛' => 100, '戌戊' => 60, '亥壬' => 70, '丑癸' => 30, '寅丙' => 30, '辰乙' => 30, '巳戊' => 30, '午己' => 30, '未丁' => 30,
            '申壬' => 30, '戌辛' => 30, '亥甲' => 30, '丑辛' => 10, '寅戊' => 10, '辰癸' => 10, '巳庚' => 10, '未乙' => 10, '申戊' => 10, '戌丁' => 10,
        ];
        $wxAttr = WuXing::GZ_TO_WX;
        $wxFen = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $yinFen = 0;
        $yanFen = 0;
        foreach ($jiNian as $v) {
            $wx = $wxAttr[$v[0]];
            $yy = BaziExt::getYinYang($v[0]);
            $factor = $list1[$wx];
            $fen = bcmul(100, $factor, 3);
            if ($yy) {
                $yanFen = bcadd($yanFen, $fen, 3);
            } else {
                $yinFen = bcadd($yinFen, $fen, 3);
            }
            $wxFen[$wx] = bcadd($wxFen[$wx], $fen, 3);
        }
        foreach ($god2 as $key => $rs) {
            $k1 = substr($key, 0, 1);
            $dz1 = $jiNian[$k1][1];
            foreach ($rs['hide'] as $rs1) {
                $fen = $list2[$dz1 . $rs1] ?? 0;
                $wx = $wxAttr[$rs1];// 五行
                $yy = BaziExt::getYinYang($rs1);// 阴阳
                $factor = $list1[$wx];
                $fen = bcmul($fen, $factor, 3);
                if ($yy) {
                    $yanFen = bcadd($yanFen, $fen, 3);
                } else {
                    $yinFen = bcadd($yinFen, $fen, 3);
                }
                $wxFen[$wx] = bcadd($wxFen[$wx], $fen, 3);
            }
        }
        $totalFen = bcadd($yinFen, $yanFen, 3);
        $yanZb = bcmul(bcdiv($yanFen, $totalFen, 4), 100, 2);
        $yinZb = bcsub(100, $yanZb, 2);
        $wxZb = [];
        foreach ($wxFen as $k => $v) {
            $wxZb[] = [$k, bcdiv(bcmul($v, 100), $totalFen, 2)];
        }
        //$wxFen = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        $wxZb[4][1] = number_format(100 - $wxZb[0][1] - $wxZb[1][1] - $wxZb[2][1] - $wxZb[3][1], 2);
        return [
            'yy' => [
                'yan' => $yanZb, 'yin' => $yinZb,
            ],
            'wx' => $wxZb,
            'wx_fen' => $wxFen,
            'yy_fen' => [
                'yin' => $yinFen, 'yan' => $yanFen,
            ],
        ];
    }

    /**
     * 五行分析
     * 能量强度表
     * @return array
     */
    public function fenXi()
    {
        $baziFx = $this->lunar->getWxFenxi();
        $kingWx = array_column($baziFx['king'], 'wx');
        $kingT = array_sum(array_column($baziFx['king'], 'fen'));
        $unkingWx = array_column($baziFx['unking'], 'wx');
        $unkingT = array_sum(array_column($baziFx['unking'], 'fen'));
        $total = $kingT + $unkingT;
        $fraction = [];
        foreach ($baziFx['king'] as $v) {
            $fraction[] = [
                $v['wx'], $v['fen'], round(($v['fen'] / $total) * 100),
            ];
        }
        foreach ($baziFx['unking'] as $v) {
            $fraction[] = [
                $v['wx'], $v['fen'], round(($v['fen'] / $total) * 100),
            ];
        }
        $title = $baziFx['differ'] > 0 ? '偏旺' : '偏弱';
        // 同类分
        $kingFen = round($kingT, 3);
        // 异类分
        $unkingFen = round($unkingT, 3);
        // 同类+异类总分
        $kingTotalFen = round($kingT, 3) + round($unkingT, 3);
        $result = [
            // 原局五行能量强度表各五行汇总和各五行百分比占比
            'fraction' => $fraction,
            // 同类五行
            'king' => [
                'wx' => implode('', $kingWx),
                'fen' => $kingFen,
                'per' => round(($kingFen / $kingTotalFen) * 100),
            ],
            // 异类五行
            'unking' => [
                'wx' => implode('', $unkingWx),
                'fen' => $unkingFen,
                'per' => round(($unkingFen / $kingTotalFen) * 100),
            ],
            // 差值（同类减少异类）
            'differ' => [
                'fen' => $baziFx['differ'],
                'title' => $title,
            ],
            // 喜用神
            'like_god' => $baziFx['xy']['yong'],
        ];
        $liShi = $this->getLiShiFx();
        $liShiTotal = array_sum($liShi['wx_fen']);
        $lsFen = 0;
        foreach ($kingWx as $v) {
            $lsFen += $liShi['wx_fen'][$v];
        }
        $lsKingPer = round(($lsFen / $liShiTotal) * 100);
        $lsUkFen = round(($liShiTotal - $lsFen), 3);
        $liShi['king'] = [
            'wx' => implode('', $kingWx),
            'fen' => round($lsFen, 3),
            'per' => $lsKingPer,
        ];
        $liShi['unking'] = [
            'wx' => implode('', $unkingWx),
            'fen' => $lsUkFen,
            'per' => 100 - $lsKingPer,
        ];
        return [
            'fx' => $result,
            'li_shi' => $liShi,
        ];
    }
}
