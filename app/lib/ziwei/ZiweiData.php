<?php
// +----------------------------------------------------------------------
// | 紫微斗数
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\lib\ziwei;

use calendar\Ex;
use calendar\exceptions\Exception;

class ZiweiData
{
    /**
     * Calendar 实例
     * @var Ex|null
     */
    protected ?Ex $calendar = null;

    /**
     * 农历相关信息
     * @var array
     */
    protected array $lunar;

    /**
     * 八字基础
     * @var array
     */
    protected array $lunar2;

    /**
     * 用户姓名
     * @var string
     */
    protected string $userName;

    /**
     * 时间戳
     * @var int|string
     */
    public string | int $dateTime;

    /**
     * 性别  0：表示男性，1：表示女性
     * @var int
     */
    public int $sex = 0;

    /**
     * @var string[]
     */
    protected array $seGong = ['命宫', '兄弟', '夫妻', '子女', '财帛', '疾厄', '迁移', '奴仆', '官禄', '田宅', '福德', '父母'];

    /**
     * 宫支
     * @var array
     */
    protected array $gongZhi = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];

    /**
     * 天干
     * @var array
     */
    protected array $tg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

    /**
     * 地支
     * @var array
     */
    protected array $dz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

    /**
     * 农历日期数组
     * @var array
     */
    protected array $arrDayList = [
        '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
        '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
        '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十',
    ];

    /**
     * 农历月份
     * @var array
     */
    protected array $monthHash = [
        '1' => '正月', '2' => '二月', '3' => '三月', '4' => '四月', '5' => '五月', '6' => '六月',
        '7' => '七月', '8' => '八月', '9' => '九月', '10' => '十月', '11' => '冬月', '12' => '腊月',
    ];

    /**
     * 命宫位置
     * @var int
     */
    protected int $mingGIndex;

    /**
     * 身宫位置
     * @var int
     */
    protected int $shenGIndex;

    /**
     * 时辰的记法
     * @var int
     */
    protected int $hIndex;

    /**
     * 五行局
     * @var array
     */
    protected array $wuXinJu;

    /**
     * 主星
     * @var array
     */
    protected array $zhuXingList = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'];

    /**
     * 吉星
     * @var array
     */
    protected array $jiXing = ['左辅', '右弼', '文昌', '文曲', '天魁', '天钺', '禄存', '天马'];

    /**
     * 凶星
     * @var array
     */
    protected array $xiongXing = ['擎羊', '陀罗', '火星', '铃星', '地空', '地劫'];

    /**
     * 阴阳
     * @var string
     */
    protected string $yinyang;

    /**
     * 当前时间
     * @var string
     */
    protected string $otime = '';

    /**
     * 构造函数，接受时间戳或者时间格式
     * 注意：时间格式必须是完整的
     * @param string $userName
     * @param int|string|null $dateTime
     * @param int $sex 性别
     * @param string $otime 时间字符串
     * @throws Exception
     * @throws \DateMalformedStringException
     */
    public function __construct(string $userName = '无名', int | string $dateTime = null, int $sex = 0, string $otime = '')
    {
        if (empty($otime) || false === strtotime($otime)) {
            $otime = date('Y-m-d');
        }
        $this->otime = $otime;
        if (is_null($dateTime)) {
            $this->dateTime = time();
        } else {
            if (preg_match('/^\d+$/i', $dateTime)) {
                $this->dateTime = $dateTime;
            } else {
                if ((int)$dateTime < 0) {
                    $this->dateTime = $dateTime;
                } else {
                    $newTime = new \DateTime($dateTime, new \DateTimeZone('PRC'));
                    $this->dateTime = $newTime->format('U');
                }
            }
        }
        // 实例化日历类
        $this->calendar = Ex::date($this->dateTime)->sex($sex);
        $this->lunar2 = $this->calendar->getLunarByBetween();
        $lunar = $this->lunar2;
        $jinianYear = $this->getJiaZiNian($lunar['_nongli']['y']);
        $lunar['jinian']['y'] = $jinianYear;
        // 设阴阳
        $tmpindex = array_search($jinianYear[0], $this->tg);
        if ($tmpindex % 2 == 0) {
            $this->yinyang = '阳';
        } else {
            $this->yinyang = '阴';
        }
        $this->lunar = $lunar;
        $this->userName = $userName;
        $this->sex = $sex;
        $this->hIndex = $this->getHIndex();
        $this->getMindex();
        $this->wuXinJu = $this->getWuXingJu();
    }

    /**
     * 魔术方法,获取对象属性
     * @param mixed $name 属性名称
     */
    public function __get($name)
    {
        return $this->{$name} ?? null;
    }

    /**
     * 返回年份所对应的甲子年
     * @param int $year 年份 农历
     * @return array
     */
    private function getJiaZiNian($year): array
    {
        $gan = $this->tg[($year - 4) % 10];
        $zhi = $this->dz[($year - 4) % 12];
        return [$gan, $zhi];
    }

    /**
     * 时辰的记法
     * @return int
     */
    public function getHIndex(): int
    {
        $h = (int)date('H', $this->dateTime);
        $hIndex = (int)(($h + 1) / 2) % 12;
        return $hIndex;
    }

    /**
     * 命宫位置和身宫位置设置
     * @return void
     */
    public function getMindex()
    {
        $lunar = $this->lunar;
        $m = $lunar['_nongli']['m'];
        $mingGIndex = ($m - 1 - $this->hIndex + 12) % 12;
        $this->mingGIndex = $mingGIndex;
        $shenGIndex = ($m - 1 + $this->hIndex + 12) % 12;
        $this->shenGIndex = $shenGIndex;
    }

    /**
     * 获得排版12宫表格
     * @return array 各宫排版数据
     * @throws \Exception
     */
    public function getTableDate(): array
    {
        $lunar = $this->lunar2;
        $ret = [];
        // 命宫数组
        $tmpminggong = $this->getMingGong();
        // 中间数据
        $base = [
            'username' => $this->userName,
            // 公历时间
            'calendar' => date('Y年m月d日 H时', $this->dateTime),
            // 阴历时间
            'lunar' => $lunar['nongli']['y'] . '年' . $lunar['nongli']['m'] . '' . $lunar['nongli']['d'],
            'hour' => $lunar['jinian']['h'][1],
            'shengxiao' => $lunar['shengxiao'],
            'sex' => $this->yinyang . ($this->sex ? '女' : '男'),
            'qianzao' => $lunar['jinian'],
            'mingzhu' => $this->getMingZhu(),
            'shengzhu' => $this->getShengZhu(),
            'wuxingju' => $this->getWuXingJu(),
            'ziniandoujun' => $this->getZiiNianDouJun(),
            // 大运数据
            'fate' => $this->calendar->getFate(),
            'minggong' => array_search('命宫', $tmpminggong),
            'shenggong' => $this->getShengGong(),
        ];
        // 身宫位置
        $tshenggong = $this->getShengGong();
        // 盘位对应的天干
        $tmpdtpg = $this->getDptg();
        // 流年将前十二星
        $liunianjiang12 = $this->getLiuNianJiang12();
        // 生年博士十二神
        $sybssesheng = $this->getShengNianBoShi12Sheng();
        // 大限
        $daxiansuishu = $this->getDaXianSuiShu();
        // 流年岁前十二星
        $liuniansui12 = $this->getLiuNianSui12();
        // 小限
        $xiaoxian = $this->getXiaoXian();
        // 长生十二神
        $changsheng12 = $this->getChangSheng12();
        foreach ($tmpminggong as $k => $v) {
            $ret[$k]['minggong'] = $v;
            $ret[$k]['tg'] = $tmpdtpg[$k];
            $ret[$k]['right1'] = $sybssesheng[$k];
            $ret[$k]['right2'] = $liuniansui12[$k];
            $ret[$k]['right3'] = $liunianjiang12[$k];
            $ret[$k]['daxian'] = $daxiansuishu[$k];
            $ret[$k]['xiaoxian'] = $xiaoxian[$k];
            $ret[$k]['left2'] = $changsheng12[$k];
            // 表格顶部数据 数据初始化
            $ret[$k]['shenggong'] = '';
            $ret[$k]['liunian'] = '';
            $ret[$k]['liuyue'] = '';
            $ret[$k]['liuri'] = '';
            $ret[$k]['liuniandoujun'] = '';
            $ret[$k]['top1'] = [];
            $ret[$k]['top2'] = [];
        }
        $ret[$tshenggong]['shenggong'] = '身宫';
        // 主星庙旺失陷
        $zhuxingmiao = $this->getZhuXingMwsx();
        // 四化星
        $siHuaXing = $this->getSiHuaXing();
        foreach ($zhuxingmiao as $k5 => $v5) {
            if (array_key_exists($k5, $siHuaXing)) {
                $ret[$v5[1]]['top1'][] = [$k5, $v5[0], $siHuaXing[$k5]];
            } else {
                $ret[$v5[1]]['top1'][] = [$k5, $v5[0], ''];
            }
        }
        // '生年干系诸星'(禄存,擎羊,陀罗 已用)
        $sngxzx = $this->getShengNianXiZhuXing();
        foreach ($sngxzx as $k6 => $v6) {
            if (!in_array($k6, ['禄存', '擎羊', '陀罗'])) {
                $ret[$v6]['top2'][] = $k6;
            }
        }
        // '生年支系诸星'
        $syzxzx = $this->getShengNianZhiXiZhuXing();
        foreach ($syzxzx as $k7 => $v7) {
            $ret[$v7]['top2'][] = $k7;
        }
        // '生月系诸星'
        $shengyuexizhuxing = $this->getShengYueXiZhuXing();
        foreach ($shengyuexizhuxing as $k8 => $v8) {
            $ret[$v8]['top2'][] = $k8;
        }
        // '时系诸星'
        $shixizhuxing = $this->getShiXiZhuXing();
        foreach ($shixizhuxing as $k9 => $v9) {
            if (!in_array($k9, ['文昌', '文曲'])) {
                $ret[$v9]['top2'][] = $k9;
            }
        }
        // 日系诸星
        $rixizhuxing = $this->getRiXiZhuXing();
        foreach ($rixizhuxing as $k => $v) {
            $ret[$v]['top2'][] = $k;
        }
        // '天才'
        $ret[$this->getTianCai()]['top2'][] = '天才';
        // '天寿'
        $ret[$this->getTianShou()]['top2'][] = '天寿';
        // 天使: '巳', 天伤: '卯'
        $tianshang = $this->getTianShang();
        foreach ($tianshang as $k => $v) {
            $ret[$v]['top2'][] = $k;
        }
        // 截空 旬空
        $ret[$this->getJieKong()]['top2'][] = '截空';
        $ret[$this->getXunKong()]['top2'][] = '旬空';
        // 流年相关
        $liuniang = $this->getLiuNianDouJun();
        $ret[$liuniang['流年']]['liunian'] = '流年';
        $ret[$liuniang['流月']]['liuyue'] = '流月';
        $ret[$liuniang['流日']]['liuri'] = '流日';
        $ret[$liuniang['流年斗君']]['liuniandoujun'] = '流年斗君';
        foreach ($ret as $k => $v) {
            $siHuaCur = [];
            $top1 = array_column($v['top1'], 0);
            $top2 = $v['top2'];
            foreach ($siHuaXing as $k1 => $v1) {
                if (in_array($k1, $top2)) {
                    $siHuaCur[] = [$k1, $v1];
                }
                if (in_array($k1, $top1)) {
                    $siHuaCur[] = [$k1, $v1];
                }
            }
            $ret[$k]['sihua'] = $siHuaCur;
        }
        return ['base' => $base, 'info' => $ret];
    }

    /**
     * 命宫
     * @return array
     */
    public function getMingGong(): array
    {
        $minggong = [];
        $mingGIndex = $this->mingGIndex;
        // 排12宫，以命工为基准，逆排
        for ($i = 1; $i <= 12; $i++) {
            $MgIndex = ($mingGIndex - $i + 12) % 12;
            $tmp = $MgIndex + 1;
            if ($tmp == 12) {
                $tmp = 0;
            }
            $minggong[$this->gongZhi[$tmp]] = $this->seGong[$i - 1];
        }
        return $minggong;
    }

    /**
     * 身宫
     * @return string 身宫所对应的位置
     */
    public function getShengGong(): string
    {
        $shenGIndex = $this->shenGIndex;
        return $this->gongZhi[$shenGIndex];
    }

    /**
     * 求出地盘所对应的天干
     * return array 天干数组
     */
    public function getDptg(): array
    {
        $lunar = $this->lunar;
        $DpTg = [];
        $tg_list = [
            ['丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁'],
            ['戊', '己', '庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己'],
            ['庚', '辛', '壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛'],
            ['壬', '癸', '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'],
            ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸', '甲', '乙'],
        ];
        $tg_list2 = ['甲' => 0, '乙' => 1, '丙' => 2, '丁' => 3, '戊' => 4, '己' => 0, '庚' => 1, '辛' => 2, '壬' => 3, '癸' => 4];
        $y = $lunar['jinian']['y'][0];
        $tmp = $tg_list[$tg_list2[$y]];
        foreach ($this->gongZhi as $k => $v) {
            $DpTg[$v] = $tmp[$k];
        }
        return $DpTg;
    }

    /**
     * 五行局
     * @return array 如['土五局','5']
     */
    public function getWuXingJu(): array
    {
        $wxIndex = ($this->mingGIndex + 2) % 12;
        $dptg = $this->getDptg();
        $wxgz = $dptg[$this->gongZhi[$this->mingGIndex]] . $this->dz[$wxIndex];
        $wuXinJu = [];
        $wxlist = [
            [['丙子', '丁丑', '甲申', '乙酉', '壬辰', '癸巳', '丙午', '丁未', '甲寅', '乙卯', '壬戌', '癸亥'], '水二局', '2'],
            [['戊辰', '己巳', '壬午', '癸未', '庚寅', '辛卯', '戊戌', '己亥', '壬子', '癸丑', '庚申', '辛酉'], '木三局', '3'],
            [['甲子', '乙丑', '壬申', '癸酉', '庚辰', '辛巳', '甲午', '乙未', '壬寅', '癸卯', '庚戌', '辛亥'], '金四局', '4'],
            [['庚午', '辛未', '戊寅', '己卯', '丙戌', '丁亥', '庚子', '辛丑', '戊申', '己酉', '丙辰', '丁巳'], '土五局', '5'],
            [['丙寅', '丁卯', '甲戌', '乙亥', '戊子', '己丑', '丙申', '丁酉', '甲辰', '乙巳', '戊午', '己未'], '火六局', '6'],
        ];
        foreach ($wxlist as $v) {
            if (in_array($wxgz, $v[0])) {
                $wuXinJu = [$v[1], $v[2]];
                break;
            }
        }
        return $wuXinJu;
    }

    /**
     * 定紫薇星
     * @return string
     */
    public function getZhiWeiXing(): string
    {
        $zwxinglist = [
            '2' => ['丑', '寅', '寅', '卯', '卯', '辰', '辰', '巳', '巳', '午', '午', '未', '未', '申', '申', '酉', '酉', '戌', '戌', '亥', '亥', '子', '子', '丑', '丑', '寅', '寅', '卯', '卯', '辰'],
            '3' => ['辰', '丑', '寅', '巳', '寅', '卯', '午', '卯', '辰', '未', '辰', '巳', '申', '巳', '午', '酉', '午', '未', '戌', '未', '申', '亥', '申', '酉', '子', '酉', '戌', '丑', '戌', '亥'],
            '4' => ['亥', '辰', '丑', '寅', '子', '巳', '寅', '卯', '丑', '午', '卯', '辰', '寅', '未', '辰', '巳', '卯', '申', '巳', '午', '辰', '酉', '午', '未', '巳', '戌', '未', '申', '午', '亥'],
            '5' => ['午', '亥', '辰', '丑', '寅', '未', '子', '巳', '寅', '卯', '申', '丑', '午', '卯', '辰', '酉', '寅', '未', '辰', '巳', '戌', '卯', '申', '巳', '午', '亥', '辰', '酉', '午', '未'],
            '6' => ['酉', '午', '亥', '辰', '丑', '寅', '戌', '未', '子', '巳', '寅', '卯', '亥', '申', '丑', '午', '卯', '辰', '子', '酉', '寅', '未', '辰', '巳', '丑', '戌', '卯', '申', '巳', '午'],
        ];
        $lunar = $this->lunar;
        $d = $lunar['_nongli']['d'];
        $jindex = $this->wuXinJu[1];
        return $zwxinglist[$jindex][$d - 1];
    }

    /**
     * 十四正星
     * @return array
     */
    public function getFourteenZhengXing(): array
    {
        $fourteenzx_list = [
            '子' => ['天机' => '亥', '太阳' => '酉', '武曲' => '申', '天同' => '未', '廉贞' => '辰', '天府' => '辰', '太阴' => '巳', '贪狼' => '午', '巨门' => '未', '天相' => '申', '天梁' => '酉', '七杀' => '戌', '破军' => '寅'],
            '丑' => ['天机' => '子', '太阳' => '戌', '武曲' => '酉', '天同' => '申', '廉贞' => '巳', '天府' => '卯', '太阴' => '辰', '贪狼' => '巳', '巨门' => '午', '天相' => '未', '天梁' => '申', '七杀' => '酉', '破军' => '丑'],
            '寅' => ['天机' => '丑', '太阳' => '亥', '武曲' => '戌', '天同' => '酉', '廉贞' => '午', '天府' => '寅', '太阴' => '卯', '贪狼' => '辰', '巨门' => '巳', '天相' => '午', '天梁' => '未', '七杀' => '申', '破军' => '子'],
            '卯' => ['天机' => '寅', '太阳' => '子', '武曲' => '亥', '天同' => '戌', '廉贞' => '未', '天府' => '丑', '太阴' => '寅', '贪狼' => '卯', '巨门' => '辰', '天相' => '巳', '天梁' => '午', '七杀' => '未', '破军' => '亥'],
            '辰' => ['天机' => '卯', '太阳' => '丑', '武曲' => '子', '天同' => '亥', '廉贞' => '申', '天府' => '子', '太阴' => '丑', '贪狼' => '寅', '巨门' => '卯', '天相' => '辰', '天梁' => '巳', '七杀' => '午', '破军' => '戌'],
            '巳' => ['天机' => '辰', '太阳' => '寅', '武曲' => '丑', '天同' => '子', '廉贞' => '酉', '天府' => '亥', '太阴' => '子', '贪狼' => '丑', '巨门' => '寅', '天相' => '卯', '天梁' => '辰', '七杀' => '巳', '破军' => '酉'],
            '午' => ['天机' => '巳', '太阳' => '卯', '武曲' => '寅', '天同' => '丑', '廉贞' => '戌', '天府' => '戌', '太阴' => '亥', '贪狼' => '子', '巨门' => '丑', '天相' => '寅', '天梁' => '卯', '七杀' => '辰', '破军' => '申'],
            '未' => ['天机' => '午', '太阳' => '辰', '武曲' => '卯', '天同' => '寅', '廉贞' => '亥', '天府' => '酉', '太阴' => '戌', '贪狼' => '亥', '巨门' => '子', '天相' => '丑', '天梁' => '寅', '七杀' => '卯', '破军' => '未'],
            '申' => ['天机' => '未', '太阳' => '巳', '武曲' => '辰', '天同' => '卯', '廉贞' => '子', '天府' => '申', '太阴' => '酉', '贪狼' => '戌', '巨门' => '亥', '天相' => '子', '天梁' => '丑', '七杀' => '寅', '破军' => '午'],
            '酉' => ['天机' => '申', '太阳' => '午', '武曲' => '巳', '天同' => '辰', '廉贞' => '丑', '天府' => '未', '太阴' => '申', '贪狼' => '酉', '巨门' => '戌', '天相' => '亥', '天梁' => '子', '七杀' => '丑', '破军' => '巳'],
            '戌' => ['天机' => '酉', '太阳' => '未', '武曲' => '午', '天同' => '巳', '廉贞' => '寅', '天府' => '午', '太阴' => '未', '贪狼' => '申', '巨门' => '酉', '天相' => '戌', '天梁' => '亥', '七杀' => '子', '破军' => '辰'],
            '亥' => ['天机' => '戌', '太阳' => '申', '武曲' => '未', '天同' => '午', '廉贞' => '卯', '天府' => '巳', '太阴' => '午', '贪狼' => '未', '巨门' => '申', '天相' => '酉', '天梁' => '戌', '七杀' => '亥', '破军' => '卯'],
        ];
        return $fourteenzx_list[$this->getZhiWeiXing()];
    }

    /**
     * 生月系诸星
     * @return array
     */
    public function getShengYueXiZhuXing(): array
    {
        $syxzx_list = [
            '左辅' => ['辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯'],
            '右弼' => ['戌', '酉', '申', '未', '午', '巳', '辰', '卯', '寅', '丑', '子', '亥'],
            '天刑' => ['酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申'],
            '天姚' => ['丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子'],
            '解神' => ['申', '申', '戌', '戌', '子', '子', '寅', '寅', '辰', '辰', '午', '午'],
            '天巫' => ['巳', '申', '寅', '亥', '巳', '申', '寅', '亥', '巳', '申', '寅', '亥'],
            '天月' => ['戌', '巳', '辰', '寅', '未', '卯', '亥', '未', '寅', '午', '戌', '寅'],
            '阴煞' => ['寅', '子', '戌', '申', '午', '辰', '寅', '子', '戌', '申', '午', '辰'],
        ];
        $lunar = $this->lunar;
        $m = $lunar['_nongli']['m'];
        $ret_syxzx = [];
        /*foreach($this->dz as $v_dz){
            $ret_syxzx[$v_dz]='';
        }*/
        foreach ($syxzx_list as $k => $v) {
            $ret_syxzx[$k] = $v[$m - 1];
        }
        return $ret_syxzx;
    }

    /**
     * 生年支系诸星
     * @return array 各星系所对应的位置 如'天空'=>'丑'
     */
    public function getShengNianZhiXiZhuXing(): array
    {
        $syzxzx_list = [
            '天空' => ['丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子'],
            '天哭' => ['午', '巳', '辰', '卯', '寅', '丑', '子', '亥', '戌', '酉', '申', '未'],
            '天虚' => ['午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳'],
            '天马' => ['寅', '亥', '申', '巳', '寅', '亥', '申', '巳', '寅', '亥', '申', '巳'],
            '龙池' => ['辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯'],
            '凤阁' => ['戌', '酉', '申', '未', '午', '巳', '辰', '卯', '寅', '丑', '子', '亥'],
            '红鸾' => ['卯', '寅', '丑', '子', '亥', '戌', '酉', '申', '未', '午', '巳', '辰'],
            '天喜' => ['酉', '申', '未', '午', '巳', '辰', '卯', '寅', '丑', '子', '亥', '戌'],
            '蜚廉' => ['申', '酉', '戌', '巳', '午', '未', '寅', '卯', '辰', '亥', '子', '丑'],
            '破碎' => ['巳', '丑', '酉', '巳', '丑', '酉', '巳', '丑', '酉', '巳', '丑', '酉'],
            '孤辰' => ['寅', '寅', '巳', '巳', '巳', '申', '申', '申', '亥', '亥', '亥', '寅'],
            '寡宿' => ['戌', '戌', '丑', '丑', '丑', '辰', '辰', '辰', '未', '未', '未', '戌'],
        ];
        $lunar = $this->lunar;
        $tmpy = $lunar['jinian']['y'][1];
        $yindx = array_search($tmpy, $this->dz);
        $ret_syzxzx = [];
        foreach ($syzxzx_list as $k => $v) {
            $ret_syzxzx[$k] = $v[$yindx];
        }
        return $ret_syzxzx;
    }

    /**
     * 获得天才位
     * @return  string 天才所对应的位置
     */
    public function getTianCai(): string
    {
        $tiancai_list = ['命宫', '父母', '福德', '田宅', '官禄', '奴仆', '迁移', '疾厄', '财帛', '子女', '夫妻', '兄弟'];
        $lunar = $this->lunar;
        $tmpy = $lunar['jinian']['y'][1];
        $yindx = array_search($tmpy, $this->dz);
        $tiancai_minggong = $tiancai_list[$yindx];
        $minggong = $this->getMingGong();
        // $tiancai_pos=[];
        $tiancai_pos = '';
        foreach ($minggong as $k => $v) {
            if ($v == $tiancai_minggong) {
                // $tiancai_pos[$k]='天才';
                $tiancai_pos = $k;
            }
        }
        return $tiancai_pos;
    }

    /**
     * 天寿(在哪个地支)
     * @return string 天寿所对应的位置
     */
    public function getTianShou(): string
    {
        $shenGIndex = $this->shenGIndex;
        $lunar = $this->lunar;
        // jiNianYearD
        $tmpy = $lunar['jinian']['y'][1];
        $yindx = array_search($tmpy, $this->dz);
        $tianshouindex = ($shenGIndex + $yindx) % 12;
        return $this->gongZhi[$tianshouindex];
    }

    /**
     * 生年干系诸星
     * @return array 如'禄存' =>'卯'等
     */
    public function getShengNianXiZhuXing(): array
    {
        $lunar = $this->lunar;
        // jiNianYearT
        $tmpy = $lunar['jinian']['y'][0];
        $sngxzx_list = [
            '禄存' => ['寅', '卯', '巳', '午', '巳', '午', '申', '酉', '亥', '子'],
            '擎羊' => ['卯', '辰', '午', '未', '午', '未', '酉', '戌', '子', '丑'],
            '陀罗' => ['丑', '寅', '辰', '巳', '辰', '巳', '未', '申', '戌', '亥'],
            '天魁' => ['丑', '子', '亥', '亥', '丑', '子', '丑', '午', '卯', '卯'],
            '天钺' => ['未', '申', '酉', '酉', '未', '申', '未', '寅', '巳', '巳'],
            '天官' => ['未', '辰', '巳', '寅', '卯', '酉', '亥', '酉', '戌', '午'],
            '天福' => ['酉', '申', '子', '亥', '卯', '寅', '午', '巳', '午', '巳'],
        ];
        $yindx = array_search($tmpy, $this->tg);
        $ret_sngxzx = [];
        foreach ($sngxzx_list as $k => $v) {
            $ret_sngxzx[$k] = $v[$yindx];
        }
        return $ret_sngxzx;
    }

    /**
     * 四化星
     * @return array 如 '天机'=>'禄'
     */
    public function getSiHuaXing(): array
    {
        $lunar = $this->lunar;
        // jiNianYearT
        $tmpy = $lunar['jinian']['y'][0];
        $yindx = array_search($tmpy, $this->tg);
        $ret_sihuaxing = [];
        $sihuaxing_list = [
            '禄' => ['廉贞', '天机', '天同', '太阴', '贪狼', '武曲', '太阳', '巨门', '天梁', '破军'],
            '权' => ['破军', '天梁', '天机', '天同', '太阴', '贪狼', '武曲', '太阳', '紫微', '巨门'],
            '科' => ['武曲', '紫微', '文昌', '天机', '右弼', '天梁', '太阴', '文曲', '左辅', '太阴'],
            '忌' => ['太阳', '太阴', '廉贞', '巨门', '天机', '文曲', '天同', '文昌', '武曲', '贪狼'],
        ];
        foreach ($sihuaxing_list as $k => $v) {
            $ret_sihuaxing[$v[$yindx]] = $k;
        }
        return $ret_sihuaxing;
    }

    /**
     * 时系诸星
     * @return array '文昌' =>'酉'
     */
    public function getShiXiZhuXing(): array
    {
        $lunar = $this->lunar;
        // jiNianHourD 出生时支
        $tmphourD = $lunar['jinian']['h'][1];
        $sxzx_list = [
            '文昌' => ['戌', '酉', '申', '未', '午', '巳', '辰', '卯', '寅', '丑', '子', '亥'],
            '文曲' => ['辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯'],
            '地劫' => ['亥', '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌'],
            '地空' => ['亥', '戌', '酉', '申', '未', '午', '巳', '辰', '卯', '寅', '丑', '子'],
            '台辅' => ['午', '未', '申', '酉', '戌', '亥', '子', '丑', '寅', '卯', '辰', '巳'],
            '封诰' => ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'],
        ];
        $sxzxindex = array_search($tmphourD, $this->dz);
        $ret_sxzx = [];
        foreach ($sxzx_list as $k => $v) {
            $ret_sxzx[$k] = $v[$sxzxindex];
        }
        return $ret_sxzx;
    }

    /**
     * 获得火灵二星
     * @return array '火星' =>'酉'
     */
    public function getHuoLing(): array
    {
        $lunar = $this->lunar;
        // jiNianYearD 年支
        $y2 = $lunar['jinian']['y'][1];
        // jiNianHourD 时支
        $h2 = $lunar['jinian']['h'][1];
        $num = array_search($h2, $this->dz);
        if (in_array($y2, ['寅', '午', '戌'])) {
            $rethuoxing = $this->dz[($num + 1) % 12];
            $retlingxing = $this->dz[($num + 3) % 12];
        } elseif (in_array($y2, ['申', '子', '辰'])) {
            $rethuoxing = $this->dz[($num + 2) % 12];
            $retlingxing = $this->dz[($num + 10) % 12];
        } elseif (in_array($y2, ['巳', '酉', '丑'])) {
            $rethuoxing = $this->dz[($num + 3) % 12];
            $retlingxing = $this->dz[($num + 10) % 12];
        } else {
            $rethuoxing = $this->dz[($num + 9) % 12];
            $retlingxing = $this->dz[($num + 10) % 12];
        }
        return ['火星' => $rethuoxing, '铃星' => $retlingxing];
    }

    /**
     * 日系诸星
     * @return array  '三台' =>'酉',
     */
    public function getRiXiZhuXing(): array
    {
        $lunar = $this->lunar;
        // 生月系诸星
        $syxzx = $this->getShengYueXiZhuXing();
        // 时系诸星
        $sxzx = $this->getShiXiZhuXing();

        $zuofu = $syxzx['左辅'];
        $zuofuindex = array_search($zuofu, $this->dz);
        $d = $lunar['_nongli']['d'];
        $santindex = ($zuofuindex + $d - 1) % 12;
        // 三台
        $dantai = $this->dz[$santindex];
        // 八  座
        // 右弼所在位置
        $youbiindex = array_search($syxzx['右弼'], $this->dz);
        $bazuoindex = ($youbiindex - (($d - 1) % 12) + 12) % 12;
        // 八  座
        $bazuo = $this->dz[$bazuoindex];
        // 恩  光
        // 文昌所在位置
        $wenchangindex = array_search($sxzx['文昌'], $this->dz);
        // 恩光位置
        $engindex = ($wenchangindex + $d + 12 - 2) % 12;
        $enguang = $this->dz[$engindex];// 恩光
        // 天贵
        // 文曲所在位置
        $wenquindex = array_search($sxzx['文曲'], $this->dz);
        // 天贵位置
        $tianguiindex = ($wenquindex + $d + 12 - 2) % 12;
        // 天贵位置
        $tiangui = $this->dz[$tianguiindex];
        return [
            '三台' => $dantai,
            '八座' => $bazuo,
            '恩光' => $enguang,
            '天贵' => $tiangui,
        ];
    }

    /**
     * 天伤、天使星
     * @return array  '天伤' =>'酉',
     */
    public function getTianShang(): array
    {
        $minggong = $this->getMingGong();
        $ret = [];
        foreach ($minggong as $k => $v) {
            // 天伤星固定在奴仆宫，天使星固定在疾厄宫
            if ($v == '奴仆') {
                $ret['天伤'] = $k;
            }
            if ($v == '疾厄') {
                $ret['天使'] = $k;
            }
        }
        return $ret;
    }

    /**
     * 生年博士十二神
     * @return array  '酉'=>'博士'
     */
    public function getShengNianBoShi12Sheng(): array
    {
        // 阳男阴女顺行，阴男阳女逆行  年数的最后一个是偶数的，就是阳年//sex 0 男
        $orders = 0;
        $tmp = $this->yinyang . ($this->sex ? '女' : '男');
        if (in_array($tmp, ['阳男', '阴女'])) {
            $orders = 1;
        }
        // 生年干系诸星
        $sngxzx = $this->getShengNianXiZhuXing();
        $boshi = ['博士', '力士', '青龙', '小耗', '将军', '奏书', '飞廉', '喜神', '病符', '大耗', '伏兵', '官符'];
        $lucunindex = array_search($sngxzx['禄存'], $this->dz);
        $boshis = [];
        if ($orders) {
            for ($i = 0; $i <= 11; $i++) {
                $boshis[$this->dz[($lucunindex + $i) % 12]] = $boshi[$i];
            }
        } else {
            for ($i = 0; $i <= 11; $i++) {
                $boshis[$this->dz[($lucunindex + 12 - $i) % 12]] = $boshi[$i];
            }
        }
        return $boshis;
    }

    /**
     * 长生十二神
     * @return array  '酉'=>'长生'
     */
    public function getChangSheng12(): array
    {
        $changsheng = ['长生', '沐浴', '冠带', '临官', '帝旺', '衰', '病', '死', '墓', '绝', '胎', '养'];
        $orders = 0;
        // 排序  阳男阴女顺行，阴男阳女逆行
        //        if ((($this->sex == 0) && (date('Y', $this->dateTime) % 2 == 0)) || (($this->sex == 1) && (date('Y', $this->dateTime) % 2 == 1))) {
        //            $orders = 1;
        //        }//
        $tmp = $this->yinyang . ($this->sex ? '女' : '男');
        if (in_array($tmp, ['阳男', '阴女'])) {
            $orders = 1;
        }
        $wuxing = $this->getWuXingJu();
        $changsh_list = ['2' => '申', '3' => '亥', '4' => '巳', '5' => '申', '6' => '寅'];
        $changsh = $changsh_list[$wuxing[1]];
        $changshindex = array_search($changsh, $this->dz);
        $ret_changsheng = [];
        if ($orders) {
            for ($i = 0; $i <= 11; $i++) {
                $ret_changsheng[$this->dz[($changshindex + $i) % 12]] = $changsheng[$i];
            }
        } else {
            for ($i = 0; $i <= 11; $i++) {
                $ret_changsheng[$this->dz[($changshindex + 12 - $i) % 12]] = $changsheng[$i];
            }
        }
        return $ret_changsheng;
    }

    /**
     * 大限岁数
     * @return array  '酉'=>'4-14'
     */
    public function getDaXianSuiShu(): array
    {
        // 命宫在dz的位置
        $mingGIndex = ($this->mingGIndex + 2) % 12;
        // 排序  阳男阴女顺行，阴男阳女逆行
        $orders = 0;
        $tmp = $this->yinyang . ($this->sex ? '女' : '男');
        if (in_array($tmp, ['阳男', '阴女'])) {
            $orders = 1;
        }
        $wuxingju = $this->getWuXingJu();
        $wuxingindex = $wuxingju[1];
        $xushui = $wuxingindex - 1;
        if ($orders) {
            for ($i = 0; $i <= 11; $i++) {
                $daxian[$this->dz[($mingGIndex + $i) % 12]] = ($xushui + 1 + $i * 10) . '-' . (($xushui + 10) + $i * 10);
            }
        } else {
            for ($i = 0; $i <= 11; $i++) {
                $daxian[$this->dz[($mingGIndex + 12 - $i) % 12]] = ($xushui + 1 + $i * 10) . '-' . (($xushui + 10) + $i * 10);
            }
        }
        return $daxian;
    }

    /**
     * 起小限岁数
     * @return array  '酉'=>[1,2,3,4]
     */
    public function getXiaoXian(): array
    {
        $lunar = $this->lunar;
        // jiNianYearD 年支
        $y2 = $lunar['jinian']['y'][1];
        if (in_array($y2, ['寅', '午', '戌'])) {
            $xiaoxian = '辰';// 辰	戌	未	丑
        } elseif (in_array($y2, ['申', '子', '辰'])) {
            $xiaoxian = '戌';
        } elseif (in_array($y2, ['巳', '酉', '丑'])) {
            $xiaoxian = '未';
        } else {
            $xiaoxian = '丑';
        }
        $xiaoxianindex = array_search($xiaoxian, $this->dz);
        // 男顺女逆
        $ret_xiaoxian = [];
        if ($this->sex == 0) {
            for ($i = 0; $i <= 11; $i++) {
                for ($r = 0; $r < 10; $r++) {
                    $ret_xiaoxian[$this->dz[($xiaoxianindex + $i) % 12]][] = $i + 1 + $r * 12;
                }
            }
        } else {
            for ($i = 0; $i <= 11; $i++) {
                for ($r = 0; $r < 10; $r++) {
                    $ret_xiaoxian[$this->dz[($xiaoxianindex + 12 - $i) % 12]][] = $i + 1 + $r * 12;
                }
            }
        }

        return $ret_xiaoxian;
    }

    /**
     * 流年将前十二星
     * @return array 各宫支十二星 '酉'=>'将星'
     */
    public function getLiuNianJiang12(): array
    {
        $lunar = $this->lunar;
        // jiNianYearD 年支
        $y2 = $lunar['jinian']['y'][1];
        $lnj_list = ['将星', '攀鞍', '岁驿', '息神', '华盖', '劫煞', '灾煞', '天煞', '指背', '咸池', '月煞', '亡神'];
        $num = array_search($y2, $this->dz);
        if (in_array($y2, ['寅', '午', '戌'])) {
            $liunianjiangindex = 6;
        } elseif (in_array($y2, ['申', '子', '辰'])) {
            $liunianjiangindex = 0;
        } elseif (in_array($y2, ['巳', '酉', '丑'])) {
            $liunianjiangindex = 3;
        } else {
            $liunianjiangindex = 9;
        }
        $ret_liunianjian = [];
        foreach ($this->dz as $k => $v) {
            $ret_liunianjian[$v] = $lnj_list[($k + $liunianjiangindex) % 12];
        }
        return $ret_liunianjian;
    }

    /**
     * 流年岁前十二星
     * @return array 各宫支 流年岁前十二星  如'酉'=>'岁建
     */
    public function getLiuNianSui12(): array
    {
        $lunar = $this->lunar;
        // jiNianYearD 年支
        $y2 = $lunar['jinian']['y'][1];
        $lns_list = ['岁建', '晦气', '丧门', '贯索', '官符', '小耗', '大耗', '龙德', '白虎', '天德', '吊客', '病符'];
        $num = array_search($y2, $this->dz);
        $ret = [];
        foreach ($this->dz as $k => $v) {
            $ret[$v] = $lns_list[(12 - $num + $k) % 12];
        }
        return $ret;
    }

    /**
     * 主星庙旺失陷
     * @return array 主星庙旺失陷  如'紫微'=>['庙','甲']
     */
    public function getZhuXingMwsx(): array
    {
        // 庙+3　旺+2　地+1　利 0　平-1　不-2　陷-3
        $mwsxstr_list = ['3' => '庙', '2' => '旺', '1' => '地', '0' => '利', '-1' => '平', '-2' => '不', '-3' => '陷'];
        $mwsx_list = [
            '紫微' => ['-1', '3', '3', '2', '1', '2', '3', '3', '2', '2', '1', '2'],
            '天机' => ['3', '-3', '1', '2', '0', '-1', '3', '-3', '1', '2', '0', '-1'],
            '太阳' => ['-3', '-2', '2', '3', '2', '2', '3', '1', '0', '-1', '-3', '-3'],
            '武曲' => ['2', '3', '1', '0', '3', '-1', '2', '3', '1', '0', '3', '-1'],
            '天同' => ['2', '-2', '0', '3', '-1', '3', '-3', '-2', '2', '-1', '-1', '3'],
            '廉贞' => ['-1', '0', '3', '-1', '0', '-3', '-1', '0', '3', '-1', '0', '-3'],
            '天府' => ['3', '3', '3', '1', '2', '1', '2', '3', '1', '2', '2', '1'],
            '太阴' => ['3', '3', '-2', '-3', '-3', '-3', '-3', '-1', '0', '2', '2', '3'],
            '贪狼' => ['2', '3', '-1', '1', '3', '-3', '2', '3', '-1', '0', '3', '-3'],
            '巨门' => ['2', '-2', '3', '3', '-1', '-1', '2', '-1', '3', '3', '-1', '2'],
            '天相' => ['3', '3', '3', '-3', '-1', '1', '1', '1', '3', '-3', '1', '1'],
            '天梁' => ['3', '2', '3', '2', '3', '-3', '3', '2', '-3', '1', '3', '-3'],
            '七杀' => ['2', '3', '3', '2', '1', '-1', '2', '3', '3', '2', '3', '-1'],
            '破军' => ['3', '2', '1', '-3', '2', '-1', '3', '2', '1', '-3', '2', '-1'],
            '火星' => ['-3', '1', '3', '0', '-3', '1', '3', '0', '-3', '1', '3', '0'],
            '铃星' => ['-3', '1', '3', '0', '-3', '1', '3', '0', '-3', '1', '3', '0'],
            '文昌' => ['1', '3', '-3', '2', '1', '3', '-3', '0', '1', '3', '-3', '0'],
            '文曲' => ['1', '3', '-1', '2', '1', '3', '-3', '2', '1', '3', '-3', '2'],
            '擎羊' => ['2', '3', ' ', '-3', '3', ' ', '-3', '3', ' ', '2', '3', ' '],
            '陀罗' => [' ', '3', '-3', ' ', '3', '-3', ' ', '3', '-3', ' ', '3', '-3'],
        ];
        // 获得紫微星位置
        $ziweixing = $this->getZhiWeiXing();

        $ziweixingindex = array_search($ziweixing, $this->dz);
        $ret = [];
        $ret['紫微'] = [$mwsxstr_list[$mwsx_list['紫微'][$ziweixingindex]], $ziweixing];
        // 十四正星
        $fourteenzx = $this->getFourteenZhengXing();
        foreach ($fourteenzx as $k => $v) {
            $vindex = array_search($v, $this->dz);
            $ret[$k] = [$mwsxstr_list[$mwsx_list[$k][$vindex]], $v];
        }
        $huoling = $this->getHuoLing();
        foreach ($huoling as $k1 => $v1) {
            $vindex = array_search($v1, $this->dz);
            $ret[$k1] = [$mwsxstr_list[$mwsx_list[$k1][$vindex]], $v1];
        }
        // 时系诸星
        $shixzx = $this->getShiXiZhuXing();
        $wenquindex = array_search($shixzx['文曲'], $this->dz);
        $ret['文曲'] = [$mwsxstr_list[$mwsx_list['文昌'][$wenquindex]], $shixzx['文曲']];
        $wenquindex = array_search($shixzx['文昌'], $this->dz);
        $ret['文昌'] = [$mwsxstr_list[$mwsx_list['文昌'][$wenquindex]], $shixzx['文昌']];
        // 生年干系诸星 主要里面的禄存，擎羊 ，陀罗
        $sngxzx = $this->getShengNianXiZhuXing();
        $lucunindex = array_search($sngxzx['禄存'], $this->dz);
        $luncun_list = ['旺', '', '庙', '旺', '', '庙', '旺', '', '庙', '旺', '', '庙'];
        $ret['禄存'] = [$luncun_list[$lucunindex], $sngxzx['禄存']];
        $qingyangindex = array_search($sngxzx['擎羊'], $this->dz);
        if (in_array($sngxzx['擎羊'], ['寅', '巳', '申', '亥'])) {
            $ret['擎羊'] = ['', $sngxzx['擎羊']];
        } else {
            $ret['擎羊'] = [$mwsxstr_list[$mwsx_list['擎羊'][$qingyangindex]], $sngxzx['擎羊']];
        }
        $tuoluoindex = array_search($sngxzx['陀罗'], $this->dz);
        if (in_array($sngxzx['陀罗'], ['子', '卯', '午', '酉'])) {
            $ret['陀罗'] = ['', $sngxzx['陀罗']];
        } else {
            $ret['陀罗'] = [$mwsxstr_list[$mwsx_list['陀罗'][$tuoluoindex]], $sngxzx['陀罗']];
        }
        return $ret;
    }

    /**
     *截路空亡表
     * @return string 截空对应的位置
     */
    public function getJieKong(): string
    {
        $jiekonglist = ['甲' => '申', '己' => '酉', '乙' => '午', '庚' => '未', '丙' => '辰', '辛' => '巳', '丁' => '寅', '壬' => '卯', '戊' => '子', '癸' => '丑'];
        // 天干
        $lunar = $this->lunar;
        // jiNianYearT 年干
        $year = $lunar['jinian']['y'][0];
        $jiekong = '';
        if (array_key_exists($year, $jiekonglist)) {
            $jiekong = $jiekonglist[$year];
        }
        return $jiekong;
    }

    /**
     * 旬中空亡表
     * @return string 旬空对应的位置
     */
    public function getXunKong(): string
    {
        $lunar = $this->lunar;
        $kongwang_list = [
            '戌,亥' => ['甲子', '乙丑', '丙寅', '丁卯', '戊辰', '己巳', '庚午', '辛未', '壬申', '癸酉'],
            '申,酉' => ['甲戌', '乙亥', '丙子', '丁丑', '戊寅', '己卯', '庚辰', '辛巳', '壬午', '癸未'],
            '午,未' => ['甲申', '乙酉', '丙戌', '丁亥', '戊子', '己丑', '庚寅', '辛卯', '壬辰', '癸巳'],
            '辰,巳' => ['甲午', '乙未', '丙申', '丁酉', '戊戌', '己亥', '庚子', '辛丑', '壬寅', '癸卯'],
            '寅,卯' => ['甲辰', '乙巳', '丙午', '丁未', '戊申', '己酉', '庚戌', '辛亥', '壬子', '癸丑'],
            '子,丑' => ['甲寅', '乙卯', '丙辰', '丁巳', '戊午', '己未', '庚申', '辛酉', '壬戌', '癸亥'],
        ];
        // 阴1 阳0
        $orders = (date('Y', $this->dateTime) % 2) ? '1' : '0';
        //  jiNianYearT .jiNianYearD;
        $year = $lunar['jinian']['y'][0] . $lunar['jinian']['y'][1];
        $kongwang = '';
        foreach ($kongwang_list as $k => $v) {
            if (in_array($year, $v)) {
                // 阳年空阳支，阴年空阴支，即阳年干支两旬空星中的阳支为正空，阴支为副空；
                $tmp = explode(',', $k);
                if ($orders) {
                    $kongwang = $tmp[1];
                } else {
                    $kongwang = $tmp[0];
                }
                break;
            }
        }
        return $kongwang;
    }

    /**
     * 流年斗君,流年，流月，流日
     * @return array 流年斗君,流年，流月，流日
     * @throws \Exception
     */
    public function getLiuNianDouJun(): array
    {
        $calendar = Ex::date($this->otime)->sex(0);
        $lunar = $calendar->getLunarByBetween();
        // jiNianYearD 当前查询时间所对应的数据
        $y2 = $lunar['jinian']['y'][1];
        // 年所处位置
        $y2index = array_search($y2, $this->dz);
        // 月数字 输入的日期所对应数据
        $m = $this->lunar['_nongli']['m'];
        // 时 jiNianHourD 输入的日期所对应数据
        $h = $this->lunar['jinian']['h'][1];
        $hindex = array_search($h, $this->dz);// 时所处位置
        // 流年斗君位置
        $lyindex = (($y2index + 12 - $m) % 12 + $hindex + 1) % 12;
        // 日 当前查询时间所对应的数据
        $d2 = $lunar['_nongli']['d'];
        // 月数字 当前查询时间所对应的数据
        $m2 = $lunar['_nongli']['m'];
        if (str_contains($lunar['nongli']['m'], '闰')) {
            if ($d2 > 15) {
                $m2 = ($m2 + 1) % 12;
            }
        }
        $lindexyue = ($lyindex + $m2 - 1) % 12;// 流月位置
        $ldindex = ($lindexyue + $d2 - 1) % 12;

        return [
            '流年' => $y2,
            '流年斗君' => $this->dz[$lyindex],
            '流月' => $this->dz[$lindexyue],
            '流日' => $this->dz[$ldindex],
        ];
    }

    /**
     * 命主
     * @return string 命主
     */
    public function getMingZhu(): string
    {
        $mingzhu_list = [
            '子' => '贪狼', '丑' => '巨门', '寅' => '禄存', '卯' => '文曲', '辰' => '廉贞', '巳' => '武曲',
            '午' => '破军', '未' => '武曲', '申' => '廉贞', '酉' => '文曲', '戌' => '禄存', '亥' => '巨门',
        ];
        // 命宫所在位置
        $minggongpos = $this->gongZhi[$this->mingGIndex];
        return $mingzhu_list[$minggongpos];
    }

    /**
     * 身主
     * @return string 身主
     */
    public function getShengZhu(): string
    {
        $shengzhu_list = [
            '子' => '铃星', '丑' => '天相', '寅' => '天梁', '卯' => '天同', '辰' => '文昌', '巳' => '天机',
            '午' => '火星', '未' => '天相', '申' => '天梁', '酉' => '天同', '戌' => '文昌', '亥' => '天机',
        ];
        $lunar = $this->lunar;
        // jiNianYearD
        $y2 = $lunar['jinian']['y'][1];
        return $shengzhu_list[$y2];
    }

    /**
     * 子年斗君
     * @return string 子年斗君
     */
    public function getZiiNianDouJun(): string
    {
        $lunar = $this->lunar;
        // 农历月数字
        $m = $lunar['_nongli']['m'];
        // 时 jiNianHourD
        $h = $lunar['jinian']['h'][1];
        // 时所处位置
        $hindex = array_search($h, $this->dz);
        $zindex = ($m + $hindex) % 12;
        return $this->dz[$zindex];
    }

    /**
     * 各宫主星吉星，凶星
     * @return array
     */
    public function getGonginfo(): array
    {
        $tmpList = [];
        $res = [
            '子' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '丑' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '寅' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '卯' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '辰' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '巳' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '午' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '未' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '申' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '酉' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '戌' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
            '亥' => ['zhuxing' => [], 'jixing' => [], 'xongxing' => [], 'sihua' => []],
        ];
        $res[$this->getZhiWeiXing()]['zhuxing'][] = '紫微';
        //$zhengxing=$this->getFourteenZhengXing();'天机' => '亥',
        foreach ($this->getFourteenZhengXing() as $k => $v) {
            $res[$v]['zhuxing'][] = $k;
        }
        $tmpList = $this->getFourteenZhengXing();
        $tmpList['紫微'] = $this->getZhiWeiXing();
        // '生月系诸星'
        $shengyuexizhuxing = $this->getShengYueXiZhuXing();
        $res[$shengyuexizhuxing['左辅']]['jixing'][] = '左辅';
        $res[$shengyuexizhuxing['右弼']]['jixing'][] = '右弼';
        $tmpList['左辅'] = $shengyuexizhuxing['左辅'];
        $tmpList['右弼'] = $shengyuexizhuxing['右弼'];
        // 时系诸星
        $ShiXiZhuXing = $this->getShiXiZhuXing();
        $res[$ShiXiZhuXing['文昌']]['jixing'][] = '文昌';
        $res[$ShiXiZhuXing['文曲']]['jixing'][] = '文曲';
        $tmpList['文昌'] = $ShiXiZhuXing['文昌'];
        $tmpList['文曲'] = $ShiXiZhuXing['文曲'];
        $res[$ShiXiZhuXing['地空']]['xongxing'][] = '地空';
        $res[$ShiXiZhuXing['地劫']]['xongxing'][] = '地劫';
        // 生年系
        $ShengNianXiZhuXing = $this->getShengNianXiZhuXing();
        $res[$ShengNianXiZhuXing['禄存']]['jixing'][] = '禄存';
        $res[$ShengNianXiZhuXing['天魁']]['jixing'][] = '天魁';
        $res[$ShengNianXiZhuXing['天钺']]['jixing'][] = '天钺';
        $res[$ShengNianXiZhuXing['陀罗']]['xongxing'][] = '陀罗';
        $res[$ShengNianXiZhuXing['擎羊']]['xongxing'][] = '擎羊';
        // 生年支系诸星
        $ShengNianZhiXiZhuXing = $this->getShengNianZhiXiZhuXing();
        $res[$ShengNianZhiXiZhuXing['天马']]['jixing'][] = '天马';
        $houling = $this->getHuoLing();
        $res[$houling['火星']]['xongxing'][] = '火星';
        $res[$houling['铃星']]['xongxing'][] = '铃星';
        $SiHuaXing = $this->getSiHuaXing();
        foreach ($SiHuaXing as $k => $v) {
            $res[$tmpList[$k]]['sihua'][$k] = $v;
        }
        return $res;
    }

    /**
     * 获得用户对象
     * @return Ex
     */
    public function getCalendar(): Ex
    {
        return $this->calendar;
    }
}
