# 开启调试模式
app_debug = true


# 缓存配置
[cache]
driver = redis


# 缓存配置
[redis]
host = redis-server


# rpc接口服务
[rpc_api]
host = think-api
port = 9220
max_active = 10


# 连接池-数据库
[pool_db]
min_active = 0
max_active = 5


# 连接池-缓存
[pool_cache]
min_active = 0
max_active = 10


# 日志相关
[log]
channel = file


# 默认数据库
[database]
driver = default
hostname = *************
database = tools
username = dev
password = !@#qwe123
prefix = bw_
