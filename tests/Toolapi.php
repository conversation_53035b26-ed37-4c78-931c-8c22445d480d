<?php
// +----------------------------------------------------------------------
// | Toolapi
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests;

use think\facade\Log;
use think\helper\Str;

class Too<PERSON><PERSON> extends TestBase
{
    /**
     * 配置
     * @var array
     */
    protected array $config = [
        'app_id' => 2,
        'secret' => 'asdljklxjc12897SDKJXDsd046564xhd',
    ];

    /**
     * 接口地址
     * @var string|array
     */
    protected string | array $apiUrl = [];

    /**
     * 预期结果
     * @var string|array
     */
    protected string | array $beforeData = [];

    /**
     * 忽略字段
     * @var array
     */
    protected array $ignoreArr = [];

    /**
     * 随机测试
     * @var array
     */
    protected array $randArr = [];

    /**
     * 接口常规测试
     * @return true|void
     */
    public function testApi()
    {
        if (empty($this->apiUrl) || empty($this->beforeData)) {
            $this->assertTrue(true);
            return true;
        }
        if (!is_array($this->apiUrl)) {
            $this->apiUrl = [$this->apiUrl];
            $this->beforeData = [$this->beforeData];
        }

        foreach ($this->apiUrl as $key => $url) {
            $apiUrl = $this->baseUrl . $url;
            // 接口数据
            $apiData = $this->apiGet($apiUrl);
            // 预期结果
            $beforeData = $this->beforeData[$key];
            if (!is_array($beforeData)) {
                $beforeData = json_decode($beforeData, true);
            }
            // 需要比对的数据，多做一次json=>array
            $afterArr = json_encode($apiData['data']);
            $afterArr = json_decode($afterArr, true);
            dump($apiUrl);
            if (Str::contains($apiUrl, 'xxxx')) {
                Log::error($apiUrl);
                Log::error(json_encode($afterArr, JSON_UNESCAPED_UNICODE));
            }
            // 进行结果详细比对
            $this->valueEquals($beforeData, $afterArr, '', $apiUrl);
        }
    }

    /**
     * 随机测试
     * @return bool|void
     */
    public function testRandData()
    {
        if (empty($this->randArr)) {
            $this->assertTrue(true);
            return true;
        }
        $param = [];
        foreach ($this->randArr as $v) {
            foreach ($v['params'] as $k1 => $v1) {
                if (!is_array($v1)) {
                    // 固定数据
                    $param[$k1] = $v1;
                    continue;
                }
                $tmpParms = $v1[1] ?? [];
                $param[$k1] = $this->getRandValue($v1[0], $tmpParms);
            }
            $apiUrl = $this->baseUrl . $v['url'];
            $this->apiGet($apiUrl, $param);
        }
    }

    /**
     * 接口POST测试
     * @param string $uri 接口路径
     * @param array $query 参数
     * @param null $func 闭包
     * @return Toolapi
     */
    protected function apiPost($uri, $query = [], $func = null)
    {
        $parseUrl = parse_url($uri);
        if (isset($parseUrl['query'])) {
            $par = array_filter(explode('&', urldecode($parseUrl['query'])));
            $res = [];
            foreach ($par as $item) {
                [$key, $val] = explode('=', $item);
                if (in_array($key, ['dev', 'app_id'])) {
                    continue;
                }
                $res[$key] = $val;
            }
            // post请求参数
            $query = array_merge($res, $query);
        }
        // get请求参数
        $query2 = [];
        $query2['request_time'] = time();
        $query2['app_id'] = $this->config('app_id');
        $query2['sign2'] = $this->signature($query);
        // 转换为query参数
        $query2 = http_build_query($query2);
        // 构建url
        $uri = "{$parseUrl['scheme']}://{$parseUrl['host']}{$parseUrl['path']}?{$query2}";
        $this->pagePost($uri, $query);
        return $this->api($func);
    }

    /**
     * 接口GET测试
     * @param string $uri 接口路径
     * @param array $query 参数
     * @param null $func 闭包
     * @return $this
     */
    protected function apiGet($uri, $query = [], $func = null)
    {
        $parseUrl = parse_url($uri);
        if (isset($parseUrl['query'])) {
            $par = array_filter(explode('&', urldecode($parseUrl['query'])));
            $res = [];
            foreach ($par as $item) {
                [$key, $val] = explode('=', $item);
                if (in_array($key, ['dev', 'app_id'])) {
                    continue;
                }
                $res[$key] = $val;
            }
            $query = array_merge($res, $query);
        }
        $query['request_time'] = time();
        $query['app_id'] = $this->config('app_id');
        $query['sign2'] = $this->signature($query);
        $query = http_build_query($query);
        $uri = $parseUrl['scheme'] . '://' . $parseUrl['host'] . $parseUrl['path'] . '?' . $query;
        $this->pageGet($uri);
        return $this->api($func);
    }

    /**
     * 接口单元测试
     * @param $func
     * @return mixed
     */
    protected function api($func)
    {
        $this->isJsonRetrun();
        $json = $this->response->getData();
        if (empty($json['status'])) {
            Log::error(json_encode($json));
        }
        $this->assertTrue(isset($json['status']) && $json['status'] == 1, $this->error('接口状态不正确'));
        if ($func instanceof \Closure) {
            $params = [
                $this->response,
                $json,
            ];
            call_user_func_array($func, $params);
        }
        return $json;
    }

    /**
     * 获取一个随机的生日
     * @param int $startYear 起始年份
     * @param int $endYear 结束年份
     * @return string 如：2007-05-09
     */
    protected function getRandBirthday(int $startYear = 1960, int $endYear = 2010)
    {
        $month = rand(1, 12);
        $month = $month < 10 ? '0' . $month : $month;
        $day = rand(1, 28);
        $day = $day < 10 ? '0' . $day : $day;
        $birthday = rand($startYear, $endYear) . '-' . $month . '-' . $day;
        return $birthday;
    }

    /**
     * 获得随机参数的值
     * @param $func
     * @param array $param
     * @return mixed|false
     */
    protected function getRandValue($func, array $param = [])
    {
        if (method_exists($this, $func)) {
            return call_user_func_array([$this, $func], $param);
        } elseif (function_exists($func)) {
            return call_user_func_array($func, $param);
        } else {
            return $func;
        }
    }

    /**
     * 接口结果对比
     * @param string|array $beforeArr 正确的数据
     * @param string|array $afterArr 被验证的数据
     * @param string $kName 当前字段名称
     * @param string $apiUrl 请求地址
     */
    protected function valueEquals($beforeArr, $afterArr, $kName = '', $apiUrl = null)
    {
        // 字符串过滤处理
        $valueFun = function ($value) {
            $value = trim($value);
            $value = str_replace([' ', "\u{a0}"], '', $value);
            return $value;
        };
        // 当前循环到的字段名称
        $arrKeyName = '';
        if (is_array($beforeArr)) {
            // 遍历正确的数据，比对被验证的数据
            foreach ($beforeArr as $key => $val) {
                if (is_string($key)) {
                    $arrKeyName = $kName . "['{$key}']";
                } else {
                    $arrKeyName = $kName . "[{$key}]";
                }
                // 忽略字段
                if (!empty($this->ignoreArr)) {
                    // 是否忽略
                    $continue = false;
                    foreach ($this->ignoreArr as $match) {
                        // 正则
                        if (str_starts_with($match, '/')) {
                            if (preg_match($match, $arrKeyName)) {
                                $continue = true;
                                break;
                            }
                        }
                        // 全等
                        if ($match == $arrKeyName) {
                            $continue = true;
                            break;
                        }
                    }
                    if ($continue) {
                        continue;
                    } else {
                        // afterArr 是否存在键值
                        $this->assertTrue(isset($afterArr[$key]), "1.url:\n{$apiUrl}\n \$afterArr{$arrKeyName} 键值不存在 ignore ");
                    }
                } else {
                    // afterArr 是否存在键值
                    $this->assertTrue(isset($afterArr[$key]), "2.url:\n{$apiUrl}\n \$afterArr{$arrKeyName} 键值不存在");
                }
                // 如果是数组，继续递归
                if (is_array($val)) {
                    $this->valueEquals($val, $afterArr[$key], $arrKeyName);
                } else {
                    $type1 = gettype($val);
                    $type2 = gettype($afterArr[$key]);
                    $this->assertTrue($type1 === $type2, "3.url:\n{$apiUrl}\n \$beforeArr{$arrKeyName}={$type1}\n or \n\$afterArr{$arrKeyName}={$type2} \n 变量类型不相同");
                    $this->assertEquals($valueFun($val), $valueFun($afterArr[$key]), "4.url:\n{$apiUrl}\n \$beforeArr{$arrKeyName} \n or \n \$afterArr{$arrKeyName} \n 值不相同\n\n\n");
                }
            }
        } elseif (is_string($beforeArr)) {
            $this->assertTrue($beforeArr === $afterArr, "5.url:\n{$apiUrl}\n {$beforeArr} or {$afterArr} 数据不相同");
        }
    }


    /**
     * 设置获取config配置
     * @param array|string $name 配置项
     * @return $this|array
     */
    public function config(array | string $name = '')
    {
        if (is_array($name)) {
            $this->config = array_merge($this->config, $name);
            return $this;
        } elseif ($name) {
            return $this->config[$name] ?? null;
        }
        return $this->config;
    }

    /**
     * 计算sign2
     * @param array $data
     * @return string
     */
    protected function signature($data)
    {
        $params = [];
        foreach ($data as $key => $val) {
            // 过滤系统参数
            if (is_string($key) && in_array($key, ['sign', 'sign2', 'version', 'controller', 'action'])) {
                continue;
            }
            // 如果键值是数组
            if (is_array($val)) {
                $params[$key] = "[{$this->signature($val)}]";
            } else {
                $params[$key] = $val;
            }
        }
        // 待签名字符串
        $str = '';
        // 先将参数以其参数名的字典序升序进行排序
        ksort($params);
        // 遍历排序后的参数数组中的每一个key/value对
        foreach ($params as $k => $v) {
            // 为key/value对生成一个key=value格式的字符串，并拼接到待签名字符串后面
            $str .= "{$k}={$v}";
        }
        $str = strtolower($str);
        // 将签名密钥拼接到签名字符串最后面
        $str .= $this->config('secret');
        // 通过md5算法为签名字符串生成一个md5签名，该签名就是我们要追加的sign参数值
        return md5($str);
    }
}
