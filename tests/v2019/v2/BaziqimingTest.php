<?php
// +----------------------------------------------------------------------
// | BaziqimingTest.第一星座-八字取名接口开发
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v2;

use tests\Toolapi;

class BaziqimingTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = [
        '/v1/baziqiming/index.html?time=2001-10-5&sex=0&xing=林&num=2&otime=2020-08-20',
        '/v2/baziqiming/index.html?time=2020-4-5&xing=林&sex=0&limit=30&zi=1&otime=2020-02-12',
    ];

    /**
     * 忽略字段
     * @var string[]
     */
    protected array $ignoreArr = [
        "/^\['names'\]/i",
    ];

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = [
        '{"xing":{"xing":"林","bihua":8,"wx":"木"},"likegod":"木","bihuazuhe":["8+16","9+7","9+6","13+12","10+6","3+12","7+16","9+6"],"yiwx":"","wxpeizhi":["木火","木金","木木","木水","木土","火木","金木","水木","土木"]}',
        '{"bases":{"nongli":{"y":"二零二零","m":"三月","d":"十三"},"_nongli":{"y":2020,"m":3,"d":13},"shengxiao":"鼠","jinian":{"y":["庚","子"],"m":["庚","辰"],"d":["戊","寅"],"h":["壬","子"]}},"xing":{"xing":"林","bihua":8,"wx":"木"},"likegod":"火","jnwx":[["金",2],["木",1],["水",3],["火",0],["土",2]],"names":["臣慕","随泷","新茗","鑫岳","千思","周弈","愉天","锐卓","利祖","镇钤","司司","鎏亭","尊奎","舜珉","齐纭","旌绍","信永","千善","才采","锋圣","载纯","宣伟","纯庚","周麟","金钊","金梓","捷平","然札","金锡","鑫北"]}',
    ];

    /**
     * 随机测试
     * @var array[]
     */
    protected array $randArr = [
        [
            'url' => '/v1/baziqiming/index.html',
            'params' => [
                'xing' => '林',
                'time' => ['getRandBirthday'],
                'sex' => ['rand', [0, 1]],
                'num' => ['rand', [1, 2]],
            ],
        ],
    ];
}
