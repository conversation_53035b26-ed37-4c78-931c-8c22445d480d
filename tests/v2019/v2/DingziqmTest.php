<?php
// +----------------------------------------------------------------------
// | 定字起名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v2;

use tests\Toolapi;

class DingziqmTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = [
        '/v1/dingziqm/index.html?xing=陈&zi=龙',
        '/v2/dingziqm/index.html?xing=林&time=2010-05-11%2013:00:00&zi=海&type=1&page=1&limit=36&sex=0',
    ];

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = [
        '[{"xm":"陈龙生","fs":81.25},{"xm":"陈龙小","fs":66.35},{"xm":"陈龙春","fs":81.85},{"xm":"陈龙少","fs":67.45},{"xm":"陈龙仁","fs":67.45},{"xm":"陈龙青","fs":77.85},{"xm":"陈龙敬","fs":70.35},{"xm":"陈龙才","fs":67.45},{"xm":"陈龙臣","fs":70.55},{"xm":"陈龙诚","fs":71.45},{"xm":"陈龙佳","fs":77.85},{"xm":"陈龙双","fs":65.85},{"xm":"陈龙善","fs":68.45},{"xm":"陈龙楚","fs":70.35},{"xm":"陈龙秀","fs":78.55},{"xm":"陈龙梓","fs":71.35},{"xm":"陈龙纯","fs":75.95},{"xm":"陈龙尚","fs":77.85},{"xm":"陈龙杨","fs":70.35},{"xm":"陈龙三","fs":66.35},{"xm":"陈龙献","fs":71.95},{"xm":"陈龙前","fs":81.85},{"xm":"陈龙周","fs":77.85},{"xm":"陈龙驹","fs":81.95},{"xm":"陈龙兰","fs":78.45},{"xm":"陈龙关","fs":81.85},{"xm":"陈龙儒","fs":81.25},{"xm":"陈龙舒","fs":68.45},{"xm":"陈龙又","fs":79.45},{"xm":"陈龙笑","fs":75.95},{"xm":"陈龙宙","fs":77.85},{"xm":"陈龙舟","fs":70.55},{"xm":"陈龙棋","fs":68.45},{"xm":"陈龙祈","fs":81.85},{"xm":"陈龙贡","fs":75.95},{"xm":"陈龙舸","fs":71.35}]',
        '{"base":{"nongli":{"y":"二零一零","m":"三月","d":"廿八"},"_nongli":{"y":2010,"m":3,"d":28},"shengxiao":"虎","jinian":{"y":["庚","寅"],"m":["辛","巳"],"d":["辛","酉"],"h":["乙","未"]}},"likegod":"水","xm":["海洇","海虚","海雯","海涨","海浏","海派","海涪","海浓","海凼","海纹","海巴","海浒","海米","海鲍","海乎","海绘","海媒","海娆","海溆","海魂","海浣","海弁","海祜","海菏","海浍","海涯","海吩","海马","海扁","海谱","海亥","海聘","海流","海伴","海不","海飙"]}',
    ];
}
