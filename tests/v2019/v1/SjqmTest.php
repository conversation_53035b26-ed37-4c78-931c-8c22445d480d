<?php
// +----------------------------------------------------------------------
// |  SjqmTest
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡
// +----------------------------------------------------------------------

namespace tests\v2019\v1;

use tests\Toolapi;
use think\facade\Validate;

class SjqmTest extends Toolapi
{
    /**
     * 测试论语起名
     * @return void
     */
    public function testList()
    {
        $url = $this->baseUrl . "/v1/lunyu/index.html";
        $this->apiGet($url);
        $data = $this->response->getData();
        $validate = Validate::rule(
            [
                'data.count' => ['require', 'number'],
                'data.data' => ['array'],
                'data.data.0.ming' => ['chs'],
                'data.data.0.id' => ['number'],
                'data.data.0.gender' => ['number'],
                'data.data.0.ziArr' => ['array'],
                'data.data.0.ziExplain' => ['array'],
                'data.data.0.detail' => ['require', 'array'],
            ]
        );
        if (!$validate->check($data)) {
            $this->fail($validate->getError());
        }
        $this->assertTrue(true, 'ok');
    }
}
