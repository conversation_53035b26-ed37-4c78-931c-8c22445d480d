<?php
// +----------------------------------------------------------------------
// | QimendunjiaTest.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v1;

use tests\Toolapi;

class QimendunjiaTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = [
        '/v1/qimendunjia/index.html?time=1993-12-20%2020:00:00&type=1&fs=1',
    ];

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = [
        '{"nongli":{"y":"一九九三","m":"冬月","d":"初八"},"jinian":{"y":["癸","酉"],"m":["甲","子"],"d":["乙","亥"],"h":["丙","戌"]},"xunkong":{"y":"戌亥","m":"戌亥","d":"申酉","h":"午未"},"jieqi":{"current":["大雪","1993-12-07 10:33:49"],"next":["冬至","1993-12-22 04:25:48"]},"ju":["阴","一","下元","",""],"fuzhou":[["甲","申"],"庚"],"zhifu":["天任","震三",2],"zhishi":["生门","乾六",5],"detail":[{"top":["九天","天英","辛"],"center":["","开门",""],"bottom":["九天","巽四","丁"]},{"top":["六合","天禽","乙"],"center":["","死门",""],"bottom":["六合","离九","己"]},{"top":["腾蛇","天柱","己"],"center":["","杜门",""],"bottom":["腾蛇","坤二","乙"]},{"top":["直符","天任","庚"],"center":["","中门",""],"bottom":["直符","震三","丙"]},{"top":["九地","天蓬","壬"],"center":["","惊门",""],"bottom":["九地","中五","癸"]},{"top":["太常","天冲","丁"],"center":["","景门",""],"bottom":["太常","兑七","辛"]},{"top":["白虎","天辅","丙"],"center":["","休门",""],"bottom":["白虎","艮八","庚"]},{"top":["太阴","天心","戊"],"center":["","伤门",""],"bottom":["太阴","坎一","戊"]},{"top":["玄武","天芮","癸"],"center":["","生门",""],"bottom":["玄武","乾六","壬"]}]}',
    ];

    /**
     * 随机测试
     * @var array[]
     */
    protected array $randArr = [
        [
            'url' => '/v1/qimendunjia/index.html',
            'params' => [
                'time' => ['getRandBirthday'],
                'type' => ['rand', [1, 3]],
                'fs' => ['rand', [0, 1]],
            ],
        ],
    ];
}
