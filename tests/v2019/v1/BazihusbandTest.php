<?php
// +----------------------------------------------------------------------
// | DemoTest 测试示例脚本
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v1;

use tests\Toolapi;

class BazihusbandTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = '/v1/bazihusband/getinfo.html?username=测试&time=2010-08-08%2000:00:00&sex=0';

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = '{"lunar":{"nongli":{"y":"二零一零","m":"六月","d":"廿八"},"_nongli":{"y":2010,"m":6,"d":28},"shengxiao":"虎","jinian":{"y":["庚","寅"],"m":["甲","申"],"d":["庚","寅"],"h":["丙","子"]}},"yuansheng":"庚金","hunhou":"他在家中有大男子主义倾向，不喜欢别人质疑他的权威。然而在工作上，他却是个拼命三郎，无论取得何种成就，都保持着谦逊学习的态度。这种特质使得他即使面临中年危机，也能够化险为夷。","suggtest":"和他在一起，你的生活将无忧无虑，甚至在中年时还会有意想不到的惊喜。只需维护他的男性自尊，让他在家中扮演主角。不要与他争执。","zishu":70}';
    /**
     * 随机测试
     * @var array[]
     */
    protected array $randArr = [
        [
            'url' => '/v1/bazihusband/getinfo.html',
            'params' => [
                'username' => '测试',
                'time' => ['getRandBirthday'],
                'sex' => ['rand', [0, 1]],
            ],
        ],
    ];
}
