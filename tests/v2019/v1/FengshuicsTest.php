<?php
// +----------------------------------------------------------------------
// | FengshuicsTest.
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: 水平凡 <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v1;

use tests\Toolapi;

class FengshuicsTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = '/v1/fengshuics/index.html?time=2001-10-5&sex=0&pos=北';

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = '{"base":{"nongli":{"y":"二零零一","m":"八月","d":"十九"},"_nongli":{"y":2001,"m":8,"d":19},"shengxiao":"蛇","jinian":{"y":["辛","巳"],"m":["丁","酉"],"d":["辛","丑"],"h":["戊","子"]}},"nayin":{"year":"白腊金","month":"山下火","day":"壁上土","hour":"霹雳火"},"gua":"艮","siming":"西","pan":[{"pos":"东","wx":"震木","fu":"六煞","good":0},{"pos":"东南","wx":"巽木","fu":"绝命","good":0},{"pos":"南","wx":"离火","fu":"祸害","good":0},{"pos":"西南","wx":"坤土","fu":"生气","good":1},{"pos":"西","wx":"兑金","fu":"延年","good":1},{"pos":"西北","wx":"乾金","fu":"天医","good":1},{"pos":"北","wx":"坎水","fu":"五鬼","good":0},{"pos":"东北","wx":"艮土","fu":"伏位","good":1}],"zai":"东","li":"不利","good":["西","西北","西南","东北"],"ji":[["生气","西南"],["天医","西北"],["伏位","东北"],["延年","西"]],"xiong":[["祸害","南","火煞","黄色、棕色、咖啡色","咖啡色"],["六煞","东","木煞","红色、紫色","红色"],["五鬼","北","水煞","青色、绿色","绿色"],["绝命","东南","木煞","红色、紫色","红色"]]}';

    /**
     * 随机测试
     * @var array[]
     */
    protected array $randArr = [
        [
            'url' => '/v1/duoshou/index.html',
            'params' => [
                'username' => '测试',
                'time' => ['getRandBirthday'],
                'sex' => ['rand', [0, 1]],
                'pos' => '北',
            ],
        ],
    ];
}
