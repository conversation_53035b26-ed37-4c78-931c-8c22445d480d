<?php
// +----------------------------------------------------------------------
// | 公司起名
// +----------------------------------------------------------------------
// | Copyright (c) 2024 http://www.shuipf.com, All rights reserved.
// +----------------------------------------------------------------------
// | Author: zelda <<EMAIL>>
// +----------------------------------------------------------------------

namespace tests\v2019\v1;

use tests\Toolapi;

class GongsiqmTest extends Toolapi
{
    /**
     * 请求地址
     * @var string|array
     */
    protected string | array $apiUrl = [
        '/v1/gongsiqm/index.html?type=1',
    ];

    /**
     * 预先测试结果
     * @var string|array
     */
    protected string | array $beforeData = [
        '["元义","宝伟","伟永","厚鑫","贵富","安晶","协干","康鑫","利盈","新巨","如复","高大","公盈","公厚","康义","益进","厚聚","干昌","圣伟","欣干"]',
    ];

    /**
     * 随机测试
     * @var array[]
     */
    protected array $randArr = [
        [
            'url' => '/v1/gongsiqm/index.html',
            'params' => [
                'type' => ['rand', [1, 3]],
            ],
        ],
    ];
}
