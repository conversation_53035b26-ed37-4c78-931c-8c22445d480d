name: 构建Docker最新版latest镜像
on:
    push:
        branches:
            - dev

jobs:
    build:
        runs-on: juqianwh_k8s
        container:
            image: catthehacker/ubuntu:act-latest
        steps:
            -   run: docker version

            -   name: 拉取代码
                uses: actions/checkout@v4

            -   run: ls -all ./

            -   name: 登录Docker
                uses: actions/docker-login-action@v3
                with:
                    registry: registry.cn-hangzhou.aliyuncs.com
                    username: ${{ secrets.DOCKER_USERNAME }}
                    password: ${{ secrets.DOCKER_PASSWORD }}

            -   name: 构建和推送
                id: docker_build
                uses: actions/docker-build-push-action@v5
                with:
                    context: ./
                    file: ./docker/swoole/Dockerfile
                    push: true
                    tags: |
                        registry.cn-hangzhou.aliyuncs.com/projecta/think-tools:dev-latest

            -   name: 测试机部署
                run: |
                    curl -X PUT \
                        -H "Content-Type: application/yaml" \
                        -H "Cookie: KuboardUsername=admin; KuboardAccessKey=${{ secrets.KUBOARD_ACCESS_KEY }}" \
                        -d '{"kind":"deployments","namespace":"default","name":"think6-tools"}' \
                        "http://47.97.161.225:30080/kuboard-api/cluster/default/kind/CICDApi/admin/resource/restartWorkload"
