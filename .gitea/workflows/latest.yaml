name: 构建Docker最新版latest镜像
on:
    push:
        branches:
            - main

jobs:
    build:
        runs-on: juqianwh_k8s
        container:
            image: catthehacker/ubuntu:act-latest
        steps:
            -   run: docker version

            -   name: 拉取代码
                uses: actions/checkout@v4

            -   run: ls -all ./

            -   name: 登录Docker
                uses: actions/docker-login-action@v3
                with:
                    registry: registry.cn-hangzhou.aliyuncs.com
                    username: ${{ secrets.DOCKER_USERNAME }}
                    password: ${{ secrets.DOCKER_PASSWORD }}

            -   name: 构建和推送
                id: docker_build
                uses: actions/docker-build-push-action@v5
                with:
                    context: ./
                    file: ./docker/swoole/Dockerfile
                    push: true
                    tags: |
                        registry.cn-hangzhou.aliyuncs.com/projecta/think-tools:latest

    deploy:
        runs-on: juqianwh_k8s
        container:
            image: catthehacker/ubuntu:act-latest
        steps:
            -   name: k8s部署
                env:
                    K8S_REDEPLOY_URL: ${{ secrets.K8S_REDEPLOY_URL }}
                run: |
                    curl "$K8S_REDEPLOY_URL"
